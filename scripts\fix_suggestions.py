#!/usr/bin/env python3
import re

# 读取文件
with open('internal/service/mock_ai_service.go', 'r', encoding='utf-8') as f:
    content = f.read()

# 替换所有的 IntelligentSuggestion{ 为 MockIntelligentSuggestion{
content = re.sub(r'\bIntelligentSuggestion\{', 'MockIntelligentSuggestion{', content)

# 替换所有的 var suggestions []IntelligentSuggestion 为 var suggestions []MockIntelligentSuggestion
content = re.sub(r'var suggestions \[\]IntelligentSuggestion', 'var suggestions []MockIntelligentSuggestion', content)

# 写回文件
with open('internal/service/mock_ai_service.go', 'w', encoding='utf-8') as f:
    f.write(content)

print("Fixed all IntelligentSuggestion references in mock_ai_service.go")
