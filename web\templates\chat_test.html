<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>聊天功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .chat-container {
            border: 1px solid #ddd;
            height: 400px;
            overflow-y: auto;
            padding: 10px;
            margin-bottom: 10px;
            background-color: #f9f9f9;
        }
        .message {
            margin-bottom: 10px;
            padding: 8px;
            border-radius: 5px;
        }
        .user-message {
            background-color: #007bff;
            color: white;
            text-align: right;
        }
        .assistant-message {
            background-color: #e9ecef;
            color: #333;
        }
        .input-container {
            display: flex;
            gap: 10px;
        }
        #messageInput {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        #sendButton {
            padding: 10px 20px;
            background-color: #007bff;
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
        }
        #sendButton:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background-color: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <h1>聊天功能测试</h1>
    
    <div class="chat-container" id="chatContainer">
        <!-- 聊天消息将在这里显示 -->
    </div>
    
    <div class="input-container">
        <input type="text" id="messageInput" placeholder="输入您的消息..." />
        <button id="sendButton">发送</button>
    </div>
    
    <div id="status" class="status" style="display: none;"></div>

    <script>
        let currentSessionId = null;
        
        // 页面加载时创建会话
        document.addEventListener('DOMContentLoaded', async function() {
            await createSession();
        });
        
        // 创建会话
        async function createSession() {
            try {
                showStatus('正在创建会话...', 'info');
                
                const response = await fetch('/api/v1/chat/sessions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        title: '测试会话'
                    })
                });
                
                const data = await response.json();
                console.log('会话创建响应:', data);
                
                if (data.code === 201 && data.data && data.data.session_id) {
                    currentSessionId = data.data.session_id;
                    showStatus(`会话创建成功: ${currentSessionId}`, 'success');
                } else {
                    throw new Error('会话创建失败: ' + (data.message || '未知错误'));
                }
            } catch (error) {
                console.error('创建会话失败:', error);
                showStatus('会话创建失败: ' + error.message, 'error');
            }
        }
        
        // 发送消息
        async function sendMessage() {
            const input = document.getElementById('messageInput');
            const message = input.value.trim();
            
            if (!message) return;
            if (!currentSessionId) {
                showStatus('请先创建会话', 'error');
                return;
            }
            
            // 显示用户消息
            addMessage('user', message);
            input.value = '';
            
            // 禁用发送按钮
            const sendButton = document.getElementById('sendButton');
            sendButton.disabled = true;
            sendButton.textContent = '发送中...';
            
            try {
                showStatus('正在发送消息...', 'info');
                
                const response = await fetch('/api/v1/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        session_id: currentSessionId,
                        content: message
                    })
                });
                
                const data = await response.json();
                console.log('消息响应:', data);
                
                if (data.code === 200 && data.data) {
                    const aiResponse = data.data.content || data.data.ai_response || '收到回复但内容为空';
                    addMessage('assistant', aiResponse);
                    showStatus('消息发送成功', 'success');
                } else {
                    throw new Error('发送失败: ' + (data.message || '未知错误'));
                }
            } catch (error) {
                console.error('发送消息失败:', error);
                addMessage('assistant', '抱歉，发送消息时出现错误: ' + error.message);
                showStatus('发送失败: ' + error.message, 'error');
            } finally {
                // 恢复发送按钮
                sendButton.disabled = false;
                sendButton.textContent = '发送';
            }
        }
        
        // 添加消息到聊天容器
        function addMessage(type, content) {
            const chatContainer = document.getElementById('chatContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = `message ${type}-message`;
            messageDiv.textContent = content;
            chatContainer.appendChild(messageDiv);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // 显示状态信息
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
            statusDiv.style.display = 'block';
            
            // 3秒后自动隐藏成功和信息状态
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    statusDiv.style.display = 'none';
                }, 3000);
            }
        }
        
        // 绑定事件
        document.getElementById('sendButton').addEventListener('click', sendMessage);
        document.getElementById('messageInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
    </script>
</body>
</html>
