package main

import (
	"fmt"
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Host 主机模型
type Host struct {
	ID                int64  `json:"id" gorm:"primaryKey;autoIncrement"`
	Name              string `json:"name" gorm:"uniqueIndex;size:100;not null"`
	IPAddress         string `json:"ip_address" gorm:"size:45;not null"`
	Port              int    `json:"port" gorm:"not null;default:22"`
	Username          string `json:"username" gorm:"size:50;not null"`
	PasswordEncrypted string `json:"-" gorm:"type:text"`
	Status            string `json:"status" gorm:"size:20;not null;default:unknown"`
}

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("data/aiops.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 查询**************主机
	var hosts []Host
	result := db.Where("ip_address = ?", "**************").Find(&hosts)
	if result.Error != nil {
		log.Fatal("Query failed:", result.Error)
	}

	fmt.Printf("找到 %d 个主机记录:\n", len(hosts))
	fmt.Println("============================================")

	for _, host := range hosts {
		fmt.Printf("ID: %d\n", host.ID)
		fmt.Printf("名称: %s\n", host.Name)
		fmt.Printf("IP地址: %s\n", host.IPAddress)
		fmt.Printf("端口: %d\n", host.Port)
		fmt.Printf("用户名: %s\n", host.Username)
		fmt.Printf("密码加密状态: %s\n", func() string {
			if host.PasswordEncrypted == "" {
				return "❌ 未设置密码"
			} else if len(host.PasswordEncrypted) > 50 {
				return "✅ 已加密 (长度: " + fmt.Sprintf("%d", len(host.PasswordEncrypted)) + ")"
			} else {
				return "⚠️ 可能是明文 (长度: " + fmt.Sprintf("%d", len(host.PasswordEncrypted)) + ")"
			}
		}())
		fmt.Printf("密码内容: %s\n", host.PasswordEncrypted)
		fmt.Printf("状态: %s\n", host.Status)
		fmt.Println("--------------------------------------------")
	}

	// 查询所有主机
	var allHosts []Host
	db.Find(&allHosts)
	fmt.Printf("\n数据库中总共有 %d 个主机记录\n", len(allHosts))
}
