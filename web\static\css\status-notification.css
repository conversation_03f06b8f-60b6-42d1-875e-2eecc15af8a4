/* ========================================
   实时状态与通知系统样式
   提供完整的状态监控和通知功能
   ======================================== */

/* 通知按钮 */
.notification-toggle-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  font-size: 18px;
}

.notification-toggle-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  transform: scale(1.05);
}

.notification-badge {
  position: absolute;
  top: 6px;
  right: 6px;
  background: var(--color-error);
  color: white;
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
}

/* 状态指示器 */
.status-indicator {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: var(--space-1);
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  position: relative;
  transition: all var(--duration-fast) var(--easing-ease);
}

.status-dot::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: 50%;
  opacity: 0.3;
  animation: pulse 2s infinite;
}

.status-dot.online {
  background-color: var(--color-success);
}

.status-dot.online::before {
  background-color: var(--color-success);
}

.status-dot.offline {
  background-color: var(--color-error);
}

.status-dot.offline::before {
  background-color: var(--color-error);
}

.status-dot.warning {
  background-color: var(--color-warning);
}

.status-dot.warning::before {
  background-color: var(--color-warning);
}

.status-dot.loading {
  background-color: var(--color-info);
  animation: spin 1s linear infinite;
}

.status-text {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

/* 连接状态栏 */
.connection-status {
  position: fixed;
  top: var(--navbar-height);
  left: 0;
  right: 0;
  background: var(--color-warning);
  color: white;
  padding: var(--space-2) var(--space-4);
  text-align: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  z-index: 9999;
  transform: translateY(-100%);
  transition: transform var(--duration-normal) var(--easing-ease);
}

.connection-status.show {
  transform: translateY(0);
}

.connection-status.error {
  background: var(--color-error);
}

.connection-status.success {
  background: var(--color-success);
}

.connection-status.reconnecting {
  background: var(--color-info);
}

/* 通知中心 */
.notification-center {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: var(--bg-primary);
  border-left: 1px solid var(--border-primary);
  box-shadow: var(--shadow-xl);
  z-index: 10000;
  transition: right var(--duration-normal) var(--easing-ease);
  display: flex;
  flex-direction: column;
}

.notification-center.open {
  right: 0;
}

.notification-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.notification-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.notification-actions {
  display: flex;
  gap: var(--space-2);
}

.notification-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.notification-action-btn:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.notification-filters {
  display: flex;
  gap: var(--space-1);
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-tertiary);
}

.notification-filter {
  padding: var(--space-1) var(--space-2);
  border: 1px solid var(--border-primary);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.notification-filter:hover {
  background: var(--bg-hover);
  border-color: var(--color-primary);
}

.notification-filter.active {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-2);
}

/* 通知项 */
.notification-item {
  display: flex;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-lg);
  margin-bottom: var(--space-2);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  position: relative;
  border: 1px solid transparent;
}

.notification-item:hover {
  background: var(--bg-hover);
  border-color: var(--border-secondary);
}

.notification-item.unread {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
}

.notification-item.unread::before {
  content: '';
  position: absolute;
  top: var(--space-3);
  left: var(--space-2);
  width: 6px;
  height: 6px;
  background: var(--color-primary);
  border-radius: 50%;
}

.notification-icon {
  width: 40px;
  height: 40px;
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.notification-icon.info {
  background: linear-gradient(135deg, var(--color-info), #0c7489);
  color: white;
}

.notification-icon.success {
  background: linear-gradient(135deg, var(--color-success), #047857);
  color: white;
}

.notification-icon.warning {
  background: linear-gradient(135deg, var(--color-warning), #d97706);
  color: white;
}

.notification-icon.error {
  background: linear-gradient(135deg, var(--color-error), #dc2626);
  color: white;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.notification-message {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: var(--space-2);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.notification-time {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.notification-actions-inline {
  display: flex;
  gap: var(--space-1);
}

.notification-action-inline {
  padding: var(--space-1) var(--space-2);
  border: 1px solid var(--border-primary);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.notification-action-inline:hover {
  background: var(--bg-hover);
  border-color: var(--color-primary);
}

/* 浮动通知 */
.toast-container {
  position: fixed;
  top: calc(var(--navbar-height) + var(--space-4));
  right: var(--space-4);
  z-index: 10001;
  pointer-events: none;
}

.toast {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-2);
  box-shadow: var(--shadow-lg);
  max-width: 400px;
  pointer-events: auto;
  transform: translateX(100%);
  opacity: 0;
  transition: all var(--duration-normal) var(--easing-ease);
}

.toast.show {
  transform: translateX(0);
  opacity: 1;
}

.toast.hide {
  transform: translateX(100%);
  opacity: 0;
}

.toast-icon {
  width: 24px;
  height: 24px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  flex-shrink: 0;
}

.toast.info .toast-icon {
  background: var(--color-info);
  color: white;
}

.toast.success .toast-icon {
  background: var(--color-success);
  color: white;
}

.toast.warning .toast-icon {
  background: var(--color-warning);
  color: white;
}

.toast.error .toast-icon {
  background: var(--color-error);
  color: white;
}

.toast-content {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.toast-message {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.toast-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  flex-shrink: 0;
}

.toast-close:hover {
  background: var(--bg-hover);
  color: var(--text-secondary);
}

/* 系统健康监控面板 */
.health-monitor {
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.health-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-4);
}

.health-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.health-status {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background: var(--bg-primary);
  border-radius: var(--radius-lg);
  border: 1px solid var(--border-primary);
}

.health-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--space-3);
}

.health-metric {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  padding: var(--space-3);
}

.metric-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.metric-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.metric-status {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.metric-value {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.metric-change {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

.metric-change.positive {
  color: var(--color-success);
}

.metric-change.negative {
  color: var(--color-error);
}

.metric-change.neutral {
  color: var(--text-tertiary);
}

.metric-chart {
  height: 40px;
  margin-top: var(--space-2);
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  position: relative;
  overflow: hidden;
}

.metric-chart-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary), var(--color-primary-hover));
  border-radius: var(--radius-sm);
  transition: width var(--duration-normal) var(--easing-ease);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .notification-center {
    width: 100vw;
    right: -100vw;
  }
  
  .toast-container {
    left: var(--space-2);
    right: var(--space-2);
    top: calc(var(--navbar-height) + var(--space-2));
  }
  
  .toast {
    max-width: none;
    transform: translateY(-100%);
  }
  
  .toast.show {
    transform: translateY(0);
  }
  
  .toast.hide {
    transform: translateY(-100%);
  }
  
  .health-metrics {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .connection-status {
    padding: var(--space-1) var(--space-2);
    font-size: var(--font-size-xs);
  }
  
  .notification-header {
    padding: var(--space-3);
  }
  
  .notification-item {
    padding: var(--space-2);
  }
  
  .notification-icon {
    width: 32px;
    height: 32px;
    font-size: 16px;
  }
  
  .toast {
    padding: var(--space-3);
  }
}
