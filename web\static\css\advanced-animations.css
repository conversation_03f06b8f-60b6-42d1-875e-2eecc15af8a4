/* ========================================
   高级动画与微交互系统
   提供专业级的用户体验
   ======================================== */

/* 动画变量系统 */
:root {
  /* 动画时长 */
  --duration-instant: 100ms;
  --duration-fast: 200ms;
  --duration-normal: 300ms;
  --duration-slow: 500ms;
  --duration-slower: 800ms;
  
  /* 缓动函数 */
  --ease-out-quart: cubic-bezier(0.25, 1, 0.5, 1);
  --ease-out-expo: cubic-bezier(0.19, 1, 0.22, 1);
  --ease-out-back: cubic-bezier(0.34, 1.56, 0.64, 1);
  --ease-in-out-quart: cubic-bezier(0.76, 0, 0.24, 1);
  --ease-spring: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  
  /* 动画距离 */
  --translate-sm: 4px;
  --translate-md: 8px;
  --translate-lg: 16px;
  --translate-xl: 24px;
  
  /* 缩放比例 */
  --scale-sm: 0.95;
  --scale-md: 0.9;
  --scale-lg: 1.05;
  --scale-xl: 1.1;
}

/* 页面加载动画 */
@keyframes pageLoad {
  0% {
    opacity: 0;
    transform: translateY(20px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromLeft {
  0% {
    opacity: 0;
    transform: translateX(-100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromRight {
  0% {
    opacity: 0;
    transform: translateX(100%);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInFromTop {
  0% {
    opacity: 0;
    transform: translateY(-100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInFromBottom {
  0% {
    opacity: 0;
    transform: translateY(100%);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.8);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes fadeInUp {
  0% {
    opacity: 0;
    transform: translateY(var(--translate-lg));
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  0% {
    opacity: 0;
    transform: translateY(calc(-1 * var(--translate-lg)));
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载动画 */
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.05);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes typing {
  0%, 20% { transform: scale(1); }
  10% { transform: scale(1.1); }
}

@keyframes wave {
  0%, 60%, 100% {
    transform: initial;
  }
  30% {
    transform: translateY(-15px);
  }
}

/* 页面转场动画 */
.page-transition-enter {
  animation: pageLoad var(--duration-normal) var(--ease-out-quart);
}

.sidebar-enter {
  animation: slideInFromLeft var(--duration-normal) var(--ease-out-quart);
}

.assistant-panel-enter {
  animation: slideInFromRight var(--duration-normal) var(--ease-out-quart);
}

.navbar-enter {
  animation: slideInFromTop var(--duration-normal) var(--ease-out-quart);
}

.chat-input-enter {
  animation: slideInFromBottom var(--duration-normal) var(--ease-out-quart);
}

/* 消息动画 */
.message-enter {
  animation: fadeInUp var(--duration-normal) var(--ease-out-quart);
}

.message-user-enter {
  animation: fadeInUp var(--duration-normal) var(--ease-out-quart);
}

.message-assistant-enter {
  animation: fadeInUp var(--duration-normal) var(--ease-out-quart);
}

/* 模态框动画 */
.modal-enter {
  animation: scaleIn var(--duration-normal) var(--ease-out-back);
}

.modal-backdrop-enter {
  animation: fadeIn var(--duration-fast) ease-out;
}

/* 通知动画 */
.notification-enter {
  animation: slideInFromTop var(--duration-normal) var(--ease-out-back);
}

.notification-exit {
  animation: slideOutToTop var(--duration-fast) var(--ease-in-out-quart);
}

@keyframes slideOutToTop {
  0% {
    opacity: 1;
    transform: translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateY(-100%);
  }
}

/* 按钮微交互 */
.btn-interactive {
  position: relative;
  overflow: hidden;
  transition: all var(--duration-fast) var(--ease-out-quart);
}

.btn-interactive::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width var(--duration-normal) var(--ease-out-quart),
              height var(--duration-normal) var(--ease-out-quart);
}

.btn-interactive:hover::before {
  width: 300px;
  height: 300px;
}

.btn-interactive:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.btn-interactive:active {
  transform: translateY(0);
  transition-duration: var(--duration-instant);
}

/* 卡片悬停效果 */
.card-interactive {
  transition: all var(--duration-normal) var(--ease-out-quart);
  position: relative;
}

.card-interactive::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
  opacity: 0;
  transition: opacity var(--duration-normal) var(--ease-out-quart);
  pointer-events: none;
  border-radius: inherit;
}

.card-interactive:hover {
  transform: translateY(-4px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.card-interactive:hover::before {
  opacity: 1;
}

/* 输入框聚焦动画 */
.input-interactive {
  position: relative;
  transition: all var(--duration-normal) var(--ease-out-quart);
}

.input-interactive::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: var(--color-primary);
  transition: all var(--duration-normal) var(--ease-out-quart);
  transform: translateX(-50%);
}

.input-interactive:focus-within::after {
  width: 100%;
}

.input-interactive:focus-within {
  transform: translateY(-2px);
}

/* 加载状态动画 */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-primary);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-dots {
  display: inline-flex;
  gap: 4px;
}

.loading-dots span {
  width: 6px;
  height: 6px;
  background: var(--color-primary);
  border-radius: 50%;
  animation: wave 1.4s ease-in-out infinite;
}

.loading-dots span:nth-child(2) {
  animation-delay: 0.2s;
}

.loading-dots span:nth-child(3) {
  animation-delay: 0.4s;
}

/* 骨架屏动画 */
.skeleton {
  background: linear-gradient(90deg, 
    var(--bg-tertiary) 25%, 
    var(--bg-hover) 50%, 
    var(--bg-tertiary) 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.skeleton-text {
  height: 1em;
  border-radius: 4px;
  margin-bottom: 8px;
}

.skeleton-text:last-child {
  width: 60%;
}

.skeleton-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.skeleton-button {
  height: 36px;
  border-radius: 6px;
  width: 100px;
}

/* 打字机效果 */
.typewriter {
  overflow: hidden;
  border-right: 2px solid var(--color-primary);
  white-space: nowrap;
  animation: typing 3.5s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes blink-caret {
  from, to { border-color: transparent; }
  50% { border-color: var(--color-primary); }
}

/* 滚动动画 */
.scroll-reveal {
  opacity: 0;
  transform: translateY(30px);
  transition: all var(--duration-slow) var(--ease-out-quart);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

/* 粒子效果 */
.particle-container {
  position: relative;
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 4px;
  height: 4px;
  background: var(--color-primary);
  border-radius: 50%;
  opacity: 0.6;
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 响应式动画调整 */
@media (max-width: 768px) {
  :root {
    --duration-fast: 150ms;
    --duration-normal: 250ms;
    --duration-slow: 400ms;
  }
  
  .card-interactive:hover {
    transform: translateY(-2px) scale(1.01);
  }
  
  .btn-interactive:hover {
    transform: translateY(-1px);
  }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .scroll-reveal {
    opacity: 1;
    transform: none;
  }
}
