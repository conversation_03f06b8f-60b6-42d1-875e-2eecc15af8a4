package handler

import (
	"net/http"

	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// GetSystemOverview 获取系统概览
func (h *StatsHandler) GetSystemOverview(c *gin.Context) {
	overview, err := h.services.Stats.GetSystemOverview()
	if err != nil {
		h.logger.WithError(err).Error("Failed to get system overview")
		c.<PERSON>(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get system overview",
		})
		return
	}

	c.<PERSON><PERSON><PERSON>(http.StatusOK, gin.H{
		"code":    200,
		"message": "System overview retrieved successfully",
		"data":    overview,
	})
}

// GetHostStats 获取主机统计
func (h *StatsHandler) GetHostStats(c *gin.Context) {
	var req service.HostStatsRequest
	req.TimeRange = c.Query("time_range")
	req.GroupBy = c.Query("group_by")

	stats, err := h.services.Stats.GetHostStats(&req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get host stats")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get host statistics",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Host statistics retrieved successfully",
		"data":    stats,
	})
}

// GetAlertStats 获取告警统计
func (h *StatsHandler) GetAlertStats(c *gin.Context) {
	var req service.AlertStatsRequest
	req.TimeRange = c.Query("time_range")
	req.Severity = c.Query("severity")

	stats, err := h.services.Stats.GetAlertStats(&req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get alert stats")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get alert statistics",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Alert statistics retrieved successfully",
		"data":    stats,
	})
}

// GetOperationStats 获取操作统计
func (h *StatsHandler) GetOperationStats(c *gin.Context) {
	var req service.OperationStatsRequest
	req.TimeRange = c.Query("time_range")

	if userIDStr := c.Query("user_id"); userIDStr != "" {
		// TODO: 解析用户ID
	}

	stats, err := h.services.Stats.GetOperationStats(&req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get operation stats")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get operation statistics",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Operation statistics retrieved successfully",
		"data":    stats,
	})
}

// GetUserStats 获取用户统计
func (h *StatsHandler) GetUserStats(c *gin.Context) {
	var req service.UserStatsRequest
	req.TimeRange = c.Query("time_range")
	req.Role = c.Query("role")

	stats, err := h.services.Stats.GetUserStats(&req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user stats")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get user statistics",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "User statistics retrieved successfully",
		"data":    stats,
	})
}
