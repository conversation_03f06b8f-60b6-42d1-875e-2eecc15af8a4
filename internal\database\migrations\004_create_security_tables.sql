-- 命令审计日志表
CREATE TABLE IF NOT EXISTS command_audit_logs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    host_id BIGINT NOT NULL,
    command TEXT NOT NULL,
    risk_level VARCHAR(20) NOT NULL,
    is_allowed BOOLEAN NOT NULL,
    executed_at TIMESTAMP NULL,
    result TEXT,
    exit_code INT DEFAULT 0,
    duration INT DEFAULT 0,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(100),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_host_id (host_id),
    INDEX idx_risk_level (risk_level),
    INDEX idx_is_allowed (is_allowed),
    INDEX idx_executed_at (executed_at),
    INDEX idx_ip_address (ip_address),
    INDEX idx_session_id (session_id),
    INDEX idx_created_at (created_at)
);

-- 安全事件表
CREATE TABLE IF NOT EXISTS security_events (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    event_type VARCHAR(50) NOT NULL,
    severity VARCHAR(20) NOT NULL,
    description TEXT NOT NULL,
    details JSON,
    ip_address VARCHAR(45),
    user_agent TEXT,
    session_id VARCHAR(100),
    resolved BOOLEAN DEFAULT FALSE,
    resolved_by BIGINT NULL,
    resolved_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_event_type (event_type),
    INDEX idx_severity (severity),
    INDEX idx_resolved (resolved),
    INDEX idx_ip_address (ip_address),
    INDEX idx_session_id (session_id),
    INDEX idx_created_at (created_at)
);

-- 用户会话表
CREATE TABLE IF NOT EXISTS user_sessions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    session_id VARCHAR(100) UNIQUE NOT NULL,
    ip_address VARCHAR(45),
    user_agent TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_is_active (is_active),
    INDEX idx_last_activity (last_activity),
    INDEX idx_expires_at (expires_at)
);

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    resource VARCHAR(100) NOT NULL,
    action VARCHAR(50) NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_resource (resource),
    INDEX idx_action (action)
);

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    role_id BIGINT NOT NULL,
    granted_by BIGINT NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_role_id (role_id),
    UNIQUE KEY uk_user_role (user_id, role_id)
);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    role_id BIGINT NOT NULL,
    permission_id BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_role_id (role_id),
    INDEX idx_permission_id (permission_id),
    UNIQUE KEY uk_role_permission (role_id, permission_id)
);

-- 主机权限表
CREATE TABLE IF NOT EXISTS host_permissions (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    host_id BIGINT NOT NULL,
    permission VARCHAR(50) NOT NULL,
    granted_by BIGINT NOT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_user_id (user_id),
    INDEX idx_host_id (host_id),
    UNIQUE KEY uk_user_host_permission (user_id, host_id, permission)
);

-- 系统配置表
CREATE TABLE IF NOT EXISTS system_configs (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    `key` VARCHAR(100) UNIQUE NOT NULL,
    `value` TEXT,
    `type` VARCHAR(20) DEFAULT 'string',
    category VARCHAR(50),
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    updated_by BIGINT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_category (category),
    INDEX idx_is_public (is_public)
);

-- 插入默认权限
INSERT IGNORE INTO permissions (name, resource, action, description) VALUES
('host.read', 'host', 'read', '查看主机信息'),
('host.create', 'host', 'create', '添加主机'),
('host.update', 'host', 'update', '更新主机信息'),
('host.delete', 'host', 'delete', '删除主机'),
('host.execute', 'host', 'execute', '在主机上执行命令'),
('command.execute.safe', 'command', 'execute_safe', '执行安全命令'),
('command.execute.low_risk', 'command', 'execute_low_risk', '执行低风险命令'),
('command.execute.medium_risk', 'command', 'execute_medium_risk', '执行中风险命令'),
('command.execute.high_risk', 'command', 'execute_high_risk', '执行高风险命令'),
('command.execute.critical_risk', 'command', 'execute_critical_risk', '执行极高风险命令'),
('system.config.read', 'system', 'config_read', '查看系统配置'),
('system.config.write', 'system', 'config_write', '修改系统配置'),
('audit.read', 'audit', 'read', '查看审计日志'),
('security.manage', 'security', 'manage', '管理安全设置'),
('user.manage', 'user', 'manage', '管理用户');

-- 插入默认角色
INSERT IGNORE INTO roles (name, description, is_system) VALUES
('admin', '系统管理员，拥有所有权限', TRUE),
('operator', '运维人员，可以执行大部分操作', TRUE),
('user', '普通用户，只能执行基本操作', TRUE),
('readonly', '只读用户，只能查看信息', TRUE);

-- 为管理员角色分配所有权限
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'admin';

-- 为运维人员分配权限
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'operator' AND p.name IN (
    'host.read', 'host.create', 'host.update', 'host.execute',
    'command.execute.safe', 'command.execute.low_risk', 'command.execute.medium_risk',
    'audit.read'
);

-- 为普通用户分配权限
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'user' AND p.name IN (
    'host.read', 'host.execute',
    'command.execute.safe', 'command.execute.low_risk'
);

-- 为只读用户分配权限
INSERT IGNORE INTO role_permissions (role_id, permission_id)
SELECT r.id, p.id
FROM roles r, permissions p
WHERE r.name = 'readonly' AND p.name IN (
    'host.read', 'audit.read'
);

-- 插入默认系统配置
INSERT IGNORE INTO system_configs (`key`, `value`, `type`, category, description, is_public, updated_by) VALUES
('security.jwt_expiration', '24h', 'string', 'security', 'JWT令牌过期时间', FALSE, 1),
('security.allow_high_risk_commands', 'false', 'bool', 'security', '是否允许执行高风险命令', FALSE, 1),
('security.require_auth_for_high_risk', 'true', 'bool', 'security', '高风险命令是否需要额外认证', FALSE, 1),
('security.enable_audit_log', 'true', 'bool', 'security', '是否启用审计日志', FALSE, 1),
('security.audit_log_retention_days', '90', 'int', 'security', '审计日志保留天数', FALSE, 1),
('security.session_timeout', '30m', 'string', 'security', '会话超时时间', FALSE, 1),
('security.max_concurrent_sessions', '5', 'int', 'security', '最大并发会话数', FALSE, 1),
('system.default_user_role', 'user', 'string', 'system', '默认用户角色', FALSE, 1),
('ui.theme', 'light', 'string', 'ui', '默认主题', TRUE, 1),
('ui.language', 'zh-CN', 'string', 'ui', '默认语言', TRUE, 1);
