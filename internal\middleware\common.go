package middleware

import (
	"bytes"
	"context"
	"fmt"
	"io"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"golang.org/x/time/rate"
)

// LoggerMiddleware 日志中间件
func LoggerMiddleware(logger *logrus.Logger) gin.HandlerFunc {
	return gin.LoggerWithFormatter(func(param gin.LogFormatterParams) string {
		var statusColor, methodColor, resetColor string
		if param.IsOutputColor() {
			statusColor = param.StatusCodeColor()
			methodColor = param.MethodColor()
			resetColor = param.ResetColor()
		}

		if param.Latency > time.Minute {
			param.Latency = param.Latency.Truncate(time.Second)
		}

		return fmt.Sprintf("[GIN] %v |%s %3d %s| %13v | %15s |%s %-7s %s %#v\n%s",
			param.TimeStamp.Format("2006/01/02 - 15:04:05"),
			statusColor, param.StatusCode, resetColor,
			param.Latency,
			param.ClientIP,
			methodColor, param.Method, resetColor,
			param.Path,
			param.ErrorMessage,
		)
	})
}

// RequestLoggerMiddleware 请求日志中间件
func RequestLoggerMiddleware(logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		start := time.Now()
		path := c.Request.URL.Path
		raw := c.Request.URL.RawQuery

		// 记录请求体（仅用于调试，生产环境应谨慎使用）
		var bodyBytes []byte
		if c.Request.Body != nil && c.Request.ContentLength > 0 && c.Request.ContentLength < 1024*1024 { // 限制1MB
			bodyBytes, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
		}

		c.Next()

		param := gin.LogFormatterParams{
			Request:      c.Request,
			TimeStamp:    time.Now(),
			Latency:      time.Since(start),
			ClientIP:     c.ClientIP(),
			Method:       c.Request.Method,
			StatusCode:   c.Writer.Status(),
			ErrorMessage: c.Errors.ByType(gin.ErrorTypePrivate).String(),
			BodySize:     c.Writer.Size(),
		}

		if raw != "" {
			path = path + "?" + raw
		}
		param.Path = path

		// 构建日志字段
		fields := logrus.Fields{
			"method":     param.Method,
			"path":       param.Path,
			"status":     param.StatusCode,
			"latency":    param.Latency,
			"client_ip":  param.ClientIP,
			"user_agent": c.Request.UserAgent(),
			"body_size":  param.BodySize,
		}

		// 添加用户信息
		if userID, exists := GetUserIDFromContext(c); exists {
			fields["user_id"] = userID
		}
		if username, exists := GetUsernameFromContext(c); exists {
			fields["username"] = username
		}

		// 记录请求体（仅在调试模式下）
		if gin.Mode() == gin.DebugMode && len(bodyBytes) > 0 {
			fields["request_body"] = string(bodyBytes)
		}

		// 🔧 优化日志：过滤常见请求，减少日志噪音
		shouldLog := true

		// 过滤静态资源和健康检查请求
		if strings.HasPrefix(param.Path, "/static/") ||
		   strings.HasPrefix(param.Path, "/health") ||
		   strings.HasPrefix(param.Path, "/metrics") ||
		   param.Path == "/favicon.ico" {
			shouldLog = false
		}

		// 过滤WebSocket升级请求的成功日志
		if strings.HasPrefix(param.Path, "/ws/") && param.StatusCode == 200 {
			shouldLog = false
		}

		if shouldLog {
			// 根据状态码选择日志级别
			entry := logger.WithFields(fields)
			if param.StatusCode >= 500 {
				entry.Error("HTTP request completed with server error")
			} else if param.StatusCode >= 400 {
				entry.Warn("HTTP request completed with client error")
			} else {
				entry.Info("HTTP request completed")
			}
		}
	}
}

// CORSMiddleware CORS中间件
func CORSMiddleware(allowOrigins []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")

		// 检查是否允许该来源
		allowed := false
		for _, allowedOrigin := range allowOrigins {
			if allowedOrigin == "*" || allowedOrigin == origin {
				allowed = true
				break
			}
		}

		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}

		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, accept, origin, Cache-Control, X-Requested-With, X-API-Key")
		c.Header("Access-Control-Allow-Methods", "POST, OPTIONS, GET, PUT, DELETE, PATCH")

		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}

		c.Next()
	}
}

// RateLimitMiddleware 限流中间件
type RateLimitMiddleware struct {
	limiters map[string]*rate.Limiter
	global   *rate.Limiter
}

// NewRateLimitMiddleware 创建限流中间件
func NewRateLimitMiddleware(globalRate rate.Limit, globalBurst int) *RateLimitMiddleware {
	return &RateLimitMiddleware{
		limiters: make(map[string]*rate.Limiter),
		global:   rate.NewLimiter(globalRate, globalBurst),
	}
}

// RateLimit 限流中间件
func (rlm *RateLimitMiddleware) RateLimit(perUserRate rate.Limit, perUserBurst int) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 全局限流检查
		if !rlm.global.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"code":    429,
				"message": "Global rate limit exceeded",
			})
			c.Abort()
			return
		}

		// 获取用户标识（优先使用用户ID，其次使用IP）
		var key string
		if userID, exists := GetUserIDFromContext(c); exists {
			key = fmt.Sprintf("user:%d", userID)
		} else {
			key = fmt.Sprintf("ip:%s", c.ClientIP())
		}

		// 获取或创建用户限流器
		limiter, exists := rlm.limiters[key]
		if !exists {
			limiter = rate.NewLimiter(perUserRate, perUserBurst)
			rlm.limiters[key] = limiter
		}

		// 用户限流检查
		if !limiter.Allow() {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"code":    429,
				"message": "Rate limit exceeded",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 防止点击劫持
		c.Header("X-Frame-Options", "DENY")

		// 防止MIME类型嗅探
		c.Header("X-Content-Type-Options", "nosniff")

		// XSS保护
		c.Header("X-XSS-Protection", "1; mode=block")

		// 强制HTTPS
		c.Header("Strict-Transport-Security", "max-age=31536000; includeSubDomains")

		// 内容安全策略 - 严格的本地资源策略
		csp := "default-src 'self'; " +
			"script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; " +
			"style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; " +
			"font-src 'self' https://cdn.jsdelivr.net; " +
			"img-src 'self' data: https:; " +
			"connect-src 'self'"
		c.Header("Content-Security-Policy", csp)

		// 引用策略
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")

		// 权限策略
		c.Header("Permissions-Policy", "geolocation=(), microphone=(), camera=()")

		c.Next()
	}
}

// RecoveryMiddleware 恢复中间件
func RecoveryMiddleware(logger *logrus.Logger) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		if err, ok := recovered.(string); ok {
			logger.WithFields(logrus.Fields{
				"method":     c.Request.Method,
				"path":       c.Request.URL.Path,
				"client_ip":  c.ClientIP(),
				"user_agent": c.Request.UserAgent(),
				"error":      err,
			}).Error("Panic recovered")
		}

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Internal server error",
		})
	})
}

// TimeoutMiddleware 超时中间件
func TimeoutMiddleware(timeout time.Duration) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置超时上下文
		ctx, cancel := context.WithTimeout(c.Request.Context(), timeout)
		defer cancel()

		c.Request = c.Request.WithContext(ctx)

		// 使用通道来检测处理是否完成
		finished := make(chan struct{})
		go func() {
			c.Next()
			finished <- struct{}{}
		}()

		select {
		case <-finished:
			// 请求正常完成
		case <-ctx.Done():
			// 请求超时
			c.JSON(http.StatusRequestTimeout, gin.H{
				"code":    408,
				"message": "Request timeout",
			})
			c.Abort()
		}
	}
}

// RequestIDMiddleware 请求ID中间件
func RequestIDMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := c.GetHeader("X-Request-ID")
		if requestID == "" {
			requestID = generateRequestID()
		}

		traceID := c.GetHeader("X-Trace-ID")
		if traceID == "" {
			traceID = generateTraceID()
		}

		c.Set("request_id", requestID)
		c.Set("trace_id", traceID)
		c.Header("X-Request-ID", requestID)
		c.Header("X-Trace-ID", traceID)

		c.Next()
	}
}

// generateRequestID 生成请求ID
func generateRequestID() string {
	return fmt.Sprintf("req_%d", time.Now().UnixNano())
}

// generateTraceID 生成跟踪ID
func generateTraceID() string {
	return fmt.Sprintf("trace_%d_%d", time.Now().Unix(), time.Now().UnixNano()%1000000)
}

// GetRequestID 从上下文获取请求ID
func GetRequestID(c *gin.Context) string {
	if requestID, exists := c.Get("request_id"); exists {
		if id, ok := requestID.(string); ok {
			return id
		}
	}
	return ""
}

// GetTraceID 从上下文获取跟踪ID
func GetTraceID(c *gin.Context) string {
	if traceID, exists := c.Get("trace_id"); exists {
		if id, ok := traceID.(string); ok {
			return id
		}
	}
	return ""
}

// PaginationMiddleware 分页中间件
func PaginationMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取分页参数
		pageStr := c.DefaultQuery("page", "1")
		limitStr := c.DefaultQuery("limit", "20")

		page, err := strconv.Atoi(pageStr)
		if err != nil || page < 1 {
			page = 1
		}

		limit, err := strconv.Atoi(limitStr)
		if err != nil || limit < 1 || limit > 100 {
			limit = 20
		}

		// 计算偏移量
		offset := (page - 1) * limit

		// 设置到上下文
		c.Set("page", page)
		c.Set("limit", limit)
		c.Set("offset", offset)

		c.Next()
	}
}

// GetPaginationFromContext 从上下文获取分页信息
func GetPaginationFromContext(c *gin.Context) (page, limit, offset int) {
	pageVal, _ := c.Get("page")
	limitVal, _ := c.Get("limit")
	offsetVal, _ := c.Get("offset")

	if p, ok := pageVal.(int); ok {
		page = p
	} else {
		page = 1
	}

	if l, ok := limitVal.(int); ok {
		limit = l
	} else {
		limit = 20
	}

	if o, ok := offsetVal.(int); ok {
		offset = o
	} else {
		offset = 0
	}

	return
}

// ValidateJSONMiddleware JSON验证中间件
func ValidateJSONMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		if c.Request.Method == "POST" || c.Request.Method == "PUT" || c.Request.Method == "PATCH" {
			contentType := c.GetHeader("Content-Type")
			if strings.Contains(contentType, "application/json") {
				// 检查JSON格式
				var json interface{}
				if err := c.ShouldBindJSON(&json); err != nil {
					c.JSON(http.StatusBadRequest, gin.H{
						"code":    400,
						"message": "Invalid JSON format",
						"error":   err.Error(),
					})
					c.Abort()
					return
				}

				// 重新设置请求体
				c.Request.Body = io.NopCloser(strings.NewReader(c.Request.FormValue("json")))
			}
		}

		c.Next()
	}
}
