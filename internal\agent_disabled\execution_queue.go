package agent

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ExecutionQueue 执行队列
type ExecutionQueue struct {
	maxConcurrent   int
	logger          *logrus.Logger
	queue           []*QueuedExecution
	activeJobs      map[string]*ActiveJob
	completedJobs   map[string]*ExecutionResult
	totalExecutions int64
	mutex           sync.RWMutex
	workerPool      chan struct{}
	stop<PERSON>han        chan struct{}
	isRunning       bool
}

// QueuedExecution 队列中的执行任务
type QueuedExecution struct {
	Request  *ExecutionRequest
	ResultCh chan *ExecutionResult
	ErrorCh  chan error
	QueuedAt time.Time
	Priority int
}

// ActiveJob 活跃的任务
type ActiveJob struct {
	Request   *ExecutionRequest
	StartTime time.Time
	Cancel    context.CancelFunc
}

// NewExecutionQueue 创建执行队列
func NewExecutionQueue(maxConcurrent int, logger *logrus.Logger) *ExecutionQueue {
	if maxConcurrent <= 0 {
		maxConcurrent = 5
	}

	return &ExecutionQueue{
		maxConcurrent:   maxConcurrent,
		logger:          logger,
		queue:           make([]*QueuedExecution, 0),
		activeJobs:      make(map[string]*ActiveJob),
		completedJobs:   make(map[string]*ExecutionResult),
		totalExecutions: 0,
		workerPool:      make(chan struct{}, maxConcurrent),
		stopChan:        make(chan struct{}),
		isRunning:       false,
	}
}

// Start 启动执行队列
func (eq *ExecutionQueue) Start(ctx context.Context) error {
	eq.mutex.Lock()
	defer eq.mutex.Unlock()

	if eq.isRunning {
		return nil
	}

	eq.logger.Info("Starting execution queue")
	eq.isRunning = true

	// 启动工作协程
	go eq.processQueue(ctx)

	return nil
}

// Stop 停止执行队列
func (eq *ExecutionQueue) Stop(ctx context.Context) error {
	eq.mutex.Lock()
	defer eq.mutex.Unlock()

	if !eq.isRunning {
		return nil
	}

	eq.logger.Info("Stopping execution queue")
	eq.isRunning = false

	// 取消所有活跃任务
	for _, job := range eq.activeJobs {
		if job.Cancel != nil {
			job.Cancel()
		}
	}

	close(eq.stopChan)

	return nil
}

// Submit 提交执行请求
func (eq *ExecutionQueue) Submit(ctx context.Context, req *ExecutionRequest) (*ExecutionResult, error) {
	if !eq.isRunning {
		return nil, fmt.Errorf("execution queue is not running")
	}

	// 创建队列项
	queuedExec := &QueuedExecution{
		Request:  req,
		ResultCh: make(chan *ExecutionResult, 1),
		ErrorCh:  make(chan error, 1),
		QueuedAt: time.Now(),
		Priority: req.Priority,
	}

	// 添加到队列
	eq.mutex.Lock()
	eq.queue = append(eq.queue, queuedExec)
	eq.sortQueue()
	eq.mutex.Unlock()

	eq.logger.WithFields(logrus.Fields{
		"request_id": req.ID,
		"priority":   req.Priority,
		"queue_size": len(eq.queue),
	}).Info("Request queued for execution")

	// 等待结果
	select {
	case result := <-queuedExec.ResultCh:
		return result, nil
	case err := <-queuedExec.ErrorCh:
		return nil, err
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// SubmitAsync 异步提交执行请求
func (eq *ExecutionQueue) SubmitAsync(req *ExecutionRequest) error {
	if !eq.isRunning {
		return fmt.Errorf("execution queue is not running")
	}

	// 创建队列项
	queuedExec := &QueuedExecution{
		Request:  req,
		ResultCh: make(chan *ExecutionResult, 1),
		ErrorCh:  make(chan error, 1),
		QueuedAt: time.Now(),
		Priority: req.Priority,
	}

	// 添加到队列
	eq.mutex.Lock()
	eq.queue = append(eq.queue, queuedExec)
	eq.sortQueue()
	eq.mutex.Unlock()

	eq.logger.WithFields(logrus.Fields{
		"request_id": req.ID,
		"priority":   req.Priority,
		"queue_size": len(eq.queue),
	}).Info("Request queued for async execution")

	return nil
}

// processQueue 处理队列
func (eq *ExecutionQueue) processQueue(ctx context.Context) {
	ticker := time.NewTicker(100 * time.Millisecond)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-eq.stopChan:
			return
		case <-ticker.C:
			eq.processNextJob(ctx)
		}
	}
}

// processNextJob 处理下一个任务
func (eq *ExecutionQueue) processNextJob(ctx context.Context) {
	eq.mutex.Lock()

	// 检查是否有可用的工作槽位
	if len(eq.activeJobs) >= eq.maxConcurrent {
		eq.mutex.Unlock()
		return
	}

	// 检查队列是否为空
	if len(eq.queue) == 0 {
		eq.mutex.Unlock()
		return
	}

	// 获取下一个任务
	queuedExec := eq.queue[0]
	eq.queue = eq.queue[1:]

	eq.mutex.Unlock()

	// 启动任务执行
	go eq.executeJob(ctx, queuedExec)
}

// executeJob 执行任务
func (eq *ExecutionQueue) executeJob(ctx context.Context, queuedExec *QueuedExecution) {
	req := queuedExec.Request

	// 获取工作槽位
	eq.workerPool <- struct{}{}
	defer func() { <-eq.workerPool }()

	// 创建执行上下文
	jobCtx, cancel := context.WithTimeout(ctx, req.Timeout)
	defer cancel()

	// 添加到活跃任务
	eq.mutex.Lock()
	eq.activeJobs[req.ID] = &ActiveJob{
		Request:   req,
		StartTime: time.Now(),
		Cancel:    cancel,
	}
	eq.totalExecutions++
	eq.mutex.Unlock()

	eq.logger.WithFields(logrus.Fields{
		"request_id": req.ID,
		"command":    req.Command,
		"user_id":    req.UserID,
	}).Info("Starting job execution")

	// 执行命令
	result, err := eq.executeCommand(jobCtx, req)

	// 从活跃任务中移除
	eq.mutex.Lock()
	delete(eq.activeJobs, req.ID)
	if result != nil {
		eq.completedJobs[req.ID] = result
	}
	eq.mutex.Unlock()

	// 发送结果
	if err != nil {
		select {
		case queuedExec.ErrorCh <- err:
		default:
		}
	} else {
		select {
		case queuedExec.ResultCh <- result:
		default:
		}
	}

	eq.logger.WithFields(logrus.Fields{
		"request_id": req.ID,
		"success":    err == nil,
		"duration":   time.Since(eq.activeJobs[req.ID].StartTime),
	}).Info("Job execution completed")
}

// executeCommand 执行命令（简化实现）
func (eq *ExecutionQueue) executeCommand(ctx context.Context, req *ExecutionRequest) (*ExecutionResult, error) {
	startTime := time.Now()

	// 这里应该调用实际的命令执行逻辑
	// 为了简化，我们模拟一个执行过程

	// 模拟执行时间
	select {
	case <-time.After(time.Duration(100) * time.Millisecond):
	case <-ctx.Done():
		return nil, ctx.Err()
	}

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// 创建模拟结果
	result := &ExecutionResult{
		ID:        generateResultID(),
		RequestID: req.ID,
		Success:   true,
		ExitCode:  0,
		Stdout:    fmt.Sprintf("Command '%s' executed successfully", req.Command),
		Stderr:    "",
		Duration:  duration,
		StartTime: startTime,
		EndTime:   endTime,
		Metadata:  make(map[string]interface{}),
	}

	return result, nil
}

// sortQueue 按优先级排序队列
func (eq *ExecutionQueue) sortQueue() {
	sort.Slice(eq.queue, func(i, j int) bool {
		// 优先级高的在前面
		if eq.queue[i].Priority != eq.queue[j].Priority {
			return eq.queue[i].Priority > eq.queue[j].Priority
		}
		// 优先级相同时，先入队的在前面
		return eq.queue[i].QueuedAt.Before(eq.queue[j].QueuedAt)
	})
}

// GetQueueSize 获取队列大小
func (eq *ExecutionQueue) GetQueueSize() int {
	eq.mutex.RLock()
	defer eq.mutex.RUnlock()

	return len(eq.queue)
}

// GetActiveJobs 获取活跃任务数量
func (eq *ExecutionQueue) GetActiveJobs() int {
	eq.mutex.RLock()
	defer eq.mutex.RUnlock()

	return len(eq.activeJobs)
}

// GetTotalExecutions 获取总执行次数
func (eq *ExecutionQueue) GetTotalExecutions() int64 {
	eq.mutex.RLock()
	defer eq.mutex.RUnlock()

	return eq.totalExecutions
}

// GetQueueStatus 获取队列状态
func (eq *ExecutionQueue) GetQueueStatus() *QueueStatus {
	eq.mutex.RLock()
	defer eq.mutex.RUnlock()

	queuedJobs := make([]*QueuedJobInfo, len(eq.queue))
	for i, queuedExec := range eq.queue {
		queuedJobs[i] = &QueuedJobInfo{
			RequestID: queuedExec.Request.ID,
			Command:   queuedExec.Request.Command,
			Priority:  queuedExec.Priority,
			QueuedAt:  queuedExec.QueuedAt,
			WaitTime:  time.Since(queuedExec.QueuedAt),
		}
	}

	activeJobsInfo := make([]*ActiveJobInfo, 0, len(eq.activeJobs))
	for _, job := range eq.activeJobs {
		activeJobsInfo = append(activeJobsInfo, &ActiveJobInfo{
			RequestID: job.Request.ID,
			Command:   job.Request.Command,
			StartTime: job.StartTime,
			Duration:  time.Since(job.StartTime),
		})
	}

	return &QueueStatus{
		IsRunning:       eq.isRunning,
		MaxConcurrent:   eq.maxConcurrent,
		QueueSize:       len(eq.queue),
		ActiveJobs:      len(eq.activeJobs),
		TotalExecutions: eq.totalExecutions,
		QueuedJobs:      queuedJobs,
		ActiveJobsInfo:  activeJobsInfo,
	}
}

// CancelJob 取消任务
func (eq *ExecutionQueue) CancelJob(requestID string) error {
	eq.mutex.Lock()
	defer eq.mutex.Unlock()

	// 检查活跃任务
	if job, exists := eq.activeJobs[requestID]; exists {
		if job.Cancel != nil {
			job.Cancel()
		}
		delete(eq.activeJobs, requestID)
		eq.logger.WithField("request_id", requestID).Info("Active job cancelled")
		return nil
	}

	// 检查队列中的任务
	for i, queuedExec := range eq.queue {
		if queuedExec.Request.ID == requestID {
			// 从队列中移除
			eq.queue = append(eq.queue[:i], eq.queue[i+1:]...)

			// 发送取消错误
			select {
			case queuedExec.ErrorCh <- fmt.Errorf("job cancelled"):
			default:
			}

			eq.logger.WithField("request_id", requestID).Info("Queued job cancelled")
			return nil
		}
	}

	return fmt.Errorf("job not found: %s", requestID)
}

// QueueStatus 队列状态
type QueueStatus struct {
	IsRunning       bool             `json:"is_running"`
	MaxConcurrent   int              `json:"max_concurrent"`
	QueueSize       int              `json:"queue_size"`
	ActiveJobs      int              `json:"active_jobs"`
	TotalExecutions int64            `json:"total_executions"`
	QueuedJobs      []*QueuedJobInfo `json:"queued_jobs"`
	ActiveJobsInfo  []*ActiveJobInfo `json:"active_jobs_info"`
}

// QueuedJobInfo 队列中的任务信息
type QueuedJobInfo struct {
	RequestID string        `json:"request_id"`
	Command   string        `json:"command"`
	Priority  int           `json:"priority"`
	QueuedAt  time.Time     `json:"queued_at"`
	WaitTime  time.Duration `json:"wait_time"`
}

// ActiveJobInfo 活跃任务信息
type ActiveJobInfo struct {
	RequestID string        `json:"request_id"`
	Command   string        `json:"command"`
	StartTime time.Time     `json:"start_time"`
	Duration  time.Duration `json:"duration"`
}
