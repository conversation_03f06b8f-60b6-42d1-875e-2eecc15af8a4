/* AI运维管理平台 - 对话界面专用样式 */
/* 现代化AI对话体验，参考Claude、ChatGPT等顶级应用 */

/* ========================================
   对话界面布局系统
   ======================================== */

.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-primary-50) 100%);
  overflow: hidden;
}

/* 顶部导航栏 */
.chat-navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(var(--blur-lg)) saturate(180%);
  border-bottom: 1px solid var(--color-gray-200);
  padding: var(--spacing-4) var(--spacing-6);
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: var(--z-sticky);
  box-shadow: var(--shadow-sm);
}

.chat-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-weight: var(--font-weight-bold);
  font-size: var(--font-size-xl);
  color: var(--color-gray-900);
  text-decoration: none;
  transition: all var(--transition-base);
}

.chat-brand:hover {
  color: var(--color-primary-600);
  transform: translateY(-1px);
}

.brand-icon {
  width: var(--spacing-9);
  height: var(--spacing-9);
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 50%, var(--color-primary-700) 100%);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-lg);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.brand-icon::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255,255,255,0.2) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform var(--transition-slow);
}

.chat-brand:hover .brand-icon::before {
  transform: translateX(100%);
}

.chat-actions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.user-menu {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2_5) var(--spacing-4);
  border-radius: var(--radius-xl);
  background: var(--color-gray-100);
  color: var(--color-gray-700);
  text-decoration: none;
  transition: all var(--transition-base);
  cursor: pointer;
  border: 1px solid transparent;
  font-weight: var(--font-weight-medium);
}

.user-menu:hover {
  background: var(--color-gray-200);
  color: var(--color-gray-900);
  border-color: var(--color-gray-300);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.user-avatar {
  width: var(--spacing-8);
  height: var(--spacing-8);
  background: linear-gradient(135deg, var(--color-primary-400) 0%, var(--color-primary-600) 100%);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  box-shadow: var(--shadow-sm);
}

/* ========================================
   主对话区域
   ======================================== */

.chat-main {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: flex-start;
  overflow: hidden;
  padding: var(--spacing-6);
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.chat-area {
  width: 100%;
  max-width: 900px;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: var(--radius-2xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid var(--color-gray-200);
  position: relative;
  height: calc(100vh - 140px);
}

.chat-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-primary-200), transparent);
}

/* 对话头部 */
.chat-header {
  padding: var(--spacing-8) var(--spacing-10) var(--spacing-6);
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-gray-50) 100%);
  border-bottom: 1px solid var(--color-gray-100);
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: var(--spacing-4);
}

.chat-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: var(--spacing-10);
  right: var(--spacing-10);
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-gray-200), transparent);
}

.chat-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-2);
  letter-spacing: var(--letter-spacing-tight);
  line-height: var(--line-height-tight);
}

.chat-subtitle {
  color: var(--color-gray-600);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
}

.chat-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
}

.session-info {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  text-align: right;
}

/* ========================================
   消息区域
   ======================================== */

.chat-messages {
  flex: 1;
  padding: var(--spacing-8) var(--spacing-6);
  overflow-y: auto;
  scroll-behavior: smooth;
  position: relative;
  background: linear-gradient(180deg, var(--color-gray-50) 0%, var(--color-primary-50) 100%);
}

.chat-messages::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: var(--spacing-4);
  background: linear-gradient(180deg, var(--color-gray-50) 0%, transparent 100%);
  pointer-events: none;
  z-index: 1;
}

.chat-messages::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: var(--spacing-4);
  background: linear-gradient(0deg, var(--color-primary-50) 0%, transparent 100%);
  pointer-events: none;
  z-index: 1;
}

/* 自定义滚动条 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
  border-radius: var(--radius-base);
  margin: var(--spacing-4) 0;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: rgba(99, 102, 241, 0.3);
  border-radius: var(--radius-base);
  transition: all var(--transition-base);
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: rgba(99, 102, 241, 0.5);
}

/* ========================================
   欢迎界面
   ======================================== */

.welcome-screen {
  text-align: center;
  padding: var(--spacing-16) var(--spacing-12);
  max-width: 700px;
  margin: 0 auto;
  animation: fadeInUp var(--duration-700) var(--ease-out);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(var(--spacing-8));
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.welcome-avatar {
  width: var(--spacing-24);
  height: var(--spacing-24);
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 50%, var(--color-primary-700) 100%);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-10);
  color: white;
  font-size: var(--font-size-4xl);
  box-shadow: var(--shadow-2xl);
  position: relative;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    box-shadow: var(--shadow-2xl), 0 0 0 0 rgba(99, 102, 241, 0.4);
  }
  50% {
    box-shadow: var(--shadow-2xl), 0 0 0 var(--spacing-5) rgba(99, 102, 241, 0);
  }
}

.welcome-title {
  font-size: var(--font-size-4xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-6);
  letter-spacing: var(--letter-spacing-tight);
  line-height: var(--line-height-tight);
}

.welcome-description {
  font-size: var(--font-size-xl);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-12);
  line-height: var(--line-height-relaxed);
  font-weight: var(--font-weight-normal);
}

/* ========================================
   功能卡片
   ======================================== */

.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--spacing-5);
  margin-bottom: var(--spacing-12);
}

.feature-card {
  padding: var(--spacing-8) var(--spacing-6);
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  text-decoration: none;
  color: var(--color-gray-700);
  transition: all var(--transition-slow);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.feature-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary-50), var(--color-primary-100));
  opacity: 0;
  transition: opacity var(--transition-slow);
}

.feature-card:hover {
  border-color: var(--color-primary-300);
  color: var(--color-primary-700);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.feature-card:hover::before {
  opacity: 1;
}

.feature-icon {
  width: var(--spacing-14);
  height: var(--spacing-14);
  background: var(--color-gray-100);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-3xl);
  transition: all var(--transition-slow);
  position: relative;
  z-index: var(--z-10);
}

.feature-card:hover .feature-icon {
  background: var(--color-primary-500);
  color: white;
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.feature-title {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  position: relative;
  z-index: var(--z-10);
}

.feature-desc {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  text-align: center;
  line-height: var(--line-height-relaxed);
  position: relative;
  z-index: var(--z-10);
}

.feature-example {
  font-size: var(--font-size-xs);
  color: var(--color-primary-600);
  font-style: italic;
  background: var(--color-primary-50);
  padding: var(--spacing-1) var(--spacing-2);
  border-radius: var(--radius-md);
  position: relative;
  z-index: var(--z-10);
}

/* ========================================
   消息样式系统
   ======================================== */

.message {
  display: flex;
  margin-bottom: var(--spacing-6);
  animation: messageSlideIn var(--duration-700) var(--ease-out);
  position: relative;
  z-index: 2;
  isolation: isolate;
}

@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(var(--spacing-8)) scale(0.95);
    filter: blur(2px);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
    filter: blur(0);
  }
}

/* 用户消息 */
.message.user {
  justify-content: flex-end;
  padding-left: 15%;
}

/* AI助手消息 */
.message.assistant {
  justify-content: flex-start;
  padding-right: 15%;
}

/* 消息头像 */
.message-avatar {
  width: var(--spacing-10);
  height: var(--spacing-10);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  margin: 0 var(--spacing-3);
  flex-shrink: 0;
  box-shadow: var(--shadow-lg);
  position: relative;
  transition: all var(--transition-base);
}

.message-avatar::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  border-radius: var(--radius-full);
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.3), transparent);
  opacity: 0;
  transition: opacity var(--transition-base);
}

.message:hover .message-avatar::before {
  opacity: 1;
}

/* 用户头像 */
.message.user .message-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  order: 1;
  margin-left: var(--spacing-3);
  margin-right: 0;
}

/* AI助手头像 */
.message.assistant .message-avatar {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
  margin-right: var(--spacing-3);
  margin-left: 0;
}

/* 消息气泡 */
.message-content {
  max-width: 70%;
  padding: var(--spacing-4) var(--spacing-5);
  border-radius: var(--radius-2xl);
  font-size: var(--font-size-base);
  line-height: var(--line-height-relaxed);
  word-wrap: break-word;
  position: relative;
  transition: all var(--transition-base);
  backdrop-filter: blur(var(--blur-sm));
  border: 1px solid transparent;
  z-index: 1;
}

.message-content::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  background: linear-gradient(45deg, transparent, rgba(255,255,255,0.1), transparent);
  opacity: 0;
  transition: opacity var(--transition-base);
  z-index: -1;
}

.message-content:hover::before {
  opacity: 1;
}

.message-content:hover {
  transform: translateY(-2px) scale(1.01);
  box-shadow: var(--shadow-2xl);
}

/* 用户消息气泡 */
.message.user .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-bottom-right-radius: var(--radius-md);
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.message.user .message-content::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: -8px;
  width: 0;
  height: 0;
  border-left: 8px solid #764ba2;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}

/* AI助手消息气泡 */
.message.assistant .message-content {
  background: rgba(255, 255, 255, 0.98);
  color: var(--color-gray-800);
  border: 1px solid var(--color-gray-200);
  border-bottom-left-radius: var(--radius-md);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
  backdrop-filter: blur(var(--blur-lg));
  position: relative;
  z-index: 2;
}

.message.assistant .message-content::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -8px;
  width: 0;
  height: 0;
  border-right: 8px solid rgba(255, 255, 255, 0.98);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  z-index: 1;
}

/* 消息时间戳 */
.message-time {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  margin-top: var(--spacing-1_5);
  opacity: 0.7;
  transition: opacity var(--transition-base);
}

.message:hover .message-time {
  opacity: 1;
}

.message.user .message-time {
  text-align: right;
  color: rgba(255, 255, 255, 0.8);
}

.message.assistant .message-time {
  text-align: left;
  color: var(--color-gray-500);
}

/* ========================================
   打字指示器
   ======================================== */

.typing-indicator {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-4) var(--spacing-5);
  background: rgba(255, 255, 255, 0.95);
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-2xl);
  border-bottom-left-radius: var(--radius-md);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  backdrop-filter: blur(var(--blur-lg));
  animation: messageSlideIn var(--duration-700) var(--ease-out);
  position: relative;
}

.typing-indicator::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: -8px;
  width: 0;
  height: 0;
  border-right: 8px solid rgba(255, 255, 255, 0.95);
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
}

.typing-text {
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-right: var(--spacing-1);
}

.typing-dots {
  display: flex;
  gap: var(--spacing-1_5);
  align-items: center;
}

.typing-dot {
  width: var(--spacing-1_5);
  height: var(--spacing-1_5);
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-radius: var(--radius-full);
  animation: typingPulse 1.6s infinite ease-in-out;
  box-shadow: 0 2px 8px rgba(240, 147, 251, 0.3);
}

.typing-dot:nth-child(1) { animation-delay: 0s; }
.typing-dot:nth-child(2) { animation-delay: 0.3s; }
.typing-dot:nth-child(3) { animation-delay: 0.6s; }

@keyframes typingPulse {
  0%, 70%, 100% {
    transform: scale(1) translateY(0);
    opacity: 0.5;
  }
  35% {
    transform: scale(1.3) translateY(-4px);
    opacity: 1;
  }
}

/* ========================================
   状态消息和错误提示
   ======================================== */

/* 状态消息样式 */
.status-message {
  display: flex;
  justify-content: center;
  margin: var(--spacing-2) 0;
  animation: fadeInUp var(--duration-300) var(--ease-out);
}

.status-message.info .status-content {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  color: var(--color-blue-600);
}

.status-message.warning .status-content {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
  color: var(--color-yellow-600);
}

.status-message.error .status-content {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: var(--color-red-600);
}

.status-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  backdrop-filter: blur(var(--blur-sm));
}

/* 错误消息样式 */
.message.error .message-content {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(220, 38, 38, 0.05) 100%);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: var(--color-red-700);
}

.error-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-1);
}

.error-content strong {
  color: var(--color-red-800);
  font-weight: var(--font-weight-semibold);
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ========================================
   输入区域
   ======================================== */

.chat-input-area {
  padding: var(--spacing-8) var(--spacing-10);
  background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-primary-50) 100%);
  border-top: 1px solid var(--color-gray-100);
  position: relative;
}

.chat-input-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: var(--spacing-10);
  right: var(--spacing-10);
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--color-gray-200), transparent);
}

.chat-input-container {
  max-width: 900px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  background: white;
  border: 2px solid var(--color-gray-200);
  border-radius: var(--radius-2xl);
  padding: var(--spacing-4) var(--spacing-5);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.input-wrapper::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary-50), var(--color-primary-100));
  opacity: 0;
  transition: opacity var(--transition-base);
}

.input-wrapper:focus-within {
  border-color: var(--color-primary-500);
  box-shadow: var(--shadow-lg), 0 0 0 4px var(--color-primary-100);
  transform: translateY(-2px);
}

.input-wrapper:focus-within::before {
  opacity: 0.5;
}

.chat-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: var(--font-size-lg);
  color: var(--color-gray-900);
  background: transparent;
  resize: none;
  min-height: var(--spacing-6);
  max-height: var(--spacing-32);
  line-height: var(--line-height-normal);
  font-weight: var(--font-weight-normal);
  position: relative;
  z-index: var(--z-10);
  font-family: var(--font-family-sans);
}

.chat-input::placeholder {
  color: var(--color-gray-400);
  font-weight: var(--font-weight-normal);
}

.send-button {
  width: var(--spacing-12);
  height: var(--spacing-12);
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  border: none;
  border-radius: var(--radius-full);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-base);
  margin-left: var(--spacing-3);
  box-shadow: var(--shadow-md);
  position: relative;
  z-index: var(--z-10);
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  transform: scale(1.05);
  box-shadow: var(--shadow-lg);
}

.send-button:active:not(:disabled) {
  transform: scale(0.95);
}

.send-button:disabled {
  background: var(--color-gray-300);
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--shadow-sm);
}

/* 输入建议 */
.input-suggestions {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  margin-top: var(--spacing-6);
  flex-wrap: wrap;
  justify-content: center;
}

.suggestion-label {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  font-weight: var(--font-weight-semibold);
  letter-spacing: var(--letter-spacing-wide);
}

.suggestion-btn {
  padding: var(--spacing-2_5) var(--spacing-5);
  background: white;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-xl);
  color: var(--color-gray-600);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-base);
  box-shadow: var(--shadow-xs);
  position: relative;
  overflow: hidden;
}

.suggestion-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary-50), var(--color-primary-100));
  opacity: 0;
  transition: opacity var(--transition-base);
}

.suggestion-btn:hover {
  border-color: var(--color-primary-400);
  color: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.suggestion-btn:hover::before {
  opacity: 1;
}

.suggestion-btn span {
  position: relative;
  z-index: var(--z-10);
}

/* ========================================
   快速操作按钮
   ======================================== */

.quick-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: var(--spacing-5);
  margin-bottom: var(--spacing-12);
}

.quick-action-btn {
  padding: var(--spacing-8) var(--spacing-6);
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  text-decoration: none;
  color: var(--color-gray-700);
  transition: all var(--transition-slow);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-4);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  box-shadow: var(--shadow-sm);
}

.quick-action-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--color-primary-50), var(--color-primary-100));
  opacity: 0;
  transition: opacity var(--transition-slow);
}

.quick-action-btn:hover {
  border-color: var(--color-primary-300);
  color: var(--color-primary-700);
  transform: translateY(-4px) scale(1.02);
  box-shadow: var(--shadow-xl);
}

.quick-action-btn:hover::before {
  opacity: 1;
}

.quick-action-icon {
  width: var(--spacing-14);
  height: var(--spacing-14);
  background: var(--color-gray-100);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: var(--font-size-3xl);
  transition: all var(--transition-slow);
  position: relative;
  z-index: var(--z-10);
}

.quick-action-btn:hover .quick-action-icon {
  background: var(--color-primary-500);
  color: white;
  transform: scale(1.1);
  box-shadow: var(--shadow-lg);
}

.quick-action-text {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-base);
  position: relative;
  z-index: var(--z-10);
}

/* ========================================
   响应式设计系统 - 完善版
   ======================================== */

/* 超大屏幕优化 (2K/4K显示器) */
@media (min-width: 1920px) {
  .chat-container {
    max-width: 1800px;
    margin: 0 auto;
  }

  .chat-area {
    max-width: 1200px;
  }

  .chat-title {
    font-size: var(--font-size-5xl);
  }

  .welcome-title {
    font-size: var(--font-size-6xl);
  }

  .feature-cards {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--spacing-8);
  }

  .quick-actions {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* 大屏幕优化 (桌面端) */
@media (min-width: 1280px) and (max-width: 1919px) {
  .chat-area {
    max-width: 1000px;
  }

  .feature-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-6);
  }

  .quick-actions {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 中等屏幕优化 (笔记本电脑) */
@media (min-width: 1024px) and (max-width: 1279px) {
  .chat-main {
    padding: var(--spacing-4);
  }

  .chat-area {
    max-width: 900px;
    height: calc(100vh - 120px);
  }

  .chat-header {
    padding: var(--spacing-6) var(--spacing-8) var(--spacing-4);
  }

  .chat-title {
    font-size: var(--font-size-2xl);
  }

  .feature-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-5);
  }

  .quick-actions {
    grid-template-columns: repeat(4, 1fr);
  }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1023px) {
  .chat-navbar {
    padding: var(--spacing-3) var(--spacing-5);
  }

  .chat-brand {
    font-size: var(--font-size-lg);
  }

  .brand-icon {
    width: var(--spacing-8);
    height: var(--spacing-8);
    font-size: var(--font-size-base);
  }

  .chat-main {
    padding: var(--spacing-3);
  }

  .chat-area {
    border-radius: var(--radius-xl);
    height: calc(100vh - 110px);
  }

  .chat-header {
    padding: var(--spacing-5) var(--spacing-6) var(--spacing-4);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-3);
  }

  .chat-header-right {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chat-title {
    font-size: var(--font-size-xl);
  }

  .chat-subtitle {
    font-size: var(--font-size-sm);
  }

  .chat-messages {
    padding: var(--spacing-5);
  }

  .welcome-screen {
    padding: var(--spacing-8) var(--spacing-4);
  }

  .welcome-title {
    font-size: var(--font-size-3xl);
  }

  .welcome-description {
    font-size: var(--font-size-lg);
  }

  .feature-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-4);
  }

  .quick-actions {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--spacing-3);
  }

  .quick-action-btn {
    padding: var(--spacing-5) var(--spacing-3);
  }

  .quick-action-icon {
    width: var(--spacing-10);
    height: var(--spacing-10);
    font-size: var(--font-size-xl);
  }

  .chat-input-area {
    padding: var(--spacing-5);
  }

  .input-wrapper {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .chat-input {
    font-size: var(--font-size-base);
  }

  .send-button {
    width: var(--spacing-10);
    height: var(--spacing-10);
    margin-left: var(--spacing-2);
  }

  /* 消息样式调整 */
  .message {
    margin-bottom: var(--spacing-4);
  }

  .message.user {
    padding-left: 15%;
  }

  .message.assistant {
    padding-right: 15%;
  }

  .message-content {
    max-width: 75%;
    padding: var(--spacing-3) var(--spacing-4);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-xl);
  }

  .message-avatar {
    width: var(--spacing-9);
    height: var(--spacing-9);
    margin: 0 var(--spacing-2);
    font-size: var(--font-size-xs);
  }
}

/* 移动端优化 (大屏手机) */
@media (min-width: 480px) and (max-width: 767px) {
  .chat-navbar {
    padding: var(--spacing-3) var(--spacing-4);
  }

  .chat-brand {
    font-size: var(--font-size-base);
  }

  .brand-icon {
    width: var(--spacing-7);
    height: var(--spacing-7);
    font-size: var(--font-size-sm);
  }

  .user-menu {
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-sm);
  }

  .user-avatar {
    width: var(--spacing-6);
    height: var(--spacing-6);
    font-size: var(--font-size-xs);
  }

  .chat-main {
    padding: var(--spacing-2);
  }

  .chat-area {
    border-radius: var(--radius-lg);
    height: calc(100vh - 100px);
  }

  .chat-header {
    padding: var(--spacing-4) var(--spacing-4) var(--spacing-3);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .chat-header-right {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .chat-controls {
    display: flex;
    gap: var(--spacing-2);
    width: 100%;
  }

  .chat-controls .btn {
    flex: 1;
    font-size: var(--font-size-xs);
    padding: var(--spacing-2) var(--spacing-3);
  }

  .chat-title {
    font-size: var(--font-size-lg);
  }

  .chat-subtitle {
    font-size: var(--font-size-xs);
    line-height: var(--line-height-normal);
  }

  .chat-messages {
    padding: var(--spacing-4) var(--spacing-3);
  }

  .welcome-screen {
    padding: var(--spacing-6) var(--spacing-3);
  }

  .welcome-avatar {
    width: var(--spacing-16);
    height: var(--spacing-16);
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-6);
  }

  .welcome-title {
    font-size: var(--font-size-2xl);
    margin-bottom: var(--spacing-4);
  }

  .welcome-description {
    font-size: var(--font-size-base);
    margin-bottom: var(--spacing-8);
  }

  .feature-cards {
    grid-template-columns: 1fr;
    gap: var(--spacing-3);
    margin-bottom: var(--spacing-8);
  }

  .feature-card {
    padding: var(--spacing-5) var(--spacing-4);
  }

  .feature-icon {
    width: var(--spacing-10);
    height: var(--spacing-10);
    font-size: var(--font-size-xl);
  }

  .feature-title {
    font-size: var(--font-size-sm);
  }

  .feature-desc {
    font-size: var(--font-size-xs);
  }

  .feature-example {
    font-size: 0.6875rem; /* 11px */
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-2);
  }

  .quick-action-btn {
    padding: var(--spacing-4) var(--spacing-2);
  }

  .quick-action-icon {
    width: var(--spacing-8);
    height: var(--spacing-8);
    font-size: var(--font-size-lg);
  }

  .quick-action-text {
    font-size: var(--font-size-xs);
  }

  .chat-input-area {
    padding: var(--spacing-4);
  }

  .input-wrapper {
    padding: var(--spacing-2_5) var(--spacing-3);
  }

  .chat-input {
    font-size: var(--font-size-sm);
  }

  .send-button {
    width: var(--spacing-9);
    height: var(--spacing-9);
    margin-left: var(--spacing-2);
  }

  .input-suggestions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-2);
    margin-top: var(--spacing-3);
  }

  .suggestion-btn {
    text-align: center;
    padding: var(--spacing-2) var(--spacing-3);
    font-size: var(--font-size-xs);
  }

  /* 消息样式优化 */
  .message {
    margin-bottom: var(--spacing-3);
  }

  .message.user {
    padding-left: 8%;
  }

  .message.assistant {
    padding-right: 8%;
  }

  .message-content {
    max-width: 85%;
    padding: var(--spacing-2_5) var(--spacing-3);
    font-size: var(--font-size-sm);
    border-radius: var(--radius-lg);
  }

  .message.user .message-content {
    border-bottom-right-radius: var(--radius-sm);
  }

  .message.assistant .message-content {
    border-bottom-left-radius: var(--radius-sm);
  }

  .message-avatar {
    width: var(--spacing-8);
    height: var(--spacing-8);
    margin: 0 var(--spacing-1_5);
    font-size: var(--font-size-xs);
  }

  .message-time {
    font-size: 0.6875rem; /* 11px */
    margin-top: var(--spacing-1);
  }

  /* 打字指示器优化 */
  .typing-indicator {
    padding: var(--spacing-2_5) var(--spacing-3);
  }

  .typing-text {
    font-size: var(--font-size-xs);
  }

  .typing-dot {
    width: var(--spacing-1);
    height: var(--spacing-1);
  }
}

/* 小屏手机优化 */
@media (max-width: 479px) {
  .chat-navbar {
    padding: var(--spacing-2_5) var(--spacing-3);
  }

  .chat-brand {
    font-size: var(--font-size-sm);
    gap: var(--spacing-2);
  }

  .brand-icon {
    width: var(--spacing-6);
    height: var(--spacing-6);
    font-size: var(--font-size-xs);
  }

  .user-menu {
    padding: var(--spacing-1_5) var(--spacing-2);
    font-size: var(--font-size-xs);
  }

  .user-avatar {
    width: var(--spacing-5);
    height: var(--spacing-5);
    font-size: 0.6875rem; /* 11px */
  }

  .chat-main {
    padding: var(--spacing-1);
  }

  .chat-area {
    border-radius: var(--radius-md);
    height: calc(100vh - 90px);
  }

  .chat-header {
    padding: var(--spacing-3) var(--spacing-3) var(--spacing-2);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .chat-header-right {
    width: 100%;
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-2);
  }

  .chat-controls {
    display: flex;
    gap: var(--spacing-1_5);
    width: 100%;
  }

  .chat-controls .btn {
    flex: 1;
    font-size: 0.6875rem; /* 11px */
    padding: var(--spacing-1_5) var(--spacing-2);
  }

  .chat-title {
    font-size: var(--font-size-base);
    line-height: var(--line-height-tight);
  }

  .chat-subtitle {
    font-size: 0.6875rem; /* 11px */
    line-height: var(--line-height-normal);
  }

  .session-info {
    font-size: 0.6875rem; /* 11px */
  }

  .chat-messages {
    padding: var(--spacing-3) var(--spacing-2);
  }

  .welcome-screen {
    padding: var(--spacing-4) var(--spacing-2);
  }

  .welcome-avatar {
    width: var(--spacing-12);
    height: var(--spacing-12);
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-4);
  }

  .welcome-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--spacing-3);
    line-height: var(--line-height-tight);
  }

  .welcome-description {
    font-size: var(--font-size-sm);
    margin-bottom: var(--spacing-6);
    line-height: var(--line-height-relaxed);
  }

  .feature-cards {
    grid-template-columns: 1fr;
    gap: var(--spacing-2);
    margin-bottom: var(--spacing-6);
  }

  .feature-card {
    padding: var(--spacing-4) var(--spacing-3);
    gap: var(--spacing-2);
  }

  .feature-icon {
    width: var(--spacing-8);
    height: var(--spacing-8);
    font-size: var(--font-size-lg);
  }

  .feature-title {
    font-size: var(--font-size-xs);
    font-weight: var(--font-weight-semibold);
  }

  .feature-desc {
    font-size: 0.6875rem; /* 11px */
    line-height: var(--line-height-relaxed);
  }

  .feature-example {
    font-size: 0.625rem; /* 10px */
    padding: var(--spacing-1) var(--spacing-1_5);
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--spacing-1_5);
  }

  .quick-action-btn {
    padding: var(--spacing-3) var(--spacing-1_5);
    gap: var(--spacing-2);
  }

  .quick-action-icon {
    width: var(--spacing-6);
    height: var(--spacing-6);
    font-size: var(--font-size-base);
  }

  .quick-action-text {
    font-size: 0.6875rem; /* 11px */
  }

  .chat-input-area {
    padding: var(--spacing-3);
  }

  .input-wrapper {
    padding: var(--spacing-2) var(--spacing-2_5);
  }

  .chat-input {
    font-size: var(--font-size-sm);
    min-height: var(--spacing-5);
  }

  .send-button {
    width: var(--spacing-8);
    height: var(--spacing-8);
    margin-left: var(--spacing-1_5);
    font-size: var(--font-size-xs);
  }

  .input-suggestions {
    flex-direction: column;
    align-items: stretch;
    gap: var(--spacing-1_5);
    margin-top: var(--spacing-2);
  }

  .suggestion-label {
    font-size: 0.6875rem; /* 11px */
  }

  .suggestion-btn {
    text-align: center;
    padding: var(--spacing-1_5) var(--spacing-2);
    font-size: 0.6875rem; /* 11px */
  }

  /* 消息样式优化 */
  .message {
    margin-bottom: var(--spacing-2_5);
  }

  .message.user {
    padding-left: 5%;
  }

  .message.assistant {
    padding-right: 5%;
  }

  .message-content {
    max-width: 90%;
    padding: var(--spacing-2) var(--spacing-2_5);
    font-size: var(--font-size-xs);
    border-radius: var(--radius-md);
    line-height: var(--line-height-relaxed);
  }

  .message.user .message-content {
    border-bottom-right-radius: var(--spacing-1);
  }

  .message.assistant .message-content {
    border-bottom-left-radius: var(--spacing-1);
  }

  .message-avatar {
    width: var(--spacing-6);
    height: var(--spacing-6);
    margin: 0 var(--spacing-1);
    font-size: 0.6875rem; /* 11px */
  }

  .message-time {
    font-size: 0.625rem; /* 10px */
    margin-top: var(--spacing-0_5);
  }

  /* 打字指示器优化 */
  .typing-indicator {
    padding: var(--spacing-2) var(--spacing-2_5);
    gap: var(--spacing-2);
  }

  .typing-text {
    font-size: 0.6875rem; /* 11px */
  }

  .typing-dot {
    width: 0.1875rem; /* 3px */
    height: 0.1875rem; /* 3px */
  }
}

/* ========================================
   触摸设备优化
   ======================================== */

/* 触摸目标大小优化 */
@media (pointer: coarse) {
  .btn,
  .feature-card,
  .quick-action-btn,
  .suggestion-btn,
  .send-button {
    min-height: 44px; /* iOS推荐的最小触摸目标 */
    min-width: 44px;
  }

  .chat-input {
    min-height: 44px;
  }

  .message-content {
    /* 增加触摸区域 */
    padding: var(--spacing-3) var(--spacing-4);
  }

  /* 触摸反馈增强 */
  .btn:active,
  .feature-card:active,
  .quick-action-btn:active,
  .suggestion-btn:active {
    transform: scale(0.98);
    transition: transform 0.1s ease-out;
  }
}

/* 悬停设备优化 */
@media (hover: hover) {
  .feature-card:hover,
  .quick-action-btn:hover,
  .suggestion-btn:hover {
    transform: translateY(-2px) scale(1.02);
  }

  .message-content:hover {
    transform: translateY(-1px) scale(1.005);
  }
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .welcome-avatar {
    animation: none !important;
  }

  .typing-dot {
    animation: none !important;
  }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
  .chat-area,
  .message-content,
  .input-wrapper {
    border-width: 2px;
    border-color: var(--color-gray-900);
  }

  .btn-primary {
    background: var(--color-gray-900);
    border-color: var(--color-gray-900);
  }

  .feature-card,
  .quick-action-btn {
    border-width: 2px;
    border-color: var(--color-gray-700);
  }
}

/* ========================================
   消息内容格式化样式
   ======================================== */

.message-content {
    line-height: 1.6;
    word-wrap: break-word;
}

.message-content .code-block {
    margin: 0.75rem 0;
    border-radius: var(--radius-lg);
    overflow: hidden;
    background: var(--color-gray-50);
    border: 1px solid var(--color-gray-200);
}

.message-content .code-block pre {
    margin: 0;
    padding: var(--spacing-4);
    background: transparent;
    color: var(--color-gray-800);
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: var(--font-size-sm);
    overflow-x: auto;
    line-height: 1.5;
}

.message-content .inline-code {
    background: var(--color-gray-100);
    color: var(--color-red-600);
    padding: 0.125rem 0.375rem;
    border-radius: var(--radius-md);
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.875em;
    font-weight: var(--font-weight-medium);
}

.message-content strong {
    font-weight: var(--font-weight-bold);
    color: var(--color-gray-900);
}

.message-content em {
    font-style: italic;
    color: var(--color-gray-700);
}

.message-content .emoji {
    font-size: 1.1em;
    margin: 0 0.125rem;
    display: inline-block;
}

/* 深色主题下的代码样式 */
[data-theme="dark"] .message-content .code-block {
    background: var(--color-gray-800);
    border-color: var(--color-gray-700);
}

[data-theme="dark"] .message-content .code-block pre {
    color: var(--color-gray-200);
}

[data-theme="dark"] .message-content .inline-code {
    background: var(--color-gray-700);
    color: var(--color-red-400);
}

/* ========================================
   修复视觉异常问题
   ======================================== */

/* 确保消息内容不会产生视觉重叠 */
.message-content {
    isolation: isolate;
    transform: translateZ(0);
    will-change: transform;
}

/* 修复可能的伪元素层级问题 */
.message-content::before,
.message-content::after {
    will-change: opacity;
}

/* 确保打字指示器不会产生视觉冲突 */
.typing-indicator {
    isolation: isolate;
    transform: translateZ(0);
}

/* ========================================
   工作流相关样式
   ======================================== */

/* 工作流指示器 */
.workflow-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1000;
    background: linear-gradient(135deg, var(--color-primary-500), var(--color-primary-600));
    color: white;
    border-radius: var(--radius-lg);
    padding: var(--spacing-3) var(--spacing-4);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(var(--blur-lg));
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all var(--transition-base);
    animation: slideInRight var(--duration-500) var(--ease-out);
}

.workflow-indicator.hidden {
    transform: translateX(100%);
    opacity: 0;
    pointer-events: none;
}

.workflow-status {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    font-weight: var(--font-weight-medium);
}

.workflow-icon {
    animation: spin 2s linear infinite;
    color: var(--color-warning-300);
}

.workflow-cancel {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: var(--radius-full);
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    cursor: pointer;
    transition: all var(--transition-base);
}

.workflow-cancel:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

/* 工作流建议 */
.workflow-suggestion {
    margin: var(--spacing-4) 0;
    padding: var(--spacing-4);
    background: linear-gradient(135deg, var(--color-warning-50), var(--color-warning-100));
    border: 1px solid var(--color-warning-200);
    border-radius: var(--radius-lg);
    animation: slideInUp var(--duration-500) var(--ease-out);
}

.suggestion-content {
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
}

.suggestion-content i {
    font-size: var(--font-size-lg);
}

.suggestion-content .btn {
    font-size: var(--font-size-xs);
    padding: var(--spacing-1) var(--spacing-3);
}

/* 系统消息 */
.system-message {
    margin: var(--spacing-3) 0;
    padding: var(--spacing-3) var(--spacing-4);
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    gap: var(--spacing-2);
    font-size: var(--font-size-sm);
    animation: slideInUp var(--duration-300) var(--ease-out);
}

.system-message i {
    font-size: var(--font-size-base);
    flex-shrink: 0;
}

/* 工作流进度指示器 */
.workflow-progress {
    margin: var(--spacing-4) 0;
    padding: var(--spacing-4);
    background: var(--color-gray-50);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-lg);
}

.progress-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: var(--spacing-3);
}

.progress-title {
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-800);
}

.progress-percentage {
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--color-gray-200);
    border-radius: var(--radius-full);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--color-primary-500), var(--color-primary-600));
    border-radius: var(--radius-full);
    transition: width var(--duration-500) var(--ease-out);
}

.progress-steps {
    margin-top: var(--spacing-3);
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
}

/* 工作流输入表单 */
.workflow-input-form {
    margin: var(--spacing-4) 0;
    padding: var(--spacing-4);
    background: var(--color-blue-50);
    border: 1px solid var(--color-blue-200);
    border-radius: var(--radius-lg);
}

.input-requirement {
    margin-bottom: var(--spacing-3);
}

.input-requirement label {
    display: block;
    font-weight: var(--font-weight-medium);
    color: var(--color-gray-800);
    margin-bottom: var(--spacing-1);
}

.input-requirement input,
.input-requirement select,
.input-requirement textarea {
    width: 100%;
    padding: var(--spacing-2) var(--spacing-3);
    border: 1px solid var(--color-gray-300);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    transition: border-color var(--transition-base);
}

.input-requirement input:focus,
.input-requirement select:focus,
.input-requirement textarea:focus {
    outline: none;
    border-color: var(--color-primary-500);
    box-shadow: 0 0 0 3px rgba(var(--color-primary-500-rgb), 0.1);
}

.input-requirement .help-text {
    font-size: var(--font-size-xs);
    color: var(--color-gray-600);
    margin-top: var(--spacing-1);
}

.input-requirement.required label::after {
    content: " *";
    color: var(--color-red-500);
}

/* 工作流操作按钮 */
.workflow-actions {
    display: flex;
    gap: var(--spacing-2);
    margin-top: var(--spacing-4);
}

.workflow-actions .btn {
    font-size: var(--font-size-sm);
    padding: var(--spacing-2) var(--spacing-4);
}

/* 动画 */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        transform: translateY(20px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .workflow-indicator {
        top: 10px;
        right: 10px;
        padding: var(--spacing-2) var(--spacing-3);
    }

    .workflow-status {
        font-size: var(--font-size-xs);
    }

    .suggestion-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-3);
    }

    .workflow-actions {
        flex-direction: column;
    }
}
