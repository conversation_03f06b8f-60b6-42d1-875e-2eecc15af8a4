package service

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
)

// 🚀 革命性架构：AI执行引擎核心组件实现

// NewAIInstructionParser 创建AI指令解析器
func NewAIInstructionParser(logger *logrus.Logger) *AIInstructionParser {
	return &AIInstructionParser{
		logger: logger,
	}
}

// ParseInstructions 解析AI生成的指令
func (aip *AIInstructionParser) ParseInstructions(rawInstructions interface{}) ([]*AIGeneratedInstruction, error) {
	aip.logger.Debug("🎯 开始解析AI指令")

	switch v := rawInstructions.(type) {
	case string:
		return aip.parseFromJSON(v)
	case []interface{}:
		return aip.parseFromSlice(v)
	case []*AIGeneratedInstruction:
		return v, nil
	default:
		return nil, fmt.Errorf("不支持的指令格式: %T", rawInstructions)
	}
}

// parseFromJSON 从JSON字符串解析指令
func (aip *AIInstructionParser) parseFromJSON(jsonStr string) ([]*AIGeneratedInstruction, error) {
	var instructions []*AIGeneratedInstruction
	if err := json.Unmarshal([]byte(jsonStr), &instructions); err != nil {
		return nil, fmt.Errorf("JSON解析失败: %w", err)
	}
	return instructions, nil
}

// parseFromSlice 从切片解析指令
func (aip *AIInstructionParser) parseFromSlice(slice []interface{}) ([]*AIGeneratedInstruction, error) {
	var instructions []*AIGeneratedInstruction
	
	for i, item := range slice {
		instruction, err := aip.parseInstruction(item)
		if err != nil {
			aip.logger.WithError(err).WithField("index", i).Warn("指令解析失败，跳过")
			continue
		}
		instructions = append(instructions, instruction)
	}
	
	return instructions, nil
}

// parseInstruction 解析单个指令
func (aip *AIInstructionParser) parseInstruction(item interface{}) (*AIGeneratedInstruction, error) {
	switch v := item.(type) {
	case map[string]interface{}:
		return aip.parseFromMap(v)
	case *AIGeneratedInstruction:
		return v, nil
	default:
		return nil, fmt.Errorf("不支持的指令项格式: %T", item)
	}
}

// parseFromMap 从map解析指令
func (aip *AIInstructionParser) parseFromMap(m map[string]interface{}) (*AIGeneratedInstruction, error) {
	instruction := &AIGeneratedInstruction{}
	
	// 基本字段
	if v, ok := m["instruction_type"].(string); ok {
		instruction.InstructionType = v
	}
	if v, ok := m["executor_type"].(string); ok {
		instruction.ExecutorType = v
	}
	if v, ok := m["content"].(string); ok {
		instruction.Content = v
	}
	if v, ok := m["description"].(string); ok {
		instruction.Description = v
	}
	if v, ok := m["security_level"].(string); ok {
		instruction.SecurityLevel = v
	}
	if v, ok := m["require_confirm"].(bool); ok {
		instruction.RequireConfirm = v
	}
	if v, ok := m["timeout"].(float64); ok {
		instruction.Timeout = int(v)
	}
	
	// 参数
	if v, ok := m["parameters"].(map[string]interface{}); ok {
		instruction.Parameters = v
	}
	
	// 目标资源
	if v, ok := m["target_resource"].(map[string]interface{}); ok {
		instruction.TargetResource = aip.parseTargetResource(v)
	}
	
	// 预期结果
	if v, ok := m["expected_result"].(map[string]interface{}); ok {
		instruction.ExpectedResult = aip.parseExpectedResult(v)
	}
	
	return instruction, nil
}

// parseTargetResource 解析目标资源
func (aip *AIInstructionParser) parseTargetResource(m map[string]interface{}) *TargetResource {
	resource := &TargetResource{}
	
	if v, ok := m["type"].(string); ok {
		resource.Type = v
	}
	if v, ok := m["identifier"].(string); ok {
		resource.Identifier = v
	}
	if v, ok := m["metadata"].(map[string]interface{}); ok {
		resource.Metadata = v
	}
	
	return resource
}

// parseExpectedResult 解析预期结果
func (aip *AIInstructionParser) parseExpectedResult(m map[string]interface{}) *ExpectedResult {
	result := &ExpectedResult{}
	
	if v, ok := m["data_type"].(string); ok {
		result.DataType = v
	}
	if v, ok := m["format"].(string); ok {
		result.Format = v
	}
	if v, ok := m["validation"].(string); ok {
		result.Validation = v
	}
	
	// 列名
	if v, ok := m["columns"].([]interface{}); ok {
		for _, col := range v {
			if colStr, ok := col.(string); ok {
				result.Columns = append(result.Columns, colStr)
			}
		}
	}
	
	// 行数
	if v, ok := m["row_count"].(float64); ok {
		count := int(v)
		result.RowCount = &count
	}
	
	return result
}

// NewExecutorRegistry 创建执行器注册表
func NewExecutorRegistry(logger *logrus.Logger) *ExecutorRegistry {
	return &ExecutorRegistry{
		executors: make(map[string]GenericExecutor),
		logger:    logger,
	}
}

// RegisterExecutor 注册执行器
func (er *ExecutorRegistry) RegisterExecutor(name string, executor GenericExecutor) {
	er.executors[name] = executor
	er.logger.WithFields(logrus.Fields{
		"executor_name": name,
		"executor_info": executor.GetExecutorInfo(),
	}).Info("🚀 注册通用执行器")
}

// GetExecutor 获取执行器
func (er *ExecutorRegistry) GetExecutor(executorType string) (GenericExecutor, error) {
	executor, exists := er.executors[executorType]
	if !exists {
		return nil, fmt.Errorf("未找到执行器类型: %s", executorType)
	}
	return executor, nil
}

// GetAllExecutors 获取所有执行器
func (er *ExecutorRegistry) GetAllExecutors() map[string]GenericExecutor {
	return er.executors
}

// NewAISecurityValidator 创建AI指令安全验证器
func NewAISecurityValidator(logger *logrus.Logger) *AISecurityValidator {
	return &AISecurityValidator{
		logger: logger,
		blockedPatterns: []string{
			`(?i)rm\s+-rf\s+/`,
			`(?i)drop\s+database`,
			`(?i)format\s+c:`,
			`(?i)del\s+/s\s+/q`,
		},
	}
}

// AISecurityValidator AI指令安全验证器
type AISecurityValidator struct {
	logger          *logrus.Logger
	blockedPatterns []string
}

// ValidateInstruction 验证指令安全性
func (asv *AISecurityValidator) ValidateInstruction(instruction *AIGeneratedInstruction) error {
	asv.logger.WithFields(logrus.Fields{
		"instruction_type": instruction.InstructionType,
		"security_level":   instruction.SecurityLevel,
	}).Debug("🔒 开始安全验证")

	// 检查安全级别
	if instruction.SecurityLevel == "dangerous" && !instruction.RequireConfirm {
		return fmt.Errorf("危险操作必须要求确认")
	}

	// 检查阻止模式
	for _, pattern := range asv.blockedPatterns {
		if matched, _ := regexp.MatchString(pattern, instruction.Content); matched {
			return fmt.Errorf("检测到被阻止的危险模式: %s", pattern)
		}
	}

	// 根据指令类型进行特定验证
	switch instruction.InstructionType {
	case "sql":
		return asv.validateSQL(instruction.Content)
	case "shell":
		return asv.validateShell(instruction.Content)
	}

	return nil
}

// validateSQL 验证SQL安全性
func (asv *AISecurityValidator) validateSQL(sqlContent string) error {
	dangerousSQL := []string{
		`(?i)drop\s+table`,
		`(?i)drop\s+database`,
		`(?i)truncate\s+table`,
		`(?i)delete\s+from.*where\s+1\s*=\s*1`,
	}

	for _, pattern := range dangerousSQL {
		if matched, _ := regexp.MatchString(pattern, sqlContent); matched {
			return fmt.Errorf("检测到危险SQL模式: %s", pattern)
		}
	}

	return nil
}

// validateShell 验证Shell命令安全性
func (asv *AISecurityValidator) validateShell(shellContent string) error {
	dangerousCommands := []string{
		`(?i)rm\s+-rf`,
		`(?i)dd\s+if=.*of=/dev/`,
		`(?i)mkfs`,
		`(?i)^shutdown`,        // 只匹配以shutdown开头的命令
		`(?i)^reboot`,          // 只匹配以reboot开头的命令
		`(?i)^halt`,            // 只匹配以halt开头的命令
	}

	for _, pattern := range dangerousCommands {
		if matched, _ := regexp.MatchString(pattern, shellContent); matched {
			return fmt.Errorf("检测到危险Shell命令: %s", pattern)
		}
	}

	return nil
}

// NewExecutionOrchestrator 创建执行编排器
func NewExecutionOrchestrator(logger *logrus.Logger, registry *ExecutorRegistry, validator *AISecurityValidator) *ExecutionOrchestrator {
	return &ExecutionOrchestrator{
		logger:    logger,
		registry:  registry,
		validator: validator,
	}
}

// ExecuteInstructions 执行指令集
func (eo *ExecutionOrchestrator) ExecuteInstructions(ctx context.Context, instructions []*AIGeneratedInstruction, req *ExecutionRequest) (*GenericExecutionResult, error) {
	eo.logger.WithField("instruction_count", len(instructions)).Info("🎯 开始编排执行指令")

	if len(instructions) == 0 {
		return &GenericExecutionResult{
			Success: false,
			Content: "❌ 没有可执行的指令",
			Action:  "no_instructions",
		}, nil
	}

	// 🚀 单指令执行
	if len(instructions) == 1 {
		return eo.executeSingleInstruction(ctx, instructions[0])
	}

	// 🎯 多指令编排执行
	return eo.executeMultipleInstructions(ctx, instructions)
}

// executeSingleInstruction 执行单个指令
func (eo *ExecutionOrchestrator) executeSingleInstruction(ctx context.Context, instruction *AIGeneratedInstruction) (*GenericExecutionResult, error) {
	// 获取对应的执行器
	executor, err := eo.registry.GetExecutor(instruction.ExecutorType)
	if err != nil {
		return &GenericExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 未找到执行器: %s", instruction.ExecutorType),
			Action:  "executor_not_found",
		}, nil
	}

	// 验证指令
	if err := executor.ValidateInstruction(instruction); err != nil {
		return &GenericExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 指令验证失败: %s", err.Error()),
			Action:  "instruction_validation_failed",
		}, nil
	}

	// 执行指令
	return executor.Execute(ctx, instruction)
}

// executeMultipleInstructions 执行多个指令
func (eo *ExecutionOrchestrator) executeMultipleInstructions(ctx context.Context, instructions []*AIGeneratedInstruction) (*GenericExecutionResult, error) {
	var results []*GenericExecutionResult
	var combinedContent strings.Builder
	allSuccess := true

	combinedContent.WriteString("🚀 **批量指令执行结果**\n\n")

	for i, instruction := range instructions {
		eo.logger.WithFields(logrus.Fields{
			"instruction_index": i + 1,
			"instruction_type":  instruction.InstructionType,
		}).Info("执行指令")

		result, err := eo.executeSingleInstruction(ctx, instruction)
		if err != nil {
			allSuccess = false
			combinedContent.WriteString(fmt.Sprintf("❌ **指令 %d 执行失败**: %s\n\n", i+1, err.Error()))
			continue
		}

		results = append(results, result)
		
		if !result.Success {
			allSuccess = false
		}

		combinedContent.WriteString(fmt.Sprintf("📋 **指令 %d**: %s\n", i+1, instruction.Description))
		combinedContent.WriteString(result.Content)
		combinedContent.WriteString("\n\n---\n\n")
	}

	return &GenericExecutionResult{
		Success: allSuccess,
		Content: combinedContent.String(),
		Action:  "batch_execution_completed",
		Data: map[string]interface{}{
			"total_instructions": len(instructions),
			"successful_count":   len(results),
			"results":           results,
		},
	}, nil
}
