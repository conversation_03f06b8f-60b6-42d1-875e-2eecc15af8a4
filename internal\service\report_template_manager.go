package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ReportTemplateManager 报表模板管理器
type ReportTemplateManager struct {
	db        *gorm.DB
	logger    *logrus.Logger
	templates map[string]*ReportTemplate
	mutex     sync.RWMutex
}

// ReportTemplate 报表模板
type ReportTemplate struct {
	ID          string                 `json:"id" gorm:"primaryKey"`
	Name        string                 `json:"name" gorm:"not null"`
	Description string                 `json:"description"`
	Type        string                 `json:"type" gorm:"not null"` // operation, system_health, ai_usage, custom
	Category    string                 `json:"category"`              // dashboard, report, widget
	Version     string                 `json:"version"`
	Author      string                 `json:"author"`
	IsBuiltIn   bool                   `json:"is_built_in" gorm:"default:false"`
	IsActive    bool                   `json:"is_active" gorm:"default:true"`
	Layout      *TemplateLayout        `json:"layout" gorm:"type:json"`
	Sections    []*TemplateSection     `json:"sections" gorm:"type:json"`
	Charts      []*TemplateChart       `json:"charts" gorm:"type:json"`
	Styles      *TemplateStyles        `json:"styles" gorm:"type:json"`
	Config      map[string]interface{} `json:"config" gorm:"type:json"`
	Metadata    map[string]interface{} `json:"metadata" gorm:"type:json"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	CreatedBy   int64                  `json:"created_by"`
	UpdatedBy   int64                  `json:"updated_by"`
}

// TemplateLayout 模板布局
type TemplateLayout struct {
	Type        string                 `json:"type"`         // grid, flex, absolute
	Columns     int                    `json:"columns"`      // 网格列数
	Rows        int                    `json:"rows"`         // 网格行数
	Gap         int                    `json:"gap"`          // 间距
	Padding     int                    `json:"padding"`      // 内边距
	Responsive  bool                   `json:"responsive"`   // 是否响应式
	Breakpoints map[string]interface{} `json:"breakpoints"`  // 断点配置
	Areas       []LayoutArea           `json:"areas"`        // 布局区域
}

// LayoutArea 布局区域
type LayoutArea struct {
	ID       string `json:"id"`
	Name     string `json:"name"`
	X        int    `json:"x"`        // 起始列
	Y        int    `json:"y"`        // 起始行
	Width    int    `json:"width"`    // 宽度（列数）
	Height   int    `json:"height"`   // 高度（行数）
	MinWidth int    `json:"min_width"`
	MaxWidth int    `json:"max_width"`
	Order    int    `json:"order"`    // 显示顺序
}

// TemplateSection 模板章节
type TemplateSection struct {
	ID          string                 `json:"id"`
	Title       string                 `json:"title"`
	Type        string                 `json:"type"`        // header, content, chart, table, metric, text
	AreaID      string                 `json:"area_id"`     // 对应的布局区域ID
	Order       int                    `json:"order"`
	Visible     bool                   `json:"visible"`
	Collapsible bool                   `json:"collapsible"`
	Content     *SectionContent        `json:"content"`
	Style       map[string]interface{} `json:"style"`
	Config      map[string]interface{} `json:"config"`
}

// SectionContent 章节内容
type SectionContent struct {
	Type       string                 `json:"type"`       // static, dynamic, template
	Template   string                 `json:"template"`   // 模板字符串
	DataSource string                 `json:"data_source"` // 数据源
	Fields     []string               `json:"fields"`     // 字段列表
	Filters    map[string]interface{} `json:"filters"`    // 过滤条件
	Transform  string                 `json:"transform"`  // 数据转换脚本
}

// TemplateChart 模板图表
type TemplateChart struct {
	ID         string                 `json:"id"`
	Title      string                 `json:"title"`
	Type       string                 `json:"type"`       // line, bar, pie, area, etc.
	AreaID     string                 `json:"area_id"`    // 对应的布局区域ID
	DataSource string                 `json:"data_source"`
	DataKey    string                 `json:"data_key"`
	Options    map[string]interface{} `json:"options"`
	Style      map[string]interface{} `json:"style"`
	Config     map[string]interface{} `json:"config"`
	Order      int                    `json:"order"`
	Visible    bool                   `json:"visible"`
}

// TemplateStyles 模板样式
type TemplateStyles struct {
	Theme      string                 `json:"theme"`       // light, dark, custom
	Colors     []string               `json:"colors"`      // 颜色调色板
	Fonts      map[string]string      `json:"fonts"`       // 字体配置
	Spacing    map[string]int         `json:"spacing"`     // 间距配置
	Borders    map[string]interface{} `json:"borders"`     // 边框配置
	Shadows    map[string]interface{} `json:"shadows"`     // 阴影配置
	Custom     map[string]interface{} `json:"custom"`      // 自定义样式
}

// NewReportTemplateManager 创建报表模板管理器
func NewReportTemplateManager(db *gorm.DB, logger *logrus.Logger) *ReportTemplateManager {
	return &ReportTemplateManager{
		db:        db,
		logger:    logger,
		templates: make(map[string]*ReportTemplate),
	}
}

// Start 启动模板管理器
func (rtm *ReportTemplateManager) Start(ctx context.Context) error {
	// 自动迁移数据库表
	if err := rtm.db.AutoMigrate(&ReportTemplate{}); err != nil {
		return fmt.Errorf("failed to migrate report template table: %w", err)
	}

	// 加载内置模板
	if err := rtm.loadBuiltInTemplates(); err != nil {
		rtm.logger.WithError(err).Warn("Failed to load built-in templates")
	}

	// 从数据库加载自定义模板
	if err := rtm.loadCustomTemplates(); err != nil {
		rtm.logger.WithError(err).Warn("Failed to load custom templates")
	}

	rtm.logger.WithField("templates", len(rtm.templates)).Info("Report template manager started")
	return nil
}

// loadBuiltInTemplates 加载内置模板
func (rtm *ReportTemplateManager) loadBuiltInTemplates() error {
	builtInTemplates := []*ReportTemplate{
		rtm.createOperationReportTemplate(),
		rtm.createSystemHealthTemplate(),
		rtm.createAIUsageTemplate(),
		rtm.createPerformanceDashboardTemplate(),
		rtm.createExecutiveSummaryTemplate(),
	}

	rtm.mutex.Lock()
	defer rtm.mutex.Unlock()

	for _, template := range builtInTemplates {
		rtm.templates[template.ID] = template
		
		// 保存到数据库（如果不存在）
		var existing ReportTemplate
		if err := rtm.db.Where("id = ?", template.ID).First(&existing).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				if err := rtm.db.Create(template).Error; err != nil {
					rtm.logger.WithError(err).WithField("template_id", template.ID).Warn("Failed to save built-in template")
				}
			}
		}
	}

	rtm.logger.WithField("built_in_templates", len(builtInTemplates)).Info("Built-in templates loaded")
	return nil
}

// createOperationReportTemplate 创建运维操作报表模板
func (rtm *ReportTemplateManager) createOperationReportTemplate() *ReportTemplate {
	return &ReportTemplate{
		ID:          "operation_report_v1",
		Name:        "运维操作报表",
		Description: "展示系统运维操作的详细统计和趋势分析",
		Type:        "operation",
		Category:    "report",
		Version:     "1.0.0",
		Author:      "system",
		IsBuiltIn:   true,
		IsActive:    true,
		Layout: &TemplateLayout{
			Type:       "grid",
			Columns:    12,
			Rows:       8,
			Gap:        16,
			Padding:    20,
			Responsive: true,
			Areas: []LayoutArea{
				{ID: "header", Name: "标题区域", X: 0, Y: 0, Width: 12, Height: 1, Order: 1},
				{ID: "summary", Name: "摘要区域", X: 0, Y: 1, Width: 12, Height: 1, Order: 2},
				{ID: "trend_chart", Name: "趋势图表", X: 0, Y: 2, Width: 8, Height: 3, Order: 3},
				{ID: "metrics", Name: "关键指标", X: 8, Y: 2, Width: 4, Height: 3, Order: 4},
				{ID: "distribution", Name: "分布图表", X: 0, Y: 5, Width: 6, Height: 3, Order: 5},
				{ID: "details", Name: "详细数据", X: 6, Y: 5, Width: 6, Height: 3, Order: 6},
			},
		},
		Sections: []*TemplateSection{
			{
				ID:      "header_section",
				Title:   "运维操作报表",
				Type:    "header",
				AreaID:  "header",
				Order:   1,
				Visible: true,
				Content: &SectionContent{
					Type:     "template",
					Template: "<h1>{{.title}}</h1><p>报表时间：{{.time_range}} | 生成时间：{{.generated_at}}</p>",
				},
			},
			{
				ID:      "summary_section",
				Title:   "执行摘要",
				Type:    "content",
				AreaID:  "summary",
				Order:   2,
				Visible: true,
				Content: &SectionContent{
					Type:       "dynamic",
					DataSource: "operation_summary",
					Template:   "总操作数：{{.total_operations}} | 成功率：{{.success_rate}}% | 平均响应时间：{{.avg_response_time}}ms",
				},
			},
		},
		Charts: []*TemplateChart{
			{
				ID:         "operation_trend",
				Title:      "操作趋势",
				Type:       "line",
				AreaID:     "trend_chart",
				DataSource: "operation_trend",
				DataKey:    "trend_data",
				Order:      1,
				Visible:    true,
				Options: map[string]interface{}{
					"responsive": true,
					"legend":     map[string]interface{}{"display": true},
				},
			},
			{
				ID:         "operation_types",
				Title:      "操作类型分布",
				Type:       "pie",
				AreaID:     "distribution",
				DataSource: "operation_types",
				DataKey:    "type_distribution",
				Order:      2,
				Visible:    true,
				Options: map[string]interface{}{
					"responsive": true,
					"legend":     map[string]interface{}{"position": "right"},
				},
			},
		},
		Styles: &TemplateStyles{
			Theme:  "light",
			Colors: []string{"#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6"},
			Fonts: map[string]string{
				"title":   "24px 'Microsoft YaHei', sans-serif",
				"heading": "18px 'Microsoft YaHei', sans-serif",
				"body":    "14px 'Microsoft YaHei', sans-serif",
			},
			Spacing: map[string]int{
				"section": 20,
				"chart":   16,
				"text":    12,
			},
		},
		Config: map[string]interface{}{
			"auto_refresh": true,
			"refresh_interval": 300, // 5分钟
			"export_formats": []string{"pdf", "excel", "html"},
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		CreatedBy: 0, // 系统创建
		UpdatedBy: 0,
	}
}

// createSystemHealthTemplate 创建系统健康模板
func (rtm *ReportTemplateManager) createSystemHealthTemplate() *ReportTemplate {
	return &ReportTemplate{
		ID:          "system_health_v1",
		Name:        "系统健康仪表盘",
		Description: "实时展示系统健康状况和关键性能指标",
		Type:        "system_health",
		Category:    "dashboard",
		Version:     "1.0.0",
		Author:      "system",
		IsBuiltIn:   true,
		IsActive:    true,
		Layout: &TemplateLayout{
			Type:       "grid",
			Columns:    12,
			Rows:       6,
			Gap:        12,
			Padding:    16,
			Responsive: true,
			Areas: []LayoutArea{
				{ID: "health_score", Name: "健康评分", X: 0, Y: 0, Width: 3, Height: 2, Order: 1},
				{ID: "cpu_usage", Name: "CPU使用率", X: 3, Y: 0, Width: 3, Height: 2, Order: 2},
				{ID: "memory_usage", Name: "内存使用率", X: 6, Y: 0, Width: 3, Height: 2, Order: 3},
				{ID: "disk_usage", Name: "磁盘使用率", X: 9, Y: 0, Width: 3, Height: 2, Order: 4},
				{ID: "resource_trend", Name: "资源趋势", X: 0, Y: 2, Width: 8, Height: 4, Order: 5},
				{ID: "alerts", Name: "告警信息", X: 8, Y: 2, Width: 4, Height: 4, Order: 6},
			},
		},
		Charts: []*TemplateChart{
			{
				ID:         "health_gauge",
				Title:      "系统健康评分",
				Type:       "gauge",
				AreaID:     "health_score",
				DataSource: "system_health",
				DataKey:    "health_score",
				Order:      1,
				Visible:    true,
			},
			{
				ID:         "resource_area",
				Title:      "资源使用趋势",
				Type:       "area",
				AreaID:     "resource_trend",
				DataSource: "resource_usage",
				DataKey:    "usage_trend",
				Order:      2,
				Visible:    true,
			},
		},
		Styles: &TemplateStyles{
			Theme:  "dark",
			Colors: []string{"#00d4aa", "#ff6b6b", "#4ecdc4", "#45b7d1", "#f9ca24"},
		},
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
		CreatedBy: 0,
		UpdatedBy: 0,
	}
}

// createAIUsageTemplate 创建AI使用模板
func (rtm *ReportTemplateManager) createAIUsageTemplate() *ReportTemplate {
	return &ReportTemplate{
		ID:          "ai_usage_v1",
		Name:        "AI使用分析报表",
		Description: "分析AI功能的使用情况和性能表现",
		Type:        "ai_usage",
		Category:    "report",
		Version:     "1.0.0",
		Author:      "system",
		IsBuiltIn:   true,
		IsActive:    true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		CreatedBy:   0,
		UpdatedBy:   0,
	}
}

// createPerformanceDashboardTemplate 创建性能仪表盘模板
func (rtm *ReportTemplateManager) createPerformanceDashboardTemplate() *ReportTemplate {
	return &ReportTemplate{
		ID:          "performance_dashboard_v1",
		Name:        "性能监控仪表盘",
		Description: "实时监控系统性能指标和响应时间",
		Type:        "performance",
		Category:    "dashboard",
		Version:     "1.0.0",
		Author:      "system",
		IsBuiltIn:   true,
		IsActive:    true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		CreatedBy:   0,
		UpdatedBy:   0,
	}
}

// createExecutiveSummaryTemplate 创建高管摘要模板
func (rtm *ReportTemplateManager) createExecutiveSummaryTemplate() *ReportTemplate {
	return &ReportTemplate{
		ID:          "executive_summary_v1",
		Name:        "高管摘要报表",
		Description: "为管理层提供简洁的关键指标概览",
		Type:        "executive",
		Category:    "report",
		Version:     "1.0.0",
		Author:      "system",
		IsBuiltIn:   true,
		IsActive:    true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		CreatedBy:   0,
		UpdatedBy:   0,
	}
}

// loadCustomTemplates 加载自定义模板
func (rtm *ReportTemplateManager) loadCustomTemplates() error {
	var templates []*ReportTemplate
	if err := rtm.db.Where("is_built_in = ? AND is_active = ?", false, true).Find(&templates).Error; err != nil {
		return err
	}

	rtm.mutex.Lock()
	defer rtm.mutex.Unlock()

	for _, template := range templates {
		rtm.templates[template.ID] = template
	}

	rtm.logger.WithField("custom_templates", len(templates)).Info("Custom templates loaded")
	return nil
}

// GetTemplate 获取模板
func (rtm *ReportTemplateManager) GetTemplate(templateID string) (*ReportTemplate, error) {
	rtm.mutex.RLock()
	defer rtm.mutex.RUnlock()

	template, exists := rtm.templates[templateID]
	if !exists {
		return nil, fmt.Errorf("template not found: %s", templateID)
	}

	return template, nil
}

// ListTemplates 列出模板
func (rtm *ReportTemplateManager) ListTemplates(category string, templateType string) []*ReportTemplate {
	rtm.mutex.RLock()
	defer rtm.mutex.RUnlock()

	var result []*ReportTemplate
	for _, template := range rtm.templates {
		if !template.IsActive {
			continue
		}
		
		if category != "" && template.Category != category {
			continue
		}
		
		if templateType != "" && template.Type != templateType {
			continue
		}
		
		result = append(result, template)
	}

	return result
}

// CreateTemplate 创建模板
func (rtm *ReportTemplateManager) CreateTemplate(template *ReportTemplate) error {
	template.CreatedAt = time.Now()
	template.UpdatedAt = time.Now()
	template.IsBuiltIn = false
	template.IsActive = true

	// 保存到数据库
	if err := rtm.db.Create(template).Error; err != nil {
		return fmt.Errorf("failed to create template: %w", err)
	}

	// 添加到内存缓存
	rtm.mutex.Lock()
	rtm.templates[template.ID] = template
	rtm.mutex.Unlock()

	rtm.logger.WithField("template_id", template.ID).Info("Template created")
	return nil
}

// UpdateTemplate 更新模板
func (rtm *ReportTemplateManager) UpdateTemplate(templateID string, updates *ReportTemplate) error {
	rtm.mutex.Lock()
	defer rtm.mutex.Unlock()

	existing, exists := rtm.templates[templateID]
	if !exists {
		return fmt.Errorf("template not found: %s", templateID)
	}

	if existing.IsBuiltIn {
		return fmt.Errorf("cannot update built-in template: %s", templateID)
	}

	updates.ID = templateID
	updates.UpdatedAt = time.Now()

	// 更新数据库
	if err := rtm.db.Save(updates).Error; err != nil {
		return fmt.Errorf("failed to update template: %w", err)
	}

	// 更新内存缓存
	rtm.templates[templateID] = updates

	rtm.logger.WithField("template_id", templateID).Info("Template updated")
	return nil
}

// DeleteTemplate 删除模板
func (rtm *ReportTemplateManager) DeleteTemplate(templateID string) error {
	rtm.mutex.Lock()
	defer rtm.mutex.Unlock()

	template, exists := rtm.templates[templateID]
	if !exists {
		return fmt.Errorf("template not found: %s", templateID)
	}

	if template.IsBuiltIn {
		return fmt.Errorf("cannot delete built-in template: %s", templateID)
	}

	// 软删除：标记为不活跃
	template.IsActive = false
	template.UpdatedAt = time.Now()

	// 更新数据库
	if err := rtm.db.Save(template).Error; err != nil {
		return fmt.Errorf("failed to delete template: %w", err)
	}

	// 从内存缓存中移除
	delete(rtm.templates, templateID)

	rtm.logger.WithField("template_id", templateID).Info("Template deleted")
	return nil
}
