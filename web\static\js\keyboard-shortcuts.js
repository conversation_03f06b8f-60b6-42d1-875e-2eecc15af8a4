/* ========================================
   键盘快捷键系统JavaScript
   提供完整的键盘导航和快捷键支持
   ======================================== */

class KeyboardShortcutSystem {
    constructor() {
        this.shortcuts = new Map();
        this.isKeyboardMode = false;
        this.currentFocusIndex = -1;
        this.focusableElements = [];
        this.commandPalette = null;
        this.shortcutsPanel = null;
        
        this.init();
    }
    
    init() {
        this.registerDefaultShortcuts();
        this.createShortcutsPanel();
        this.createCommandPalette();
        this.createKeyboardIndicator();
        this.bindEvents();
        this.updateFocusableElements();
        
        console.log('⌨️ 键盘快捷键系统已初始化');
    }
    
    // 注册默认快捷键
    registerDefaultShortcuts() {
        // 全局快捷键
        this.register('?', () => this.showShortcutsPanel(), '显示快捷键帮助');
        this.register('Escape', () => this.handleEscape(), '关闭面板/取消操作');
        this.register('ctrl+k', () => this.showCommandPalette(), '打开命令面板');
        this.register('ctrl+/', () => this.showShortcutsPanel(), '显示快捷键帮助');
        
        // 搜索相关
        this.register('ctrl+f', () => this.focusSearch(), '聚焦搜索框');
        this.register('ctrl+shift+f', () => this.toggleAdvancedSearch(), '高级搜索');
        
        // 导航相关
        this.register('ctrl+1', () => this.focusSidebar(), '聚焦侧边栏');
        this.register('ctrl+2', () => this.focusMainChat(), '聚焦主对话区');
        this.register('ctrl+3', () => this.focusAssistantPanel(), '聚焦助手面板');
        
        // 对话相关
        this.register('ctrl+n', () => this.newChat(), '新建对话');
        this.register('ctrl+enter', () => this.sendMessage(), '发送消息');
        this.register('ctrl+shift+c', () => this.clearChat(), '清空对话');
        
        // 主题相关
        this.register('ctrl+shift+t', () => this.toggleTheme(), '切换主题');
        this.register('ctrl+shift+d', () => this.toggleDarkMode(), '切换深色模式');
        
        // 通知相关
        this.register('ctrl+shift+n', () => this.toggleNotifications(), '打开通知中心');
        this.register('ctrl+shift+m', () => this.markAllNotificationsRead(), '标记所有通知为已读');
        
        // 可访问性
        this.register('alt+shift+k', () => this.toggleKeyboardMode(), '切换键盘导航模式');
        this.register('tab', (e) => this.handleTabNavigation(e), '键盘导航');
        this.register('shift+tab', (e) => this.handleShiftTabNavigation(e), '反向键盘导航');
        
        // 开发者工具
        this.register('ctrl+shift+i', () => this.toggleDebugInfo(), '切换调试信息');
        this.register('ctrl+shift+r', () => this.reloadApp(), '重新加载应用');
    }
    
    // 注册快捷键
    register(key, callback, description, category = 'general') {
        const normalizedKey = this.normalizeKey(key);
        this.shortcuts.set(normalizedKey, {
            key: normalizedKey,
            originalKey: key,
            callback,
            description,
            category
        });
    }
    
    // 标准化按键
    normalizeKey(key) {
        return key.toLowerCase()
            .replace(/\s+/g, '')
            .replace(/cmd/g, 'ctrl')
            .replace(/command/g, 'ctrl')
            .replace(/option/g, 'alt')
            .replace(/\+/g, '+');
    }
    
    // 创建快捷键帮助面板
    createShortcutsPanel() {
        // 创建背景遮罩
        const backdrop = document.createElement('div');
        backdrop.className = 'shortcuts-backdrop';
        backdrop.id = 'shortcuts-backdrop';
        backdrop.onclick = () => this.hideShortcutsPanel();
        
        // 创建面板
        const panel = document.createElement('div');
        panel.className = 'shortcuts-panel';
        panel.id = 'shortcuts-panel';
        panel.innerHTML = this.createShortcutsPanelHTML();
        
        document.body.appendChild(backdrop);
        document.body.appendChild(panel);
        
        this.shortcutsPanel = panel;
        
        // 绑定搜索事件
        const searchInput = panel.querySelector('.shortcuts-search-input');
        searchInput.addEventListener('input', (e) => {
            this.filterShortcuts(e.target.value);
        });
    }
    
    createShortcutsPanelHTML() {
        const categories = this.groupShortcutsByCategory();
        
        return `
            <div class="shortcuts-header">
                <h3 class="shortcuts-title">
                    <i class="shortcuts-title-icon bi bi-keyboard"></i>
                    键盘快捷键
                </h3>
                <button class="shortcuts-close" onclick="window.keyboardShortcuts.hideShortcutsPanel()">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            
            <div class="shortcuts-content">
                <div class="shortcuts-search">
                    <i class="shortcuts-search-icon bi bi-search"></i>
                    <input type="text" class="shortcuts-search-input" placeholder="搜索快捷键...">
                </div>
                
                ${Object.keys(categories).map(category => this.createCategoryHTML(category, categories[category])).join('')}
            </div>
        `;
    }
    
    groupShortcutsByCategory() {
        const categories = {
            general: { title: '通用', icon: 'bi-gear', shortcuts: [] },
            navigation: { title: '导航', icon: 'bi-compass', shortcuts: [] },
            chat: { title: '对话', icon: 'bi-chat-dots', shortcuts: [] },
            search: { title: '搜索', icon: 'bi-search', shortcuts: [] },
            theme: { title: '主题', icon: 'bi-palette', shortcuts: [] },
            notifications: { title: '通知', icon: 'bi-bell', shortcuts: [] },
            accessibility: { title: '无障碍', icon: 'bi-universal-access', shortcuts: [] },
            developer: { title: '开发者', icon: 'bi-code-slash', shortcuts: [] }
        };
        
        this.shortcuts.forEach(shortcut => {
            const category = this.getCategoryForShortcut(shortcut);
            if (categories[category]) {
                categories[category].shortcuts.push(shortcut);
            }
        });
        
        return categories;
    }
    
    getCategoryForShortcut(shortcut) {
        const key = shortcut.originalKey.toLowerCase();
        
        if (key.includes('ctrl+1') || key.includes('ctrl+2') || key.includes('ctrl+3')) return 'navigation';
        if (key.includes('ctrl+n') || key.includes('ctrl+enter') || key.includes('ctrl+shift+c')) return 'chat';
        if (key.includes('ctrl+f') || key.includes('ctrl+k')) return 'search';
        if (key.includes('ctrl+shift+t') || key.includes('ctrl+shift+d')) return 'theme';
        if (key.includes('ctrl+shift+n') || key.includes('ctrl+shift+m')) return 'notifications';
        if (key.includes('alt+shift') || key.includes('tab')) return 'accessibility';
        if (key.includes('ctrl+shift+i') || key.includes('ctrl+shift+r')) return 'developer';
        
        return 'general';
    }
    
    createCategoryHTML(categoryKey, category) {
        if (category.shortcuts.length === 0) return '';
        
        return `
            <div class="shortcuts-section" data-category="${categoryKey}">
                <h4 class="shortcuts-section-title">
                    <i class="shortcuts-section-icon ${category.icon}"></i>
                    ${category.title}
                </h4>
                <div class="shortcuts-list">
                    ${category.shortcuts.map(shortcut => this.createShortcutItemHTML(shortcut)).join('')}
                </div>
            </div>
        `;
    }
    
    createShortcutItemHTML(shortcut) {
        const keys = this.parseShortcutKeys(shortcut.originalKey);
        
        return `
            <div class="shortcut-item" data-shortcut="${shortcut.key}">
                <div class="shortcut-description">
                    ${shortcut.description}
                </div>
                <div class="shortcut-keys">
                    ${keys.map(key => `<span class="shortcut-key">${key}</span>`).join('<span class="shortcut-plus">+</span>')}
                </div>
            </div>
        `;
    }
    
    parseShortcutKeys(keyString) {
        return keyString.split('+').map(key => {
            const keyMap = {
                'ctrl': 'Ctrl',
                'shift': 'Shift',
                'alt': 'Alt',
                'cmd': 'Cmd',
                'enter': 'Enter',
                'escape': 'Esc',
                'tab': 'Tab',
                ' ': 'Space'
            };
            
            return keyMap[key.toLowerCase()] || key.toUpperCase();
        });
    }
    
    // 创建命令面板
    createCommandPalette() {
        const palette = document.createElement('div');
        palette.className = 'command-palette';
        palette.id = 'command-palette';
        palette.innerHTML = this.createCommandPaletteHTML();
        
        document.body.appendChild(palette);
        this.commandPalette = palette;
        
        // 绑定事件
        const input = palette.querySelector('.command-input');
        input.addEventListener('input', (e) => {
            this.filterCommands(e.target.value);
        });
        
        input.addEventListener('keydown', (e) => {
            this.handleCommandPaletteKeydown(e);
        });
    }
    
    createCommandPaletteHTML() {
        return `
            <input type="text" class="command-input" placeholder="输入命令或搜索...">
            <div class="command-results" id="command-results">
                ${this.createCommandItems()}
            </div>
        `;
    }
    
    createCommandItems() {
        const commands = [
            { id: 'new-chat', title: '新建对话', description: '开始新的对话', icon: 'bi-plus-circle', action: () => this.newChat() },
            { id: 'search', title: '搜索', description: '搜索对话和消息', icon: 'bi-search', action: () => this.focusSearch() },
            { id: 'toggle-theme', title: '切换主题', description: '在浅色和深色主题间切换', icon: 'bi-palette', action: () => this.toggleTheme() },
            { id: 'notifications', title: '通知中心', description: '查看所有通知', icon: 'bi-bell', action: () => this.toggleNotifications() },
            { id: 'settings', title: '设置', description: '打开设置面板', icon: 'bi-gear', action: () => this.openSettings() },
            { id: 'help', title: '帮助', description: '查看快捷键帮助', icon: 'bi-question-circle', action: () => this.showShortcutsPanel() }
        ];
        
        return commands.map((cmd, index) => `
            <div class="command-item ${index === 0 ? 'selected' : ''}" data-command="${cmd.id}">
                <div class="command-icon">
                    <i class="bi ${cmd.icon}"></i>
                </div>
                <div class="command-content">
                    <div class="command-title">${cmd.title}</div>
                    <div class="command-description">${cmd.description}</div>
                </div>
            </div>
        `).join('');
    }
    
    // 创建键盘导航指示器
    createKeyboardIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'keyboard-navigation-indicator';
        indicator.id = 'keyboard-indicator';
        indicator.innerHTML = `
            <i class="bi bi-keyboard"></i>
            <span>键盘导航模式已启用</span>
        `;
        
        document.body.appendChild(indicator);
    }
    
    // 绑定事件
    bindEvents() {
        // 全局键盘事件
        document.addEventListener('keydown', (e) => {
            this.handleKeydown(e);
        });
        
        // 窗口焦点事件
        window.addEventListener('focus', () => {
            this.updateFocusableElements();
        });
        
        // 监听DOM变化
        const observer = new MutationObserver(() => {
            this.updateFocusableElements();
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    }
    
    // 处理按键事件
    handleKeydown(e) {
        const key = this.getKeyString(e);
        const shortcut = this.shortcuts.get(key);
        
        if (shortcut) {
            e.preventDefault();
            shortcut.callback(e);
            this.showShortcutFeedback(shortcut.description);
        }
        
        // 检测键盘导航模式
        if (e.key === 'Tab') {
            this.enableKeyboardMode();
        }
    }
    
    getKeyString(e) {
        const parts = [];
        
        if (e.ctrlKey) parts.push('ctrl');
        if (e.shiftKey) parts.push('shift');
        if (e.altKey) parts.push('alt');
        if (e.metaKey) parts.push('cmd');
        
        const key = e.key.toLowerCase();
        if (key !== 'control' && key !== 'shift' && key !== 'alt' && key !== 'meta') {
            parts.push(key);
        }
        
        return parts.join('+');
    }
    
    // 显示快捷键反馈
    showShortcutFeedback(description) {
        const status = document.createElement('div');
        status.className = 'shortcuts-status show';
        status.innerHTML = `
            <div class="shortcuts-status-text">
                <i class="shortcuts-status-icon bi bi-check-circle"></i>
                <span>${description}</span>
            </div>
        `;
        
        document.body.appendChild(status);
        
        setTimeout(() => {
            status.classList.remove('show');
            setTimeout(() => {
                if (status.parentNode) {
                    status.parentNode.removeChild(status);
                }
            }, 300);
        }, 2000);
    }
    
    // 更新可聚焦元素
    updateFocusableElements() {
        const selectors = [
            'button:not([disabled])',
            'input:not([disabled])',
            'textarea:not([disabled])',
            'select:not([disabled])',
            'a[href]',
            '[tabindex]:not([tabindex="-1"])',
            '.message',
            '.chat-item',
            '.notification-item'
        ];
        
        this.focusableElements = Array.from(document.querySelectorAll(selectors.join(', ')))
            .filter(el => {
                return el.offsetParent !== null && // 元素可见
                       !el.hasAttribute('disabled') &&
                       el.tabIndex !== -1;
            });
    }
    
    // 启用键盘模式
    enableKeyboardMode() {
        if (!this.isKeyboardMode) {
            this.isKeyboardMode = true;
            document.body.classList.add('keyboard-mode');
            
            const indicator = document.getElementById('keyboard-indicator');
            indicator.classList.add('show');
            
            setTimeout(() => {
                indicator.classList.remove('show');
            }, 3000);
        }
    }
    
    // 快捷键实现方法
    showShortcutsPanel() {
        const backdrop = document.getElementById('shortcuts-backdrop');
        const panel = document.getElementById('shortcuts-panel');
        
        backdrop.classList.add('show');
        panel.classList.add('show');
        
        // 聚焦搜索框
        setTimeout(() => {
            const searchInput = panel.querySelector('.shortcuts-search-input');
            searchInput.focus();
        }, 100);
    }
    
    hideShortcutsPanel() {
        const backdrop = document.getElementById('shortcuts-backdrop');
        const panel = document.getElementById('shortcuts-panel');
        
        backdrop.classList.remove('show');
        panel.classList.remove('show');
    }
    
    showCommandPalette() {
        const palette = document.getElementById('command-palette');
        palette.classList.add('show');
        
        setTimeout(() => {
            const input = palette.querySelector('.command-input');
            input.focus();
            input.value = '';
        }, 100);
    }
    
    hideCommandPalette() {
        const palette = document.getElementById('command-palette');
        palette.classList.remove('show');
    }
    
    handleEscape() {
        // 按优先级关闭面板
        if (document.getElementById('command-palette').classList.contains('show')) {
            this.hideCommandPalette();
        } else if (document.getElementById('shortcuts-panel').classList.contains('show')) {
            this.hideShortcutsPanel();
        } else if (document.getElementById('notification-center')?.classList.contains('open')) {
            window.statusNotificationSystem?.closeNotificationCenter();
        } else if (document.getElementById('settings-panel')?.classList.contains('open')) {
            window.themeManager?.closeSettingsPanel();
        }
    }
    
    focusSearch() {
        const searchInput = document.getElementById('global-search-input');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    }
    
    newChat() {
        if (window.chatHistoryManager) {
            window.chatHistoryManager.createNewConversation();
        }
    }
    
    sendMessage() {
        if (window.smartInput) {
            const message = window.smartInput.getValue();
            if (message.trim()) {
                window.sendSmartMessage(message);
                window.smartInput.clear();
            }
        }
    }
    
    toggleTheme() {
        if (window.themeManager) {
            const currentTheme = window.themeManager.currentTheme;
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            window.themeManager.setTheme(newTheme);
        }
    }
    
    toggleNotifications() {
        if (window.statusNotificationSystem) {
            window.statusNotificationSystem.toggleNotificationCenter();
        }
    }
    
    // 过滤快捷键
    filterShortcuts(query) {
        const sections = document.querySelectorAll('.shortcuts-section');
        const queryLower = query.toLowerCase();
        
        sections.forEach(section => {
            const items = section.querySelectorAll('.shortcut-item');
            let hasVisibleItems = false;
            
            items.forEach(item => {
                const description = item.querySelector('.shortcut-description').textContent.toLowerCase();
                const keys = item.querySelector('.shortcut-keys').textContent.toLowerCase();
                
                if (description.includes(queryLower) || keys.includes(queryLower)) {
                    item.style.display = 'flex';
                    item.classList.toggle('highlighted', queryLower.length > 0);
                    hasVisibleItems = true;
                } else {
                    item.style.display = 'none';
                    item.classList.remove('highlighted');
                }
            });
            
            section.style.display = hasVisibleItems ? 'block' : 'none';
        });
    }
    
    // 其他快捷键实现...
    focusSidebar() { document.getElementById('sidebar')?.focus(); }
    focusMainChat() { document.getElementById('chat-messages')?.focus(); }
    focusAssistantPanel() { document.getElementById('assistant-panel')?.focus(); }
    clearChat() { if (confirm('确定要清空当前对话吗？')) { /* 实现清空逻辑 */ } }
    toggleDarkMode() { this.toggleTheme(); }
    markAllNotificationsRead() { window.statusNotificationSystem?.markAllAsRead(); }
    toggleKeyboardMode() { this.enableKeyboardMode(); }
    toggleAdvancedSearch() { window.searchSystem?.toggleFiltersPanel(); }
    toggleDebugInfo() { window.debugHelper?.showDebugInfo(); }
    reloadApp() { if (confirm('确定要重新加载应用吗？')) { window.location.reload(); } }
    openSettings() { window.themeManager?.toggleSettingsPanel(); }
    
    handleTabNavigation(e) {
        // Tab导航逻辑
        this.enableKeyboardMode();
    }
    
    handleShiftTabNavigation(e) {
        // Shift+Tab反向导航逻辑
        this.enableKeyboardMode();
    }
    
    filterCommands(query) {
        // 命令面板过滤逻辑
        const items = document.querySelectorAll('.command-item');
        const queryLower = query.toLowerCase();
        
        items.forEach((item, index) => {
            const title = item.querySelector('.command-title').textContent.toLowerCase();
            const description = item.querySelector('.command-description').textContent.toLowerCase();
            
            if (title.includes(queryLower) || description.includes(queryLower)) {
                item.style.display = 'flex';
                if (index === 0) item.classList.add('selected');
            } else {
                item.style.display = 'none';
                item.classList.remove('selected');
            }
        });
    }
    
    handleCommandPaletteKeydown(e) {
        const items = Array.from(document.querySelectorAll('.command-item:not([style*="display: none"])'));
        const selected = document.querySelector('.command-item.selected');
        const currentIndex = items.indexOf(selected);
        
        if (e.key === 'ArrowDown') {
            e.preventDefault();
            const nextIndex = (currentIndex + 1) % items.length;
            this.selectCommandItem(items, nextIndex);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            const prevIndex = currentIndex === 0 ? items.length - 1 : currentIndex - 1;
            this.selectCommandItem(items, prevIndex);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (selected) {
                // 执行选中的命令
                this.executeCommand(selected.dataset.command);
                this.hideCommandPalette();
            }
        } else if (e.key === 'Escape') {
            this.hideCommandPalette();
        }
    }
    
    selectCommandItem(items, index) {
        items.forEach(item => item.classList.remove('selected'));
        if (items[index]) {
            items[index].classList.add('selected');
            items[index].scrollIntoView({ block: 'nearest' });
        }
    }
    
    executeCommand(commandId) {
        const commands = {
            'new-chat': () => this.newChat(),
            'search': () => this.focusSearch(),
            'toggle-theme': () => this.toggleTheme(),
            'notifications': () => this.toggleNotifications(),
            'settings': () => this.openSettings(),
            'help': () => this.showShortcutsPanel()
        };
        
        const command = commands[commandId];
        if (command) {
            command();
        }
    }
}

// 全局键盘快捷键系统实例
window.keyboardShortcuts = new KeyboardShortcutSystem();
