package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ConversationalOpsService 对话式运维服务
type ConversationalOpsService struct {
	logger              *logrus.Logger
	diagnosisEngine     *IntelligentDiagnosisEngine
	toolFramework       *OpsToolFramework
	conversationManager *ConversationManager
	suggestionEngine    *SuggestionEngine
	// contextManager      *ConversationContextManager
}

// ConversationManager 对话管理器
type ConversationManager struct {
	sessions map[string]*ConversationSession
	mutex    sync.RWMutex
	logger   *logrus.Logger
}

// ConversationSession 对话会话
type ConversationSession struct {
	SessionID    string                   `json:"session_id"`
	UserID       int64                    `json:"user_id"`
	StartTime    time.Time                `json:"start_time"`
	LastActivity time.Time                `json:"last_activity"`
	Context      *OpsConversationContext  `json:"context"`
	History      []OpsConversationMessage `json:"history"`
	State        string                   `json:"state"`
	Metadata     map[string]interface{}   `json:"metadata"`
}

// OpsConversationContext 运维对话上下文
type OpsConversationContext struct {
	CurrentTopic    string                 `json:"current_topic"`
	ActiveTargets   []string               `json:"active_targets"`   // 当前关注的主机/服务
	PendingActions  []OpsPendingAction     `json:"pending_actions"`  // 待执行的操作
	RecentResults   []OperationResult      `json:"recent_results"`   // 最近的操作结果
	UserPreferences map[string]interface{} `json:"user_preferences"` // 用户偏好
	WorkflowState   map[string]interface{} `json:"workflow_state"`   // 工作流状态
}

// OpsConversationMessage 运维对话消息
type OpsConversationMessage struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"` // user, assistant, system
	Content   string                 `json:"content"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// OpsPendingAction 运维待执行操作
type OpsPendingAction struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
	CreatedAt   time.Time              `json:"created_at"`
	ExpiresAt   time.Time              `json:"expires_at"`
}

// OperationResult 操作结果
type OperationResult struct {
	ID        string        `json:"id"`
	Operation string        `json:"operation"`
	Target    string        `json:"target"`
	Success   bool          `json:"success"`
	Result    interface{}   `json:"result"`
	Timestamp time.Time     `json:"timestamp"`
	Duration  time.Duration `json:"duration"`
}

// SuggestionEngine 建议引擎
type SuggestionEngine struct {
	logger *logrus.Logger
}

// ConversationalRequest 对话式请求
type ConversationalRequest struct {
	SessionID string                 `json:"session_id"`
	UserID    int64                  `json:"user_id"`
	Message   string                 `json:"message"`
	Context   map[string]interface{} `json:"context"`
}

// ConversationalResponse 对话式响应
type ConversationalResponse struct {
	Success     bool                    `json:"success"`
	Message     string                  `json:"message"`
	Suggestions []string                `json:"suggestions"`
	Actions     []SuggestedAction       `json:"actions"`
	Context     *OpsConversationContext `json:"context"`
	Metadata    map[string]interface{}  `json:"metadata"`
}

// SuggestedAction 建议操作
type SuggestedAction struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
	Confidence  float64                `json:"confidence"`
}

// NewConversationalOpsService 创建对话式运维服务
func NewConversationalOpsService(
	logger *logrus.Logger,
	diagnosisEngine *IntelligentDiagnosisEngine,
	toolFramework *OpsToolFramework,
) *ConversationalOpsService {

	conversationManager := &ConversationManager{
		sessions: make(map[string]*ConversationSession),
		logger:   logger,
	}

	suggestionEngine := &SuggestionEngine{
		logger: logger,
	}

	// contextManager := &ConversationContextManager{
	//	logger: logger,
	// }

	return &ConversationalOpsService{
		logger:              logger,
		diagnosisEngine:     diagnosisEngine,
		toolFramework:       toolFramework,
		conversationManager: conversationManager,
		suggestionEngine:    suggestionEngine,
		// contextManager:      contextManager,
	}
}

// ProcessConversation 处理对话
func (cos *ConversationalOpsService) ProcessConversation(ctx context.Context, request *ConversationalRequest) (*ConversationalResponse, error) {
	// 获取或创建会话
	session := cos.conversationManager.GetOrCreateSession(request.SessionID, request.UserID)

	// 更新会话活动时间
	session.LastActivity = time.Now()

	// 添加用户消息到历史
	userMessage := OpsConversationMessage{
		ID:        fmt.Sprintf("msg_%d", time.Now().UnixNano()),
		Type:      "user",
		Content:   request.Message,
		Timestamp: time.Now(),
		Metadata:  request.Context,
	}
	session.History = append(session.History, userMessage)

	cos.logger.WithFields(logrus.Fields{
		"session_id": request.SessionID,
		"user_id":    request.UserID,
		"message":    request.Message,
	}).Info("Processing conversational request")

	// 分析用户意图和上下文
	intent, err := cos.analyzeUserIntent(request.Message, session.Context)
	if err != nil {
		return cos.createErrorResponse(fmt.Sprintf("意图分析失败: %v", err)), nil
	}

	// 根据意图处理请求
	var response *ConversationalResponse
	switch intent.Type {
	case "diagnosis_request":
		response, err = cos.handleDiagnosisRequest(ctx, intent, session)
	case "tool_execution":
		response, err = cos.handleToolExecution(ctx, intent, session)
	case "context_query":
		response, err = cos.handleContextQuery(ctx, intent, session)
	case "help_request":
		response, err = cos.handleHelpRequest(ctx, intent, session)
	default:
		response = cos.createGeneralResponse("我理解您的请求，但目前还不支持这种操作类型。请尝试询问主机诊断、工具执行或帮助相关的问题。")
	}

	if err != nil {
		cos.logger.WithError(err).Error("Failed to process conversation")
		return cos.createErrorResponse(fmt.Sprintf("处理请求失败: %v", err)), nil
	}

	// 添加助手响应到历史
	assistantMessage := OpsConversationMessage{
		ID:        fmt.Sprintf("msg_%d", time.Now().UnixNano()),
		Type:      "assistant",
		Content:   response.Message,
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"intent_type": intent.Type,
			"success":     response.Success,
		},
	}
	session.History = append(session.History, assistantMessage)

	// 更新会话上下文
	response.Context = session.Context

	// 生成智能建议
	suggestions := cos.suggestionEngine.GenerateSuggestions(session.Context, intent)
	response.Suggestions = suggestions

	return response, nil
}

// GetSessionHistory 获取会话历史
func (cos *ConversationalOpsService) GetSessionHistory(sessionID string) ([]OpsConversationMessage, error) {
	session := cos.conversationManager.GetSession(sessionID)
	if session == nil {
		return nil, fmt.Errorf("session not found")
	}

	return session.History, nil
}

// ClearSessionContext 清除会话上下文
func (cos *ConversationalOpsService) ClearSessionContext(sessionID string) error {
	session := cos.conversationManager.GetSession(sessionID)
	if session == nil {
		return fmt.Errorf("session not found")
	}

	// 重置上下文
	session.Context = &OpsConversationContext{
		ActiveTargets:   make([]string, 0),
		PendingActions:  make([]OpsPendingAction, 0),
		RecentResults:   make([]OperationResult, 0),
		UserPreferences: make(map[string]interface{}),
		WorkflowState:   make(map[string]interface{}),
	}

	cos.logger.WithField("session_id", sessionID).Info("Session context cleared")

	return nil
}

// OpsIntent 运维意图结构
type OpsIntent struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Context    map[string]interface{} `json:"context"`
}

// analyzeUserIntent 分析用户意图
func (cos *ConversationalOpsService) analyzeUserIntent(message string, context *OpsConversationContext) (*OpsIntent, error) {
	// 简化的意图分析逻辑
	lowerMessage := strings.ToLower(message)

	// 诊断相关关键词
	diagnosisKeywords := []string{"诊断", "检查", "为什么", "为啥", "问题", "故障", "连接", "离线"}
	for _, keyword := range diagnosisKeywords {
		if strings.Contains(lowerMessage, keyword) {
			return &OpsIntent{
				Type:       "diagnosis_request",
				Confidence: 0.8,
				Parameters: map[string]interface{}{
					"message": message,
				},
			}, nil
		}
	}

	// 工具执行关键词
	toolKeywords := []string{"执行", "运行", "启动", "停止", "重启", "命令"}
	for _, keyword := range toolKeywords {
		if strings.Contains(lowerMessage, keyword) {
			return &OpsIntent{
				Type:       "tool_execution",
				Confidence: 0.7,
				Parameters: map[string]interface{}{
					"message": message,
				},
			}, nil
		}
	}

	// 帮助请求关键词
	helpKeywords := []string{"帮助", "help", "怎么", "如何", "什么"}
	for _, keyword := range helpKeywords {
		if strings.Contains(lowerMessage, keyword) {
			return &OpsIntent{
				Type:       "help_request",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"message": message,
				},
			}, nil
		}
	}

	// 默认为上下文查询
	return &OpsIntent{
		Type:       "context_query",
		Confidence: 0.5,
		Parameters: map[string]interface{}{
			"message": message,
		},
	}, nil
}

// handleDiagnosisRequest 处理诊断请求
func (cos *ConversationalOpsService) handleDiagnosisRequest(ctx context.Context, intent *OpsIntent, session *ConversationSession) (*ConversationalResponse, error) {
	message := intent.Parameters["message"].(string)

	// 从消息中提取主机信息
	hostIP := cos.extractHostIP(message)
	if hostIP == "" {
		return cos.createGeneralResponse("请指定要诊断的主机IP地址，例如：检查*************的连接状态"), nil
	}

	// 执行诊断
	result, err := cos.diagnosisEngine.DiagnoseHost(ctx, hostIP, "comprehensive")
	if err != nil {
		return cos.createErrorResponse(fmt.Sprintf("诊断失败: %v", err)), nil
	}

	// 更新会话上下文
	session.Context.CurrentTopic = "host_diagnosis"
	session.Context.ActiveTargets = []string{hostIP}
	session.Context.RecentResults = append(session.Context.RecentResults, OperationResult{
		ID:        fmt.Sprintf("diag_%d", time.Now().UnixNano()),
		Operation: "diagnosis",
		Target:    hostIP,
		Success:   result.Status != "error",
		Result:    result,
		Timestamp: time.Now(),
		Duration:  result.Duration,
	})

	// 格式化诊断结果
	content := cos.formatDiagnosisForConversation(result)

	return &ConversationalResponse{
		Success: result.Status != "error",
		Message: content,
		Actions: cos.generateDiagnosisActions(result),
	}, nil
}

// handleToolExecution 处理工具执行
func (cos *ConversationalOpsService) handleToolExecution(ctx context.Context, intent *OpsIntent, session *ConversationSession) (*ConversationalResponse, error) {
	return cos.createGeneralResponse("🔧 工具执行功能正在开发中，敬请期待"), nil
}

// handleContextQuery 处理上下文查询
func (cos *ConversationalOpsService) handleContextQuery(ctx context.Context, intent *OpsIntent, session *ConversationSession) (*ConversationalResponse, error) {
	return cos.createGeneralResponse("💬 我是您的智能运维助手，可以帮您诊断主机问题、执行运维操作。请告诉我您需要什么帮助？"), nil
}

// handleHelpRequest 处理帮助请求
func (cos *ConversationalOpsService) handleHelpRequest(ctx context.Context, intent *OpsIntent, session *ConversationSession) (*ConversationalResponse, error) {
	helpContent := `🤖 **智能运维助手帮助**

我可以帮您完成以下操作：

🔍 **主机诊断**
- 检查主机连接状态
- 诊断网络问题
- 分析认证问题
- 故障排查

💡 **使用示例**：
- "检查*************的连接状态"
- "为什么192.168.1.50连接不上"
- "诊断web-server的问题"

🛠️ **更多功能正在开发中**...`

	return cos.createGeneralResponse(helpContent), nil
}

// 辅助方法
func (cos *ConversationalOpsService) createErrorResponse(message string) *ConversationalResponse {
	return &ConversationalResponse{
		Success: false,
		Message: fmt.Sprintf("❌ %s", message),
	}
}

func (cos *ConversationalOpsService) createGeneralResponse(message string) *ConversationalResponse {
	return &ConversationalResponse{
		Success: true,
		Message: message,
	}
}

// extractHostIP 从消息中提取主机IP
func (cos *ConversationalOpsService) extractHostIP(message string) string {
	// 简单的IP地址正则匹配
	words := strings.Fields(message)
	for _, word := range words {
		// 简单检查是否为IP格式
		parts := strings.Split(word, ".")
		if len(parts) == 4 {
			// 进一步验证是否为有效IP
			validIP := true
			for _, part := range parts {
				if len(part) == 0 || len(part) > 3 {
					validIP = false
					break
				}
				// 简单检查是否为数字
				for _, char := range part {
					if char < '0' || char > '9' {
						validIP = false
						break
					}
				}
				if !validIP {
					break
				}
			}
			if validIP {
				return word
			}
		}
	}
	return ""
}

// formatDiagnosisForConversation 为对话格式化诊断结果
func (cos *ConversationalOpsService) formatDiagnosisForConversation(result *DiagnosisResult) string {
	var content strings.Builder

	statusIcon := cos.getStatusIcon(result.Status)
	content.WriteString(fmt.Sprintf("%s **主机诊断完成**\n\n", statusIcon))
	content.WriteString(fmt.Sprintf("🎯 **目标主机**: %s\n", result.Target.Identifier))
	content.WriteString(fmt.Sprintf("📋 **诊断结果**: %s\n\n", result.Summary))

	// 关键问题摘要
	var issues []string
	var warnings []string

	for _, detail := range result.Details {
		if detail.Status == "fail" {
			issues = append(issues, detail.Message)
		} else if detail.Status == "warning" {
			warnings = append(warnings, detail.Message)
		}
	}

	if len(issues) > 0 {
		content.WriteString("❌ **发现问题**:\n")
		for _, issue := range issues {
			content.WriteString(fmt.Sprintf("  • %s\n", issue))
		}
		content.WriteString("\n")
	}

	if len(warnings) > 0 {
		content.WriteString("⚠️ **注意事项**:\n")
		for _, warning := range warnings {
			content.WriteString(fmt.Sprintf("  • %s\n", warning))
		}
		content.WriteString("\n")
	}

	// 建议
	if len(result.Suggestions) > 0 {
		content.WriteString("💡 **建议**:\n")
		for i, suggestion := range result.Suggestions {
			content.WriteString(fmt.Sprintf("%d. %s\n", i+1, suggestion))
		}
	}

	return content.String()
}

// generateDiagnosisActions 生成诊断相关的建议操作
func (cos *ConversationalOpsService) generateDiagnosisActions(result *DiagnosisResult) []SuggestedAction {
	var actions []SuggestedAction

	if result.Status == "error" {
		actions = append(actions, SuggestedAction{
			ID:          "retry_diagnosis",
			Type:        "diagnosis",
			Title:       "重新诊断",
			Description: "重新执行主机诊断",
			Confidence:  0.8,
		})

		actions = append(actions, SuggestedAction{
			ID:          "check_network",
			Type:        "network_test",
			Title:       "网络测试",
			Description: "执行详细的网络连通性测试",
			Confidence:  0.7,
		})
	}

	return actions
}

// getStatusIcon 获取状态图标
func (cos *ConversationalOpsService) getStatusIcon(status string) string {
	switch status {
	case "success":
		return "✅"
	case "warning":
		return "⚠️"
	case "error":
		return "❌"
	default:
		return "ℹ️"
	}
}

// ConversationManager 方法实现

// GetOrCreateSession 获取或创建会话
func (cm *ConversationManager) GetOrCreateSession(sessionID string, userID int64) *ConversationSession {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if session, exists := cm.sessions[sessionID]; exists {
		return session
	}

	// 创建新会话
	session := &ConversationSession{
		SessionID:    sessionID,
		UserID:       userID,
		StartTime:    time.Now(),
		LastActivity: time.Now(),
		Context: &OpsConversationContext{
			ActiveTargets:   make([]string, 0),
			PendingActions:  make([]OpsPendingAction, 0),
			RecentResults:   make([]OperationResult, 0),
			UserPreferences: make(map[string]interface{}),
			WorkflowState:   make(map[string]interface{}),
		},
		History:  make([]OpsConversationMessage, 0),
		State:    "active",
		Metadata: make(map[string]interface{}),
	}

	cm.sessions[sessionID] = session

	cm.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("New conversation session created")

	return session
}

// GetSession 获取会话
func (cm *ConversationManager) GetSession(sessionID string) *ConversationSession {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	return cm.sessions[sessionID]
}

// CleanupExpiredSessions 清理过期会话
func (cm *ConversationManager) CleanupExpiredSessions(maxAge time.Duration) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	now := time.Now()
	var expiredSessions []string

	for sessionID, session := range cm.sessions {
		if now.Sub(session.LastActivity) > maxAge {
			expiredSessions = append(expiredSessions, sessionID)
		}
	}

	for _, sessionID := range expiredSessions {
		delete(cm.sessions, sessionID)
		cm.logger.WithField("session_id", sessionID).Info("Expired session cleaned up")
	}

	if len(expiredSessions) > 0 {
		cm.logger.WithField("cleaned_sessions", len(expiredSessions)).Info("Session cleanup completed")
	}
}

// SuggestionEngine 方法实现

// GenerateSuggestions 生成建议
func (se *SuggestionEngine) GenerateSuggestions(context *OpsConversationContext, intent *OpsIntent) []string {
	var suggestions []string

	switch intent.Type {
	case "diagnosis_request":
		suggestions = append(suggestions, "查看详细的诊断报告")
		suggestions = append(suggestions, "执行网络连通性测试")
		suggestions = append(suggestions, "检查SSH认证配置")

	case "help_request":
		suggestions = append(suggestions, "查看主机列表")
		suggestions = append(suggestions, "诊断特定主机")
		suggestions = append(suggestions, "查看系统监控")

	default:
		suggestions = append(suggestions, "需要帮助吗？输入'帮助'查看可用功能")
	}

	// 基于上下文添加建议
	if len(context.ActiveTargets) > 0 {
		suggestions = append(suggestions, fmt.Sprintf("继续诊断主机 %s", context.ActiveTargets[0]))
	}

	return suggestions
}
