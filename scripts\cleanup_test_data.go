package main

import (
	"fmt"
	"log"
	"os"
	"path/filepath"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// 数据库清理脚本
// 用于清除测试数据，保留生产环境的真实数据

func main() {
	fmt.Println("=== AI运维管理平台数据库清理工具 ===")
	fmt.Println("此工具将清除测试数据，保留真实的生产数据")
	
	// 确认操作
	fmt.Print("确认要清理测试数据吗？(y/N): ")
	var confirm string
	fmt.Scanln(&confirm)
	
	if confirm != "y" && confirm != "Y" {
		fmt.Println("操作已取消")
		return
	}

	// 连接数据库
	dbPath := "./data/aiops.db"
	if _, err := os.Stat(dbPath); os.IsNotExist(err) {
		fmt.Printf("数据库文件不存在: %s\n", dbPath)
		return
	}

	db, err := gorm.Open(sqlite.Open(dbPath), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}

	fmt.Println("开始清理测试数据...")

	// 清理测试主机数据
	cleanupTestHosts(db)
	
	// 清理测试用户数据
	cleanupTestUsers(db)
	
	// 清理测试会话数据
	cleanupTestSessions(db)
	
	// 清理测试告警数据
	cleanupTestAlerts(db)
	
	// 清理测试日志数据
	cleanupTestLogs(db)

	// 清理缓存文件
	cleanupCacheFiles()

	// 清理日志文件
	cleanupLogFiles()

	fmt.Println("✅ 测试数据清理完成！")
	fmt.Println("系统现在可以使用真实的生产数据了。")
}

// cleanupTestHosts 清理测试主机数据
func cleanupTestHosts(db *gorm.DB) {
	fmt.Println("清理测试主机数据...")
	
	// 删除明显的测试主机
	testHostPatterns := []string{
		"*************",
		"*************", 
		"*************",
		"test-server%",
		"demo-host%",
		"localhost",
		"127.0.0.1",
	}

	for _, pattern := range testHostPatterns {
		result := db.Exec("DELETE FROM hosts WHERE ip_address LIKE ? OR hostname LIKE ?", pattern, pattern)
		if result.Error != nil {
			fmt.Printf("清理主机数据时出错: %v\n", result.Error)
		} else if result.RowsAffected > 0 {
			fmt.Printf("删除了 %d 个测试主机记录 (模式: %s)\n", result.RowsAffected, pattern)
		}
	}
}

// cleanupTestUsers 清理测试用户数据
func cleanupTestUsers(db *gorm.DB) {
	fmt.Println("清理测试用户数据...")
	
	// 删除测试用户（保留admin用户）
	testUserPatterns := []string{
		"test%",
		"demo%", 
		"user%",
		"sample%",
	}

	for _, pattern := range testUserPatterns {
		result := db.Exec("DELETE FROM users WHERE username LIKE ? AND username != 'admin'", pattern)
		if result.Error != nil {
			fmt.Printf("清理用户数据时出错: %v\n", result.Error)
		} else if result.RowsAffected > 0 {
			fmt.Printf("删除了 %d 个测试用户记录 (模式: %s)\n", result.RowsAffected, pattern)
		}
	}
}

// cleanupTestSessions 清理测试会话数据
func cleanupTestSessions(db *gorm.DB) {
	fmt.Println("清理过期会话数据...")
	
	// 删除过期的会话数据
	result := db.Exec("DELETE FROM sessions WHERE expires_at < datetime('now')")
	if result.Error != nil {
		fmt.Printf("清理会话数据时出错: %v\n", result.Error)
	} else if result.RowsAffected > 0 {
		fmt.Printf("删除了 %d 个过期会话记录\n", result.RowsAffected)
	}

	// 删除测试会话
	result = db.Exec("DELETE FROM sessions WHERE session_id LIKE 'test_%' OR session_id LIKE 'demo_%'")
	if result.Error != nil {
		fmt.Printf("清理测试会话时出错: %v\n", result.Error)
	} else if result.RowsAffected > 0 {
		fmt.Printf("删除了 %d 个测试会话记录\n", result.RowsAffected)
	}
}

// cleanupTestAlerts 清理测试告警数据
func cleanupTestAlerts(db *gorm.DB) {
	fmt.Println("清理测试告警数据...")
	
	// 删除测试告警
	result := db.Exec("DELETE FROM alerts WHERE title LIKE 'Test%' OR title LIKE 'Demo%' OR description LIKE '%test%'")
	if result.Error != nil {
		fmt.Printf("清理告警数据时出错: %v\n", result.Error)
	} else if result.RowsAffected > 0 {
		fmt.Printf("删除了 %d 个测试告警记录\n", result.RowsAffected)
	}
}

// cleanupTestLogs 清理测试日志数据
func cleanupTestLogs(db *gorm.DB) {
	fmt.Println("清理旧日志数据...")
	
	// 删除30天前的日志数据
	result := db.Exec("DELETE FROM logs WHERE created_at < datetime('now', '-30 days')")
	if result.Error != nil {
		fmt.Printf("清理日志数据时出错: %v\n", result.Error)
	} else if result.RowsAffected > 0 {
		fmt.Printf("删除了 %d 个旧日志记录\n", result.RowsAffected)
	}
}

// cleanupCacheFiles 清理缓存文件
func cleanupCacheFiles() {
	fmt.Println("清理缓存文件...")
	
	cacheDir := "./cache"
	if _, err := os.Stat(cacheDir); os.IsNotExist(err) {
		fmt.Println("缓存目录不存在，跳过清理")
		return
	}

	err := filepath.Walk(cacheDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		if !info.IsDir() {
			if err := os.Remove(path); err != nil {
				fmt.Printf("删除缓存文件失败: %s, 错误: %v\n", path, err)
			} else {
				fmt.Printf("删除缓存文件: %s\n", path)
			}
		}
		return nil
	})

	if err != nil {
		fmt.Printf("清理缓存文件时出错: %v\n", err)
	}
}

// cleanupLogFiles 清理日志文件
func cleanupLogFiles() {
	fmt.Println("清理旧日志文件...")
	
	logDir := "./logs"
	if _, err := os.Stat(logDir); os.IsNotExist(err) {
		fmt.Println("日志目录不存在，跳过清理")
		return
	}

	err := filepath.Walk(logDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}
		
		// 保留最新的日志文件，删除旧的
		if !info.IsDir() && filepath.Ext(path) == ".log" {
			// 如果文件名包含日期且是7天前的，则删除
			if info.ModTime().Before(filepath.Join(logDir, "aiops.log")) {
				if err := os.Remove(path); err != nil {
					fmt.Printf("删除日志文件失败: %s, 错误: %v\n", path, err)
				} else {
					fmt.Printf("删除旧日志文件: %s\n", path)
				}
			}
		}
		return nil
	})

	if err != nil {
		fmt.Printf("清理日志文件时出错: %v\n", err)
	}
}
