package ai

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// TroubleshootingHandler 故障排查处理器
type TroubleshootingHandler struct {
	logger *logrus.Logger
}

// NewTroubleshootingHandler 创建故障排查处理器
func NewTroubleshootingHandler(logger *logrus.Logger) *TroubleshootingHandler {
	return &TroubleshootingHandler{
		logger: logger,
	}
}

// CanHandle 检查是否能处理该意图
func (t *TroubleshootingHandler) CanHandle(intentType string) bool {
	supportedIntents := []string{"troubleshooting", "log_analysis", "network_diagnostics"}
	for _, intent := range supportedIntents {
		if intent == intentType {
			return true
		}
	}
	return false
}

// GetSupportedIntents 获取支持的意图类型
func (t *TroubleshootingHandler) GetSupportedIntents() []string {
	return []string{"troubleshooting", "log_analysis", "network_diagnostics"}
}

// Handle 处理故障排查意图
func (t *Troubleshooting<PERSON>and<PERSON>) Handle(ctx context.Context, result *EnhancedIntentResult, sessionID string) (*IntentHandleResult, error) {
	t.logger.WithFields(logrus.Fields{
		"intent_type": result.Type,
		"session_id":  sessionID,
		"parameters":  result.Parameters,
	}).Info("Handling troubleshooting intent")

	switch result.Type {
	case "troubleshooting":
		return t.handleTroubleshooting(ctx, result)
	case "log_analysis":
		return t.handleLogAnalysis(ctx, result)
	case "network_diagnostics":
		return t.handleNetworkDiagnostics(ctx, result)
	default:
		return &IntentHandleResult{
			Success: false,
			Message: fmt.Sprintf("不支持的故障排查类型: %s", result.Type),
		}, nil
	}
}

// handleTroubleshooting 处理故障排查
func (t *TroubleshootingHandler) handleTroubleshooting(ctx context.Context, result *EnhancedIntentResult) (*IntentHandleResult, error) {
	// 提取故障信息
	hostIP, hasHost := result.Parameters["host"].(string)
	issueType, hasIssue := result.Parameters["issue_type"].(string)

	if !hasHost {
		return &IntentHandleResult{
			Success: false,
			Message: "请指定要排查的主机IP地址或主机名",
			NextSteps: []string{
				"例如：检查*************的连接问题",
				"例如：排查web-server-01的故障",
			},
		}, nil
	}

	// 生成诊断步骤
	if !hasIssue {
		issueType = "general" // 默认通用诊断
	}
	diagnosticSteps := t.generateDiagnosticSteps(hostIP, issueType)

	return &IntentHandleResult{
		Success: true,
		Message: fmt.Sprintf("🔍 开始对主机 %s 进行故障诊断", hostIP),
		Data: map[string]interface{}{
			"target_host":      hostIP,
			"issue_type":       issueType,
			"started_at":       time.Now(),
			"diagnostic_steps": diagnosticSteps,
		},
		Actions: t.createDiagnosticActions(hostIP, diagnosticSteps),
		NextSteps: []string{
			"正在执行网络连通性测试...",
			"正在检查SSH服务状态...",
			"正在分析系统日志...",
		},
	}, nil
}

// generateDiagnosticSteps 生成诊断步骤
func (t *TroubleshootingHandler) generateDiagnosticSteps(hostIP, issueType string) []string {
	baseSteps := []string{
		"网络连通性测试 (ping)",
		"端口扫描 (SSH端口22)",
		"DNS解析检查",
	}

	switch issueType {
	case "connection_failure":
		return append(baseSteps, []string{
			"SSH连接测试",
			"防火墙规则检查",
			"网络路由分析",
		}...)
	case "performance_issue":
		return append(baseSteps, []string{
			"系统负载检查",
			"CPU使用率分析",
			"内存使用情况",
			"磁盘IO性能",
		}...)
	case "service_failure":
		return append(baseSteps, []string{
			"服务状态检查",
			"进程列表分析",
			"服务日志查看",
			"依赖服务检查",
		}...)
	default:
		return append(baseSteps, []string{
			"系统状态概览",
			"错误日志分析",
			"资源使用检查",
		}...)
	}
}

// createDiagnosticActions 创建诊断操作
func (t *TroubleshootingHandler) createDiagnosticActions(hostIP string, steps []string) []ActionItem {
	actions := make([]ActionItem, len(steps))

	for i, step := range steps {
		actions[i] = ActionItem{
			ID:          fmt.Sprintf("diagnostic_step_%d", i+1),
			Type:        "diagnostic",
			Description: step,
			Status:      "pending",
			Parameters: map[string]interface{}{
				"target_host": hostIP,
				"step_order":  i + 1,
			},
		}
	}

	return actions
}

// handleLogAnalysis 处理日志分析
func (t *TroubleshootingHandler) handleLogAnalysis(ctx context.Context, result *EnhancedIntentResult) (*IntentHandleResult, error) {
	logType, hasLogType := result.Parameters["log_type"].(string)
	if !hasLogType {
		logType = "system" // 默认系统日志
	}

	timeRange, hasTimeRange := result.Parameters["time_range"].(string)
	if !hasTimeRange {
		timeRange = "last_hour" // 默认最近一小时
	}

	return &IntentHandleResult{
		Success: true,
		Message: fmt.Sprintf("📊 开始分析 %s 日志 (时间范围: %s)", logType, timeRange),
		Data: map[string]interface{}{
			"log_type":   logType,
			"time_range": timeRange,
			"started_at": time.Now(),
		},
		Actions: []ActionItem{
			{
				ID:          "fetch_logs",
				Type:        "log_operation",
				Description: "获取日志文件",
				Status:      "pending",
				Parameters: map[string]interface{}{
					"log_type":   logType,
					"time_range": timeRange,
				},
			},
			{
				ID:          "analyze_patterns",
				Type:        "analysis",
				Description: "分析日志模式",
				Status:      "pending",
				Parameters: map[string]interface{}{
					"analysis_type": "pattern_recognition",
				},
			},
			{
				ID:          "extract_errors",
				Type:        "analysis",
				Description: "提取错误信息",
				Status:      "pending",
				Parameters: map[string]interface{}{
					"filter": "error_level",
				},
			},
		},
		NextSteps: []string{
			"正在获取日志文件...",
			"正在分析错误模式...",
			"正在生成分析报告...",
		},
	}, nil
}

// handleNetworkDiagnostics 处理网络诊断
func (t *TroubleshootingHandler) handleNetworkDiagnostics(ctx context.Context, result *EnhancedIntentResult) (*IntentHandleResult, error) {
	targetHost, hasTarget := result.Parameters["target_host"].(string)
	if !hasTarget {
		return &IntentHandleResult{
			Success: false,
			Message: "请指定要诊断的目标主机",
			NextSteps: []string{
				"例如：诊断到*************的网络连接",
				"例如：检查web-server-01的网络状态",
			},
		}, nil
	}

	diagnosticType, hasDiagnosticType := result.Parameters["diagnostic_type"].(string)
	if !hasDiagnosticType {
		diagnosticType = "comprehensive" // 默认综合诊断
	}

	networkTests := t.generateNetworkTests(targetHost, diagnosticType)

	return &IntentHandleResult{
		Success: true,
		Message: fmt.Sprintf("🌐 开始对 %s 进行网络诊断", targetHost),
		Data: map[string]interface{}{
			"target_host":     targetHost,
			"diagnostic_type": diagnosticType,
			"started_at":      time.Now(),
			"network_tests":   networkTests,
		},
		Actions: t.createNetworkTestActions(targetHost, networkTests),
		NextSteps: []string{
			"正在执行ping测试...",
			"正在检查端口连通性...",
			"正在分析网络路径...",
		},
	}, nil
}

// generateNetworkTests 生成网络测试
func (t *TroubleshootingHandler) generateNetworkTests(targetHost, diagnosticType string) []string {
	baseTests := []string{
		"ICMP ping测试",
		"DNS解析测试",
	}

	switch diagnosticType {
	case "connectivity":
		return append(baseTests, []string{
			"TCP连接测试 (端口22)",
			"HTTP连接测试 (端口80)",
			"HTTPS连接测试 (端口443)",
		}...)
	case "performance":
		return append(baseTests, []string{
			"网络延迟测试",
			"带宽测试",
			"丢包率测试",
		}...)
	case "comprehensive":
		return append(baseTests, []string{
			"端口扫描 (常用端口)",
			"路由跟踪",
			"网络延迟测试",
			"MTU发现",
		}...)
	default:
		return baseTests
	}
}

// createNetworkTestActions 创建网络测试操作
func (t *TroubleshootingHandler) createNetworkTestActions(targetHost string, tests []string) []ActionItem {
	actions := make([]ActionItem, len(tests))

	for i, test := range tests {
		actions[i] = ActionItem{
			ID:          fmt.Sprintf("network_test_%d", i+1),
			Type:        "network_test",
			Description: test,
			Status:      "pending",
			Parameters: map[string]interface{}{
				"target_host": targetHost,
				"test_type":   strings.ToLower(strings.ReplaceAll(test, " ", "_")),
			},
		}
	}

	return actions
}
