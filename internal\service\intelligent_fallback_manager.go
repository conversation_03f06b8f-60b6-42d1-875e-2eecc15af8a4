package service

import (
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// FallbackLevel 降级级别
type FallbackLevel int

const (
	FallbackLevelNone        FallbackLevel = 0 // 无降级
	FallbackLevelPartial     FallbackLevel = 1 // 部分降级
	FallbackLevelTraditional FallbackLevel = 2 // 传统模式
	FallbackLevelBasic       FallbackLevel = 3 // 基础模式
	FallbackLevelEmergency   FallbackLevel = 4 // 紧急模式
)

// FallbackReason 降级原因
type FallbackReason string

const (
	ReasonTimeout          FallbackReason = "timeout"
	ReasonAPIError         FallbackReason = "api_error"
	ReasonServiceUnavailable FallbackReason = "service_unavailable"
	ReasonRateLimited      FallbackReason = "rate_limited"
	ReasonParsingError     FallbackReason = "parsing_error"
	ReasonExecutionError   FallbackReason = "execution_error"
	ReasonUnknownError     FallbackReason = "unknown_error"
)

// FallbackStrategy 降级策略
type FallbackStrategy struct {
	Level       FallbackLevel  `json:"level"`
	Reason      FallbackReason `json:"reason"`
	Description string         `json:"description"`
	RetryCount  int            `json:"retry_count"`
	MaxRetries  int            `json:"max_retries"`
	NextLevel   FallbackLevel  `json:"next_level"`
}

// IntelligentFallbackManager 智能降级管理器
type IntelligentFallbackManager struct {
	logger           *logrus.Logger
	currentLevel     FallbackLevel
	strategies       map[FallbackLevel]*FallbackStrategy
	errorHistory     []FallbackEvent
	mutex            sync.RWMutex
	
	// 服务健康状态
	enhancedServiceHealth    bool
	traditionalServiceHealth bool
	basicServiceHealth       bool
	
	// 统计信息
	totalRequests     int64
	successfulRequests int64
	fallbackRequests  int64
	
	// 配置
	maxErrorHistory   int
	healthCheckInterval time.Duration
}

// FallbackEvent 降级事件
type FallbackEvent struct {
	Timestamp   time.Time      `json:"timestamp"`
	Level       FallbackLevel  `json:"level"`
	Reason      FallbackReason `json:"reason"`
	Error       string         `json:"error"`
	UserID      int64          `json:"user_id"`
	SessionID   string         `json:"session_id"`
	Message     string         `json:"message"`
	Duration    time.Duration  `json:"duration"`
}

// NewIntelligentFallbackManager 创建智能降级管理器
func NewIntelligentFallbackManager(logger *logrus.Logger) *IntelligentFallbackManager {
	manager := &IntelligentFallbackManager{
		logger:                   logger,
		currentLevel:             FallbackLevelNone,
		strategies:               make(map[FallbackLevel]*FallbackStrategy),
		errorHistory:             make([]FallbackEvent, 0),
		enhancedServiceHealth:    true,
		traditionalServiceHealth: true,
		basicServiceHealth:       true,
		maxErrorHistory:          100,
		healthCheckInterval:      30 * time.Second,
	}
	
	// 初始化降级策略
	manager.initializeFallbackStrategies()
	
	// 启动健康检查
	go manager.startHealthCheck()
	
	return manager
}

// initializeFallbackStrategies 初始化降级策略
func (ifm *IntelligentFallbackManager) initializeFallbackStrategies() {
	ifm.strategies[FallbackLevelNone] = &FallbackStrategy{
		Level:       FallbackLevelNone,
		Description: "正常模式 - 使用增强AI服务",
		MaxRetries:  3,
		NextLevel:   FallbackLevelPartial,
	}
	
	ifm.strategies[FallbackLevelPartial] = &FallbackStrategy{
		Level:       FallbackLevelPartial,
		Description: "部分降级 - 跳过结果分析和渲染",
		MaxRetries:  2,
		NextLevel:   FallbackLevelTraditional,
	}
	
	ifm.strategies[FallbackLevelTraditional] = &FallbackStrategy{
		Level:       FallbackLevelTraditional,
		Description: "传统模式 - 使用传统AI服务",
		MaxRetries:  2,
		NextLevel:   FallbackLevelBasic,
	}
	
	ifm.strategies[FallbackLevelBasic] = &FallbackStrategy{
		Level:       FallbackLevelBasic,
		Description: "基础模式 - 使用基础聊天服务",
		MaxRetries:  1,
		NextLevel:   FallbackLevelEmergency,
	}
	
	ifm.strategies[FallbackLevelEmergency] = &FallbackStrategy{
		Level:       FallbackLevelEmergency,
		Description: "紧急模式 - 返回预定义响应",
		MaxRetries:  0,
		NextLevel:   FallbackLevelEmergency,
	}
}

// DetermineFallbackLevel 确定降级级别
func (ifm *IntelligentFallbackManager) DetermineFallbackLevel(err error, context map[string]interface{}) FallbackLevel {
	ifm.mutex.Lock()
	defer ifm.mutex.Unlock()
	
	ifm.totalRequests++
	
	if err == nil {
		ifm.successfulRequests++
		// 成功时考虑恢复到更高级别
		if ifm.currentLevel > FallbackLevelNone {
			ifm.considerLevelRecovery()
		}
		return ifm.currentLevel
	}
	
	// 分析错误类型
	reason := ifm.analyzeError(err)
	
	// 记录降级事件
	event := FallbackEvent{
		Timestamp: time.Now(),
		Level:     ifm.currentLevel,
		Reason:    reason,
		Error:     err.Error(),
		Duration:  0,
	}
	
	if userID, ok := context["user_id"].(int64); ok {
		event.UserID = userID
	}
	if sessionID, ok := context["session_id"].(string); ok {
		event.SessionID = sessionID
	}
	if message, ok := context["message"].(string); ok {
		event.Message = message
	}
	
	ifm.addErrorEvent(event)
	
	// 确定新的降级级别
	newLevel := ifm.calculateNewFallbackLevel(reason)
	
	if newLevel != ifm.currentLevel {
		ifm.logger.WithFields(logrus.Fields{
			"old_level": ifm.currentLevel,
			"new_level": newLevel,
			"reason":    reason,
			"error":     err.Error(),
		}).Warn("IntelligentFallbackManager: 降级级别变更")
		
		ifm.currentLevel = newLevel
	}
	
	ifm.fallbackRequests++
	
	return newLevel
}

// analyzeError 分析错误类型
func (ifm *IntelligentFallbackManager) analyzeError(err error) FallbackReason {
	errStr := strings.ToLower(err.Error())
	
	switch {
	case strings.Contains(errStr, "timeout") || strings.Contains(errStr, "deadline exceeded"):
		return ReasonTimeout
	case strings.Contains(errStr, "rate limit") || strings.Contains(errStr, "too many requests"):
		return ReasonRateLimited
	case strings.Contains(errStr, "service unavailable") || strings.Contains(errStr, "connection refused"):
		return ReasonServiceUnavailable
	case strings.Contains(errStr, "parse") || strings.Contains(errStr, "unmarshal") || strings.Contains(errStr, "json"):
		return ReasonParsingError
	case strings.Contains(errStr, "execution") || strings.Contains(errStr, "command"):
		return ReasonExecutionError
	case strings.Contains(errStr, "api") || strings.Contains(errStr, "http"):
		return ReasonAPIError
	default:
		return ReasonUnknownError
	}
}

// calculateNewFallbackLevel 计算新的降级级别
func (ifm *IntelligentFallbackManager) calculateNewFallbackLevel(reason FallbackReason) FallbackLevel {
	strategy, exists := ifm.strategies[ifm.currentLevel]
	if !exists {
		return FallbackLevelBasic
	}
	
	strategy.RetryCount++
	
	// 根据错误类型和重试次数决定是否降级
	switch reason {
	case ReasonTimeout:
		// 超时错误，快速降级
		if strategy.RetryCount >= 2 {
			return strategy.NextLevel
		}
	case ReasonServiceUnavailable:
		// 服务不可用，立即降级
		return strategy.NextLevel
	case ReasonRateLimited:
		// 限流错误，降级并等待
		return strategy.NextLevel
	case ReasonAPIError:
		// API错误，重试后降级
		if strategy.RetryCount >= strategy.MaxRetries {
			return strategy.NextLevel
		}
	case ReasonParsingError, ReasonExecutionError:
		// 解析或执行错误，可能是临时问题
		if strategy.RetryCount >= strategy.MaxRetries {
			return strategy.NextLevel
		}
	default:
		// 未知错误，保守降级
		if strategy.RetryCount >= strategy.MaxRetries {
			return strategy.NextLevel
		}
	}
	
	return ifm.currentLevel
}

// considerLevelRecovery 考虑级别恢复
func (ifm *IntelligentFallbackManager) considerLevelRecovery() {
	// 检查最近的成功率
	recentSuccessRate := ifm.calculateRecentSuccessRate()
	
	// 如果成功率足够高，考虑恢复到更高级别
	if recentSuccessRate > 0.8 && ifm.currentLevel > FallbackLevelNone {
		// 重置当前级别的重试计数
		if strategy, exists := ifm.strategies[ifm.currentLevel]; exists {
			strategy.RetryCount = 0
		}
		
		// 恢复到上一级别
		ifm.currentLevel = ifm.getPreviousLevel(ifm.currentLevel)
		
		ifm.logger.WithFields(logrus.Fields{
			"new_level":     ifm.currentLevel,
			"success_rate":  recentSuccessRate,
		}).Info("IntelligentFallbackManager: 恢复到更高级别")
	}
}

// calculateRecentSuccessRate 计算最近的成功率
func (ifm *IntelligentFallbackManager) calculateRecentSuccessRate() float64 {
	if ifm.totalRequests == 0 {
		return 1.0
	}
	
	// 计算最近100个请求的成功率
	recentRequests := int64(100)
	if ifm.totalRequests < recentRequests {
		recentRequests = ifm.totalRequests
	}
	
	recentSuccesses := ifm.successfulRequests
	if recentSuccesses > recentRequests {
		recentSuccesses = recentRequests
	}
	
	return float64(recentSuccesses) / float64(recentRequests)
}

// getPreviousLevel 获取上一级别
func (ifm *IntelligentFallbackManager) getPreviousLevel(current FallbackLevel) FallbackLevel {
	switch current {
	case FallbackLevelEmergency:
		return FallbackLevelBasic
	case FallbackLevelBasic:
		return FallbackLevelTraditional
	case FallbackLevelTraditional:
		return FallbackLevelPartial
	case FallbackLevelPartial:
		return FallbackLevelNone
	default:
		return FallbackLevelNone
	}
}

// addErrorEvent 添加错误事件
func (ifm *IntelligentFallbackManager) addErrorEvent(event FallbackEvent) {
	ifm.errorHistory = append(ifm.errorHistory, event)
	
	// 保持历史记录在限制范围内
	if len(ifm.errorHistory) > ifm.maxErrorHistory {
		ifm.errorHistory = ifm.errorHistory[1:]
	}
}

// startHealthCheck 启动健康检查
func (ifm *IntelligentFallbackManager) startHealthCheck() {
	ticker := time.NewTicker(ifm.healthCheckInterval)
	defer ticker.Stop()
	
	for range ticker.C {
		ifm.performHealthCheck()
	}
}

// performHealthCheck 执行健康检查
func (ifm *IntelligentFallbackManager) performHealthCheck() {
	ifm.mutex.Lock()
	defer ifm.mutex.Unlock()
	
	// 这里可以实现实际的健康检查逻辑
	// 例如：ping各个服务、检查响应时间等
	
	ifm.logger.WithFields(logrus.Fields{
		"current_level":           ifm.currentLevel,
		"enhanced_service_health": ifm.enhancedServiceHealth,
		"traditional_service_health": ifm.traditionalServiceHealth,
		"basic_service_health":    ifm.basicServiceHealth,
		"total_requests":          ifm.totalRequests,
		"successful_requests":     ifm.successfulRequests,
		"fallback_requests":       ifm.fallbackRequests,
	}).Debug("IntelligentFallbackManager: 健康检查完成")
}

// GetCurrentLevel 获取当前降级级别
func (ifm *IntelligentFallbackManager) GetCurrentLevel() FallbackLevel {
	ifm.mutex.RLock()
	defer ifm.mutex.RUnlock()
	return ifm.currentLevel
}

// GetStatistics 获取统计信息
func (ifm *IntelligentFallbackManager) GetStatistics() map[string]interface{} {
	ifm.mutex.RLock()
	defer ifm.mutex.RUnlock()
	
	successRate := float64(0)
	if ifm.totalRequests > 0 {
		successRate = float64(ifm.successfulRequests) / float64(ifm.totalRequests)
	}
	
	return map[string]interface{}{
		"current_level":       ifm.currentLevel,
		"total_requests":      ifm.totalRequests,
		"successful_requests": ifm.successfulRequests,
		"fallback_requests":   ifm.fallbackRequests,
		"success_rate":        successRate,
		"error_history_count": len(ifm.errorHistory),
		"enhanced_service_health": ifm.enhancedServiceHealth,
		"traditional_service_health": ifm.traditionalServiceHealth,
		"basic_service_health": ifm.basicServiceHealth,
	}
}
