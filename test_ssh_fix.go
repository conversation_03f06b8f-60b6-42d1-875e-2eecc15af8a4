package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Host 主机模型
type Host struct {
	ID                int64  `json:"id" gorm:"primaryKey;autoIncrement"`
	Name              string `json:"name" gorm:"uniqueIndex;size:100;not null"`
	IPAddress         string `json:"ip_address" gorm:"size:45;not null"`
	Port              int    `json:"port" gorm:"not null;default:22"`
	Username          string `json:"username" gorm:"size:50;not null"`
	PasswordEncrypted string `json:"-" gorm:"type:text"`
	Status            string `json:"status" gorm:"size:20;not null;default:unknown"`
}

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("data/aiops.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 当前的加密密钥
	encryptionKey := "aiops-dev-encryption-key-32byte!"
	
	fmt.Println("🔧 SSH连接问题修复方案")
	fmt.Println("============================================")
	
	// 方案1：重新设置**************的密码
	fmt.Println("方案1：重新加密**************的密码")
	
	// 使用正确的密码
	correctPassword := "1qaz#EDC"
	fmt.Printf("🔧 使用正确的密码重新加密: %s\n", correctPassword)
	
	for _, pwd := range possiblePasswords {
		fmt.Printf("尝试密码: %s\n", pwd)
		encryptedPwd, err := encryptData(pwd, encryptionKey)
		if err != nil {
			fmt.Printf("  ❌ 加密失败: %v\n", err)
			continue
		}
		
		// 更新数据库
		result := db.Model(&Host{}).Where("ip_address = ?", "**************").Update("password_encrypted", encryptedPwd)
		if result.Error != nil {
			fmt.Printf("  ❌ 更新失败: %v\n", result.Error)
			continue
		}
		
		fmt.Printf("  ✅ 密码已更新，请测试SSH连接\n")
		fmt.Printf("  加密后密码: %s\n", encryptedPwd)
		
		// 验证解密
		decrypted, err := decryptData(encryptedPwd, encryptionKey)
		if err != nil {
			fmt.Printf("  ❌ 解密验证失败: %v\n", err)
		} else {
			fmt.Printf("  ✅ 解密验证成功: %s\n", decrypted)
		}
		
		break // 只尝试第一个密码
	}
	
	fmt.Println("\n方案2：检查SSH连接配置")
	
	// 查询主机信息
	var host Host
	if err := db.Where("ip_address = ?", "**************").First(&host).Error; err != nil {
		fmt.Printf("❌ 查询主机失败: %v\n", err)
		return
	}
	
	fmt.Printf("主机信息:\n")
	fmt.Printf("  ID: %d\n", host.ID)
	fmt.Printf("  名称: %s\n", host.Name)
	fmt.Printf("  IP: %s\n", host.IPAddress)
	fmt.Printf("  端口: %d\n", host.Port)
	fmt.Printf("  用户名: %s\n", host.Username)
	fmt.Printf("  状态: %s\n", host.Status)
	
	fmt.Println("\n🔧 修复建议:")
	fmt.Println("1. 确认**************主机是否可达")
	fmt.Println("2. 确认SSH服务是否运行在端口22")
	fmt.Println("3. 确认用户名'root'是否正确")
	fmt.Println("4. 确认密码是否正确")
	fmt.Println("5. 检查SSH服务是否允许密码认证")
	
	fmt.Println("\n如果主机不存在或无法连接，建议:")
	fmt.Println("- 使用ping命令测试网络连通性")
	fmt.Println("- 使用telnet测试SSH端口连通性")
	fmt.Println("- 检查防火墙设置")
	fmt.Println("- 确认SSH服务配置")
}

// encryptData 加密数据
func encryptData(data, key string) (string, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, aead.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", err
	}

	ciphertext := aead.Seal(nonce, nonce, []byte(data), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decryptData 解密数据
func decryptData(encryptedData, key string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := aead.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := aead.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}
