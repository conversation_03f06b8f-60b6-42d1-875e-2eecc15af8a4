<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强终端管理 - AI运维管理平台</title>
    <link rel="stylesheet" href="/static/css/design-system.css">
    <script src="https://cdn.jsdelivr.net/npm/xterm@5.3.0/lib/xterm.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm-addon-fit@0.8.0/lib/xterm-addon-fit.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/xterm-addon-web-links@0.9.0/lib/xterm-addon-web-links.min.js"></script>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/xterm@5.3.0/css/xterm.css">
    <style>
        .terminal-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-primary);
            min-height: 100vh;
        }

        .terminal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px;
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }

        .terminal-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .terminal-actions {
            display: flex;
            gap: 12px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: var(--bg-hover);
        }

        .terminal-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 200px);
        }

        .terminal-sidebar {
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 20px;
            overflow-y: auto;
        }

        .terminal-main {
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .sidebar-section {
            margin-bottom: 30px;
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--border-color);
        }

        .session-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .session-item {
            padding: 12px;
            margin-bottom: 8px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .session-item:hover {
            background: var(--bg-hover);
            transform: translateX(4px);
        }

        .session-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .session-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 4px;
        }

        .session-name {
            font-weight: 500;
            font-size: 14px;
        }

        .session-status {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 10px;
            font-weight: 500;
        }

        .status-connected {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
        }

        .status-connecting {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
        }

        .status-disconnected {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
        }

        .session-details {
            font-size: 12px;
            opacity: 0.7;
        }

        .terminal-tabs {
            display: flex;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
            padding: 0 20px;
        }

        .terminal-tab {
            padding: 12px 20px;
            background: transparent;
            border: none;
            color: var(--text-secondary);
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .terminal-tab.active {
            color: var(--text-primary);
            border-bottom-color: #667eea;
        }

        .terminal-tab:hover {
            color: var(--text-primary);
            background: var(--bg-hover);
        }

        .terminal-content {
            flex: 1;
            position: relative;
            overflow: hidden;
        }

        .terminal-instance {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            padding: 20px;
            display: none;
        }

        .terminal-instance.active {
            display: block;
        }

        .xterm-viewport {
            background: #1a1a1a !important;
        }

        .terminal-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 20px;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
        }

        .terminal-info {
            display: flex;
            gap: 20px;
            font-size: 12px;
            color: var(--text-secondary);
        }

        .terminal-controls {
            display: flex;
            gap: 8px;
        }

        .control-btn {
            padding: 6px 12px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 4px;
            color: var(--text-primary);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .control-btn:hover {
            background: var(--bg-hover);
        }

        .history-panel {
            display: none;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 8px;
            margin: 20px;
            border: 1px solid var(--border-color);
        }

        .history-panel.active {
            display: block;
        }

        .history-item {
            padding: 12px;
            margin-bottom: 8px;
            background: var(--bg-secondary);
            border-radius: 6px;
            border: 1px solid var(--border-color);
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
        }

        .history-command {
            font-weight: 600;
            color: #667eea;
            margin-bottom: 4px;
        }

        .history-output {
            font-size: 12px;
            color: var(--text-secondary);
            white-space: pre-wrap;
            max-height: 100px;
            overflow-y: auto;
        }

        .history-meta {
            display: flex;
            justify-content: space-between;
            font-size: 10px;
            color: var(--text-secondary);
            margin-top: 8px;
            padding-top: 8px;
            border-top: 1px solid var(--border-color);
        }

        .connection-modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .connection-modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 30px;
            width: 500px;
            max-width: 90vw;
            border: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .form-select {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .notification.error {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }

        .notification.info {
            background: linear-gradient(135deg, #17a2b8, #007bff);
        }

        .loading-spinner {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .file-transfer {
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 8px;
            margin: 20px;
            border: 1px solid var(--border-color);
            display: none;
        }

        .file-transfer.active {
            display: block;
        }

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .upload-area:hover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .upload-area.dragover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }
    </style>
</head>
<body>
    <div class="terminal-container">
        <!-- 终端头部 -->
        <div class="terminal-header">
            <h1 class="terminal-title">💻 增强终端管理</h1>
            <div class="terminal-actions">
                <button class="btn-secondary" onclick="showHistory()">📜 历史记录</button>
                <button class="btn-secondary" onclick="showFileTransfer()">📁 文件传输</button>
                <button class="btn-primary" onclick="showConnectionModal()">🔗 新建连接</button>
            </div>
        </div>

        <!-- 终端主体 -->
        <div class="terminal-layout">
            <!-- 侧边栏 -->
            <div class="terminal-sidebar">
                <div class="sidebar-section">
                    <h3 class="sidebar-title">🖥️ 活跃会话</h3>
                    <ul class="session-list" id="sessionList">
                        <!-- 动态加载会话列表 -->
                    </ul>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-title">📊 统计信息</h3>
                    <div id="terminalStats">
                        <div style="margin-bottom: 10px;">
                            <strong>活跃会话:</strong> <span id="activeSessions">0</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>总会话数:</strong> <span id="totalSessions">0</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>执行命令:</strong> <span id="totalCommands">0</span>
                        </div>
                        <div>
                            <strong>数据传输:</strong> <span id="dataTransferred">0 KB</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="terminal-main">
                <!-- 工具栏 -->
                <div class="terminal-toolbar">
                    <div class="terminal-info">
                        <span id="currentHost">未连接</span>
                        <span id="currentDir">/</span>
                        <span id="connectionStatus">离线</span>
                    </div>
                    <div class="terminal-controls">
                        <button class="control-btn" onclick="clearTerminal()">🗑️ 清屏</button>
                        <button class="control-btn" onclick="resizeTerminal()">📐 调整</button>
                        <button class="control-btn" onclick="disconnectSession()">🔌 断开</button>
                    </div>
                </div>

                <!-- 标签页 -->
                <div class="terminal-tabs">
                    <button class="terminal-tab active" onclick="showTab('terminal')">🖥️ 终端</button>
                    <button class="terminal-tab" onclick="showTab('history')">📜 历史</button>
                    <button class="terminal-tab" onclick="showTab('files')">📁 文件</button>
                </div>

                <!-- 终端内容 -->
                <div class="terminal-content">
                    <!-- 终端实例 -->
                    <div class="terminal-instance active" id="terminalInstance">
                        <div id="terminal"></div>
                    </div>

                    <!-- 历史记录面板 -->
                    <div class="history-panel" id="historyPanel">
                        <div id="historyList">
                            <!-- 动态加载历史记录 -->
                        </div>
                    </div>

                    <!-- 文件传输面板 -->
                    <div class="file-transfer" id="fileTransfer">
                        <h3>文件传输</h3>
                        <div class="upload-area" id="uploadArea">
                            <p>📁 拖拽文件到此处或点击选择文件</p>
                            <input type="file" id="fileInput" style="display: none;" multiple>
                        </div>
                        <div id="transferProgress" style="margin-top: 20px; display: none;">
                            <div style="margin-bottom: 10px;">上传进度:</div>
                            <div style="background: var(--bg-secondary); border-radius: 4px; overflow: hidden;">
                                <div id="progressBar" style="height: 8px; background: #667eea; width: 0%; transition: width 0.3s ease;"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 连接模态框 -->
    <div class="connection-modal" id="connectionModal">
        <div class="modal-content">
            <h2 class="modal-title">创建新的终端连接</h2>
            <div class="form-group">
                <label class="form-label">选择主机</label>
                <select class="form-select" id="hostSelect">
                    <option value="">请选择主机...</option>
                </select>
            </div>
            <div class="form-group">
                <label class="form-label">终端大小</label>
                <div style="display: flex; gap: 10px;">
                    <input type="number" class="form-select" id="terminalCols" placeholder="列数" value="80" style="width: 50%;">
                    <input type="number" class="form-select" id="terminalRows" placeholder="行数" value="24" style="width: 50%;">
                </div>
            </div>
            <div class="modal-actions">
                <button class="btn-secondary" onclick="hideConnectionModal()">取消</button>
                <button class="btn-primary" onclick="createConnection()">连接</button>
            </div>
        </div>
    </div>

    <!-- 通知组件 -->
    <div class="notification" id="notification"></div>

    <script>
        // 全局变量
        let terminal = null;
        let websocket = null;
        let currentSessionId = null;
        let sessions = new Map();
        let fitAddon = null;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeTerminal();
            loadSessions();
            loadHosts();
            loadStats();
            setupFileTransfer();
            
            // 定期更新统计信息
            setInterval(loadStats, 30000);
        });

        // 初始化终端
        function initializeTerminal() {
            terminal = new Terminal({
                theme: {
                    background: '#1a1a1a',
                    foreground: '#ffffff',
                    cursor: '#ffffff',
                    selection: 'rgba(255, 255, 255, 0.3)',
                    black: '#000000',
                    red: '#e74c3c',
                    green: '#2ecc71',
                    yellow: '#f39c12',
                    blue: '#3498db',
                    magenta: '#9b59b6',
                    cyan: '#1abc9c',
                    white: '#ecf0f1',
                    brightBlack: '#34495e',
                    brightRed: '#c0392b',
                    brightGreen: '#27ae60',
                    brightYellow: '#e67e22',
                    brightBlue: '#2980b9',
                    brightMagenta: '#8e44ad',
                    brightCyan: '#16a085',
                    brightWhite: '#bdc3c7'
                },
                fontSize: 14,
                fontFamily: 'Consolas, Monaco, "Courier New", monospace',
                cursorBlink: true,
                cursorStyle: 'block',
                scrollback: 1000,
                tabStopWidth: 4
            });

            // 添加插件
            fitAddon = new FitAddon.FitAddon();
            terminal.loadAddon(fitAddon);
            terminal.loadAddon(new WebLinksAddon.WebLinksAddon());

            // 打开终端
            terminal.open(document.getElementById('terminal'));
            fitAddon.fit();

            // 监听窗口大小变化
            window.addEventListener('resize', () => {
                if (fitAddon) {
                    fitAddon.fit();
                }
            });

            // 监听用户输入
            terminal.onData(data => {
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    websocket.send(JSON.stringify({
                        type: 'input',
                        data: data
                    }));
                }
            });

            // 显示欢迎信息
            terminal.writeln('🚀 增强终端管理系统');
            terminal.writeln('请选择一个主机开始连接...');
            terminal.writeln('');
        }

        // 加载会话列表
        async function loadSessions() {
            try {
                const response = await fetch('/api/v1/enhanced-terminal/sessions');
                const result = await response.json();
                
                if (result.success) {
                    renderSessionList(result.data);
                }
            } catch (error) {
                console.error('Failed to load sessions:', error);
                showNotification('加载会话列表失败', 'error');
            }
        }

        // 渲染会话列表
        function renderSessionList(sessionData) {
            const sessionList = document.getElementById('sessionList');
            sessionList.innerHTML = '';

            sessionData.forEach(session => {
                const li = document.createElement('li');
                li.className = 'session-item';
                li.onclick = () => selectSession(session);
                
                li.innerHTML = `
                    <div class="session-info">
                        <span class="session-name">${session.host_name}</span>
                        <span class="session-status status-${session.status}">${getStatusText(session.status)}</span>
                    </div>
                    <div class="session-details">
                        ${session.ip_address} | ${session.working_dir}
                    </div>
                `;
                
                sessionList.appendChild(li);
                sessions.set(session.id, session);
            });
        }

        // 获取状态文本
        function getStatusText(status) {
            const statusMap = {
                'connected': '已连接',
                'connecting': '连接中',
                'disconnected': '已断开'
            };
            return statusMap[status] || status;
        }

        // 选择会话
        function selectSession(session) {
            // 移除之前的选中状态
            document.querySelectorAll('.session-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加选中状态
            event.currentTarget.classList.add('active');
            
            // 连接到会话
            connectToSession(session);
            
            // 更新界面信息
            updateTerminalInfo(session);
        }

        // 连接到会话
        function connectToSession(session) {
            if (websocket) {
                websocket.close();
            }

            currentSessionId = session.id;
            
            // 建立WebSocket连接
            const wsUrl = `ws://${window.location.host}/api/v1/enhanced-terminal/sessions/${session.id}/ws`;
            websocket = new WebSocket(wsUrl);

            websocket.onopen = () => {
                showNotification(`已连接到 ${session.host_name}`, 'success');
                updateConnectionStatus('已连接');
            };

            websocket.onmessage = (event) => {
                const message = JSON.parse(event.data);
                handleWebSocketMessage(message);
            };

            websocket.onclose = () => {
                updateConnectionStatus('已断开');
                showNotification('连接已断开', 'info');
            };

            websocket.onerror = (error) => {
                console.error('WebSocket error:', error);
                showNotification('连接错误', 'error');
            };
        }

        // 处理WebSocket消息
        function handleWebSocketMessage(message) {
            switch (message.type) {
                case 'terminal_output':
                    if (message.data && message.data.data) {
                        terminal.write(message.data.data);
                    }
                    break;
                case 'terminal_error':
                    terminal.write(`\r\n\x1b[31m错误: ${message.data.data}\x1b[0m\r\n`);
                    break;
                case 'pong':
                    // 心跳响应
                    break;
                default:
                    console.log('Unknown message type:', message.type);
            }
        }

        // 更新终端信息
        function updateTerminalInfo(session) {
            document.getElementById('currentHost').textContent = `${session.host_name} (${session.ip_address})`;
            document.getElementById('currentDir').textContent = session.working_dir;
        }

        // 更新连接状态
        function updateConnectionStatus(status) {
            document.getElementById('connectionStatus').textContent = status;
        }

        // 加载主机列表
        async function loadHosts() {
            try {
                const response = await fetch('/api/v1/hosts');
                const result = await response.json();
                
                if (result.success) {
                    const hostSelect = document.getElementById('hostSelect');
                    hostSelect.innerHTML = '<option value="">请选择主机...</option>';
                    
                    result.data.hosts.forEach(host => {
                        const option = document.createElement('option');
                        option.value = host.id;
                        option.textContent = `${host.name} (${host.ip_address})`;
                        hostSelect.appendChild(option);
                    });
                }
            } catch (error) {
                console.error('Failed to load hosts:', error);
                showNotification('加载主机列表失败', 'error');
            }
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/api/v1/enhanced-terminal/metrics');
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('activeSessions').textContent = stats.active_sessions || 0;
                    document.getElementById('totalSessions').textContent = stats.total_sessions || 0;
                    document.getElementById('totalCommands').textContent = stats.total_commands || 0;
                    document.getElementById('dataTransferred').textContent = formatBytes(stats.data_transferred || 0);
                }
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }

        // 格式化字节数
        function formatBytes(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 显示连接模态框
        function showConnectionModal() {
            document.getElementById('connectionModal').classList.add('show');
        }

        // 隐藏连接模态框
        function hideConnectionModal() {
            document.getElementById('connectionModal').classList.remove('show');
        }

        // 创建连接
        async function createConnection() {
            const hostId = document.getElementById('hostSelect').value;
            const cols = parseInt(document.getElementById('terminalCols').value) || 80;
            const rows = parseInt(document.getElementById('terminalRows').value) || 24;

            if (!hostId) {
                showNotification('请选择主机', 'error');
                return;
            }

            try {
                const response = await fetch('/api/v1/enhanced-terminal/sessions', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        host_id: parseInt(hostId),
                        cols: cols,
                        rows: rows
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    hideConnectionModal();
                    showNotification('连接创建成功', 'success');
                    loadSessions(); // 重新加载会话列表
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Failed to create connection:', error);
                showNotification('创建连接失败: ' + error.message, 'error');
            }
        }

        // 显示标签页
        function showTab(tabName) {
            // 移除所有标签的活跃状态
            document.querySelectorAll('.terminal-tab').forEach(tab => {
                tab.classList.remove('active');
            });
            
            // 添加当前标签的活跃状态
            event.currentTarget.classList.add('active');
            
            // 隐藏所有面板
            document.getElementById('terminalInstance').classList.remove('active');
            document.getElementById('historyPanel').classList.remove('active');
            document.getElementById('fileTransfer').classList.remove('active');
            
            // 显示对应面板
            switch (tabName) {
                case 'terminal':
                    document.getElementById('terminalInstance').classList.add('active');
                    if (fitAddon) {
                        setTimeout(() => fitAddon.fit(), 100);
                    }
                    break;
                case 'history':
                    document.getElementById('historyPanel').classList.add('active');
                    loadCommandHistory();
                    break;
                case 'files':
                    document.getElementById('fileTransfer').classList.add('active');
                    break;
            }
        }

        // 加载命令历史
        async function loadCommandHistory() {
            try {
                const response = await fetch('/api/v1/enhanced-terminal/history?limit=20');
                const result = await response.json();
                
                if (result.success) {
                    renderCommandHistory(result.data);
                }
            } catch (error) {
                console.error('Failed to load command history:', error);
                showNotification('加载命令历史失败', 'error');
            }
        }

        // 渲染命令历史
        function renderCommandHistory(historyData) {
            const historyList = document.getElementById('historyList');
            historyList.innerHTML = '';

            if (historyData.length === 0) {
                historyList.innerHTML = '<p style="text-align: center; color: var(--text-secondary); padding: 40px;">暂无命令历史</p>';
                return;
            }

            historyData.forEach(item => {
                const div = document.createElement('div');
                div.className = 'history-item';
                
                div.innerHTML = `
                    <div class="history-command">$ ${item.command}</div>
                    <div class="history-output">${item.output || '(无输出)'}</div>
                    <div class="history-meta">
                        <span>退出码: ${item.exit_code}</span>
                        <span>耗时: ${item.duration}ms</span>
                        <span>${new Date(item.start_time).toLocaleString()}</span>
                    </div>
                `;
                
                historyList.appendChild(div);
            });
        }

        // 设置文件传输
        function setupFileTransfer() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            uploadArea.onclick = () => fileInput.click();

            uploadArea.ondragover = (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            };

            uploadArea.ondragleave = () => {
                uploadArea.classList.remove('dragover');
            };

            uploadArea.ondrop = (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFileUpload(e.dataTransfer.files);
            };

            fileInput.onchange = (e) => {
                handleFileUpload(e.target.files);
            };
        }

        // 处理文件上传
        function handleFileUpload(files) {
            if (!currentSessionId) {
                showNotification('请先连接到终端会话', 'error');
                return;
            }

            Array.from(files).forEach(file => {
                uploadFile(file);
            });
        }

        // 上传文件
        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);

            const progressBar = document.getElementById('progressBar');
            const transferProgress = document.getElementById('transferProgress');
            
            transferProgress.style.display = 'block';
            progressBar.style.width = '0%';

            try {
                const response = await fetch(`/api/v1/enhanced-terminal/sessions/${currentSessionId}/upload`, {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    progressBar.style.width = '100%';
                    showNotification(`文件 ${file.name} 上传成功`, 'success');
                    setTimeout(() => {
                        transferProgress.style.display = 'none';
                    }, 2000);
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Failed to upload file:', error);
                showNotification('文件上传失败: ' + error.message, 'error');
                transferProgress.style.display = 'none';
            }
        }

        // 清屏
        function clearTerminal() {
            if (terminal) {
                terminal.clear();
            }
        }

        // 调整终端大小
        function resizeTerminal() {
            if (fitAddon) {
                fitAddon.fit();
                
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    websocket.send(JSON.stringify({
                        type: 'resize',
                        cols: terminal.cols,
                        rows: terminal.rows
                    }));
                }
                
                showNotification('终端大小已调整', 'info');
            }
        }

        // 断开会话
        function disconnectSession() {
            if (websocket) {
                websocket.close();
                websocket = null;
            }
            
            currentSessionId = null;
            updateConnectionStatus('未连接');
            updateTerminalInfo({ host_name: '未连接', ip_address: '', working_dir: '/' });
            
            // 移除会话选中状态
            document.querySelectorAll('.session-item').forEach(item => {
                item.classList.remove('active');
            });
            
            showNotification('已断开连接', 'info');
        }

        // 显示历史记录
        function showHistory() {
            showTab('history');
            document.querySelector('[onclick="showTab(\'history\')"]').classList.add('active');
        }

        // 显示文件传输
        function showFileTransfer() {
            showTab('files');
            document.querySelector('[onclick="showTab(\'files\')"]').classList.add('active');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 心跳检测
        setInterval(() => {
            if (websocket && websocket.readyState === WebSocket.OPEN) {
                websocket.send(JSON.stringify({
                    type: 'ping',
                    timestamp: Date.now()
                }));
            }
        }, 30000);
    </script>
</body>
</html>
