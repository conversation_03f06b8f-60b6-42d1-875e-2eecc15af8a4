# 快速开始指南

## 🚀 快速部署

### 前置要求
- Docker 20.10+
- Docker Compose 2.0+
- Git
- 至少2GB可用内存
- 至少5GB可用磁盘空间

### 一键部署
```bash
# 1. 克隆项目
git clone <repository-url>
cd aiops-platform

# 2. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，至少配置以下必要参数：
# - DEEPSEEK_API_KEY=your-api-key
# - JWT_SECRET=your-jwt-secret
# - ENCRYPTION_KEY=your-encryption-key

# 3. 启动服务
docker-compose up -d

# 4. 查看服务状态
docker-compose ps

# 5. 查看日志
docker-compose logs -f aiops-platform
```

### 访问系统
- **主应用**: http://localhost:8080
- **监控面板**: http://localhost:3000 (Grafana, admin/admin123)
- **指标接口**: http://localhost:9091 (Prometheus)
- **健康检查**: http://localhost:8080/health

## 🔧 开发环境搭建

### 本地开发
```bash
# 1. 安装Go 1.21+
go version

# 2. 克隆项目
git clone <repository-url>
cd aiops-platform

# 3. 安装依赖
go mod download

# 4. 配置环境变量
cp .env.example .env
# 编辑 .env 文件

# 5. 初始化数据库
go run cmd/migrate/main.go

# 6. 启动开发服务器
go run cmd/server/main.go
```

### 开发工具推荐
```bash
# 安装开发工具
go install github.com/cosmtrek/air@latest          # 热重载
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest  # 代码检查
go install github.com/swaggo/swag/cmd/swag@latest  # API文档生成

# 启动热重载开发服务器
air

# 代码质量检查
golangci-lint run

# 生成API文档
swag init -g cmd/server/main.go
```

## 📝 基础配置

### 必要环境变量
```bash
# DeepSeek API配置 (必填)
DEEPSEEK_API_KEY=sk-your-deepseek-api-key-here

# 安全配置 (必填)
JWT_SECRET=your-super-secret-jwt-key-change-in-production
ENCRYPTION_KEY=your-32-byte-encryption-key-here!!

# 数据库配置
DB_PATH=./data/aiops.db

# 应用配置
ENV=development
DEBUG=true
LOG_LEVEL=info
PORT=8080
```

### 获取DeepSeek API Key
1. 访问 [DeepSeek开放平台](https://platform.deepseek.com)
2. 注册账号并完成认证
3. 创建API密钥
4. 将密钥配置到环境变量中

### 生成安全密钥
```bash
# 生成JWT密钥
openssl rand -base64 32

# 生成加密密钥 (32字节)
openssl rand -base64 32 | head -c 32
```

## 👤 初始用户设置

### 创建管理员账号
```bash
# 方法1: 通过API创建
curl -X POST http://localhost:8080/api/v1/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123",
    "email": "<EMAIL>",
    "full_name": "系统管理员",
    "role": "super_admin"
  }'

# 方法2: 通过命令行工具创建
docker-compose exec aiops-platform /app/scripts/create-admin.sh
```

### 登录系统
```bash
# 获取访问令牌
curl -X POST http://localhost:8080/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "username": "admin",
    "password": "admin123"
  }'
```

## 🖥️ 添加第一台主机

### 通过API添加主机
```bash
# 使用获取的token
TOKEN="your-access-token"

curl -X POST http://localhost:8080/api/v1/hosts \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "web-server-01",
    "ip_address": "*************",
    "port": 22,
    "username": "root",
    "password": "your-ssh-password",
    "description": "Web服务器",
    "group_name": "web",
    "tags": ["production", "nginx"]
  }'
```

### 通过Web界面添加
1. 访问 http://localhost:8080
2. 使用管理员账号登录
3. 进入"主机管理"页面
4. 点击"添加主机"按钮
5. 填写主机信息并保存

## 🤖 开始AI对话

### 基础对话示例
```bash
# 发送对话消息
curl -X POST http://localhost:8080/api/v1/chat/message \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "content": "帮我查看所有主机的状态",
    "session_id": "new"
  }'
```

### 常用对话指令
- `查看主机列表`
- `在web-server-01上执行 ps aux`
- `显示最近的告警信息`
- `生成今天的运维统计报表`
- `检查所有主机的磁盘使用情况`

## 📊 监控和告警

### 查看系统指标
```bash
# 系统概览
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/stats/overview

# 主机统计
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/stats/hosts

# 告警统计
curl -H "Authorization: Bearer $TOKEN" \
  http://localhost:8080/api/v1/stats/alerts
```

### 配置告警规则
```bash
# 创建告警
curl -X POST http://localhost:8080/api/v1/alerts \
  -H "Authorization: Bearer $TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "CPU使用率过高",
    "message": "服务器CPU使用率超过90%",
    "level": "critical",
    "source": "monitoring",
    "host_id": 1
  }'
```

## 🔧 常见问题

### 服务启动失败
```bash
# 检查日志
docker-compose logs aiops-platform

# 检查配置
docker-compose config

# 重启服务
docker-compose restart aiops-platform
```

### 数据库连接问题
```bash
# 检查数据库文件权限
ls -la ./data/aiops.db

# 重新初始化数据库
docker-compose exec aiops-platform /app/migrate --action=reset
```

### DeepSeek API调用失败
```bash
# 测试API连接
curl -X POST https://api.deepseek.com/v1/chat/completions \
  -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
  -H "Content-Type: application/json" \
  -d '{
    "model": "deepseek-chat",
    "messages": [{"role": "user", "content": "hello"}],
    "max_tokens": 10
  }'
```

### 内存使用过高
```bash
# 检查内存使用
docker stats aiops-platform

# 调整内存限制
# 编辑 docker-compose.yml 中的 memory 配置
```

## 📚 下一步

### 深入了解
- [系统架构设计](./architecture/system-architecture.md)
- [API接口文档](./architecture/api-design.md)
- [安全配置指南](./security/security-architecture.md)
- [部署运维指南](./deployment/high-availability.md)

### 高级配置
- [性能优化](./implementation/performance-optimization.md)
- [监控告警配置](./operations/monitoring.md)
- [备份恢复策略](./deployment/backup-recovery.md)
- [高可用部署](./deployment/high-availability.md)

### 开发指南
- [开发环境搭建](./development/development-plan.md)
- [代码贡献指南](./development/coding-standards.md)
- [测试指南](./development/testing-strategy.md)

## 🆘 获取帮助

### 技术支持
- 查看文档：`docs/` 目录
- 提交Issue：GitHub Issues
- 社区讨论：GitHub Discussions

### 联系方式
- 邮箱：<EMAIL>
- 文档：https://docs.aiops.example.com
- 官网：https://aiops.example.com
