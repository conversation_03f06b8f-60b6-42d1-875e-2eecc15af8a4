apiVersion: v1
kind: Namespace
metadata:
  name: revolutionary-aiops
  labels:
    name: revolutionary-aiops
    version: "2.0.0"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: revolutionary-aiops-config
  namespace: revolutionary-aiops
data:
  config.yaml: |
    server:
      port: 8080
      host: "0.0.0.0"
      read_timeout: 30s
      write_timeout: 30s
      idle_timeout: 60s
    
    ai_brain:
      name: "Revolutionary-AI-Brain"
      version: "2.0.0"
      max_concurrent_ops: 1000
      response_timeout: 30s
      learning_enabled: true
      prediction_enabled: true
      
    intent_engine:
      name: "Revolutionary-Intent-Engine"
      version: "2.0.0"
      max_concurrent_requests: 1000
      processing_timeout: 10s
      confidence_threshold: 0.7
      multimodal_enabled: true
      context_window_size: 20
      intent_chain_max_depth: 10
      self_learning_enabled: true
      cache_enabled: true
    
    database:
      type: "postgresql"
      host: "postgresql-service"
      port: 5432
      name: "revolutionary_aiops"
      username: "aiops_user"
      password: "secure_password"
      max_connections: 100
      
    redis:
      host: "redis-service"
      port: 6379
      password: ""
      db: 0
      
    deepseek:
      api_url: "https://api.deepseek.com"
      api_key: "${DEEPSEEK_API_KEY}"
      model: "deepseek-chat"
      max_tokens: 4000
      temperature: 0.7
      
    logging:
      level: "info"
      format: "json"
      output: "stdout"
---
apiVersion: v1
kind: Secret
metadata:
  name: revolutionary-aiops-secrets
  namespace: revolutionary-aiops
type: Opaque
data:
  deepseek-api-key: ""  # Base64 encoded API key
  database-password: c2VjdXJlX3Bhc3N3b3Jk  # Base64 encoded "secure_password"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: revolutionary-aiops-server
  namespace: revolutionary-aiops
  labels:
    app: revolutionary-aiops-server
    version: "2.0.0"
spec:
  replicas: 3
  selector:
    matchLabels:
      app: revolutionary-aiops-server
  template:
    metadata:
      labels:
        app: revolutionary-aiops-server
        version: "2.0.0"
    spec:
      containers:
      - name: revolutionary-aiops
        image: revolutionary-aiops:2.0.0
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: DEEPSEEK_API_KEY
          valueFrom:
            secretKeyRef:
              name: revolutionary-aiops-secrets
              key: deepseek-api-key
        - name: DATABASE_PASSWORD
          valueFrom:
            secretKeyRef:
              name: revolutionary-aiops-secrets
              key: database-password
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
          readOnly: true
        resources:
          requests:
            memory: "2Gi"
            cpu: "1000m"
          limits:
            memory: "4Gi"
            cpu: "2000m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /health
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
      volumes:
      - name: config-volume
        configMap:
          name: revolutionary-aiops-config
---
apiVersion: v1
kind: Service
metadata:
  name: revolutionary-aiops-service
  namespace: revolutionary-aiops
  labels:
    app: revolutionary-aiops-server
spec:
  type: ClusterIP
  ports:
  - port: 80
    targetPort: 8080
    protocol: TCP
    name: http
  selector:
    app: revolutionary-aiops-server
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: revolutionary-aiops-ingress
  namespace: revolutionary-aiops
  annotations:
    kubernetes.io/ingress.class: "nginx"
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/websocket-services: "revolutionary-aiops-service"
    nginx.ingress.kubernetes.io/proxy-read-timeout: "3600"
    nginx.ingress.kubernetes.io/proxy-send-timeout: "3600"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
  - hosts:
    - aiops.example.com
    secretName: revolutionary-aiops-tls
  rules:
  - host: aiops.example.com
    http:
      paths:
      - path: /
        pathType: Prefix
        backend:
          service:
            name: revolutionary-aiops-service
            port:
              number: 80
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: postgresql
  namespace: revolutionary-aiops
  labels:
    app: postgresql
spec:
  replicas: 1
  selector:
    matchLabels:
      app: postgresql
  template:
    metadata:
      labels:
        app: postgresql
    spec:
      containers:
      - name: postgresql
        image: postgres:15
        ports:
        - containerPort: 5432
        env:
        - name: POSTGRES_DB
          value: "revolutionary_aiops"
        - name: POSTGRES_USER
          value: "aiops_user"
        - name: POSTGRES_PASSWORD
          valueFrom:
            secretKeyRef:
              name: revolutionary-aiops-secrets
              key: database-password
        volumeMounts:
        - name: postgresql-storage
          mountPath: /var/lib/postgresql/data
        resources:
          requests:
            memory: "1Gi"
            cpu: "500m"
          limits:
            memory: "2Gi"
            cpu: "1000m"
      volumes:
      - name: postgresql-storage
        persistentVolumeClaim:
          claimName: postgresql-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: postgresql-service
  namespace: revolutionary-aiops
  labels:
    app: postgresql
spec:
  type: ClusterIP
  ports:
  - port: 5432
    targetPort: 5432
    protocol: TCP
  selector:
    app: postgresql
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: postgresql-pvc
  namespace: revolutionary-aiops
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 20Gi
  storageClassName: fast-ssd
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: redis
  namespace: revolutionary-aiops
  labels:
    app: redis
spec:
  replicas: 1
  selector:
    matchLabels:
      app: redis
  template:
    metadata:
      labels:
        app: redis
    spec:
      containers:
      - name: redis
        image: redis:7-alpine
        ports:
        - containerPort: 6379
        command:
        - redis-server
        - --appendonly
        - "yes"
        - --maxmemory
        - "1gb"
        - --maxmemory-policy
        - "allkeys-lru"
        volumeMounts:
        - name: redis-storage
          mountPath: /data
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
      volumes:
      - name: redis-storage
        persistentVolumeClaim:
          claimName: redis-pvc
---
apiVersion: v1
kind: Service
metadata:
  name: redis-service
  namespace: revolutionary-aiops
  labels:
    app: redis
spec:
  type: ClusterIP
  ports:
  - port: 6379
    targetPort: 6379
    protocol: TCP
  selector:
    app: redis
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: redis-pvc
  namespace: revolutionary-aiops
spec:
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 10Gi
  storageClassName: fast-ssd
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: revolutionary-aiops-hpa
  namespace: revolutionary-aiops
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: revolutionary-aiops-server
  minReplicas: 3
  maxReplicas: 20
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: 70
  - type: Resource
    resource:
      name: memory
      target:
        type: Utilization
        averageUtilization: 80
  behavior:
    scaleUp:
      stabilizationWindowSeconds: 60
      policies:
      - type: Percent
        value: 100
        periodSeconds: 15
    scaleDown:
      stabilizationWindowSeconds: 300
      policies:
      - type: Percent
        value: 10
        periodSeconds: 60
---
apiVersion: v1
kind: ServiceMonitor
metadata:
  name: revolutionary-aiops-monitor
  namespace: revolutionary-aiops
  labels:
    app: revolutionary-aiops-server
spec:
  selector:
    matchLabels:
      app: revolutionary-aiops-server
  endpoints:
  - port: http
    path: /metrics
    interval: 30s
    scrapeTimeout: 10s
