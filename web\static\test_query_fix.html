<!DOCTYPE html>
<html>
<head>
    <title>测试主机查询功能修复</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; border-radius: 3px; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        #messages { height: 300px; overflow-y: auto; border: 1px solid #ccc; padding: 10px; }
    </style>
</head>
<body>
    <h1>测试主机查询功能修复</h1>
    
    <div class="test-section">
        <h2>1. 直接API测试</h2>
        <button onclick="testListHostsAPI()">测试 list_hosts API</button>
        <div id="api-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>2. WebSocket聊天测试</h2>
        <button onclick="connectWebSocket()">连接WebSocket</button>
        <button onclick="testQuery('查询现有主机账号')">测试: 查询现有主机账号</button>
        <button onclick="testQuery('查询主机账号信息')">测试: 查询主机账号信息</button>
        <button onclick="testQuery('显示主机账号信息')">测试: 显示主机账号信息</button>
        <button onclick="testQuery('现有主机')">测试: 现有主机</button>
        <div id="ws-status" class="result"></div>
        <div id="messages"></div>
    </div>

    <script>
        let ws = null;
        let sessionId = 'test-session-' + Date.now();

        async function testListHostsAPI() {
            const resultDiv = document.getElementById('api-result');
            try {
                const response = await fetch('/api/v1/hosts');
                const data = await response.json();
                
                if (response.ok && data.data && data.data.hosts) {
                    const hosts = data.data.hosts;
                    let result = `✅ 成功获取 ${hosts.length} 台主机信息\n\n`;
                    
                    if (hosts.length > 0) {
                        const host = hosts[0];
                        result += `示例主机信息:\n`;
                        result += `- ID: ${host.id}\n`;
                        result += `- 名称: ${host.name}\n`;
                        result += `- IP地址: ${host.ip_address}\n`;
                        result += `- 端口: ${host.port}\n`;
                        result += `- 用户名: ${host.username}\n`;
                        result += `- 环境: ${host.environment}\n`;
                        result += `- 状态: ${host.status}\n`;
                        
                        if (host.password) {
                            result += `❌ 错误：返回了密码信息\n`;
                        } else {
                            result += `✅ 正确：未返回密码信息\n`;
                        }
                    }
                    
                    resultDiv.className = 'result success';
                    resultDiv.innerHTML = '<pre>' + result + '</pre>';
                } else {
                    resultDiv.className = 'result error';
                    resultDiv.textContent = '❌ API调用失败: ' + JSON.stringify(data);
                }
            } catch (error) {
                resultDiv.className = 'result error';
                resultDiv.textContent = '❌ 请求失败: ' + error.message;
            }
        }

        function connectWebSocket() {
            const statusDiv = document.getElementById('ws-status');
            const messagesDiv = document.getElementById('messages');
            
            try {
                ws = new WebSocket('ws://localhost:8766/ws/chat');
                
                ws.onopen = function() {
                    statusDiv.className = 'result success';
                    statusDiv.textContent = '✅ WebSocket连接成功';
                };
                
                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    const messageDiv = document.createElement('div');
                    messageDiv.style.margin = '10px 0';
                    messageDiv.style.padding = '10px';
                    messageDiv.style.border = '1px solid #ddd';
                    messageDiv.style.borderRadius = '5px';
                    
                    let content = `<strong>AI响应:</strong><br>${data.content || data.message || JSON.stringify(data)}`;
                    
                    // 检查是否包含主机信息
                    const text = (data.content || data.message || '').toLowerCase();
                    if (text.includes('主机') || text.includes('ip') || text.includes('用户名') || text.includes('账号')) {
                        messageDiv.style.background = '#d4edda';
                        content += '<br><strong>✅ 包含主机相关信息</strong>';
                    } else if (text.includes('命令') || text.includes('linux') || text.includes('windows')) {
                        messageDiv.style.background = '#f8d7da';
                        content += '<br><strong>❌ 返回了通用教程而非平台数据</strong>';
                    }
                    
                    messageDiv.innerHTML = content;
                    messagesDiv.appendChild(messageDiv);
                    messagesDiv.scrollTop = messagesDiv.scrollHeight;
                };
                
                ws.onerror = function(error) {
                    statusDiv.className = 'result error';
                    statusDiv.textContent = '❌ WebSocket连接错误: ' + error;
                };
                
                ws.onclose = function() {
                    statusDiv.className = 'result';
                    statusDiv.textContent = 'WebSocket连接已关闭';
                };
                
            } catch (error) {
                statusDiv.className = 'result error';
                statusDiv.textContent = '❌ WebSocket连接失败: ' + error.message;
            }
        }

        function testQuery(query) {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                alert('请先连接WebSocket');
                return;
            }
            
            const message = {
                type: 'message',
                session_id: sessionId,
                message: query,
                user_id: 1
            };
            
            // 添加查询到消息显示
            const messagesDiv = document.getElementById('messages');
            const queryDiv = document.createElement('div');
            queryDiv.style.margin = '10px 0';
            queryDiv.style.padding = '10px';
            queryDiv.style.background = '#e3f2fd';
            queryDiv.style.border = '1px solid #2196f3';
            queryDiv.style.borderRadius = '5px';
            queryDiv.innerHTML = `<strong>用户查询:</strong> ${query}`;
            messagesDiv.appendChild(queryDiv);
            
            ws.send(JSON.stringify(message));
        }
    </script>
</body>
</html>
