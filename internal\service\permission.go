package service

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// PermissionManager 权限管理器
type PermissionManager struct {
	db          *gorm.DB
	logger      *logrus.Logger
	roles       map[string]*Role
	permissions map[string]*Permission
	policies    map[string]*AccessPolicy
	cache       *PermissionCache
}

// Role 角色
type Role struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"unique"`
	Description string    `json:"description"`
	Permissions []string  `json:"permissions" gorm:"type:text"` // JSON array
	Inherits    []string  `json:"inherits" gorm:"type:text"`    // JSON array of role IDs
	Priority    int       `json:"priority"`
	Enabled     bool      `json:"enabled"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// Permission 权限
type Permission struct {
	ID          string                 `json:"id" gorm:"primaryKey"`
	Name        string                 `json:"name" gorm:"unique"`
	Description string                 `json:"description"`
	Resource    string                 `json:"resource"`
	Action      string                 `json:"action"`
	Conditions  map[string]interface{} `json:"conditions" gorm:"type:text"` // JSON
	Category    string                 `json:"category"`
	RiskLevel   string                 `json:"risk_level"`
	Enabled     bool                   `json:"enabled"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// AccessPolicy 访问策略
type AccessPolicy struct {
	ID          string                 `json:"id" gorm:"primaryKey"`
	Name        string                 `json:"name" gorm:"unique"`
	Description string                 `json:"description"`
	Rules       string                 `json:"rules" gorm:"type:text"` // JSON string
	Effect      string                 `json:"effect"`                 // allow, deny
	Priority    int                    `json:"priority"`
	Conditions  map[string]interface{} `json:"conditions" gorm:"type:text"` // JSON
	Enabled     bool                   `json:"enabled"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// UserRole 用户角色关联
type UserRole struct {
	UserID    int64      `json:"user_id" gorm:"primaryKey"`
	RoleID    string     `json:"role_id" gorm:"primaryKey"`
	GrantedBy int64      `json:"granted_by"`
	GrantedAt time.Time  `json:"granted_at"`
	ExpiresAt *time.Time `json:"expires_at"`
	Enabled   bool       `json:"enabled"`
}

// ResourcePermission 资源权限
type ResourcePermission struct {
	ID           string     `json:"id" gorm:"primaryKey"`
	UserID       int64      `json:"user_id" gorm:"index"`
	ResourceType string     `json:"resource_type" gorm:"index"`
	ResourceID   string     `json:"resource_id" gorm:"index"`
	Actions      []string   `json:"actions" gorm:"type:text"`    // JSON array
	Conditions   string     `json:"conditions" gorm:"type:text"` // JSON
	GrantedBy    int64      `json:"granted_by"`
	GrantedAt    time.Time  `json:"granted_at"`
	ExpiresAt    *time.Time `json:"expires_at"`
	Enabled      bool       `json:"enabled"`
}

// PermissionCache 权限缓存
type PermissionCache struct {
	userPermissions map[int64]map[string]bool
	userRoles       map[int64][]string
	lastUpdate      time.Time
	ttl             time.Duration
}

// PermissionRequest 权限请求
type PermissionRequest struct {
	UserID     int64                  `json:"user_id"`
	Resource   string                 `json:"resource"`
	Action     string                 `json:"action"`
	ResourceID string                 `json:"resource_id"`
	Context    map[string]interface{} `json:"context"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	SessionID  string                 `json:"session_id"`
}

// PermissionResult 权限结果
type PermissionResult struct {
	Allowed    bool                   `json:"allowed"`
	Reason     string                 `json:"reason"`
	Policies   []string               `json:"policies"`
	Conditions map[string]interface{} `json:"conditions"`
	TTL        time.Duration          `json:"ttl"`
	CacheHit   bool                   `json:"cache_hit"`
}

// NewPermissionManager 创建权限管理器
func NewPermissionManager(db *gorm.DB, logger *logrus.Logger) *PermissionManager {
	pm := &PermissionManager{
		db:          db,
		logger:      logger,
		roles:       make(map[string]*Role),
		permissions: make(map[string]*Permission),
		policies:    make(map[string]*AccessPolicy),
		cache: &PermissionCache{
			userPermissions: make(map[int64]map[string]bool),
			userRoles:       make(map[int64][]string),
			ttl:             5 * time.Minute,
		},
	}

	// 初始化数据库表
	pm.initializeDatabase()

	// 加载默认权限和角色
	pm.loadDefaultPermissions()
	pm.loadDefaultRoles()
	pm.loadDefaultPolicies()

	// 启动缓存刷新例程
	go pm.startCacheRefreshRoutine(context.Background())

	return pm
}

// initializeDatabase 初始化数据库
func (pm *PermissionManager) initializeDatabase() error {
	return pm.db.AutoMigrate(&Role{}, &Permission{}, &AccessPolicy{}, &UserRole{}, &ResourcePermission{})
}

// loadDefaultPermissions 加载默认权限
func (pm *PermissionManager) loadDefaultPermissions() {
	defaultPermissions := []*Permission{
		{
			ID:          "host.read",
			Name:        "查看主机",
			Description: "查看主机信息和状态",
			Resource:    "host",
			Action:      "read",
			Category:    "basic",
			RiskLevel:   "safe",
			Enabled:     true,
		},
		{
			ID:          "host.execute",
			Name:        "执行命令",
			Description: "在主机上执行命令",
			Resource:    "host",
			Action:      "execute",
			Category:    "operations",
			RiskLevel:   "medium",
			Enabled:     true,
		},
		{
			ID:          "host.manage",
			Name:        "管理主机",
			Description: "添加、删除、修改主机配置",
			Resource:    "host",
			Action:      "manage",
			Category:    "administration",
			RiskLevel:   "high",
			Enabled:     true,
		},
		{
			ID:          "user.read",
			Name:        "查看用户",
			Description: "查看用户信息",
			Resource:    "user",
			Action:      "read",
			Category:    "basic",
			RiskLevel:   "safe",
			Enabled:     true,
		},
		{
			ID:          "user.manage",
			Name:        "管理用户",
			Description: "添加、删除、修改用户",
			Resource:    "user",
			Action:      "manage",
			Category:    "administration",
			RiskLevel:   "critical",
			Enabled:     true,
		},
		{
			ID:          "audit.read",
			Name:        "查看审计日志",
			Description: "查看系统审计日志",
			Resource:    "audit",
			Action:      "read",
			Category:    "security",
			RiskLevel:   "low",
			Enabled:     true,
		},
		{
			ID:          "approval.manage",
			Name:        "管理审批",
			Description: "审批用户请求",
			Resource:    "approval",
			Action:      "manage",
			Category:    "governance",
			RiskLevel:   "high",
			Enabled:     true,
		},
	}

	for _, perm := range defaultPermissions {
		pm.permissions[perm.ID] = perm
		// 保存到数据库
		pm.db.FirstOrCreate(perm, Permission{ID: perm.ID})
	}

	pm.logger.WithField("permission_count", len(pm.permissions)).Info("Default permissions loaded")
}

// loadDefaultRoles 加载默认角色
func (pm *PermissionManager) loadDefaultRoles() {
	defaultRoles := []*Role{
		{
			ID:          "viewer",
			Name:        "查看者",
			Description: "只能查看信息，无操作权限",
			Permissions: []string{"host.read", "user.read"},
			Priority:    10,
			Enabled:     true,
		},
		{
			ID:          "operator",
			Name:        "操作员",
			Description: "可以执行基本运维操作",
			Permissions: []string{"host.read", "host.execute", "audit.read"},
			Inherits:    []string{"viewer"},
			Priority:    20,
			Enabled:     true,
		},
		{
			ID:          "admin",
			Name:        "管理员",
			Description: "拥有所有权限",
			Permissions: []string{"host.read", "host.execute", "host.manage", "user.read", "user.manage", "audit.read", "approval.manage"},
			Inherits:    []string{"operator"},
			Priority:    100,
			Enabled:     true,
		},
		{
			ID:          "approver",
			Name:        "审批者",
			Description: "可以审批用户请求",
			Permissions: []string{"host.read", "approval.manage", "audit.read"},
			Inherits:    []string{"viewer"},
			Priority:    50,
			Enabled:     true,
		},
	}

	for _, role := range defaultRoles {
		pm.roles[role.ID] = role
		// 保存到数据库
		pm.db.FirstOrCreate(role, Role{ID: role.ID})
	}

	pm.logger.WithField("role_count", len(pm.roles)).Info("Default roles loaded")
}

// loadDefaultPolicies 加载默认策略
func (pm *PermissionManager) loadDefaultPolicies() {
	defaultPolicies := []*AccessPolicy{
		{
			ID:          "production_protection",
			Name:        "生产环境保护",
			Description: "限制对生产环境的访问",
			Effect:      "deny",
			Priority:    100,
			Conditions: map[string]interface{}{
				"environment": "production",
				"risk_level":  []string{"high", "critical"},
			},
			Enabled: true,
		},
		{
			ID:          "time_restriction",
			Name:        "时间限制",
			Description: "限制非工作时间的操作",
			Effect:      "deny",
			Priority:    80,
			Conditions: map[string]interface{}{
				"time_range": map[string]string{
					"start": "18:00",
					"end":   "09:00",
				},
				"weekdays_only": true,
			},
			Enabled: false, // 默认禁用
		},
	}

	for _, policy := range defaultPolicies {
		pm.policies[policy.ID] = policy
		// 保存到数据库
		pm.db.FirstOrCreate(policy, AccessPolicy{ID: policy.ID})
	}

	pm.logger.WithField("policy_count", len(pm.policies)).Info("Default policies loaded")
}

// CheckPermission 检查权限
func (pm *PermissionManager) CheckPermission(ctx context.Context, req *PermissionRequest) (*PermissionResult, error) {
	// 检查缓存
	if result := pm.checkCache(req); result != nil {
		result.CacheHit = true
		return result, nil
	}

	// 获取用户角色
	userRoles, err := pm.getUserRoles(req.UserID)
	if err != nil {
		return &PermissionResult{
			Allowed: false,
			Reason:  fmt.Sprintf("Failed to get user roles: %v", err),
		}, nil
	}

	// 检查基本权限
	hasPermission := pm.checkBasicPermission(req, userRoles)
	if !hasPermission {
		result := &PermissionResult{
			Allowed: false,
			Reason:  "Insufficient permissions",
		}
		pm.updateCache(req, result)
		return result, nil
	}

	// 检查访问策略
	policyResult := pm.checkAccessPolicies(req)
	if !policyResult.Allowed {
		pm.updateCache(req, policyResult)
		return policyResult, nil
	}

	// 检查资源特定权限
	resourceResult := pm.checkResourcePermission(req)
	if !resourceResult.Allowed {
		pm.updateCache(req, resourceResult)
		return resourceResult, nil
	}

	// 权限检查通过
	result := &PermissionResult{
		Allowed:    true,
		Reason:     "Permission granted",
		Policies:   policyResult.Policies,
		Conditions: policyResult.Conditions,
		TTL:        5 * time.Minute,
	}

	pm.updateCache(req, result)

	pm.logger.WithFields(logrus.Fields{
		"user_id":     req.UserID,
		"resource":    req.Resource,
		"action":      req.Action,
		"resource_id": req.ResourceID,
		"allowed":     result.Allowed,
	}).Info("Permission check completed")

	return result, nil
}

// getUserRoles 获取用户角色
func (pm *PermissionManager) getUserRoles(userID int64) ([]string, error) {
	// 检查缓存
	if roles, exists := pm.cache.userRoles[userID]; exists {
		return roles, nil
	}

	var userRoles []UserRole
	err := pm.db.Where("user_id = ? AND enabled = ?", userID, true).Find(&userRoles).Error
	if err != nil {
		return nil, err
	}

	roles := make([]string, 0, len(userRoles))
	for _, ur := range userRoles {
		// 检查是否过期
		if ur.ExpiresAt != nil && time.Now().After(*ur.ExpiresAt) {
			continue
		}
		roles = append(roles, ur.RoleID)
	}

	// 更新缓存
	pm.cache.userRoles[userID] = roles

	return roles, nil
}

// checkBasicPermission 检查基本权限
func (pm *PermissionManager) checkBasicPermission(req *PermissionRequest, userRoles []string) bool {
	requiredPermission := fmt.Sprintf("%s.%s", req.Resource, req.Action)

	for _, roleID := range userRoles {
		if role, exists := pm.roles[roleID]; exists && role.Enabled {
			// 检查直接权限
			for _, perm := range role.Permissions {
				if perm == requiredPermission {
					return true
				}
			}

			// 检查继承权限
			if pm.checkInheritedPermissions(role, requiredPermission) {
				return true
			}
		}
	}

	return false
}

// checkInheritedPermissions 检查继承权限
func (pm *PermissionManager) checkInheritedPermissions(role *Role, requiredPermission string) bool {
	for _, inheritedRoleID := range role.Inherits {
		if inheritedRole, exists := pm.roles[inheritedRoleID]; exists && inheritedRole.Enabled {
			// 检查继承角色的权限
			for _, perm := range inheritedRole.Permissions {
				if perm == requiredPermission {
					return true
				}
			}

			// 递归检查继承角色的继承权限
			if pm.checkInheritedPermissions(inheritedRole, requiredPermission) {
				return true
			}
		}
	}

	return false
}

// checkAccessPolicies 检查访问策略
func (pm *PermissionManager) checkAccessPolicies(req *PermissionRequest) *PermissionResult {
	matchedPolicies := make([]string, 0)
	conditions := make(map[string]interface{})

	for _, policy := range pm.policies {
		if !policy.Enabled {
			continue
		}

		if pm.evaluatePolicy(policy, req) {
			matchedPolicies = append(matchedPolicies, policy.ID)

			if policy.Effect == "deny" {
				return &PermissionResult{
					Allowed:  false,
					Reason:   fmt.Sprintf("Access denied by policy: %s", policy.Name),
					Policies: matchedPolicies,
				}
			}

			// 合并条件
			for k, v := range policy.Conditions {
				conditions[k] = v
			}
		}
	}

	return &PermissionResult{
		Allowed:    true,
		Policies:   matchedPolicies,
		Conditions: conditions,
	}
}

// evaluatePolicy 评估策略
func (pm *PermissionManager) evaluatePolicy(policy *AccessPolicy, req *PermissionRequest) bool {
	// 简单的策略评估实现
	// 在实际应用中应该实现更复杂的条件匹配逻辑

	// 检查环境条件
	if env, exists := policy.Conditions["environment"]; exists {
		if hostEnv, ok := req.Context["environment"]; ok {
			if env != hostEnv {
				return false
			}
		}
	}

	// 检查风险等级条件
	if riskLevels, exists := policy.Conditions["risk_level"]; exists {
		if levels, ok := riskLevels.([]string); ok {
			if reqRiskLevel, ok := req.Context["risk_level"].(string); ok {
				found := false
				for _, level := range levels {
					if level == reqRiskLevel {
						found = true
						break
					}
				}
				if found {
					return true
				}
			}
		}
	}

	return false
}

// checkResourcePermission 检查资源特定权限
func (pm *PermissionManager) checkResourcePermission(req *PermissionRequest) *PermissionResult {
	if req.ResourceID == "" {
		return &PermissionResult{Allowed: true}
	}

	var resourcePerms []ResourcePermission
	err := pm.db.Where("user_id = ? AND resource_type = ? AND resource_id = ? AND enabled = ?",
		req.UserID, req.Resource, req.ResourceID, true).Find(&resourcePerms).Error
	if err != nil {
		return &PermissionResult{
			Allowed: false,
			Reason:  fmt.Sprintf("Failed to check resource permission: %v", err),
		}
	}

	for _, perm := range resourcePerms {
		// 检查是否过期
		if perm.ExpiresAt != nil && time.Now().After(*perm.ExpiresAt) {
			continue
		}

		// 检查动作权限
		for _, action := range perm.Actions {
			if action == req.Action || action == "*" {
				return &PermissionResult{Allowed: true}
			}
		}
	}

	return &PermissionResult{
		Allowed: false,
		Reason:  "No specific resource permission",
	}
}

// checkCache 检查缓存
func (pm *PermissionManager) checkCache(req *PermissionRequest) *PermissionResult {
	if time.Since(pm.cache.lastUpdate) > pm.cache.ttl {
		return nil
	}

	cacheKey := fmt.Sprintf("%d:%s:%s:%s", req.UserID, req.Resource, req.Action, req.ResourceID)
	if userPerms, exists := pm.cache.userPermissions[req.UserID]; exists {
		if allowed, exists := userPerms[cacheKey]; exists {
			return &PermissionResult{
				Allowed:  allowed,
				Reason:   "Cached result",
				CacheHit: true,
			}
		}
	}

	return nil
}

// updateCache 更新缓存
func (pm *PermissionManager) updateCache(req *PermissionRequest, result *PermissionResult) {
	cacheKey := fmt.Sprintf("%d:%s:%s:%s", req.UserID, req.Resource, req.Action, req.ResourceID)

	if pm.cache.userPermissions[req.UserID] == nil {
		pm.cache.userPermissions[req.UserID] = make(map[string]bool)
	}

	pm.cache.userPermissions[req.UserID][cacheKey] = result.Allowed
	pm.cache.lastUpdate = time.Now()
}

// startCacheRefreshRoutine 启动缓存刷新例程
func (pm *PermissionManager) startCacheRefreshRoutine(ctx context.Context) {
	ticker := time.NewTicker(pm.cache.ttl)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			pm.logger.Info("Permission cache refresh routine stopped")
			return
		case <-ticker.C:
			pm.refreshCache()
		}
	}
}

// refreshCache 刷新缓存
func (pm *PermissionManager) refreshCache() {
	pm.cache.userPermissions = make(map[int64]map[string]bool)
	pm.cache.userRoles = make(map[int64][]string)
	pm.cache.lastUpdate = time.Now()

	pm.logger.Debug("Permission cache refreshed")
}

// AssignRole 分配角色
func (pm *PermissionManager) AssignRole(userID int64, roleID string, grantedBy int64, expiresAt *time.Time) error {
	userRole := &UserRole{
		UserID:    userID,
		RoleID:    roleID,
		GrantedBy: grantedBy,
		GrantedAt: time.Now(),
		ExpiresAt: expiresAt,
		Enabled:   true,
	}

	err := pm.db.Create(userRole).Error
	if err != nil {
		return fmt.Errorf("failed to assign role: %w", err)
	}

	// 清除缓存
	delete(pm.cache.userRoles, userID)
	delete(pm.cache.userPermissions, userID)

	pm.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"role_id":    roleID,
		"granted_by": grantedBy,
	}).Info("Role assigned to user")

	return nil
}

// RevokeRole 撤销角色
func (pm *PermissionManager) RevokeRole(userID int64, roleID string) error {
	err := pm.db.Where("user_id = ? AND role_id = ?", userID, roleID).Delete(&UserRole{}).Error
	if err != nil {
		return fmt.Errorf("failed to revoke role: %w", err)
	}

	// 清除缓存
	delete(pm.cache.userRoles, userID)
	delete(pm.cache.userPermissions, userID)

	pm.logger.WithFields(logrus.Fields{
		"user_id": userID,
		"role_id": roleID,
	}).Info("Role revoked from user")

	return nil
}

// GetUserPermissions 获取用户权限
func (pm *PermissionManager) GetUserPermissions(userID int64) ([]string, error) {
	userRoles, err := pm.getUserRoles(userID)
	if err != nil {
		return nil, err
	}

	permissionSet := make(map[string]bool)

	for _, roleID := range userRoles {
		if role, exists := pm.roles[roleID]; exists && role.Enabled {
			// 添加直接权限
			for _, perm := range role.Permissions {
				permissionSet[perm] = true
			}

			// 添加继承权限
			pm.addInheritedPermissions(role, permissionSet)
		}
	}

	permissions := make([]string, 0, len(permissionSet))
	for perm := range permissionSet {
		permissions = append(permissions, perm)
	}

	return permissions, nil
}

// addInheritedPermissions 添加继承权限
func (pm *PermissionManager) addInheritedPermissions(role *Role, permissionSet map[string]bool) {
	for _, inheritedRoleID := range role.Inherits {
		if inheritedRole, exists := pm.roles[inheritedRoleID]; exists && inheritedRole.Enabled {
			// 添加继承角色的权限
			for _, perm := range inheritedRole.Permissions {
				permissionSet[perm] = true
			}

			// 递归添加继承角色的继承权限
			pm.addInheritedPermissions(inheritedRole, permissionSet)
		}
	}
}

// GetPermissionStatistics 获取权限统计
func (pm *PermissionManager) GetPermissionStatistics() map[string]interface{} {
	stats := map[string]interface{}{
		"total_roles":       len(pm.roles),
		"total_permissions": len(pm.permissions),
		"total_policies":    len(pm.policies),
		"cache_size":        len(pm.cache.userPermissions),
		"cache_ttl":         pm.cache.ttl.String(),
	}

	// 统计启用的角色和权限
	enabledRoles := 0
	for _, role := range pm.roles {
		if role.Enabled {
			enabledRoles++
		}
	}
	stats["enabled_roles"] = enabledRoles

	enabledPermissions := 0
	for _, perm := range pm.permissions {
		if perm.Enabled {
			enabledPermissions++
		}
	}
	stats["enabled_permissions"] = enabledPermissions

	enabledPolicies := 0
	for _, policy := range pm.policies {
		if policy.Enabled {
			enabledPolicies++
		}
	}
	stats["enabled_policies"] = enabledPolicies

	return stats
}
