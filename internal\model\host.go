package model

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// Host 主机模型
type Host struct {
	ID                        int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	Name                      string         `json:"name" gorm:"uniqueIndex;size:100;not null"`
	IPAddress                 string         `json:"ip_address" gorm:"size:45;not null"`
	Port                      int            `json:"port" gorm:"not null;default:22"`
	Username                  string         `json:"username" gorm:"size:50;not null"`
	PasswordEncrypted         string         `json:"-" gorm:"type:text"`
	SSHKeyPath                string         `json:"ssh_key_path" gorm:"size:255"`
	SSHKeyPassphraseEncrypted string         `json:"-" gorm:"type:text"`
	Description               string         `json:"description" gorm:"type:text"`
	Status                    string         `json:"status" gorm:"size:20;not null;default:unknown"`
	OSType                    string         `json:"os_type" gorm:"size:20"`
	OSVersion                 string         `json:"os_version" gorm:"size:50"`
	CPUCores                  int            `json:"cpu_cores"`
	MemoryGB                  int            `json:"memory_gb"`
	DiskGB                    int            `json:"disk_gb"`
	Tags                      string         `json:"-" gorm:"type:text"` // JSON array
	Environment               string         `json:"environment" gorm:"size:20;default:production"`
	GroupName                 string         `json:"group_name" gorm:"size:50"`
	MonitoringEnabled         bool           `json:"monitoring_enabled" gorm:"default:true"`
	BackupEnabled             bool           `json:"backup_enabled" gorm:"default:false"`
	CreatedAt                 time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt                 time.Time      `json:"updated_at" gorm:"not null"`
	CreatedBy                 int64          `json:"created_by" gorm:"not null"`
	LastConnected             *time.Time     `json:"last_connected"`
	ConnectionCount           int            `json:"connection_count" gorm:"default:0"`
	DeletedAt                 gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Creator       User           `json:"creator" gorm:"foreignKey:CreatedBy"`
	Alerts        []Alert        `json:"-" gorm:"foreignKey:HostID"`
	OperationLogs []OperationLog `json:"-" gorm:"foreignKey:HostID"`
}

// TableName 指定表名
func (Host) TableName() string {
	return "hosts"
}

// BeforeCreate GORM钩子：创建前
func (h *Host) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	h.CreatedAt = now
	h.UpdatedAt = now
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (h *Host) BeforeUpdate(tx *gorm.DB) error {
	h.UpdatedAt = time.Now()
	return nil
}

// GetTags 获取标签列表
func (h *Host) GetTags() []string {
	if h.Tags == "" {
		return []string{}
	}

	var tags []string
	if err := json.Unmarshal([]byte(h.Tags), &tags); err != nil {
		return []string{}
	}
	return tags
}

// SetTags 设置标签列表
func (h *Host) SetTags(tags []string) error {
	if tags == nil {
		tags = []string{}
	}

	data, err := json.Marshal(tags)
	if err != nil {
		return err
	}
	h.Tags = string(data)
	return nil
}

// IsOnline 检查主机是否在线
func (h *Host) IsOnline() bool {
	return h.Status == "online"
}

// HostStatus 主机状态枚举
type HostStatus string

const (
	HostStatusOnline      HostStatus = "online"
	HostStatusOffline     HostStatus = "offline"
	HostStatusUnknown     HostStatus = "unknown"
	HostStatusError       HostStatus = "error"
	HostStatusMaintenance HostStatus = "maintenance"
)

// IsValidStatus 检查状态是否有效
func IsValidHostStatus(status string) bool {
	switch HostStatus(status) {
	case HostStatusOnline, HostStatusOffline, HostStatusUnknown, HostStatusError, HostStatusMaintenance:
		return true
	default:
		return false
	}
}

// HostEnvironment 主机环境枚举
type HostEnvironment string

const (
	EnvProduction  HostEnvironment = "production"
	EnvStaging     HostEnvironment = "staging"
	EnvDevelopment HostEnvironment = "development"
	EnvTesting     HostEnvironment = "testing"
)

// IsValidEnvironment 检查环境是否有效
func IsValidEnvironment(env string) bool {
	switch HostEnvironment(env) {
	case EnvProduction, EnvStaging, EnvDevelopment, EnvTesting:
		return true
	default:
		return false
	}
}

// HostCreateRequest 创建主机请求
type HostCreateRequest struct {
	Name              string   `json:"name" binding:"required,min=1,max=100"`
	IPAddress         string   `json:"ip_address" binding:"required,ip"`
	Port              int      `json:"port" binding:"omitempty,min=1,max=65535"`
	Username          string   `json:"username" binding:"required,min=1,max=50"`
	Password          string   `json:"password" binding:"omitempty"`
	SSHKeyPath        string   `json:"ssh_key_path" binding:"omitempty,max=255"`
	SSHKeyPassphrase  string   `json:"ssh_key_passphrase" binding:"omitempty"`
	Description       string   `json:"description" binding:"omitempty,max=500"`
	Environment       string   `json:"environment" binding:"omitempty,oneof=production staging development testing"`
	GroupName         string   `json:"group_name" binding:"omitempty,max=50"`
	Tags              []string `json:"tags" binding:"omitempty"`
	MonitoringEnabled bool     `json:"monitoring_enabled"`
	BackupEnabled     bool     `json:"backup_enabled"`
	CreatedBy         int64    `json:"-"`
}

// HostUpdateRequest 更新主机请求
type HostUpdateRequest struct {
	Name              string   `json:"name" binding:"omitempty,min=1,max=100"`
	IPAddress         string   `json:"ip_address" binding:"omitempty,ip"`
	Port              int      `json:"port" binding:"omitempty,min=1,max=65535"`
	Username          string   `json:"username" binding:"omitempty,min=1,max=50"`
	Password          string   `json:"password" binding:"omitempty"`
	SSHKeyPath        string   `json:"ssh_key_path" binding:"omitempty,max=255"`
	SSHKeyPassphrase  string   `json:"ssh_key_passphrase" binding:"omitempty"`
	Description       string   `json:"description" binding:"omitempty,max=500"`
	Environment       string   `json:"environment" binding:"omitempty,oneof=production staging development testing"`
	GroupName         string   `json:"group_name" binding:"omitempty,max=50"`
	Tags              []string `json:"tags" binding:"omitempty"`
	MonitoringEnabled *bool    `json:"monitoring_enabled"`
	BackupEnabled     *bool    `json:"backup_enabled"`
}

// HostResponse 主机响应
type HostResponse struct {
	ID                int64         `json:"id"`
	Name              string        `json:"name"`
	IPAddress         string        `json:"ip_address"`
	Port              int           `json:"port"`
	Username          string        `json:"username"`
	Description       string        `json:"description"`
	Status            string        `json:"status"`
	OSType            string        `json:"os_type"`
	OSVersion         string        `json:"os_version"`
	CPUCores          int           `json:"cpu_cores"`
	MemoryGB          int           `json:"memory_gb"`
	DiskGB            int           `json:"disk_gb"`
	Tags              []string      `json:"tags"`
	Environment       string        `json:"environment"`
	GroupName         string        `json:"group_name"`
	MonitoringEnabled bool          `json:"monitoring_enabled"`
	BackupEnabled     bool          `json:"backup_enabled"`
	CreatedAt         time.Time     `json:"created_at"`
	UpdatedAt         time.Time     `json:"updated_at"`
	CreatedBy         int64         `json:"created_by"`
	LastConnected     *time.Time    `json:"last_connected"`
	ConnectionCount   int           `json:"connection_count"`
	Creator           *UserResponse `json:"creator,omitempty"`
}

// ToResponse 转换为响应格式
func (h *Host) ToResponse() *HostResponse {
	resp := &HostResponse{
		ID:                h.ID,
		Name:              h.Name,
		IPAddress:         h.IPAddress,
		Port:              h.Port,
		Username:          h.Username,
		Description:       h.Description,
		Status:            h.Status,
		OSType:            h.OSType,
		OSVersion:         h.OSVersion,
		CPUCores:          h.CPUCores,
		MemoryGB:          h.MemoryGB,
		DiskGB:            h.DiskGB,
		Tags:              h.GetTags(),
		Environment:       h.Environment,
		GroupName:         h.GroupName,
		MonitoringEnabled: h.MonitoringEnabled,
		BackupEnabled:     h.BackupEnabled,
		CreatedAt:         h.CreatedAt,
		UpdatedAt:         h.UpdatedAt,
		CreatedBy:         h.CreatedBy,
		LastConnected:     h.LastConnected,
		ConnectionCount:   h.ConnectionCount,
	}

	if h.Creator.ID != 0 {
		resp.Creator = h.Creator.ToResponse()
	}

	return resp
}

// HostListQuery 主机列表查询参数
type HostListQuery struct {
	Page        int    `form:"page,default=1" binding:"min=1"`
	Limit       int    `form:"limit,default=20" binding:"min=1,max=100"`
	Status      string `form:"status" binding:"omitempty,oneof=online offline unknown error maintenance"`
	Environment string `form:"environment" binding:"omitempty,oneof=production staging development testing"`
	GroupName   string `form:"group" binding:"omitempty,max=50"`
	Search      string `form:"search" binding:"omitempty,max=100"`
	Tags        string `form:"tags" binding:"omitempty"`
}

// HostListResponse 主机列表响应
type HostListResponse struct {
	Hosts      []*HostResponse `json:"hosts"`
	Pagination *Pagination     `json:"pagination"`
}

// CommandExecuteRequest 命令执行请求
type CommandExecuteRequest struct {
	Command          string `json:"command" binding:"required,min=1"`
	Timeout          int    `json:"timeout" binding:"omitempty,min=1,max=300"`
	WorkingDirectory string `json:"working_directory" binding:"omitempty,max=255"`
}

// CommandExecuteResponse 命令执行响应
type CommandExecuteResponse struct {
	Command      string    `json:"command"`
	ExitCode     int       `json:"exit_code"`
	Stdout       string    `json:"stdout"`
	Stderr       string    `json:"stderr"`
	Duration     int       `json:"duration"` // milliseconds
	ExecutedAt   time.Time `json:"executed_at"`
	ErrorMessage string    `json:"error_message,omitempty"`
}

// HostTestRequest 主机连接测试请求
type HostTestRequest struct {
	Timeout int `json:"timeout" binding:"omitempty,min=1,max=60"`
}

// HostTestResponse 主机连接测试响应
type HostTestResponse struct {
	Success    bool                   `json:"success"`
	Message    string                 `json:"message"`
	Duration   int                    `json:"duration"` // milliseconds
	TestedAt   time.Time              `json:"tested_at"`
	OSInfo     string                 `json:"os_info,omitempty"`
	SystemInfo map[string]interface{} `json:"system_info,omitempty"`
}
