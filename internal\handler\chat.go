package handler

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"aiops-platform/internal/middleware"
	"aiops-platform/internal/model"
	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
)

// WebSocket升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 生产环境应该检查Origin
	},
}

// CreateSession 创建对话会话
func (h *ChatHandler) CreateSession(c *gin.Context) {
	var req model.ChatSessionCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// TODO: 暂时使用默认用户ID，后续需要实现认证
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		// 暂时使用默认用户ID 1
		userID = 1
	}

	session, err := h.services.Chat.CreateSession(userID, &req)
	if err != nil {
		h.logger.WithFields(logrus.Fields{
			"user_id": userID,
			"error":   err.Error(),
		}).Error("Failed to create chat session")

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to create session",
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"user_id":    userID,
	}).Info("Chat session created")

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "Session created successfully",
		"data":    session,
	})
}

// GetSession 获取会话详情
func (h *ChatHandler) GetSession(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid session ID",
		})
		return
	}

	session, err := h.services.Chat.GetSessionByID(id)
	if err != nil {
		if err.Error() == "session not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Session not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to get session",
			})
		}
		return
	}

	// TODO: 暂时跳过权限检查，后续需要实现认证
	// userID, _ := middleware.GetUserIDFromContext(c)
	// if session.UserID != userID {
	// 	user, _ := middleware.GetUserFromContext(c)
	// 	if user == nil || !user.IsAdmin() {
	// 		c.JSON(http.StatusForbidden, gin.H{
	// 			"code":    403,
	// 			"message": "Access denied",
	// 		})
	// 		return
	// 	}
	// }

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Session retrieved successfully",
		"data":    session,
	})
}

// ListSessions 获取会话列表
func (h *ChatHandler) ListSessions(c *gin.Context) {
	// TODO: 暂时使用默认用户ID，后续需要实现认证
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		// 暂时使用默认用户ID 1
		userID = 1
	}

	var req model.ChatSessionListQuery

	// 解析查询参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}
	if req.Page == 0 {
		req.Page = 1
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 100 {
			req.Limit = limit
		}
	}
	if req.Limit == 0 {
		req.Limit = 20
	}

	req.Status = c.Query("status")
	req.Search = c.Query("search")

	sessions, err := h.services.Chat.ListSessions(userID, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list sessions")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get sessions",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Sessions retrieved successfully",
		"data":    sessions,
	})
}

// SendMessage 发送消息
func (h *ChatHandler) SendMessage(c *gin.Context) {
	var req model.ChatMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 获取用户ID
	userID, _ := middleware.GetUserIDFromContext(c)
	if userID == 0 {
		userID = 1 // 默认用户ID
	}

	// TODO: 暂时跳过权限检查，后续需要实现认证
	// if session.UserID != userID {
	// 	user, _ := middleware.GetUserFromContext(c)
	// 	if user == nil || !user.IsAdmin() {
	// 		c.JSON(http.StatusForbidden, gin.H{
	// 			"code":    403,
	// 			"message": "Access denied",
	// 		})
	// 		return
	// 	}
	// }

	message, err := h.services.Chat.SendMessage(&req)
	if err != nil {
		h.logger.WithFields(logrus.Fields{
			"session_id": req.SessionID,
			"user_id":    userID,
			"error":      err.Error(),
		}).Error("Failed to send message")

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to send message",
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"message_id": message.ID,
		"user_id":    userID,
	}).Info("Message sent successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Message sent successfully",
		"data":    message,
	})
}

// GetMessages 获取会话消息
func (h *ChatHandler) GetMessages(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid session ID",
		})
		return
	}

	// 验证会话权限
	session, err := h.services.Chat.GetSessionByID(id)
	if err != nil {
		if err.Error() == "session not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Session not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to get session",
			})
		}
		return
	}

	// TODO: 暂时跳过权限检查，后续需要实现认证
	// userID, _ := middleware.GetUserIDFromContext(c)
	// if session.UserID != userID {
	// 	user, _ := middleware.GetUserFromContext(c)
	// 	if user == nil || !user.IsAdmin() {
	// 		c.JSON(http.StatusForbidden, gin.H{
	// 			"code":    403,
	// 			"message": "Access denied",
	// 		})
	// 		return
	// 	}
	// }

	var req service.GetMessagesRequest

	// 解析查询参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}
	if req.Page == 0 {
		req.Page = 1
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 100 {
			req.Limit = limit
		}
	}
	if req.Limit == 0 {
		req.Limit = 50
	}

	// TODO: 实现获取消息列表的逻辑
	// messages, err := h.services.Chat.GetMessages(session.SessionID, &req)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Messages retrieved successfully",
		"data": gin.H{
			"messages": session.Messages,
			"pagination": gin.H{
				"page":     req.Page,
				"limit":    req.Limit,
				"total":    len(session.Messages),
				"pages":    1,
				"has_next": false,
				"has_prev": false,
			},
		},
	})
}

// DeleteSession 删除会话
func (h *ChatHandler) DeleteSession(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid session ID",
		})
		return
	}

	// 验证会话权限
	session, err := h.services.Chat.GetSessionByID(id)
	if err != nil {
		if err.Error() == "session not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Session not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to get session",
			})
		}
		return
	}

	// TODO: 暂时跳过权限检查，后续需要实现认证
	// userID, _ := middleware.GetUserIDFromContext(c)
	// if session.UserID != userID {
	// 	user, _ := middleware.GetUserFromContext(c)
	// 	if user == nil || !user.IsAdmin() {
	// 		c.JSON(http.StatusForbidden, gin.H{
	// 			"code":    403,
	// 			"message": "Access denied",
	// 		})
	// 		return
	// 	}
	// }

	// TODO: 实现删除会话的逻辑
	// if err := h.services.Chat.DeleteSession(session.SessionID); err != nil {

	h.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"user_id":    session.UserID,
	}).Info("Session deleted successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Session deleted successfully",
	})
}

// HandleWebSocket 处理WebSocket连接 - 使用统一的WebSocket管理器
func (h *ChatHandler) HandleWebSocket(c *gin.Context) {
	// TODO: 暂时使用默认用户ID，后续需要实现认证
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		// 暂时使用默认用户ID 1
		userID = 1
	}

	sessionID := c.Query("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "session_id is required",
		})
		return
	}

	// 确保会话存在，如果不存在则自动创建
	if err := h.ensureSessionExists(userID, sessionID); err != nil {
		h.logger.WithError(err).Error("Failed to ensure session exists")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to initialize session",
		})
		return
	}

	// 使用统一的WebSocket管理器处理连接
	if h.services.WebSocket != nil {
		err := h.services.WebSocket.HandleWebSocket(c.Writer, c.Request, userID, sessionID)
		if err != nil {
			h.logger.WithError(err).Error("Failed to handle WebSocket connection")
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Failed to establish WebSocket connection",
			})
			return
		}
	} else {
		// 回退到原有的WebSocket处理方式
		h.handleWebSocketLegacy(c, userID, sessionID)
	}
}

// ensureSessionExists 确保会话存在，如果不存在则创建
func (h *ChatHandler) ensureSessionExists(userID int64, sessionID string) error {
	// 检查会话是否存在
	_, err := h.services.Chat.GetSessionBySessionID(sessionID)
	if err != nil {
		if err.Error() == "session not found" {
			// 会话不存在，创建新会话
			req := &model.ChatSessionCreateRequest{
				Title: "WebSocket会话 - " + time.Now().Format("2006-01-02 15:04"),
			}

			// 创建会话，但使用指定的sessionID
			session := &model.ChatSession{
				SessionID:    sessionID,
				UserID:       userID,
				Title:        req.Title,
				Status:       string(model.SessionStatusActive),
				MessageCount: 0,
				TokenUsage:   0,
			}

			if err := h.services.Chat.CreateSessionWithID(session); err != nil {
				return fmt.Errorf("failed to create session: %w", err)
			}

			h.logger.WithFields(logrus.Fields{
				"session_id": sessionID,
				"user_id":    userID,
			}).Info("Auto-created WebSocket session")
		} else {
			return fmt.Errorf("failed to check session: %w", err)
		}
	}
	return nil
}

// handleWebSocketLegacy 处理WebSocket连接的回退方式
func (h *ChatHandler) handleWebSocketLegacy(c *gin.Context, userID int64, sessionID string) {
	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.logger.WithError(err).Error("Failed to upgrade WebSocket connection")
		return
	}
	defer conn.Close()

	h.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"session_id": sessionID,
	}).Info("WebSocket connection established (legacy mode)")

	// 发送连接成功消息
	conn.WriteJSON(gin.H{
		"type":    "connected",
		"message": "WebSocket connection established",
		"data": gin.H{
			"session_id": sessionID,
			"user_id":    userID,
		},
	})

	// 处理消息循环
	for {
		var msg struct {
			Type    string `json:"type"`
			Content string `json:"content"`
		}

		if err := conn.ReadJSON(&msg); err != nil {
			h.logger.WithError(err).Debug("WebSocket connection closed")
			break
		}

		switch msg.Type {
		case "message":
			h.handleWebSocketMessage(conn, userID, sessionID, msg.Content)
		case "ping":
			conn.WriteJSON(gin.H{
				"type": "pong",
			})
		default:
			conn.WriteJSON(gin.H{
				"type":    "error",
				"message": "Unknown message type",
			})
		}
	}
}

// handleWebSocketMessage 处理WebSocket消息
func (h *ChatHandler) handleWebSocketMessage(conn *websocket.Conn, userID int64, sessionID, content string) {
	// 发送消息请求
	req := &model.ChatMessageRequest{
		SessionID: sessionID,
		Content:   content,
		Stream:    true,
	}

	// 发送用户消息确认
	conn.WriteJSON(gin.H{
		"type": "user_message",
		"data": gin.H{
			"content":    content,
			"created_at": time.Now(),
		},
	})

	// 处理AI响应
	message, err := h.services.Chat.SendMessage(req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process WebSocket message")
		conn.WriteJSON(gin.H{
			"type":    "error",
			"message": "Failed to process message",
		})
		return
	}

	// 发送AI响应
	conn.WriteJSON(gin.H{
		"type": "assistant_message",
		"data": message,
	})
}
