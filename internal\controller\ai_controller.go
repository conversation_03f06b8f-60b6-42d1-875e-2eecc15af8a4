package controller

import (
	"net/http"
	"strconv"

	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// AIController AI控制器
type AIController struct {
	aiService service.AIService
	logger    *logrus.Logger
}

// NewAIController 创建AI控制器
func NewAIController(aiService service.AIService, logger *logrus.Logger) *AIController {
	return &AIController{
		aiService: aiService,
		logger:    logger,
	}
}

// ProcessMessage 处理消息
// @Summary 处理AI对话消息
// @Description 处理用户发送的消息，支持工具调用
// @Tags AI
// @Accept json
// @Produce json
// @Param request body service.ProcessMessageRequest true "消息请求"
// @Success 200 {object} service.ProcessMessageResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/ai/message [post]
func (c *AIController) ProcessMessage(ctx *gin.Context) {
	var req service.ProcessMessageRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.WithError(err).Error("Failed to bind request")
		ctx.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
		})
		return
	}

	// 从JWT中获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "Unauthorized",
			Message: "User ID not found in token",
		})
		return
	}

	req.UserID = userID.(int64)

	c.logger.WithFields(logrus.Fields{
		"user_id":    req.UserID,
		"session_id": req.SessionID,
		"message":    req.Message,
	}).Info("Processing AI message")

	response, err := c.aiService.ProcessMessageWithTools(ctx, &req)
	if err != nil {
		c.logger.WithError(err).Error("Failed to process message")
		ctx.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to process message",
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// ProcessBasicMessage 处理基础消息（不使用工具）
// @Summary 处理基础AI对话消息
// @Description 处理用户发送的消息，不使用工具调用
// @Tags AI
// @Accept json
// @Produce json
// @Param request body service.ProcessMessageRequest true "消息请求"
// @Success 200 {object} service.ProcessMessageResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/ai/message/basic [post]
func (c *AIController) ProcessBasicMessage(ctx *gin.Context) {
	var req service.ProcessMessageRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.WithError(err).Error("Failed to bind request")
		ctx.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
		})
		return
	}

	// 从JWT中获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "Unauthorized",
			Message: "User ID not found in token",
		})
		return
	}

	req.UserID = userID.(int64)

	response, err := c.aiService.ProcessMessage(ctx, &req)
	if err != nil {
		c.logger.WithError(err).Error("Failed to process basic message")
		ctx.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to process message",
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// GetContext 获取对话上下文
// @Summary 获取对话上下文
// @Description 获取指定会话的对话上下文
// @Tags AI
// @Produce json
// @Param session_id path string true "会话ID"
// @Success 200 {object} service.ConversationContext
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/ai/context/{session_id} [get]
func (c *AIController) GetContext(ctx *gin.Context) {
	sessionID := ctx.Param("session_id")
	if sessionID == "" {
		ctx.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid session ID",
			Message: "Session ID is required",
		})
		return
	}

	context, err := c.aiService.GetContext(sessionID)
	if err != nil {
		c.logger.WithError(err).Error("Failed to get context")
		ctx.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get context",
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, context)
}

// ClearContext 清除对话上下文
// @Summary 清除对话上下文
// @Description 清除指定会话的对话上下文
// @Tags AI
// @Param session_id path string true "会话ID"
// @Success 200 {object} SuccessResponse
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/ai/context/{session_id} [delete]
func (c *AIController) ClearContext(ctx *gin.Context) {
	sessionID := ctx.Param("session_id")
	if sessionID == "" {
		ctx.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid session ID",
			Message: "Session ID is required",
		})
		return
	}

	err := c.aiService.ClearContext(sessionID)
	if err != nil {
		c.logger.WithError(err).Error("Failed to clear context")
		ctx.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to clear context",
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, SuccessResponse{
		Message: "Context cleared successfully",
	})
}

// GetAvailableTools 获取可用工具
// @Summary 获取可用工具列表
// @Description 获取当前用户可用的AI工具列表
// @Tags AI
// @Produce json
// @Success 200 {array} service.ToolDefinition
// @Failure 500 {object} ErrorResponse
// @Router /api/ai/tools [get]
func (c *AIController) GetAvailableTools(ctx *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, ErrorResponse{
			Error:   "Unauthorized",
			Message: "User ID not found in token",
		})
		return
	}

	tools, err := c.aiService.GetAvailableTools(userID.(int64))
	if err != nil {
		c.logger.WithError(err).Error("Failed to get available tools")
		ctx.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to get tools",
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, tools)
}

// SummarizeConversation 总结对话
// @Summary 总结对话内容
// @Description 对指定会话的对话内容进行AI总结
// @Tags AI
// @Produce json
// @Param session_id path string true "会话ID"
// @Success 200 {object} service.ConversationSummary
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/ai/conversation/{session_id}/summary [get]
func (c *AIController) SummarizeConversation(ctx *gin.Context) {
	sessionID := ctx.Param("session_id")
	if sessionID == "" {
		ctx.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid session ID",
			Message: "Session ID is required",
		})
		return
	}

	summary, err := c.aiService.SummarizeConversation(ctx, sessionID)
	if err != nil {
		c.logger.WithError(err).Error("Failed to summarize conversation")
		ctx.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to summarize conversation",
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, summary)
}

// ValidateCommand 验证命令
// @Summary 验证命令安全性
// @Description 使用AI分析命令的安全性和风险等级
// @Tags AI
// @Accept json
// @Produce json
// @Param request body ValidateCommandRequest true "命令验证请求"
// @Success 200 {object} service.CommandValidation
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/ai/validate-command [post]
func (c *AIController) ValidateCommand(ctx *gin.Context) {
	var req ValidateCommandRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.WithError(err).Error("Failed to bind request")
		ctx.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
		})
		return
	}

	// 获取用户上下文（如果有会话ID）
	var context *service.ConversationContext
	if req.SessionID != "" {
		var err error
		context, err = c.aiService.GetContext(req.SessionID)
		if err != nil {
			c.logger.WithError(err).Warn("Failed to get context for command validation")
		}
	}

	validation, err := c.aiService.ValidateCommand(ctx, req.Command, context)
	if err != nil {
		c.logger.WithError(err).Error("Failed to validate command")
		ctx.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to validate command",
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, validation)
}

// GenerateResponse 生成响应
// @Summary 生成AI响应
// @Description 根据给定的提示和上下文生成AI响应
// @Tags AI
// @Accept json
// @Produce json
// @Param request body service.GenerateResponseRequest true "生成响应请求"
// @Success 200 {object} service.GenerateResponseResult
// @Failure 400 {object} ErrorResponse
// @Failure 500 {object} ErrorResponse
// @Router /api/ai/generate [post]
func (c *AIController) GenerateResponse(ctx *gin.Context) {
	var req service.GenerateResponseRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		c.logger.WithError(err).Error("Failed to bind request")
		ctx.JSON(http.StatusBadRequest, ErrorResponse{
			Error:   "Invalid request format",
			Message: err.Error(),
		})
		return
	}

	response, err := c.aiService.GenerateResponse(ctx, &req)
	if err != nil {
		c.logger.WithError(err).Error("Failed to generate response")
		ctx.JSON(http.StatusInternalServerError, ErrorResponse{
			Error:   "Failed to generate response",
			Message: err.Error(),
		})
		return
	}

	ctx.JSON(http.StatusOK, response)
}

// ValidateCommandRequest 命令验证请求
type ValidateCommandRequest struct {
	Command   string `json:"command" binding:"required"`
	SessionID string `json:"session_id,omitempty"`
}

// SuccessResponse 成功响应
type SuccessResponse struct {
	Message string `json:"message"`
}

// ErrorResponse 错误响应
type ErrorResponse struct {
	Error   string `json:"error"`
	Message string `json:"message"`
}
