# AI运维管理平台 - 日志清理脚本
# Log Cleanup Script for AI Ops Platform

param(
    [switch]$DryRun = $false,
    [int]$KeepDays = 7,
    [string]$LogPath = ".\logs"
)

Write-Host "AI运维管理平台日志清理工具" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 检查日志目录
if (-not (Test-Path $LogPath)) {
    Write-Host "日志目录不存在: $LogPath" -ForegroundColor Red
    exit 1
}

# 获取当前日志文件信息
$currentLog = Get-ChildItem "$LogPath\aiops.log" -ErrorAction SilentlyContinue
if ($currentLog) {
    $sizeMB = [math]::Round($currentLog.Length / 1MB, 2)
    Write-Host "当前日志文件大小: $sizeMB MB" -ForegroundColor Yellow

    # 如果日志文件超过5MB，进行轮转
    if ($currentLog.Length -gt 5MB) {
        Write-Host "日志文件过大，开始轮转..." -ForegroundColor Yellow

        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupName = "aiops_$timestamp.log"
        $backupPath = Join-Path $LogPath $backupName

        if (-not $DryRun) {
            # 备份当前日志
            Copy-Item $currentLog.FullName $backupPath
            Write-Host "日志已备份到: $backupName" -ForegroundColor Green

            # 清空当前日志文件
            Clear-Content $currentLog.FullName
            Write-Host "当前日志文件已清空" -ForegroundColor Green
        } else {
            Write-Host "[模拟] 将备份到: $backupName" -ForegroundColor Cyan
            Write-Host "[模拟] 将清空当前日志文件" -ForegroundColor Cyan
        }
    }
}

# 清理旧的日志备份文件
$cutoffDate = (Get-Date).AddDays(-$KeepDays)
$oldLogs = Get-ChildItem "$LogPath\aiops_*.log" | Where-Object { $_.LastWriteTime -lt $cutoffDate }

if ($oldLogs) {
    Write-Host "发现 $($oldLogs.Count) 个过期日志文件 (超过 $KeepDays 天)" -ForegroundColor Yellow

    $totalSize = ($oldLogs | Measure-Object -Property Length -Sum).Sum
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)

    foreach ($log in $oldLogs) {
        $sizeMB = [math]::Round($log.Length / 1MB, 2)
        if (-not $DryRun) {
            Remove-Item $log.FullName -Force
            Write-Host "已删除: $($log.Name) ($sizeMB MB)" -ForegroundColor Red
        } else {
            Write-Host "[模拟] 将删除: $($log.Name) ($sizeMB MB)" -ForegroundColor Cyan
        }
    }

    if (-not $DryRun) {
        Write-Host "已释放磁盘空间: $totalSizeMB MB" -ForegroundColor Green
    } else {
        Write-Host "[模拟] 将释放磁盘空间: $totalSizeMB MB" -ForegroundColor Cyan
    }
} else {
    Write-Host "没有发现过期的日志文件" -ForegroundColor Green
}

# 显示清理后的状态
Write-Host ""
Write-Host "清理后状态:" -ForegroundColor Cyan
$remainingLogs = Get-ChildItem "$LogPath\*.log"
$totalSize = ($remainingLogs | Measure-Object -Property Length -Sum).Sum
$totalSizeMB = [math]::Round($totalSize / 1MB, 2)

Write-Host "   - 日志文件数量: $($remainingLogs.Count)" -ForegroundColor White
Write-Host "   - 总占用空间: $totalSizeMB MB" -ForegroundColor White

Write-Host ""
Write-Host "日志清理完成!" -ForegroundColor Green
