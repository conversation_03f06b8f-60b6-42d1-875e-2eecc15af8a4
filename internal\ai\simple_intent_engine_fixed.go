package ai

import (
	"context"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
)

// SimpleIntentEngine 简单高效的意图识别引擎
type SimpleIntentEngine struct {
	logger *logrus.Logger
}

// SimpleIntentResult 简单意图识别结果
type SimpleIntentResult struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Command    string                 `json:"command"`
}

// NewSimpleIntentEngine 创建简单意图引擎
func NewSimpleIntentEngine(logger *logrus.Logger) *SimpleIntentEngine {
	return &SimpleIntentEngine{
		logger: logger,
	}
}

// NewSimpleIntentEngineFixed 别名函数，避免冲突
func NewSimpleIntentEngineFixed(logger *logrus.Logger) *SimpleIntentEngine {
	return NewSimpleIntentEngine(logger)
}

// RecognizeIntent 识别意图
func (sie *SimpleIntentEngine) RecognizeIntent(ctx context.Context, message string) (*SimpleIntentResult, error) {
	messageLower := strings.ToLower(strings.TrimSpace(message))

	sie.logger.WithField("message", message).Info("开始意图识别")

	// 1. 主机列表查询
	if sie.isHostListQuery(messageLower) {
		return &SimpleIntentResult{
			Type:       "database_operations",
			Confidence: 0.9,
			Parameters: map[string]interface{}{
				"operation": "select",
				"table":     "hosts",
			},
			Command: "查询主机列表",
		}, nil
	}

	// 2. 主机添加操作
	if result := sie.parseHostAddition(messageLower, message); result != nil {
		return result, nil
	}

	// 3. 主机删除操作
	if result := sie.parseHostDeletion(messageLower, message); result != nil {
		return result, nil
	}

	// 4. 主机状态查询
	if result := sie.parseHostStatusQuery(messageLower, message); result != nil {
		return result, nil
	}

	// 5. SSH命令执行
	if result := sie.parseSSHCommand(messageLower, message); result != nil {
		return result, nil
	}

	// 6. 帮助和问候
	if sie.isHelpOrGreeting(messageLower) {
		return &SimpleIntentResult{
			Type:       "general_chat",
			Confidence: 0.8,
			Parameters: map[string]interface{}{
				"intent": sie.getGeneralChatType(messageLower),
			},
			Command: "通用对话",
		}, nil
	}

	// 默认返回通用对话
	return &SimpleIntentResult{
		Type:       "general_chat",
		Confidence: 0.3,
		Parameters: map[string]interface{}{
			"intent":  "unknown",
			"message": message,
		},
		Command: "未知意图",
	}, nil
}

// isHostListQuery 检查是否为主机列表查询
func (sie *SimpleIntentEngine) isHostListQuery(messageLower string) bool {
	listKeywords := []string{"列出", "查看", "显示", "list", "show"}
	hostKeywords := []string{"主机", "服务器", "hosts", "servers", "host"}

	hasListKeyword := false
	hasHostKeyword := false

	for _, keyword := range listKeywords {
		if strings.Contains(messageLower, keyword) {
			hasListKeyword = true
			break
		}
	}

	for _, keyword := range hostKeywords {
		if strings.Contains(messageLower, keyword) {
			hasHostKeyword = true
			break
		}
	}

	return hasListKeyword && hasHostKeyword
}

// parseHostAddition 解析主机添加操作
func (sie *SimpleIntentEngine) parseHostAddition(messageLower, originalMessage string) *SimpleIntentResult {
	// 检查添加关键词
	addKeywords := []string{"添加", "新增", "创建", "add", "create"}
	hasAddKeyword := false
	for _, keyword := range addKeywords {
		if strings.Contains(messageLower, keyword) {
			hasAddKeyword = true
			break
		}
	}

	if !hasAddKeyword || !strings.Contains(messageLower, "主机") {
		return nil
	}

	// 提取IP地址
	ipRegex := regexp.MustCompile(`(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})`)
	ipMatches := ipRegex.FindStringSubmatch(originalMessage)

	// 提取用户名和密码（简单模式）
	words := strings.Fields(originalMessage)

	params := map[string]interface{}{
		"operation": "insert",
		"table":     "hosts",
	}

	if len(ipMatches) > 1 {
		params["ip_address"] = ipMatches[1]
	}

	// 尝试提取用户名和密码
	if len(words) >= 4 {
		for i, word := range words {
			if ipRegex.MatchString(word) && i+2 < len(words) {
				params["username"] = words[i+1]
				params["password"] = words[i+2]
				break
			}
		}
	}

	return &SimpleIntentResult{
		Type:       "database_operations",
		Confidence: 0.85,
		Parameters: params,
		Command:    "添加主机",
	}
}

// parseHostDeletion 解析主机删除操作
func (sie *SimpleIntentEngine) parseHostDeletion(messageLower, originalMessage string) *SimpleIntentResult {
	deleteKeywords := []string{"删除", "移除", "delete", "remove"}
	hasDeleteKeyword := false
	for _, keyword := range deleteKeywords {
		if strings.Contains(messageLower, keyword) {
			hasDeleteKeyword = true
			break
		}
	}

	if !hasDeleteKeyword || !strings.Contains(messageLower, "主机") {
		return nil
	}

	// 提取IP地址或主机名
	ipRegex := regexp.MustCompile(`(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})`)
	ipMatches := ipRegex.FindStringSubmatch(originalMessage)

	params := map[string]interface{}{
		"operation": "delete",
		"table":     "hosts",
	}

	if len(ipMatches) > 1 {
		params["ip_address"] = ipMatches[1]
	}

	return &SimpleIntentResult{
		Type:       "database_operations",
		Confidence: 0.8,
		Parameters: params,
		Command:    "删除主机",
	}
}

// parseHostStatusQuery 解析主机状态查询
func (sie *SimpleIntentEngine) parseHostStatusQuery(messageLower, originalMessage string) *SimpleIntentResult {
	statusKeywords := []string{"状态", "status", "检查", "check"}
	hasStatusKeyword := false
	for _, keyword := range statusKeywords {
		if strings.Contains(messageLower, keyword) {
			hasStatusKeyword = true
			break
		}
	}

	if !hasStatusKeyword || !strings.Contains(messageLower, "主机") {
		return nil
	}

	// 提取IP地址
	ipRegex := regexp.MustCompile(`(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})`)
	ipMatches := ipRegex.FindStringSubmatch(originalMessage)

	params := map[string]interface{}{
		"operation": "status_check",
	}

	if len(ipMatches) > 1 {
		params["ip_address"] = ipMatches[1]
	}

	return &SimpleIntentResult{
		Type:       "monitoring_operations",
		Confidence: 0.8,
		Parameters: params,
		Command:    "检查主机状态",
	}
}

// parseSSHCommand 解析SSH命令执行
func (sie *SimpleIntentEngine) parseSSHCommand(messageLower, originalMessage string) *SimpleIntentResult {
	executeKeywords := []string{"执行", "运行", "execute", "run"}
	hasExecuteKeyword := false
	for _, keyword := range executeKeywords {
		if strings.Contains(messageLower, keyword) {
			hasExecuteKeyword = true
			break
		}
	}

	if !hasExecuteKeyword {
		return nil
	}

	// 提取IP地址和命令
	ipRegex := regexp.MustCompile(`(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})`)
	ipMatches := ipRegex.FindStringSubmatch(originalMessage)

	params := map[string]interface{}{
		"operation": "execute_command",
	}

	if len(ipMatches) > 1 {
		params["ip_address"] = ipMatches[1]
	}

	// 简单提取命令（在IP地址后面的部分）
	if len(ipMatches) > 1 {
		ipIndex := strings.Index(originalMessage, ipMatches[1])
		if ipIndex != -1 {
			afterIP := originalMessage[ipIndex+len(ipMatches[1]):]
			afterIP = strings.TrimSpace(afterIP)
			if afterIP != "" {
				params["command"] = afterIP
			}
		}
	}

	return &SimpleIntentResult{
		Type:       "ssh_operations",
		Confidence: 0.8,
		Parameters: params,
		Command:    "执行SSH命令",
	}
}

// isHelpOrGreeting 检查是否为帮助或问候
func (sie *SimpleIntentEngine) isHelpOrGreeting(messageLower string) bool {
	helpKeywords := []string{"帮助", "help", "?", "？"}
	greetingKeywords := []string{"你好", "hello", "hi", "嗨"}

	for _, keyword := range helpKeywords {
		if strings.Contains(messageLower, keyword) {
			return true
		}
	}

	for _, keyword := range greetingKeywords {
		if strings.Contains(messageLower, keyword) {
			return true
		}
	}

	return false
}

// getGeneralChatType 获取通用对话类型
func (sie *SimpleIntentEngine) getGeneralChatType(messageLower string) string {
	if strings.Contains(messageLower, "帮助") || strings.Contains(messageLower, "help") {
		return "help"
	}
	if strings.Contains(messageLower, "你好") || strings.Contains(messageLower, "hello") || strings.Contains(messageLower, "hi") {
		return "greeting"
	}
	return "general"
}
