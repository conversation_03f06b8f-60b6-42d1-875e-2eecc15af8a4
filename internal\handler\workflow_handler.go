package handler

import (
	"fmt"
	"net/http"
	"time"

	"aiops-platform/internal/service"
	"aiops-platform/internal/workflow"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// WorkflowHandler 工作流处理器
type WorkflowHandler struct {
	workflowService service.WorkflowService
	logger          *logrus.Logger
}

// NewWorkflowHandler 创建工作流处理器
func NewWorkflowHandler(workflowService service.WorkflowService, logger *logrus.Logger) *WorkflowHandler {
	return &WorkflowHandler{
		workflowService: workflowService,
		logger:          logger,
	}
}

// TriggerWorkflow 触发工作流
func (wh *WorkflowHandler) TriggerWorkflow(c *gin.Context) {
	var req workflow.TriggerWorkflowRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 从上下文获取用户信息
	userID, exists := c.Get("user_id")
	if exists {
		req.UserID = userID.(int64)
	}

	// 从上下文获取会话ID
	sessionID, exists := c.Get("session_id")
	if exists {
		req.SessionID = sessionID.(string)
	} else {
		// 如果没有会话ID，生成一个
		req.SessionID = generateSessionID()
	}

	instance, err := wh.workflowService.TriggerWorkflow(c.Request.Context(), &req)
	if err != nil {
		wh.logger.WithError(err).Error("Failed to trigger workflow")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to trigger workflow",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"instance_id": instance.ID,
		"status":      instance.Status,
		"message":     "Workflow triggered successfully",
	})
}

// ProcessInput 处理用户输入
func (wh *WorkflowHandler) ProcessInput(c *gin.Context) {
	instanceID := c.Param("instance_id")
	if instanceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Instance ID is required",
		})
		return
	}

	var req struct {
		Input string `json:"input" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	response, err := wh.workflowService.ProcessUserInput(c.Request.Context(), instanceID, req.Input)
	if err != nil {
		wh.logger.WithError(err).Error("Failed to process user input")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to process input",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetActiveWorkflows 获取活跃的工作流
func (wh *WorkflowHandler) GetActiveWorkflows(c *gin.Context) {
	sessionID := c.Query("session_id")
	if sessionID == "" {
		// 尝试从上下文获取
		if sid, exists := c.Get("session_id"); exists {
			sessionID = sid.(string)
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Session ID is required",
			})
			return
		}
	}

	workflows, err := wh.workflowService.GetActiveWorkflows(sessionID)
	if err != nil {
		wh.logger.WithError(err).Error("Failed to get active workflows")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get active workflows",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"workflows": workflows,
		"count":     len(workflows),
	})
}

// GetWorkflowInstance 获取工作流实例
func (wh *WorkflowHandler) GetWorkflowInstance(c *gin.Context) {
	instanceID := c.Param("instance_id")
	if instanceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Instance ID is required",
		})
		return
	}

	instance, err := wh.workflowService.GetWorkflowInstance(instanceID)
	if err != nil {
		wh.logger.WithError(err).Error("Failed to get workflow instance")
		c.JSON(http.StatusNotFound, gin.H{
			"error":   "Workflow instance not found",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"instance": instance,
	})
}

// CancelWorkflow 取消工作流
func (wh *WorkflowHandler) CancelWorkflow(c *gin.Context) {
	instanceID := c.Param("instance_id")
	if instanceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Instance ID is required",
		})
		return
	}

	err := wh.workflowService.CancelWorkflow(instanceID)
	if err != nil {
		wh.logger.WithError(err).Error("Failed to cancel workflow")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to cancel workflow",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Workflow cancelled successfully",
	})
}

// AnalyzeIntent 分析用户意图
func (wh *WorkflowHandler) AnalyzeIntent(c *gin.Context) {
	var req struct {
		Message   string `json:"message" binding:"required"`
		SessionID string `json:"session_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.Error(),
		})
		return
	}

	// 如果没有提供会话ID，尝试从上下文获取
	if req.SessionID == "" {
		if sid, exists := c.Get("session_id"); exists {
			req.SessionID = sid.(string)
		} else {
			req.SessionID = generateSessionID()
		}
	}

	result, err := wh.workflowService.AnalyzeUserIntent(c.Request.Context(), req.SessionID, req.Message)
	if err != nil {
		wh.logger.WithError(err).Error("Failed to analyze user intent")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to analyze intent",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"result":  result,
	})
}

// GenerateGuidance 生成工作流引导
func (wh *WorkflowHandler) GenerateGuidance(c *gin.Context) {
	sessionID := c.Query("session_id")
	if sessionID == "" {
		// 尝试从上下文获取
		if sid, exists := c.Get("session_id"); exists {
			sessionID = sid.(string)
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"error": "Session ID is required",
			})
			return
		}
	}

	guidance, err := wh.workflowService.GenerateGuidance(c.Request.Context(), sessionID)
	if err != nil {
		wh.logger.WithError(err).Error("Failed to generate guidance")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate guidance",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":  true,
		"guidance": guidance,
	})
}

// GetWorkflowDefinitions 获取工作流定义
func (wh *WorkflowHandler) GetWorkflowDefinitions(c *gin.Context) {
	category := c.Query("category")

	definitions, err := wh.workflowService.GetWorkflowDefinitions(category)
	if err != nil {
		wh.logger.WithError(err).Error("Failed to get workflow definitions")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get workflow definitions",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":     true,
		"definitions": definitions,
		"count":       len(definitions),
	})
}

// GetWorkflowMetrics 获取工作流指标
func (wh *WorkflowHandler) GetWorkflowMetrics(c *gin.Context) {
	metrics, err := wh.workflowService.GetWorkflowMetrics()
	if err != nil {
		wh.logger.WithError(err).Error("Failed to get workflow metrics")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get workflow metrics",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"metrics": metrics,
	})
}

// GetWorkflowHistory 获取工作流历史
func (wh *WorkflowHandler) GetWorkflowHistory(c *gin.Context) {
	instanceID := c.Param("instance_id")
	if instanceID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Instance ID is required",
		})
		return
	}

	history, err := wh.workflowService.GetWorkflowHistory(instanceID)
	if err != nil {
		wh.logger.WithError(err).Error("Failed to get workflow history")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get workflow history",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"history": history,
	})
}

// RegisterRoutes 注册路由
func (wh *WorkflowHandler) RegisterRoutes(r *gin.RouterGroup) {
	workflow := r.Group("/workflow")
	{
		// 工作流管理
		workflow.POST("/trigger", wh.TriggerWorkflow)
		workflow.POST("/:instance_id/input", wh.ProcessInput)
		workflow.GET("/active", wh.GetActiveWorkflows)
		workflow.GET("/:instance_id", wh.GetWorkflowInstance)
		workflow.DELETE("/:instance_id", wh.CancelWorkflow)

		// 智能引导
		workflow.POST("/analyze-intent", wh.AnalyzeIntent)
		workflow.GET("/guidance", wh.GenerateGuidance)

		// 工作流定义和统计
		workflow.GET("/definitions", wh.GetWorkflowDefinitions)
		workflow.GET("/metrics", wh.GetWorkflowMetrics)
		workflow.GET("/:instance_id/history", wh.GetWorkflowHistory)
	}
}

// generateSessionID 生成会话ID
func generateSessionID() string {
	return fmt.Sprintf("session_%d", time.Now().UnixNano())
}
