package resilience

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// RecoveryAction 恢复动作类型
type RecoveryAction string

const (
	ActionRestart     RecoveryAction = "restart"      // 重启服务
	ActionReconnect   RecoveryAction = "reconnect"    // 重新连接
	ActionClearCache  RecoveryAction = "clear_cache"  // 清除缓存
	ActionResetState  RecoveryAction = "reset_state"  // 重置状态
	ActionFallback    RecoveryAction = "fallback"     // 降级处理
	ActionNotify      RecoveryAction = "notify"       // 通知管理员
	ActionCustom      RecoveryAction = "custom"       // 自定义动作
)

// ErrorPattern 错误模式
type ErrorPattern struct {
	Name        string   `json:"name"`
	Keywords    []string `json:"keywords"`     // 错误关键词
	ErrorCodes  []string `json:"error_codes"`  // 错误代码
	Severity    string   `json:"severity"`     // 严重程度: low, medium, high, critical
	MaxRetries  int      `json:"max_retries"`  // 最大重试次数
	RetryDelay  time.Duration `json:"retry_delay"` // 重试延迟
	Actions     []RecoveryAction `json:"actions"`  // 恢复动作
}

// RecoveryRule 恢复规则
type RecoveryRule struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Enabled     bool           `json:"enabled"`
	Pattern     ErrorPattern   `json:"pattern"`
	Conditions  []string       `json:"conditions"`  // 触发条件
	CooldownPeriod time.Duration `json:"cooldown_period"` // 冷却期
	MaxExecutions  int          `json:"max_executions"`  // 最大执行次数
	Handler     RecoveryHandler `json:"-"`           // 恢复处理器
}

// RecoveryHandler 恢复处理器接口
type RecoveryHandler interface {
	Handle(ctx context.Context, errorInfo ErrorInfo) error
	Name() string
}

// ErrorInfo 错误信息
type ErrorInfo struct {
	Error       error                  `json:"error"`
	Timestamp   time.Time              `json:"timestamp"`
	Service     string                 `json:"service"`
	Component   string                 `json:"component"`
	Severity    string                 `json:"severity"`
	Context     map[string]interface{} `json:"context"`
	StackTrace  string                 `json:"stack_trace"`
	RequestID   string                 `json:"request_id"`
	UserID      string                 `json:"user_id"`
}

// RecoveryExecution 恢复执行记录
type RecoveryExecution struct {
	RuleID      string    `json:"rule_id"`
	Timestamp   time.Time `json:"timestamp"`
	Success     bool      `json:"success"`
	Duration    time.Duration `json:"duration"`
	Error       string    `json:"error,omitempty"`
	Actions     []RecoveryAction `json:"actions"`
}

// ErrorRecoveryManager 错误恢复管理器
type ErrorRecoveryManager struct {
	rules       map[string]*RecoveryRule
	handlers    map[RecoveryAction]RecoveryHandler
	executions  []RecoveryExecution
	mutex       sync.RWMutex
	logger      *logrus.Logger
	enabled     bool
	maxExecutions int
	cleanupInterval time.Duration
	stopChan    chan struct{}
	running     bool
}

// NewErrorRecoveryManager 创建错误恢复管理器
func NewErrorRecoveryManager(logger *logrus.Logger) *ErrorRecoveryManager {
	erm := &ErrorRecoveryManager{
		rules:           make(map[string]*RecoveryRule),
		handlers:        make(map[RecoveryAction]RecoveryHandler),
		executions:      make([]RecoveryExecution, 0),
		logger:          logger,
		enabled:         true,
		maxExecutions:   1000,
		cleanupInterval: 24 * time.Hour,
		stopChan:        make(chan struct{}),
	}

	// 注册默认处理器
	erm.registerDefaultHandlers()

	return erm
}

// Start 启动错误恢复管理器
func (erm *ErrorRecoveryManager) Start(ctx context.Context) error {
	erm.mutex.Lock()
	defer erm.mutex.Unlock()

	if erm.running {
		return fmt.Errorf("error recovery manager is already running")
	}

	erm.running = true

	// 启动清理协程
	go erm.cleanupLoop(ctx)

	erm.logger.Info("Error recovery manager started")
	return nil
}

// Stop 停止错误恢复管理器
func (erm *ErrorRecoveryManager) Stop() error {
	erm.mutex.Lock()
	defer erm.mutex.Unlock()

	if !erm.running {
		return nil
	}

	close(erm.stopChan)
	erm.running = false

	erm.logger.Info("Error recovery manager stopped")
	return nil
}

// RegisterRule 注册恢复规则
func (erm *ErrorRecoveryManager) RegisterRule(rule *RecoveryRule) error {
	erm.mutex.Lock()
	defer erm.mutex.Unlock()

	if rule.ID == "" {
		return fmt.Errorf("rule ID cannot be empty")
	}

	erm.rules[rule.ID] = rule
	erm.logger.WithFields(logrus.Fields{
		"rule_id":   rule.ID,
		"rule_name": rule.Name,
	}).Info("Recovery rule registered")

	return nil
}

// RegisterHandler 注册恢复处理器
func (erm *ErrorRecoveryManager) RegisterHandler(action RecoveryAction, handler RecoveryHandler) {
	erm.mutex.Lock()
	defer erm.mutex.Unlock()

	erm.handlers[action] = handler
	erm.logger.WithFields(logrus.Fields{
		"action":  action,
		"handler": handler.Name(),
	}).Info("Recovery handler registered")
}

// HandleError 处理错误
func (erm *ErrorRecoveryManager) HandleError(ctx context.Context, errorInfo ErrorInfo) error {
	if !erm.enabled {
		return nil
	}

	erm.mutex.RLock()
	rules := make([]*RecoveryRule, 0, len(erm.rules))
	for _, rule := range erm.rules {
		if rule.Enabled && erm.matchesPattern(errorInfo, rule.Pattern) {
			rules = append(rules, rule)
		}
	}
	erm.mutex.RUnlock()

	if len(rules) == 0 {
		erm.logger.WithFields(logrus.Fields{
			"error":   errorInfo.Error.Error(),
			"service": errorInfo.Service,
		}).Debug("No matching recovery rules found")
		return nil
	}

	// 执行匹配的规则
	for _, rule := range rules {
		if erm.shouldExecuteRule(rule) {
			if err := erm.executeRule(ctx, rule, errorInfo); err != nil {
				erm.logger.WithFields(logrus.Fields{
					"rule_id": rule.ID,
					"error":   err.Error(),
				}).Error("Failed to execute recovery rule")
			}
		}
	}

	return nil
}

// matchesPattern 检查错误是否匹配模式
func (erm *ErrorRecoveryManager) matchesPattern(errorInfo ErrorInfo, pattern ErrorPattern) bool {
	errorStr := errorInfo.Error.Error()

	// 检查关键词
	for _, keyword := range pattern.Keywords {
		if keyword != "" && contains(errorStr, keyword) {
			return true
		}
	}

	// 检查错误代码
	for _, code := range pattern.ErrorCodes {
		if code != "" && contains(errorStr, code) {
			return true
		}
	}

	return false
}

// shouldExecuteRule 检查是否应该执行规则
func (erm *ErrorRecoveryManager) shouldExecuteRule(rule *RecoveryRule) bool {
	erm.mutex.RLock()
	defer erm.mutex.RUnlock()

	now := time.Now()
	executionCount := 0
	lastExecution := time.Time{}

	// 统计最近的执行次数
	for _, execution := range erm.executions {
		if execution.RuleID == rule.ID {
			if now.Sub(execution.Timestamp) <= rule.CooldownPeriod {
				return false // 在冷却期内
			}
			if execution.Timestamp.After(lastExecution) {
				lastExecution = execution.Timestamp
			}
			executionCount++
		}
	}

	// 检查最大执行次数
	if rule.MaxExecutions > 0 && executionCount >= rule.MaxExecutions {
		return false
	}

	return true
}

// executeRule 执行恢复规则
func (erm *ErrorRecoveryManager) executeRule(ctx context.Context, rule *RecoveryRule, errorInfo ErrorInfo) error {
	startTime := time.Now()
	var lastErr error

	erm.logger.WithFields(logrus.Fields{
		"rule_id":   rule.ID,
		"rule_name": rule.Name,
		"error":     errorInfo.Error.Error(),
	}).Info("Executing recovery rule")

	// 执行恢复动作
	for _, action := range rule.Pattern.Actions {
		if err := erm.executeAction(ctx, action, errorInfo); err != nil {
			lastErr = err
			erm.logger.WithFields(logrus.Fields{
				"rule_id": rule.ID,
				"action":  action,
				"error":   err.Error(),
			}).Error("Recovery action failed")
		}
	}

	// 如果有自定义处理器，执行它
	if rule.Handler != nil {
		if err := rule.Handler.Handle(ctx, errorInfo); err != nil {
			lastErr = err
			erm.logger.WithFields(logrus.Fields{
				"rule_id": rule.ID,
				"handler": rule.Handler.Name(),
				"error":   err.Error(),
			}).Error("Custom recovery handler failed")
		}
	}

	// 记录执行结果
	execution := RecoveryExecution{
		RuleID:    rule.ID,
		Timestamp: startTime,
		Success:   lastErr == nil,
		Duration:  time.Since(startTime),
		Actions:   rule.Pattern.Actions,
	}
	if lastErr != nil {
		execution.Error = lastErr.Error()
	}

	erm.mutex.Lock()
	erm.executions = append(erm.executions, execution)
	erm.mutex.Unlock()

	return lastErr
}

// executeAction 执行恢复动作
func (erm *ErrorRecoveryManager) executeAction(ctx context.Context, action RecoveryAction, errorInfo ErrorInfo) error {
	erm.mutex.RLock()
	handler, exists := erm.handlers[action]
	erm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("no handler registered for action: %s", action)
	}

	return handler.Handle(ctx, errorInfo)
}

// cleanupLoop 清理循环
func (erm *ErrorRecoveryManager) cleanupLoop(ctx context.Context) {
	ticker := time.NewTicker(erm.cleanupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-erm.stopChan:
			return
		case <-ticker.C:
			erm.cleanup()
		}
	}
}

// cleanup 清理过期的执行记录
func (erm *ErrorRecoveryManager) cleanup() {
	erm.mutex.Lock()
	defer erm.mutex.Unlock()

	now := time.Now()
	cutoff := now.Add(-7 * 24 * time.Hour) // 保留7天的记录

	filtered := make([]RecoveryExecution, 0)
	for _, execution := range erm.executions {
		if execution.Timestamp.After(cutoff) {
			filtered = append(filtered, execution)
		}
	}

	removed := len(erm.executions) - len(filtered)
	erm.executions = filtered

	if removed > 0 {
		erm.logger.WithField("removed_count", removed).Debug("Cleaned up old recovery executions")
	}

	// 限制最大记录数
	if len(erm.executions) > erm.maxExecutions {
		erm.executions = erm.executions[len(erm.executions)-erm.maxExecutions:]
	}
}

// GetStats 获取统计信息
func (erm *ErrorRecoveryManager) GetStats() map[string]interface{} {
	erm.mutex.RLock()
	defer erm.mutex.RUnlock()

	totalExecutions := len(erm.executions)
	successfulExecutions := 0
	for _, execution := range erm.executions {
		if execution.Success {
			successfulExecutions++
		}
	}

	successRate := 0.0
	if totalExecutions > 0 {
		successRate = float64(successfulExecutions) / float64(totalExecutions)
	}

	return map[string]interface{}{
		"enabled":               erm.enabled,
		"total_rules":           len(erm.rules),
		"total_handlers":        len(erm.handlers),
		"total_executions":      totalExecutions,
		"successful_executions": successfulExecutions,
		"success_rate":          successRate,
	}
}

// registerDefaultHandlers 注册默认处理器
func (erm *ErrorRecoveryManager) registerDefaultHandlers() {
	// 注册基本的恢复处理器
	erm.RegisterHandler(ActionNotify, &NotificationHandler{logger: erm.logger})
	erm.RegisterHandler(ActionClearCache, &ClearCacheHandler{logger: erm.logger})
	erm.RegisterHandler(ActionResetState, &ResetStateHandler{logger: erm.logger})
}

// 默认处理器实现

// NotificationHandler 通知处理器
type NotificationHandler struct {
	logger *logrus.Logger
}

func (h *NotificationHandler) Name() string {
	return "notification_handler"
}

func (h *NotificationHandler) Handle(ctx context.Context, errorInfo ErrorInfo) error {
	h.logger.WithFields(logrus.Fields{
		"error":     errorInfo.Error.Error(),
		"service":   errorInfo.Service,
		"component": errorInfo.Component,
		"severity":  errorInfo.Severity,
	}).Error("Error recovery notification")
	return nil
}

// ClearCacheHandler 清除缓存处理器
type ClearCacheHandler struct {
	logger *logrus.Logger
}

func (h *ClearCacheHandler) Name() string {
	return "clear_cache_handler"
}

func (h *ClearCacheHandler) Handle(ctx context.Context, errorInfo ErrorInfo) error {
	h.logger.WithField("service", errorInfo.Service).Info("Clearing cache for error recovery")
	// 这里应该实现实际的缓存清除逻辑
	return nil
}

// ResetStateHandler 重置状态处理器
type ResetStateHandler struct {
	logger *logrus.Logger
}

func (h *ResetStateHandler) Name() string {
	return "reset_state_handler"
}

func (h *ResetStateHandler) Handle(ctx context.Context, errorInfo ErrorInfo) error {
	h.logger.WithField("service", errorInfo.Service).Info("Resetting state for error recovery")
	// 这里应该实现实际的状态重置逻辑
	return nil
}

// 辅助函数
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || 
		(len(substr) > 0 && len(s) > len(substr) && 
		 (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
		  findSubstring(s, substr))))
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}
