package service

import (
	"fmt"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// statsService 统计服务实现
type statsService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewStatsService 创建统计服务
func NewStatsService(db *gorm.DB, logger *logrus.Logger) StatsService {
	return &statsService{
		db:     db,
		logger: logger,
	}
}

// GetSystemOverview 获取系统概览
func (s *statsService) GetSystemOverview() (*SystemOverviewResponse, error) {
	var overview SystemOverviewResponse

	// 获取主机统计
	var hostStats struct {
		Total  int64 `json:"total"`
		Online int64 `json:"online"`
		Error  int64 `json:"error"`
	}

	if err := s.db.Model(&model.Host{}).Count(&hostStats.Total).Error; err != nil {
		return nil, fmt.Errorf("failed to count hosts: %w", err)
	}

	if err := s.db.Model(&model.Host{}).Where("status = ?", "online").Count(&hostStats.Online).Error; err != nil {
		return nil, fmt.Errorf("failed to count online hosts: %w", err)
	}

	if err := s.db.Model(&model.Host{}).Where("status = ?", "error").Count(&hostStats.Error).Error; err != nil {
		return nil, fmt.Errorf("failed to count error hosts: %w", err)
	}

	overview.Hosts = hostStats

	// 获取告警统计
	var alertStats struct {
		Total    int64 `json:"total"`
		Critical int64 `json:"critical"`
		Warning  int64 `json:"warning"`
		Active   int64 `json:"active"`
	}

	if err := s.db.Model(&model.Alert{}).Count(&alertStats.Total).Error; err != nil {
		return nil, fmt.Errorf("failed to count alerts: %w", err)
	}

	if err := s.db.Model(&model.Alert{}).Where("severity = ?", "critical").Count(&alertStats.Critical).Error; err != nil {
		return nil, fmt.Errorf("failed to count critical alerts: %w", err)
	}

	if err := s.db.Model(&model.Alert{}).Where("severity = ?", "warning").Count(&alertStats.Warning).Error; err != nil {
		return nil, fmt.Errorf("failed to count warning alerts: %w", err)
	}

	if err := s.db.Model(&model.Alert{}).Where("status = ?", "active").Count(&alertStats.Active).Error; err != nil {
		return nil, fmt.Errorf("failed to count active alerts: %w", err)
	}

	overview.Alerts = alertStats

	// 获取用户统计
	var userStats struct {
		Total  int64 `json:"total"`
		Active int64 `json:"active"`
		Online int64 `json:"online"`
	}

	if err := s.db.Model(&model.User{}).Count(&userStats.Total).Error; err != nil {
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	if err := s.db.Model(&model.User{}).Where("is_active = ?", true).Count(&userStats.Active).Error; err != nil {
		return nil, fmt.Errorf("failed to count active users: %w", err)
	}

	// 获取在线用户数（最近15分钟有活动的用户）
	recentTime := time.Now().Add(-15 * time.Minute)
	if err := s.db.Model(&model.User{}).Where("last_login > ?", recentTime).Count(&userStats.Online).Error; err != nil {
		return nil, fmt.Errorf("failed to count online users: %w", err)
	}

	overview.Users = userStats

	// 获取会话统计
	var sessionStats struct {
		Total  int64 `json:"total"`
		Active int64 `json:"active"`
		Today  int64 `json:"today"`
	}

	if err := s.db.Model(&model.ChatSession{}).Count(&sessionStats.Total).Error; err != nil {
		return nil, fmt.Errorf("failed to count sessions: %w", err)
	}

	if err := s.db.Model(&model.ChatSession{}).Where("status = ?", "active").Count(&sessionStats.Active).Error; err != nil {
		return nil, fmt.Errorf("failed to count active sessions: %w", err)
	}

	today := time.Now().Truncate(24 * time.Hour)
	if err := s.db.Model(&model.ChatSession{}).Where("started_at >= ?", today).Count(&sessionStats.Today).Error; err != nil {
		return nil, fmt.Errorf("failed to count today sessions: %w", err)
	}

	overview.Sessions = sessionStats

	// 获取操作统计
	var operationStats struct {
		Total    int64 `json:"total"`
		Success  int64 `json:"success"`
		Failed   int64 `json:"failed"`
		Today    int64 `json:"today"`
		LastHour int64 `json:"last_hour"`
	}

	if err := s.db.Model(&model.OperationLog{}).Count(&operationStats.Total).Error; err != nil {
		return nil, fmt.Errorf("failed to count operations: %w", err)
	}

	if err := s.db.Model(&model.OperationLog{}).Where("status = ?", "success").Count(&operationStats.Success).Error; err != nil {
		return nil, fmt.Errorf("failed to count success operations: %w", err)
	}

	if err := s.db.Model(&model.OperationLog{}).Where("status = ?", "failed").Count(&operationStats.Failed).Error; err != nil {
		return nil, fmt.Errorf("failed to count failed operations: %w", err)
	}

	if err := s.db.Model(&model.OperationLog{}).Where("executed_at >= ?", today).Count(&operationStats.Today).Error; err != nil {
		return nil, fmt.Errorf("failed to count today operations: %w", err)
	}

	lastHour := time.Now().Add(-1 * time.Hour)
	if err := s.db.Model(&model.OperationLog{}).Where("executed_at >= ?", lastHour).Count(&operationStats.LastHour).Error; err != nil {
		return nil, fmt.Errorf("failed to count last hour operations: %w", err)
	}

	overview.Operations = operationStats

	// 系统信息
	overview.SystemInfo = map[string]interface{}{
		"uptime":      time.Since(time.Now().Truncate(24 * time.Hour)).String(),
		"version":     "1.0.0",
		"environment": "production",
		"updated_at":  time.Now(),
	}

	return &overview, nil
}

// GetHostStats 获取主机统计
func (s *statsService) GetHostStats(req *HostStatsRequest) (*HostStatsResponse, error) {
	var response HostStatsResponse

	// 按环境分组统计
	var envStats []struct {
		Environment string `json:"environment"`
		Count       int64  `json:"count"`
		Online      int64  `json:"online"`
	}

	if err := s.db.Model(&model.Host{}).
		Select("environment, COUNT(*) as count, SUM(CASE WHEN status = 'online' THEN 1 ELSE 0 END) as online").
		Group("environment").
		Find(&envStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get environment stats: %w", err)
	}

	response.ByEnvironment = envStats

	// 按状态分组统计
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	if err := s.db.Model(&model.Host{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get status stats: %w", err)
	}

	response.ByStatus = statusStats

	// 按组分组统计
	var groupStats []struct {
		GroupName string `json:"group_name"`
		Count     int64  `json:"count"`
	}

	if err := s.db.Model(&model.Host{}).
		Select("group_name, COUNT(*) as count").
		Where("group_name != ''").
		Group("group_name").
		Find(&groupStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get group stats: %w", err)
	}

	response.ByGroup = groupStats

	// 连接统计
	var connectionStats []struct {
		Date        string `json:"date"`
		Connections int64  `json:"connections"`
	}

	// 获取最近7天的连接统计
	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Format("2006-01-02")
		startTime := time.Now().AddDate(0, 0, -i).Truncate(24 * time.Hour)
		endTime := startTime.Add(24 * time.Hour)

		var count int64
		s.db.Model(&model.OperationLog{}).
			Where("operation_type = ? AND executed_at >= ? AND executed_at < ?", "ssh_command", startTime, endTime).
			Count(&count)

		connectionStats = append(connectionStats, struct {
			Date        string `json:"date"`
			Connections int64  `json:"connections"`
		}{
			Date:        date,
			Connections: count,
		})
	}

	response.ConnectionTrend = connectionStats

	// 最活跃主机
	var activeHosts []struct {
		HostID          int64  `json:"host_id"`
		HostName        string `json:"host_name"`
		IPAddress       string `json:"ip_address"`
		ConnectionCount int64  `json:"connection_count"`
	}

	if err := s.db.Table("hosts h").
		Select("h.id as host_id, h.name as host_name, h.ip_address, COUNT(ol.id) as connection_count").
		Joins("LEFT JOIN operations_log ol ON h.id = ol.host_id").
		Where("ol.executed_at >= ?", time.Now().AddDate(0, 0, -7)).
		Group("h.id, h.name, h.ip_address").
		Order("connection_count DESC").
		Limit(10).
		Find(&activeHosts).Error; err != nil {
		return nil, fmt.Errorf("failed to get active hosts: %w", err)
	}

	response.MostActive = activeHosts

	return &response, nil
}

// GetAlertStats 获取告警统计
func (s *statsService) GetAlertStats(req *AlertStatsRequest) (*AlertStatsResponse, error) {
	var response AlertStatsResponse

	// 按严重程度统计
	var severityStats []struct {
		Severity string `json:"severity"`
		Count    int64  `json:"count"`
		Active   int64  `json:"active"`
	}

	if err := s.db.Model(&model.Alert{}).
		Select("severity, COUNT(*) as count, SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active").
		Group("severity").
		Find(&severityStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get severity stats: %w", err)
	}

	response.BySeverity = severityStats

	// 按状态统计
	var statusStats []struct {
		Status string `json:"status"`
		Count  int64  `json:"count"`
	}

	if err := s.db.Model(&model.Alert{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get alert status stats: %w", err)
	}

	response.ByStatus = statusStats

	// 告警趋势（最近7天）
	var alertTrend []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}

	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Format("2006-01-02")
		startTime := time.Now().AddDate(0, 0, -i).Truncate(24 * time.Hour)
		endTime := startTime.Add(24 * time.Hour)

		var count int64
		s.db.Model(&model.Alert{}).
			Where("created_at >= ? AND created_at < ?", startTime, endTime).
			Count(&count)

		alertTrend = append(alertTrend, struct {
			Date  string `json:"date"`
			Count int64  `json:"count"`
		}{
			Date:  date,
			Count: count,
		})
	}

	response.AlertTrend = alertTrend

	// 最近告警
	var recentAlerts []struct {
		ID        int64     `json:"id"`
		Title     string    `json:"title"`
		Severity  string    `json:"severity"`
		Status    string    `json:"status"`
		HostName  string    `json:"host_name"`
		CreatedAt time.Time `json:"created_at"`
	}

	if err := s.db.Table("alerts a").
		Select("a.id, a.title, a.severity, a.status, h.name as host_name, a.created_at").
		Joins("LEFT JOIN hosts h ON a.host_id = h.id").
		Order("a.created_at DESC").
		Limit(10).
		Find(&recentAlerts).Error; err != nil {
		return nil, fmt.Errorf("failed to get recent alerts: %w", err)
	}

	response.RecentAlerts = recentAlerts

	return &response, nil
}

// GetOperationStats 获取操作统计
func (s *statsService) GetOperationStats(req *OperationStatsRequest) (*OperationStatsResponse, error) {
	var response OperationStatsResponse

	// 按操作类型统计
	var typeStats []struct {
		OperationType string  `json:"operation_type"`
		Count         int64   `json:"count"`
		SuccessRate   float64 `json:"success_rate"`
	}

	if err := s.db.Model(&model.OperationLog{}).
		Select("operation_type, COUNT(*) as count, AVG(CASE WHEN status = 'success' THEN 1.0 ELSE 0.0 END) * 100 as success_rate").
		Group("operation_type").
		Find(&typeStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get operation type stats: %w", err)
	}

	response.ByType = typeStats

	// 操作趋势（最近24小时，按小时统计）
	var operationTrend []struct {
		Hour    string `json:"hour"`
		Count   int64  `json:"count"`
		Success int64  `json:"success"`
		Failed  int64  `json:"failed"`
	}

	for i := 23; i >= 0; i-- {
		hour := time.Now().Add(time.Duration(-i) * time.Hour).Format("15:04")
		startTime := time.Now().Add(time.Duration(-i) * time.Hour).Truncate(time.Hour)
		endTime := startTime.Add(time.Hour)

		var total, success, failed int64
		s.db.Model(&model.OperationLog{}).
			Where("executed_at >= ? AND executed_at < ?", startTime, endTime).
			Count(&total)

		s.db.Model(&model.OperationLog{}).
			Where("executed_at >= ? AND executed_at < ? AND status = ?", startTime, endTime, "success").
			Count(&success)

		s.db.Model(&model.OperationLog{}).
			Where("executed_at >= ? AND executed_at < ? AND status = ?", startTime, endTime, "failed").
			Count(&failed)

		operationTrend = append(operationTrend, struct {
			Hour    string `json:"hour"`
			Count   int64  `json:"count"`
			Success int64  `json:"success"`
			Failed  int64  `json:"failed"`
		}{
			Hour:    hour,
			Count:   total,
			Success: success,
			Failed:  failed,
		})
	}

	response.OperationTrend = operationTrend

	// 最活跃用户
	var activeUsers []struct {
		UserID         int64  `json:"user_id"`
		Username       string `json:"username"`
		OperationCount int64  `json:"operation_count"`
	}

	if err := s.db.Table("operations_log ol").
		Select("ol.user_id, u.username, COUNT(ol.id) as operation_count").
		Joins("LEFT JOIN users u ON ol.user_id = u.id").
		Where("ol.executed_at >= ?", time.Now().AddDate(0, 0, -7)).
		Group("ol.user_id, u.username").
		Order("operation_count DESC").
		Limit(10).
		Find(&activeUsers).Error; err != nil {
		return nil, fmt.Errorf("failed to get active users: %w", err)
	}

	response.MostActiveUsers = activeUsers

	return &response, nil
}

// GetUserStats 获取用户统计
func (s *statsService) GetUserStats(req *UserStatsRequest) (*UserStatsResponse, error) {
	var response UserStatsResponse

	// 按角色统计
	var roleStats []struct {
		Role   string `json:"role"`
		Count  int64  `json:"count"`
		Active int64  `json:"active"`
	}

	if err := s.db.Model(&model.User{}).
		Select("role, COUNT(*) as count, SUM(CASE WHEN is_active = true THEN 1 ELSE 0 END) as active").
		Group("role").
		Find(&roleStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get role stats: %w", err)
	}

	response.ByRole = roleStats

	// 登录趋势（最近7天）
	var loginTrend []struct {
		Date   string `json:"date"`
		Logins int64  `json:"logins"`
		Users  int64  `json:"users"`
	}

	for i := 6; i >= 0; i-- {
		date := time.Now().AddDate(0, 0, -i).Format("2006-01-02")
		startTime := time.Now().AddDate(0, 0, -i).Truncate(24 * time.Hour)
		endTime := startTime.Add(24 * time.Hour)

		var logins, users int64
		s.db.Model(&model.OperationLog{}).
			Where("operation_type = ? AND executed_at >= ? AND executed_at < ?", "login", startTime, endTime).
			Count(&logins)

		s.db.Model(&model.User{}).
			Where("last_login >= ? AND last_login < ?", startTime, endTime).
			Count(&users)

		loginTrend = append(loginTrend, struct {
			Date   string `json:"date"`
			Logins int64  `json:"logins"`
			Users  int64  `json:"users"`
		}{
			Date:   date,
			Logins: logins,
			Users:  users,
		})
	}

	response.LoginTrend = loginTrend

	return &response, nil
}

// 请求响应结构体定义
type SystemOverviewResponse struct {
	Hosts      interface{}            `json:"hosts"`
	Alerts     interface{}            `json:"alerts"`
	Users      interface{}            `json:"users"`
	Sessions   interface{}            `json:"sessions"`
	Operations interface{}            `json:"operations"`
	SystemInfo map[string]interface{} `json:"system_info"`
}

type HostStatsRequest struct {
	TimeRange string `json:"time_range"`
	GroupBy   string `json:"group_by"`
}

type HostStatsResponse struct {
	ByEnvironment   interface{} `json:"by_environment"`
	ByStatus        interface{} `json:"by_status"`
	ByGroup         interface{} `json:"by_group"`
	ConnectionTrend interface{} `json:"connection_trend"`
	MostActive      interface{} `json:"most_active"`
}

type AlertStatsRequest struct {
	TimeRange string `json:"time_range"`
	Severity  string `json:"severity"`
}

type AlertStatsResponse struct {
	BySeverity   interface{} `json:"by_severity"`
	ByStatus     interface{} `json:"by_status"`
	AlertTrend   interface{} `json:"alert_trend"`
	RecentAlerts interface{} `json:"recent_alerts"`
}

type OperationStatsRequest struct {
	TimeRange string `json:"time_range"`
	UserID    *int64 `json:"user_id"`
}

type OperationStatsResponse struct {
	ByType          interface{} `json:"by_type"`
	OperationTrend  interface{} `json:"operation_trend"`
	MostActiveUsers interface{} `json:"most_active_users"`
}

type UserStatsRequest struct {
	TimeRange string `json:"time_range"`
	Role      string `json:"role"`
}

type UserStatsResponse struct {
	ByRole     interface{} `json:"by_role"`
	LoginTrend interface{} `json:"login_trend"`
}
