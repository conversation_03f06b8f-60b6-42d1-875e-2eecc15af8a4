package agent

import (
	"context"
	"fmt"
	"time"

	"aiops-platform/internal/interfaces"
	// "aiops-platform/internal/service"
	// "aiops-platform/internal/workflow"

	"github.com/sirupsen/logrus"
)

// AgentPlatform Agent-based智能运维平台
type AgentPlatform struct {
	registry        *AgentRegistry
	decisionEngine  *DecisionEngine
	executionEngine *ExecutionEngine
	eventBus        *EventBus
	logger          *logrus.Logger
	config          *PlatformConfig

	// 服务依赖
	deepseekService interfaces.DeepSeekServiceInterface
	hostService     interfaces.HostServiceInterface

	// 状态管理
	status    PlatformStatus
	startTime time.Time
}

// PlatformConfig 平台配置
type PlatformConfig struct {
	EnableAutoRegistration bool          `json:"enable_auto_registration"`
	HealthCheckInterval    time.Duration `json:"health_check_interval"`
	MaxConcurrentRequests  int           `json:"max_concurrent_requests"`
	RequestTimeout         time.Duration `json:"request_timeout"`
	EnableMetrics          bool          `json:"enable_metrics"`
	EnableEventLogging     bool          `json:"enable_event_logging"`
}

// PlatformStatus 平台状态
type PlatformStatus string

const (
	PlatformStatusStopped      PlatformStatus = "stopped"
	PlatformStatusInitializing PlatformStatus = "initializing"
	PlatformStatusRunning      PlatformStatus = "running"
	PlatformStatusError        PlatformStatus = "error"
	PlatformStatusMaintenance  PlatformStatus = "maintenance"
)

// PlatformRequest 平台请求
type PlatformRequest struct {
	UserMessage string                 `json:"user_message"`
	SessionID   string                 `json:"session_id"`
	UserID      int64                  `json:"user_id"`
	TraceID     string                 `json:"trace_id"`
	Context     map[string]interface{} `json:"context"`
	Options     map[string]interface{} `json:"options"`
}

// PlatformResponse 平台响应
type PlatformResponse struct {
	Success          bool                   `json:"success"`
	Message          string                 `json:"message"`
	DecisionResult   *DecisionResult        `json:"decision_result,omitempty"`
	ExecutionSession *ExecutionSession      `json:"execution_session,omitempty"`
	Metadata         map[string]interface{} `json:"metadata"`
	ProcessingTime   time.Duration          `json:"processing_time"`
	Timestamp        time.Time              `json:"timestamp"`
}

// NewAgentPlatform 创建Agent平台
func NewAgentPlatform(
	deepseekService interfaces.DeepSeekServiceInterface,
	hostService interfaces.HostServiceInterface,
	logger *logrus.Logger,
) *AgentPlatform {
	config := &PlatformConfig{
		EnableAutoRegistration: true,
		HealthCheckInterval:    30 * time.Second,
		MaxConcurrentRequests:  50,
		RequestTimeout:         5 * time.Minute,
		EnableMetrics:          true,
		EnableEventLogging:     true,
	}

	// 创建核心组件
	eventBus := NewEventBus(logger)
	registry := NewAgentRegistry(logger, DefaultRegistryConfig())
	decisionEngine := NewDecisionEngine(deepseekService, registry, logger)
	executionEngine := NewExecutionEngine(registry, eventBus, logger)

	platform := &AgentPlatform{
		registry:        registry,
		decisionEngine:  decisionEngine,
		executionEngine: executionEngine,
		eventBus:        eventBus,
		logger:          logger,
		config:          config,
		deepseekService: deepseekService,
		hostService:     hostService,
		status:          PlatformStatusStopped,
	}

	// 设置事件处理器
	platform.setupEventHandlers()

	return platform
}

// Initialize 初始化平台
func (ap *AgentPlatform) Initialize(ctx context.Context) error {
	ap.logger.Info("Initializing Agent Platform")
	ap.status = PlatformStatusInitializing

	// 注册内置Agent
	if err := ap.registerBuiltinAgents(ctx); err != nil {
		ap.status = PlatformStatusError
		return fmt.Errorf("failed to register builtin agents: %w", err)
	}

	ap.status = PlatformStatusRunning
	ap.startTime = time.Now()

	ap.logger.Info("Agent Platform initialized successfully")
	return nil
}

// Start 启动平台
func (ap *AgentPlatform) Start(ctx context.Context) error {
	if ap.status != PlatformStatusStopped {
		return fmt.Errorf("platform is not in stopped state: %s", ap.status)
	}

	ap.logger.Info("Starting Agent Platform")

	if err := ap.Initialize(ctx); err != nil {
		return err
	}

	ap.logger.Info("Agent Platform started successfully")
	return nil
}

// Stop 停止平台
func (ap *AgentPlatform) Stop(ctx context.Context) error {
	ap.logger.Info("Stopping Agent Platform")

	// 停止所有Agent
	agents := ap.registry.ListAgents()
	for _, registration := range agents {
		if agent, err := ap.registry.GetAgent(registration.Metadata.ID); err == nil {
			if err := agent.Stop(ctx); err != nil {
				ap.logger.WithError(err).Warnf("Failed to stop agent: %s", registration.Metadata.ID)
			}
		}
	}

	ap.status = PlatformStatusStopped
	ap.logger.Info("Agent Platform stopped")
	return nil
}

// ProcessRequest 处理用户请求
func (ap *AgentPlatform) ProcessRequest(ctx context.Context, request *PlatformRequest) (*PlatformResponse, error) {
	start := time.Now()

	ap.logger.WithFields(logrus.Fields{
		"session_id":   request.SessionID,
		"user_id":      request.UserID,
		"user_message": request.UserMessage,
	}).Info("Processing platform request")

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(ctx, ap.config.RequestTimeout)
	defer cancel()

	// 构建决策请求
	decisionRequest := &DecisionRequest{
		UserMessage: request.UserMessage,
		Context: &DecisionContext{
			SessionID: request.SessionID,
			UserID:    request.UserID,
			TraceID:   request.TraceID,
			Variables: request.Context,
		},
		Options: request.Options,
	}

	// 进行决策
	decisionResult, err := ap.decisionEngine.MakeDecision(ctx, decisionRequest)
	if err != nil {
		return &PlatformResponse{
			Success:        false,
			Message:        fmt.Sprintf("决策失败: %v", err),
			Metadata:       map[string]interface{}{"error": err.Error()},
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
		}, nil
	}

	// 执行决策
	executionContext := &ExecutionContext{
		SessionID: request.SessionID,
		UserID:    request.UserID,
		TraceID:   request.TraceID,
		Variables: request.Context,
		Metadata:  make(map[string]interface{}),
	}

	executionSession, err := ap.executionEngine.ExecuteDecision(ctx, decisionResult, executionContext)
	if err != nil {
		return &PlatformResponse{
			Success:        false,
			Message:        fmt.Sprintf("执行失败: %v", err),
			DecisionResult: decisionResult,
			Metadata:       map[string]interface{}{"error": err.Error()},
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
		}, nil
	}

	// 构建响应
	response := &PlatformResponse{
		Success:          true,
		Message:          ap.buildResponseMessage(decisionResult, executionSession),
		DecisionResult:   decisionResult,
		ExecutionSession: executionSession,
		Metadata: map[string]interface{}{
			"agents_selected":    len(decisionResult.Agents),
			"execution_strategy": decisionResult.ExecutionPlan.Strategy,
			"confidence":         decisionResult.Confidence,
		},
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}

	ap.logger.WithFields(logrus.Fields{
		"session_id":      request.SessionID,
		"success":         response.Success,
		"agents_selected": len(decisionResult.Agents),
		"processing_time": response.ProcessingTime,
	}).Info("Platform request processed")

	return response, nil
}

// buildResponseMessage 构建响应消息
func (ap *AgentPlatform) buildResponseMessage(decision *DecisionResult, session *ExecutionSession) string {
	if len(decision.Agents) == 1 {
		return fmt.Sprintf("🤖 **AI决策完成**\n\n%s\n\n**执行Agent**: %s\n**执行状态**: %s",
			decision.Explanation, decision.Agents[0].AgentID, session.Status)
	}

	return fmt.Sprintf("🤖 **AI决策完成**\n\n%s\n\n**协作Agent**: %d个\n**执行策略**: %s\n**执行状态**: %s",
		decision.Explanation, len(decision.Agents), decision.ExecutionPlan.Strategy, session.Status)
}

// registerBuiltinAgents 注册内置Agent
func (ap *AgentPlatform) registerBuiltinAgents(ctx context.Context) error {
	ap.logger.Info("Registering builtin agents")

	// 简化实现 - 暂时跳过具体的Agent注册
	// 在实际实现中，这里会注册各种具体的Agent
	// 例如：主机管理、系统监控、命令执行、网络诊断、日志分析、安全检查、备份恢复等

	ap.logger.Info("Builtin agents registration completed (simplified)")
	return nil
}

// setupEventHandlers 设置事件处理器
func (ap *AgentPlatform) setupEventHandlers() {
	if ap.config.EnableEventLogging {
		ap.eventBus.Subscribe(EventTypeAgentRegistered, LoggingEventHandler(ap.logger))
		ap.eventBus.Subscribe(EventTypeAgentUnregistered, LoggingEventHandler(ap.logger))
		ap.eventBus.Subscribe(EventTypeAgentStatusChanged, LoggingEventHandler(ap.logger))
		ap.eventBus.Subscribe(EventTypeAgentExecutionStart, LoggingEventHandler(ap.logger))
		ap.eventBus.Subscribe(EventTypeAgentExecutionEnd, LoggingEventHandler(ap.logger))
		ap.eventBus.Subscribe(EventTypeAgentError, AlertEventHandler(ap.logger))
	}

	if ap.config.EnableMetrics {
		ap.eventBus.Subscribe(EventTypeAgentRegistered, MetricsEventHandler())
		ap.eventBus.Subscribe(EventTypeAgentExecutionStart, MetricsEventHandler())
		ap.eventBus.Subscribe(EventTypeAgentExecutionEnd, MetricsEventHandler())
	}
}

// GetStatus 获取平台状态
func (ap *AgentPlatform) GetStatus() PlatformStatus {
	return ap.status
}

// GetStatistics 获取平台统计信息
func (ap *AgentPlatform) GetStatistics() map[string]interface{} {
	stats := map[string]interface{}{
		"platform_status": ap.status,
		"uptime":          time.Since(ap.startTime).String(),
		"registry_stats":  ap.registry.GetStatistics(),
		"execution_stats": ap.executionEngine.GetExecutionStatistics(),
	}

	if ap.status == PlatformStatusRunning {
		stats["decision_capabilities"] = ap.decisionEngine.GetDecisionCapabilities()
	}

	return stats
}

// GetAgentCapabilities 获取Agent能力信息（供前端展示）
func (ap *AgentPlatform) GetAgentCapabilities() map[string]interface{} {
	return ap.decisionEngine.GetDecisionCapabilities()
}

// RegisterAgent 注册新Agent
func (ap *AgentPlatform) RegisterAgent(ctx context.Context, agent Agent) error {
	return ap.registry.RegisterAgent(ctx, agent)
}

// UnregisterAgent 注销Agent
func (ap *AgentPlatform) UnregisterAgent(ctx context.Context, agentID string) error {
	return ap.registry.UnregisterAgent(ctx, agentID)
}

// GetExecutionSession 获取执行会话
func (ap *AgentPlatform) GetExecutionSession(sessionID string) (*ExecutionSession, error) {
	return ap.executionEngine.GetExecutionSession(sessionID)
}

// CancelExecution 取消执行
func (ap *AgentPlatform) CancelExecution(sessionID string) error {
	return ap.executionEngine.CancelExecution(sessionID)
}

// HealthCheck 平台健康检查
func (ap *AgentPlatform) HealthCheck(ctx context.Context) map[string]interface{} {
	health := map[string]interface{}{
		"platform_status": ap.status,
		"uptime":          time.Since(ap.startTime).String(),
		"components":      make(map[string]interface{}),
	}

	components := health["components"].(map[string]interface{})

	// 检查注册中心
	registryStats := ap.registry.GetStatistics()
	components["registry"] = map[string]interface{}{
		"status":       "healthy",
		"total_agents": registryStats["total_agents"],
	}

	// 检查决策引擎
	components["decision_engine"] = map[string]interface{}{
		"status": "healthy",
	}

	// 检查执行引擎
	executionStats := ap.executionEngine.GetExecutionStatistics()
	components["execution_engine"] = map[string]interface{}{
		"status":         "healthy",
		"total_sessions": executionStats["total_sessions"],
	}

	return health
}
