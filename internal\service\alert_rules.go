package service

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// AlertRuleManager 告警规则管理器
type AlertRuleManager struct {
	db     *gorm.DB
	logger *logrus.Logger
	rules  map[string]*AlertRule
	engine *AlertRuleEngine
}

// AlertRule 告警规则
type AlertRule struct {
	ID          string    `json:"id" gorm:"primaryKey"`
	Name        string    `json:"name" gorm:"unique"`
	Description string    `json:"description"`
	MetricType  string    `json:"metric_type" gorm:"index"`
	Condition   string    `json:"condition"` // >, <, >=, <=, ==, !=
	Threshold   float64   `json:"threshold"`
	Level       string    `json:"level" gorm:"index"` // info, warning, critical
	Duration    int       `json:"duration"`           // seconds
	Enabled     bool      `json:"enabled" gorm:"index"`
	Channels    string    `json:"channels" gorm:"type:text"` // JSON array
	Tags        string    `json:"tags" gorm:"type:text"`     // JSON object
	Metadata    string    `json:"metadata" gorm:"type:text"` // JSON object
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// AlertRuleEngine 告警规则引擎
type AlertRuleEngine struct {
	logger    *logrus.Logger
	evaluator *RuleEvaluator
}

// RuleEvaluator 规则评估器
type RuleEvaluator struct {
	logger *logrus.Logger
}

// RuleEvaluationResult 规则评估结果
type RuleEvaluationResult struct {
	RuleID    string    `json:"rule_id"`
	Matched   bool      `json:"matched"`
	Value     float64   `json:"value"`
	Threshold float64   `json:"threshold"`
	Level     string    `json:"level"`
	Message   string    `json:"message"`
	Timestamp time.Time `json:"timestamp"`
}

// AlertRuleCreateRequest 创建告警规则请求
type AlertRuleCreateRequest struct {
	Name        string                 `json:"name" binding:"required"`
	Description string                 `json:"description"`
	MetricType  string                 `json:"metric_type" binding:"required"`
	Condition   string                 `json:"condition" binding:"required"`
	Threshold   float64                `json:"threshold" binding:"required"`
	Level       string                 `json:"level" binding:"required"`
	Duration    int                    `json:"duration"`
	Channels    []string               `json:"channels"`
	Tags        map[string]string      `json:"tags"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// AlertRuleUpdateRequest 更新告警规则请求
type AlertRuleUpdateRequest struct {
	Name        *string                `json:"name"`
	Description *string                `json:"description"`
	MetricType  *string                `json:"metric_type"`
	Condition   *string                `json:"condition"`
	Threshold   *float64               `json:"threshold"`
	Level       *string                `json:"level"`
	Duration    *int                   `json:"duration"`
	Enabled     *bool                  `json:"enabled"`
	Channels    []string               `json:"channels"`
	Tags        map[string]string      `json:"tags"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// NewAlertRuleManager 创建告警规则管理器
func NewAlertRuleManager(db *gorm.DB, logger *logrus.Logger) *AlertRuleManager {
	manager := &AlertRuleManager{
		db:     db,
		logger: logger,
		rules:  make(map[string]*AlertRule),
		engine: &AlertRuleEngine{
			logger: logger,
			evaluator: &RuleEvaluator{
				logger: logger,
			},
		},
	}

	// 初始化数据库表
	manager.initializeDatabase()

	// 加载规则
	manager.loadRules()

	// 创建默认规则
	manager.createDefaultRules()

	return manager
}

// initializeDatabase 初始化数据库
func (arm *AlertRuleManager) initializeDatabase() error {
	return arm.db.AutoMigrate(&AlertRule{})
}

// loadRules 加载规则
func (arm *AlertRuleManager) loadRules() {
	var rules []*AlertRule
	if err := arm.db.Where("enabled = ?", true).Find(&rules).Error; err != nil {
		arm.logger.WithError(err).Error("Failed to load alert rules")
		return
	}

	for _, rule := range rules {
		arm.rules[rule.ID] = rule
	}

	arm.logger.WithField("rule_count", len(arm.rules)).Info("Alert rules loaded")
}

// createDefaultRules 创建默认规则
func (arm *AlertRuleManager) createDefaultRules() {
	defaultRules := []*AlertRuleCreateRequest{
		{
			Name:        "CPU使用率过高",
			Description: "CPU使用率超过80%时触发告警",
			MetricType:  "cpu_usage",
			Condition:   ">",
			Threshold:   80.0,
			Level:       "warning",
			Duration:    300, // 5分钟
			Channels:    []string{"websocket", "log"},
		},
		{
			Name:        "CPU使用率严重过高",
			Description: "CPU使用率超过95%时触发严重告警",
			MetricType:  "cpu_usage",
			Condition:   ">",
			Threshold:   95.0,
			Level:       "critical",
			Duration:    60, // 1分钟
			Channels:    []string{"websocket", "log", "email"},
		},
		{
			Name:        "内存使用率过高",
			Description: "内存使用率超过85%时触发告警",
			MetricType:  "memory_usage",
			Condition:   ">",
			Threshold:   85.0,
			Level:       "warning",
			Duration:    300,
			Channels:    []string{"websocket", "log"},
		},
		{
			Name:        "磁盘使用率过高",
			Description: "磁盘使用率超过90%时触发告警",
			MetricType:  "disk_usage",
			Condition:   ">",
			Threshold:   90.0,
			Level:       "critical",
			Duration:    600, // 10分钟
			Channels:    []string{"websocket", "log", "email"},
		},
		{
			Name:        "主机离线",
			Description: "主机连接断开时触发告警",
			MetricType:  "host_status",
			Condition:   "==",
			Threshold:   0, // 0表示离线
			Level:       "critical",
			Duration:    60,
			Channels:    []string{"websocket", "log", "email"},
		},
	}

	for _, req := range defaultRules {
		// 检查是否已存在
		var existing AlertRule
		if err := arm.db.Where("name = ?", req.Name).First(&existing).Error; err == gorm.ErrRecordNotFound {
			// 不存在，创建默认规则
			if _, err := arm.CreateRule(context.Background(), req); err != nil {
				arm.logger.WithError(err).WithField("rule_name", req.Name).Error("Failed to create default rule")
			}
		}
	}
}

// CreateRule 创建规则
func (arm *AlertRuleManager) CreateRule(ctx context.Context, req *AlertRuleCreateRequest) (*AlertRule, error) {
	// 验证条件
	if !arm.isValidCondition(req.Condition) {
		return nil, fmt.Errorf("invalid condition: %s", req.Condition)
	}

	// 验证级别
	if !arm.isValidLevel(req.Level) {
		return nil, fmt.Errorf("invalid level: %s", req.Level)
	}

	rule := &AlertRule{
		ID:          fmt.Sprintf("rule_%d", time.Now().Unix()),
		Name:        req.Name,
		Description: req.Description,
		MetricType:  req.MetricType,
		Condition:   req.Condition,
		Threshold:   req.Threshold,
		Level:       req.Level,
		Duration:    req.Duration,
		Enabled:     true,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	// 序列化channels
	if len(req.Channels) > 0 {
		rule.Channels = fmt.Sprintf(`["%s"]`, req.Channels[0]) // 简化处理
	}

	// 保存到数据库
	if err := arm.db.Create(rule).Error; err != nil {
		return nil, fmt.Errorf("failed to create rule: %w", err)
	}

	// 添加到内存
	arm.rules[rule.ID] = rule

	arm.logger.WithFields(logrus.Fields{
		"rule_id":     rule.ID,
		"rule_name":   rule.Name,
		"metric_type": rule.MetricType,
		"threshold":   rule.Threshold,
	}).Info("Alert rule created")

	return rule, nil
}

// UpdateRule 更新规则
func (arm *AlertRuleManager) UpdateRule(ctx context.Context, ruleID string, req *AlertRuleUpdateRequest) (*AlertRule, error) {
	rule, exists := arm.rules[ruleID]
	if !exists {
		return nil, fmt.Errorf("rule not found: %s", ruleID)
	}

	// 更新字段
	if req.Name != nil {
		rule.Name = *req.Name
	}
	if req.Description != nil {
		rule.Description = *req.Description
	}
	if req.MetricType != nil {
		rule.MetricType = *req.MetricType
	}
	if req.Condition != nil {
		if !arm.isValidCondition(*req.Condition) {
			return nil, fmt.Errorf("invalid condition: %s", *req.Condition)
		}
		rule.Condition = *req.Condition
	}
	if req.Threshold != nil {
		rule.Threshold = *req.Threshold
	}
	if req.Level != nil {
		if !arm.isValidLevel(*req.Level) {
			return nil, fmt.Errorf("invalid level: %s", *req.Level)
		}
		rule.Level = *req.Level
	}
	if req.Duration != nil {
		rule.Duration = *req.Duration
	}
	if req.Enabled != nil {
		rule.Enabled = *req.Enabled
	}

	rule.UpdatedAt = time.Now()

	// 保存到数据库
	if err := arm.db.Save(rule).Error; err != nil {
		return nil, fmt.Errorf("failed to update rule: %w", err)
	}

	arm.logger.WithField("rule_id", ruleID).Info("Alert rule updated")

	return rule, nil
}

// DeleteRule 删除规则
func (arm *AlertRuleManager) DeleteRule(ctx context.Context, ruleID string) error {
	if _, exists := arm.rules[ruleID]; !exists {
		return fmt.Errorf("rule not found: %s", ruleID)
	}

	// 从数据库删除
	if err := arm.db.Delete(&AlertRule{}, "id = ?", ruleID).Error; err != nil {
		return fmt.Errorf("failed to delete rule: %w", err)
	}

	// 从内存删除
	delete(arm.rules, ruleID)

	arm.logger.WithField("rule_id", ruleID).Info("Alert rule deleted")

	return nil
}

// GetRule 获取规则
func (arm *AlertRuleManager) GetRule(ruleID string) (*AlertRule, error) {
	rule, exists := arm.rules[ruleID]
	if !exists {
		return nil, fmt.Errorf("rule not found: %s", ruleID)
	}
	return rule, nil
}

// ListRules 列出规则
func (arm *AlertRuleManager) ListRules() []*AlertRule {
	rules := make([]*AlertRule, 0, len(arm.rules))
	for _, rule := range arm.rules {
		rules = append(rules, rule)
	}
	return rules
}

// EvaluateMetric 评估指标
func (arm *AlertRuleManager) EvaluateMetric(metric *MetricData) []*RuleEvaluationResult {
	results := make([]*RuleEvaluationResult, 0)

	for _, rule := range arm.rules {
		if !rule.Enabled || rule.MetricType != metric.MetricType {
			continue
		}

		result := arm.engine.evaluator.Evaluate(rule, metric)
		if result != nil {
			results = append(results, result)
		}
	}

	return results
}

// Evaluate 评估规则
func (re *RuleEvaluator) Evaluate(rule *AlertRule, metric *MetricData) *RuleEvaluationResult {
	matched := false
	value := metric.Value
	threshold := rule.Threshold

	switch rule.Condition {
	case ">":
		matched = value > threshold
	case ">=":
		matched = value >= threshold
	case "<":
		matched = value < threshold
	case "<=":
		matched = value <= threshold
	case "==":
		matched = value == threshold
	case "!=":
		matched = value != threshold
	default:
		re.logger.WithField("condition", rule.Condition).Warn("Unknown condition")
		return nil
	}

	if !matched {
		return nil
	}

	message := fmt.Sprintf("%s %s %.2f (threshold: %.2f)",
		rule.MetricType, rule.Condition, value, threshold)

	return &RuleEvaluationResult{
		RuleID:    rule.ID,
		Matched:   matched,
		Value:     value,
		Threshold: threshold,
		Level:     rule.Level,
		Message:   message,
		Timestamp: time.Now(),
	}
}

// isValidCondition 验证条件
func (arm *AlertRuleManager) isValidCondition(condition string) bool {
	validConditions := []string{">", ">=", "<", "<=", "==", "!="}
	for _, valid := range validConditions {
		if condition == valid {
			return true
		}
	}
	return false
}

// isValidLevel 验证级别
func (arm *AlertRuleManager) isValidLevel(level string) bool {
	validLevels := []string{"info", "warning", "critical"}
	for _, valid := range validLevels {
		if level == valid {
			return true
		}
	}
	return false
}

// GetRuleStatistics 获取规则统计
func (arm *AlertRuleManager) GetRuleStatistics() map[string]interface{} {
	stats := map[string]interface{}{
		"total_rules":   len(arm.rules),
		"enabled_rules": 0,
	}

	levelCounts := map[string]int{
		"info":     0,
		"warning":  0,
		"critical": 0,
	}

	for _, rule := range arm.rules {
		if rule.Enabled {
			stats["enabled_rules"] = stats["enabled_rules"].(int) + 1
		}
		levelCounts[rule.Level]++
	}

	stats["rules_by_level"] = levelCounts

	return stats
}
