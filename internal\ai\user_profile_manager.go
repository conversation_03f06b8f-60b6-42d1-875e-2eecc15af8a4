package ai

import (
	"fmt"
	"math"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// UserProfileManager 用户画像管理器
type UserProfileManager struct {
	db     *gorm.DB
	logger *logrus.Logger
	config *PersonalizationConfig
}

// NewUserProfileManager 创建用户画像管理器
func NewUserProfileManager(db *gorm.DB, logger *logrus.Logger, config *PersonalizationConfig) *UserProfileManager {
	return &UserProfileManager{
		db:     db,
		logger: logger,
		config: config,
	}
}

// CreateProfile 创建用户画像
func (upm *UserProfileManager) CreateProfile(userID int64) (*UserProfile, error) {
	profile := &UserProfile{
		UserID:           userID,
		CreatedAt:        time.Now(),
		LastUpdated:      time.Now(),
		InteractionCount: 0,
		LearningProgress: 0.0,
		PreferredModes:   make(map[InteractionMode]float64),
		ResponseStyle: ResponseStylePreference{
			Formality:    0.5,
			Verbosity:    0.5,
			Friendliness: 0.7,
			Directness:   0.6,
			Humor:        0.3,
			Empathy:      0.6,
			Patience:     0.7,
		},
		CommunicationStyle: CommunicationStylePreference{
			PreferredTone:       "friendly",
			PreferredPace:       "normal",
			PreferredComplexity: "medium",
			PreferredExamples:   true,
			PreferredSummary:    true,
			PreferredSteps:      true,
		},
		BehaviorPatterns:   make(map[string]*BehaviorPattern),
		TaskPreferences:    make(map[string]*TaskPreference),
		TechnicalLevel:     0.5,
		DomainExpertise:    make(map[string]float64),
		LearningSpeed:      0.5,
		CustomSettings:     make(map[string]interface{}),
		AccessibilityNeeds: []string{},
		LanguagePreference: "zh-CN",
		LearningState: &LearningState{
			Phase:              PhaseInitial,
			Progress:           0.0,
			LastLearningUpdate: time.Now(),
			LearningGoals:      []LearningGoal{},
			Achievements:       []Achievement{},
			LearningChallenges: []LearningChallenge{},
		},
		AdaptationHistory: []AdaptationRecord{},
		PrivacySettings: &PrivacySettings{
			DataCollection:   true,
			BehaviorTracking: true,
			ProfileSharing:   false,
			DataRetention:    90,
			AnonymousMode:    false,
			AllowedDataTypes: []string{"interaction", "preference", "behavior"},
		},
	}

	// 初始化默认偏好模态
	profile.PreferredModes[ModeText] = 1.0
	profile.PreferredModes[ModeSpeech] = 0.5
	profile.PreferredModes[ModeImage] = 0.3

	// 保存到数据库
	record := &UserProfileRecord{}
	if err := record.FromUserProfile(profile); err != nil {
		return nil, fmt.Errorf("failed to convert profile: %w", err)
	}

	if err := upm.db.Create(record).Error; err != nil {
		return nil, fmt.Errorf("failed to save profile: %w", err)
	}

	upm.logger.WithField("user_id", userID).Info("创建用户画像")
	return profile, nil
}

// LoadProfile 加载用户画像
func (upm *UserProfileManager) LoadProfile(userID int64) (*UserProfile, error) {
	var record UserProfileRecord
	if err := upm.db.Where("user_id = ?", userID).First(&record).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("profile not found for user %d", userID)
		}
		return nil, fmt.Errorf("failed to load profile: %w", err)
	}

	profile, err := record.ToUserProfile()
	if err != nil {
		return nil, fmt.Errorf("failed to convert profile: %w", err)
	}

	return profile, nil
}

// SaveProfile 保存用户画像
func (upm *UserProfileManager) SaveProfile(profile *UserProfile) error {
	record := &UserProfileRecord{}
	if err := record.FromUserProfile(profile); err != nil {
		return fmt.Errorf("failed to convert profile: %w", err)
	}

	if err := upm.db.Save(record).Error; err != nil {
		return fmt.Errorf("failed to save profile: %w", err)
	}

	return nil
}

// UpdateProfile 更新用户画像
func (upm *UserProfileManager) UpdateProfile(userID int64, updates map[string]interface{}) error {
	profile, err := upm.LoadProfile(userID)
	if err != nil {
		return err
	}

	// 应用更新
	for key, value := range updates {
		switch key {
		case "technical_level":
			if level, ok := value.(float64); ok {
				profile.TechnicalLevel = level
			}
		case "language_preference":
			if lang, ok := value.(string); ok {
				profile.LanguagePreference = lang
			}
		case "accessibility_needs":
			if needs, ok := value.([]string); ok {
				profile.AccessibilityNeeds = needs
			}
		default:
			profile.CustomSettings[key] = value
		}
	}

	profile.LastUpdated = time.Now()
	return upm.SaveProfile(profile)
}

// DeleteProfile 删除用户画像
func (upm *UserProfileManager) DeleteProfile(userID int64) error {
	if err := upm.db.Where("user_id = ?", userID).Delete(&UserProfileRecord{}).Error; err != nil {
		return fmt.Errorf("failed to delete profile: %w", err)
	}

	upm.logger.WithField("user_id", userID).Info("删除用户画像")
	return nil
}

// BehaviorAnalyzer 行为分析器
type BehaviorAnalyzer struct {
	logger *logrus.Logger
	config *PersonalizationConfig
}

// NewBehaviorAnalyzer 创建行为分析器
func NewBehaviorAnalyzer(logger *logrus.Logger, config *PersonalizationConfig) *BehaviorAnalyzer {
	return &BehaviorAnalyzer{
		logger: logger,
		config: config,
	}
}

// AnalyzeBehavior 分析用户行为
func (ba *BehaviorAnalyzer) AnalyzeBehavior(interaction *InteractionData, profile *UserProfile) (*BehaviorInsights, error) {
	ba.logger.WithFields(logrus.Fields{
		"user_id":        profile.UserID,
		"interaction_id": interaction.ID,
		"type":           interaction.Type,
	}).Debug("分析用户行为")

	insights := &BehaviorInsights{
		Patterns: []BehaviorPattern{},
	}

	// 分析交互模式
	if pattern := ba.analyzeInteractionPattern(interaction, profile); pattern != nil {
		insights.Patterns = append(insights.Patterns, *pattern)
	}

	// 分析时间模式
	if pattern := ba.analyzeTimePattern(interaction, profile); pattern != nil {
		insights.Patterns = append(insights.Patterns, *pattern)
	}

	// 分析任务模式
	if pattern := ba.analyzeTaskPattern(interaction, profile); pattern != nil {
		insights.Patterns = append(insights.Patterns, *pattern)
	}

	// 分析错误模式
	if pattern := ba.analyzeErrorPattern(interaction, profile); pattern != nil {
		insights.Patterns = append(insights.Patterns, *pattern)
	}

	return insights, nil
}

func (ba *BehaviorAnalyzer) analyzeInteractionPattern(interaction *InteractionData, profile *UserProfile) *BehaviorPattern {
	// 分析交互模式偏好
	modeName := string(interaction.Mode)
	patternName := fmt.Sprintf("prefers_%s_interaction", modeName)

	// 计算频率（基于历史使用）
	frequency := 0.1 // 基础频率
	if existing, exists := profile.BehaviorPatterns[patternName]; exists {
		frequency = existing.Frequency + 0.1
	}

	return &BehaviorPattern{
		PatternName:  patternName,
		Frequency:    frequency,
		Confidence:   0.8,
		LastObserved: time.Now(),
		Triggers:     []string{"user_interaction"},
		Outcomes:     []string{"mode_preference_update"},
		Context: map[string]interface{}{
			"interaction_mode": modeName,
			"success":          interaction.Success,
		},
	}
}

func (ba *BehaviorAnalyzer) analyzeTimePattern(interaction *InteractionData, profile *UserProfile) *BehaviorPattern {
	// 分析时间使用模式
	hour := interaction.Timestamp.Hour()
	var timeCategory string

	switch {
	case hour >= 6 && hour < 12:
		timeCategory = "morning"
	case hour >= 12 && hour < 18:
		timeCategory = "afternoon"
	case hour >= 18 && hour < 22:
		timeCategory = "evening"
	default:
		timeCategory = "night"
	}

	patternName := fmt.Sprintf("active_during_%s", timeCategory)

	frequency := 0.1
	if existing, exists := profile.BehaviorPatterns[patternName]; exists {
		frequency = existing.Frequency + 0.05
	}

	return &BehaviorPattern{
		PatternName:  patternName,
		Frequency:    frequency,
		Confidence:   0.7,
		LastObserved: time.Now(),
		Triggers:     []string{"time_based"},
		Outcomes:     []string{"usage_pattern_update"},
		Context: map[string]interface{}{
			"time_category": timeCategory,
			"hour":          hour,
		},
	}
}

func (ba *BehaviorAnalyzer) analyzeTaskPattern(interaction *InteractionData, profile *UserProfile) *BehaviorPattern {
	// 分析任务执行模式
	taskType := ba.extractTaskType(interaction)
	if taskType == "" {
		return nil
	}

	patternName := fmt.Sprintf("frequently_performs_%s", taskType)

	frequency := 0.1
	if existing, exists := profile.BehaviorPatterns[patternName]; exists {
		frequency = existing.Frequency + 0.1
	}

	return &BehaviorPattern{
		PatternName:  patternName,
		Frequency:    frequency,
		Confidence:   0.8,
		LastObserved: time.Now(),
		Triggers:     []string{"task_execution"},
		Outcomes:     []string{"task_preference_update"},
		Context: map[string]interface{}{
			"task_type": taskType,
			"success":   interaction.Success,
			"duration":  interaction.Duration,
		},
	}
}

func (ba *BehaviorAnalyzer) analyzeErrorPattern(interaction *InteractionData, profile *UserProfile) *BehaviorPattern {
	if interaction.Success {
		return nil // 只分析失败的交互
	}

	patternName := "encounters_errors"

	frequency := 0.05
	if existing, exists := profile.BehaviorPatterns[patternName]; exists {
		frequency = existing.Frequency + 0.05
	}

	return &BehaviorPattern{
		PatternName:  patternName,
		Frequency:    frequency,
		Confidence:   0.9,
		LastObserved: time.Now(),
		Triggers:     []string{"interaction_failure"},
		Outcomes:     []string{"support_need_identified"},
		Context: map[string]interface{}{
			"interaction_type": interaction.Type,
			"error_context":    interaction.Context,
		},
	}
}

func (ba *BehaviorAnalyzer) extractTaskType(interaction *InteractionData) string {
	// 从交互内容中提取任务类型
	content := interaction.Content

	// 简化的任务类型识别
	taskKeywords := map[string]string{
		"查看": "query",
		"显示": "display",
		"添加": "add",
		"删除": "delete",
		"修改": "modify",
		"配置": "config",
		"监控": "monitor",
		"告警": "alert",
		"报表": "report",
		"状态": "status",
	}

	for keyword, taskType := range taskKeywords {
		if contains(content, keyword) {
			return taskType
		}
	}

	return ""
}

// PreferencePredictor 偏好预测器
type PreferencePredictor struct {
	logger *logrus.Logger
	config *PersonalizationConfig
}

// NewPreferencePredictor 创建偏好预测器
func NewPreferencePredictor(logger *logrus.Logger, config *PersonalizationConfig) *PreferencePredictor {
	return &PreferencePredictor{
		logger: logger,
		config: config,
	}
}

// PredictPreferences 预测用户偏好
func (pp *PreferencePredictor) PredictPreferences(interaction *InteractionData, profile *UserProfile) (*PreferenceUpdates, error) {
	pp.logger.WithFields(logrus.Fields{
		"user_id":        profile.UserID,
		"interaction_id": interaction.ID,
	}).Debug("预测用户偏好")

	updates := &PreferenceUpdates{
		ModePreferences: make(map[InteractionMode]float64),
	}

	// 预测响应风格偏好
	if styleUpdate := pp.predictResponseStyle(interaction, profile); styleUpdate != nil {
		updates.ResponseStyle = styleUpdate
	}

	// 预测沟通风格偏好
	if commUpdate := pp.predictCommunicationStyle(interaction, profile); commUpdate != nil {
		updates.CommunicationStyle = commUpdate
	}

	// 预测模态偏好
	updates.ModePreferences = pp.predictModePreferences(interaction, profile)

	return updates, nil
}

func (pp *PreferencePredictor) predictResponseStyle(interaction *InteractionData, profile *UserProfile) *ResponseStylePreference {
	// 基于交互反馈预测响应风格偏好
	if interaction.Feedback == nil {
		return nil
	}

	current := profile.ResponseStyle
	adjustment := 0.1

	// 根据用户反馈调整
	if interaction.Feedback.Rating >= 4.0 {
		// 高评分，保持当前风格
		return nil
	} else if interaction.Feedback.Rating <= 2.0 {
		// 低评分，调整风格
		return &ResponseStylePreference{
			Formality:    current.Formality + adjustment,
			Verbosity:    current.Verbosity - adjustment,
			Friendliness: current.Friendliness + adjustment,
			Directness:   current.Directness + adjustment,
			Humor:        current.Humor - adjustment,
			Empathy:      current.Empathy + adjustment,
			Patience:     current.Patience + adjustment,
		}
	}

	return nil
}

func (pp *PreferencePredictor) predictCommunicationStyle(interaction *InteractionData, profile *UserProfile) *CommunicationStylePreference {
	// 基于交互模式预测沟通风格
	current := profile.CommunicationStyle

	// 根据交互时长调整节奏偏好
	if interaction.Duration > 5*time.Minute {
		current.PreferredPace = "slow"
	} else if interaction.Duration < 1*time.Minute {
		current.PreferredPace = "fast"
	}

	// 根据交互成功率调整复杂度偏好
	if !interaction.Success {
		current.PreferredComplexity = "simple"
		current.PreferredExamples = true
		current.PreferredSteps = true
	}

	return &current
}

func (pp *PreferencePredictor) predictModePreferences(interaction *InteractionData, profile *UserProfile) map[InteractionMode]float64 {
	preferences := make(map[InteractionMode]float64)

	// 基于交互成功率调整模态偏好
	mode := interaction.Mode
	adjustment := 0.05

	if interaction.Success {
		// 成功的交互增加该模态的偏好
		preferences[mode] = adjustment
	} else {
		// 失败的交互降低该模态的偏好
		preferences[mode] = -adjustment
	}

	return preferences
}

// AdaptationController 适应控制器
type AdaptationController struct {
	logger *logrus.Logger
	config *PersonalizationConfig
}

// NewAdaptationController 创建适应控制器
func NewAdaptationController(logger *logrus.Logger, config *PersonalizationConfig) *AdaptationController {
	return &AdaptationController{
		logger: logger,
		config: config,
	}
}

// GenerateAdaptations 生成适应策略
func (ac *AdaptationController) GenerateAdaptations(profile *UserProfile, interaction *InteractionData) ([]Adaptation, error) {
	ac.logger.WithFields(logrus.Fields{
		"user_id":        profile.UserID,
		"learning_phase": profile.LearningState.Phase,
	}).Debug("生成适应策略")

	adaptations := []Adaptation{}

	// 基于学习阶段生成适应
	switch profile.LearningState.Phase {
	case PhaseObservation:
		adaptations = append(adaptations, ac.generateObservationAdaptations(profile, interaction)...)
	case PhaseLearning:
		adaptations = append(adaptations, ac.generateLearningAdaptations(profile, interaction)...)
	case PhaseAdaptation:
		adaptations = append(adaptations, ac.generateAdaptationAdaptations(profile, interaction)...)
	case PhaseOptimized:
		adaptations = append(adaptations, ac.generateOptimizationAdaptations(profile, interaction)...)
	}

	return adaptations, nil
}

func (ac *AdaptationController) generateObservationAdaptations(profile *UserProfile, interaction *InteractionData) []Adaptation {
	// 观察阶段的适应：主要收集数据，轻微调整
	adaptations := []Adaptation{}

	// 模态偏好适应
	if adaptation := ac.createModePreferenceAdaptation(profile, interaction); adaptation != nil {
		adaptations = append(adaptations, *adaptation)
	}

	return adaptations
}

func (ac *AdaptationController) generateLearningAdaptations(profile *UserProfile, interaction *InteractionData) []Adaptation {
	// 学习阶段的适应：基于模式进行调整
	adaptations := []Adaptation{}

	// 响应风格适应
	if adaptation := ac.createResponseStyleAdaptation(profile, interaction); adaptation != nil {
		adaptations = append(adaptations, *adaptation)
	}

	// 沟通风格适应
	if adaptation := ac.createCommunicationStyleAdaptation(profile, interaction); adaptation != nil {
		adaptations = append(adaptations, *adaptation)
	}

	return adaptations
}

func (ac *AdaptationController) generateAdaptationAdaptations(profile *UserProfile, interaction *InteractionData) []Adaptation {
	// 适应阶段的适应：精细调整
	return ac.generateLearningAdaptations(profile, interaction)
}

func (ac *AdaptationController) generateOptimizationAdaptations(profile *UserProfile, interaction *InteractionData) []Adaptation {
	// 优化阶段的适应：微调和优化
	adaptations := []Adaptation{}

	// 只在必要时进行微调
	if interaction.Feedback != nil && interaction.Feedback.Rating < 3.0 {
		adaptations = append(adaptations, ac.generateLearningAdaptations(profile, interaction)...)
	}

	return adaptations
}

func (ac *AdaptationController) createModePreferenceAdaptation(profile *UserProfile, interaction *InteractionData) *Adaptation {
	mode := interaction.Mode
	current := profile.PreferredModes[mode]

	var newValue float64
	if interaction.Success {
		newValue = current + 0.05
	} else {
		newValue = current - 0.05
	}

	// 限制在0-1范围内
	newValue = math.Max(0.0, math.Min(1.0, newValue))

	if math.Abs(newValue-current) < 0.01 {
		return nil // 变化太小，不需要适应
	}

	return &Adaptation{
		Type: "mode_preference",
		Before: map[string]interface{}{
			"mode_preferences": profile.PreferredModes,
		},
		After: map[string]interface{}{
			"mode_preferences": map[InteractionMode]float64{
				mode: newValue,
			},
		},
		Reason:     fmt.Sprintf("Interaction %s with mode %s", map[bool]string{true: "succeeded", false: "failed"}[interaction.Success], mode),
		Confidence: 0.7,
	}
}

func (ac *AdaptationController) createResponseStyleAdaptation(profile *UserProfile, interaction *InteractionData) *Adaptation {
	if interaction.Feedback == nil || interaction.Feedback.Rating >= 3.0 {
		return nil
	}

	current := profile.ResponseStyle
	adjusted := current

	// 根据反馈调整
	if interaction.Feedback.Rating < 2.0 {
		adjusted.Friendliness += 0.1
		adjusted.Patience += 0.1
		adjusted.Empathy += 0.1
	}

	return &Adaptation{
		Type: "response_style",
		Before: map[string]interface{}{
			"response_style": current,
		},
		After: map[string]interface{}{
			"response_style": adjusted,
		},
		Reason:     "Low user satisfaction, adjusting response style",
		Confidence: 0.8,
	}
}

func (ac *AdaptationController) createCommunicationStyleAdaptation(profile *UserProfile, interaction *InteractionData) *Adaptation {
	if interaction.Success {
		return nil
	}

	current := profile.CommunicationStyle
	adjusted := current

	// 失败时简化沟通风格
	adjusted.PreferredComplexity = "simple"
	adjusted.PreferredExamples = true
	adjusted.PreferredSteps = true

	return &Adaptation{
		Type: "communication_style",
		Before: map[string]interface{}{
			"communication_style": current,
		},
		After: map[string]interface{}{
			"communication_style": adjusted,
		},
		Reason:     "Interaction failed, simplifying communication style",
		Confidence: 0.6,
	}
}

// LearningOptimizer 学习优化器
type LearningOptimizer struct {
	logger *logrus.Logger
	config *PersonalizationConfig
}

// NewLearningOptimizer 创建学习优化器
func NewLearningOptimizer(logger *logrus.Logger, config *PersonalizationConfig) *LearningOptimizer {
	return &LearningOptimizer{
		logger: logger,
		config: config,
	}
}

// OptimizeLearning 优化学习过程
func (lo *LearningOptimizer) OptimizeLearning(profile *UserProfile, interaction *InteractionData) {
	lo.logger.WithFields(logrus.Fields{
		"user_id": profile.UserID,
		"phase":   profile.LearningState.Phase,
	}).Debug("优化学习过程")

	// 调整学习速度
	lo.adjustLearningSpeed(profile, interaction)

	// 更新学习目标
	lo.updateLearningGoals(profile, interaction)

	// 检查成就
	lo.checkAchievements(profile, interaction)
}

func (lo *LearningOptimizer) adjustLearningSpeed(profile *UserProfile, interaction *InteractionData) {
	// 根据交互成功率调整学习速度
	if interaction.Success {
		profile.LearningSpeed = math.Min(1.0, profile.LearningSpeed+0.01)
	} else {
		profile.LearningSpeed = math.Max(0.1, profile.LearningSpeed-0.01)
	}
}

func (lo *LearningOptimizer) updateLearningGoals(profile *UserProfile, interaction *InteractionData) {
	// 根据用户行为更新学习目标
	// 简化实现：检查是否需要添加新目标

	if len(profile.LearningState.LearningGoals) < 3 {
		goal := LearningGoal{
			ID:          fmt.Sprintf("goal_%d", time.Now().Unix()),
			Description: "提高交互效率",
			Progress:    0.0,
			Deadline:    time.Now().Add(30 * 24 * time.Hour),
			Priority:    1,
		}
		profile.LearningState.LearningGoals = append(profile.LearningState.LearningGoals, goal)
	}
}

func (lo *LearningOptimizer) checkAchievements(profile *UserProfile, interaction *InteractionData) {
	// 检查是否解锁新成就

	// 交互次数成就
	if profile.InteractionCount == 10 {
		achievement := Achievement{
			ID:          "first_10_interactions",
			Name:        "初学者",
			Description: "完成前10次交互",
			UnlockedAt:  time.Now(),
			Category:    "interaction",
		}
		profile.LearningState.Achievements = append(profile.LearningState.Achievements, achievement)
	}

	// 学习阶段成就
	if profile.LearningState.Phase == PhaseOptimized {
		achievement := Achievement{
			ID:          "learning_optimized",
			Name:        "学习大师",
			Description: "达到优化学习阶段",
			UnlockedAt:  time.Now(),
			Category:    "learning",
		}
		profile.LearningState.Achievements = append(profile.LearningState.Achievements, achievement)
	}
}

// PerformPeriodicUpdate 执行定期更新
func (lo *LearningOptimizer) PerformPeriodicUpdate(profile *UserProfile) {
	// 定期优化学习过程

	// 更新学习目标进度
	for i := range profile.LearningState.LearningGoals {
		goal := &profile.LearningState.LearningGoals[i]
		goal.Progress = math.Min(1.0, goal.Progress+0.1)
	}

	// 清理过期的行为模式
	lo.cleanupExpiredPatterns(profile)
}

func (lo *LearningOptimizer) cleanupExpiredPatterns(profile *UserProfile) {
	// 清理超过30天未观察到的行为模式
	cutoff := time.Now().Add(-30 * 24 * time.Hour)

	for name, pattern := range profile.BehaviorPatterns {
		if pattern.LastObserved.Before(cutoff) {
			delete(profile.BehaviorPatterns, name)
		}
	}
}

// PrivacyManager 隐私管理器
type PrivacyManager struct {
	logger *logrus.Logger
	config *PersonalizationConfig
}

// NewPrivacyManager 创建隐私管理器
func NewPrivacyManager(logger *logrus.Logger, config *PersonalizationConfig) *PrivacyManager {
	return &PrivacyManager{
		logger: logger,
		config: config,
	}
}

// CheckPrivacyCompliance 检查隐私合规性
func (pm *PrivacyManager) CheckPrivacyCompliance(profile *UserProfile, dataType string) bool {
	if profile.PrivacySettings.AnonymousMode {
		return false // 匿名模式下不收集数据
	}

	if !profile.PrivacySettings.DataCollection {
		return false // 用户禁用数据收集
	}

	// 检查数据类型是否被允许
	for _, allowedType := range profile.PrivacySettings.AllowedDataTypes {
		if allowedType == dataType {
			return true
		}
	}

	return false
}

// AnonymizeData 匿名化数据
func (pm *PrivacyManager) AnonymizeData(data interface{}) interface{} {
	// 简化实现：移除敏感信息
	if dataMap, ok := data.(map[string]interface{}); ok {
		anonymized := make(map[string]interface{})
		for key, value := range dataMap {
			if !pm.isSensitiveField(key) {
				anonymized[key] = value
			}
		}
		return anonymized
	}

	return data
}

func (pm *PrivacyManager) isSensitiveField(field string) bool {
	sensitiveFields := []string{"user_id", "ip_address", "location", "device_id"}
	for _, sensitive := range sensitiveFields {
		if field == sensitive {
			return true
		}
	}
	return false
}
