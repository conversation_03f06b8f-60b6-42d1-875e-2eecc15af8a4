package health

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/shirou/gopsutil/v3/cpu"
	"github.com/shirou/gopsutil/v3/disk"
	"github.com/shirou/gopsutil/v3/mem"
	"github.com/shirou/gopsutil/v3/net"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// HealthChecker 健康检查器
type HealthChecker struct {
	db           *gorm.DB
	logger       *logrus.Logger
	checkers     map[string]Checker
	mu           sync.RWMutex
	startTime    time.Time
	lastCheck    time.Time
	checkResults map[string]*CheckResult
}

// Checker 检查器接口
type Checker interface {
	Name() string
	Check(ctx context.Context) *CheckResult
}

// CheckResult 检查结果
type CheckResult struct {
	Name      string        `json:"name"`
	Status    string        `json:"status"`
	Message   string        `json:"message,omitempty"`
	Latency   time.Duration `json:"latency"`
	Timestamp time.Time     `json:"timestamp"`
	Details   interface{}   `json:"details,omitempty"`
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(db *gorm.DB, logger *logrus.Logger) *HealthChecker {
	hc := &HealthChecker{
		db:           db,
		logger:       logger,
		checkers:     make(map[string]Checker),
		startTime:    time.Now(),
		checkResults: make(map[string]*CheckResult),
	}

	// 注册默认检查器
	hc.RegisterChecker(&DatabaseChecker{db: db})
	hc.RegisterChecker(&SystemChecker{})
	hc.RegisterChecker(&MemoryChecker{})
	hc.RegisterChecker(&DiskChecker{})

	return hc
}

// RegisterChecker 注册检查器
func (hc *HealthChecker) RegisterChecker(checker Checker) {
	hc.mu.Lock()
	defer hc.mu.Unlock()
	hc.checkers[checker.Name()] = checker
}

// UnregisterChecker 注销检查器
func (hc *HealthChecker) UnregisterChecker(name string) {
	hc.mu.Lock()
	defer hc.mu.Unlock()
	delete(hc.checkers, name)
}

// CheckAll 执行所有健康检查
func (hc *HealthChecker) CheckAll(ctx context.Context) *model.HealthCheckResponse {
	hc.mu.Lock()
	defer hc.mu.Unlock()

	checks := make(map[string]model.HealthCheck)
	overallStatus := "healthy"

	// 并发执行所有检查
	var wg sync.WaitGroup
	resultChan := make(chan *CheckResult, len(hc.checkers))

	for _, checker := range hc.checkers {
		wg.Add(1)
		go func(c Checker) {
			defer wg.Done()
			result := c.Check(ctx)
			resultChan <- result
		}(checker)
	}

	// 等待所有检查完成
	go func() {
		wg.Wait()
		close(resultChan)
	}()

	// 收集结果
	for result := range resultChan {
		hc.checkResults[result.Name] = result

		checks[result.Name] = model.HealthCheck{
			Status:  result.Status,
			Message: result.Message,
			Latency: result.Latency.String(),
		}

		// 如果任何检查失败，整体状态为不健康
		if result.Status != "healthy" {
			overallStatus = "degraded"
		}
	}

	hc.lastCheck = time.Now()

	return &model.HealthCheckResponse{
		Status:    overallStatus,
		Timestamp: time.Now().Unix(),
		Version:   "1.0.0", // TODO: 从配置获取
		Uptime:    time.Since(hc.startTime).Milliseconds(),
		Checks:    checks,
	}
}

// GetMetrics 获取系统指标
func (hc *HealthChecker) GetMetrics(ctx context.Context) (*model.SystemMetrics, error) {
	metrics := &model.SystemMetrics{
		Timestamp:  time.Now().Unix(),
		Goroutines: runtime.NumGoroutine(),
	}

	// CPU指标
	if cpuPercent, err := cpu.PercentWithContext(ctx, time.Second, false); err == nil && len(cpuPercent) > 0 {
		metrics.CPU.Usage = cpuPercent[0]
	}

	// LoadAvg在Windows上不可用，跳过
	// if loadAvg, err := cpu.LoadAvgWithContext(ctx); err == nil {
	// 	metrics.CPU.LoadAvg1 = loadAvg.Load1
	// 	metrics.CPU.LoadAvg5 = loadAvg.Load5
	// 	metrics.CPU.LoadAvg15 = loadAvg.Load15
	// }

	// 内存指标
	if memInfo, err := mem.VirtualMemoryWithContext(ctx); err == nil {
		metrics.Memory.Total = memInfo.Total
		metrics.Memory.Used = memInfo.Used
		metrics.Memory.Free = memInfo.Free
		metrics.Memory.Usage = memInfo.UsedPercent
	}

	if swapInfo, err := mem.SwapMemoryWithContext(ctx); err == nil {
		metrics.Memory.SwapTotal = swapInfo.Total
		metrics.Memory.SwapUsed = swapInfo.Used
	}

	// 磁盘指标
	if diskInfo, err := disk.UsageWithContext(ctx, "/"); err == nil {
		metrics.Disk.Total = diskInfo.Total
		metrics.Disk.Used = diskInfo.Used
		metrics.Disk.Free = diskInfo.Free
		metrics.Disk.Usage = diskInfo.UsedPercent
	}

	// 网络指标
	if netStats, err := net.IOCountersWithContext(ctx, false); err == nil && len(netStats) > 0 {
		metrics.Network.BytesSent = netStats[0].BytesSent
		metrics.Network.BytesRecv = netStats[0].BytesRecv
		metrics.Network.PacketsSent = netStats[0].PacketsSent
		metrics.Network.PacketsRecv = netStats[0].PacketsRecv
	}

	// 数据库指标
	if hc.db != nil {
		if sqlDB, err := hc.db.DB(); err == nil {
			stats := sqlDB.Stats()
			metrics.Database.Connections = stats.OpenConnections
			metrics.Database.MaxConnections = stats.MaxOpenConnections
			metrics.Database.IdleConnections = stats.Idle
		}
	}

	return metrics, nil
}

// DatabaseChecker 数据库检查器
type DatabaseChecker struct {
	db *gorm.DB
}

func (c *DatabaseChecker) Name() string {
	return "database"
}

func (c *DatabaseChecker) Check(ctx context.Context) *CheckResult {
	start := time.Now()
	result := &CheckResult{
		Name:      c.Name(),
		Timestamp: start,
	}

	if c.db == nil {
		result.Status = "error"
		result.Message = "Database connection is nil"
		result.Latency = time.Since(start)
		return result
	}

	// 执行简单查询测试连接
	var count int64
	if err := c.db.WithContext(ctx).Raw("SELECT 1").Count(&count).Error; err != nil {
		result.Status = "error"
		result.Message = fmt.Sprintf("Database query failed: %v", err)
	} else {
		result.Status = "healthy"
		result.Message = "Database connection is healthy"

		// 获取数据库统计信息
		if sqlDB, err := c.db.DB(); err == nil {
			stats := sqlDB.Stats()
			result.Details = map[string]interface{}{
				"open_connections": stats.OpenConnections,
				"max_connections":  stats.MaxOpenConnections,
				"idle_connections": stats.Idle,
			}
		}
	}

	result.Latency = time.Since(start)
	return result
}

// SystemChecker 系统检查器
type SystemChecker struct{}

func (c *SystemChecker) Name() string {
	return "system"
}

func (c *SystemChecker) Check(ctx context.Context) *CheckResult {
	start := time.Now()
	result := &CheckResult{
		Name:      c.Name(),
		Status:    "healthy",
		Message:   "System is running normally",
		Timestamp: start,
	}

	// 检查Goroutine数量
	numGoroutines := runtime.NumGoroutine()
	if numGoroutines > 1000 { // 阈值可配置
		result.Status = "warning"
		result.Message = fmt.Sprintf("High number of goroutines: %d", numGoroutines)
	}

	result.Details = map[string]interface{}{
		"goroutines":   numGoroutines,
		"go_version":   runtime.Version(),
		"num_cpu":      runtime.NumCPU(),
		"num_cgo_call": runtime.NumCgoCall(),
	}

	result.Latency = time.Since(start)
	return result
}

// MemoryChecker 内存检查器
type MemoryChecker struct{}

func (c *MemoryChecker) Name() string {
	return "memory"
}

func (c *MemoryChecker) Check(ctx context.Context) *CheckResult {
	start := time.Now()
	result := &CheckResult{
		Name:      c.Name(),
		Status:    "healthy",
		Timestamp: start,
	}

	// 获取内存信息
	memInfo, err := mem.VirtualMemoryWithContext(ctx)
	if err != nil {
		result.Status = "error"
		result.Message = fmt.Sprintf("Failed to get memory info: %v", err)
		result.Latency = time.Since(start)
		return result
	}

	// 检查内存使用率
	if memInfo.UsedPercent > 90 {
		result.Status = "error"
		result.Message = fmt.Sprintf("High memory usage: %.2f%%", memInfo.UsedPercent)
	} else if memInfo.UsedPercent > 80 {
		result.Status = "warning"
		result.Message = fmt.Sprintf("Memory usage warning: %.2f%%", memInfo.UsedPercent)
	} else {
		result.Message = fmt.Sprintf("Memory usage: %.2f%%", memInfo.UsedPercent)
	}

	result.Details = map[string]interface{}{
		"total":        memInfo.Total,
		"used":         memInfo.Used,
		"free":         memInfo.Free,
		"used_percent": memInfo.UsedPercent,
	}

	result.Latency = time.Since(start)
	return result
}

// DiskChecker 磁盘检查器
type DiskChecker struct{}

func (c *DiskChecker) Name() string {
	return "disk"
}

func (c *DiskChecker) Check(ctx context.Context) *CheckResult {
	start := time.Now()
	result := &CheckResult{
		Name:      c.Name(),
		Status:    "healthy",
		Timestamp: start,
	}

	// 获取磁盘信息
	diskInfo, err := disk.UsageWithContext(ctx, "/")
	if err != nil {
		result.Status = "error"
		result.Message = fmt.Sprintf("Failed to get disk info: %v", err)
		result.Latency = time.Since(start)
		return result
	}

	// 检查磁盘使用率
	if diskInfo.UsedPercent > 95 {
		result.Status = "error"
		result.Message = fmt.Sprintf("Critical disk usage: %.2f%%", diskInfo.UsedPercent)
	} else if diskInfo.UsedPercent > 85 {
		result.Status = "warning"
		result.Message = fmt.Sprintf("High disk usage: %.2f%%", diskInfo.UsedPercent)
	} else {
		result.Message = fmt.Sprintf("Disk usage: %.2f%%", diskInfo.UsedPercent)
	}

	result.Details = map[string]interface{}{
		"total":        diskInfo.Total,
		"used":         diskInfo.Used,
		"free":         diskInfo.Free,
		"used_percent": diskInfo.UsedPercent,
	}

	result.Latency = time.Since(start)
	return result
}
