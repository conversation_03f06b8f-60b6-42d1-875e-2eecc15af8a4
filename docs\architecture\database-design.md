# 数据库设计方案

## 📊 数据库概述

采用SQLite作为主数据库，轻量级、高性能、零配置，适合单体应用部署。

### 设计原则
- **数据完整性**：外键约束、非空约束、唯一约束
- **安全性**：敏感数据加密存储、密码哈希
- **可扩展性**：JSON字段存储元数据、预留扩展字段
- **审计性**：创建时间、更新时间、操作日志

## 🗃️ 核心数据表

### 1. 用户表 (users)
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role VARCHAR(20) NOT NULL DEFAULT 'viewer',
    is_active BOOLEAN NOT NULL DEFAULT true,
    avatar_url VARCHAR(255),
    phone VARCHAR(20),
    department VARCHAR(50),
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_login DATETIME,
    login_count INTEGER DEFAULT 0,
    failed_login_count INTEGER DEFAULT 0,
    locked_until DATETIME,
    password_changed_at DATETIME,
    two_factor_enabled BOOLEAN DEFAULT false,
    two_factor_secret VARCHAR(32)
);

-- 索引
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_active ON users(is_active);
```

### 2. 主机表 (hosts)
```sql
CREATE TABLE hosts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    port INTEGER NOT NULL DEFAULT 22,
    username VARCHAR(50) NOT NULL,
    password_encrypted TEXT,
    ssh_key_path VARCHAR(255),
    ssh_key_passphrase_encrypted TEXT,
    description TEXT,
    status VARCHAR(20) NOT NULL DEFAULT 'unknown',
    os_type VARCHAR(20),
    os_version VARCHAR(50),
    cpu_cores INTEGER,
    memory_gb INTEGER,
    disk_gb INTEGER,
    tags TEXT, -- JSON array
    environment VARCHAR(20) DEFAULT 'production',
    group_name VARCHAR(50),
    monitoring_enabled BOOLEAN DEFAULT true,
    backup_enabled BOOLEAN DEFAULT false,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL,
    last_connected DATETIME,
    connection_count INTEGER DEFAULT 0,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- 索引
CREATE INDEX idx_hosts_name ON hosts(name);
CREATE INDEX idx_hosts_ip ON hosts(ip_address);
CREATE INDEX idx_hosts_status ON hosts(status);
CREATE INDEX idx_hosts_group ON hosts(group_name);
CREATE INDEX idx_hosts_environment ON hosts(environment);
```

### 3. 告警表 (alerts)
```sql
CREATE TABLE alerts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    level VARCHAR(20) NOT NULL, -- critical, warning, info
    source VARCHAR(50) NOT NULL, -- system, host, application
    status VARCHAR(20) NOT NULL DEFAULT 'open', -- open, acknowledged, resolved, closed
    alert_time DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    acknowledged_time DATETIME,
    resolved_time DATETIME,
    closed_time DATETIME,
    host_id INTEGER,
    assigned_to INTEGER,
    created_by INTEGER,
    rule_id VARCHAR(50),
    fingerprint VARCHAR(64), -- 用于去重
    metadata TEXT, -- JSON格式的额外信息
    notification_sent BOOLEAN DEFAULT false,
    escalation_level INTEGER DEFAULT 0,
    parent_alert_id INTEGER,
    FOREIGN KEY (host_id) REFERENCES hosts(id),
    FOREIGN KEY (assigned_to) REFERENCES users(id),
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (parent_alert_id) REFERENCES alerts(id)
);

-- 索引
CREATE INDEX idx_alerts_status ON alerts(status);
CREATE INDEX idx_alerts_level ON alerts(level);
CREATE INDEX idx_alerts_time ON alerts(alert_time);
CREATE INDEX idx_alerts_host ON alerts(host_id);
CREATE INDEX idx_alerts_assigned ON alerts(assigned_to);
CREATE INDEX idx_alerts_fingerprint ON alerts(fingerprint);
```

### 4. 操作日志表 (operations_log)
```sql
CREATE TABLE operations_log (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    operation_type VARCHAR(50) NOT NULL, -- ssh_command, api_call, system_action
    command TEXT,
    result TEXT,
    status VARCHAR(20) NOT NULL, -- success, failed, timeout
    executed_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    duration_ms INTEGER,
    host_id INTEGER,
    user_id INTEGER NOT NULL,
    session_id VARCHAR(36),
    client_ip VARCHAR(45),
    user_agent TEXT,
    error_message TEXT,
    exit_code INTEGER,
    working_directory VARCHAR(255),
    environment_vars TEXT, -- JSON格式
    FOREIGN KEY (host_id) REFERENCES hosts(id),
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 索引
CREATE INDEX idx_operations_type ON operations_log(operation_type);
CREATE INDEX idx_operations_status ON operations_log(status);
CREATE INDEX idx_operations_time ON operations_log(executed_at);
CREATE INDEX idx_operations_user ON operations_log(user_id);
CREATE INDEX idx_operations_host ON operations_log(host_id);
CREATE INDEX idx_operations_session ON operations_log(session_id);
```

### 5. 对话会话表 (chat_sessions)
```sql
CREATE TABLE chat_sessions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id VARCHAR(36) UNIQUE NOT NULL,
    user_id INTEGER NOT NULL,
    title VARCHAR(200),
    started_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    ended_at DATETIME,
    status VARCHAR(20) NOT NULL DEFAULT 'active', -- active, ended, archived
    context_data TEXT, -- JSON格式的上下文信息
    message_count INTEGER DEFAULT 0,
    token_usage INTEGER DEFAULT 0,
    last_activity DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    client_info TEXT, -- JSON格式的客户端信息
    FOREIGN KEY (user_id) REFERENCES users(id)
);

-- 索引
CREATE INDEX idx_chat_sessions_id ON chat_sessions(session_id);
CREATE INDEX idx_chat_sessions_user ON chat_sessions(user_id);
CREATE INDEX idx_chat_sessions_status ON chat_sessions(status);
CREATE INDEX idx_chat_sessions_activity ON chat_sessions(last_activity);
```

### 6. 对话消息表 (chat_messages)
```sql
CREATE TABLE chat_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    session_id INTEGER NOT NULL,
    message_type VARCHAR(20) NOT NULL, -- user, assistant, system, tool
    content TEXT NOT NULL,
    ai_response TEXT,
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    intent VARCHAR(50),
    extracted_params TEXT, -- JSON格式
    tool_calls TEXT, -- JSON格式的工具调用信息
    tool_results TEXT, -- JSON格式的工具执行结果
    token_count INTEGER,
    processing_time_ms INTEGER,
    error_message TEXT,
    FOREIGN KEY (session_id) REFERENCES chat_sessions(id) ON DELETE CASCADE
);

-- 索引
CREATE INDEX idx_chat_messages_session ON chat_messages(session_id);
CREATE INDEX idx_chat_messages_type ON chat_messages(message_type);
CREATE INDEX idx_chat_messages_time ON chat_messages(created_at);
CREATE INDEX idx_chat_messages_intent ON chat_messages(intent);
```

### 7. 权限表 (permissions)
```sql
CREATE TABLE permissions (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    resource VARCHAR(50) NOT NULL, -- hosts, alerts, users, stats, config
    action VARCHAR(50) NOT NULL, -- read, write, delete, execute
    role VARCHAR(20) NOT NULL, -- super_admin, admin, operator, viewer
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    description TEXT
);

-- 预置权限数据
INSERT INTO permissions (resource, action, role, description) VALUES
('hosts', 'read', 'viewer', '查看主机列表'),
('hosts', 'read', 'operator', '查看主机列表'),
('hosts', 'write', 'operator', '管理主机信息'),
('hosts', 'execute', 'operator', '执行主机命令'),
('alerts', 'read', 'viewer', '查看告警信息'),
('alerts', 'write', 'operator', '处理告警'),
('users', 'read', 'admin', '查看用户列表'),
('users', 'write', 'admin', '管理用户'),
('config', 'read', 'admin', '查看系统配置'),
('config', 'write', 'super_admin', '修改系统配置');

-- 索引
CREATE INDEX idx_permissions_role ON permissions(role);
CREATE INDEX idx_permissions_resource ON permissions(resource);
CREATE UNIQUE INDEX idx_permissions_unique ON permissions(resource, action, role);
```

### 8. 系统配置表 (system_config)
```sql
CREATE TABLE system_config (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value TEXT NOT NULL,
    config_type VARCHAR(20) NOT NULL DEFAULT 'string', -- string, number, boolean, json
    description TEXT,
    is_sensitive BOOLEAN DEFAULT false,
    category VARCHAR(50) DEFAULT 'general',
    validation_rule TEXT, -- 验证规则
    default_value TEXT,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_by INTEGER,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- 预置配置数据
INSERT INTO system_config (config_key, config_value, config_type, description, category) VALUES
('deepseek_api_url', 'https://api.deepseek.com', 'string', 'DeepSeek API地址', 'ai'),
('deepseek_model', 'deepseek-chat', 'string', 'DeepSeek模型名称', 'ai'),
('max_context_tokens', '4000', 'number', '最大上下文Token数', 'ai'),
('ssh_timeout', '30', 'number', 'SSH连接超时时间(秒)', 'ssh'),
('max_ssh_connections', '10', 'number', '最大SSH连接数', 'ssh'),
('alert_retention_days', '90', 'number', '告警保留天数', 'monitoring'),
('log_retention_days', '30', 'number', '日志保留天数', 'logging'),
('session_timeout', '24', 'number', '会话超时时间(小时)', 'security');

-- 索引
CREATE INDEX idx_config_key ON system_config(config_key);
CREATE INDEX idx_config_category ON system_config(category);
```

## 🔗 表关系说明

### 主要外键关系
- `hosts.created_by` → `users.id`
- `alerts.host_id` → `hosts.id`
- `alerts.assigned_to` → `users.id`
- `operations_log.host_id` → `hosts.id`
- `operations_log.user_id` → `users.id`
- `chat_sessions.user_id` → `users.id`
- `chat_messages.session_id` → `chat_sessions.id`

### 数据完整性约束
- 级联删除：删除会话时自动删除相关消息
- 软删除：用户和主机使用状态字段标记删除
- 外键约束：保证数据引用完整性

## 📈 性能优化

### 索引策略
- **主键索引**：自动创建，用于快速定位
- **唯一索引**：用户名、邮箱、主机名等唯一字段
- **复合索引**：多字段查询优化
- **时间索引**：按时间范围查询优化

### 查询优化
- **分页查询**：LIMIT + OFFSET
- **条件过滤**：WHERE子句优化
- **连接查询**：适当使用JOIN
- **聚合查询**：GROUP BY + 聚合函数

### 数据归档
- **日志轮转**：定期清理旧日志
- **告警归档**：已解决告警定期归档
- **会话清理**：过期会话自动清理

## 🔒 安全考虑

### 敏感数据加密
- **密码**：bcrypt哈希存储
- **SSH密码**：AES-256-GCM加密
- **SSH密钥密码**：AES-256-GCM加密
- **双因子密钥**：加密存储

### 数据访问控制
- **行级安全**：基于用户角色的数据访问
- **字段级安全**：敏感字段访问控制
- **审计日志**：所有数据变更记录

### 备份安全
- **加密备份**：备份文件加密存储
- **访问控制**：备份文件访问权限
- **完整性校验**：备份文件完整性验证
