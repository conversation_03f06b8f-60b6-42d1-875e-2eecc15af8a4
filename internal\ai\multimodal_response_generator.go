package ai

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// MultimodalResponseGenerator 多模态响应生成器
type MultimodalResponseGenerator struct {
	logger            *logrus.Logger
	config            *MultimodalConfig
	responseTemplates map[string]*MultimodalResponseTemplate
	adaptationEngine  *ResponseAdaptationEngine
}

// MultimodalResponseTemplate 多模态响应模板
type MultimodalResponseTemplate struct {
	Name           string                 `json:"name"`
	Type           string                 `json:"type"`
	SupportedModes []InteractionMode      `json:"supported_modes"`
	TextTemplate   string                 `json:"text_template"`
	SpeechTemplate string                 `json:"speech_template"`
	ImageTemplate  string                 `json:"image_template"`
	UITemplate     string                 `json:"ui_template"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// ResponseAdaptationEngine 响应适应引擎
type ResponseAdaptationEngine struct {
	logger *logrus.Logger
}

// NewMultimodalResponseGenerator 创建多模态响应生成器
func NewMultimodalResponseGenerator(logger *logrus.Logger, config *MultimodalConfig) *MultimodalResponseGenerator {
	generator := &MultimodalResponseGenerator{
		logger:            logger,
		config:            config,
		responseTemplates: make(map[string]*MultimodalResponseTemplate),
		adaptationEngine:  NewResponseAdaptationEngine(logger),
	}

	// 初始化响应模板
	generator.initializeResponseTemplates()

	logger.Info("🎭 多模态响应生成器初始化完成")
	return generator
}

// GenerateResponse 生成多模态响应
func (mrg *MultimodalResponseGenerator) GenerateResponse(
	ctx context.Context,
	input interface{},
	originalInput *MultimodalInput,
) (*MultimodalResponse, error) {
	start := time.Now()

	mrg.logger.WithFields(logrus.Fields{
		"input_mode": originalInput.PrimaryMode,
		"session_id": originalInput.SessionID,
	}).Info("🎭 开始生成多模态响应")

	// 1. 分析输入和上下文
	analysis := mrg.analyzeInputForResponse(input, originalInput)

	// 2. 选择响应策略
	strategy := mrg.selectResponseStrategy(analysis, originalInput)

	// 3. 确定输出模态
	outputModes := mrg.determineOutputModes(strategy, originalInput)

	// 4. 生成基础响应内容
	baseContent := mrg.generateBaseContent(analysis, strategy)

	// 5. 创建多模态响应
	response := &MultimodalResponse{
		SessionID:   originalInput.SessionID,
		ResponseID:  mrg.generateResponseID(),
		Timestamp:   time.Now(),
		PrimaryMode: outputModes[0],
		Confidence:  analysis.Confidence,
		Metadata:    make(map[string]interface{}),
	}

	// 6. 生成各种模态的响应
	if err := mrg.generateModalResponses(ctx, response, baseContent, outputModes, analysis); err != nil {
		return nil, fmt.Errorf("failed to generate modal responses: %w", err)
	}

	// 7. 应用个性化适应
	if err := mrg.applyPersonalization(response, analysis); err != nil {
		mrg.logger.WithError(err).Warn("Failed to apply personalization")
	}

	response.ProcessingTime = time.Since(start)

	mrg.logger.WithFields(logrus.Fields{
		"response_id":     response.ResponseID,
		"primary_mode":    response.PrimaryMode,
		"processing_time": response.ProcessingTime,
		"confidence":      response.Confidence,
	}).Info("🎭 多模态响应生成完成")

	return response, nil
}

// 私有方法

func (mrg *MultimodalResponseGenerator) analyzeInputForResponse(input interface{}, originalInput *MultimodalInput) *ResponseAnalysis {
	analysis := &ResponseAnalysis{
		Intent:        "general",
		Confidence:    0.8,
		UserContext:   make(map[string]interface{}),
		TaskContext:   make(map[string]interface{}),
		ResponseType:  "informational",
		Urgency:       "normal",
		Complexity:    "medium",
		RequiredModes: []InteractionMode{ModeText},
	}

	// 从增强输入中提取信息
	if enhanced, ok := input.(map[string]interface{}); ok {
		// 提取会话上下文
		if sessionContext, exists := enhanced["session_context"]; exists {
			if ctx, ok := sessionContext.(*SessionContext); ok {
				analysis.Intent = ctx.Intent
				analysis.TaskContext = ctx.TaskContext
			}
		}

		// 提取用户偏好
		if userPrefs, exists := enhanced["user_preferences"]; exists {
			if prefs, ok := userPrefs.(map[string]interface{}); ok {
				analysis.UserContext = prefs
			}
		}

		// 提取适应性配置文件
		if adaptProfile, exists := enhanced["adaptation_profile"]; exists {
			if profile, ok := adaptProfile.(*AdaptationProfile); ok {
				analysis.UserPreferences = profile
			}
		}

		// 提取分析结果
		if inputAnalysis, exists := enhanced["analysis"]; exists {
			if ia, ok := inputAnalysis.(*InputAnalysis); ok {
				analysis.Confidence = ia.Confidence
				analysis.Intent = ia.Intent
			}
		}
	}

	// 根据意图确定响应类型
	analysis.ResponseType = mrg.determineResponseType(analysis.Intent)
	analysis.Urgency = mrg.determineUrgency(analysis.Intent)
	analysis.Complexity = mrg.determineComplexity(analysis.Intent, analysis.TaskContext)

	return analysis
}

func (mrg *MultimodalResponseGenerator) selectResponseStrategy(analysis *ResponseAnalysis, originalInput *MultimodalInput) *MultimodalResponseStrategy {
	strategy := &MultimodalResponseStrategy{
		Type:           "adaptive",
		Verbosity:      "medium",
		TechnicalLevel: "medium",
		Tone:           "friendly",
		Structure:      "conversational",
	}

	// 根据用户偏好调整策略
	if analysis.UserPreferences != nil {
		strategy.Verbosity = mrg.mapVerbosityLevel(analysis.UserPreferences.VerbosityLevel)
		strategy.TechnicalLevel = mrg.mapTechnicalLevel(analysis.UserPreferences.TechnicalLevel)
		strategy.Tone = analysis.UserPreferences.ResponseStyle
	}

	// 根据意图调整策略
	switch analysis.Intent {
	case "help":
		strategy.Type = "instructional"
		strategy.Verbosity = "high"
		strategy.Structure = "step_by_step"
	case "alert", "error":
		strategy.Type = "urgent"
		strategy.Tone = "serious"
		strategy.Verbosity = "low"
	case "query":
		strategy.Type = "informational"
		strategy.Structure = "structured"
	case "status":
		strategy.Type = "status_report"
		strategy.Structure = "tabular"
	}

	return strategy
}

func (mrg *MultimodalResponseGenerator) determineOutputModes(strategy *MultimodalResponseStrategy, originalInput *MultimodalInput) []InteractionMode {
	modes := []InteractionMode{ModeText} // 默认包含文本

	// 根据输入模态确定输出模态
	switch originalInput.PrimaryMode {
	case ModeSpeech:
		if mrg.config.EnableSpeechOutput {
			modes = append(modes, ModeSpeech)
		}
	case ModeImage:
		modes = append(modes, ModeImage)
	case ModeGesture:
		modes = append(modes, ModeGesture)
	}

	// 根据响应类型添加合适的模态
	switch strategy.Type {
	case "status_report":
		modes = append(modes, ModeImage) // 添加图表
	case "instructional":
		if mrg.config.EnableSpeechOutput {
			modes = append(modes, ModeSpeech) // 添加语音指导
		}
	case "urgent":
		if mrg.config.EnableSpeechOutput {
			modes = append(modes, ModeSpeech) // 紧急情况使用语音
		}
	}

	// 去重
	uniqueModes := make([]InteractionMode, 0)
	seen := make(map[InteractionMode]bool)
	for _, mode := range modes {
		if !seen[mode] {
			uniqueModes = append(uniqueModes, mode)
			seen[mode] = true
		}
	}

	return uniqueModes
}

func (mrg *MultimodalResponseGenerator) generateBaseContent(analysis *ResponseAnalysis, strategy *MultimodalResponseStrategy) string {
	// 根据意图生成基础内容
	switch analysis.Intent {
	case "query":
		return mrg.generateQueryResponse(analysis)
	case "add":
		return mrg.generateAddResponse(analysis)
	case "delete":
		return mrg.generateDeleteResponse(analysis)
	case "status":
		return mrg.generateStatusResponse(analysis)
	case "help":
		return mrg.generateHelpResponse(analysis)
	case "alert":
		return mrg.generateAlertResponse(analysis)
	default:
		return mrg.generateGeneralResponse(analysis)
	}
}

func (mrg *MultimodalResponseGenerator) generateModalResponses(
	ctx context.Context,
	response *MultimodalResponse,
	baseContent string,
	outputModes []InteractionMode,
	analysis *ResponseAnalysis,
) error {
	for _, mode := range outputModes {
		switch mode {
		case ModeText:
			response.TextResponse = mrg.generateTextResponse(baseContent, analysis)
		case ModeSpeech:
			speechResponse, err := mrg.generateSpeechResponse(ctx, baseContent, analysis)
			if err != nil {
				mrg.logger.WithError(err).Warn("Failed to generate speech response")
			} else {
				response.SpeechResponse = speechResponse
			}
		case ModeImage:
			imageResponse, err := mrg.generateImageResponse(ctx, baseContent, analysis)
			if err != nil {
				mrg.logger.WithError(err).Warn("Failed to generate image response")
			} else {
				response.ImageResponse = imageResponse
			}
		case ModeGesture:
			response.GestureResponse = mrg.generateGestureResponse(baseContent, analysis)
		}
	}

	// 总是生成UI响应
	response.UIResponse = mrg.generateUIResponse(baseContent, analysis)

	return nil
}

func (mrg *MultimodalResponseGenerator) generateTextResponse(content string, analysis *ResponseAnalysis) *TextResponse {
	return &TextResponse{
		Content:  content,
		Language: "zh-CN",
		Formatting: map[string]interface{}{
			"style": "conversational",
			"tone":  "friendly",
		},
		Links:   []string{},
		Actions: mrg.generateSuggestedActions(analysis),
	}
}

func (mrg *MultimodalResponseGenerator) generateSpeechResponse(ctx context.Context, content string, analysis *ResponseAnalysis) (*SpeechResponse, error) {
	// 这里应该调用语音合成服务
	// 暂时返回模拟数据
	return &SpeechResponse{
		AudioData: make([]byte, 1024), // 模拟音频数据
		Format:    "wav",
		Duration:  float64(len(content)) * 0.1,
		Voice:     "zh-CN-XiaoxiaoNeural",
		Speed:     1.0,
		Volume:    0.8,
		Language:  "zh-CN",
	}, nil
}

func (mrg *MultimodalResponseGenerator) generateImageResponse(ctx context.Context, content string, analysis *ResponseAnalysis) (*ImageResponse, error) {
	// 这里应该生成图表或图像
	// 暂时返回模拟数据
	return &ImageResponse{
		ImageData:   make([]byte, 2048), // 模拟图像数据
		Format:      "png",
		Width:       800,
		Height:      600,
		Description: "系统状态图表",
		Annotations: []ImageAnnotation{
			{
				Type:     "label",
				Text:     "CPU使用率",
				Position: Point3D{X: 100, Y: 50, Z: 0},
				Color:    "#FF0000",
			},
		},
	}, nil
}

func (mrg *MultimodalResponseGenerator) generateGestureResponse(content string, analysis *ResponseAnalysis) *GestureResponse {
	return &GestureResponse{
		GestureType: "confirmation",
		Instructions: []string{
			"点击确认按钮",
			"或者说'确认'",
		},
		Feedback: "操作已完成",
	}
}

func (mrg *MultimodalResponseGenerator) generateUIResponse(content string, analysis *ResponseAnalysis) *UIResponse {
	return &UIResponse{
		ComponentType: "card",
		Layout:        "vertical",
		Elements: []UIElement{
			{
				ID:      "response_text",
				Type:    "text",
				Content: content,
				Properties: map[string]interface{}{
					"fontSize": "14px",
					"color":    "#333333",
				},
			},
			{
				ID:   "action_buttons",
				Type: "button_group",
				Properties: map[string]interface{}{
					"buttons": mrg.generateSuggestedActions(analysis),
				},
			},
		},
		Interactions: []UIInteraction{
			{
				Type:   "click",
				Target: "action_buttons",
				Action: "execute_action",
			},
		},
	}
}

func (mrg *MultimodalResponseGenerator) applyPersonalization(response *MultimodalResponse, analysis *ResponseAnalysis) error {
	// 根据用户偏好个性化响应
	if analysis.UserPreferences != nil {
		// 调整语言
		if response.TextResponse != nil {
			response.TextResponse.Language = analysis.UserPreferences.LanguagePreference
		}
		if response.SpeechResponse != nil {
			response.SpeechResponse.Language = analysis.UserPreferences.LanguagePreference
		}

		// 调整详细程度
		if analysis.UserPreferences.VerbosityLevel < 0.5 && response.TextResponse != nil {
			response.TextResponse.Content = mrg.shortenContent(response.TextResponse.Content)
		}
	}

	return nil
}

// 辅助方法

func (mrg *MultimodalResponseGenerator) generateResponseID() string {
	return fmt.Sprintf("resp_%d", time.Now().UnixNano())
}

func (mrg *MultimodalResponseGenerator) determineResponseType(intent string) string {
	switch intent {
	case "query":
		return "informational"
	case "add", "delete", "modify":
		return "confirmational"
	case "help":
		return "instructional"
	case "alert", "error":
		return "warning"
	case "status":
		return "status_report"
	default:
		return "conversational"
	}
}

func (mrg *MultimodalResponseGenerator) determineUrgency(intent string) string {
	switch intent {
	case "alert", "error":
		return "high"
	case "delete":
		return "medium"
	default:
		return "normal"
	}
}

func (mrg *MultimodalResponseGenerator) determineComplexity(intent string, taskContext map[string]interface{}) string {
	switch intent {
	case "help":
		return "high"
	case "status":
		return "medium"
	case "query":
		if len(taskContext) > 3 {
			return "medium"
		}
		return "low"
	default:
		return "low"
	}
}

func (mrg *MultimodalResponseGenerator) mapVerbosityLevel(level float64) string {
	if level < 0.3 {
		return "low"
	} else if level < 0.7 {
		return "medium"
	}
	return "high"
}

func (mrg *MultimodalResponseGenerator) mapTechnicalLevel(level float64) string {
	if level < 0.3 {
		return "basic"
	} else if level < 0.7 {
		return "intermediate"
	}
	return "advanced"
}

func (mrg *MultimodalResponseGenerator) generateSuggestedActions(analysis *ResponseAnalysis) []string {
	actions := []string{}

	switch analysis.Intent {
	case "query":
		actions = append(actions, "查看详情", "导出数据", "刷新")
	case "add":
		actions = append(actions, "确认添加", "取消", "修改")
	case "delete":
		actions = append(actions, "确认删除", "取消")
	case "status":
		actions = append(actions, "刷新状态", "查看历史", "设置告警")
	case "help":
		actions = append(actions, "查看文档", "联系支持", "返回")
	default:
		actions = append(actions, "确定", "取消")
	}

	return actions
}

func (mrg *MultimodalResponseGenerator) shortenContent(content string) string {
	// 简化内容，保留关键信息
	if len(content) <= 100 {
		return content
	}
	return content[:97] + "..."
}

// 响应生成方法

func (mrg *MultimodalResponseGenerator) generateQueryResponse(analysis *ResponseAnalysis) string {
	return "查询结果已显示，您可以查看相关信息。"
}

func (mrg *MultimodalResponseGenerator) generateAddResponse(analysis *ResponseAnalysis) string {
	return "添加操作已完成，新项目已成功创建。"
}

func (mrg *MultimodalResponseGenerator) generateDeleteResponse(analysis *ResponseAnalysis) string {
	return "删除操作已完成，指定项目已被移除。"
}

func (mrg *MultimodalResponseGenerator) generateStatusResponse(analysis *ResponseAnalysis) string {
	return "系统状态正常，所有服务运行良好。"
}

func (mrg *MultimodalResponseGenerator) generateHelpResponse(analysis *ResponseAnalysis) string {
	return "我可以帮助您管理系统、查看状态、配置设置等。请告诉我您需要什么帮助。"
}

func (mrg *MultimodalResponseGenerator) generateAlertResponse(analysis *ResponseAnalysis) string {
	return "检测到重要事件，请及时处理。"
}

func (mrg *MultimodalResponseGenerator) generateGeneralResponse(analysis *ResponseAnalysis) string {
	return "我已收到您的请求，正在为您处理。"
}

func (mrg *MultimodalResponseGenerator) initializeResponseTemplates() {
	// 初始化响应模板
	mrg.responseTemplates["general"] = &MultimodalResponseTemplate{
		Name:           "通用响应",
		Type:           "general",
		SupportedModes: []InteractionMode{ModeText, ModeSpeech},
		TextTemplate:   "{{.content}}",
		SpeechTemplate: "{{.content}}",
	}

	mrg.responseTemplates["status"] = &MultimodalResponseTemplate{
		Name:           "状态响应",
		Type:           "status",
		SupportedModes: []InteractionMode{ModeText, ModeImage},
		TextTemplate:   "系统状态：{{.status}}",
		ImageTemplate:  "status_chart",
	}
}

// ResponseAnalysis 响应分析结果
type ResponseAnalysis struct {
	Intent          string                 `json:"intent"`
	Confidence      float64                `json:"confidence"`
	UserContext     map[string]interface{} `json:"user_context"`
	TaskContext     map[string]interface{} `json:"task_context"`
	ResponseType    string                 `json:"response_type"`
	Urgency         string                 `json:"urgency"`
	Complexity      string                 `json:"complexity"`
	RequiredModes   []InteractionMode      `json:"required_modes"`
	UserPreferences *AdaptationProfile     `json:"user_preferences"`
}

// MultimodalResponseStrategy 多模态响应策略
type MultimodalResponseStrategy struct {
	Type           string `json:"type"`
	Verbosity      string `json:"verbosity"`
	TechnicalLevel string `json:"technical_level"`
	Tone           string `json:"tone"`
	Structure      string `json:"structure"`
}

// NewResponseAdaptationEngine 创建响应适应引擎
func NewResponseAdaptationEngine(logger *logrus.Logger) *ResponseAdaptationEngine {
	return &ResponseAdaptationEngine{
		logger: logger,
	}
}
