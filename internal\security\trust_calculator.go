package security

import (
	"fmt"
	"math"
	"net"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// TrustCalculator 信任计算器
type TrustCalculator struct {
	logger *logrus.Logger
}

// NewTrustCalculator 创建信任计算器
func NewTrustCalculator(logger *logrus.Logger) *TrustCalculator {
	return &TrustCalculator{
		logger: logger,
	}
}

// CalculateTrustScore 计算信任分数
func (tc *TrustCalculator) CalculateTrustScore(identity *Identity, device *Device, context *AccessContext) float64 {
	var score float64 = 0.5 // 基础分数

	// 身份信任因子 (权重: 40%)
	identityScore := tc.calculateIdentityTrust(identity)
	score += identityScore * 0.4

	// 设备信任因子 (权重: 30%)
	deviceScore := tc.calculateDeviceTrust(device)
	score += deviceScore * 0.3

	// 上下文信任因子 (权重: 20%)
	contextScore := tc.calculateContextTrust(context)
	score += contextScore * 0.2

	// 历史行为因子 (权重: 10%)
	behaviorScore := tc.calculateBehaviorTrust(identity, device)
	score += behaviorScore * 0.1

	// 确保分数在0-1范围内
	if score < 0 {
		score = 0
	} else if score > 1 {
		score = 1
	}

	tc.logger.WithFields(logrus.Fields{
		"identity_id":     identity.ID,
		"device_id":       device.ID,
		"identity_score":  identityScore,
		"device_score":    deviceScore,
		"context_score":   contextScore,
		"behavior_score":  behaviorScore,
		"final_score":     score,
	}).Debug("Trust score calculated")

	return score
}

// calculateIdentityTrust 计算身份信任
func (tc *TrustCalculator) calculateIdentityTrust(identity *Identity) float64 {
	score := 0.5 // 基础分数

	// 身份类型权重
	switch identity.Type {
	case "user":
		score += 0.1
	case "service":
		score += 0.2
	case "admin":
		score += 0.3
	}

	// 角色权重
	for _, role := range identity.Roles {
		switch role {
		case "admin":
			score += 0.2
		case "operator":
			score += 0.15
		case "viewer":
			score += 0.1
		}
	}

	// 活跃度权重
	if identity.IsActive {
		score += 0.1
	}

	// 最近活跃时间权重
	timeSinceLastSeen := time.Since(identity.LastSeen)
	if timeSinceLastSeen < 1*time.Hour {
		score += 0.1
	} else if timeSinceLastSeen < 24*time.Hour {
		score += 0.05
	}

	// 历史信任分数权重
	score += identity.TrustScore * 0.2

	return math.Min(score, 1.0)
}

// calculateDeviceTrust 计算设备信任
func (tc *TrustCalculator) calculateDeviceTrust(device *Device) float64 {
	score := 0.3 // 基础分数

	// 设备管理状态
	if device.IsManaged {
		score += 0.3
	}

	// 设备合规状态
	if device.IsCompliant {
		score += 0.2
	}

	// 设备类型权重
	switch device.Type {
	case "server":
		score += 0.2
	case "laptop":
		score += 0.15
	case "mobile":
		score += 0.1
	case "iot":
		score += 0.05
	}

	// 操作系统权重
	if strings.Contains(strings.ToLower(device.OS), "windows") ||
		strings.Contains(strings.ToLower(device.OS), "linux") ||
		strings.Contains(strings.ToLower(device.OS), "macos") {
		score += 0.1
	}

	// 设备信任级别
	switch device.TrustLevel {
	case TrustFull:
		score += 0.2
	case TrustHigh:
		score += 0.15
	case TrustMedium:
		score += 0.1
	case TrustLow:
		score += 0.05
	}

	// 最近活跃时间
	timeSinceLastSeen := time.Since(device.LastSeen)
	if timeSinceLastSeen < 1*time.Hour {
		score += 0.05
	}

	return math.Min(score, 1.0)
}

// calculateContextTrust 计算上下文信任
func (tc *TrustCalculator) calculateContextTrust(context *AccessContext) float64 {
	score := 0.5 // 基础分数

	// 网络类型权重
	switch context.NetworkType {
	case "corporate":
		score += 0.3
	case "home":
		score += 0.1
	case "public":
		score -= 0.2
	}

	// VPN/代理检查
	if context.IsVPN {
		score -= 0.1
	}
	if context.IsTor {
		score -= 0.3
	}
	if context.IsProxy {
		score -= 0.1
	}

	// 安全级别
	switch context.SecurityLevel {
	case "high":
		score += 0.2
	case "medium":
		score += 0.1
	case "low":
		score -= 0.1
	}

	// IP地址信任
	if tc.isTrustedIP(context.IPAddress) {
		score += 0.2
	} else if tc.isSuspiciousIP(context.IPAddress) {
		score -= 0.3
	}

	// 时间因子
	score += tc.calculateTimeTrust(context.TimeOfDay, context.DayOfWeek)

	// 地理位置因子
	if context.Location != "" {
		score += tc.calculateLocationTrust(context.Location)
	}

	return math.Max(math.Min(score, 1.0), 0.0)
}

// calculateBehaviorTrust 计算行为信任
func (tc *TrustCalculator) calculateBehaviorTrust(identity *Identity, device *Device) float64 {
	score := 0.5 // 基础分数

	// 简化实现：基于历史数据
	// 实际实现应该分析用户的历史行为模式

	// 账户年龄
	accountAge := time.Since(identity.CreatedAt)
	if accountAge > 365*24*time.Hour { // 超过1年
		score += 0.2
	} else if accountAge > 30*24*time.Hour { // 超过1个月
		score += 0.1
	}

	// 设备一致性
	// 如果用户总是使用相同的设备，增加信任
	// 这里简化为检查设备是否为已知设备
	if device.LastSeen.Before(time.Now().Add(-7*24*time.Hour)) {
		score += 0.1 // 设备使用超过7天
	}

	return math.Min(score, 1.0)
}

// calculateTimeTrust 计算时间信任
func (tc *TrustCalculator) calculateTimeTrust(timeOfDay, dayOfWeek string) float64 {
	score := 0.0

	// 工作时间权重
	if tc.isBusinessHours(timeOfDay) {
		score += 0.1
	}

	// 工作日权重
	if tc.isBusinessDay(dayOfWeek) {
		score += 0.05
	}

	return score
}

// calculateLocationTrust 计算地理位置信任
func (tc *TrustCalculator) calculateLocationTrust(location string) float64 {
	// 简化实现：基于已知安全位置
	trustedLocations := []string{
		"US", "CA", "GB", "DE", "FR", "JP", "AU", "SG",
	}

	for _, trusted := range trustedLocations {
		if strings.Contains(strings.ToUpper(location), trusted) {
			return 0.1
		}
	}

	// 高风险国家
	highRiskLocations := []string{
		"CN", "RU", "KP", "IR",
	}

	for _, risky := range highRiskLocations {
		if strings.Contains(strings.ToUpper(location), risky) {
			return -0.2
		}
	}

	return 0.0
}

// isTrustedIP 检查是否为可信IP
func (tc *TrustCalculator) isTrustedIP(ipStr string) bool {
	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	// 内网IP被认为是可信的
	if ip.IsPrivate() || ip.IsLoopback() {
		return true
	}

	// 这里可以添加企业的公网IP段
	trustedCIDRs := []string{
		"***********/24", // 示例企业IP段
	}

	for _, cidrStr := range trustedCIDRs {
		_, cidr, err := net.ParseCIDR(cidrStr)
		if err != nil {
			continue
		}
		if cidr.Contains(ip) {
			return true
		}
	}

	return false
}

// isSuspiciousIP 检查是否为可疑IP
func (tc *TrustCalculator) isSuspiciousIP(ipStr string) bool {
	// 简化实现：检查已知的恶意IP段
	// 实际实现应该集成威胁情报源

	suspiciousCIDRs := []string{
		"*********/24",   // 示例恶意IP段
		"************/24", // 示例恶意IP段
	}

	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	for _, cidrStr := range suspiciousCIDRs {
		_, cidr, err := net.ParseCIDR(cidrStr)
		if err != nil {
			continue
		}
		if cidr.Contains(ip) {
			return true
		}
	}

	return false
}

// isBusinessHours 检查是否为工作时间
func (tc *TrustCalculator) isBusinessHours(timeOfDay string) bool {
	// 简化实现：9:00-18:00为工作时间
	// 实际实现应该考虑时区和企业具体的工作时间

	if timeOfDay == "" {
		return false
	}

	// 解析时间格式 HH:MM
	var hour, minute int
	if _, err := fmt.Sscanf(timeOfDay, "%d:%d", &hour, &minute); err != nil {
		return false
	}

	return hour >= 9 && hour < 18
}

// isBusinessDay 检查是否为工作日
func (tc *TrustCalculator) isBusinessDay(dayOfWeek string) bool {
	businessDays := []string{
		"Monday", "Tuesday", "Wednesday", "Thursday", "Friday",
	}

	for _, day := range businessDays {
		if strings.EqualFold(dayOfWeek, day) {
			return true
		}
	}

	return false
}

// UpdateIdentityTrust 更新身份信任分数
func (tc *TrustCalculator) UpdateIdentityTrust(identity *Identity, delta float64, reason string) {
	oldScore := identity.TrustScore
	identity.TrustScore += delta

	// 确保分数在0-1范围内
	if identity.TrustScore < 0 {
		identity.TrustScore = 0
	} else if identity.TrustScore > 1 {
		identity.TrustScore = 1
	}

	tc.logger.WithFields(logrus.Fields{
		"identity_id": identity.ID,
		"old_score":   oldScore,
		"new_score":   identity.TrustScore,
		"delta":       delta,
		"reason":      reason,
	}).Info("Identity trust score updated")
}

// UpdateDeviceTrust 更新设备信任级别
func (tc *TrustCalculator) UpdateDeviceTrust(device *Device, level TrustLevel, reason string) {
	oldLevel := device.TrustLevel
	device.TrustLevel = level

	tc.logger.WithFields(logrus.Fields{
		"device_id":  device.ID,
		"old_level":  oldLevel.String(),
		"new_level":  level.String(),
		"reason":     reason,
	}).Info("Device trust level updated")
}
