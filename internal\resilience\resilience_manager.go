package resilience

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ResilienceConfig 弹性配置
type ResilienceConfig struct {
	ServiceName           string                    `json:"service_name"`
	Enabled               bool                      `json:"enabled"`
	CircuitBreakerEnabled bool                      `json:"circuit_breaker_enabled"`
	RetryEnabled          bool                      `json:"retry_enabled"`
	DegradationEnabled    bool                      `json:"degradation_enabled"`
	ErrorRecoveryEnabled  bool                      `json:"error_recovery_enabled"`
	CircuitBreakerConfigs map[string]CircuitBreakerConfig `json:"circuit_breaker_configs"`
	RetryConfigs          map[string]RetryConfig    `json:"retry_configs"`
	DegradationConfig     DegradationConfig         `json:"degradation_config"`
	HealthCheckInterval   time.Duration             `json:"health_check_interval"`
	MetricsEnabled        bool                      `json:"metrics_enabled"`
}

// ResilienceManager 弹性管理器
type ResilienceManager struct {
	config              ResilienceConfig
	circuitBreakerMgr   *CircuitBreakerManager
	gracefulDegradation *GracefulDegradation
	errorRecoveryMgr    *ErrorRecoveryManager
	retryConfigs        map[string]RetryConfig
	healthCheckers      []HealthChecker
	metrics             *ResilienceMetrics
	logger              *logrus.Logger
	mutex               sync.RWMutex
	running             bool
	stopChan            chan struct{}
}

// ResilienceMetrics 弹性指标
type ResilienceMetrics struct {
	TotalRequests        int64     `json:"total_requests"`
	SuccessfulRequests   int64     `json:"successful_requests"`
	FailedRequests       int64     `json:"failed_requests"`
	CircuitBreakerTrips  int64     `json:"circuit_breaker_trips"`
	RetryAttempts        int64     `json:"retry_attempts"`
	DegradationTriggers  int64     `json:"degradation_triggers"`
	ErrorRecoveryActions int64     `json:"error_recovery_actions"`
	AvgResponseTime      float64   `json:"avg_response_time_ms"`
	LastActivity         time.Time `json:"last_activity"`
	mutex                sync.RWMutex
}

// NewResilienceManager 创建弹性管理器
func NewResilienceManager(config ResilienceConfig, logger *logrus.Logger) *ResilienceManager {
	// 设置默认值
	if config.HealthCheckInterval <= 0 {
		config.HealthCheckInterval = 30 * time.Second
	}

	rm := &ResilienceManager{
		config:       config,
		retryConfigs: config.RetryConfigs,
		metrics:      &ResilienceMetrics{},
		logger:       logger,
		stopChan:     make(chan struct{}),
	}

	// 初始化组件
	if config.CircuitBreakerEnabled {
		rm.circuitBreakerMgr = NewCircuitBreakerManager(logger)
	}

	if config.DegradationEnabled {
		rm.gracefulDegradation = NewGracefulDegradation(config.DegradationConfig, logger)
	}

	if config.ErrorRecoveryEnabled {
		rm.errorRecoveryMgr = NewErrorRecoveryManager(logger)
	}

	return rm
}

// Start 启动弹性管理器
func (rm *ResilienceManager) Start(ctx context.Context) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if rm.running {
		return fmt.Errorf("resilience manager is already running")
	}

	if !rm.config.Enabled {
		rm.logger.Info("Resilience manager is disabled")
		return nil
	}

	rm.running = true

	// 启动组件
	if rm.gracefulDegradation != nil {
		if err := rm.gracefulDegradation.Start(ctx); err != nil {
			return fmt.Errorf("failed to start graceful degradation: %w", err)
		}
	}

	if rm.errorRecoveryMgr != nil {
		if err := rm.errorRecoveryMgr.Start(ctx); err != nil {
			return fmt.Errorf("failed to start error recovery manager: %w", err)
		}
	}

	// 启动健康检查
	go rm.healthCheckLoop(ctx)

	// 启动指标收集
	if rm.config.MetricsEnabled {
		go rm.metricsLoop(ctx)
	}

	rm.logger.WithField("service", rm.config.ServiceName).Info("Resilience manager started")
	return nil
}

// Stop 停止弹性管理器
func (rm *ResilienceManager) Stop() error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if !rm.running {
		return nil
	}

	close(rm.stopChan)
	rm.running = false

	// 停止组件
	if rm.gracefulDegradation != nil {
		rm.gracefulDegradation.Stop()
	}

	if rm.errorRecoveryMgr != nil {
		rm.errorRecoveryMgr.Stop()
	}

	rm.logger.WithField("service", rm.config.ServiceName).Info("Resilience manager stopped")
	return nil
}

// ExecuteWithResilience 带弹性的执行
func (rm *ResilienceManager) ExecuteWithResilience(ctx context.Context, operationName string, operation func(ctx context.Context) (interface{}, error)) (interface{}, error) {
	startTime := time.Now()
	
	// 更新指标
	rm.updateMetrics(func(m *ResilienceMetrics) {
		m.TotalRequests++
		m.LastActivity = startTime
	})

	var result interface{}
	var err error

	// 1. 检查降级状态
	if rm.gracefulDegradation != nil {
		if rm.gracefulDegradation.IsReadOnly() {
			if fallback := rm.gracefulDegradation.GetFallbackResponse(); fallback != nil {
				rm.updateMetrics(func(m *ResilienceMetrics) {
					m.DegradationTriggers++
				})
				return fallback, nil
			}
		}
	}

	// 2. 使用断路器执行
	if rm.circuitBreakerMgr != nil {
		cbConfig, exists := rm.config.CircuitBreakerConfigs[operationName]
		if !exists {
			// 使用默认配置
			cbConfig = CircuitBreakerConfig{
				Name:        operationName,
				MaxRequests: 10,
				Interval:    60 * time.Second,
				Timeout:     60 * time.Second,
			}
		}

		cb := rm.circuitBreakerMgr.GetOrCreate(operationName, cbConfig)
		
		result, err = cb.ExecuteWithContext(ctx, func(ctx context.Context) (interface{}, error) {
			// 3. 使用重试机制
			if rm.config.RetryEnabled {
				retryConfig, exists := rm.retryConfigs[operationName]
				if !exists {
					// 使用默认重试配置
					retryConfig = RetryConfig{
						MaxAttempts:     3,
						InitialDelay:    100 * time.Millisecond,
						BackoffStrategy: BackoffExponential,
					}
				}

				retryer := NewRetryer(retryConfig, rm.logger)
				return retryer.ExecuteWithContextAndResult(ctx, operation)
			}

			return operation(ctx)
		})

		// 检查断路器是否被触发
		if err == ErrOpenState {
			rm.updateMetrics(func(m *ResilienceMetrics) {
				m.CircuitBreakerTrips++
			})
		}
	} else {
		// 直接执行
		result, err = operation(ctx)
	}

	// 4. 处理错误恢复
	if err != nil && rm.errorRecoveryMgr != nil {
		errorInfo := ErrorInfo{
			Error:     err,
			Timestamp: time.Now(),
			Service:   rm.config.ServiceName,
			Component: operationName,
			Context:   map[string]interface{}{"operation": operationName},
		}

		if recoveryErr := rm.errorRecoveryMgr.HandleError(ctx, errorInfo); recoveryErr != nil {
			rm.logger.WithError(recoveryErr).Error("Error recovery failed")
		} else {
			rm.updateMetrics(func(m *ResilienceMetrics) {
				m.ErrorRecoveryActions++
			})
		}
	}

	// 更新指标
	duration := time.Since(startTime)
	rm.updateMetrics(func(m *ResilienceMetrics) {
		if err == nil {
			m.SuccessfulRequests++
		} else {
			m.FailedRequests++
		}
		
		// 更新平均响应时间
		if m.TotalRequests > 0 {
			m.AvgResponseTime = (m.AvgResponseTime*float64(m.TotalRequests-1) + float64(duration.Milliseconds())) / float64(m.TotalRequests)
		}
	})

	return result, err
}

// GetCircuitBreaker 获取断路器
func (rm *ResilienceManager) GetCircuitBreaker(name string) (*CircuitBreaker, bool) {
	if rm.circuitBreakerMgr == nil {
		return nil, false
	}
	return rm.circuitBreakerMgr.Get(name)
}

// GetDegradationLevel 获取降级级别
func (rm *ResilienceManager) GetDegradationLevel() DegradationLevel {
	if rm.gracefulDegradation == nil {
		return LevelNormal
	}
	return rm.gracefulDegradation.GetCurrentLevel()
}

// IsFeatureEnabled 检查功能是否启用
func (rm *ResilienceManager) IsFeatureEnabled(feature string) bool {
	if rm.gracefulDegradation == nil {
		return true
	}
	return rm.gracefulDegradation.IsFeatureEnabled(feature)
}

// RegisterHealthChecker 注册健康检查器
func (rm *ResilienceManager) RegisterHealthChecker(checker HealthChecker) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	rm.healthCheckers = append(rm.healthCheckers, checker)
	
	if rm.gracefulDegradation != nil {
		rm.gracefulDegradation.config.HealthCheckers = rm.healthCheckers
	}
}

// RegisterRecoveryRule 注册恢复规则
func (rm *ResilienceManager) RegisterRecoveryRule(rule *RecoveryRule) error {
	if rm.errorRecoveryMgr == nil {
		return fmt.Errorf("error recovery manager is not enabled")
	}
	return rm.errorRecoveryMgr.RegisterRule(rule)
}

// RegisterRecoveryHandler 注册恢复处理器
func (rm *ResilienceManager) RegisterRecoveryHandler(action RecoveryAction, handler RecoveryHandler) {
	if rm.errorRecoveryMgr != nil {
		rm.errorRecoveryMgr.RegisterHandler(action, handler)
	}
}

// healthCheckLoop 健康检查循环
func (rm *ResilienceManager) healthCheckLoop(ctx context.Context) {
	ticker := time.NewTicker(rm.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rm.stopChan:
			return
		case <-ticker.C:
			rm.performHealthCheck(ctx)
		}
	}
}

// performHealthCheck 执行健康检查
func (rm *ResilienceManager) performHealthCheck(ctx context.Context) {
	rm.mutex.RLock()
	checkers := make([]HealthChecker, len(rm.healthCheckers))
	copy(checkers, rm.healthCheckers)
	rm.mutex.RUnlock()

	for _, checker := range checkers {
		status := checker.Check(ctx)
		rm.logger.WithFields(logrus.Fields{
			"checker": checker.Name(),
			"healthy": status.Healthy,
			"details": status.Details,
		}).Debug("Health check completed")
	}
}

// metricsLoop 指标收集循环
func (rm *ResilienceManager) metricsLoop(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rm.stopChan:
			return
		case <-ticker.C:
			rm.logMetrics()
		}
	}
}

// logMetrics 记录指标
func (rm *ResilienceManager) logMetrics() {
	metrics := rm.GetMetrics()
	rm.logger.WithFields(logrus.Fields{
		"service":               rm.config.ServiceName,
		"total_requests":        metrics.TotalRequests,
		"successful_requests":   metrics.SuccessfulRequests,
		"failed_requests":       metrics.FailedRequests,
		"circuit_breaker_trips": metrics.CircuitBreakerTrips,
		"retry_attempts":        metrics.RetryAttempts,
		"degradation_triggers":  metrics.DegradationTriggers,
		"error_recovery_actions": metrics.ErrorRecoveryActions,
		"avg_response_time":     metrics.AvgResponseTime,
	}).Info("Resilience metrics")
}

// updateMetrics 更新指标
func (rm *ResilienceManager) updateMetrics(updateFunc func(*ResilienceMetrics)) {
	rm.metrics.mutex.Lock()
	defer rm.metrics.mutex.Unlock()
	updateFunc(rm.metrics)
}

// GetMetrics 获取指标
func (rm *ResilienceManager) GetMetrics() ResilienceMetrics {
	rm.metrics.mutex.RLock()
	defer rm.metrics.mutex.RUnlock()
	return *rm.metrics
}

// GetStatus 获取状态
func (rm *ResilienceManager) GetStatus() map[string]interface{} {
	status := map[string]interface{}{
		"service":  rm.config.ServiceName,
		"enabled":  rm.config.Enabled,
		"running":  rm.running,
		"metrics":  rm.GetMetrics(),
	}

	if rm.circuitBreakerMgr != nil {
		status["circuit_breakers"] = rm.circuitBreakerMgr.GetStats()
	}

	if rm.gracefulDegradation != nil {
		status["degradation_level"] = rm.gracefulDegradation.GetCurrentLevel().String()
		status["health_status"] = rm.gracefulDegradation.GetHealthStatus()
	}

	if rm.errorRecoveryMgr != nil {
		status["error_recovery"] = rm.errorRecoveryMgr.GetStats()
	}

	return status
}
