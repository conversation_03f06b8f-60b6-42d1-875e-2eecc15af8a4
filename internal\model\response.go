package model

import (
	"time"

	"gorm.io/gorm"
)

// BaseModel 基础模型
type BaseModel struct {
	ID        int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	CreatedAt time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`
}

// PaginationQuery 分页查询参数
type PaginationQuery struct {
	Page     int `json:"page" form:"page" binding:"min=1"`
	PageSize int `json:"page_size" form:"page_size" binding:"min=1,max=100"`
}

// DefaultPagination 设置默认分页参数
func (p *PaginationQuery) DefaultPagination() {
	if p.Page <= 0 {
		p.Page = 1
	}
	if p.PageSize <= 0 {
		p.PageSize = 20
	}
	if p.PageSize > 100 {
		p.PageSize = 100
	}
}

// Offset 计算偏移量
func (p *PaginationQuery) Offset() int {
	return (p.Page - 1) * p.PageSize
}

// Pagination 分页信息
type Pagination struct {
	Total       int64 `json:"total"`
	Page        int   `json:"page"`
	PageSize    int   `json:"page_size"`
	TotalPages  int   `json:"total_pages"`
	HasNext     bool  `json:"has_next"`
	HasPrevious bool  `json:"has_previous"`
}

// NewPagination 创建分页信息
func NewPagination(total int64, page, pageSize int) *Pagination {
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))
	if totalPages == 0 {
		totalPages = 1
	}

	return &Pagination{
		Total:       total,
		Page:        page,
		PageSize:    pageSize,
		TotalPages:  totalPages,
		HasNext:     page < totalPages,
		HasPrevious: page > 1,
	}
}

// Response 通用响应结构
type Response struct {
	Code      int         `json:"code"`
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	RequestID string      `json:"request_id,omitempty"`
	Timestamp int64       `json:"timestamp"`
}

// ListResponse 列表响应结构
type ListResponse struct {
	Code       int         `json:"code"`
	Message    string      `json:"message"`
	Data       interface{} `json:"data,omitempty"`
	Pagination *Pagination `json:"pagination,omitempty"`
	RequestID  string      `json:"request_id,omitempty"`
	Timestamp  int64       `json:"timestamp"`
}

// ErrorResponse 错误响应结构
type ErrorResponse struct {
	Code      int               `json:"code"`
	Message   string            `json:"message"`
	Details   string            `json:"details,omitempty"`
	Errors    []ValidationError `json:"errors,omitempty"`
	RequestID string            `json:"request_id,omitempty"`
	Timestamp int64             `json:"timestamp"`
	TraceID   string            `json:"trace_id,omitempty"`
}

// SuccessResponse 创建成功响应
func SuccessResponse(data interface{}, requestID string) *Response {
	return &Response{
		Code:      int(ErrCodeSuccess),
		Message:   "操作成功",
		Data:      data,
		RequestID: requestID,
		Timestamp: time.Now().Unix(),
	}
}

// SuccessListResponse 创建成功列表响应
func SuccessListResponse(data interface{}, pagination *Pagination, requestID string) *ListResponse {
	return &ListResponse{
		Code:       int(ErrCodeSuccess),
		Message:    "操作成功",
		Data:       data,
		Pagination: pagination,
		RequestID:  requestID,
		Timestamp:  time.Now().Unix(),
	}
}

// ErrorResponseFromAppError 从AppError创建错误响应
func ErrorResponseFromAppError(appErr *AppError, requestID, traceID string) *ErrorResponse {
	return &ErrorResponse{
		Code:      int(appErr.Code),
		Message:   appErr.Message,
		Details:   appErr.Details,
		RequestID: requestID,
		Timestamp: time.Now().Unix(),
		TraceID:   traceID,
	}
}

// ValidationErrorResponse 创建验证错误响应
func ValidationErrorResponse(errors ValidationErrors, requestID, traceID string) *ErrorResponse {
	return &ErrorResponse{
		Code:      int(ErrCodeValidationFailed),
		Message:   "数据验证失败",
		Errors:    errors,
		RequestID: requestID,
		Timestamp: time.Now().Unix(),
		TraceID:   traceID,
	}
}

// InternalErrorResponse 创建内部错误响应
func InternalErrorResponse(requestID, traceID string) *ErrorResponse {
	return &ErrorResponse{
		Code:      int(ErrCodeInternalError),
		Message:   "内部服务器错误",
		RequestID: requestID,
		Timestamp: time.Now().Unix(),
		TraceID:   traceID,
	}
}

// HealthCheckResponse 健康检查响应
type HealthCheckResponse struct {
	Status    string                 `json:"status"`
	Timestamp int64                  `json:"timestamp"`
	Version   string                 `json:"version"`
	Uptime    int64                  `json:"uptime"`
	Checks    map[string]HealthCheck `json:"checks"`
}

// HealthCheck 健康检查项
type HealthCheck struct {
	Status  string `json:"status"`
	Message string `json:"message,omitempty"`
	Latency string `json:"latency,omitempty"`
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	CPU        CPUMetrics      `json:"cpu"`
	Memory     MemoryMetrics   `json:"memory"`
	Disk       DiskMetrics     `json:"disk"`
	Network    NetworkMetrics  `json:"network"`
	Database   DatabaseMetrics `json:"database"`
	API        APIMetrics      `json:"api"`
	Goroutines int             `json:"goroutines"`
	Timestamp  int64           `json:"timestamp"`
}

// CPUMetrics CPU指标
type CPUMetrics struct {
	Usage     float64 `json:"usage"`
	LoadAvg1  float64 `json:"load_avg_1"`
	LoadAvg5  float64 `json:"load_avg_5"`
	LoadAvg15 float64 `json:"load_avg_15"`
}

// MemoryMetrics 内存指标
type MemoryMetrics struct {
	Total     uint64  `json:"total"`
	Used      uint64  `json:"used"`
	Free      uint64  `json:"free"`
	Usage     float64 `json:"usage"`
	SwapTotal uint64  `json:"swap_total"`
	SwapUsed  uint64  `json:"swap_used"`
}

// DiskMetrics 磁盘指标
type DiskMetrics struct {
	Total uint64  `json:"total"`
	Used  uint64  `json:"used"`
	Free  uint64  `json:"free"`
	Usage float64 `json:"usage"`
}

// NetworkMetrics 网络指标
type NetworkMetrics struct {
	BytesSent   uint64 `json:"bytes_sent"`
	BytesRecv   uint64 `json:"bytes_recv"`
	PacketsSent uint64 `json:"packets_sent"`
	PacketsRecv uint64 `json:"packets_recv"`
}

// DatabaseMetrics 数据库指标
type DatabaseMetrics struct {
	Connections     int   `json:"connections"`
	MaxConnections  int   `json:"max_connections"`
	IdleConnections int   `json:"idle_connections"`
	QueryCount      int64 `json:"query_count"`
	SlowQueryCount  int64 `json:"slow_query_count"`
}

// APIMetrics API指标
type APIMetrics struct {
	RequestCount    int64   `json:"request_count"`
	ErrorCount      int64   `json:"error_count"`
	AvgResponseTime float64 `json:"avg_response_time"`
	P95ResponseTime float64 `json:"p95_response_time"`
	P99ResponseTime float64 `json:"p99_response_time"`
}

// SystemOverview 系统概览
type SystemOverview struct {
	TotalHosts     int64 `json:"total_hosts"`
	OnlineHosts    int64 `json:"online_hosts"`
	OfflineHosts   int64 `json:"offline_hosts"`
	TotalAlerts    int64 `json:"total_alerts"`
	ActiveAlerts   int64 `json:"active_alerts"`
	TotalUsers     int64 `json:"total_users"`
	ActiveUsers    int64 `json:"active_users"`
	TotalSessions  int64 `json:"total_sessions"`
	ActiveSessions int64 `json:"active_sessions"`
	Timestamp      int64 `json:"timestamp"`
}
