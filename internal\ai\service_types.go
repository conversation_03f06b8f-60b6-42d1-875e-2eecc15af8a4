package ai

import "time"

// 在ai包中定义service包的类型，避免循环导入
type ServiceWorkflowIntentAnalysis struct {
	NeedsWorkflow   bool                   `json:"needs_workflow"`
	WorkflowType    string                 `json:"workflow_type"`
	Confidence      float64                `json:"confidence"`
	Intent          string                 `json:"intent"`
	SuggestedAction string                 `json:"suggested_action"`
	Parameters      map[string]interface{} `json:"parameters"`
	Context         string                 `json:"context"`
}

type ServiceWorkflowState struct {
	InstanceID    string                 `json:"instance_id"`
	DefinitionID  string                 `json:"definition_id"`
	CurrentStep   string                 `json:"current_step"`
	Status        string                 `json:"status"`
	Variables     map[string]interface{} `json:"variables"`
	CollectedData map[string]interface{} `json:"collected_data"`
	Progress      float64                `json:"progress"`
}

type ServiceWorkflowGuidance struct {
	Message           string   `json:"message"`
	Suggestions       []string `json:"suggestions"`
	NextAction        string   `json:"next_action"`
	RequiredInputs    []string `json:"required_inputs"`
	ProgressIndicator string   `json:"progress_indicator"`
	HelpText          string   `json:"help_text"`
}

type ServiceProcessMessageRequest struct {
	SessionID string                 `json:"session_id" binding:"required"`
	UserID    int64                  `json:"user_id" binding:"required"`
	Message   string                 `json:"message" binding:"required"`
	Context   map[string]interface{} `json:"context,omitempty"`
}

type ServiceProcessMessageResponse struct {
	Content            string                 `json:"content"`
	Intent             string                 `json:"intent,omitempty"`
	Confidence         float64                `json:"confidence,omitempty"`
	Parameters         map[string]interface{} `json:"parameters,omitempty"`
	ToolCalls          []ServiceToolCallResult       `json:"tool_calls,omitempty"`
	ToolResults        []ServiceToolResult           `json:"tool_results,omitempty"`
	RecognizedActions  []ServiceRecognizedAction     `json:"recognized_actions,omitempty"`
	ActionSuggestions  []string               `json:"action_suggestions,omitempty"`
	WorkflowSuggestion *ServiceWorkflowSuggestion    `json:"workflow_suggestion,omitempty"`
	TokenCount         int                    `json:"token_count,omitempty"`
	ProcessingTime     time.Duration          `json:"processing_time,omitempty"`
	Timestamp          time.Time              `json:"timestamp"`
	Source             string                 `json:"source,omitempty"`
	Success            bool                   `json:"success"`
}

type ServiceConversationContext struct {
	SessionID    string                    `json:"session_id"`
	UserID       int64                     `json:"user_id"`
	Messages     []ServiceConversationMessage     `json:"messages"`
	Variables    map[string]interface{}    `json:"variables"`
	LastActivity time.Time                 `json:"last_activity"`
}

type ServiceConversationMessage struct {
	Role      string                 `json:"role"`
	Content   string                 `json:"content"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

type ServiceIntentResult struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Command    string                 `json:"command,omitempty"`
}

type ServiceToolDefinition struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

type ServiceToolCall struct {
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments"`
}

type ServiceToolResult struct {
	Success bool                   `json:"success"`
	Result  string                 `json:"result"`
	Data    map[string]interface{} `json:"data"`
}

type ServiceToolCallResult struct {
	ID       string `json:"id"`
	Type     string `json:"type"`
	Function struct {
		Name      string `json:"name"`
		Arguments string `json:"arguments"`
	} `json:"function"`
}

type ServiceRecognizedAction struct {
	Type       string                 `json:"type"`
	Parameters map[string]interface{} `json:"parameters"`
	Confidence float64                `json:"confidence"`
}

type ServiceWorkflowSuggestion struct {
	WorkflowType    string                 `json:"workflow_type"`
	SuggestedAction string                 `json:"suggested_action"`
	Parameters      map[string]interface{} `json:"parameters"`
	Confidence      float64                `json:"confidence"`
}

type ServiceGenerateResponseRequest struct {
	Message   string                 `json:"message"`
	Context   map[string]interface{} `json:"context"`
	SessionID string                 `json:"session_id"`
}

type ServiceGenerateResponseResult struct {
	Content   string `json:"content"`
	TokenUsed int    `json:"token_used"`
}

type ServiceConversationSummary struct {
	Summary     string   `json:"summary"`
	KeyPoints   []string `json:"key_points"`
	ActionItems []string `json:"action_items"`
}

type ServiceCommandValidation struct {
	Command     string    `json:"command"`
	IsSafe      bool      `json:"is_safe"`
	RiskLevel   string    `json:"risk_level"`
	Risks       []string  `json:"risks"`
	Suggestions []string  `json:"suggestions"`
	Alternative string    `json:"alternative,omitempty"`
	ValidatedAt time.Time `json:"validated_at"`
}
