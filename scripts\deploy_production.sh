#!/bin/bash

# AI运维管理平台生产环境部署脚本
# 作者: Claude 4.0 sonnet
# 版本: 3.0.0

set -e

echo "=== AI运维管理平台生产环境部署脚本 ==="
echo "版本: 3.0.0"
echo "时间: $(date)"
echo ""

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的环境变量
check_environment() {
    log_info "检查环境变量..."
    
    required_vars=(
        "AIOPS_DEEPSEEK_API_KEY"
        "AIOPS_JWT_SECRET"
        "AIOPS_ENCRYPTION_KEY"
    )
    
    missing_vars=()
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -ne 0 ]; then
        log_error "缺少必要的环境变量:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        echo ""
        echo "请设置这些环境变量后重新运行部署脚本。"
        echo "参考 .env.example 文件了解所需的环境变量。"
        exit 1
    fi
    
    log_success "环境变量检查通过"
}

# 检查系统依赖
check_dependencies() {
    log_info "检查系统依赖..."
    
    # 检查Go版本
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请先安装Go 1.19或更高版本"
        exit 1
    fi
    
    go_version=$(go version | awk '{print $3}' | sed 's/go//')
    log_info "Go版本: $go_version"
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        log_error "Git未安装，请先安装Git"
        exit 1
    fi
    
    # 检查systemctl (用于服务管理)
    if ! command -v systemctl &> /dev/null; then
        log_warning "systemctl未找到，将跳过服务安装"
    fi
    
    log_success "系统依赖检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    directories=(
        "./data"
        "./logs"
        "./cache"
        "./cache/l2"
        "./backups"
        "./config"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    # 设置目录权限
    chmod 755 ./data ./logs ./cache ./backups
    chmod 700 ./config  # 配置目录更严格的权限
    
    log_success "目录创建完成"
}

# 清理测试数据
cleanup_test_data() {
    log_info "清理测试数据..."
    
    if [ -f "./scripts/cleanup_test_data.go" ]; then
        cd scripts
        go run cleanup_test_data.go
        cd ..
        log_success "测试数据清理完成"
    else
        log_warning "清理脚本不存在，跳过测试数据清理"
    fi
}

# 构建应用程序
build_application() {
    log_info "构建应用程序..."
    
    # 设置构建环境
    export CGO_ENABLED=1
    export GOOS=linux
    
    # 构建主程序
    go build -ldflags="-s -w -X main.Version=3.0.0 -X main.BuildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)" -o aiops-platform ./cmd/server
    
    if [ $? -eq 0 ]; then
        log_success "应用程序构建成功"
    else
        log_error "应用程序构建失败"
        exit 1
    fi
    
    # 设置执行权限
    chmod +x aiops-platform
}

# 运行数据库迁移
run_migrations() {
    log_info "运行数据库迁移..."
    
    if [ -f "./cmd/migrate/main.go" ]; then
        go run ./cmd/migrate/main.go
        log_success "数据库迁移完成"
    else
        log_warning "迁移脚本不存在，跳过数据库迁移"
    fi
}

# 创建systemd服务文件
create_systemd_service() {
    if ! command -v systemctl &> /dev/null; then
        log_warning "systemctl不可用，跳过服务创建"
        return
    fi
    
    log_info "创建systemd服务..."
    
    service_file="/etc/systemd/system/aiops-platform.service"
    current_dir=$(pwd)
    current_user=$(whoami)
    
    sudo tee "$service_file" > /dev/null <<EOF
[Unit]
Description=AI运维管理平台
Documentation=https://github.com/your-org/aiops-platform
After=network.target

[Service]
Type=simple
User=$current_user
WorkingDirectory=$current_dir
ExecStart=$current_dir/aiops-platform
ExecReload=/bin/kill -HUP \$MAINPID
KillMode=mixed
KillSignal=SIGTERM
TimeoutStopSec=30
Restart=always
RestartSec=5
StartLimitInterval=0

# 环境变量
Environment=AIOPS_ENV=production
Environment=AIOPS_DEBUG=false

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=$current_dir/data $current_dir/logs $current_dir/cache $current_dir/backups

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target
EOF
    
    # 重新加载systemd配置
    sudo systemctl daemon-reload
    
    log_success "systemd服务创建完成"
}

# 配置防火墙
configure_firewall() {
    log_info "配置防火墙..."
    
    if command -v ufw &> /dev/null; then
        # 使用ufw
        sudo ufw allow 8080/tcp comment "AI运维管理平台"
        sudo ufw allow 9090/tcp comment "AI运维管理平台监控"
        log_success "ufw防火墙配置完成"
    elif command -v firewall-cmd &> /dev/null; then
        # 使用firewalld
        sudo firewall-cmd --permanent --add-port=8080/tcp
        sudo firewall-cmd --permanent --add-port=9090/tcp
        sudo firewall-cmd --reload
        log_success "firewalld防火墙配置完成"
    else
        log_warning "未找到防火墙管理工具，请手动开放端口8080和9090"
    fi
}

# 创建备份脚本
create_backup_script() {
    log_info "创建备份脚本..."
    
    backup_script="./scripts/backup.sh"
    
    cat > "$backup_script" <<'EOF'
#!/bin/bash

# AI运维管理平台备份脚本

BACKUP_DIR="./backups"
TIMESTAMP=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/aiops_backup_$TIMESTAMP.tar.gz"

echo "开始备份..."

# 创建备份目录
mkdir -p "$BACKUP_DIR"

# 停止服务
sudo systemctl stop aiops-platform 2>/dev/null || true

# 创建备份
tar -czf "$BACKUP_FILE" \
    --exclude='./logs/*.log' \
    --exclude='./cache/*' \
    --exclude='./backups/*' \
    ./data ./configs

# 启动服务
sudo systemctl start aiops-platform 2>/dev/null || true

echo "备份完成: $BACKUP_FILE"

# 清理30天前的备份
find "$BACKUP_DIR" -name "aiops_backup_*.tar.gz" -mtime +30 -delete

echo "旧备份清理完成"
EOF
    
    chmod +x "$backup_script"
    log_success "备份脚本创建完成"
}

# 设置定时任务
setup_cron_jobs() {
    log_info "设置定时任务..."
    
    # 添加备份任务（每天凌晨2点）
    (crontab -l 2>/dev/null; echo "0 2 * * * $(pwd)/scripts/backup.sh >> $(pwd)/logs/backup.log 2>&1") | crontab -
    
    log_success "定时任务设置完成"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."
    
    # 检查可执行文件
    if [ ! -f "./aiops-platform" ]; then
        log_error "可执行文件不存在"
        exit 1
    fi
    
    # 检查配置文件
    if [ ! -f "./configs/config.yaml" ]; then
        log_error "配置文件不存在"
        exit 1
    fi
    
    # 检查数据库文件
    if [ ! -f "./data/aiops.db" ]; then
        log_warning "数据库文件不存在，首次运行时将自动创建"
    fi
    
    log_success "部署验证通过"
}

# 启动服务
start_service() {
    log_info "启动服务..."
    
    if command -v systemctl &> /dev/null; then
        sudo systemctl enable aiops-platform
        sudo systemctl start aiops-platform
        
        # 等待服务启动
        sleep 3
        
        if sudo systemctl is-active --quiet aiops-platform; then
            log_success "服务启动成功"
            log_info "服务状态: $(sudo systemctl is-active aiops-platform)"
        else
            log_error "服务启动失败"
            log_info "查看日志: sudo journalctl -u aiops-platform -f"
            exit 1
        fi
    else
        log_info "手动启动应用程序..."
        nohup ./aiops-platform > ./logs/aiops.log 2>&1 &
        echo $! > ./aiops-platform.pid
        log_success "应用程序已在后台启动"
    fi
}

# 显示部署信息
show_deployment_info() {
    echo ""
    echo "=== 部署完成 ==="
    echo ""
    log_success "AI运维管理平台已成功部署到生产环境！"
    echo ""
    echo "📋 部署信息:"
    echo "  • 版本: 3.0.0"
    echo "  • 环境: 生产环境"
    echo "  • 端口: 8080 (主服务), 9090 (监控)"
    echo "  • 数据目录: ./data"
    echo "  • 日志目录: ./logs"
    echo "  • 缓存目录: ./cache"
    echo "  • 备份目录: ./backups"
    echo ""
    echo "🔗 访问地址:"
    echo "  • Web界面: http://localhost:8080"
    echo "  • 监控指标: http://localhost:9090/metrics"
    echo "  • 健康检查: http://localhost:8080/health"
    echo ""
    echo "🛠️ 管理命令:"
    if command -v systemctl &> /dev/null; then
        echo "  • 查看状态: sudo systemctl status aiops-platform"
        echo "  • 启动服务: sudo systemctl start aiops-platform"
        echo "  • 停止服务: sudo systemctl stop aiops-platform"
        echo "  • 重启服务: sudo systemctl restart aiops-platform"
        echo "  • 查看日志: sudo journalctl -u aiops-platform -f"
    else
        echo "  • 查看进程: ps aux | grep aiops-platform"
        echo "  • 停止服务: kill \$(cat aiops-platform.pid)"
        echo "  • 查看日志: tail -f ./logs/aiops.log"
    fi
    echo "  • 手动备份: ./scripts/backup.sh"
    echo ""
    echo "📚 文档和支持:"
    echo "  • 配置文件: ./configs/config.yaml"
    echo "  • 环境变量: .env.example"
    echo "  • 日志文件: ./logs/aiops.log"
    echo ""
    log_success "部署完成！系统已准备好处理生产环境的工作负载。"
}

# 主函数
main() {
    echo "开始生产环境部署..."
    echo ""
    
    check_environment
    check_dependencies
    create_directories
    cleanup_test_data
    build_application
    run_migrations
    create_systemd_service
    configure_firewall
    create_backup_script
    setup_cron_jobs
    verify_deployment
    start_service
    show_deployment_info
}

# 执行主函数
main "$@"
