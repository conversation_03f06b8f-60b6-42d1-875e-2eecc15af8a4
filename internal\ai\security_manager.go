package ai

import (
	"context"
	"crypto/sha256"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// EnhancedSecurityManager 增强安全管理器
type EnhancedSecurityManager struct {
	riskAssessor    *RiskAssessor
	permissionMgr   *PermissionManager
	auditLogger     *EnhancedAuditLogger
	confirmationMgr *ConfirmationManager
	logger          *logrus.Logger
	config          *EnhancedSecurityConfig
}

// EnhancedSecurityConfig 增强安全配置
type EnhancedSecurityConfig struct {
	EnableRiskAssessment  bool          `json:"enable_risk_assessment"`
	EnablePermissionCheck bool          `json:"enable_permission_check"`
	EnableAuditLogging    bool          `json:"enable_audit_logging"`
	RequireConfirmation   bool          `json:"require_confirmation"`
	MaxRiskLevel          string        `json:"max_risk_level"`
	ConfirmationTimeout   time.Duration `json:"confirmation_timeout"`
	AuditRetentionDays    int           `json:"audit_retention_days"`
}

// RiskAssessment 风险评估结果
type RiskAssessment struct {
	Level      string                 `json:"level"`
	Score      float64                `json:"score"`
	Factors    []RiskFactor           `json:"factors"`
	Mitigation []string               `json:"mitigation"`
	Approved   bool                   `json:"approved"`
	AssessedAt time.Time              `json:"assessed_at"`
	Details    map[string]interface{} `json:"details"`
}

// RiskFactor 风险因子
type RiskFactor struct {
	Type        string  `json:"type"`
	Description string  `json:"description"`
	Weight      float64 `json:"weight"`
	Score       float64 `json:"score"`
}

// PermissionCheck 权限检查结果
type PermissionCheck struct {
	Allowed     bool                   `json:"allowed"`
	UserID      int64                  `json:"user_id"`
	Resource    string                 `json:"resource"`
	Action      string                 `json:"action"`
	Permissions []string               `json:"permissions"`
	Reason      string                 `json:"reason"`
	CheckedAt   time.Time              `json:"checked_at"`
	Context     map[string]interface{} `json:"context"`
}

// AuditEntry 审计日志条目
type AuditEntry struct {
	ID         string                 `json:"id"`
	UserID     int64                  `json:"user_id"`
	SessionID  string                 `json:"session_id"`
	Action     string                 `json:"action"`
	Resource   string                 `json:"resource"`
	Intent     string                 `json:"intent"`
	Parameters map[string]interface{} `json:"parameters"`
	Result     string                 `json:"result"`
	RiskLevel  string                 `json:"risk_level"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	Timestamp  time.Time              `json:"timestamp"`
	Duration   time.Duration          `json:"duration"`
}

// ConfirmationRequest 确认请求
type ConfirmationRequest struct {
	ID        string                 `json:"id"`
	UserID    int64                  `json:"user_id"`
	SessionID string                 `json:"session_id"`
	Operation string                 `json:"operation"`
	RiskLevel string                 `json:"risk_level"`
	Details   map[string]interface{} `json:"details"`
	ExpiresAt time.Time              `json:"expires_at"`
	CreatedAt time.Time              `json:"created_at"`
	Status    string                 `json:"status"`
}

// NewEnhancedSecurityManager 创建增强安全管理器
func NewEnhancedSecurityManager(logger *logrus.Logger) *EnhancedSecurityManager {
	config := &EnhancedSecurityConfig{
		EnableRiskAssessment:  true,
		EnablePermissionCheck: true,
		EnableAuditLogging:    true,
		RequireConfirmation:   true,
		MaxRiskLevel:          "high",
		ConfirmationTimeout:   5 * time.Minute,
		AuditRetentionDays:    90,
	}

	return &EnhancedSecurityManager{
		riskAssessor:    NewRiskAssessor(logger),
		permissionMgr:   NewPermissionManager(logger),
		auditLogger:     NewEnhancedAuditLogger(logger),
		confirmationMgr: NewConfirmationManager(logger),
		logger:          logger,
		config:          config,
	}
}

// ValidateOperation 验证操作安全性
func (sm *EnhancedSecurityManager) ValidateOperation(ctx context.Context, req *SecurityValidationRequest) (*SecurityValidationResult, error) {
	sm.logger.WithFields(logrus.Fields{
		"user_id":    req.UserID,
		"session_id": req.SessionID,
		"intent":     req.Intent,
	}).Info("Starting security validation")

	result := &SecurityValidationResult{
		Allowed:     false,
		Timestamp:   time.Now(),
		Validations: make(map[string]interface{}),
	}

	// 1. 风险评估
	if sm.config.EnableRiskAssessment {
		riskAssessment, err := sm.riskAssessor.AssessRisk(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("risk assessment failed: %w", err)
		}
		result.RiskAssessment = riskAssessment
		result.Validations["risk_assessment"] = riskAssessment

		// 检查风险等级是否超过限制
		if !sm.isRiskLevelAcceptable(riskAssessment.Level) {
			result.Reason = fmt.Sprintf("风险等级 %s 超过允许的最大风险等级 %s", riskAssessment.Level, sm.config.MaxRiskLevel)
			return result, nil
		}
	}

	// 2. 权限检查
	if sm.config.EnablePermissionCheck {
		permissionCheck, err := sm.permissionMgr.CheckPermission(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("permission check failed: %w", err)
		}
		result.PermissionCheck = permissionCheck
		result.Validations["permission_check"] = permissionCheck

		if !permissionCheck.Allowed {
			result.Reason = permissionCheck.Reason
			return result, nil
		}
	}

	// 3. 确认检查
	if sm.config.RequireConfirmation && result.RiskAssessment != nil && result.RiskAssessment.Level != "low" {
		confirmationReq, err := sm.confirmationMgr.CreateConfirmationRequest(ctx, req)
		if err != nil {
			return nil, fmt.Errorf("confirmation request creation failed: %w", err)
		}
		result.ConfirmationRequired = true
		result.ConfirmationRequest = confirmationReq
		result.Reason = "需要用户确认才能执行此操作"
		return result, nil
	}

	// 4. 审计日志
	if sm.config.EnableAuditLogging {
		auditEntry := sm.createAuditEntry(req, result)
		if err := sm.auditLogger.LogEntry(ctx, auditEntry); err != nil {
			sm.logger.WithError(err).Warn("Failed to log audit entry")
		}
	}

	result.Allowed = true
	result.Reason = "安全验证通过"
	return result, nil
}

// SecurityValidationRequest 安全验证请求
type SecurityValidationRequest struct {
	UserID     int64                  `json:"user_id"`
	SessionID  string                 `json:"session_id"`
	Intent     string                 `json:"intent"`
	Parameters map[string]interface{} `json:"parameters"`
	RiskLevel  string                 `json:"risk_level"`
	IPAddress  string                 `json:"ip_address"`
	UserAgent  string                 `json:"user_agent"`
	Context    map[string]interface{} `json:"context"`
}

// SecurityValidationResult 安全验证结果
type SecurityValidationResult struct {
	Allowed              bool                   `json:"allowed"`
	Reason               string                 `json:"reason"`
	RiskAssessment       *RiskAssessment        `json:"risk_assessment,omitempty"`
	PermissionCheck      *PermissionCheck       `json:"permission_check,omitempty"`
	ConfirmationRequired bool                   `json:"confirmation_required"`
	ConfirmationRequest  *ConfirmationRequest   `json:"confirmation_request,omitempty"`
	Validations          map[string]interface{} `json:"validations"`
	Timestamp            time.Time              `json:"timestamp"`
}

// isRiskLevelAcceptable 检查风险等级是否可接受
func (sm *EnhancedSecurityManager) isRiskLevelAcceptable(level string) bool {
	riskLevels := map[string]int{
		"low":    1,
		"medium": 2,
		"high":   3,
	}

	currentLevel, exists := riskLevels[level]
	if !exists {
		return false
	}

	maxLevel, exists := riskLevels[sm.config.MaxRiskLevel]
	if !exists {
		return false
	}

	return currentLevel <= maxLevel
}

// createAuditEntry 创建审计日志条目
func (sm *EnhancedSecurityManager) createAuditEntry(req *SecurityValidationRequest, result *SecurityValidationResult) *AuditEntry {
	entryID := fmt.Sprintf("audit_%d_%x", time.Now().UnixNano(), sha256.Sum256([]byte(req.SessionID)))

	return &AuditEntry{
		ID:         entryID,
		UserID:     req.UserID,
		SessionID:  req.SessionID,
		Action:     "security_validation",
		Resource:   req.Intent,
		Intent:     req.Intent,
		Parameters: req.Parameters,
		Result:     fmt.Sprintf("allowed=%t, reason=%s", result.Allowed, result.Reason),
		RiskLevel:  req.RiskLevel,
		IPAddress:  req.IPAddress,
		UserAgent:  req.UserAgent,
		Timestamp:  time.Now(),
	}
}

// RiskAssessor 风险评估器
type RiskAssessor struct {
	logger *logrus.Logger
}

// NewRiskAssessor 创建风险评估器
func NewRiskAssessor(logger *logrus.Logger) *RiskAssessor {
	return &RiskAssessor{logger: logger}
}

// AssessRisk 评估风险
func (ra *RiskAssessor) AssessRisk(ctx context.Context, req *SecurityValidationRequest) (*RiskAssessment, error) {
	factors := []RiskFactor{}
	totalScore := 0.0

	// 评估意图风险
	intentRisk := ra.assessIntentRisk(req.Intent)
	factors = append(factors, intentRisk)
	totalScore += intentRisk.Score * intentRisk.Weight

	// 评估参数风险
	paramRisk := ra.assessParameterRisk(req.Parameters)
	factors = append(factors, paramRisk)
	totalScore += paramRisk.Score * paramRisk.Weight

	// 评估环境风险
	envRisk := ra.assessEnvironmentRisk(req.Context)
	factors = append(factors, envRisk)
	totalScore += envRisk.Score * envRisk.Weight

	// 确定风险等级
	level := "low"
	if totalScore >= 0.7 {
		level = "high"
	} else if totalScore >= 0.4 {
		level = "medium"
	}

	return &RiskAssessment{
		Level:      level,
		Score:      totalScore,
		Factors:    factors,
		Mitigation: ra.generateMitigation(level, factors),
		AssessedAt: time.Now(),
		Details: map[string]interface{}{
			"total_score":  totalScore,
			"factor_count": len(factors),
		},
	}, nil
}

// assessIntentRisk 评估意图风险
func (ra *RiskAssessor) assessIntentRisk(intent string) RiskFactor {
	highRiskIntents := []string{"automation_workflow", "deployment_management", "service_management"}
	mediumRiskIntents := []string{"host_management", "security_audit"}

	score := 0.1 // 默认低风险
	description := "低风险意图"

	for _, highRisk := range highRiskIntents {
		if strings.Contains(intent, highRisk) {
			score = 0.8
			description = "高风险意图：可能影响系统稳定性"
			break
		}
	}

	if score == 0.1 {
		for _, mediumRisk := range mediumRiskIntents {
			if strings.Contains(intent, mediumRisk) {
				score = 0.5
				description = "中等风险意图：需要谨慎操作"
				break
			}
		}
	}

	return RiskFactor{
		Type:        "intent_risk",
		Description: description,
		Weight:      0.4,
		Score:       score,
	}
}

// assessParameterRisk 评估参数风险
func (ra *RiskAssessor) assessParameterRisk(params map[string]interface{}) RiskFactor {
	score := 0.1
	description := "参数风险较低"

	// 检查批量操作
	if ra.containsBatchOperation(params) {
		score = 0.9
		description = "检测到批量操作，风险较高"
	} else if ra.containsProductionEnvironment(params) {
		score = 0.6
		description = "涉及生产环境，风险中等"
	} else if ra.containsSensitiveData(params) {
		score = 0.7
		description = "包含敏感数据，需要保护"
	}

	return RiskFactor{
		Type:        "parameter_risk",
		Description: description,
		Weight:      0.3,
		Score:       score,
	}
}

// assessEnvironmentRisk 评估环境风险
func (ra *RiskAssessor) assessEnvironmentRisk(context map[string]interface{}) RiskFactor {
	score := 0.2
	description := "环境风险正常"

	// 这里可以根据时间、用户行为等评估环境风险
	// 暂时返回默认值

	return RiskFactor{
		Type:        "environment_risk",
		Description: description,
		Weight:      0.3,
		Score:       score,
	}
}

// containsBatchOperation 检查是否包含批量操作
func (ra *RiskAssessor) containsBatchOperation(params map[string]interface{}) bool {
	batchKeywords := []string{"batch", "all", "批量", "所有", "全部"}

	for _, value := range params {
		if str, ok := value.(string); ok {
			for _, keyword := range batchKeywords {
				if strings.Contains(strings.ToLower(str), keyword) {
					return true
				}
			}
		}
	}
	return false
}

// containsProductionEnvironment 检查是否涉及生产环境
func (ra *RiskAssessor) containsProductionEnvironment(params map[string]interface{}) bool {
	prodKeywords := []string{"production", "prod", "生产"}

	for _, value := range params {
		if str, ok := value.(string); ok {
			for _, keyword := range prodKeywords {
				if strings.Contains(strings.ToLower(str), keyword) {
					return true
				}
			}
		}
	}
	return false
}

// containsSensitiveData 检查是否包含敏感数据
func (ra *RiskAssessor) containsSensitiveData(params map[string]interface{}) bool {
	sensitiveKeys := []string{"password", "key", "secret", "token", "密码", "密钥"}

	for key := range params {
		for _, sensitiveKey := range sensitiveKeys {
			if strings.Contains(strings.ToLower(key), sensitiveKey) {
				return true
			}
		}
	}
	return false
}

// generateMitigation 生成缓解措施
func (ra *RiskAssessor) generateMitigation(level string, factors []RiskFactor) []string {
	mitigation := []string{}

	switch level {
	case "high":
		mitigation = append(mitigation, "需要管理员审批", "建议在测试环境先验证", "准备回滚方案")
	case "medium":
		mitigation = append(mitigation, "需要用户确认", "建议备份相关数据")
	case "low":
		mitigation = append(mitigation, "可以直接执行", "建议记录操作日志")
	}

	// 根据具体风险因子添加特定缓解措施
	for _, factor := range factors {
		if factor.Type == "parameter_risk" && factor.Score > 0.7 {
			mitigation = append(mitigation, "验证参数合法性", "限制操作范围")
		}
	}

	return mitigation
}

// PermissionManager 权限管理器
type PermissionManager struct {
	logger *logrus.Logger
}

// NewPermissionManager 创建权限管理器
func NewPermissionManager(logger *logrus.Logger) *PermissionManager {
	return &PermissionManager{logger: logger}
}

// CheckPermission 检查权限
func (pm *PermissionManager) CheckPermission(ctx context.Context, req *SecurityValidationRequest) (*PermissionCheck, error) {
	// 简化的权限检查逻辑
	// 在实际应用中，这里应该查询数据库或权限服务

	allowed := true
	reason := "权限检查通过"
	permissions := []string{"read", "write", "execute"}

	// 基于意图类型的权限检查
	switch req.Intent {
	case "automation_workflow", "deployment_management":
		// 高级操作需要管理员权限
		if req.UserID != 1 { // 假设用户ID 1是管理员
			allowed = false
			reason = "需要管理员权限才能执行自动化工作流"
			permissions = []string{"read"}
		}
	case "host_management":
		// 主机管理需要运维权限
		if req.UserID == 0 {
			allowed = false
			reason = "需要运维权限才能管理主机"
			permissions = []string{"read"}
		}
	}

	return &PermissionCheck{
		Allowed:     allowed,
		UserID:      req.UserID,
		Resource:    req.Intent,
		Action:      "execute",
		Permissions: permissions,
		Reason:      reason,
		CheckedAt:   time.Now(),
		Context: map[string]interface{}{
			"session_id": req.SessionID,
			"ip_address": req.IPAddress,
		},
	}, nil
}

// EnhancedAuditLogger 增强审计日志器
type EnhancedAuditLogger struct {
	logger *logrus.Logger
}

// NewEnhancedAuditLogger 创建增强审计日志器
func NewEnhancedAuditLogger(logger *logrus.Logger) *EnhancedAuditLogger {
	return &EnhancedAuditLogger{logger: logger}
}

// LogEntry 记录审计日志
func (al *EnhancedAuditLogger) LogEntry(ctx context.Context, entry *AuditEntry) error {
	al.logger.WithFields(logrus.Fields{
		"audit_id":   entry.ID,
		"user_id":    entry.UserID,
		"session_id": entry.SessionID,
		"action":     entry.Action,
		"resource":   entry.Resource,
		"result":     entry.Result,
		"risk_level": entry.RiskLevel,
		"ip_address": entry.IPAddress,
	}).Info("Audit log entry")

	// 在实际应用中，这里应该将审计日志保存到数据库或专门的审计系统
	// 这里只是记录到应用日志中

	return nil
}

// ConfirmationManager 确认管理器
type ConfirmationManager struct {
	logger          *logrus.Logger
	pendingRequests map[string]*ConfirmationRequest
	mutex           sync.RWMutex
}

// NewConfirmationManager 创建确认管理器
func NewConfirmationManager(logger *logrus.Logger) *ConfirmationManager {
	return &ConfirmationManager{
		logger:          logger,
		pendingRequests: make(map[string]*ConfirmationRequest),
	}
}

// CreateConfirmationRequest 创建确认请求
func (cm *ConfirmationManager) CreateConfirmationRequest(ctx context.Context, req *SecurityValidationRequest) (*ConfirmationRequest, error) {
	requestID := fmt.Sprintf("confirm_%d_%x", time.Now().UnixNano(), sha256.Sum256([]byte(req.SessionID)))

	confirmReq := &ConfirmationRequest{
		ID:        requestID,
		UserID:    req.UserID,
		SessionID: req.SessionID,
		Operation: req.Intent,
		RiskLevel: req.RiskLevel,
		Details: map[string]interface{}{
			"parameters": req.Parameters,
			"context":    req.Context,
		},
		ExpiresAt: time.Now().Add(5 * time.Minute),
		CreatedAt: time.Now(),
		Status:    "pending",
	}

	cm.mutex.Lock()
	cm.pendingRequests[requestID] = confirmReq
	cm.mutex.Unlock()

	cm.logger.WithFields(logrus.Fields{
		"request_id": requestID,
		"user_id":    req.UserID,
		"session_id": req.SessionID,
		"operation":  req.Intent,
		"risk_level": req.RiskLevel,
	}).Info("Created confirmation request")

	return confirmReq, nil
}

// ProcessConfirmation 处理确认
func (cm *ConfirmationManager) ProcessConfirmation(requestID string, confirmed bool) (*ConfirmationRequest, error) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	req, exists := cm.pendingRequests[requestID]
	if !exists {
		return nil, fmt.Errorf("confirmation request not found: %s", requestID)
	}

	if time.Now().After(req.ExpiresAt) {
		req.Status = "expired"
		delete(cm.pendingRequests, requestID)
		return req, fmt.Errorf("confirmation request expired")
	}

	if confirmed {
		req.Status = "confirmed"
	} else {
		req.Status = "rejected"
	}

	delete(cm.pendingRequests, requestID)

	cm.logger.WithFields(logrus.Fields{
		"request_id": requestID,
		"confirmed":  confirmed,
		"status":     req.Status,
	}).Info("Processed confirmation request")

	return req, nil
}

// GetPendingRequest 获取待确认请求
func (cm *ConfirmationManager) GetPendingRequest(requestID string) (*ConfirmationRequest, bool) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	req, exists := cm.pendingRequests[requestID]
	return req, exists
}

// CleanupExpiredRequests 清理过期请求
func (cm *ConfirmationManager) CleanupExpiredRequests() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	now := time.Now()
	for id, req := range cm.pendingRequests {
		if now.After(req.ExpiresAt) {
			req.Status = "expired"
			delete(cm.pendingRequests, id)
			cm.logger.WithField("request_id", id).Info("Cleaned up expired confirmation request")
		}
	}
}
