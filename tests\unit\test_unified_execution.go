//go:build ignore
// +build ignore

package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/model"
	"aiops-platform/internal/service"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockHostService 模拟主机服务
type MockHostService struct{}

func (m *MockHostService) CreateHost(req *model.HostCreateRequest) (*model.HostResponse, error) {
	return &model.HostResponse{
		ID:        123,
		Name:      req.Name,
		IPAddress: req.IPAddress,
		Username:  req.Username,
		Port:      req.Port,
		Status:    "online",
		CreatedAt: time.Now(),
	}, nil
}

func (m *MockHostService) GetHostByID(id int64) (*model.HostResponse, error) {
	return &model.HostResponse{
		ID:        id,
		Name:      "test-host",
		IPAddress: "*************",
		Status:    "online",
	}, nil
}

func (m *MockHostService) GetHostByName(name string) (*model.HostResponse, error) {
	return &model.HostResponse{
		ID:        1,
		Name:      name,
		IPAddress: "*************",
		Status:    "online",
	}, nil
}

func (m *MockHostService) UpdateHost(id int64, req *model.HostUpdateRequest) (*model.HostResponse, error) {
	return &model.HostResponse{
		ID:        id,
		Name:      "updated-host",
		IPAddress: "*************",
		Status:    "online",
	}, nil
}

func (m *MockHostService) DeleteHost(id int64) error {
	fmt.Printf("MockHostService: Deleting host %d\n", id)
	return nil
}

func (m *MockHostService) ListHosts(req *model.HostListQuery) (*model.HostListResponse, error) {
	hosts := []*model.HostResponse{
		{
			ID:          1,
			Name:        "web-server-01",
			IPAddress:   "*************",
			Status:      "online",
			Environment: "production",
			CreatedAt:   time.Now(),
		},
		{
			ID:          2,
			Name:        "db-server-01",
			IPAddress:   "*************",
			Status:      "offline",
			Environment: "production",
			CreatedAt:   time.Now(),
		},
	}

	return &model.HostListResponse{
		Hosts: hosts,
		Pagination: &model.Pagination{
			Total:    int64(len(hosts)),
			Page:     1,
			PageSize: 10,
		},
	}, nil
}

func (m *MockHostService) TestConnection(id int64) (*model.HostTestResponse, error) {
	return &model.HostTestResponse{
		Success:  true,
		Message:  "Connection successful",
		Duration: 100,
		TestedAt: time.Now(),
	}, nil
}

func (m *MockHostService) ExecuteCommand(id int64, req *model.CommandExecuteRequest) (*model.CommandExecuteResponse, error) {
	return &model.CommandExecuteResponse{
		Command:    req.Command,
		ExitCode:   0,
		Stdout:     "Command executed successfully",
		Stderr:     "",
		Duration:   100,
		ExecutedAt: time.Now(),
	}, nil
}

func (m *MockHostService) GetHostStatus(id int64) (*model.HostResponse, error) {
	return &model.HostResponse{
		ID:     id,
		Status: "online",
	}, nil
}

func (m *MockHostService) UpdateHostStatus(id int64, status string) error {
	fmt.Printf("MockHostService: Updating host %d status to %s\n", id, status)
	return nil
}

func main() {
	// 创建日志器
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 创建配置
	cfg := &config.Config{
		DeepSeek: config.DeepSeekConfig{
			APIKey:     "sk-test-key",
			APIURL:     "https://api.deepseek.com/v1",
			Model:      "deepseek-chat",
			Timeout:    30 * time.Second,
			MaxRetries: 3,
		},
	}

	// 创建模拟主机服务
	hostService := &MockHostService{}

	// 创建增强AI服务
	aiService := service.NewEnhancedAIService(db, cfg, logger, hostService)

	fmt.Println("🚀 统一执行引擎测试开始")
	fmt.Println("====================================================")

	// 测试用例1：查看主机列表
	fmt.Println("\n📊 测试用例1：查看主机列表")
	testProcessMessage(aiService, "查看所有主机", 1)

	// 测试用例2：添加主机
	fmt.Println("\n🖥️ 测试用例2：添加主机")
	testProcessMessage(aiService, "添加主机 ************* root password123", 1)

	// 测试用例3：通用对话
	fmt.Println("\n💬 测试用例3：通用对话")
	testProcessMessage(aiService, "你好", 1)

	// 测试用例4：帮助信息
	fmt.Println("\n❓ 测试用例4：帮助信息")
	testProcessMessage(aiService, "帮助", 1)

	fmt.Println("\n====================================================")
	fmt.Println("✅ 统一执行引擎测试完成")
}

func testProcessMessage(aiService *service.EnhancedAIService, message string, userID int64) {
	ctx := context.Background()

	req := &service.ProcessMessageRequest{
		SessionID: fmt.Sprintf("test-session-%d", time.Now().UnixNano()),
		UserID:    userID,
		Message:   message,
	}

	fmt.Printf("📝 输入: %s\n", message)

	start := time.Now()
	response, err := aiService.ProcessMessage(ctx, req)
	duration := time.Since(start)

	if err != nil {
		fmt.Printf("❌ 错误: %s\n", err.Error())
		return
	}

	fmt.Printf("🎯 意图: %s (置信度: %.2f)\n", response.Intent, response.Confidence)
	fmt.Printf("⏱️ 处理时间: %v\n", duration)
	fmt.Printf("📄 响应内容:\n%s\n", response.Content)

	if len(response.ActionSuggestions) > 0 {
		fmt.Printf("💡 操作建议:\n")
		for _, suggestion := range response.ActionSuggestions {
			fmt.Printf("  • %s\n", suggestion)
		}
	}

	if response.Parameters != nil {
		if confirmToken, exists := response.Parameters["confirm_token"]; exists {
			fmt.Printf("🔐 确认令牌: %s\n", confirmToken)
		}
	}

	fmt.Println()
}
