//go:build ignore
// +build ignore

package main

import (
	"fmt"
	"log"

	"aiops-platform/internal/config"
	"aiops-platform/internal/handler"
	"aiops-platform/internal/middleware"
	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🚀 启动统一执行引擎集成测试服务器")
	fmt.Println("====================================================")

	// 创建日志器
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 创建配置
	cfg := &config.Config{
		DeepSeek: config.DeepSeekConfig{
			APIKey:     "sk-test-key",
			APIURL:     "https://api.deepseek.com/v1",
			Model:      "deepseek-chat",
			Timeout:    30,
			MaxRetries: 3,
		},
	}

	// 创建主机服务（使用现有的实现）
	hostService := service.NewHostService(db, cfg, logger)

	// 创建增强AI服务
	aiService := service.NewEnhancedAIService(db, cfg, logger, hostService)

	// 创建增强AI处理器
	aiHandler := handler.NewEnhancedAIHandler(aiService, logger)

	// 创建Gin路由器
	gin.SetMode(gin.ReleaseMode)
	router := gin.New()

	// 添加中间件
	router.Use(gin.Recovery())
	router.Use(middleware.CORSMiddleware())
	router.Use(middleware.LoggerMiddleware(logger))

	// 模拟用户认证中间件
	router.Use(func(c *gin.Context) {
		c.Set("user_id", int64(1)) // 模拟用户ID
		c.Next()
	})

	// 注册路由
	api := router.Group("/api/v1")
	aiHandler.RegisterRoutes(api)

	// 添加健康检查
	router.GET("/health", func(c *gin.Context) {
		c.JSON(200, gin.H{
			"status":  "healthy",
			"service": "unified-execution-engine",
			"version": "1.0.0",
		})
	})

	// 添加测试页面
	router.GET("/", func(c *gin.Context) {
		html := `
<!DOCTYPE html>
<html>
<head>
    <title>统一执行引擎测试</title>
    <meta charset="utf-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .container { max-width: 800px; margin: 0 auto; }
        .test-section { margin: 20px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #0056b3; }
        .result { margin-top: 10px; padding: 10px; background: #f8f9fa; border-radius: 4px; white-space: pre-wrap; }
        input[type="text"] { width: 100%; padding: 8px; margin: 5px 0; border: 1px solid #ddd; border-radius: 4px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 统一执行引擎测试界面</h1>
        
        <div class="test-section">
            <h3>💬 AI对话测试</h3>
            <input type="text" id="messageInput" placeholder="输入您的消息，例如：查看所有主机" value="查看所有主机">
            <button onclick="sendMessage()">发送消息</button>
            <div id="messageResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>📊 系统状态</h3>
            <button onclick="getSystemStatus()">获取系统状态</button>
            <div id="statusResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🔐 待确认操作</h3>
            <button onclick="getPendingConfirmations()">获取待确认操作</button>
            <div id="confirmationsResult" class="result"></div>
        </div>

        <div class="test-section">
            <h3>🧪 预设测试用例</h3>
            <button onclick="testCase('查看所有主机')">测试：查看主机列表</button>
            <button onclick="testCase('添加主机 ************* root password123')">测试：添加主机</button>
            <button onclick="testCase('你好')">测试：问候对话</button>
            <button onclick="testCase('帮助')">测试：帮助信息</button>
            <div id="testResult" class="result"></div>
        </div>
    </div>

    <script>
        async function sendMessage() {
            const message = document.getElementById('messageInput').value;
            const result = document.getElementById('messageResult');
            
            try {
                const response = await fetch('/api/v1/ai/enhanced/message', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        session_id: 'test-session-' + Date.now(),
                        message: message
                    })
                });
                
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                result.textContent = 'Error: ' + error.message;
            }
        }

        async function getSystemStatus() {
            const result = document.getElementById('statusResult');
            
            try {
                const response = await fetch('/api/v1/ai/enhanced/status');
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                result.textContent = 'Error: ' + error.message;
            }
        }

        async function getPendingConfirmations() {
            const result = document.getElementById('confirmationsResult');
            
            try {
                const response = await fetch('/api/v1/ai/enhanced/confirmations');
                const data = await response.json();
                result.textContent = JSON.stringify(data, null, 2);
            } catch (error) {
                result.textContent = 'Error: ' + error.message;
            }
        }

        async function testCase(message) {
            const result = document.getElementById('testResult');
            
            try {
                const response = await fetch('/api/v1/ai/enhanced/message', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        session_id: 'test-case-' + Date.now(),
                        message: message
                    })
                });
                
                const data = await response.json();
                result.textContent = '测试用例: ' + message + '\n\n' + JSON.stringify(data, null, 2);
            } catch (error) {
                result.textContent = 'Error: ' + error.message;
            }
        }
    </script>
</body>
</html>`
		c.Header("Content-Type", "text/html; charset=utf-8")
		c.String(200, html)
	})

	fmt.Println("✅ 服务器配置完成")
	fmt.Println("📡 服务器地址: http://localhost:8081")
	fmt.Println("🔗 测试页面: http://localhost:8081")
	fmt.Println("🔗 健康检查: http://localhost:8081/health")
	fmt.Println("🔗 API端点: http://localhost:8081/api/v1/ai/enhanced/")
	fmt.Println("====================================================")

	// 启动服务器
	if err := router.Run(":8081"); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}
