/* ========================================
   动画控制器
   管理页面所有动画和微交互
   ======================================== */

class AnimationController {
    constructor() {
        this.isReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;
        this.observers = new Map();
        this.animationQueue = [];
        this.isAnimating = false;
        
        this.init();
    }
    
    init() {
        this.setupScrollReveal();
        this.setupPageTransitions();
        this.setupInteractiveElements();
        this.setupLoadingAnimations();
        this.bindEvents();
        
        console.log('🎬 动画控制器已初始化');
    }
    
    // 页面加载动画
    setupPageTransitions() {
        if (this.isReducedMotion) return;
        
        // 页面加载时的入场动画
        this.animatePageLoad();
        
        // 监听页面可见性变化
        document.addEventListener('visibilitychange', () => {
            if (!document.hidden) {
                this.refreshAnimations();
            }
        });
    }
    
    animatePageLoad() {
        const elements = [
            { selector: '.app-navbar', animation: 'navbar-enter', delay: 0 },
            { selector: '.sidebar', animation: 'sidebar-enter', delay: 100 },
            { selector: '.chat-main', animation: 'page-transition-enter', delay: 200 },
            { selector: '.assistant-panel', animation: 'assistant-panel-enter', delay: 300 },
            { selector: '.chat-input-area', animation: 'chat-input-enter', delay: 400 }
        ];
        
        elements.forEach(({ selector, animation, delay }) => {
            const element = document.querySelector(selector);
            if (element) {
                setTimeout(() => {
                    element.classList.add(animation);
                }, delay);
            }
        });
    }
    
    // 滚动显示动画
    setupScrollReveal() {
        if (this.isReducedMotion) return;
        
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('revealed');
                    observer.unobserve(entry.target);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        });
        
        // 观察所有需要滚动显示的元素
        document.querySelectorAll('.scroll-reveal').forEach(el => {
            observer.observe(el);
        });
        
        this.observers.set('scrollReveal', observer);
    }
    
    // 交互元素动画
    setupInteractiveElements() {
        // 为按钮添加交互效果
        this.enhanceButtons();
        
        // 为卡片添加悬停效果
        this.enhanceCards();
        
        // 为输入框添加聚焦效果
        this.enhanceInputs();
    }
    
    enhanceButtons() {
        const buttons = document.querySelectorAll('button, .btn, .action-btn');
        buttons.forEach(button => {
            if (!button.classList.contains('btn-interactive')) {
                button.classList.add('btn-interactive');
            }
            
            // 添加点击波纹效果
            button.addEventListener('click', (e) => {
                this.createRippleEffect(e, button);
            });
        });
    }
    
    enhanceCards() {
        const cards = document.querySelectorAll('.card, .message, .chat-item, .feature-card');
        cards.forEach(card => {
            if (!card.classList.contains('card-interactive')) {
                card.classList.add('card-interactive');
            }
        });
    }
    
    enhanceInputs() {
        const inputs = document.querySelectorAll('input, textarea, .input-wrapper');
        inputs.forEach(input => {
            if (!input.classList.contains('input-interactive')) {
                input.classList.add('input-interactive');
            }
        });
    }
    
    // 波纹效果
    createRippleEffect(event, element) {
        if (this.isReducedMotion) return;
        
        const ripple = document.createElement('span');
        const rect = element.getBoundingClientRect();
        const size = Math.max(rect.width, rect.height);
        const x = event.clientX - rect.left - size / 2;
        const y = event.clientY - rect.top - size / 2;
        
        ripple.style.cssText = `
            position: absolute;
            width: ${size}px;
            height: ${size}px;
            left: ${x}px;
            top: ${y}px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 50%;
            transform: scale(0);
            animation: ripple 0.6s ease-out;
            pointer-events: none;
            z-index: 1000;
        `;
        
        element.style.position = 'relative';
        element.style.overflow = 'hidden';
        element.appendChild(ripple);
        
        setTimeout(() => {
            if (ripple.parentNode) {
                ripple.parentNode.removeChild(ripple);
            }
        }, 600);
    }
    
    // 加载动画
    setupLoadingAnimations() {
        this.createLoadingSpinner();
        this.createSkeletonScreens();
    }
    
    createLoadingSpinner() {
        const style = document.createElement('style');
        style.textContent = `
            @keyframes ripple {
                0% { transform: scale(0); opacity: 1; }
                100% { transform: scale(4); opacity: 0; }
            }
        `;
        document.head.appendChild(style);
    }
    
    createSkeletonScreens() {
        // 为消息创建骨架屏
        this.messageSkeletonTemplate = `
            <div class="message skeleton-message">
                <div class="skeleton skeleton-avatar"></div>
                <div class="message-content">
                    <div class="skeleton skeleton-text"></div>
                    <div class="skeleton skeleton-text"></div>
                    <div class="skeleton skeleton-text" style="width: 60%;"></div>
                </div>
            </div>
        `;
    }
    
    // 消息动画
    animateMessage(messageElement, type = 'assistant') {
        if (this.isReducedMotion) {
            return Promise.resolve();
        }
        
        return new Promise(resolve => {
            messageElement.classList.add(`message-${type}-enter`);
            
            // 如果是AI消息，添加打字机效果
            if (type === 'assistant') {
                this.addTypingEffect(messageElement);
            }
            
            setTimeout(resolve, 300);
        });
    }
    
    addTypingEffect(messageElement) {
        const content = messageElement.querySelector('.message-content');
        if (!content) return;
        
        const text = content.textContent;
        content.textContent = '';
        content.classList.add('typewriter');
        
        let i = 0;
        const typeInterval = setInterval(() => {
            content.textContent += text.charAt(i);
            i++;
            
            if (i >= text.length) {
                clearInterval(typeInterval);
                content.classList.remove('typewriter');
            }
        }, 30);
    }
    
    // 显示加载状态
    showLoading(container, type = 'spinner') {
        if (!container) return null;
        
        const loadingElement = document.createElement('div');
        loadingElement.className = 'loading-container';
        
        if (type === 'spinner') {
            loadingElement.innerHTML = '<div class="loading-spinner"></div>';
        } else if (type === 'dots') {
            loadingElement.innerHTML = `
                <div class="loading-dots">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
            `;
        } else if (type === 'skeleton') {
            loadingElement.innerHTML = this.messageSkeletonTemplate;
        }
        
        container.appendChild(loadingElement);
        return loadingElement;
    }
    
    hideLoading(loadingElement) {
        if (!loadingElement) return;
        
        if (this.isReducedMotion) {
            loadingElement.remove();
            return;
        }
        
        loadingElement.style.transition = 'opacity 0.3s ease-out';
        loadingElement.style.opacity = '0';
        
        setTimeout(() => {
            if (loadingElement.parentNode) {
                loadingElement.parentNode.removeChild(loadingElement);
            }
        }, 300);
    }
    
    // 通知动画
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // 设置样式
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: var(--color-${type === 'error' ? 'error' : type === 'success' ? 'success' : 'info'});
            color: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 10000;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        document.body.appendChild(notification);
        
        if (!this.isReducedMotion) {
            notification.classList.add('notification-enter');
        }
        
        // 自动移除
        setTimeout(() => {
            this.hideNotification(notification);
        }, duration);
        
        return notification;
    }
    
    hideNotification(notification) {
        if (!notification || !notification.parentNode) return;
        
        if (this.isReducedMotion) {
            notification.remove();
            return;
        }
        
        notification.classList.add('notification-exit');
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }
    
    // 模态框动画
    showModal(modalElement) {
        if (!modalElement) return;
        
        modalElement.style.display = 'block';
        
        if (!this.isReducedMotion) {
            modalElement.classList.add('modal-enter');
        }
        
        // 添加背景遮罩动画
        const backdrop = modalElement.querySelector('.modal-backdrop');
        if (backdrop && !this.isReducedMotion) {
            backdrop.classList.add('modal-backdrop-enter');
        }
    }
    
    hideModal(modalElement) {
        if (!modalElement) return;
        
        if (this.isReducedMotion) {
            modalElement.style.display = 'none';
            return;
        }
        
        modalElement.style.transition = 'opacity 0.3s ease-out';
        modalElement.style.opacity = '0';
        
        setTimeout(() => {
            modalElement.style.display = 'none';
            modalElement.style.opacity = '';
            modalElement.style.transition = '';
        }, 300);
    }
    
    // 粒子效果
    createParticleEffect(container, count = 20) {
        if (this.isReducedMotion || !container) return;
        
        container.classList.add('particle-container');
        
        for (let i = 0; i < count; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = Math.random() * 100 + '%';
            particle.style.animationDelay = Math.random() * 6 + 's';
            particle.style.animationDuration = (Math.random() * 3 + 3) + 's';
            
            container.appendChild(particle);
        }
    }
    
    // 事件绑定
    bindEvents() {
        // 监听减少动画偏好变化
        const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        mediaQuery.addListener((e) => {
            this.isReducedMotion = e.matches;
            if (this.isReducedMotion) {
                this.disableAllAnimations();
            }
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            this.refreshAnimations();
        });
    }
    
    // 刷新动画
    refreshAnimations() {
        // 重新设置滚动显示
        this.setupScrollReveal();
        
        // 重新增强交互元素
        this.enhanceButtons();
        this.enhanceCards();
        this.enhanceInputs();
    }
    
    // 禁用所有动画
    disableAllAnimations() {
        const style = document.createElement('style');
        style.textContent = `
            *, *::before, *::after {
                animation-duration: 0.01ms !important;
                animation-iteration-count: 1 !important;
                transition-duration: 0.01ms !important;
            }
        `;
        document.head.appendChild(style);
    }
    
    // 清理资源
    destroy() {
        this.observers.forEach(observer => observer.disconnect());
        this.observers.clear();
    }
}

// 全局动画控制器实例
window.animationController = new AnimationController();
