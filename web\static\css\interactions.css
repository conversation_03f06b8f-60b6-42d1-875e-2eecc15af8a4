/* AI运维管理平台 - 现代化交互系统 V2.0 */
/* 参考顶级应用的微交互和动画效果 */

/* ========================================
   全局交互基础
   ======================================== */

/* 硬件加速优化 */
.btn,
.card,
.form-control,
.nav-link,
.badge {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* ========================================
   按钮微交互系统
   ======================================== */

.btn {
  position: relative;
  overflow: hidden;
  transition: all var(--transition-base);
}

/* 涟漪效果 */
.btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  transform: translate(-50%, -50%);
  transition: width var(--duration-500) var(--ease-out), height var(--duration-500) var(--ease-out);
  z-index: 0;
  pointer-events: none;
}

.btn:hover::before {
  width: 300px;
  height: 300px;
}

/* 按钮按下效果 */
.btn:active {
  transform: scale(0.98) translateZ(0);
  transition: transform var(--duration-100) var(--ease-out);
}

/* 特殊按钮效果 */
.btn-primary::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left var(--duration-700) var(--ease-out);
}

.btn-primary:hover::after {
  left: 100%;
}

/* ========================================
   卡片交互系统
   ======================================== */

.card {
  transition: all var(--transition-base);
  position: relative;
}

.card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(99, 102, 241, 0.05) 0%, rgba(217, 70, 239, 0.05) 100%);
  opacity: 0;
  transition: opacity var(--transition-base);
  border-radius: inherit;
  pointer-events: none;
}

.card:hover::before {
  opacity: 1;
}

.card:hover {
  transform: translateY(-4px) scale(1.01);
  box-shadow: var(--shadow-xl);
  border-color: var(--color-primary-200);
}

/* 卡片内容动画 */
.card:hover .card-header,
.card:hover .card-body,
.card:hover .card-footer {
  transform: translateY(-1px);
  transition: transform var(--transition-base);
}

/* ========================================
   表单交互系统
   ======================================== */

.form-control {
  position: relative;
  transition: all var(--transition-base);
}

/* 输入框焦点效果 */
.form-control:focus {
  transform: translateY(-1px) scale(1.005);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow-md);
  border-color: var(--color-primary-500);
}

/* 输入框标签动画 */
.form-group {
  position: relative;
}

.form-label {
  transition: all var(--transition-base);
}

.form-control:focus + .form-label,
.form-control:not(:placeholder-shown) + .form-label {
  transform: translateY(-2px);
  color: var(--color-primary-600);
  font-weight: var(--font-weight-semibold);
}

/* 表单验证动画 */
.form-control.is-valid {
  animation: success-pulse var(--duration-500) var(--ease-out);
}

.form-control.is-invalid {
  animation: error-shake var(--duration-500) var(--ease-out);
}

@keyframes success-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(34, 197, 94, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
  }
}

@keyframes error-shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-4px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(4px);
  }
}

/* ========================================
   加载状态动画
   ======================================== */

/* 脉冲加载效果 */
.loading-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 骨架屏加载 */
.skeleton {
  background: linear-gradient(90deg, var(--color-gray-200) 25%, var(--color-gray-100) 50%, var(--color-gray-200) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* 旋转加载器 */
.loading-spinner-modern {
  width: 24px;
  height: 24px;
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-primary-500);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  display: inline-block;
}

.loading-spinner-sm {
  width: 16px;
  height: 16px;
  border-width: 2px;
}

.loading-spinner-lg {
  width: 32px;
  height: 32px;
  border-width: 4px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 点状加载器 */
.loading-dots-modern {
  display: inline-flex;
  gap: var(--spacing-1);
  align-items: center;
}

.loading-dots-modern .dot {
  width: 6px;
  height: 6px;
  background-color: var(--color-primary-500);
  border-radius: 50%;
  animation: dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dots-modern .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots-modern .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots-modern .dot:nth-child(3) { animation-delay: 0s; }

@keyframes dot-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* ========================================
   状态反馈动画
   ======================================== */

/* 成功状态 */
.success-feedback {
  animation: success-bounce 0.6s ease-out;
}

@keyframes success-bounce {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 错误状态 */
.error-feedback {
  animation: error-shake 0.6s ease-in-out;
}

@keyframes error-shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-10px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(10px);
  }
}

/* 警告状态 */
.warning-feedback {
  animation: warning-pulse 1s ease-in-out infinite;
}

@keyframes warning-pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(245, 158, 11, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(245, 158, 11, 0);
  }
}

/* ========================================
   页面过渡动画
   ======================================== */

/* 淡入动画 */
.fade-in {
  animation: fadeIn 0.5s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 滑入动画 */
.slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.slide-in-left {
  animation: slideInLeft 0.5s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-100px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 缩放动画 */
.scale-in {
  animation: scaleIn 0.3s ease-out;
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* ========================================
   悬停状态增强
   ======================================== */

/* 链接悬停效果 */
.link-modern {
  position: relative;
  text-decoration: none;
  transition: color var(--transition-base);
}

.link-modern::after {
  content: '';
  position: absolute;
  width: 0;
  height: 2px;
  bottom: -2px;
  left: 0;
  background-color: var(--color-primary-500);
  transition: width var(--transition-base);
}

.link-modern:hover::after {
  width: 100%;
}

/* 图标悬停效果 */
.icon-hover {
  transition: all var(--transition-base);
}

.icon-hover:hover {
  transform: scale(1.1) rotate(5deg);
  color: var(--color-primary-500);
}

/* 徽章悬停效果 */
.badge-modern {
  transition: all var(--transition-base);
  cursor: pointer;
}

.badge-modern:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

/* ========================================
   焦点状态增强
   ======================================== */

/* 自定义焦点环 */
.focus-ring {
  transition: box-shadow var(--transition-base);
}

.focus-ring:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.5);
}

/* 键盘导航增强 */
.keyboard-nav:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* ========================================
   响应式动画控制
   ======================================== */

/* 减少动画（用户偏好） */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* 移动端优化 */
@media (max-width: 768px) {
  .card-modern:hover,
  .card:hover {
    transform: none;
    box-shadow: var(--shadow-md);
  }
  
  .btn-modern:hover::before,
  .btn:hover::before {
    width: 0;
    height: 0;
  }
}
