<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI对话 - AI对话运维管理平台</title>
    <link href="/static/css/design-system.css" rel="stylesheet">
    <link href="/static/css/interactions.css" rel="stylesheet">
    <link href="/static/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/fonts.css" rel="stylesheet">
    <link href="/static/css/main.css" rel="stylesheet">
    <style>
        .chat-container {
            height: calc(100vh - 200px);
            display: flex;
            flex-direction: column;
        }
        
        .chat-messages {
            flex: 1;
            overflow-y: auto;
            padding: 1rem;
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem 0.375rem 0 0;
        }
        
        .message {
            margin-bottom: 1rem;
            display: flex;
            align-items: flex-start;
        }
        
        .message.user {
            justify-content: flex-end;
        }
        
        .message.assistant {
            justify-content: flex-start;
        }
        
        .message-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            margin: 0 0.5rem;
        }
        
        .message.user .message-avatar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            order: 2;
        }
        
        .message.assistant .message-avatar {
            background: #28a745;
            color: white;
            order: 1;
        }
        
        .message-content {
            max-width: 70%;
            padding: 0.75rem 1rem;
            border-radius: 1rem;
            word-wrap: break-word;
            position: relative;
        }
        
        .message.user .message-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-bottom-right-radius: 0.25rem;
            order: 1;
        }
        
        .message.assistant .message-content {
            background: white;
            color: #333;
            border: 1px solid #dee2e6;
            border-bottom-left-radius: 0.25rem;
            order: 2;
        }
        
        .message-time {
            font-size: 0.75rem;
            color: #6c757d;
            margin-top: 0.25rem;
            text-align: center;
        }
        
        .chat-input {
            border-top: 1px solid #dee2e6;
            background: white;
            padding: 1rem;
            border-radius: 0 0 0.375rem 0.375rem;
        }
        
        .typing-indicator {
            display: none;
            padding: 0.5rem 1rem;
            background: #f8f9fa;
            border-radius: 1rem;
            margin: 0.5rem 0;
        }

        .action-buttons {
            margin-top: 0.5rem;
            padding-top: 0.5rem;
            border-top: 1px solid #e9ecef;
        }

        .action-btn {
            font-size: 0.875rem;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
            transition: all 0.2s ease-in-out;
        }

        .action-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }

        .action-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .command-code {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin: 0.5rem 0;
            font-family: 'Courier New', monospace;
            font-size: 0.875rem;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        
        .typing-dots {
            display: inline-block;
        }
        
        .typing-dots span {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #999;
            margin: 0 2px;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .typing-dots span:nth-child(1) { animation-delay: -0.32s; }
        .typing-dots span:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
        
        .session-sidebar {
            background: white;
            border-right: 1px solid #dee2e6;
            height: calc(100vh - 120px);
            overflow-y: auto;
        }
        
        .session-item {
            padding: 0.75rem 1rem;
            border-bottom: 1px solid #f8f9fa;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        
        .session-item:hover {
            background-color: #f8f9fa;
        }
        
        .session-item.active {
            background-color: #e3f2fd;
            border-left: 3px solid #2196f3;
        }
        
        .command-suggestion {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin: 0.5rem 0;
        }
        
        .command-code {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.25rem;
            padding: 0.5rem;
            font-family: 'Courier New', monospace;
            margin: 0.5rem 0;
        }
        
        .execute-btn {
            font-size: 0.875rem;
            padding: 0.25rem 0.5rem;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-robot"></i>
                AI运维平台
            </a>
            <div class="navbar-nav me-auto">
                <a class="nav-link" href="/dashboard">
                    <i class="bi bi-speedometer2"></i>
                    仪表板
                </a>
                <a class="nav-link" href="/hosts">
                    <i class="bi bi-server"></i>
                    主机管理
                </a>
                <a class="nav-link active" href="/chat">
                    <i class="bi bi-chat-dots"></i>
                    AI对话
                </a>
            </div>
            <div class="navbar-nav">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i>
                        <span id="nav-username">用户</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-3">
        <div class="row">
            <!-- 会话侧边栏 -->
            <div class="col-md-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="bi bi-chat-square-text"></i>
                            对话会话
                        </h6>
                        <button class="btn btn-sm btn-primary" onclick="createNewSession()">
                            <i class="bi bi-plus"></i>
                        </button>
                    </div>
                    <div class="session-sidebar" id="session-list">
                        <div class="text-center p-3">
                            <div class="loading"></div>
                            <div class="mt-2">加载中...</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 对话区域 -->
            <div class="col-md-9">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0" id="chat-title">
                            <i class="bi bi-robot"></i>
                            AI运维助手
                        </h6>
                        <div>
                            <button class="btn btn-sm btn-outline-secondary" onclick="clearChat()">
                                <i class="bi bi-trash"></i>
                                清空对话
                            </button>
                        </div>
                    </div>
                    
                    <div class="chat-container">
                        <div class="chat-messages" id="chat-messages">
                            <div class="message assistant">
                                <div class="message-avatar">
                                    <i class="bi bi-robot"></i>
                                </div>
                                <div class="message-content">
                                    <div>您好！我是AI运维助手，可以帮助您管理服务器和执行运维操作。</div>
                                    <div class="mt-2">
                                        <strong>我可以帮您：</strong>
                                        <ul class="mb-0 mt-1">
                                            <li>查看服务器状态和系统信息</li>
                                            <li>执行Linux命令和脚本</li>
                                            <li>监控系统资源使用情况</li>
                                            <li>管理服务和进程</li>
                                            <li>分析日志和排查问题</li>
                                        </ul>
                                    </div>
                                    <div class="mt-3">
                                        <strong>💡 智能提示：</strong>
                                        <div class="mt-2">
                                            <div class="alert alert-info mb-2" style="font-size: 0.9rem;">
                                                <i class="bi bi-info-circle me-2"></i>
                                                您可以用自然语言描述需求，我会智能理解并执行相应操作。支持连续对话，我会记住上下文信息。
                                            </div>
                                        </div>
                                    </div>
                                    <div class="mt-2">
                                        <strong>🚀 快速开始：</strong>
                                        <div class="mt-2 d-flex flex-wrap gap-2">
                                            <span class="badge bg-primary text-white px-3 py-2" style="cursor: pointer; font-size: 0.8rem;" onclick="sendQuickMessage(this.textContent)">查看所有主机状态</span>
                                            <span class="badge bg-success text-white px-3 py-2" style="cursor: pointer; font-size: 0.8rem;" onclick="sendQuickMessage(this.textContent)">检查磁盘使用情况</span>
                                            <span class="badge bg-warning text-dark px-3 py-2" style="cursor: pointer; font-size: 0.8rem;" onclick="sendQuickMessage(this.textContent)">显示最近告警</span>
                                            <span class="badge bg-info text-white px-3 py-2" style="cursor: pointer; font-size: 0.8rem;" onclick="sendQuickMessage(this.textContent)">生成系统报表</span>
                                        </div>
                                    </div>
                                    <div class="mt-3">
                                        <strong>📋 常用操作模板：</strong>
                                        <div class="mt-2">
                                            <div class="operation-templates">
                                                <div class="template-item" onclick="sendQuickMessage('在web-server-01上执行 ps aux | grep nginx')">
                                                    <i class="bi bi-terminal"></i>
                                                    <span>在指定主机执行命令</span>
                                                </div>
                                                <div class="template-item" onclick="sendQuickMessage('检查所有主机的CPU和内存使用率')">
                                                    <i class="bi bi-cpu"></i>
                                                    <span>批量检查资源使用</span>
                                                </div>
                                                <div class="template-item" onclick="sendQuickMessage('显示过去24小时的告警统计')">
                                                    <i class="bi bi-graph-up"></i>
                                                    <span>查看告警统计</span>
                                                </div>
                                                <div class="template-item" onclick="sendQuickMessage('重启web-server-01上的nginx服务')">
                                                    <i class="bi bi-arrow-clockwise"></i>
                                                    <span>服务管理操作</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="message-time">刚刚</div>
                                </div>
                            </div>
                            
                            <div class="typing-indicator" id="typing-indicator">
                                <div class="message assistant">
                                    <div class="message-avatar">
                                        <i class="bi bi-robot"></i>
                                    </div>
                                    <div class="message-content">
                                        <div class="typing-dots">
                                            <span></span>
                                            <span></span>
                                            <span></span>
                                        </div>
                                        AI正在思考...
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="chat-input">
                            <form id="chat-form">
                                <div class="input-group">
                                    <input type="text" class="form-control" id="message-input" 
                                           placeholder="输入您的问题或命令..." autocomplete="off">
                                    <button class="btn btn-primary" type="submit" id="send-btn">
                                        <i class="bi bi-send"></i>
                                        发送
                                    </button>
                                </div>
                            </form>
                            <div class="mt-2">
                                <small class="text-muted">
                                    <i class="bi bi-lightbulb"></i>
                                    提示：您可以直接说"查看服务器状态"、"重启nginx服务"等自然语言命令
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/static/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script>
        let currentSessionId = null;
        let websocket = null;

        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            if (!localStorage.getItem('access_token')) {
                window.location.href = '/login';
                return;
            }

            initializePage();
            loadSessions();
            setupWebSocket();
            setupChatForm();
        });

        function initializePage() {
            const username = localStorage.getItem('username') || '用户';
            document.getElementById('nav-username').textContent = username;
        }

        async function loadSessions() {
            try {
                const response = await API.get('/chat/sessions');
                const data = await response.json();

                const sessionList = document.getElementById('session-list');
                
                if (data.code === 200 && data.data.sessions && data.data.sessions.length > 0) {
                    sessionList.innerHTML = data.data.sessions.map(session => `
                        <div class="session-item" onclick="selectSession('${session.session_id}', '${session.title}')">
                            <div class="fw-bold">${session.title}</div>
                            <small class="text-muted">${new Date(session.last_activity).toLocaleString()}</small>
                            <div class="mt-1">
                                <span class="badge bg-secondary">${session.message_count} 消息</span>
                            </div>
                        </div>
                    `).join('');
                    
                    // 自动选择第一个会话
                    if (!currentSessionId && data.data.sessions.length > 0) {
                        selectSession(data.data.sessions[0].session_id, data.data.sessions[0].title);
                    }
                } else {
                    sessionList.innerHTML = `
                        <div class="text-center p-3">
                            <div class="text-muted">暂无对话会话</div>
                            <button class="btn btn-sm btn-primary mt-2" onclick="createNewSession()">
                                创建新会话
                            </button>
                        </div>
                    `;
                }
            } catch (error) {
                console.error('Failed to load sessions:', error);
            }
        }

        async function createNewSession() {
            try {
                const response = await API.post('/chat/sessions', {
                    title: '新对话 - ' + new Date().toLocaleString()
                });
                const data = await response.json();

                if (data.code === 201) {
                    await loadSessions();
                    selectSession(data.data.session_id, data.data.title);
                    Utils.showNotification('新会话创建成功', 'success');
                }
            } catch (error) {
                console.error('Failed to create session:', error);
                Utils.showNotification('创建会话失败', 'danger');
            }
        }

        function selectSession(sessionId, title) {
            currentSessionId = sessionId;
            document.getElementById('chat-title').innerHTML = `
                <i class="bi bi-robot"></i>
                ${title}
            `;

            // 更新会话列表选中状态
            document.querySelectorAll('.session-item').forEach(item => {
                item.classList.remove('active');
            });
            event.target.closest('.session-item').classList.add('active');

            // 加载会话消息
            loadSessionMessages(sessionId);
        }

        async function loadSessionMessages(sessionId) {
            try {
                const response = await API.get(`/chat/sessions/${sessionId}/messages`);
                const data = await response.json();

                if (data.code === 200 && data.data.messages) {
                    const messagesContainer = document.getElementById('chat-messages');
                    messagesContainer.innerHTML = data.data.messages.map(msg => 
                        createMessageElement(msg.message_type, msg.content, msg.created_at)
                    ).join('');
                    scrollToBottom();
                }
            } catch (error) {
                console.error('Failed to load session messages:', error);
            }
        }

        function setupWebSocket() {
            if (!currentSessionId) return;

            const token = localStorage.getItem('access_token');
            const wsUrl = `${CONFIG.WS_BASE_URL}/ws/chat?session_id=${currentSessionId}&token=${token}`;
            
            websocket = new WebSocket(wsUrl);
            
            websocket.onopen = function() {
                console.log('WebSocket connected');
            };
            
            websocket.onmessage = function(event) {
                const data = JSON.parse(event.data);
                handleWebSocketMessage(data);
            };
            
            websocket.onclose = function() {
                console.log('WebSocket disconnected');
                // 重连逻辑
                setTimeout(setupWebSocket, 3000);
            };
        }

        function handleWebSocketMessage(data) {
            switch (data.type) {
                case 'assistant_message':
                    hideTypingIndicator();
                    addMessage('assistant', data.data.content, new Date(), data.data);
                    break;
                case 'error':
                    hideTypingIndicator();
                    Utils.showNotification(data.message, 'danger');
                    break;
            }
        }

        function setupChatForm() {
            const chatForm = document.getElementById('chat-form');
            const messageInput = document.getElementById('message-input');

            chatForm.addEventListener('submit', function(e) {
                e.preventDefault();
                sendMessage();
            });

            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    sendMessage();
                }
            });
        }

        async function sendMessage() {
            const messageInput = document.getElementById('message-input');
            const message = messageInput.value.trim();
            
            if (!message || !currentSessionId) return;

            // 添加用户消息到界面
            addMessage('user', message, new Date());
            messageInput.value = '';
            showTypingIndicator();

            try {
                if (websocket && websocket.readyState === WebSocket.OPEN) {
                    // 使用WebSocket发送
                    websocket.send(JSON.stringify({
                        type: 'message',
                        content: message
                    }));
                } else {
                    // 使用HTTP API发送
                    const response = await API.post('/chat/message', {
                        session_id: currentSessionId,
                        content: message
                    });
                    const data = await response.json();

                    hideTypingIndicator();
                    if (data.code === 200) {
                        addMessage('assistant', data.data.content, new Date(data.data.created_at));
                    } else {
                        Utils.showNotification('发送消息失败', 'danger');
                    }
                }
            } catch (error) {
                hideTypingIndicator();
                console.error('Failed to send message:', error);
                Utils.showNotification('发送消息失败', 'danger');
            }
        }

        function sendQuickMessage(message) {
            document.getElementById('message-input').value = message;
            sendMessage();
        }

        function addMessage(type, content, timestamp, messageData = null) {
            const messagesContainer = document.getElementById('chat-messages');
            const messageElement = createMessageElement(type, content, timestamp, messageData);

            // 移除打字指示器
            const typingIndicator = document.getElementById('typing-indicator');
            if (typingIndicator.parentNode) {
                typingIndicator.parentNode.removeChild(typingIndicator);
            }

            messagesContainer.insertAdjacentHTML('beforeend', messageElement);

            // 重新添加打字指示器
            messagesContainer.appendChild(typingIndicator);

            scrollToBottom();
        }

        function createMessageElement(type, content, timestamp, messageData = null) {
            const time = new Date(timestamp).toLocaleTimeString();
            const avatar = type === 'user' ?
                '<i class="bi bi-person"></i>' :
                '<i class="bi bi-robot"></i>';

            let actionButtons = '';
            if (type === 'assistant' && messageData && messageData.recognized_actions) {
                actionButtons = createActionButtons(messageData.recognized_actions);
            }

            const messageElement = document.createElement('div');
            messageElement.className = `message ${type}`;
            messageElement.innerHTML = `
                <div class="message-avatar">${avatar}</div>
                <div class="message-content">
                    <div>${formatMessageContent(content)}</div>
                    ${actionButtons}
                    <div class="message-time">${time}</div>
                </div>
            `;

            return messageElement;
        }

        function formatMessageContent(content) {
            // 处理代码块
            content = content.replace(/```bash\n([\s\S]*?)\n```/g,
                '<div class="command-code">$1</div>');
            content = content.replace(/```\n([\s\S]*?)\n```/g,
                '<div class="command-code">$1</div>');

            // 处理行内代码
            content = content.replace(/`([^`]+)`/g, '<code>$1</code>');

            // 处理换行
            content = content.replace(/\n/g, '<br>');

            return content;
        }

        function createActionButtons(actions) {
            if (!actions || actions.length === 0) {
                return '';
            }

            let buttonsHtml = '<div class="action-buttons mt-2">';

            actions.forEach(action => {
                const riskClass = getRiskClass(action.risk_level);
                const icon = getActionIcon(action.action_type);

                buttonsHtml += `
                    <button class="btn btn-sm ${riskClass} me-2 mb-1 action-btn"
                            data-action-id="${action.id}"
                            data-action-type="${action.action_type}"
                            data-command="${action.command}"
                            data-risk-level="${action.risk_level}"
                            onclick="executeAction('${action.id}', '${action.action_type}', '${action.command}', '${action.risk_level}')">
                        <i class="bi ${icon}"></i>
                        ${action.description}
                    </button>
                `;
            });

            buttonsHtml += '</div>';
            return buttonsHtml;
        }

        function getRiskClass(riskLevel) {
            switch (riskLevel) {
                case 'critical':
                    return 'btn-danger';
                case 'high':
                    return 'btn-warning';
                case 'medium':
                    return 'btn-info';
                case 'low':
                default:
                    return 'btn-success';
            }
        }

        function getActionIcon(actionType) {
            switch (actionType) {
                case 'service_management':
                    return 'bi-gear';
                case 'monitoring':
                    return 'bi-graph-up';
                case 'log_analysis':
                    return 'bi-file-text';
                case 'command_execution':
                    return 'bi-terminal';
                case 'backup_restore':
                    return 'bi-archive';
                default:
                    return 'bi-play';
            }
        }

        function showTypingIndicator() {
            document.getElementById('typing-indicator').style.display = 'block';
            scrollToBottom();
        }

        function hideTypingIndicator() {
            document.getElementById('typing-indicator').style.display = 'none';
        }

        function scrollToBottom() {
            const messagesContainer = document.getElementById('chat-messages');
            messagesContainer.scrollTop = messagesContainer.scrollHeight;
        }

        function clearChat() {
            if (confirm('确定要清空当前对话吗？')) {
                const messagesContainer = document.getElementById('chat-messages');
                messagesContainer.innerHTML = `
                    <div class="message assistant">
                        <div class="message-avatar">
                            <i class="bi bi-robot"></i>
                        </div>
                        <div class="message-content">
                            <div>对话已清空，有什么可以帮助您的吗？</div>
                            <div class="message-time">刚刚</div>
                        </div>
                    </div>
                `;
            }
        }

        async function executeAction(actionId, actionType, command, riskLevel) {
            // 显示确认对话框（特别是高风险操作）
            if (riskLevel === 'high' || riskLevel === 'critical') {
                const confirmMessage = `确定要执行此${riskLevel === 'critical' ? '高风险' : '中等风险'}操作吗？\n\n命令: ${command}\n\n此操作可能影响系统稳定性。`;
                if (!confirm(confirmMessage)) {
                    return;
                }
            }

            // 禁用按钮并显示加载状态
            const button = document.querySelector(`[data-action-id="${actionId}"]`);
            if (button) {
                button.disabled = true;
                button.innerHTML = '<i class="bi bi-hourglass-split"></i> 执行中...';
            }

            try {
                const response = await API.post('/actions/execute', {
                    action_id: actionId,
                    action_type: actionType,
                    command: command,
                    risk_level: riskLevel,
                    confirmed: true,
                    session_id: currentSessionId,
                    host_id: 1 // 这里应该从上下文中获取实际的主机ID
                });

                const data = await response.json();

                if (data.code === 200) {
                    const result = data.data;

                    if (result.status === 'requires_confirmation') {
                        // 需要进一步确认
                        showConfirmationDialog(result, actionId, actionType, command, riskLevel);
                    } else if (result.status === 'success') {
                        // 执行成功
                        Utils.showNotification('操作执行成功', 'success');
                        showExecutionResult(result);
                    } else if (result.status === 'failed') {
                        // 执行失败
                        Utils.showNotification(`操作执行失败: ${result.error}`, 'danger');
                        showExecutionResult(result);
                    } else {
                        // 其他状态
                        Utils.showNotification(`操作状态: ${result.status}`, 'info');
                    }
                } else {
                    Utils.showNotification(`执行失败: ${data.message}`, 'danger');
                }
            } catch (error) {
                console.error('Action execution failed:', error);
                Utils.showNotification('操作执行失败', 'danger');
            } finally {
                // 恢复按钮状态
                if (button) {
                    button.disabled = false;
                    const icon = getActionIcon(actionType);
                    const description = button.getAttribute('data-original-text') || '执行操作';
                    button.innerHTML = `<i class="bi ${icon}"></i> ${description}`;
                }
            }
        }

        function showConfirmationDialog(result, actionId, actionType, command, riskLevel) {
            const modal = document.createElement('div');
            modal.className = 'modal fade';
            modal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">操作确认</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-warning">
                                <i class="bi bi-exclamation-triangle"></i>
                                ${result.warning_msg}
                            </div>
                            <p><strong>命令:</strong> <code>${command}</code></p>
                            <p><strong>风险级别:</strong> <span class="badge bg-${getRiskClass(riskLevel).replace('btn-', '')}">${riskLevel}</span></p>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-danger" onclick="confirmAndExecute('${actionId}', '${actionType}', '${command}', '${riskLevel}')">确认执行</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(modal);
            const bootstrapModal = new bootstrap.Modal(modal);
            bootstrapModal.show();

            // 模态框关闭后移除DOM元素
            modal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(modal);
            });
        }

        async function confirmAndExecute(actionId, actionType, command, riskLevel) {
            // 关闭确认对话框
            const modal = document.querySelector('.modal.show');
            if (modal) {
                bootstrap.Modal.getInstance(modal).hide();
            }

            // 重新执行操作，这次带上confirmed标志
            await executeAction(actionId, actionType, command, riskLevel);
        }

        function showExecutionResult(result) {
            const resultModal = document.createElement('div');
            resultModal.className = 'modal fade';
            resultModal.innerHTML = `
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">执行结果</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="alert alert-${result.status === 'success' ? 'success' : 'danger'}">
                                <i class="bi bi-${result.status === 'success' ? 'check-circle' : 'x-circle'}"></i>
                                执行${result.status === 'success' ? '成功' : '失败'}
                            </div>
                            ${result.output ? `<div class="mb-3"><strong>输出:</strong><pre class="bg-light p-2 rounded">${result.output}</pre></div>` : ''}
                            ${result.error ? `<div class="mb-3"><strong>错误:</strong><pre class="bg-danger text-white p-2 rounded">${result.error}</pre></div>` : ''}
                            <div class="row">
                                <div class="col-md-6">
                                    <strong>执行时间:</strong> ${new Date(result.executed_at).toLocaleString()}
                                </div>
                                <div class="col-md-6">
                                    <strong>耗时:</strong> ${result.duration || 'N/A'}
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.appendChild(resultModal);
            const bootstrapModal = new bootstrap.Modal(resultModal);
            bootstrapModal.show();

            // 模态框关闭后移除DOM元素
            resultModal.addEventListener('hidden.bs.modal', () => {
                document.body.removeChild(resultModal);
            });
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                if (websocket) {
                    websocket.close();
                }
                localStorage.removeItem('access_token');
                localStorage.removeItem('refresh_token');
                window.location.href = '/login';
            }
        }
    </script>
</body>
</html>
