package alerting

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// SmartAlertEngine 智能告警引擎
type SmartAlertEngine struct {
	config              *SmartAlertConfig
	logger              *logrus.Logger
	thresholdManager    *DynamicThresholdManager
	noiseReducer        *AlertNoiseReducer
	aggregator          *AlertAggregator
	notificationManager *NotificationManager
	ruleEngine          *AlertRuleEngine
	metricCollector     *MetricCollector
	alertStore          *AlertStore
	mutex               sync.RWMutex
	isRunning           bool
	stopChan            chan struct{}
}

// SmartAlertConfig 智能告警配置
type SmartAlertConfig struct {
	EnableDynamicThresholds bool          `json:"enable_dynamic_thresholds"`
	EnableNoiseReduction    bool          `json:"enable_noise_reduction"`
	EnableAggregation       bool          `json:"enable_aggregation"`
	EnableMLPrediction      bool          `json:"enable_ml_prediction"`
	ThresholdUpdateInterval time.Duration `json:"threshold_update_interval"`
	NoiseReductionWindow    time.Duration `json:"noise_reduction_window"`
	AggregationWindow       time.Duration `json:"aggregation_window"`
	MaxAlertsPerMinute      int           `json:"max_alerts_per_minute"`
	AlertRetentionDays      int           `json:"alert_retention_days"`
	NotificationChannels    []string      `json:"notification_channels"`
}

// DynamicThresholdManager 动态阈值管理器
type DynamicThresholdManager struct {
	logger     *logrus.Logger
	thresholds map[string]*DynamicThreshold
	history    map[string]*MetricHistory
	analyzer   *ThresholdAnalyzer
	mutex      sync.RWMutex
}

// DynamicThreshold 动态阈值
type DynamicThreshold struct {
	MetricName     string    `json:"metric_name"`
	CurrentValue   float64   `json:"current_value"`
	BaselineValue  float64   `json:"baseline_value"`
	UpperThreshold float64   `json:"upper_threshold"`
	LowerThreshold float64   `json:"lower_threshold"`
	Sensitivity    float64   `json:"sensitivity"`
	Confidence     float64   `json:"confidence"`
	LastUpdated    time.Time `json:"last_updated"`
	UpdateCount    int64     `json:"update_count"`
	Algorithm      string    `json:"algorithm"`
}

// MetricHistory 指标历史
type MetricHistory struct {
	MetricName  string            `json:"metric_name"`
	DataPoints  []*DataPoint      `json:"data_points"`
	Statistics  *MetricStatistics `json:"statistics"`
	Trends      *TrendAnalysis    `json:"trends"`
	Seasonality *SeasonalPattern  `json:"seasonality"`
	MaxSize     int               `json:"max_size"`
}

// DataPoint 数据点
type DataPoint struct {
	Timestamp time.Time         `json:"timestamp"`
	Value     float64           `json:"value"`
	Tags      map[string]string `json:"tags"`
}

// MetricStatistics 指标统计
type MetricStatistics struct {
	Mean     float64 `json:"mean"`
	Median   float64 `json:"median"`
	StdDev   float64 `json:"std_dev"`
	Min      float64 `json:"min"`
	Max      float64 `json:"max"`
	P95      float64 `json:"p95"`
	P99      float64 `json:"p99"`
	Variance float64 `json:"variance"`
	Skewness float64 `json:"skewness"`
	Kurtosis float64 `json:"kurtosis"`
}

// TrendAnalysis 趋势分析
type TrendAnalysis struct {
	Direction    string  `json:"direction"` // increasing, decreasing, stable
	Slope        float64 `json:"slope"`
	Correlation  float64 `json:"correlation"`
	Confidence   float64 `json:"confidence"`
	ChangeRate   float64 `json:"change_rate"`
	Acceleration float64 `json:"acceleration"`
}

// SeasonalPattern 季节性模式
type SeasonalPattern struct {
	HasSeasonality bool               `json:"has_seasonality"`
	Period         time.Duration      `json:"period"`
	Amplitude      float64            `json:"amplitude"`
	Phase          float64            `json:"phase"`
	Patterns       map[string]float64 `json:"patterns"`
	Confidence     float64            `json:"confidence"`
}

// ThresholdAnalyzer 阈值分析器
type ThresholdAnalyzer struct {
	logger     *logrus.Logger
	algorithms map[string]ThresholdAlgorithm
}

// ThresholdAlgorithm 阈值算法接口
type ThresholdAlgorithm interface {
	CalculateThreshold(history *MetricHistory) (*DynamicThreshold, error)
	GetName() string
	GetDescription() string
}

// AlertNoiseReducer 告警降噪器
type AlertNoiseReducer struct {
	logger       *logrus.Logger
	config       *NoiseReductionConfig
	filters      []NoiseFilter
	correlator   *AlertCorrelator
	deduplicator *AlertDeduplicator
	rateLimiter  *AlertRateLimiter
	mutex        sync.RWMutex
}

// NoiseReductionConfig 降噪配置
type NoiseReductionConfig struct {
	EnableCorrelation   bool          `json:"enable_correlation"`
	EnableDeduplication bool          `json:"enable_deduplication"`
	EnableRateLimit     bool          `json:"enable_rate_limit"`
	CorrelationWindow   time.Duration `json:"correlation_window"`
	DeduplicationWindow time.Duration `json:"deduplication_window"`
	MaxSimilarAlerts    int           `json:"max_similar_alerts"`
	SimilarityThreshold float64       `json:"similarity_threshold"`
}

// NoiseFilter 噪声过滤器接口
type NoiseFilter interface {
	Filter(alert *Alert) bool
	GetName() string
	GetDescription() string
}

// AlertCorrelator 告警关联器
type AlertCorrelator struct {
	logger       *logrus.Logger
	rules        []*CorrelationRule
	correlations map[string]*AlertCorrelation
	mutex        sync.RWMutex
}

// CorrelationRule 关联规则
type CorrelationRule struct {
	ID          string                  `json:"id"`
	Name        string                  `json:"name"`
	Conditions  []*CorrelationCondition `json:"conditions"`
	Action      string                  `json:"action"`
	Priority    int                     `json:"priority"`
	Enabled     bool                    `json:"enabled"`
	Description string                  `json:"description"`
}

// CorrelationCondition 关联条件
type CorrelationCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
	Weight   float64     `json:"weight"`
}

// AlertCorrelation 告警关联
type AlertCorrelation struct {
	ID         string    `json:"id"`
	Alerts     []*Alert  `json:"alerts"`
	RootCause  *Alert    `json:"root_cause"`
	Confidence float64   `json:"confidence"`
	CreatedAt  time.Time `json:"created_at"`
	UpdatedAt  time.Time `json:"updated_at"`
	Status     string    `json:"status"`
}

// AlertDeduplicator 告警去重器
type AlertDeduplicator struct {
	logger    *logrus.Logger
	cache     map[string]*DuplicateGroup
	window    time.Duration
	threshold float64
	mutex     sync.RWMutex
}

// DuplicateGroup 重复组
type DuplicateGroup struct {
	Signature  string    `json:"signature"`
	Alerts     []*Alert  `json:"alerts"`
	Count      int       `json:"count"`
	FirstSeen  time.Time `json:"first_seen"`
	LastSeen   time.Time `json:"last_seen"`
	Suppressed bool      `json:"suppressed"`
}

// AlertRateLimiter 告警限流器
type AlertRateLimiter struct {
	logger  *logrus.Logger
	buckets map[string]*RateBucket
	maxRate int
	window  time.Duration
	mutex   sync.RWMutex
}

// RateBucket 限流桶
type RateBucket struct {
	Key       string    `json:"key"`
	Count     int       `json:"count"`
	Window    time.Time `json:"window"`
	Blocked   int       `json:"blocked"`
	LastReset time.Time `json:"last_reset"`
}

// AlertAggregator 告警聚合器
type AlertAggregator struct {
	logger    *logrus.Logger
	config    *AggregationConfig
	groups    map[string]*AlertGroup
	rules     []*AggregationRule
	scheduler *AggregationScheduler
	mutex     sync.RWMutex
}

// AggregationConfig 聚合配置
type AggregationConfig struct {
	EnableTimeBasedAggregation bool          `json:"enable_time_based_aggregation"`
	EnableRuleBasedAggregation bool          `json:"enable_rule_based_aggregation"`
	DefaultAggregationWindow   time.Duration `json:"default_aggregation_window"`
	MaxAlertsPerGroup          int           `json:"max_alerts_per_group"`
	AggregationFlushInterval   time.Duration `json:"aggregation_flush_interval"`
}

// AlertGroup 告警组
type AlertGroup struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Alerts    []*Alert               `json:"alerts"`
	Summary   *GroupSummary          `json:"summary"`
	Metadata  map[string]interface{} `json:"metadata"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
	Status    string                 `json:"status"`
	FlushTime time.Time              `json:"flush_time"`
}

// GroupSummary 组摘要
type GroupSummary struct {
	TotalAlerts    int                    `json:"total_alerts"`
	CriticalAlerts int                    `json:"critical_alerts"`
	HighAlerts     int                    `json:"high_alerts"`
	MediumAlerts   int                    `json:"medium_alerts"`
	LowAlerts      int                    `json:"low_alerts"`
	TopSources     []string               `json:"top_sources"`
	TopTypes       []string               `json:"top_types"`
	TimeRange      *TimeRange             `json:"time_range"`
	Patterns       map[string]interface{} `json:"patterns"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// AggregationRule 聚合规则
type AggregationRule struct {
	ID          string                  `json:"id"`
	Name        string                  `json:"name"`
	Conditions  []*AggregationCondition `json:"conditions"`
	GroupBy     []string                `json:"group_by"`
	Window      time.Duration           `json:"window"`
	MaxSize     int                     `json:"max_size"`
	Priority    int                     `json:"priority"`
	Enabled     bool                    `json:"enabled"`
	Description string                  `json:"description"`
}

// AggregationCondition 聚合条件
type AggregationCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// AggregationScheduler 聚合调度器
type AggregationScheduler struct {
	logger *logrus.Logger
	tasks  []*AggregationTask
	ticker *time.Ticker
	mutex  sync.RWMutex
}

// AggregationTask 聚合任务
type AggregationTask struct {
	ID        string        `json:"id"`
	GroupID   string        `json:"group_id"`
	FlushTime time.Time     `json:"flush_time"`
	Interval  time.Duration `json:"interval"`
	Enabled   bool          `json:"enabled"`
}

// NotificationManager 通知管理器
type NotificationManager struct {
	logger   *logrus.Logger
	channels map[string]NotificationChannel
	router   *NotificationRouter
	queue    *NotificationQueue
	mutex    sync.RWMutex
}

// NotificationChannel 通知渠道接口
type NotificationChannel interface {
	Send(ctx context.Context, notification *Notification) error
	GetName() string
	GetType() string
	IsEnabled() bool
}

// NotificationRouter 通知路由器
type NotificationRouter struct {
	logger *logrus.Logger
	rules  []*RoutingRule
	mutex  sync.RWMutex
}

// RoutingRule 路由规则
type RoutingRule struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Conditions []*RoutingCondition    `json:"conditions"`
	Channels   []string               `json:"channels"`
	Priority   int                    `json:"priority"`
	Enabled    bool                   `json:"enabled"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// RoutingCondition 路由条件
type RoutingCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// NotificationQueue 通知队列
type NotificationQueue struct {
	logger        *logrus.Logger
	queue         chan *QueuedNotification
	workers       int
	retryAttempts int
	retryDelay    time.Duration
	mutex         sync.RWMutex
}

// QueuedNotification 队列通知
type QueuedNotification struct {
	ID           string        `json:"id"`
	Notification *Notification `json:"notification"`
	Channel      string        `json:"channel"`
	Attempts     int           `json:"attempts"`
	CreatedAt    time.Time     `json:"created_at"`
	ScheduledAt  time.Time     `json:"scheduled_at"`
}

// Notification 通知
type Notification struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Title     string                 `json:"title"`
	Message   string                 `json:"message"`
	Severity  string                 `json:"severity"`
	Source    string                 `json:"source"`
	Target    string                 `json:"target"`
	Metadata  map[string]interface{} `json:"metadata"`
	CreatedAt time.Time              `json:"created_at"`
	ExpiresAt *time.Time             `json:"expires_at,omitempty"`
}

// NewSmartAlertEngine 创建智能告警引擎
func NewSmartAlertEngine(config *SmartAlertConfig, logger *logrus.Logger) *SmartAlertEngine {
	if config == nil {
		config = DefaultSmartAlertConfig()
	}

	engine := &SmartAlertEngine{
		config:    config,
		logger:    logger,
		isRunning: false,
		stopChan:  make(chan struct{}),
	}

	// 初始化子组件
	engine.thresholdManager = NewDynamicThresholdManager(logger)
	engine.noiseReducer = NewAlertNoiseReducer(config, logger)
	engine.aggregator = NewAlertAggregator(config, logger)
	engine.notificationManager = NewNotificationManager(logger)
	engine.ruleEngine = NewAlertRuleEngine(logger)
	engine.metricCollector = NewMetricCollector(logger)
	engine.alertStore = NewAlertStore(logger)

	return engine
}

// DefaultSmartAlertConfig 默认智能告警配置
func DefaultSmartAlertConfig() *SmartAlertConfig {
	return &SmartAlertConfig{
		EnableDynamicThresholds: true,
		EnableNoiseReduction:    true,
		EnableAggregation:       true,
		EnableMLPrediction:      false,
		ThresholdUpdateInterval: 5 * time.Minute,
		NoiseReductionWindow:    10 * time.Minute,
		AggregationWindow:       5 * time.Minute,
		MaxAlertsPerMinute:      100,
		AlertRetentionDays:      30,
		NotificationChannels:    []string{"email", "webhook", "slack"},
	}
}

// Start 启动智能告警引擎
func (sae *SmartAlertEngine) Start(ctx context.Context) error {
	sae.mutex.Lock()
	defer sae.mutex.Unlock()

	if sae.isRunning {
		return fmt.Errorf("smart alert engine is already running")
	}

	sae.logger.Info("Starting smart alert engine")

	// 启动子组件
	if err := sae.thresholdManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start threshold manager: %w", err)
	}

	if err := sae.noiseReducer.Start(ctx); err != nil {
		return fmt.Errorf("failed to start noise reducer: %w", err)
	}

	if err := sae.aggregator.Start(ctx); err != nil {
		return fmt.Errorf("failed to start aggregator: %w", err)
	}

	if err := sae.notificationManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start notification manager: %w", err)
	}

	if err := sae.metricCollector.Start(ctx); err != nil {
		return fmt.Errorf("failed to start metric collector: %w", err)
	}

	// 启动后台任务
	go sae.thresholdUpdateRoutine(ctx)
	go sae.alertProcessingRoutine(ctx)
	go sae.cleanupRoutine(ctx)

	sae.isRunning = true
	sae.logger.Info("Smart alert engine started successfully")

	return nil
}

// Stop 停止智能告警引擎
func (sae *SmartAlertEngine) Stop(ctx context.Context) error {
	sae.mutex.Lock()
	defer sae.mutex.Unlock()

	if !sae.isRunning {
		return nil
	}

	sae.logger.Info("Stopping smart alert engine")

	// 发送停止信号
	close(sae.stopChan)

	// 停止子组件
	sae.thresholdManager.Stop(ctx)
	sae.noiseReducer.Stop(ctx)
	sae.aggregator.Stop(ctx)
	sae.notificationManager.Stop(ctx)
	sae.metricCollector.Stop(ctx)

	sae.isRunning = false
	sae.logger.Info("Smart alert engine stopped")

	return nil
}

// ProcessAlert 处理告警
func (sae *SmartAlertEngine) ProcessAlert(ctx context.Context, alert *Alert) error {
	sae.mutex.RLock()
	defer sae.mutex.RUnlock()

	if !sae.isRunning {
		return fmt.Errorf("smart alert engine is not running")
	}

	startTime := time.Now()

	sae.logger.WithFields(logrus.Fields{
		"alert_id":   alert.ID,
		"alert_type": alert.Type,
		"severity":   alert.Severity,
		"source":     alert.Source,
	}).Debug("Processing alert")

	// 1. 应用动态阈值
	if sae.config.EnableDynamicThresholds {
		if shouldSuppress := sae.applyDynamicThresholds(alert); shouldSuppress {
			sae.logger.WithField("alert_id", alert.ID).Debug("Alert suppressed by dynamic thresholds")
			return nil
		}
	}

	// 2. 降噪处理
	if sae.config.EnableNoiseReduction {
		if shouldFilter := sae.noiseReducer.Filter(alert); shouldFilter {
			sae.logger.WithField("alert_id", alert.ID).Debug("Alert filtered by noise reducer")
			return nil
		}
	}

	// 3. 聚合处理
	if sae.config.EnableAggregation {
		if shouldAggregate := sae.aggregator.Aggregate(alert); shouldAggregate {
			sae.logger.WithField("alert_id", alert.ID).Debug("Alert added to aggregation")
			return nil
		}
	}

	// 4. 存储告警
	if err := sae.alertStore.Store(alert); err != nil {
		sae.logger.WithError(err).Error("Failed to store alert")
	}

	// 5. 发送通知
	if err := sae.sendNotification(ctx, alert); err != nil {
		sae.logger.WithError(err).Error("Failed to send notification")
	}

	processingTime := time.Since(startTime)
	sae.logger.WithFields(logrus.Fields{
		"alert_id":        alert.ID,
		"processing_time": processingTime,
	}).Info("Alert processed successfully")

	return nil
}

// ProcessMetric 处理指标
func (sae *SmartAlertEngine) ProcessMetric(ctx context.Context, metric *Metric) error {
	sae.mutex.RLock()
	defer sae.mutex.RUnlock()

	if !sae.isRunning {
		return fmt.Errorf("smart alert engine is not running")
	}

	// 更新动态阈值
	if sae.config.EnableDynamicThresholds {
		sae.thresholdManager.UpdateMetric(metric)
	}

	// 检查是否触发告警
	alerts := sae.ruleEngine.EvaluateMetric(metric)

	// 处理生成的告警
	for _, alert := range alerts {
		if err := sae.ProcessAlert(ctx, alert); err != nil {
			sae.logger.WithError(err).WithField("alert_id", alert.ID).Error("Failed to process generated alert")
		}
	}

	return nil
}

// applyDynamicThresholds 应用动态阈值
func (sae *SmartAlertEngine) applyDynamicThresholds(alert *Alert) bool {
	// 获取相关指标的动态阈值
	threshold := sae.thresholdManager.GetThreshold(alert.MetricName)
	if threshold == nil {
		return false
	}

	// 检查告警值是否在动态阈值范围内
	if alert.Value != nil {
		if value, ok := alert.Value.(float64); ok {
			if value >= threshold.LowerThreshold && value <= threshold.UpperThreshold {
				// 在正常范围内，抑制告警
				return true
			}
		}
	}

	return false
}

// sendNotification 发送通知
func (sae *SmartAlertEngine) sendNotification(ctx context.Context, alert *Alert) error {
	// 创建通知
	notification := &Notification{
		ID:       fmt.Sprintf("notif_%d", time.Now().UnixNano()),
		Type:     "alert",
		Title:    alert.Title,
		Message:  alert.Message,
		Severity: alert.Severity,
		Source:   alert.Source,
		Metadata: map[string]interface{}{
			"alert_id":    alert.ID,
			"alert_type":  alert.Type,
			"metric_name": alert.MetricName,
			"value":       alert.Value,
		},
		CreatedAt: time.Now(),
	}

	// 路由通知到适当的渠道
	return sae.notificationManager.Send(ctx, notification)
}

// thresholdUpdateRoutine 阈值更新协程
func (sae *SmartAlertEngine) thresholdUpdateRoutine(ctx context.Context) {
	ticker := time.NewTicker(sae.config.ThresholdUpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-sae.stopChan:
			return
		case <-ticker.C:
			if sae.config.EnableDynamicThresholds {
				sae.thresholdManager.UpdateThresholds()
			}
		}
	}
}

// alertProcessingRoutine 告警处理协程
func (sae *SmartAlertEngine) alertProcessingRoutine(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-sae.stopChan:
			return
		case <-ticker.C:
			// 处理聚合告警
			if sae.config.EnableAggregation {
				sae.aggregator.FlushExpiredGroups()
			}

			// 清理过期的降噪缓存
			if sae.config.EnableNoiseReduction {
				sae.noiseReducer.Cleanup()
			}
		}
	}
}

// cleanupRoutine 清理协程
func (sae *SmartAlertEngine) cleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-sae.stopChan:
			return
		case <-ticker.C:
			// 清理过期告警
			cutoffTime := time.Now().AddDate(0, 0, -sae.config.AlertRetentionDays)
			if err := sae.alertStore.CleanupExpired(cutoffTime); err != nil {
				sae.logger.WithError(err).Error("Failed to cleanup expired alerts")
			}
		}
	}
}

// GetEngineStatus 获取引擎状态
func (sae *SmartAlertEngine) GetEngineStatus() *SmartAlertEngineStatus {
	sae.mutex.RLock()
	defer sae.mutex.RUnlock()

	return &SmartAlertEngineStatus{
		IsRunning:           sae.isRunning,
		Config:              sae.config,
		ThresholdStats:      sae.thresholdManager.GetStats(),
		NoiseReductionStats: sae.noiseReducer.GetStats(),
		AggregationStats:    sae.aggregator.GetStats(),
		NotificationStats:   sae.notificationManager.GetStats(),
		AlertStats:          sae.alertStore.GetStats(),
	}
}

// SmartAlertEngineStatus 智能告警引擎状态
type SmartAlertEngineStatus struct {
	IsRunning           bool                   `json:"is_running"`
	Config              *SmartAlertConfig      `json:"config"`
	ThresholdStats      *ThresholdManagerStats `json:"threshold_stats"`
	NoiseReductionStats *NoiseReductionStats   `json:"noise_reduction_stats"`
	AggregationStats    *AggregationStats      `json:"aggregation_stats"`
	NotificationStats   *NotificationStats     `json:"notification_stats"`
	AlertStats          *AlertStoreStats       `json:"alert_stats"`
}

// 统计类型定义

// ThresholdManagerStats 阈值管理器统计
type ThresholdManagerStats struct {
	TotalThresholds   int       `json:"total_thresholds"`
	DynamicThresholds int       `json:"dynamic_thresholds"`
	StaticThresholds  int       `json:"static_thresholds"`
	AverageConfidence float64   `json:"average_confidence"`
	LastUpdateTime    time.Time `json:"last_update_time"`
	UpdateCount       int64     `json:"update_count"`
}

// NoiseReductionStats 降噪统计
type NoiseReductionStats struct {
	TotalProcessed    int64   `json:"total_processed"`
	FilteredAlerts    int64   `json:"filtered_alerts"`
	DuplicateAlerts   int64   `json:"duplicate_alerts"`
	CorrelatedAlerts  int64   `json:"correlated_alerts"`
	RateLimitedAlerts int64   `json:"rate_limited_alerts"`
	FilterEfficiency  float64 `json:"filter_efficiency"`
}

// AggregationStats 聚合统计
type AggregationStats struct {
	TotalGroups      int     `json:"total_groups"`
	ActiveGroups     int     `json:"active_groups"`
	AggregatedAlerts int64   `json:"aggregated_alerts"`
	AverageGroupSize float64 `json:"average_group_size"`
	CompressionRatio float64 `json:"compression_ratio"`
}

// NotificationStats 通知统计
type NotificationStats struct {
	TotalSent           int64            `json:"total_sent"`
	SuccessfulSent      int64            `json:"successful_sent"`
	FailedSent          int64            `json:"failed_sent"`
	ChannelStats        map[string]int64 `json:"channel_stats"`
	AverageDeliveryTime time.Duration    `json:"average_delivery_time"`
	SuccessRate         float64          `json:"success_rate"`
}

// AlertStoreStats 告警存储统计
type AlertStoreStats struct {
	TotalAlerts    int64     `json:"total_alerts"`
	ActiveAlerts   int64     `json:"active_alerts"`
	ResolvedAlerts int64     `json:"resolved_alerts"`
	CriticalAlerts int64     `json:"critical_alerts"`
	HighAlerts     int64     `json:"high_alerts"`
	MediumAlerts   int64     `json:"medium_alerts"`
	LowAlerts      int64     `json:"low_alerts"`
	OldestAlert    time.Time `json:"oldest_alert"`
	NewestAlert    time.Time `json:"newest_alert"`
}
