package ai

import (
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// SessionManager 会话管理器
type SessionManager struct {
	logger           *logrus.Logger
	config           *MultimodalConfig
	activeSessions   map[string]*Session
	sessionMetrics   *SessionMetrics
	mutex            sync.RWMutex
}

// Session 会话
type Session struct {
	ID               string                 `json:"id"`
	UserID           int64                  `json:"user_id"`
	StartTime        time.Time              `json:"start_time"`
	LastActivity     time.Time              `json:"last_activity"`
	Status           SessionStatus          `json:"status"`
	InteractionCount int                    `json:"interaction_count"`
	TotalDuration    time.Duration          `json:"total_duration"`
	PreferredModes   []InteractionMode      `json:"preferred_modes"`
	QualityScore     float64                `json:"quality_score"`
	UserSatisfaction float64                `json:"user_satisfaction"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// SessionStatus 会话状态
type SessionStatus string

const (
	SessionStatusActive    SessionStatus = "active"
	SessionStatusInactive  SessionStatus = "inactive"
	SessionStatusExpired   SessionStatus = "expired"
	SessionStatusCompleted SessionStatus = "completed"
)

// SessionMetrics 会话指标
type SessionMetrics struct {
	TotalSessions        int64                          `json:"total_sessions"`
	ActiveSessions       int64                          `json:"active_sessions"`
	AverageSessionTime   time.Duration                  `json:"average_session_time"`
	AverageInteractions  float64                        `json:"average_interactions"`
	ModeUsageStats       map[InteractionMode]int64      `json:"mode_usage_stats"`
	UserSatisfactionAvg  float64                        `json:"user_satisfaction_avg"`
	QualityScoreAvg      float64                        `json:"quality_score_avg"`
	SessionsByStatus     map[SessionStatus]int64        `json:"sessions_by_status"`
	LastUpdated          time.Time                      `json:"last_updated"`
}

// NewSessionManager 创建会话管理器
func NewSessionManager(logger *logrus.Logger, config *MultimodalConfig) *SessionManager {
	manager := &SessionManager{
		logger:         logger,
		config:         config,
		activeSessions: make(map[string]*Session),
		sessionMetrics: &SessionMetrics{
			ModeUsageStats:   make(map[InteractionMode]int64),
			SessionsByStatus: make(map[SessionStatus]int64),
			LastUpdated:      time.Now(),
		},
	}

	// 启动会话监控
	go manager.runSessionMonitoring()

	logger.Info("📋 会话管理器初始化完成")
	return manager
}

// CreateSession 创建新会话
func (sm *SessionManager) CreateSession(userID int64, sessionID string) (*Session, error) {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	// 检查会话是否已存在
	if _, exists := sm.activeSessions[sessionID]; exists {
		return nil, fmt.Errorf("session already exists: %s", sessionID)
	}

	session := &Session{
		ID:               sessionID,
		UserID:           userID,
		StartTime:        time.Now(),
		LastActivity:     time.Now(),
		Status:           SessionStatusActive,
		InteractionCount: 0,
		TotalDuration:    0,
		PreferredModes:   []InteractionMode{ModeText},
		QualityScore:     0.8, // 默认质量分数
		UserSatisfaction: 0.8, // 默认满意度
		Metadata:         make(map[string]interface{}),
	}

	sm.activeSessions[sessionID] = session
	sm.updateMetrics()

	sm.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("📋 创建新会话")

	return session, nil
}

// GetSession 获取会话
func (sm *SessionManager) GetSession(sessionID string) (*Session, error) {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	session, exists := sm.activeSessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("session not found: %s", sessionID)
	}

	return session, nil
}

// UpdateSession 更新会话
func (sm *SessionManager) UpdateSession(sessionID string, updates map[string]interface{}) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	session, exists := sm.activeSessions[sessionID]
	if !exists {
		return fmt.Errorf("session not found: %s", sessionID)
	}

	// 更新最后活动时间
	session.LastActivity = time.Now()
	session.TotalDuration = time.Since(session.StartTime)

	// 应用更新
	for key, value := range updates {
		switch key {
		case "interaction_count":
			if count, ok := value.(int); ok {
				session.InteractionCount = count
			}
		case "preferred_modes":
			if modes, ok := value.([]InteractionMode); ok {
				session.PreferredModes = modes
			}
		case "quality_score":
			if score, ok := value.(float64); ok {
				session.QualityScore = score
			}
		case "user_satisfaction":
			if satisfaction, ok := value.(float64); ok {
				session.UserSatisfaction = satisfaction
			}
		case "status":
			if status, ok := value.(SessionStatus); ok {
				session.Status = status
			}
		default:
			session.Metadata[key] = value
		}
	}

	sm.updateMetrics()

	sm.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"updates":    updates,
	}).Debug("更新会话")

	return nil
}

// CloseSession 关闭会话
func (sm *SessionManager) CloseSession(sessionID string) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	session, exists := sm.activeSessions[sessionID]
	if !exists {
		return fmt.Errorf("session not found: %s", sessionID)
	}

	session.Status = SessionStatusCompleted
	session.TotalDuration = time.Since(session.StartTime)

	// 从活跃会话中移除
	delete(sm.activeSessions, sessionID)
	sm.updateMetrics()

	sm.logger.WithFields(logrus.Fields{
		"session_id":        sessionID,
		"total_duration":    session.TotalDuration,
		"interaction_count": session.InteractionCount,
		"quality_score":     session.QualityScore,
	}).Info("📋 关闭会话")

	return nil
}

// GetActiveSessions 获取活跃会话列表
func (sm *SessionManager) GetActiveSessions() map[string]*Session {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	// 创建副本以避免并发访问问题
	sessions := make(map[string]*Session)
	for id, session := range sm.activeSessions {
		sessionCopy := *session
		sessions[id] = &sessionCopy
	}

	return sessions
}

// GetSessionMetrics 获取会话指标
func (sm *SessionManager) GetSessionMetrics() *SessionMetrics {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	// 创建副本
	metrics := &SessionMetrics{
		TotalSessions:       sm.sessionMetrics.TotalSessions,
		ActiveSessions:      sm.sessionMetrics.ActiveSessions,
		AverageSessionTime:  sm.sessionMetrics.AverageSessionTime,
		AverageInteractions: sm.sessionMetrics.AverageInteractions,
		ModeUsageStats:      make(map[InteractionMode]int64),
		UserSatisfactionAvg: sm.sessionMetrics.UserSatisfactionAvg,
		QualityScoreAvg:     sm.sessionMetrics.QualityScoreAvg,
		SessionsByStatus:    make(map[SessionStatus]int64),
		LastUpdated:         sm.sessionMetrics.LastUpdated,
	}

	for mode, count := range sm.sessionMetrics.ModeUsageStats {
		metrics.ModeUsageStats[mode] = count
	}

	for status, count := range sm.sessionMetrics.SessionsByStatus {
		metrics.SessionsByStatus[status] = count
	}

	return metrics
}

// RecordInteraction 记录交互
func (sm *SessionManager) RecordInteraction(sessionID string, mode InteractionMode, quality float64) error {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	session, exists := sm.activeSessions[sessionID]
	if !exists {
		return fmt.Errorf("session not found: %s", sessionID)
	}

	// 更新会话
	session.InteractionCount++
	session.LastActivity = time.Now()

	// 更新质量分数（移动平均）
	alpha := 0.1 // 学习率
	session.QualityScore = session.QualityScore*(1-alpha) + quality*alpha

	// 更新模态使用统计
	sm.sessionMetrics.ModeUsageStats[mode]++

	sm.updateMetrics()

	return nil
}

// 私有方法

func (sm *SessionManager) updateMetrics() {
	// 计算活跃会话数
	sm.sessionMetrics.ActiveSessions = int64(len(sm.activeSessions))

	// 计算平均会话时间和交互数
	if len(sm.activeSessions) > 0 {
		totalDuration := time.Duration(0)
		totalInteractions := 0
		totalSatisfaction := 0.0
		totalQuality := 0.0

		for _, session := range sm.activeSessions {
			totalDuration += time.Since(session.StartTime)
			totalInteractions += session.InteractionCount
			totalSatisfaction += session.UserSatisfaction
			totalQuality += session.QualityScore
		}

		count := float64(len(sm.activeSessions))
		sm.sessionMetrics.AverageSessionTime = totalDuration / time.Duration(len(sm.activeSessions))
		sm.sessionMetrics.AverageInteractions = float64(totalInteractions) / count
		sm.sessionMetrics.UserSatisfactionAvg = totalSatisfaction / count
		sm.sessionMetrics.QualityScoreAvg = totalQuality / count
	}

	// 更新状态统计
	sm.sessionMetrics.SessionsByStatus = make(map[SessionStatus]int64)
	for _, session := range sm.activeSessions {
		sm.sessionMetrics.SessionsByStatus[session.Status]++
	}

	sm.sessionMetrics.LastUpdated = time.Now()
}

func (sm *SessionManager) runSessionMonitoring() {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		sm.cleanupExpiredSessions()
		sm.updateSessionStatuses()
	}
}

func (sm *SessionManager) cleanupExpiredSessions() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	now := time.Now()
	expiredSessions := make([]string, 0)

	for sessionID, session := range sm.activeSessions {
		// 检查会话是否过期
		if now.Sub(session.LastActivity) > sm.config.MaxSessionDuration {
			session.Status = SessionStatusExpired
			expiredSessions = append(expiredSessions, sessionID)
		}
	}

	// 移除过期会话
	for _, sessionID := range expiredSessions {
		delete(sm.activeSessions, sessionID)
		sm.logger.WithField("session_id", sessionID).Info("会话已过期并清理")
	}

	if len(expiredSessions) > 0 {
		sm.updateMetrics()
	}
}

func (sm *SessionManager) updateSessionStatuses() {
	sm.mutex.Lock()
	defer sm.mutex.Unlock()

	now := time.Now()
	inactiveThreshold := 10 * time.Minute

	for _, session := range sm.activeSessions {
		if session.Status == SessionStatusActive {
			// 检查是否变为非活跃状态
			if now.Sub(session.LastActivity) > inactiveThreshold {
				session.Status = SessionStatusInactive
			}
		} else if session.Status == SessionStatusInactive {
			// 检查是否重新变为活跃状态
			if now.Sub(session.LastActivity) <= inactiveThreshold {
				session.Status = SessionStatusActive
			}
		}
	}
}

// GetUserSessions 获取用户的所有会话
func (sm *SessionManager) GetUserSessions(userID int64) []*Session {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	userSessions := make([]*Session, 0)
	for _, session := range sm.activeSessions {
		if session.UserID == userID {
			sessionCopy := *session
			userSessions = append(userSessions, &sessionCopy)
		}
	}

	return userSessions
}

// GetSessionsByStatus 根据状态获取会话
func (sm *SessionManager) GetSessionsByStatus(status SessionStatus) []*Session {
	sm.mutex.RLock()
	defer sm.mutex.RUnlock()

	sessions := make([]*Session, 0)
	for _, session := range sm.activeSessions {
		if session.Status == status {
			sessionCopy := *session
			sessions = append(sessions, &sessionCopy)
		}
	}

	return sessions
}

// UpdateUserSatisfaction 更新用户满意度
func (sm *SessionManager) UpdateUserSatisfaction(sessionID string, satisfaction float64) error {
	return sm.UpdateSession(sessionID, map[string]interface{}{
		"user_satisfaction": satisfaction,
	})
}

// GetSessionDuration 获取会话持续时间
func (sm *SessionManager) GetSessionDuration(sessionID string) (time.Duration, error) {
	session, err := sm.GetSession(sessionID)
	if err != nil {
		return 0, err
	}

	if session.Status == SessionStatusCompleted || session.Status == SessionStatusExpired {
		return session.TotalDuration, nil
	}

	return time.Since(session.StartTime), nil
}

// IsSessionActive 检查会话是否活跃
func (sm *SessionManager) IsSessionActive(sessionID string) bool {
	session, err := sm.GetSession(sessionID)
	if err != nil {
		return false
	}

	return session.Status == SessionStatusActive
}

// GetSessionQuality 获取会话质量分数
func (sm *SessionManager) GetSessionQuality(sessionID string) (float64, error) {
	session, err := sm.GetSession(sessionID)
	if err != nil {
		return 0, err
	}

	return session.QualityScore, nil
}

// SetSessionMetadata 设置会话元数据
func (sm *SessionManager) SetSessionMetadata(sessionID string, key string, value interface{}) error {
	return sm.UpdateSession(sessionID, map[string]interface{}{
		key: value,
	})
}

// GetSessionMetadata 获取会话元数据
func (sm *SessionManager) GetSessionMetadata(sessionID string, key string) (interface{}, error) {
	session, err := sm.GetSession(sessionID)
	if err != nil {
		return nil, err
	}

	value, exists := session.Metadata[key]
	if !exists {
		return nil, fmt.Errorf("metadata key not found: %s", key)
	}

	return value, nil
}
