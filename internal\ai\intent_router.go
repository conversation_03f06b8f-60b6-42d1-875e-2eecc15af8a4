package ai

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// IntentRouter 意图路由器
type IntentRouter struct {
	handlers map[string]IntentHandler
	logger   *logrus.Logger
	metrics  *IntentMetrics
}

// IntentMetrics 意图处理指标
type IntentMetrics struct {
	TotalRequests      int64              `json:"total_requests"`
	SuccessfulRequests int64              `json:"successful_requests"`
	FailedRequests     int64              `json:"failed_requests"`
	AverageLatency     float64            `json:"average_latency_ms"`
	IntentCounts       map[string]int64   `json:"intent_counts"`
	HandlerPerformance map[string]float64 `json:"handler_performance_ms"`
	LastUpdated        time.Time          `json:"last_updated"`
}

// NewIntentRouter 创建意图路由器
func NewIntentRouter(logger *logrus.Logger) *IntentRouter {
	return &IntentRouter{
		handlers: make(map[string]IntentHandler),
		logger:   logger,
		metrics: &IntentMetrics{
			IntentCounts:       make(map[string]int64),
			HandlerPerformance: make(map[string]float64),
			LastUpdated:        time.Now(),
		},
	}
}

// RegisterHandler 注册意图处理器
func (ir *IntentRouter) RegisterHandler(handler IntentHandler) {
	supportedIntents := handler.GetSupportedIntents()
	for _, intent := range supportedIntents {
		ir.handlers[intent] = handler
		ir.logger.WithFields(logrus.Fields{
			"intent_type": intent,
			"handler":     fmt.Sprintf("%T", handler),
		}).Info("Registered intent handler")
	}
}

// RouteIntent 路由意图到对应的处理器
func (ir *IntentRouter) RouteIntent(ctx context.Context, result *EnhancedIntentResult, sessionID string) (*IntentHandleResult, error) {
	start := time.Now()

	ir.logger.WithFields(logrus.Fields{
		"intent_type": result.Type,
		"session_id":  sessionID,
		"confidence":  result.Confidence,
		"risk_level":  result.RiskLevel,
	}).Info("Routing intent to handler")

	// 更新指标
	ir.metrics.TotalRequests++
	ir.metrics.IntentCounts[result.Type]++

	// 查找处理器
	handler, exists := ir.handlers[result.Type]
	if !exists {
		ir.metrics.FailedRequests++
		return ir.createFallbackResult(result), nil
	}

	// 验证处理器是否能处理该意图
	if !handler.CanHandle(result.Type) {
		ir.metrics.FailedRequests++
		return &IntentHandleResult{
			Success: false,
			Message: fmt.Sprintf("处理器无法处理意图类型: %s", result.Type),
		}, nil
	}

	// 执行处理
	handleResult, err := handler.Handle(ctx, result, sessionID)

	// 计算处理时间
	duration := time.Since(start)
	ir.updateMetrics(result.Type, duration, err == nil && handleResult.Success)

	if err != nil {
		ir.logger.WithError(err).WithFields(logrus.Fields{
			"intent_type": result.Type,
			"session_id":  sessionID,
		}).Error("Intent handler returned error")

		return &IntentHandleResult{
			Success: false,
			Message: fmt.Sprintf("处理意图时发生错误: %v", err),
		}, err
	}

	// 添加处理时间信息
	if handleResult.Data == nil {
		handleResult.Data = make(map[string]interface{})
	}
	handleResult.Data["processing_time_ms"] = duration.Milliseconds()
	handleResult.Data["handler_type"] = fmt.Sprintf("%T", handler)

	ir.logger.WithFields(logrus.Fields{
		"intent_type": result.Type,
		"session_id":  sessionID,
		"success":     handleResult.Success,
		"duration_ms": duration.Milliseconds(),
	}).Info("Intent handling completed")

	return handleResult, nil
}

// createFallbackResult 创建降级结果
func (ir *IntentRouter) createFallbackResult(result *EnhancedIntentResult) *IntentHandleResult {
	return &IntentHandleResult{
		Success: false,
		Message: fmt.Sprintf("暂不支持的意图类型: %s", result.Type),
		Data: map[string]interface{}{
			"unsupported_intent": result.Type,
			"available_intents":  ir.getAvailableIntents(),
		},
		NextSteps: []string{
			"请尝试使用支持的意图类型",
			"或联系管理员添加新的意图处理器",
		},
	}
}

// getAvailableIntents 获取可用的意图类型
func (ir *IntentRouter) getAvailableIntents() []string {
	intents := make([]string, 0, len(ir.handlers))
	for intent := range ir.handlers {
		intents = append(intents, intent)
	}
	return intents
}

// updateMetrics 更新指标
func (ir *IntentRouter) updateMetrics(intentType string, duration time.Duration, success bool) {
	if success {
		ir.metrics.SuccessfulRequests++
	} else {
		ir.metrics.FailedRequests++
	}

	// 更新平均延迟
	totalRequests := ir.metrics.TotalRequests
	currentAvg := ir.metrics.AverageLatency
	newLatency := float64(duration.Milliseconds())
	ir.metrics.AverageLatency = (currentAvg*float64(totalRequests-1) + newLatency) / float64(totalRequests)

	// 更新处理器性能
	handlerKey := intentType
	if currentPerf, exists := ir.metrics.HandlerPerformance[handlerKey]; exists {
		ir.metrics.HandlerPerformance[handlerKey] = (currentPerf + newLatency) / 2
	} else {
		ir.metrics.HandlerPerformance[handlerKey] = newLatency
	}

	ir.metrics.LastUpdated = time.Now()
}

// GetMetrics 获取指标
func (ir *IntentRouter) GetMetrics() *IntentMetrics {
	return ir.metrics
}

// GetHandlerInfo 获取处理器信息
func (ir *IntentRouter) GetHandlerInfo() map[string]interface{} {
	info := make(map[string]interface{})

	for intentType, handler := range ir.handlers {
		info[intentType] = map[string]interface{}{
			"handler_type":      fmt.Sprintf("%T", handler),
			"supported_intents": handler.GetSupportedIntents(),
			"can_handle":        handler.CanHandle(intentType),
		}
	}

	return info
}

// InitializeDefaultHandlers 初始化默认处理器
func (ir *IntentRouter) InitializeDefaultHandlers(hostService interface{}) {
	// 注册主机管理处理器
	hostHandler := NewHostManagementHandler(hostService, ir.logger)
	ir.RegisterHandler(hostHandler)

	// 注册故障排查处理器
	troubleshootingHandler := NewTroubleshootingHandler(ir.logger)
	ir.RegisterHandler(troubleshootingHandler)

	// 注册服务管理处理器
	serviceHandler := NewServiceManagementHandler(ir.logger)
	ir.RegisterHandler(serviceHandler)

	// 注册自动化工作流处理器
	automationHandler := NewAutomationWorkflowHandler(ir.logger)
	ir.RegisterHandler(automationHandler)

	ir.logger.Info("Default intent handlers initialized")
}

// ServiceManagementHandler 服务管理处理器
type ServiceManagementHandler struct {
	logger *logrus.Logger
}

// NewServiceManagementHandler 创建服务管理处理器
func NewServiceManagementHandler(logger *logrus.Logger) *ServiceManagementHandler {
	return &ServiceManagementHandler{logger: logger}
}

func (s *ServiceManagementHandler) CanHandle(intentType string) bool {
	return intentType == "service_management" || intentType == "process_management"
}

func (s *ServiceManagementHandler) GetSupportedIntents() []string {
	return []string{"service_management", "process_management"}
}

func (s *ServiceManagementHandler) Handle(ctx context.Context, result *EnhancedIntentResult, sessionID string) (*IntentHandleResult, error) {
	return &IntentHandleResult{
		Success: true,
		Message: "🔧 服务管理功能",
		Data: map[string]interface{}{
			"service_type": result.Type,
			"timestamp":    time.Now(),
		},
		NextSteps: []string{
			"选择要管理的服务",
			"指定操作类型：启动、停止、重启、状态检查",
		},
	}, nil
}

// AutomationWorkflowHandler 自动化工作流处理器
type AutomationWorkflowHandler struct {
	logger *logrus.Logger
}

// NewAutomationWorkflowHandler 创建自动化工作流处理器
func NewAutomationWorkflowHandler(logger *logrus.Logger) *AutomationWorkflowHandler {
	return &AutomationWorkflowHandler{logger: logger}
}

func (a *AutomationWorkflowHandler) CanHandle(intentType string) bool {
	return intentType == "automation_workflow" || intentType == "deployment_management"
}

func (a *AutomationWorkflowHandler) GetSupportedIntents() []string {
	return []string{"automation_workflow", "deployment_management"}
}

func (a *AutomationWorkflowHandler) Handle(ctx context.Context, result *EnhancedIntentResult, sessionID string) (*IntentHandleResult, error) {
	return &IntentHandleResult{
		Success: true,
		Message: "🤖 自动化工作流功能",
		Data: map[string]interface{}{
			"workflow_type": result.Type,
			"timestamp":     time.Now(),
		},
		NextSteps: []string{
			"定义工作流步骤",
			"设置执行条件和参数",
			"配置错误处理和回滚策略",
		},
	}, nil
}
