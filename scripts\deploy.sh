#!/bin/bash

# AI运维管理平台部署脚本
# AI Ops Platform Deployment Script
# Version: 2.0.0
# Author: Claude 4.0 sonnet

set -euo pipefail

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置变量
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
CONFIG_DIR="$PROJECT_ROOT/configs"
LOG_DIR="$PROJECT_ROOT/logs"
BACKUP_DIR="$PROJECT_ROOT/backups"
BINARY_NAME="aiops-platform"
SERVICE_NAME="aiops-platform"
DEFAULT_PORT="8080"
DEFAULT_ENV="production"

# 部署选项
DEPLOY_MODE=""
ENVIRONMENT=""
PORT=""
ENABLE_HA="false"
ENABLE_MULTI_TENANT="false"
ENABLE_SECURITY="true"
SKIP_BACKUP="false"
SKIP_TESTS="false"
FORCE_DEPLOY="false"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 显示帮助信息
show_help() {
    cat << EOF
AI运维管理平台部署脚本

用法: $0 [选项]

选项:
    -m, --mode MODE         部署模式 (standalone|cluster|docker)
    -e, --env ENV          环境 (development|staging|production)
    -p, --port PORT        服务端口 (默认: 8080)
    --enable-ha            启用高可用
    --enable-multi-tenant  启用多租户
    --disable-security     禁用安全增强
    --skip-backup          跳过备份
    --skip-tests           跳过测试
    --force                强制部署
    -h, --help             显示帮助信息

示例:
    $0 -m standalone -e production -p 8080
    $0 -m cluster --enable-ha --enable-multi-tenant
    $0 -m docker -e development --skip-tests

EOF
}

# 解析命令行参数
parse_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -m|--mode)
                DEPLOY_MODE="$2"
                shift 2
                ;;
            -e|--env)
                ENVIRONMENT="$2"
                shift 2
                ;;
            -p|--port)
                PORT="$2"
                shift 2
                ;;
            --enable-ha)
                ENABLE_HA="true"
                shift
                ;;
            --enable-multi-tenant)
                ENABLE_MULTI_TENANT="true"
                shift
                ;;
            --disable-security)
                ENABLE_SECURITY="false"
                shift
                ;;
            --skip-backup)
                SKIP_BACKUP="true"
                shift
                ;;
            --skip-tests)
                SKIP_TESTS="true"
                shift
                ;;
            --force)
                FORCE_DEPLOY="true"
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done

    # 设置默认值
    DEPLOY_MODE=${DEPLOY_MODE:-"standalone"}
    ENVIRONMENT=${ENVIRONMENT:-"$DEFAULT_ENV"}
    PORT=${PORT:-"$DEFAULT_PORT"}
}

# 验证环境
validate_environment() {
    log_info "验证部署环境..."

    # 检查Go版本
    if ! command -v go &> /dev/null; then
        log_error "Go未安装，请先安装Go 1.19或更高版本"
        exit 1
    fi

    local go_version=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
    local major_version=$(echo $go_version | cut -d. -f1)
    local minor_version=$(echo $go_version | cut -d. -f2)

    if [[ $major_version -lt 1 ]] || [[ $major_version -eq 1 && $minor_version -lt 19 ]]; then
        log_error "Go版本过低，需要1.19或更高版本，当前版本: $go_version"
        exit 1
    fi

    # 检查必要的目录
    for dir in "$CONFIG_DIR" "$LOG_DIR" "$BACKUP_DIR"; do
        if [[ ! -d "$dir" ]]; then
            log_info "创建目录: $dir"
            mkdir -p "$dir"
        fi
    done

    # 检查端口是否被占用
    if netstat -tuln | grep -q ":$PORT "; then
        if [[ "$FORCE_DEPLOY" != "true" ]]; then
            log_error "端口 $PORT 已被占用，使用 --force 强制部署"
            exit 1
        else
            log_warning "端口 $PORT 已被占用，但将强制部署"
        fi
    fi

    log_success "环境验证通过"
}

# 创建备份
create_backup() {
    if [[ "$SKIP_BACKUP" == "true" ]]; then
        log_info "跳过备份"
        return
    fi

    log_info "创建备份..."

    local backup_timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_path="$BACKUP_DIR/backup_$backup_timestamp"

    mkdir -p "$backup_path"

    # 备份配置文件
    if [[ -d "$CONFIG_DIR" ]]; then
        cp -r "$CONFIG_DIR" "$backup_path/"
        log_info "配置文件已备份到: $backup_path/configs"
    fi

    # 备份数据库
    if [[ -f "$PROJECT_ROOT/aiops.db" ]]; then
        cp "$PROJECT_ROOT/aiops.db" "$backup_path/"
        log_info "数据库已备份到: $backup_path/aiops.db"
    fi

    # 备份日志
    if [[ -d "$LOG_DIR" ]] && [[ -n "$(ls -A "$LOG_DIR" 2>/dev/null)" ]]; then
        cp -r "$LOG_DIR" "$backup_path/"
        log_info "日志文件已备份到: $backup_path/logs"
    fi

    log_success "备份完成: $backup_path"
}

# 构建应用
build_application() {
    log_info "构建应用程序..."

    cd "$PROJECT_ROOT"

    # 清理之前的构建
    if [[ -f "$BINARY_NAME" ]]; then
        rm "$BINARY_NAME"
    fi

    # 设置构建标志
    local build_flags="-ldflags=-s -w"
    local build_tags="netgo"

    if [[ "$ENVIRONMENT" == "production" ]]; then
        build_flags="$build_flags -X main.Version=$(git describe --tags --always 2>/dev/null || echo 'unknown')"
        build_flags="$build_flags -X main.BuildTime=$(date -u +%Y-%m-%dT%H:%M:%SZ)"
        build_flags="$build_flags -X main.Environment=$ENVIRONMENT"
    fi

    # 构建
    log_info "执行构建命令..."
    if ! CGO_ENABLED=0 GOOS=linux go build $build_flags -tags "$build_tags" -o "$BINARY_NAME" ./cmd/server; then
        log_error "构建失败"
        exit 1
    fi

    # 验证构建结果
    if [[ ! -f "$BINARY_NAME" ]]; then
        log_error "构建的二进制文件不存在"
        exit 1
    fi

    # 设置执行权限
    chmod +x "$BINARY_NAME"

    log_success "应用程序构建完成"
}

# 运行测试
run_tests() {
    if [[ "$SKIP_TESTS" == "true" ]]; then
        log_info "跳过测试"
        return
    fi

    log_info "运行测试套件..."

    cd "$PROJECT_ROOT"

    # 运行单元测试
    log_info "运行单元测试..."
    if ! go test -v -race -coverprofile=coverage.out ./...; then
        log_error "单元测试失败"
        exit 1
    fi

    # 生成覆盖率报告
    if command -v go &> /dev/null; then
        go tool cover -html=coverage.out -o coverage.html
        log_info "测试覆盖率报告已生成: coverage.html"
    fi

    # 运行集成测试
    if [[ -d "tests/integration" ]]; then
        log_info "运行集成测试..."
        if ! go test -v ./tests/integration/...; then
            log_warning "集成测试失败，但继续部署"
        fi
    fi

    log_success "测试完成"
}

# 生成配置文件
generate_config() {
    log_info "生成配置文件..."

    local config_file="$CONFIG_DIR/config.yaml"

    cat > "$config_file" << EOF
# AI运维管理平台配置文件
# 生成时间: $(date)
# 环境: $ENVIRONMENT

server:
  port: $PORT
  host: "0.0.0.0"
  environment: "$ENVIRONMENT"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"

database:
  type: "sqlite"
  dsn: "./aiops.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"

deepseek:
  api_key: "\${DEEPSEEK_API_KEY}"
  base_url: "https://api.deepseek.com"
  model: "deepseek-chat"
  timeout: "15s"
  max_retries: 3

cache:
  enable: true
  l1_max_size: 1000
  l1_ttl: "5m"
  l2_max_size: 5000
  l2_ttl: "15m"
  l3_max_size: 10000
  l3_ttl: "1h"

security:
  enable: $ENABLE_SECURITY
  encryption_algorithm: "AES-256-GCM"
  key_rotation_interval: "24h"
  audit_retention_days: 90
  max_login_attempts: 5
  session_timeout: "30m"

high_availability:
  enable: $ENABLE_HA
  cluster_name: "aiops-cluster"
  node_id: "\${NODE_ID:-node-$(hostname)}"
  health_check_interval: "30s"
  failover_timeout: "5m"

multi_tenant:
  enable: $ENABLE_MULTI_TENANT
  max_tenants_per_node: 100
  default_quotas:
    max_cpu_cores: 4
    max_memory_mb: 8192
    max_storage_gb: 100
    max_hosts: 50

monitoring:
  enable: true
  collection_interval: "10s"
  retention_period: "24h"
  alert_thresholds:
    cpu_usage_percent: 80.0
    memory_usage_mb: 1024
    goroutine_count: 10000

logging:
  level: "info"
  format: "json"
  output: "stdout"
  file_path: "$LOG_DIR/aiops.log"
  max_size: 100
  max_backups: 10
  max_age: 30
EOF

    log_success "配置文件已生成: $config_file"
}

# 部署应用
deploy_application() {
    log_info "部署应用程序..."

    case "$DEPLOY_MODE" in
        "standalone")
            deploy_standalone
            ;;
        "cluster")
            deploy_cluster
            ;;
        "docker")
            deploy_docker
            ;;
        *)
            log_error "不支持的部署模式: $DEPLOY_MODE"
            exit 1
            ;;
    esac
}

# 独立部署
deploy_standalone() {
    log_info "执行独立部署..."

    # 停止现有服务
    if systemctl is-active --quiet "$SERVICE_NAME" 2>/dev/null; then
        log_info "停止现有服务..."
        sudo systemctl stop "$SERVICE_NAME"
    fi

    # 复制二进制文件
    sudo cp "$PROJECT_ROOT/$BINARY_NAME" "/usr/local/bin/"

    # 创建systemd服务文件
    create_systemd_service

    # 启动服务
    sudo systemctl daemon-reload
    sudo systemctl enable "$SERVICE_NAME"
    sudo systemctl start "$SERVICE_NAME"

    log_success "独立部署完成"
}

# 集群部署
deploy_cluster() {
    log_info "执行集群部署..."

    if [[ "$ENABLE_HA" != "true" ]]; then
        log_error "集群部署需要启用高可用功能"
        exit 1
    fi

    # 集群部署逻辑
    deploy_standalone

    log_success "集群部署完成"
}

# Docker部署
deploy_docker() {
    log_info "执行Docker部署..."

    # 创建Dockerfile
    create_dockerfile

    # 构建Docker镜像
    docker build -t "aiops-platform:$ENVIRONMENT" "$PROJECT_ROOT"

    # 创建docker-compose文件
    create_docker_compose

    # 启动容器
    docker-compose -f "$PROJECT_ROOT/docker-compose.yml" up -d

    log_success "Docker部署完成"
}

# 创建systemd服务文件
create_systemd_service() {
    local service_file="/etc/systemd/system/$SERVICE_NAME.service"

    sudo tee "$service_file" > /dev/null << EOF
[Unit]
Description=AI运维管理平台
After=network.target

[Service]
Type=simple
User=aiops
Group=aiops
WorkingDirectory=/opt/aiops
ExecStart=/usr/local/bin/$BINARY_NAME -config /opt/aiops/configs/config.yaml
Restart=always
RestartSec=5
Environment=DEEPSEEK_API_KEY=\${DEEPSEEK_API_KEY}
Environment=NODE_ID=\${HOSTNAME}

# 安全设置
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/aiops

[Install]
WantedBy=multi-user.target
EOF

    log_info "Systemd服务文件已创建: $service_file"
}

# 创建Dockerfile
create_dockerfile() {
    cat > "$PROJECT_ROOT/Dockerfile" << 'EOF'
FROM alpine:latest

RUN apk --no-cache add ca-certificates tzdata
WORKDIR /app

COPY aiops-platform .
COPY configs/ configs/

RUN addgroup -g 1001 aiops && \
    adduser -D -s /bin/sh -u 1001 -G aiops aiops && \
    chown -R aiops:aiops /app

USER aiops

EXPOSE 8080

CMD ["./aiops-platform", "-config", "configs/config.yaml"]
EOF

    log_info "Dockerfile已创建"
}

# 创建docker-compose文件
create_docker_compose() {
    cat > "$PROJECT_ROOT/docker-compose.yml" << EOF
version: '3.8'

services:
  aiops-platform:
    build: .
    ports:
      - "$PORT:8080"
    environment:
      - DEEPSEEK_API_KEY=\${DEEPSEEK_API_KEY}
      - NODE_ID=\${HOSTNAME}
    volumes:
      - ./logs:/app/logs
      - ./data:/app/data
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

volumes:
  logs:
  data:
EOF

    log_info "docker-compose.yml已创建"
}

# 验证部署
verify_deployment() {
    log_info "验证部署..."

    local max_attempts=30
    local attempt=1

    while [[ $attempt -le $max_attempts ]]; do
        log_info "验证尝试 $attempt/$max_attempts..."

        if curl -s "http://localhost:$PORT/health" > /dev/null; then
            log_success "服务健康检查通过"
            break
        fi

        if [[ $attempt -eq $max_attempts ]]; then
            log_error "服务验证失败"
            exit 1
        fi

        sleep 2
        ((attempt++))
    done

    # 显示服务状态
    if [[ "$DEPLOY_MODE" == "docker" ]]; then
        docker-compose -f "$PROJECT_ROOT/docker-compose.yml" ps
    else
        sudo systemctl status "$SERVICE_NAME" --no-pager
    fi

    log_success "部署验证完成"
}

# 显示部署信息
show_deployment_info() {
    log_success "部署完成！"
    echo
    echo "部署信息:"
    echo "  模式: $DEPLOY_MODE"
    echo "  环境: $ENVIRONMENT"
    echo "  端口: $PORT"
    echo "  高可用: $ENABLE_HA"
    echo "  多租户: $ENABLE_MULTI_TENANT"
    echo "  安全增强: $ENABLE_SECURITY"
    echo
    echo "访问地址:"
    echo "  Web界面: http://localhost:$PORT"
    echo "  健康检查: http://localhost:$PORT/health"
    echo "  API文档: http://localhost:$PORT/api/docs"
    echo
    echo "日志位置: $LOG_DIR"
    echo "配置文件: $CONFIG_DIR/config.yaml"
    echo
    echo "管理命令:"
    if [[ "$DEPLOY_MODE" == "docker" ]]; then
        echo "  查看日志: docker-compose -f $PROJECT_ROOT/docker-compose.yml logs -f"
        echo "  重启服务: docker-compose -f $PROJECT_ROOT/docker-compose.yml restart"
        echo "  停止服务: docker-compose -f $PROJECT_ROOT/docker-compose.yml down"
    else
        echo "  查看日志: sudo journalctl -u $SERVICE_NAME -f"
        echo "  重启服务: sudo systemctl restart $SERVICE_NAME"
        echo "  停止服务: sudo systemctl stop $SERVICE_NAME"
    fi
}

# 主函数
main() {
    log_info "开始部署AI运维管理平台..."

    parse_args "$@"
    validate_environment
    create_backup
    build_application
    run_tests
    generate_config
    deploy_application
    verify_deployment
    show_deployment_info

    log_success "部署流程完成！"
}

# 错误处理
trap 'log_error "部署过程中发生错误，退出码: $?"' ERR

# 执行主函数
main "$@"
