package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/model"
	"aiops-platform/internal/workflow"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// WorkflowService 工作流服务接口
type WorkflowService interface {
	// 工作流管理
	TriggerWorkflow(ctx context.Context, req *workflow.TriggerWorkflowRequest) (*workflow.WorkflowInstance, error)
	ProcessUserInput(ctx context.Context, instanceID, userInput string) (*workflow.WorkflowResponse, error)
	GetActiveWorkflows(sessionID string) ([]*workflow.WorkflowInstance, error)
	GetWorkflowInstance(instanceID string) (*workflow.WorkflowInstance, error)
	CancelWorkflow(instanceID string) error

	// 智能引导
	AnalyzeUserIntent(ctx context.Context, sessionID, message string) (*WorkflowIntentResult, error)
	GenerateGuidance(ctx context.Context, sessionID string) (*WorkflowGuidanceResponse, error)

	// 工作流定义管理
	RegisterWorkflowDefinition(definition *workflow.WorkflowDefinition) error
	GetWorkflowDefinitions(category string) ([]*workflow.WorkflowDefinition, error)

	// 统计和监控
	GetWorkflowMetrics() (*workflow.WorkflowMetrics, error)
	GetWorkflowHistory(instanceID string) (*workflow.WorkflowHistory, error)
}

// workflowService 工作流服务实现
type workflowService struct {
	db              *gorm.DB
	config          *config.Config
	logger          *logrus.Logger
	engine          *workflow.WorkflowEngine
	aiService       AIService
	hostService     HostService
	deepseekService *DeepSeekService
}

// NewWorkflowService 创建工作流服务
func NewWorkflowService(
	db *gorm.DB,
	cfg *config.Config,
	logger *logrus.Logger,
	aiService AIService,
	hostService HostService,
) WorkflowService {
	// 创建工作流引擎
	engine := workflow.NewWorkflowEngine(db, logger)

	// 创建DeepSeek服务
	deepseekService := NewDeepSeekService(&cfg.DeepSeek, logger)

	// 设置服务依赖
	// TODO: 实现接口适配器
	// engine.SetServices(hostService, aiService)

	return &workflowService{
		db:              db,
		config:          cfg,
		logger:          logger,
		engine:          engine,
		aiService:       aiService,
		hostService:     hostService,
		deepseekService: deepseekService,
	}
}

// Start 启动工作流服务
func (ws *workflowService) Start(ctx context.Context) error {
	return ws.engine.Start(ctx)
}

// Stop 停止工作流服务
func (ws *workflowService) Stop(ctx context.Context) error {
	return ws.engine.Stop(ctx)
}

// TriggerWorkflow 触发工作流
func (ws *workflowService) TriggerWorkflow(ctx context.Context, req *workflow.TriggerWorkflowRequest) (*workflow.WorkflowInstance, error) {
	ws.logger.WithFields(logrus.Fields{
		"trigger_type": req.TriggerType,
		"intent":       req.Intent,
		"session_id":   req.SessionID,
		"user_id":      req.UserID,
	}).Info("Triggering workflow")

	return ws.engine.TriggerWorkflow(ctx, req)
}

// ProcessUserInput 处理用户输入
func (ws *workflowService) ProcessUserInput(ctx context.Context, instanceID, userInput string) (*workflow.WorkflowResponse, error) {
	ws.logger.WithFields(logrus.Fields{
		"instance_id":  instanceID,
		"input_length": len(userInput),
	}).Info("Processing user input for workflow")

	return ws.engine.ProcessUserInput(ctx, instanceID, userInput)
}

// GetActiveWorkflows 获取活跃的工作流
func (ws *workflowService) GetActiveWorkflows(sessionID string) ([]*workflow.WorkflowInstance, error) {
	return ws.engine.GetActiveInstances(sessionID)
}

// GetWorkflowInstance 获取工作流实例
func (ws *workflowService) GetWorkflowInstance(instanceID string) (*workflow.WorkflowInstance, error) {
	return ws.engine.GetInstance(instanceID)
}

// CancelWorkflow 取消工作流
func (ws *workflowService) CancelWorkflow(instanceID string) error {
	instance, err := ws.engine.GetInstance(instanceID)
	if err != nil {
		return err
	}

	instance.Status = workflow.StatusCancelled
	instance.EndTime = &[]time.Time{time.Now()}[0]

	// 保存到数据库
	return ws.saveInstanceToDatabase(instance)
}

// AnalyzeUserIntent 分析用户意图
func (ws *workflowService) AnalyzeUserIntent(ctx context.Context, sessionID, message string) (*WorkflowIntentResult, error) {
	// 使用DeepSeek API分析用户意图
	prompt := fmt.Sprintf(`
分析以下用户消息的意图，判断是否需要启动工作流：

用户消息："%s"

请分析：
1. 用户的主要意图是什么？
2. 是否需要启动特定的工作流？
3. 如果需要，应该启动哪个工作流？
4. 用户可能需要什么帮助？

支持的工作流类型：
- host_management: 主机管理（添加、查看、删除主机）
- command_execution: 命令执行
- system_monitoring: 系统监控
- alert_management: 告警管理
- report_generation: 报表生成

请返回JSON格式：
{
  "needs_workflow": true/false,
  "workflow_type": "工作流类型",
  "confidence": 0.0-1.0,
  "intent": "用户意图描述",
  "suggested_action": "建议的下一步操作",
  "parameters": {}
}
`, message)

	messages := []Message{
		{
			Role:    "user",
			Content: prompt,
		},
	}

	response, err := ws.deepseekService.Chat(ctx, messages)

	if err != nil {
		return nil, fmt.Errorf("failed to analyze intent: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("no response from AI")
	}

	// 解析AI响应
	var result WorkflowIntentResult
	if err := json.Unmarshal([]byte(response.Choices[0].Message.Content), &result); err != nil {
		// 如果解析失败，返回默认结果
		return &WorkflowIntentResult{
			NeedsWorkflow:   false,
			Confidence:      0.5,
			Intent:          "general_chat",
			SuggestedAction: "继续对话",
		}, nil
	}

	return &result, nil
}

// GenerateGuidance 生成工作流引导
func (ws *workflowService) GenerateGuidance(ctx context.Context, sessionID string) (*WorkflowGuidanceResponse, error) {
	// 获取当前活跃的工作流
	activeWorkflows, err := ws.GetActiveWorkflows(sessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to get active workflows: %w", err)
	}

	if len(activeWorkflows) == 0 {
		return &WorkflowGuidanceResponse{
			Message:           "您好！我是您的AI运维助手，我可以帮您管理主机、监控系统、处理告警等。请告诉我您需要什么帮助？",
			Suggestions:       []string{"查看主机状态", "添加新主机", "检查系统告警", "生成运维报表"},
			HasActiveWorkflow: false,
		}, nil
	}

	// 获取第一个活跃工作流的状态
	activeWorkflow := activeWorkflows[0]

	// 使用AI生成个性化引导
	prompt := fmt.Sprintf(`
当前用户有一个正在进行的工作流：
- 工作流类型：%s
- 当前状态：%s
- 当前步骤：%s

请生成友好的引导消息，告诉用户：
1. 当前工作流的进展情况
2. 下一步需要做什么
3. 提供3-4个具体的操作建议

请返回JSON格式：
{
  "message": "引导消息",
  "suggestions": ["建议1", "建议2", "建议3"],
  "next_action": "下一步操作",
  "progress": "进度描述"
}
`, activeWorkflow.DefinitionID, activeWorkflow.Status, activeWorkflow.CurrentStep)

	messages := []Message{
		{
			Role:    "user",
			Content: prompt,
		},
	}

	response, err := ws.deepseekService.Chat(ctx, messages)

	if err != nil {
		// 返回默认引导
		return &WorkflowGuidanceResponse{
			Message:           "您有一个正在进行的工作流，请继续提供所需信息。",
			Suggestions:       []string{"继续当前操作", "查看工作流状态", "取消当前工作流"},
			HasActiveWorkflow: true,
			ActiveWorkflowID:  activeWorkflow.ID,
		}, nil
	}

	// 解析AI响应
	var aiGuidance struct {
		Message     string   `json:"message"`
		Suggestions []string `json:"suggestions"`
		NextAction  string   `json:"next_action"`
		Progress    string   `json:"progress"`
	}

	if len(response.Choices) == 0 {
		// 返回默认引导
		return &WorkflowGuidanceResponse{
			Message:           "您有一个正在进行的工作流，请继续提供所需信息。",
			Suggestions:       []string{"继续当前操作", "查看工作流状态", "取消当前工作流"},
			HasActiveWorkflow: true,
			ActiveWorkflowID:  activeWorkflow.ID,
		}, nil
	}

	if err := json.Unmarshal([]byte(response.Choices[0].Message.Content), &aiGuidance); err != nil {
		// 返回默认引导
		return &WorkflowGuidanceResponse{
			Message:           "您有一个正在进行的工作流，请继续提供所需信息。",
			Suggestions:       []string{"继续当前操作", "查看工作流状态", "取消当前工作流"},
			HasActiveWorkflow: true,
			ActiveWorkflowID:  activeWorkflow.ID,
		}, nil
	}

	return &WorkflowGuidanceResponse{
		Message:           aiGuidance.Message,
		Suggestions:       aiGuidance.Suggestions,
		NextAction:        aiGuidance.NextAction,
		Progress:          aiGuidance.Progress,
		HasActiveWorkflow: true,
		ActiveWorkflowID:  activeWorkflow.ID,
	}, nil
}

// RegisterWorkflowDefinition 注册工作流定义
func (ws *workflowService) RegisterWorkflowDefinition(definition *workflow.WorkflowDefinition) error {
	return ws.engine.RegisterDefinition(definition)
}

// GetWorkflowDefinitions 获取工作流定义
func (ws *workflowService) GetWorkflowDefinitions(category string) ([]*workflow.WorkflowDefinition, error) {
	// 从数据库查询工作流定义
	var dbDefinitions []model.WorkflowDefinition
	query := ws.db.Where("enabled = ?", true)

	if category != "" {
		query = query.Where("category = ?", category)
	}

	if err := query.Find(&dbDefinitions).Error; err != nil {
		return nil, fmt.Errorf("failed to query workflow definitions: %w", err)
	}

	var definitions []*workflow.WorkflowDefinition
	for _, dbDef := range dbDefinitions {
		definition := &workflow.WorkflowDefinition{}
		if err := json.Unmarshal([]byte(dbDef.Definition), definition); err != nil {
			ws.logger.WithError(err).Warnf("Failed to unmarshal workflow definition %s", dbDef.ID)
			continue
		}
		definitions = append(definitions, definition)
	}

	return definitions, nil
}

// GetWorkflowMetrics 获取工作流指标
func (ws *workflowService) GetWorkflowMetrics() (*workflow.WorkflowMetrics, error) {
	var totalInstances, runningInstances, completedToday, failedToday int64

	// 统计总实例数
	ws.db.Model(&model.WorkflowInstance{}).Count(&totalInstances)

	// 统计运行中的实例
	ws.db.Model(&model.WorkflowInstance{}).Where("status IN ?", []string{
		string(workflow.StatusRunning),
		string(workflow.StatusWaiting),
	}).Count(&runningInstances)

	// 统计今天完成的实例
	today := time.Now().Truncate(24 * time.Hour)
	ws.db.Model(&model.WorkflowInstance{}).Where("status = ? AND end_time >= ?",
		string(workflow.StatusCompleted), today).Count(&completedToday)

	// 统计今天失败的实例
	ws.db.Model(&model.WorkflowInstance{}).Where("status = ? AND end_time >= ?",
		string(workflow.StatusFailed), today).Count(&failedToday)

	// 计算成功率
	var successRate float64
	if totalInstances > 0 {
		var successfulInstances int64
		ws.db.Model(&model.WorkflowInstance{}).Where("status = ?",
			string(workflow.StatusCompleted)).Count(&successfulInstances)
		successRate = float64(successfulInstances) / float64(totalInstances)
	}

	return &workflow.WorkflowMetrics{
		TotalInstances:   totalInstances,
		RunningInstances: runningInstances,
		CompletedToday:   completedToday,
		FailedToday:      failedToday,
		SuccessRate:      successRate,
	}, nil
}

// GetWorkflowHistory 获取工作流历史
func (ws *workflowService) GetWorkflowHistory(instanceID string) (*workflow.WorkflowHistory, error) {
	// 查询工作流事件
	var events []model.WorkflowEvent
	if err := ws.db.Where("instance_id = ?", instanceID).Order("created_at ASC").Find(&events).Error; err != nil {
		return nil, fmt.Errorf("failed to query workflow events: %w", err)
	}

	// 查询步骤执行记录
	var stepExecutions []model.WorkflowStepExecution
	if err := ws.db.Where("instance_id = ?", instanceID).Order("start_time ASC").Find(&stepExecutions).Error; err != nil {
		return nil, fmt.Errorf("failed to query step executions: %w", err)
	}

	// 转换为工作流历史格式
	history := &workflow.WorkflowHistory{
		InstanceID: instanceID,
		Events:     make([]workflow.WorkflowEvent, len(events)),
		Results:    make([]workflow.StepExecutionResult, len(stepExecutions)),
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
	}

	// 转换事件
	for i, event := range events {
		history.Events[i] = workflow.WorkflowEvent{
			Type:       event.EventType,
			InstanceID: event.InstanceID,
			StepID:     event.StepID,
			Status:     event.Status,
			Timestamp:  event.CreatedAt,
			UserID:     event.UserID,
			SessionID:  event.SessionID,
		}
	}

	// 转换步骤执行结果
	for i, step := range stepExecutions {
		duration := time.Duration(0)
		if step.EndTime != nil {
			duration = step.EndTime.Sub(step.StartTime)
		}

		history.Results[i] = workflow.StepExecutionResult{
			StepID:     step.StepID,
			Success:    step.Status == "completed",
			StartTime:  step.StartTime,
			EndTime:    step.StartTime, // 默认值
			Duration:   duration,
			Error:      step.ErrorMsg,
			RetryCount: step.RetryCount,
		}

		if step.EndTime != nil {
			history.Results[i].EndTime = *step.EndTime
		}
	}

	return history, nil
}

// saveInstanceToDatabase 保存实例到数据库
func (ws *workflowService) saveInstanceToDatabase(instance *workflow.WorkflowInstance) error {
	// 序列化JSON字段
	variablesJSON, _ := json.Marshal(instance.Variables)
	collectedDataJSON, _ := json.Marshal(instance.CollectedData)
	executionPathJSON, _ := json.Marshal(instance.ExecutionPath)
	metadataJSON, _ := json.Marshal(instance.Metadata)

	dbInstance := &model.WorkflowInstance{
		ID:            instance.ID,
		DefinitionID:  instance.DefinitionID,
		SessionID:     instance.SessionID,
		UserID:        instance.UserID,
		Status:        string(instance.Status),
		CurrentStep:   instance.CurrentStep,
		Variables:     string(variablesJSON),
		CollectedData: string(collectedDataJSON),
		ExecutionPath: string(executionPathJSON),
		StartTime:     instance.StartTime,
		EndTime:       instance.EndTime,
		LastActivity:  instance.LastActivity,
		ErrorMessage:  instance.ErrorMessage,
		RetryCount:    instance.RetryCount,
		Priority:      instance.Priority,
		Metadata:      string(metadataJSON),
	}

	return ws.db.Save(dbInstance).Error
}

// WorkflowIntentResult 工作流意图分析结果
type WorkflowIntentResult struct {
	NeedsWorkflow   bool                   `json:"needs_workflow"`
	WorkflowType    string                 `json:"workflow_type"`
	Confidence      float64                `json:"confidence"`
	Intent          string                 `json:"intent"`
	SuggestedAction string                 `json:"suggested_action"`
	Parameters      map[string]interface{} `json:"parameters"`
}

// WorkflowGuidanceResponse 工作流引导响应
type WorkflowGuidanceResponse struct {
	Message           string   `json:"message"`
	Suggestions       []string `json:"suggestions"`
	NextAction        string   `json:"next_action,omitempty"`
	Progress          string   `json:"progress,omitempty"`
	HasActiveWorkflow bool     `json:"has_active_workflow"`
	ActiveWorkflowID  string   `json:"active_workflow_id,omitempty"`
}
