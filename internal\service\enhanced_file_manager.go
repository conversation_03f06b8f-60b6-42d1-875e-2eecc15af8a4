package service

import (
	"context"
	"crypto/md5"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedFileManager 增强文件管理器
type EnhancedFileManager struct {
	db            *gorm.DB
	logger        *logrus.Logger
	wsManager     *WebSocketManager
	sshPool       *SSHConnectionPool
	config        *FileManagerConfig
	permissions   *FilePermissionManager
	security      *FileSecurityManager
	audit         *FileAuditManager
	cache         *FileCache
	running       bool
	stopChan      chan struct{}
	mutex         sync.RWMutex
	metrics       *FileManagerMetrics
}

// FileManagerConfig 文件管理器配置
type FileManagerConfig struct {
	Enabled                bool          `json:"enabled"`
	MaxFileSize            int64         `json:"max_file_size"`           // 最大文件大小（字节）
	MaxConcurrentUploads   int           `json:"max_concurrent_uploads"`  // 最大并发上传数
	AllowedExtensions      []string      `json:"allowed_extensions"`      // 允许的文件扩展名
	BlockedExtensions      []string      `json:"blocked_extensions"`      // 禁止的文件扩展名
	UploadTimeout          time.Duration `json:"upload_timeout"`          // 上传超时时间
	DownloadTimeout        time.Duration `json:"download_timeout"`        // 下载超时时间
	EnableVirusScanning    bool          `json:"enable_virus_scanning"`   // 启用病毒扫描
	EnableCompression      bool          `json:"enable_compression"`      // 启用压缩
	EnableEncryption       bool          `json:"enable_encryption"`       // 启用加密
	TempDirectory          string        `json:"temp_directory"`          // 临时目录
	StorageDirectory       string        `json:"storage_directory"`       // 存储目录
	EnableVersioning       bool          `json:"enable_versioning"`       // 启用版本控制
	MaxVersions            int           `json:"max_versions"`             // 最大版本数
	EnableThumbnails       bool          `json:"enable_thumbnails"`       // 启用缩略图
	EnablePreview          bool          `json:"enable_preview"`          // 启用预览
}

// FileOperation 文件操作
type FileOperation struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"`          // upload, download, delete, copy, move, rename
	UserID        int64                  `json:"user_id"`
	HostID        int64                  `json:"host_id"`
	SourcePath    string                 `json:"source_path"`
	TargetPath    string                 `json:"target_path"`
	FileName      string                 `json:"file_name"`
	FileSize      int64                  `json:"file_size"`
	MimeType      string                 `json:"mime_type"`
	Status        string                 `json:"status"`        // pending, processing, completed, failed
	Progress      int                    `json:"progress"`      // 0-100
	StartTime     time.Time              `json:"start_time"`
	EndTime       *time.Time             `json:"end_time,omitempty"`
	Error         string                 `json:"error,omitempty"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// FileInfo 文件信息
type FileInfo struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Path         string                 `json:"path"`
	Size         int64                  `json:"size"`
	MimeType     string                 `json:"mime_type"`
	Extension    string                 `json:"extension"`
	IsDirectory  bool                   `json:"is_directory"`
	Permissions  string                 `json:"permissions"`
	Owner        string                 `json:"owner"`
	Group        string                 `json:"group"`
	ModTime      time.Time              `json:"mod_time"`
	AccessTime   time.Time              `json:"access_time"`
	CreateTime   time.Time              `json:"create_time"`
	Checksum     string                 `json:"checksum"`
	Version      int                    `json:"version"`
	IsSymlink    bool                   `json:"is_symlink"`
	LinkTarget   string                 `json:"link_target,omitempty"`
	ThumbnailURL string                 `json:"thumbnail_url,omitempty"`
	PreviewURL   string                 `json:"preview_url,omitempty"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// DirectoryListing 目录列表
type DirectoryListing struct {
	Path        string      `json:"path"`
	Files       []*FileInfo `json:"files"`
	Directories []*FileInfo `json:"directories"`
	TotalFiles  int         `json:"total_files"`
	TotalSize   int64       `json:"total_size"`
	Permissions string      `json:"permissions"`
	CanRead     bool        `json:"can_read"`
	CanWrite    bool        `json:"can_write"`
	CanExecute  bool        `json:"can_execute"`
}

// FileManagerMetrics 文件管理器指标
type FileManagerMetrics struct {
	TotalOperations    int64     `json:"total_operations"`
	SuccessOperations  int64     `json:"success_operations"`
	FailedOperations   int64     `json:"failed_operations"`
	TotalUploads       int64     `json:"total_uploads"`
	TotalDownloads     int64     `json:"total_downloads"`
	TotalBytesUploaded int64     `json:"total_bytes_uploaded"`
	TotalBytesDownloaded int64   `json:"total_bytes_downloaded"`
	AvgUploadSpeed     float64   `json:"avg_upload_speed_mbps"`
	AvgDownloadSpeed   float64   `json:"avg_download_speed_mbps"`
	ActiveOperations   int       `json:"active_operations"`
	LastActivity       time.Time `json:"last_activity"`
}

// NewEnhancedFileManager 创建增强文件管理器
func NewEnhancedFileManager(db *gorm.DB, logger *logrus.Logger, wsManager *WebSocketManager, sshPool *SSHConnectionPool) *EnhancedFileManager {
	config := &FileManagerConfig{
		Enabled:              true,
		MaxFileSize:          100 * 1024 * 1024, // 100MB
		MaxConcurrentUploads: 10,
		AllowedExtensions:    []string{".txt", ".log", ".conf", ".json", ".yaml", ".yml", ".xml", ".csv", ".md", ".sh", ".py", ".js", ".html", ".css", ".sql"},
		BlockedExtensions:    []string{".exe", ".bat", ".cmd", ".scr", ".pif", ".com", ".vbs", ".jar"},
		UploadTimeout:        10 * time.Minute,
		DownloadTimeout:      5 * time.Minute,
		EnableVirusScanning:  false, // 简化实现
		EnableCompression:    true,
		EnableEncryption:     false, // 简化实现
		TempDirectory:        "/tmp/aiops-uploads",
		StorageDirectory:     "/var/aiops/files",
		EnableVersioning:     true,
		MaxVersions:          5,
		EnableThumbnails:     false, // 简化实现
		EnablePreview:        true,
	}

	manager := &EnhancedFileManager{
		db:        db,
		logger:    logger,
		wsManager: wsManager,
		sshPool:   sshPool,
		config:    config,
		stopChan:  make(chan struct{}),
		metrics:   &FileManagerMetrics{},
	}

	// 初始化组件
	manager.permissions = NewFilePermissionManager(logger)
	manager.security = NewFileSecurityManager(logger, config)
	manager.audit = NewFileAuditManager(db, logger)
	manager.cache = NewFileCache(logger)

	return manager
}

// Start 启动增强文件管理器
func (efm *EnhancedFileManager) Start(ctx context.Context) error {
	efm.mutex.Lock()
	defer efm.mutex.Unlock()

	if efm.running {
		return fmt.Errorf("enhanced file manager is already running")
	}

	if !efm.config.Enabled {
		efm.logger.Info("Enhanced file manager is disabled")
		return nil
	}

	efm.running = true

	// 创建必要的目录
	if err := efm.createDirectories(); err != nil {
		return fmt.Errorf("failed to create directories: %w", err)
	}

	// 启动权限管理器
	if err := efm.permissions.Start(ctx); err != nil {
		return fmt.Errorf("failed to start permission manager: %w", err)
	}

	// 启动安全管理器
	if err := efm.security.Start(ctx); err != nil {
		return fmt.Errorf("failed to start security manager: %w", err)
	}

	// 启动审计管理器
	if err := efm.audit.Start(ctx); err != nil {
		return fmt.Errorf("failed to start audit manager: %w", err)
	}

	// 启动缓存管理器
	if err := efm.cache.Start(ctx); err != nil {
		return fmt.Errorf("failed to start cache manager: %w", err)
	}

	// 启动清理协程
	go efm.cleanupRoutine(ctx)

	efm.logger.Info("📁 Enhanced file manager started")
	return nil
}

// Stop 停止增强文件管理器
func (efm *EnhancedFileManager) Stop() error {
	efm.mutex.Lock()
	defer efm.mutex.Unlock()

	if !efm.running {
		return nil
	}

	close(efm.stopChan)
	efm.running = false

	efm.logger.Info("Enhanced file manager stopped")
	return nil
}

// createDirectories 创建必要的目录
func (efm *EnhancedFileManager) createDirectories() error {
	dirs := []string{
		efm.config.TempDirectory,
		efm.config.StorageDirectory,
	}

	for _, dir := range dirs {
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("failed to create directory %s: %w", dir, err)
		}
	}

	return nil
}

// ListDirectory 列出目录内容
func (efm *EnhancedFileManager) ListDirectory(ctx context.Context, hostID int64, path string, userID int64) (*DirectoryListing, error) {
	if !efm.running {
		return nil, fmt.Errorf("enhanced file manager is not running")
	}

	// 权限检查
	if !efm.permissions.CanRead(userID, hostID, path) {
		return nil, fmt.Errorf("permission denied: cannot read directory %s", path)
	}

	// 安全检查
	if !efm.security.IsPathSafe(path) {
		return nil, fmt.Errorf("security violation: unsafe path %s", path)
	}

	// 获取主机信息
	var host model.Host
	if err := efm.db.First(&host, hostID).Error; err != nil {
		return nil, fmt.Errorf("host not found: %w", err)
	}

	// 获取SSH连接
	sshConn, err := efm.sshPool.GetConnection(&host)
	if err != nil {
		return nil, fmt.Errorf("failed to get SSH connection: %w", err)
	}

	// 创建SSH会话
	session, err := sshConn.Client.NewSession()
	if err != nil {
		return nil, fmt.Errorf("failed to create SSH session: %w", err)
	}
	defer session.Close()

	// 执行ls命令获取目录内容
	cmd := fmt.Sprintf("ls -la '%s' 2>/dev/null || echo 'ERROR: Cannot access directory'", path)
	output, err := session.Output(cmd)
	if err != nil {
		return nil, fmt.Errorf("failed to list directory: %w", err)
	}

	// 解析输出
	listing := efm.parseDirectoryListing(string(output), path)

	// 记录审计日志
	efm.audit.LogOperation(&FileOperation{
		ID:         fmt.Sprintf("list_%d", time.Now().UnixNano()),
		Type:       "list",
		UserID:     userID,
		HostID:     hostID,
		SourcePath: path,
		Status:     "completed",
		StartTime:  time.Now(),
		Metadata: map[string]interface{}{
			"file_count": len(listing.Files),
			"dir_count":  len(listing.Directories),
		},
	})

	return listing, nil
}

// parseDirectoryListing 解析目录列表输出
func (efm *EnhancedFileManager) parseDirectoryListing(output, path string) *DirectoryListing {
	lines := strings.Split(strings.TrimSpace(output), "\n")
	listing := &DirectoryListing{
		Path:        path,
		Files:       make([]*FileInfo, 0),
		Directories: make([]*FileInfo, 0),
		CanRead:     true,
		CanWrite:    true,
		CanExecute:  true,
	}

	for _, line := range lines {
		if strings.HasPrefix(line, "total ") || line == "" {
			continue
		}

		if strings.HasPrefix(line, "ERROR:") {
			listing.CanRead = false
			continue
		}

		fileInfo := efm.parseFileInfo(line, path)
		if fileInfo != nil {
			if fileInfo.IsDirectory {
				listing.Directories = append(listing.Directories, fileInfo)
			} else {
				listing.Files = append(listing.Files, fileInfo)
				listing.TotalSize += fileInfo.Size
			}
		}
	}

	listing.TotalFiles = len(listing.Files)
	return listing
}

// parseFileInfo 解析文件信息
func (efm *EnhancedFileManager) parseFileInfo(line, basePath string) *FileInfo {
	fields := strings.Fields(line)
	if len(fields) < 9 {
		return nil
	}

	name := strings.Join(fields[8:], " ")
	if name == "." || name == ".." {
		return nil
	}

	permissions := fields[0]
	isDirectory := strings.HasPrefix(permissions, "d")
	isSymlink := strings.HasPrefix(permissions, "l")

	// 解析文件大小
	var size int64
	if !isDirectory {
		fmt.Sscanf(fields[4], "%d", &size)
	}

	// 构建完整路径
	fullPath := filepath.Join(basePath, name)

	fileInfo := &FileInfo{
		ID:          fmt.Sprintf("%x", md5.Sum([]byte(fullPath))),
		Name:        name,
		Path:        fullPath,
		Size:        size,
		IsDirectory: isDirectory,
		IsSymlink:   isSymlink,
		Permissions: permissions,
		Owner:       fields[2],
		Group:       fields[3],
		Extension:   filepath.Ext(name),
		Metadata:    make(map[string]interface{}),
	}

	// 设置MIME类型
	if !isDirectory {
		fileInfo.MimeType = efm.getMimeType(fileInfo.Extension)
	}

	return fileInfo
}

// getMimeType 获取MIME类型
func (efm *EnhancedFileManager) getMimeType(extension string) string {
	mimeTypes := map[string]string{
		".txt":  "text/plain",
		".log":  "text/plain",
		".conf": "text/plain",
		".json": "application/json",
		".yaml": "application/x-yaml",
		".yml":  "application/x-yaml",
		".xml":  "application/xml",
		".csv":  "text/csv",
		".md":   "text/markdown",
		".sh":   "application/x-sh",
		".py":   "text/x-python",
		".js":   "application/javascript",
		".html": "text/html",
		".css":  "text/css",
		".sql":  "application/sql",
	}

	if mimeType, exists := mimeTypes[strings.ToLower(extension)]; exists {
		return mimeType
	}

	return "application/octet-stream"
}

// cleanupRoutine 清理协程
func (efm *EnhancedFileManager) cleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-efm.stopChan:
			return
		case <-ticker.C:
			efm.performCleanup()
		}
	}
}

// performCleanup 执行清理
func (efm *EnhancedFileManager) performCleanup() {
	// 清理临时文件
	efm.cleanupTempFiles()
	
	// 清理过期缓存
	efm.cache.Cleanup()
	
	efm.logger.Debug("File manager cleanup completed")
}

// cleanupTempFiles 清理临时文件
func (efm *EnhancedFileManager) cleanupTempFiles() {
	tempDir := efm.config.TempDirectory
	
	filepath.Walk(tempDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return nil
		}
		
		// 删除超过1小时的临时文件
		if time.Since(info.ModTime()) > 1*time.Hour {
			os.Remove(path)
		}
		
		return nil
	})
}

// GetMetrics 获取指标
func (efm *EnhancedFileManager) GetMetrics() *FileManagerMetrics {
	efm.mutex.RLock()
	defer efm.mutex.RUnlock()

	metrics := *efm.metrics
	return &metrics
}

// UploadFile 上传文件
func (efm *EnhancedFileManager) UploadFile(ctx context.Context, req *FileUploadRequest) (*FileOperation, error) {
	if !efm.running {
		return nil, fmt.Errorf("enhanced file manager is not running")
	}

	// 权限检查
	if !efm.permissions.CanWrite(req.UserID, req.HostID, req.TargetPath) {
		return nil, fmt.Errorf("permission denied: cannot write to %s", req.TargetPath)
	}

	// 安全检查
	if !efm.security.IsFileAllowed(req.FileName) {
		return nil, fmt.Errorf("security violation: file type not allowed")
	}

	if req.FileSize > efm.config.MaxFileSize {
		return nil, fmt.Errorf("file too large: %d bytes (max: %d)", req.FileSize, efm.config.MaxFileSize)
	}

	// 创建文件操作记录
	operation := &FileOperation{
		ID:         fmt.Sprintf("upload_%d", time.Now().UnixNano()),
		Type:       "upload",
		UserID:     req.UserID,
		HostID:     req.HostID,
		TargetPath: req.TargetPath,
		FileName:   req.FileName,
		FileSize:   req.FileSize,
		MimeType:   req.MimeType,
		Status:     "processing",
		Progress:   0,
		StartTime:  time.Now(),
		Metadata:   make(map[string]interface{}),
	}

	// 异步处理上传
	go efm.processUpload(ctx, operation, req)

	return operation, nil
}

// FileUploadRequest 文件上传请求
type FileUploadRequest struct {
	UserID     int64                 `json:"user_id"`
	HostID     int64                 `json:"host_id"`
	TargetPath string                `json:"target_path"`
	FileName   string                `json:"file_name"`
	FileSize   int64                 `json:"file_size"`
	MimeType   string                `json:"mime_type"`
	File       multipart.File        `json:"-"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// processUpload 处理文件上传
func (efm *EnhancedFileManager) processUpload(ctx context.Context, operation *FileOperation, req *FileUploadRequest) {
	defer req.File.Close()

	// 更新指标
	efm.updateMetrics(func(m *FileManagerMetrics) {
		m.TotalOperations++
		m.TotalUploads++
		m.ActiveOperations++
	})

	defer efm.updateMetrics(func(m *FileManagerMetrics) {
		m.ActiveOperations--
	})

	// 创建临时文件
	tempFile, err := efm.createTempFile(req.FileName)
	if err != nil {
		efm.completeOperation(operation, "failed", fmt.Sprintf("Failed to create temp file: %v", err))
		return
	}
	defer os.Remove(tempFile)

	// 复制文件内容到临时文件
	written, err := efm.copyWithProgress(tempFile, req.File, operation)
	if err != nil {
		efm.completeOperation(operation, "failed", fmt.Sprintf("Failed to copy file: %v", err))
		return
	}

	operation.FileSize = written

	// 获取主机连接
	var host model.Host
	if err := efm.db.First(&host, req.HostID).Error; err != nil {
		efm.completeOperation(operation, "failed", fmt.Sprintf("Host not found: %v", err))
		return
	}

	// 上传到远程主机
	if err := efm.uploadToRemoteHost(&host, tempFile, req.TargetPath, req.FileName); err != nil {
		efm.completeOperation(operation, "failed", fmt.Sprintf("Failed to upload to remote host: %v", err))
		return
	}

	// 完成操作
	efm.completeOperation(operation, "completed", "File uploaded successfully")

	// 更新指标
	efm.updateMetrics(func(m *FileManagerMetrics) {
		m.SuccessOperations++
		m.TotalBytesUploaded += written
		if m.TotalUploads > 0 {
			m.AvgUploadSpeed = float64(m.TotalBytesUploaded) / float64(m.TotalUploads) / 1024 / 1024 // MB/s
		}
		m.LastActivity = time.Now()
	})

	// 记录审计日志
	efm.audit.LogOperation(operation)
}

// createTempFile 创建临时文件
func (efm *EnhancedFileManager) createTempFile(fileName string) (string, error) {
	tempPath := filepath.Join(efm.config.TempDirectory, fmt.Sprintf("%d_%s", time.Now().UnixNano(), fileName))
	file, err := os.Create(tempPath)
	if err != nil {
		return "", err
	}
	file.Close()
	return tempPath, nil
}

// copyWithProgress 带进度的文件复制
func (efm *EnhancedFileManager) copyWithProgress(dst string, src io.Reader, operation *FileOperation) (int64, error) {
	dstFile, err := os.OpenFile(dst, os.O_WRONLY|os.O_CREATE|os.O_TRUNC, 0644)
	if err != nil {
		return 0, err
	}
	defer dstFile.Close()

	var written int64
	buffer := make([]byte, 32*1024) // 32KB buffer

	for {
		n, err := src.Read(buffer)
		if n > 0 {
			if _, writeErr := dstFile.Write(buffer[:n]); writeErr != nil {
				return written, writeErr
			}
			written += int64(n)

			// 更新进度
			if operation.FileSize > 0 {
				progress := int(float64(written) / float64(operation.FileSize) * 100)
				efm.updateOperationProgress(operation, progress)
			}
		}
		if err == io.EOF {
			break
		}
		if err != nil {
			return written, err
		}
	}

	return written, nil
}

// uploadToRemoteHost 上传到远程主机
func (efm *EnhancedFileManager) uploadToRemoteHost(host *model.Host, localPath, remotePath, fileName string) error {
	// 获取SSH连接
	sshConn, err := efm.sshPool.GetConnection(host)
	if err != nil {
		return fmt.Errorf("failed to get SSH connection: %w", err)
	}

	// 创建SSH会话
	session, err := sshConn.Client.NewSession()
	if err != nil {
		return fmt.Errorf("failed to create SSH session: %w", err)
	}
	defer session.Close()

	// 读取本地文件
	localFile, err := os.Open(localPath)
	if err != nil {
		return fmt.Errorf("failed to open local file: %w", err)
	}
	defer localFile.Close()

	// 构建远程文件路径
	remoteFilePath := filepath.Join(remotePath, fileName)

	// 使用SCP协议上传文件
	cmd := fmt.Sprintf("cat > '%s'", remoteFilePath)
	session.Stdin = localFile

	if err := session.Run(cmd); err != nil {
		return fmt.Errorf("failed to upload file via SCP: %w", err)
	}

	return nil
}

// DownloadFile 下载文件
func (efm *EnhancedFileManager) DownloadFile(ctx context.Context, req *FileDownloadRequest) (*FileOperation, error) {
	if !efm.running {
		return nil, fmt.Errorf("enhanced file manager is not running")
	}

	// 权限检查
	if !efm.permissions.CanRead(req.UserID, req.HostID, req.SourcePath) {
		return nil, fmt.Errorf("permission denied: cannot read %s", req.SourcePath)
	}

	// 安全检查
	if !efm.security.IsPathSafe(req.SourcePath) {
		return nil, fmt.Errorf("security violation: unsafe path %s", req.SourcePath)
	}

	// 创建文件操作记录
	operation := &FileOperation{
		ID:         fmt.Sprintf("download_%d", time.Now().UnixNano()),
		Type:       "download",
		UserID:     req.UserID,
		HostID:     req.HostID,
		SourcePath: req.SourcePath,
		Status:     "processing",
		Progress:   0,
		StartTime:  time.Now(),
		Metadata:   make(map[string]interface{}),
	}

	// 异步处理下载
	go efm.processDownload(ctx, operation, req)

	return operation, nil
}

// FileDownloadRequest 文件下载请求
type FileDownloadRequest struct {
	UserID     int64                  `json:"user_id"`
	HostID     int64                  `json:"host_id"`
	SourcePath string                 `json:"source_path"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// processDownload 处理文件下载
func (efm *EnhancedFileManager) processDownload(ctx context.Context, operation *FileOperation, req *FileDownloadRequest) {
	// 更新指标
	efm.updateMetrics(func(m *FileManagerMetrics) {
		m.TotalOperations++
		m.TotalDownloads++
		m.ActiveOperations++
	})

	defer efm.updateMetrics(func(m *FileManagerMetrics) {
		m.ActiveOperations--
	})

	// 获取主机信息
	var host model.Host
	if err := efm.db.First(&host, req.HostID).Error; err != nil {
		efm.completeOperation(operation, "failed", fmt.Sprintf("Host not found: %v", err))
		return
	}

	// 从远程主机下载文件
	localPath, err := efm.downloadFromRemoteHost(&host, req.SourcePath, operation)
	if err != nil {
		efm.completeOperation(operation, "failed", fmt.Sprintf("Failed to download from remote host: %v", err))
		return
	}

	// 设置本地文件路径
	operation.TargetPath = localPath

	// 完成操作
	efm.completeOperation(operation, "completed", "File downloaded successfully")

	// 更新指标
	efm.updateMetrics(func(m *FileManagerMetrics) {
		m.SuccessOperations++
		m.TotalBytesDownloaded += operation.FileSize
		if m.TotalDownloads > 0 {
			m.AvgDownloadSpeed = float64(m.TotalBytesDownloaded) / float64(m.TotalDownloads) / 1024 / 1024 // MB/s
		}
		m.LastActivity = time.Now()
	})

	// 记录审计日志
	efm.audit.LogOperation(operation)
}

// downloadFromRemoteHost 从远程主机下载文件
func (efm *EnhancedFileManager) downloadFromRemoteHost(host *model.Host, remotePath string, operation *FileOperation) (string, error) {
	// 获取SSH连接
	sshConn, err := efm.sshPool.GetConnection(host)
	if err != nil {
		return "", fmt.Errorf("failed to get SSH connection: %w", err)
	}

	// 创建SSH会话
	session, err := sshConn.Client.NewSession()
	if err != nil {
		return "", fmt.Errorf("failed to create SSH session: %w", err)
	}
	defer session.Close()

	// 创建本地临时文件
	fileName := filepath.Base(remotePath)
	localPath := filepath.Join(efm.config.TempDirectory, fmt.Sprintf("%d_%s", time.Now().UnixNano(), fileName))

	localFile, err := os.Create(localPath)
	if err != nil {
		return "", fmt.Errorf("failed to create local file: %w", err)
	}
	defer localFile.Close()

	// 使用cat命令读取远程文件
	cmd := fmt.Sprintf("cat '%s'", remotePath)
	session.Stdout = localFile

	if err := session.Run(cmd); err != nil {
		os.Remove(localPath)
		return "", fmt.Errorf("failed to download file via SSH: %w", err)
	}

	// 获取文件大小
	if stat, err := localFile.Stat(); err == nil {
		operation.FileSize = stat.Size()
	}

	return localPath, nil
}

// updateOperationProgress 更新操作进度
func (efm *EnhancedFileManager) updateOperationProgress(operation *FileOperation, progress int) {
	operation.Progress = progress

	// 发送WebSocket通知
	if efm.wsManager != nil {
		message := &WSMessage{
			ID:       fmt.Sprintf("file_progress_%s", operation.ID),
			Type:     "file_operation_progress",
			Channel:  "file_manager",
			Priority: "normal",
			Data: map[string]interface{}{
				"operation_id": operation.ID,
				"progress":     progress,
				"status":       operation.Status,
			},
			Timestamp: time.Now(),
			UserID:    operation.UserID,
			Metadata: map[string]interface{}{
				"source": "enhanced_file_manager",
			},
		}
		efm.wsManager.SendToUser(operation.UserID, message)
	}
}

// completeOperation 完成操作
func (efm *EnhancedFileManager) completeOperation(operation *FileOperation, status, message string) {
	now := time.Now()
	operation.Status = status
	operation.EndTime = &now
	operation.Progress = 100

	if status == "failed" {
		operation.Error = message
		efm.updateMetrics(func(m *FileManagerMetrics) {
			m.FailedOperations++
		})
	}

	// 发送WebSocket通知
	if efm.wsManager != nil {
		wsMessage := &WSMessage{
			ID:       fmt.Sprintf("file_complete_%s", operation.ID),
			Type:     "file_operation_complete",
			Channel:  "file_manager",
			Priority: "normal",
			Data: map[string]interface{}{
				"operation": operation,
				"message":   message,
			},
			Timestamp: time.Now(),
			UserID:    operation.UserID,
			Metadata: map[string]interface{}{
				"source": "enhanced_file_manager",
			},
		}
		efm.wsManager.SendToUser(operation.UserID, wsMessage)
	}
}

// updateMetrics 更新指标
func (efm *EnhancedFileManager) updateMetrics(updateFunc func(*FileManagerMetrics)) {
	efm.mutex.Lock()
	defer efm.mutex.Unlock()
	updateFunc(efm.metrics)
}

// 支持组件实现

// FilePermissionManager 文件权限管理器
type FilePermissionManager struct {
	logger *logrus.Logger
}

// NewFilePermissionManager 创建文件权限管理器
func NewFilePermissionManager(logger *logrus.Logger) *FilePermissionManager {
	return &FilePermissionManager{
		logger: logger,
	}
}

// Start 启动权限管理器
func (fpm *FilePermissionManager) Start(ctx context.Context) error {
	fpm.logger.Info("File permission manager started")
	return nil
}

// CanRead 检查读取权限
func (fpm *FilePermissionManager) CanRead(userID, hostID int64, path string) bool {
	// 简化实现：基本权限检查
	return fpm.checkBasicPermission(userID, hostID, path, "read")
}

// CanWrite 检查写入权限
func (fpm *FilePermissionManager) CanWrite(userID, hostID int64, path string) bool {
	// 简化实现：基本权限检查
	return fpm.checkBasicPermission(userID, hostID, path, "write")
}

// checkBasicPermission 检查基本权限
func (fpm *FilePermissionManager) checkBasicPermission(userID, hostID int64, path, action string) bool {
	// 简化实现：允许所有操作
	// 在实际应用中，这里应该查询数据库或权限服务

	// 禁止访问敏感目录
	sensitiveDirectories := []string{
		"/etc/shadow",
		"/etc/passwd",
		"/root/.ssh",
		"/home/<USER>/.ssh",
		"/var/lib/mysql",
		"/etc/ssl/private",
	}

	for _, sensitive := range sensitiveDirectories {
		if strings.Contains(path, sensitive) {
			fpm.logger.WithFields(logrus.Fields{
				"user_id": userID,
				"host_id": hostID,
				"path":    path,
				"action":  action,
			}).Warn("Access denied to sensitive directory")
			return false
		}
	}

	return true
}

// FileSecurityManager 文件安全管理器
type FileSecurityManager struct {
	logger *logrus.Logger
	config *FileManagerConfig
}

// NewFileSecurityManager 创建文件安全管理器
func NewFileSecurityManager(logger *logrus.Logger, config *FileManagerConfig) *FileSecurityManager {
	return &FileSecurityManager{
		logger: logger,
		config: config,
	}
}

// Start 启动安全管理器
func (fsm *FileSecurityManager) Start(ctx context.Context) error {
	fsm.logger.Info("File security manager started")
	return nil
}

// IsFileAllowed 检查文件是否允许
func (fsm *FileSecurityManager) IsFileAllowed(fileName string) bool {
	extension := strings.ToLower(filepath.Ext(fileName))

	// 检查禁止的扩展名
	for _, blocked := range fsm.config.BlockedExtensions {
		if extension == blocked {
			fsm.logger.WithFields(logrus.Fields{
				"filename":  fileName,
				"extension": extension,
			}).Warn("File blocked due to extension")
			return false
		}
	}

	// 如果有允许列表，检查是否在列表中
	if len(fsm.config.AllowedExtensions) > 0 {
		for _, allowed := range fsm.config.AllowedExtensions {
			if extension == allowed {
				return true
			}
		}
		fsm.logger.WithFields(logrus.Fields{
			"filename":  fileName,
			"extension": extension,
		}).Warn("File not in allowed extensions list")
		return false
	}

	return true
}

// IsPathSafe 检查路径是否安全
func (fsm *FileSecurityManager) IsPathSafe(path string) bool {
	// 检查路径遍历攻击
	if strings.Contains(path, "..") {
		fsm.logger.WithField("path", path).Warn("Path traversal attempt detected")
		return false
	}

	// 检查绝对路径
	if !filepath.IsAbs(path) {
		fsm.logger.WithField("path", path).Warn("Relative path not allowed")
		return false
	}

	// 检查危险路径
	dangerousPaths := []string{
		"/dev/",
		"/proc/",
		"/sys/",
	}

	for _, dangerous := range dangerousPaths {
		if strings.HasPrefix(path, dangerous) {
			fsm.logger.WithField("path", path).Warn("Access to dangerous path denied")
			return false
		}
	}

	return true
}

// FileAuditManager 文件审计管理器
type FileAuditManager struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewFileAuditManager 创建文件审计管理器
func NewFileAuditManager(db *gorm.DB, logger *logrus.Logger) *FileAuditManager {
	return &FileAuditManager{
		db:     db,
		logger: logger,
	}
}

// Start 启动审计管理器
func (fam *FileAuditManager) Start(ctx context.Context) error {
	// 自动迁移审计表
	if err := fam.db.AutoMigrate(&FileAuditLog{}); err != nil {
		return fmt.Errorf("failed to migrate file audit log table: %w", err)
	}

	fam.logger.Info("File audit manager started")
	return nil
}

// FileAuditLog 文件审计日志
type FileAuditLog struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	OperationID string    `json:"operation_id" gorm:"not null;index"`
	UserID      int64     `json:"user_id" gorm:"not null;index"`
	HostID      int64     `json:"host_id" gorm:"not null;index"`
	Action      string    `json:"action" gorm:"not null"`
	SourcePath  string    `json:"source_path"`
	TargetPath  string    `json:"target_path"`
	FileName    string    `json:"file_name"`
	FileSize    int64     `json:"file_size"`
	Status      string    `json:"status" gorm:"not null"`
	Error       string    `json:"error"`
	IPAddress   string    `json:"ip_address"`
	UserAgent   string    `json:"user_agent"`
	Timestamp   time.Time `json:"timestamp" gorm:"not null;index"`
	CreatedAt   time.Time `json:"created_at"`
}

// LogOperation 记录操作日志
func (fam *FileAuditManager) LogOperation(operation *FileOperation) error {
	auditLog := &FileAuditLog{
		OperationID: operation.ID,
		UserID:      operation.UserID,
		HostID:      operation.HostID,
		Action:      operation.Type,
		SourcePath:  operation.SourcePath,
		TargetPath:  operation.TargetPath,
		FileName:    operation.FileName,
		FileSize:    operation.FileSize,
		Status:      operation.Status,
		Error:       operation.Error,
		Timestamp:   operation.StartTime,
		CreatedAt:   time.Now(),
	}

	if err := fam.db.Create(auditLog).Error; err != nil {
		fam.logger.WithError(err).Error("Failed to create audit log")
		return err
	}

	return nil
}

// FileCache 文件缓存
type FileCache struct {
	logger *logrus.Logger
	cache  map[string]*CacheEntry
	mutex  sync.RWMutex
}

// CacheEntry 缓存条目
type CacheEntry struct {
	Data      interface{}
	ExpiresAt time.Time
}

// NewFileCache 创建文件缓存
func NewFileCache(logger *logrus.Logger) *FileCache {
	return &FileCache{
		logger: logger,
		cache:  make(map[string]*CacheEntry),
	}
}

// Start 启动缓存
func (fc *FileCache) Start(ctx context.Context) error {
	fc.logger.Info("File cache started")
	return nil
}

// Get 获取缓存
func (fc *FileCache) Get(key string) (interface{}, bool) {
	fc.mutex.RLock()
	defer fc.mutex.RUnlock()

	entry, exists := fc.cache[key]
	if !exists {
		return nil, false
	}

	if time.Now().After(entry.ExpiresAt) {
		delete(fc.cache, key)
		return nil, false
	}

	return entry.Data, true
}

// Set 设置缓存
func (fc *FileCache) Set(key string, data interface{}, ttl time.Duration) {
	fc.mutex.Lock()
	defer fc.mutex.Unlock()

	fc.cache[key] = &CacheEntry{
		Data:      data,
		ExpiresAt: time.Now().Add(ttl),
	}
}

// Cleanup 清理过期缓存
func (fc *FileCache) Cleanup() {
	fc.mutex.Lock()
	defer fc.mutex.Unlock()

	now := time.Now()
	for key, entry := range fc.cache {
		if now.After(entry.ExpiresAt) {
			delete(fc.cache, key)
		}
	}
}
