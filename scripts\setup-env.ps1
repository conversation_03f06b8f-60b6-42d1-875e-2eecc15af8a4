# AI运维管理平台 - 环境变量设置脚本 (PowerShell)
# 用于Windows环境

Write-Host "=== AI运维管理平台环境变量设置 ===" -ForegroundColor Green

# 生成32字节的随机加密密钥
function Generate-EncryptionKey {
    $bytes = New-Object byte[] 32
    [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
    return [System.Convert]::ToBase64String($bytes).Substring(0, 32)
}

# 生成JWT密钥
function Generate-JWTSecret {
    $bytes = New-Object byte[] 64
    [System.Security.Cryptography.RNGCryptoServiceProvider]::Create().GetBytes($bytes)
    return [System.Convert]::ToBase64String($bytes)
}

Write-Host "正在生成安全密钥..." -ForegroundColor Yellow

# 生成密钥
$encryptionKey = Generate-EncryptionKey
$jwtSecret = Generate-JWTSecret

Write-Host "生成的密钥信息:" -ForegroundColor Cyan
Write-Host "加密密钥长度: $($encryptionKey.Length) 字节" -ForegroundColor White
Write-Host "JWT密钥长度: $($jwtSecret.Length) 字符" -ForegroundColor White

# 设置环境变量
Write-Host "`n正在设置环境变量..." -ForegroundColor Yellow

# 设置用户级环境变量
[Environment]::SetEnvironmentVariable("AIOPS_ENCRYPTION_KEY", $encryptionKey, "User")
[Environment]::SetEnvironmentVariable("AIOPS_JWT_SECRET", $jwtSecret, "User")

# 提示用户设置DeepSeek API密钥
Write-Host "`n请手动设置以下环境变量:" -ForegroundColor Red
Write-Host "AIOPS_DEEPSEEK_API_KEY=你的DeepSeek API密钥" -ForegroundColor Yellow

Write-Host "`n环境变量设置命令 (请在新的PowerShell窗口中运行):" -ForegroundColor Cyan
Write-Host "[Environment]::SetEnvironmentVariable('AIOPS_DEEPSEEK_API_KEY', '你的API密钥', 'User')" -ForegroundColor White

Write-Host "`n或者在系统环境变量中设置:" -ForegroundColor Cyan
Write-Host "AIOPS_ENCRYPTION_KEY=$encryptionKey" -ForegroundColor White
Write-Host "AIOPS_JWT_SECRET=$jwtSecret" -ForegroundColor White
Write-Host "AIOPS_DEEPSEEK_API_KEY=你的DeepSeek API密钥" -ForegroundColor White

Write-Host "`n设置完成！请重启PowerShell或重新加载环境变量。" -ForegroundColor Green
Write-Host "验证环境变量: Get-ChildItem Env:AIOPS_*" -ForegroundColor Cyan

# 创建.env文件作为备份
$envContent = @"
# AI运维管理平台环境变量配置
# 请将此文件重命名为.env并放在项目根目录

# 安全配置
AIOPS_ENCRYPTION_KEY=$encryptionKey
AIOPS_JWT_SECRET=$jwtSecret

# DeepSeek API配置 (请替换为真实的API密钥)
AIOPS_DEEPSEEK_API_KEY=your-deepseek-api-key-here

# 可选配置
# AIOPS_APP_ENV=production
# AIOPS_APP_DEBUG=false
# AIOPS_APP_PORT=8080
"@

$envContent | Out-File -FilePath "env.example" -Encoding UTF8
Write-Host "`n已创建 env.example 文件，包含生成的密钥配置。" -ForegroundColor Green
