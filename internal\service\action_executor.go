package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ActionExecutor 操作执行器
type ActionExecutor struct {
	db                *gorm.DB
	logger            *logrus.Logger
	hostService       HostService
	toolManager       *ToolManager
	securityValidator *SecurityValidator
}

// ExecuteActionRequest 执行操作请求
type ExecuteActionRequest struct {
	ActionID   string                 `json:"action_id" binding:"required"`
	ActionType string                 `json:"action_type" binding:"required"`
	Command    string                 `json:"command" binding:"required"`
	Parameters map[string]interface{} `json:"parameters"`
	HostID     *int64                 `json:"host_id"`
	UserID     int64                  `json:"user_id" binding:"required"`
	SessionID  string                 `json:"session_id" binding:"required"`
	Confirmed  bool                   `json:"confirmed"`
	RiskLevel  string                 `json:"risk_level"`
}

// ExecuteActionResponse 执行操作响应
type ExecuteActionResponse struct {
	ActionID     string                 `json:"action_id"`
	Status       string                 `json:"status"`
	Result       interface{}            `json:"result,omitempty"`
	Output       string                 `json:"output,omitempty"`
	Error        string                 `json:"error,omitempty"`
	ExitCode     *int                   `json:"exit_code,omitempty"`
	Duration     time.Duration          `json:"duration"`
	ExecutedAt   time.Time              `json:"executed_at"`
	Metadata     map[string]interface{} `json:"metadata,omitempty"`
	RequiresAuth bool                   `json:"requires_auth,omitempty"`
	WarningMsg   string                 `json:"warning_msg,omitempty"`
}

// NewActionExecutor 创建操作执行器
func NewActionExecutor(db *gorm.DB, logger *logrus.Logger, hostService HostService, toolManager *ToolManager) *ActionExecutor {
	securityValidator := NewSecurityValidator(db, logger)

	return &ActionExecutor{
		db:                db,
		logger:            logger,
		hostService:       hostService,
		toolManager:       toolManager,
		securityValidator: securityValidator,
	}
}

// ExecuteAction 执行操作
func (ae *ActionExecutor) ExecuteAction(ctx context.Context, req *ExecuteActionRequest) (*ExecuteActionResponse, error) {
	start := time.Now()

	ae.logger.WithFields(logrus.Fields{
		"action_id":   req.ActionID,
		"action_type": req.ActionType,
		"user_id":     req.UserID,
		"session_id":  req.SessionID,
		"risk_level":  req.RiskLevel,
	}).Info("Starting action execution")

	// 安全验证
	securityReq := &SecurityValidationRequest{
		UserID:     req.UserID,
		Command:    req.Command,
		ActionType: req.ActionType,
		HostID:     req.HostID,
		Parameters: req.Parameters,
		SessionID:  req.SessionID,
		// ClientIP 和 UserAgent 应该从请求上下文中获取
	}

	securityResult, err := ae.securityValidator.ValidateAction(ctx, securityReq)
	if err != nil {
		ae.logger.WithError(err).Error("Security validation failed")
		return &ExecuteActionResponse{
			ActionID:   req.ActionID,
			Status:     "security_error",
			Error:      "Security validation failed",
			ExecutedAt: time.Now(),
		}, err
	}

	// 检查是否被安全策略阻止
	if !securityResult.Allowed {
		return &ExecuteActionResponse{
			ActionID:     req.ActionID,
			Status:       "blocked",
			Error:        securityResult.Reason,
			RequiresAuth: securityResult.RequiresAuth,
			WarningMsg:   securityResult.Reason,
			ExecutedAt:   time.Now(),
		}, nil
	}

	// 检查是否需要确认
	if !req.Confirmed && (securityResult.RequiresAuth || securityResult.RequiresMFA) {
		return &ExecuteActionResponse{
			ActionID:     req.ActionID,
			Status:       "requires_confirmation",
			RequiresAuth: securityResult.RequiresAuth,
			WarningMsg:   fmt.Sprintf("风险级别: %s, %s", securityResult.RiskLevel, strings.Join(securityResult.Requirements, ", ")),
			ExecutedAt:   time.Now(),
			Metadata: map[string]interface{}{
				"risk_level":   securityResult.RiskLevel,
				"risk_score":   securityResult.RiskScore,
				"warnings":     securityResult.Warnings,
				"requirements": securityResult.Requirements,
			},
		}, nil
	}

	// 记录操作日志
	operationLog := &model.OperationLog{
		OperationType: req.ActionType,
		Command:       req.Command,
		Status:        "executing",
		UserID:        req.UserID,
		SessionID:     req.SessionID,
		HostID:        req.HostID,
		ExecutedAt:    time.Now(),
	}

	if err := ae.db.Create(operationLog).Error; err != nil {
		ae.logger.WithError(err).Error("Failed to create operation log")
	}

	// 执行操作
	result, err := ae.executeActionByType(ctx, req)

	// 更新操作日志
	duration := time.Since(start)
	operationLog.DurationMs = int(duration.Milliseconds())

	if err != nil {
		operationLog.Status = "failed"
		operationLog.ErrorMessage = err.Error()
		result = &ExecuteActionResponse{
			ActionID:   req.ActionID,
			Status:     "failed",
			Error:      err.Error(),
			Duration:   duration,
			ExecutedAt: start,
		}
	} else {
		operationLog.Status = "success"
		if result.Output != "" {
			operationLog.Result = result.Output
		}
		if result.ExitCode != nil {
			operationLog.ExitCode = result.ExitCode
		}
	}

	// 保存操作日志
	if err := ae.db.Save(operationLog).Error; err != nil {
		ae.logger.WithError(err).Error("Failed to update operation log")
	}

	ae.logger.WithFields(logrus.Fields{
		"action_id": req.ActionID,
		"status":    result.Status,
		"duration":  duration.Milliseconds(),
	}).Info("Action execution completed")

	return result, err
}

// executeActionByType 根据类型执行操作
func (ae *ActionExecutor) executeActionByType(ctx context.Context, req *ExecuteActionRequest) (*ExecuteActionResponse, error) {
	switch req.ActionType {
	case "service_management":
		return ae.executeServiceManagement(ctx, req)
	case "monitoring":
		return ae.executeMonitoring(ctx, req)
	case "log_analysis":
		return ae.executeLogAnalysis(ctx, req)
	case "command_execution":
		return ae.executeCommand(ctx, req)
	case "backup_restore":
		return ae.executeBackupRestore(ctx, req)
	default:
		return nil, fmt.Errorf("unsupported action type: %s", req.ActionType)
	}
}

// executeServiceManagement 执行服务管理操作
func (ae *ActionExecutor) executeServiceManagement(ctx context.Context, req *ExecuteActionRequest) (*ExecuteActionResponse, error) {
	if req.HostID == nil {
		return nil, fmt.Errorf("host_id is required for service management")
	}

	cmdReq := &model.CommandExecuteRequest{
		Command: req.Command,
		Timeout: 30,
	}

	result, err := ae.hostService.ExecuteCommand(*req.HostID, cmdReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute service command: %w", err)
	}

	return &ExecuteActionResponse{
		ActionID:   req.ActionID,
		Status:     ae.getStatusFromExitCode(result.ExitCode),
		Output:     result.Stdout,
		Error:      result.Stderr,
		ExitCode:   &result.ExitCode,
		Duration:   time.Duration(result.Duration) * time.Millisecond,
		ExecutedAt: result.ExecutedAt,
		Metadata: map[string]interface{}{
			"command": result.Command,
			"host_id": req.HostID,
		},
	}, nil
}

// executeMonitoring 执行监控操作
func (ae *ActionExecutor) executeMonitoring(ctx context.Context, req *ExecuteActionRequest) (*ExecuteActionResponse, error) {
	if req.HostID == nil {
		return nil, fmt.Errorf("host_id is required for monitoring")
	}

	// 使用工具管理器执行监控工具
	toolCall := ToolCall{
		ID:   fmt.Sprintf("monitoring_%d", time.Now().UnixNano()),
		Type: "function",
		Function: struct {
			Name      string `json:"name"`
			Arguments string `json:"arguments"`
		}{
			Name:      "get_system_metrics",
			Arguments: fmt.Sprintf(`{"host_id": %d}`, *req.HostID),
		},
	}

	result, err := ae.toolManager.ExecuteTool(ctx, toolCall)
	if err != nil {
		return nil, fmt.Errorf("failed to execute monitoring tool: %w", err)
	}

	// 序列化结果
	resultData, _ := json.Marshal(result)

	return &ExecuteActionResponse{
		ActionID:   req.ActionID,
		Status:     "success",
		Result:     result,
		Output:     string(resultData),
		Duration:   time.Since(time.Now()),
		ExecutedAt: time.Now(),
		Metadata: map[string]interface{}{
			"tool_name": "get_system_metrics",
			"host_id":   req.HostID,
		},
	}, nil
}

// executeLogAnalysis 执行日志分析操作
func (ae *ActionExecutor) executeLogAnalysis(ctx context.Context, req *ExecuteActionRequest) (*ExecuteActionResponse, error) {
	if req.HostID == nil {
		return nil, fmt.Errorf("host_id is required for log analysis")
	}

	cmdReq := &model.CommandExecuteRequest{
		Command: req.Command,
		Timeout: 15,
	}

	result, err := ae.hostService.ExecuteCommand(*req.HostID, cmdReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute log command: %w", err)
	}

	return &ExecuteActionResponse{
		ActionID:   req.ActionID,
		Status:     ae.getStatusFromExitCode(result.ExitCode),
		Output:     result.Stdout,
		Error:      result.Stderr,
		ExitCode:   &result.ExitCode,
		Duration:   time.Duration(result.Duration) * time.Millisecond,
		ExecutedAt: result.ExecutedAt,
		Metadata: map[string]interface{}{
			"command": result.Command,
			"host_id": req.HostID,
		},
	}, nil
}

// executeCommand 执行通用命令
func (ae *ActionExecutor) executeCommand(ctx context.Context, req *ExecuteActionRequest) (*ExecuteActionResponse, error) {
	if req.HostID == nil {
		return nil, fmt.Errorf("host_id is required for command execution")
	}

	cmdReq := &model.CommandExecuteRequest{
		Command: req.Command,
		Timeout: 60, // 通用命令给更长的超时时间
	}

	result, err := ae.hostService.ExecuteCommand(*req.HostID, cmdReq)
	if err != nil {
		return nil, fmt.Errorf("failed to execute command: %w", err)
	}

	return &ExecuteActionResponse{
		ActionID:   req.ActionID,
		Status:     ae.getStatusFromExitCode(result.ExitCode),
		Output:     result.Stdout,
		Error:      result.Stderr,
		ExitCode:   &result.ExitCode,
		Duration:   time.Duration(result.Duration) * time.Millisecond,
		ExecutedAt: result.ExecutedAt,
		Metadata: map[string]interface{}{
			"command": result.Command,
			"host_id": req.HostID,
		},
	}, nil
}

// executeBackupRestore 执行备份恢复操作
func (ae *ActionExecutor) executeBackupRestore(ctx context.Context, req *ExecuteActionRequest) (*ExecuteActionResponse, error) {
	// 备份操作通常需要特殊处理
	return &ExecuteActionResponse{
		ActionID:   req.ActionID,
		Status:     "not_implemented",
		Error:      "backup/restore operations are not yet implemented",
		Duration:   0,
		ExecutedAt: time.Now(),
	}, nil
}

// isHighRiskAction 检查是否为高风险操作
func (ae *ActionExecutor) isHighRiskAction(req *ExecuteActionRequest) bool {
	return req.RiskLevel == "high" || req.RiskLevel == "critical"
}

// generateRiskWarning 生成风险警告
func (ae *ActionExecutor) generateRiskWarning(req *ExecuteActionRequest) string {
	switch req.RiskLevel {
	case "high":
		return fmt.Sprintf("此操作具有高风险，可能影响系统稳定性。命令: %s", req.Command)
	case "critical":
		return fmt.Sprintf("此操作具有严重风险，可能导致系统不可用。命令: %s", req.Command)
	default:
		return "此操作需要确认后执行"
	}
}

// getStatusFromExitCode 根据退出码获取状态
func (ae *ActionExecutor) getStatusFromExitCode(exitCode int) string {
	if exitCode == 0 {
		return "success"
	}
	return "failed"
}
