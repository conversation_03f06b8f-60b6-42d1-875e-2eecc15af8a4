/* ========================================
   智能输入组件样式
   类似ChatGPT的高级输入体验
   ======================================== */

/* 智能输入容器 */
.smart-input-container {
  position: relative;
  max-width: 800px;
  margin: 0 auto;
}

/* 输入框包装器 */
.smart-input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--space-3);
  background-color: var(--bg-secondary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-2xl);
  padding: var(--space-3);
  transition: all var(--duration-fast) var(--easing-ease);
  position: relative;
  min-height: 56px;
}

.smart-input-wrapper:focus-within {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 4px rgb(37 99 235 / 0.1);
  background-color: var(--bg-primary);
}

.smart-input-wrapper.has-content {
  border-color: var(--color-primary);
}

/* 主输入区域 */
.smart-input-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

/* 智能文本域 */
.smart-textarea {
  border: none;
  background: none;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  font-family: var(--font-family-sans);
  line-height: 1.5;
  resize: none;
  outline: none;
  min-height: 24px;
  max-height: 200px;
  padding: 0;
  overflow-y: auto;
  word-wrap: break-word;
  white-space: pre-wrap;
}

.smart-textarea::placeholder {
  color: var(--text-tertiary);
}

.smart-textarea::-webkit-scrollbar {
  width: 4px;
}

.smart-textarea::-webkit-scrollbar-track {
  background: transparent;
}

.smart-textarea::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 2px;
}

/* 输入工具栏 */
.input-toolbar {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  opacity: 0;
  transform: translateY(4px);
  transition: all var(--duration-fast) var(--easing-ease);
}

.smart-input-wrapper:focus-within .input-toolbar,
.smart-input-wrapper.has-content .input-toolbar {
  opacity: 1;
  transform: translateY(0);
}

.toolbar-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  font-size: var(--font-size-sm);
}

.toolbar-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-secondary);
}

.toolbar-btn.active {
  background-color: var(--color-primary);
  color: white;
}

/* 发送按钮区域 */
.send-button-area {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
}

.smart-send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border: none;
  border-radius: var(--radius-xl);
  color: white;
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  font-size: 18px;
}

.smart-send-button:hover:not(:disabled) {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.smart-send-button:active:not(:disabled) {
  transform: scale(0.95);
}

.smart-send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  background: var(--bg-tertiary);
}

/* 字符计数器 */
.char-counter {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  opacity: 0;
  transition: opacity var(--duration-fast) var(--easing-ease);
}

.smart-input-wrapper:focus-within .char-counter,
.smart-input-wrapper.has-content .char-counter {
  opacity: 1;
}

.char-counter.warning {
  color: var(--color-warning);
}

.char-counter.error {
  color: var(--color-error);
}

/* 快捷键提示 */
.shortcut-hint {
  position: absolute;
  bottom: -24px;
  right: 0;
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  opacity: 0;
  transition: opacity var(--duration-fast) var(--easing-ease);
  pointer-events: none;
}

.smart-input-wrapper:focus-within .shortcut-hint {
  opacity: 1;
}

/* 自动完成建议 */
.autocomplete-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  max-height: 200px;
  overflow-y: auto;
  z-index: 100;
  margin-top: var(--space-2);
  display: none;
}

.autocomplete-suggestions.show {
  display: block;
}

.suggestion-item {
  padding: var(--space-3);
  cursor: pointer;
  border-bottom: 1px solid var(--border-primary);
  transition: background-color var(--duration-fast) var(--easing-ease);
}

.suggestion-item:last-child {
  border-bottom: none;
}

.suggestion-item:hover,
.suggestion-item.selected {
  background-color: var(--bg-hover);
}

.suggestion-text {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.suggestion-description {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 表情符号选择器 */
.emoji-picker {
  position: absolute;
  bottom: 100%;
  right: 0;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-3);
  margin-bottom: var(--space-2);
  display: none;
  z-index: 100;
  width: 280px;
  max-height: 200px;
  overflow-y: auto;
}

.emoji-picker.show {
  display: block;
}

.emoji-categories {
  display: flex;
  gap: var(--space-1);
  margin-bottom: var(--space-3);
  border-bottom: 1px solid var(--border-primary);
  padding-bottom: var(--space-2);
}

.emoji-category {
  padding: var(--space-1) var(--space-2);
  border: none;
  background: none;
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  transition: all var(--duration-fast) var(--easing-ease);
}

.emoji-category:hover,
.emoji-category.active {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.emoji-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: var(--space-1);
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: 16px;
  transition: all var(--duration-fast) var(--easing-ease);
}

.emoji-item:hover {
  background-color: var(--bg-hover);
  transform: scale(1.2);
}

/* 文件上传区域 */
.file-upload-area {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(37, 99, 235, 0.1);
  border: 2px dashed var(--color-primary);
  border-radius: var(--radius-2xl);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.file-upload-area.show {
  display: flex;
}

.file-upload-content {
  text-align: center;
  color: var(--color-primary);
}

.file-upload-icon {
  font-size: 2rem;
  margin-bottom: var(--space-2);
}

.file-upload-text {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .smart-input-wrapper {
    padding: var(--space-2);
    border-radius: var(--radius-xl);
  }
  
  .smart-send-button {
    width: 36px;
    height: 36px;
    font-size: 16px;
  }
  
  .toolbar-btn {
    width: 24px;
    height: 24px;
    font-size: var(--font-size-xs);
  }
  
  .emoji-picker {
    width: 240px;
    right: -20px;
  }
  
  .emoji-grid {
    grid-template-columns: repeat(6, 1fr);
  }
}

@media (max-width: 480px) {
  .input-toolbar {
    display: none;
  }
  
  .shortcut-hint {
    display: none;
  }
  
  .smart-input-wrapper {
    padding: var(--space-2);
  }
  
  .smart-send-button {
    width: 32px;
    height: 32px;
    font-size: 14px;
  }
}
