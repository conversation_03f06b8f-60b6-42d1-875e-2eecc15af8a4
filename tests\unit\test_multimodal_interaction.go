package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/ai"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🎤 多模态交互引擎测试开始...")

	// 1. 初始化日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 2. 初始化数据库
	db, err := gorm.Open(sqlite.Open("test_multimodal.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect database:", err)
	}

	// 3. 自动迁移数据库表
	if err := db.AutoMigrate(
		&ai.MultimodalInteractionRecord{},
		&ai.MultimodalSessionRecord{},
		&ai.MultimodalContextRecord{},
		&ai.MultimodalMetricsRecord{},
		&ai.UserPreferenceRecord{},
	); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// 4. 创建多模态交互引擎配置
	config := &ai.MultimodalConfig{
		EnableSpeechInput:     true,
		EnableSpeechOutput:    true,
		EnableImageInput:      true,
		EnableGestureInput:    true,
		EnableVideoInput:      false, // 暂时禁用视频
		FusionStrategy:        "weighted_average",
		ResponseTimeout:       30 * time.Second,
		MaxSessionDuration:    2 * time.Hour,
		SpeechLanguage:        "zh-CN",
		ImageQuality:          "high",
		GestureSensitivity:    0.8,
		EnableRealTimeMode:    true,
		CacheSize:             1000,
	}

	// 5. 创建多模态交互引擎
	engine := ai.NewMultimodalInteractionEngine(db, logger, config)

	// 6. 启动引擎
	if err := engine.Start(); err != nil {
		log.Fatal("Failed to start multimodal interaction engine:", err)
	}
	defer engine.Stop()

	fmt.Println("✅ 多模态交互引擎启动成功")

	// 7. 测试文本输入
	fmt.Println("\n🧪 测试 1: 文本输入处理")
	textInput := &ai.MultimodalInput{
		SessionID:   "test_session_001",
		UserID:      1001,
		Timestamp:   time.Now(),
		PrimaryMode: ai.ModeText,
		TextInput: &ai.TextInput{
			Content:  "查看系统状态",
			Language: "zh-CN",
			Encoding: "utf-8",
		},
		Context: map[string]interface{}{
			"source": "web_interface",
		},
	}

	ctx := context.Background()
	response, err := engine.ProcessMultimodalInput(ctx, textInput)
	if err != nil {
		fmt.Printf("❌ 文本输入处理失败: %v\n", err)
	} else {
		fmt.Printf("✅ 文本输入处理成功:\n")
		fmt.Printf("   响应ID: %s\n", response.ResponseID)
		fmt.Printf("   主要模态: %s\n", response.PrimaryMode)
		fmt.Printf("   置信度: %.2f\n", response.Confidence)
		fmt.Printf("   处理时间: %v\n", response.ProcessingTime)
		if response.TextResponse != nil {
			fmt.Printf("   文本响应: %s\n", response.TextResponse.Content)
		}
	}

	// 8. 测试语音输入
	fmt.Println("\n🧪 测试 2: 语音输入处理")
	speechInput := &ai.MultimodalInput{
		SessionID:   "test_session_001",
		UserID:      1001,
		Timestamp:   time.Now(),
		PrimaryMode: ai.ModeSpeech,
		SpeechInput: &ai.SpeechInput{
			AudioData:     make([]byte, 1024*5), // 5KB 模拟音频数据
			Format:        "wav",
			SampleRate:    16000,
			Duration:      2.5,
			Language:      "zh-CN",
			Quality:       "high",
			Transcription: "添加新主机",
			Confidence:    0.92,
		},
		Context: map[string]interface{}{
			"source": "voice_interface",
		},
	}

	response, err = engine.ProcessMultimodalInput(ctx, speechInput)
	if err != nil {
		fmt.Printf("❌ 语音输入处理失败: %v\n", err)
	} else {
		fmt.Printf("✅ 语音输入处理成功:\n")
		fmt.Printf("   响应ID: %s\n", response.ResponseID)
		fmt.Printf("   主要模态: %s\n", response.PrimaryMode)
		fmt.Printf("   置信度: %.2f\n", response.Confidence)
		fmt.Printf("   处理时间: %v\n", response.ProcessingTime)
		if response.SpeechResponse != nil {
			fmt.Printf("   语音响应: %.1f秒音频\n", response.SpeechResponse.Duration)
		}
	}

	// 9. 测试图像输入
	fmt.Println("\n🧪 测试 3: 图像输入处理")
	imageInput := &ai.MultimodalInput{
		SessionID:   "test_session_001",
		UserID:      1001,
		Timestamp:   time.Now(),
		PrimaryMode: ai.ModeImage,
		ImageInput: &ai.ImageInput{
			ImageData:   make([]byte, 1024*50), // 50KB 模拟图像数据
			Format:      "jpg",
			Width:       1920,
			Height:      1080,
			Quality:     "high",
			Description: "系统监控截图",
		},
		Context: map[string]interface{}{
			"source": "screenshot",
		},
	}

	response, err = engine.ProcessMultimodalInput(ctx, imageInput)
	if err != nil {
		fmt.Printf("❌ 图像输入处理失败: %v\n", err)
	} else {
		fmt.Printf("✅ 图像输入处理成功:\n")
		fmt.Printf("   响应ID: %s\n", response.ResponseID)
		fmt.Printf("   主要模态: %s\n", response.PrimaryMode)
		fmt.Printf("   置信度: %.2f\n", response.Confidence)
		fmt.Printf("   处理时间: %v\n", response.ProcessingTime)
		if response.ImageResponse != nil {
			fmt.Printf("   图像响应: %dx%d\n", response.ImageResponse.Width, response.ImageResponse.Height)
		}
	}

	// 10. 测试手势输入
	fmt.Println("\n🧪 测试 4: 手势输入处理")
	gestureInput := &ai.MultimodalInput{
		SessionID:   "test_session_001",
		UserID:      1001,
		Timestamp:   time.Now(),
		PrimaryMode: ai.ModeGesture,
		GestureInput: &ai.GestureInput{
			GestureType: "swipe_left",
			Coordinates: []ai.Point3D{
				{X: 100, Y: 200, Z: 0},
				{X: 50, Y: 200, Z: 0},
				{X: 0, Y: 200, Z: 0},
			},
			Confidence: 0.88,
			Duration:   0.5,
			Velocity:   200.0,
			Direction:  "left",
		},
		Context: map[string]interface{}{
			"source": "touch_interface",
		},
	}

	response, err = engine.ProcessMultimodalInput(ctx, gestureInput)
	if err != nil {
		fmt.Printf("❌ 手势输入处理失败: %v\n", err)
	} else {
		fmt.Printf("✅ 手势输入处理成功:\n")
		fmt.Printf("   响应ID: %s\n", response.ResponseID)
		fmt.Printf("   主要模态: %s\n", response.PrimaryMode)
		fmt.Printf("   置信度: %.2f\n", response.Confidence)
		fmt.Printf("   处理时间: %v\n", response.ProcessingTime)
		if response.GestureResponse != nil {
			fmt.Printf("   手势响应: %s\n", response.GestureResponse.GestureType)
		}
	}

	// 11. 测试混合模态输入
	fmt.Println("\n🧪 测试 5: 混合模态输入处理")
	mixedInput := &ai.MultimodalInput{
		SessionID:   "test_session_001",
		UserID:      1001,
		Timestamp:   time.Now(),
		PrimaryMode: ai.ModeMixed,
		TextInput: &ai.TextInput{
			Content:  "显示主机列表",
			Language: "zh-CN",
		},
		SpeechInput: &ai.SpeechInput{
			AudioData:     make([]byte, 1024*3),
			Format:        "wav",
			Duration:      1.8,
			Transcription: "显示主机列表",
			Confidence:    0.95,
		},
		Context: map[string]interface{}{
			"source": "multimodal_interface",
		},
	}

	response, err = engine.ProcessMultimodalInput(ctx, mixedInput)
	if err != nil {
		fmt.Printf("❌ 混合模态输入处理失败: %v\n", err)
	} else {
		fmt.Printf("✅ 混合模态输入处理成功:\n")
		fmt.Printf("   响应ID: %s\n", response.ResponseID)
		fmt.Printf("   主要模态: %s\n", response.PrimaryMode)
		fmt.Printf("   置信度: %.2f\n", response.Confidence)
		fmt.Printf("   处理时间: %v\n", response.ProcessingTime)
	}

	// 12. 测试支持的模态
	fmt.Println("\n🧪 测试 6: 获取支持的交互模态")
	supportedModes := engine.GetSupportedModes()
	fmt.Printf("✅ 支持的交互模态: %v\n", supportedModes)

	// 13. 测试性能指标
	fmt.Println("\n🧪 测试 7: 获取性能指标")
	metrics := engine.GetPerformanceMetrics()
	fmt.Printf("✅ 性能指标:\n")
	fmt.Printf("   总交互次数: %d\n", metrics.TotalInteractions)
	fmt.Printf("   成功交互次数: %d\n", metrics.SuccessfulInteractions)
	fmt.Printf("   平均处理时间: %v\n", metrics.AverageProcessingTime)
	fmt.Printf("   错误率: %.2f%%\n", metrics.ErrorRate*100)
	fmt.Printf("   用户满意度: %.2f\n", metrics.UserSatisfactionAvg)

	fmt.Printf("\n   模态使用统计:\n")
	for mode, count := range metrics.ModeUsageStats {
		fmt.Printf("     %s: %d次\n", mode, count)
	}

	// 14. 测试连续交互
	fmt.Println("\n🧪 测试 8: 连续交互测试")
	for i := 0; i < 3; i++ {
		continuousInput := &ai.MultimodalInput{
			SessionID:   "test_session_002",
			UserID:      1002,
			Timestamp:   time.Now(),
			PrimaryMode: ai.ModeText,
			TextInput: &ai.TextInput{
				Content:  fmt.Sprintf("第%d次交互测试", i+1),
				Language: "zh-CN",
			},
		}

		response, err := engine.ProcessMultimodalInput(ctx, continuousInput)
		if err != nil {
			fmt.Printf("   ❌ 第%d次交互失败: %v\n", i+1, err)
		} else {
			fmt.Printf("   ✅ 第%d次交互成功 (处理时间: %v)\n", i+1, response.ProcessingTime)
		}

		time.Sleep(100 * time.Millisecond) // 短暂间隔
	}

	// 15. 测试错误处理
	fmt.Println("\n🧪 测试 9: 错误处理测试")
	invalidInput := &ai.MultimodalInput{
		SessionID:   "", // 无效的会话ID
		UserID:      0,  // 无效的用户ID
		Timestamp:   time.Now(),
		PrimaryMode: ai.ModeText,
	}

	_, err = engine.ProcessMultimodalInput(ctx, invalidInput)
	if err != nil {
		fmt.Printf("✅ 错误处理正常: %v\n", err)
	} else {
		fmt.Printf("❌ 错误处理异常: 应该返回错误但没有\n")
	}

	// 16. 最终性能指标
	fmt.Println("\n📊 最终性能报告:")
	finalMetrics := engine.GetPerformanceMetrics()
	fmt.Printf("总交互次数: %d\n", finalMetrics.TotalInteractions)
	fmt.Printf("成功率: %.1f%%\n", (1.0-finalMetrics.ErrorRate)*100)
	fmt.Printf("平均处理时间: %v\n", finalMetrics.AverageProcessingTime)
	fmt.Printf("用户满意度: %.2f/1.0\n", finalMetrics.UserSatisfactionAvg)

	// 17. 测试数据库记录
	fmt.Println("\n🧪 测试 10: 检查数据库记录")
	var interactionCount int64
	db.Model(&ai.MultimodalInteractionRecord{}).Count(&interactionCount)
	fmt.Printf("数据库中的交互记录: %d 条\n", interactionCount)

	var sessionCount int64
	db.Model(&ai.MultimodalSessionRecord{}).Count(&sessionCount)
	fmt.Printf("数据库中的会话记录: %d 条\n", sessionCount)

	fmt.Println("\n🎉 多模态交互引擎测试完成！")
	fmt.Println("\n📊 测试总结:")
	fmt.Println("✅ 文本输入处理")
	fmt.Println("✅ 语音输入处理")
	fmt.Println("✅ 图像输入处理")
	fmt.Println("✅ 手势输入处理")
	fmt.Println("✅ 混合模态处理")
	fmt.Println("✅ 模态融合")
	fmt.Println("✅ 上下文管理")
	fmt.Println("✅ 响应生成")
	fmt.Println("✅ 会话管理")
	fmt.Println("✅ 性能监控")
	fmt.Println("✅ 错误处理")
	fmt.Println("✅ 数据库集成")

	fmt.Println("\n🚀 多模态交互引擎已准备就绪，可以为用户提供:")
	fmt.Println("   🎤 自然语音交互")
	fmt.Println("   📷 智能图像识别")
	fmt.Println("   👋 直观手势控制")
	fmt.Println("   💬 流畅文本对话")
	fmt.Println("   🔀 智能模态融合")
	fmt.Println("   🧠 上下文感知")
	fmt.Println("   🎯 个性化响应")
}
