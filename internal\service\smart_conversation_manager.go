package service

import (
	"aiops-platform/internal/model"
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ConversationState 对话状态
type ConversationState string

const (
	StateIdle                 ConversationState = "idle"                  // 空闲状态
	StateAwaitingConfirmation ConversationState = "awaiting_confirmation" // 等待确认
	StateCollectingParams     ConversationState = "collecting_params"     // 收集参数
	StateExecuting            ConversationState = "executing"             // 执行中
	StateCompleted            ConversationState = "completed"             // 已完成
)

// ContextAnalysis 上下文分析结果
type ContextAnalysis struct {
	ShouldContinuePreviousIntent bool                   `json:"should_continue_previous_intent"`
	IsAnswerToPreviousQuestion   bool                   `json:"is_answer_to_previous_question"`
	CanInheritParameters         bool                   `json:"can_inherit_parameters"`
	PreviousIntent               string                 `json:"previous_intent"`
	InheritableParameters        map[string]interface{} `json:"inheritable_parameters"`
	ContextualHints              []string               `json:"contextual_hints"`
	RelatedHistory               []ConversationTurn     `json:"related_history"`
	ConfidenceScore              float64                `json:"confidence_score"`
}

// SmartPendingAction 智能对话中的待执行操作
type SmartPendingAction struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`       // 操作类型：add_host, execute_command等
	Intent      string                 `json:"intent"`     // 原始意图
	Parameters  map[string]interface{} `json:"parameters"` // 已收集的参数
	Required    []string               `json:"required"`   // 必需参数列表
	Missing     []string               `json:"missing"`    // 缺失参数列表
	Confidence  float64                `json:"confidence"` // 置信度
	CreatedAt   time.Time              `json:"created_at"`
	LastUpdated time.Time              `json:"last_updated"`
}

// SmartConversationSession 智能对话会话
type SmartConversationSession struct {
	SessionID     string                 `json:"session_id"`
	UserID        int64                  `json:"user_id"`
	State         ConversationState      `json:"state"`
	PendingAction *SmartPendingAction    `json:"pending_action,omitempty"`
	Context       map[string]interface{} `json:"context"` // 会话上下文
	History       []SmartConversationTurn `json:"history"` // 对话历史
	CreatedAt     time.Time              `json:"created_at"`
	LastActivity  time.Time              `json:"last_activity"`
	mutex         sync.RWMutex           `json:"-"`
}

// SmartConversationTurn 智能对话轮次（扩展原有ConversationTurn）
type SmartConversationTurn struct {
	Role      string                 `json:"role"`     // user, assistant
	Content   string                 `json:"content"`  // 消息内容
	Intent    string                 `json:"intent"`   // 识别的意图
	Action    string                 `json:"action"`   // 执行的操作
	Metadata  map[string]interface{} `json:"metadata"` // 元数据
	Timestamp time.Time              `json:"timestamp"`
}

// SmartConversationManager 智能对话管理器
type SmartConversationManager struct {
	sessions    map[string]*SmartConversationSession
	mutex       sync.RWMutex
	logger      *logrus.Logger
	hostService HostService         // 添加主机服务依赖
	keywordMap  map[string][]string // 关键词映射表
}

// NewSmartConversationManager 创建智能对话管理器
func NewSmartConversationManager(logger *logrus.Logger, hostService HostService) *SmartConversationManager {
	scm := &SmartConversationManager{
		sessions:    make(map[string]*SmartConversationSession),
		logger:      logger,
		hostService: hostService,
		keywordMap:  make(map[string][]string),
	}

	// 初始化关键词映射表
	scm.initializeKeywordMap()

	return scm
}

// initializeKeywordMap 初始化关键词映射表
func (scm *SmartConversationManager) initializeKeywordMap() {
	// 主机管理意图关键词
	scm.keywordMap["host_add"] = []string{
		"添加", "新增", "创建", "注册", "接入", "部署", "配置", "add", "create", "register", "deploy", "setup",
		"添加主机", "新增主机", "创建主机", "注册主机", "接入主机", "部署主机", "配置主机",
	}

	scm.keywordMap["host_list"] = []string{
		"列出", "显示", "查看", "获取", "展示", "罗列", "统计", "list", "show", "display", "view", "get",
		"列出主机", "显示主机", "查看主机", "获取主机", "展示主机", "罗列主机", "统计主机", "主机列表", "主机清单",
	}

	scm.keywordMap["host_delete"] = []string{
		"删除", "移除", "卸载", "下线", "清理", "delete", "remove", "uninstall", "offline", "clean",
		"删除主机", "移除主机", "卸载主机", "下线主机", "清理主机",
	}

	// 监控告警意图关键词
	scm.keywordMap["alert_view"] = []string{
		"告警", "警报", "报警", "异常", "故障", "问题", "alert", "alarm", "warning", "error", "issue",
		"查看告警", "显示告警", "告警列表", "警报信息", "异常情况", "故障报告",
	}

	scm.keywordMap["monitor_status"] = []string{
		"监控", "状态", "健康", "性能", "指标", "数据", "monitor", "status", "health", "performance", "metrics",
		"监控状态", "系统状态", "健康状态", "性能指标", "监控数据", "系统监控",
	}

	// 命令执行意图关键词
	scm.keywordMap["command_execute"] = []string{
		"执行", "运行", "启动", "操作", "处理", "调用", "execute", "run", "start", "operate", "process", "call",
		"执行命令", "运行命令", "启动命令", "操作命令", "处理命令", "调用命令",
	}

	scm.keywordMap["log_view"] = []string{
		"日志", "记录", "输出", "信息", "详情", "log", "record", "output", "info", "detail",
		"查看日志", "显示日志", "日志信息", "系统日志", "应用日志", "错误日志",
	}

	// 服务管理意图关键词
	scm.keywordMap["service_control"] = []string{
		"启动", "停止", "重启", "暂停", "恢复", "重载", "start", "stop", "restart", "pause", "resume", "reload",
		"启动服务", "停止服务", "重启服务", "暂停服务", "恢复服务", "重载服务", "服务控制",
	}

	scm.keywordMap["service_status"] = []string{
		"状态", "运行", "活跃", "停止", "异常", "status", "running", "active", "stopped", "failed",
		"服务状态", "运行状态", "活跃状态", "服务运行", "服务异常", "服务检查",
	}
}

// GetOrCreateSession 获取或创建会话
func (scm *SmartConversationManager) GetOrCreateSession(sessionID string, userID int64) *SmartConversationSession {
	scm.mutex.Lock()
	defer scm.mutex.Unlock()

	session, exists := scm.sessions[sessionID]
	if !exists {
		session = &SmartConversationSession{
			SessionID:    sessionID,
			UserID:       userID,
			State:        StateIdle,
			Context:      make(map[string]interface{}),
			History:      make([]SmartConversationTurn, 0),
			CreatedAt:    time.Now(),
			LastActivity: time.Now(),
		}
		scm.sessions[sessionID] = session
	}

	session.LastActivity = time.Now()
	return session
}

// ProcessMessage 处理消息
func (scm *SmartConversationManager) ProcessMessage(ctx context.Context, sessionID string, userID int64, message string, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
		"message":    message,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("SmartConversationManager: Processing message")

	session := scm.GetOrCreateSession(sessionID, userID)

	session.mutex.Lock()
	defer session.mutex.Unlock()

	scm.logger.WithFields(logrus.Fields{
		"session_id":    sessionID,
		"session_state": string(session.State),
	}).Info("SmartConversationManager: Session state before processing")

	// 添加用户消息到历史 - 简化以避免循环引用
	session.addToHistory("user", message, intent.Type, "", map[string]interface{}{
		"confidence": intent.Confidence,
	})

	// 根据当前状态和意图处理消息
	response, err := scm.handleMessage(ctx, session, message, intent)
	if err != nil {
		scm.logger.WithFields(logrus.Fields{
			"session_id": sessionID,
			"error":      err.Error(),
		}).Error("SmartConversationManager: Failed to handle message")
		return nil, err
	}

	scm.logger.WithFields(logrus.Fields{
		"session_id":      sessionID,
		"response_action": response.Action,
		"response_state":  string(response.State),
		"content_length":  len(response.Content),
		"has_pending":     response.PendingAction != nil,
	}).Info("SmartConversationManager: Generated response")

	// 添加助手回复到历史 - 避免循环引用
	session.addToHistory("assistant", response.Content, intent.Type, response.Action, map[string]interface{}{
		"state":                 string(session.State),
		"requires_confirmation": response.RequiresConfirmation,
	})

	scm.logger.WithFields(logrus.Fields{
		"session_id":    sessionID,
		"final_content": response.Content,
	}).Info("SmartConversationManager: Message processing completed")

	return response, nil
}

// handleMessage 处理消息的核心逻辑（增强版）
func (scm *SmartConversationManager) handleMessage(ctx context.Context, session *SmartConversationSession, message string, intent *IntentResult) (*SmartConversationResponse, error) {
	// 首先进行上下文感知分析
	contextAnalysis := scm.analyzeConversationContext(session, message, intent)

	// 检查是否需要意图延续或参数继承
	if contextAnalysis.ShouldContinuePreviousIntent {
		return scm.handleIntentContinuation(session, message, intent, contextAnalysis)
	}

	// 检查是否是对之前问题的回答
	if contextAnalysis.IsAnswerToPreviousQuestion {
		return scm.handleAnswerToQuestion(session, message, intent, contextAnalysis)
	}

	// 检查是否需要参数补全
	if contextAnalysis.CanInheritParameters {
		scm.inheritParametersFromContext(session, intent, contextAnalysis)
	}

	// 根据当前状态处理消息
	switch session.State {
	case StateIdle:
		return scm.handleIdleState(session, message, intent)
	case StateAwaitingConfirmation:
		return scm.handleConfirmationState(session, message, intent)
	case StateCollectingParams:
		return scm.handleParamCollectionState(session, message, intent)
	case StateExecuting:
		return scm.handleExecutingState(session, message, intent)
	default:
		// 重置到空闲状态
		session.State = StateIdle
		return scm.handleIdleState(session, message, intent)
	}
}

// handleIdleState 处理空闲状态
func (scm *SmartConversationManager) handleIdleState(session *SmartConversationSession, message string, intent *IntentResult) (*SmartConversationResponse, error) {
	// 使用增强的关键词匹配系统检测操作类型
	operationType := scm.detectOperationType(message, intent)

	scm.logger.WithFields(logrus.Fields{
		"session_id":     session.SessionID,
		"intent_type":    intent.Type,
		"operation_type": operationType,
		"message":        message,
	}).Info("SmartConversationManager: Detected operation type")

	// 根据检测到的操作类型进行处理
	switch operationType {
	case "host_add":
		return scm.handleHostAdditionIntent(session, intent)
	case "host_list":
		return scm.handleHostListIntent(session, intent)
	case "host_delete":
		return scm.handleHostDeletionIntent(session, intent)
	case "alert_view":
		return scm.handleAlertViewIntent(session, intent)
	case "monitor_status":
		return scm.handleMonitorStatusIntent(session, intent)
	case "command_execute":
		return scm.handleCommandExecuteIntent(session, intent)
	case "log_view":
		return scm.handleLogViewIntent(session, intent)
	case "service_control":
		return scm.handleServiceControlIntent(session, intent)
	case "service_status":
		return scm.handleServiceStatusIntent(session, intent)
	default:
		// 未识别的操作类型，返回通用回复
		return &SmartConversationResponse{
			Content:              fmt.Sprintf("我理解您想要进行 %s 相关的操作，但需要更具体的指令。请告诉我您具体想要做什么？", intent.Type),
			Action:               "general_response",
			State:                StateIdle,
			RequiresConfirmation: false,
		}, nil
	}
}

// handleHostAdditionIntent 处理主机添加意图
func (scm *SmartConversationManager) handleHostAdditionIntent(session *SmartConversationSession, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("SmartConversationManager: Handling host addition intent")

	// 获取最后的用户消息
	var lastMessage string
	if len(session.History) > 0 {
		lastMessage = session.History[len(session.History)-1].Content
	}

	// 临时禁用参数检查以避免循环引用
	var ip, username string
	var hasIP, hasUsername, hasPassword bool

	// 从消息中直接解析参数，避免访问intent.Parameters
	parts := strings.Fields(lastMessage)
	if len(parts) >= 4 {
		ip = parts[1]
		username = parts[2]
		hasIP = true
		hasUsername = true
		hasPassword = true
	}

	scm.logger.WithFields(logrus.Fields{
		"session_id":   session.SessionID,
		"has_ip":       hasIP,
		"has_username": hasUsername,
		"has_password": hasPassword,
		"ip":           ip,
		"username":     username,
	}).Info("SmartConversationManager: Parameter validation for host addition")

	if hasIP && hasUsername && hasPassword {
		scm.logger.WithFields(logrus.Fields{
			"session_id": session.SessionID,
			"confidence": intent.Confidence,
		}).Info("SmartConversationManager: All parameters present, creating pending action")

		// 参数完整，创建待执行操作 - 安全地复制参数以避免循环引用
		safeParams := map[string]interface{}{
			"ip":       ip,
			"username": username,
			"password": "***", // 不记录密码
		}

		pendingAction := &SmartPendingAction{
			ID:          fmt.Sprintf("add_host_%d", time.Now().UnixNano()),
			Type:        "add_host",
			Intent:      intent.Type,
			Parameters:  safeParams,
			Required:    []string{"ip", "username", "password"},
			Missing:     []string{},
			Confidence:  intent.Confidence,
			CreatedAt:   time.Now(),
			LastUpdated: time.Now(),
		}

		session.PendingAction = pendingAction

		// 如果置信度很高（0.92以上），直接执行，否则需要确认
		if intent.Confidence >= 0.92 {
			scm.logger.WithFields(logrus.Fields{
				"session_id": session.SessionID,
				"confidence": intent.Confidence,
			}).Info("SmartConversationManager: High confidence, executing directly")

			// 高置信度，直接执行
			session.State = StateExecuting
			return &SmartConversationResponse{
				Content:              fmt.Sprintf("正在添加主机 %s (用户名:%s)...", ip, username),
				Action:               "execute_pending_action",
				State:                StateExecuting,
				RequiresConfirmation: false,
				// 不包含PendingAction，避免循环引用
			}, nil
		} else {
			scm.logger.WithFields(logrus.Fields{
				"session_id": session.SessionID,
				"confidence": intent.Confidence,
			}).Info("SmartConversationManager: Low confidence, requesting confirmation")

			// 低置信度，需要确认
			session.State = StateAwaitingConfirmation
			return &SmartConversationResponse{
				Content:              fmt.Sprintf("是否要添加主机 %s (用户名:%s)？", ip, username),
				Action:               "request_confirmation",
				State:                StateAwaitingConfirmation,
				RequiresConfirmation: true,
				// 不包含PendingAction，避免循环引用
			}, nil
		}
	}

	// 参数不完整，进入参数收集状态
	missing := []string{}
	if !hasIP {
		missing = append(missing, "ip")
	}
	if !hasUsername {
		missing = append(missing, "username")
	}
	if !hasPassword {
		missing = append(missing, "password")
	}

	// 安全地复制参数以避免循环引用
	safeParams := map[string]interface{}{
		"ip":       ip,
		"username": username,
	}
	if hasPassword {
		safeParams["password"] = "***" // 不记录密码
	}

	pendingAction := &SmartPendingAction{
		ID:          fmt.Sprintf("add_host_%d", time.Now().UnixNano()),
		Type:        "add_host",
		Intent:      intent.Type,
		Parameters:  safeParams,
		Required:    []string{"ip", "username", "password"},
		Missing:     missing,
		Confidence:  intent.Confidence,
		CreatedAt:   time.Now(),
		LastUpdated: time.Now(),
	}

	session.PendingAction = pendingAction
	session.State = StateCollectingParams

	return &SmartConversationResponse{
		Content:              scm.generateParamCollectionPrompt(missing),
		Action:               "collect_params",
		State:                StateCollectingParams,
		RequiresConfirmation: false,
		// 不包含PendingAction，避免循环引用
	}, nil
}

// generateParamCollectionPrompt 生成参数收集提示
func (scm *SmartConversationManager) generateParamCollectionPrompt(missing []string) string {
	if len(missing) == 0 {
		return "参数已收集完整。"
	}

	// 检查当前会话的意图类型，生成针对性的提示
	return scm.generateContextualPrompt(missing, "host_management") // 默认为主机管理
}

// generateContextualPrompt 根据上下文生成参数提示
func (scm *SmartConversationManager) generateContextualPrompt(missing []string, intentType string) string {
	switch intentType {
	case "host_management":
		return scm.generateHostManagementPrompt(missing)
	case "service_management":
		return scm.generateServiceManagementPrompt(missing)
	case "log_analysis":
		return scm.generateLogAnalysisPrompt(missing)
	case "performance_diagnosis":
		return scm.generatePerformanceDiagnosisPrompt(missing)
	case "network_diagnosis":
		return scm.generateNetworkDiagnosisPrompt(missing)
	case "security_check":
		return scm.generateSecurityCheckPrompt(missing)
	case "backup_restore":
		return scm.generateBackupRestorePrompt(missing)
	case "alert_management":
		return scm.generateAlertManagementPrompt(missing)
	default:
		return scm.generateDefaultPrompt(missing)
	}
}

// handleHostListIntent 处理主机列表查询意图
func (scm *SmartConversationManager) handleHostListIntent(session *SmartConversationSession, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("SmartConversationManager: Handling host list intent")

	// 检查hostService是否可用
	if scm.hostService == nil {
		scm.logger.WithFields(logrus.Fields{
			"session_id": session.SessionID,
		}).Error("SmartConversationManager: HostService not available")

		return &SmartConversationResponse{
			Content:              "主机服务暂时不可用，请稍后再试。",
			RequiresConfirmation: false,
			Action:               "error",
			State:                StateIdle,
		}, nil
	}

	// 直接执行主机列表查询，获取真实数据
	// 创建查询请求
	req := &model.HostListQuery{
		Page:  1,
		Limit: 100, // 获取前100台主机
	}

	hostListResponse, err := scm.hostService.ListHosts(req)
	if err != nil {
		scm.logger.WithFields(logrus.Fields{
			"session_id": session.SessionID,
			"error":      err.Error(),
		}).Error("SmartConversationManager: Failed to get host list")

		return &SmartConversationResponse{
			Content:              "获取主机列表失败：" + err.Error(),
			RequiresConfirmation: false,
			Action:               "error",
			State:                StateIdle,
		}, nil
	}

	// 格式化主机列表
	var content string
	hosts := hostListResponse.Hosts
	if len(hosts) == 0 {
		content = "当前没有已添加的主机。"
	} else {
		content = fmt.Sprintf("当前共有 %d 台主机：\n\n", len(hosts))
		for i, host := range hosts {
			status := "离线"
			if host.Status == "online" {
				status = "在线"
			}
			content += fmt.Sprintf("%d. %s (%s) - %s\n", i+1, host.IPAddress, host.Name, status)
		}
	}

	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"host_count": len(hosts),
	}).Info("SmartConversationManager: Host list retrieved successfully")

	return &SmartConversationResponse{
		Content:              content,
		RequiresConfirmation: false,
		Action:               "list_hosts_completed",
		State:                StateIdle,
	}, nil
}

// handleHostDeletionIntent 处理主机删除意图
func (scm *SmartConversationManager) handleHostDeletionIntent(session *SmartConversationSession, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("SmartConversationManager: Handling host deletion intent")

	return &SmartConversationResponse{
		Content:              "主机删除功能正在开发中，敬请期待。",
		RequiresConfirmation: false,
		Action:               "feature_not_implemented",
		State:                StateIdle,
	}, nil
}

// handleAlertViewIntent 处理告警查看意图
func (scm *SmartConversationManager) handleAlertViewIntent(session *SmartConversationSession, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("SmartConversationManager: Handling alert view intent")

	return &SmartConversationResponse{
		Content:              "告警查看功能正在开发中，敬请期待。",
		RequiresConfirmation: false,
		Action:               "feature_not_implemented",
		State:                StateIdle,
	}, nil
}

// handleMonitorStatusIntent 处理监控状态意图
func (scm *SmartConversationManager) handleMonitorStatusIntent(session *SmartConversationSession, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("SmartConversationManager: Handling monitor status intent")

	return &SmartConversationResponse{
		Content:              "监控状态查看功能正在开发中，敬请期待。",
		RequiresConfirmation: false,
		Action:               "feature_not_implemented",
		State:                StateIdle,
	}, nil
}

// handleCommandExecuteIntent 处理命令执行意图
func (scm *SmartConversationManager) handleCommandExecuteIntent(session *SmartConversationSession, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("SmartConversationManager: Handling command execute intent")

	// 获取最后的用户消息
	var lastMessage string
	if len(session.History) > 0 {
		lastMessage = session.History[len(session.History)-1].Content
	}

	// 从消息中解析主机IP和命令
	hostIP := scm.extractHostIPFromMessage(lastMessage)
	command := scm.extractCommandFromMessage(lastMessage)

	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"host_ip":    hostIP,
		"command":    command,
		"message":    lastMessage,
	}).Info("SmartConversationManager: Parsed command execution parameters")

	// 验证参数
	if hostIP == "" {
		return &SmartConversationResponse{
			Content:              "请指定要连接的主机IP地址。例如：连接*************并执行ls命令",
			RequiresConfirmation: false,
			Action:               "parameter_missing",
			State:                StateIdle,
		}, nil
	}

	if command == "" {
		return &SmartConversationResponse{
			Content:              "请指定要执行的命令。例如：连接**************并执行uname -a命令",
			RequiresConfirmation: false,
			Action:               "parameter_missing",
			State:                StateIdle,
		}, nil
	}

	// 创建待处理操作
	pendingAction := &SmartPendingAction{
		ID:     fmt.Sprintf("cmd_%d", time.Now().Unix()),
		Type:   "execute_command",
		Intent: intent.Type,
		Parameters: map[string]interface{}{
			"host_ip": hostIP,
			"command": command,
		},
		Required:   []string{"host_ip", "command"},
		Missing:    []string{},
		Confidence: intent.Confidence,
	}

	// 设置会话状态
	session.PendingAction = pendingAction
	session.State = StateExecuting

	// 高置信度直接执行
	if intent.Confidence >= 0.8 {
		scm.logger.WithFields(logrus.Fields{
			"session_id": session.SessionID,
			"confidence": intent.Confidence,
		}).Info("SmartConversationManager: High confidence, executing command directly")

		return &SmartConversationResponse{
			Content:              fmt.Sprintf("正在连接主机 %s 并执行命令 '%s'...", hostIP, command),
			RequiresConfirmation: false,
			Action:               "execute_pending_action",
			State:                StateExecuting,
		}, nil
	}

	// 低置信度需要确认
	return &SmartConversationResponse{
		Content:              fmt.Sprintf("您是否要在主机 %s 上执行命令 '%s'？请确认。", hostIP, command),
		RequiresConfirmation: true,
		Action:               "await_confirmation",
		State:                StateAwaitingConfirmation,
	}, nil
}

// handleLogViewIntent 处理日志查看意图
func (scm *SmartConversationManager) handleLogViewIntent(session *SmartConversationSession, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("SmartConversationManager: Handling log view intent")

	return &SmartConversationResponse{
		Content:              "日志查看功能正在开发中，敬请期待。",
		RequiresConfirmation: false,
		Action:               "feature_not_implemented",
		State:                StateIdle,
	}, nil
}

// handleServiceControlIntent 处理服务控制意图
func (scm *SmartConversationManager) handleServiceControlIntent(session *SmartConversationSession, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("SmartConversationManager: Handling service control intent")

	return &SmartConversationResponse{
		Content:              "服务控制功能正在开发中，敬请期待。",
		RequiresConfirmation: false,
		Action:               "feature_not_implemented",
		State:                StateIdle,
	}, nil
}

// handleServiceStatusIntent 处理服务状态意图
func (scm *SmartConversationManager) handleServiceStatusIntent(session *SmartConversationSession, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"intent":     intent.Type,
		"confidence": intent.Confidence,
	}).Info("SmartConversationManager: Handling service status intent")

	return &SmartConversationResponse{
		Content:              "服务状态查看功能正在开发中，敬请期待。",
		RequiresConfirmation: false,
		Action:               "feature_not_implemented",
		State:                StateIdle,
	}, nil
}

// addToHistory 添加到对话历史
func (session *SmartConversationSession) addToHistory(role, content, intent, action string, metadata map[string]interface{}) {
	turn := SmartConversationTurn{
		Role:      role,
		Content:   content,
		Intent:    intent,
		Action:    action,
		Metadata:  metadata,
		Timestamp: time.Now(),
	}
	session.History = append(session.History, turn)

	// 保持历史记录在合理范围内（最多50条）
	if len(session.History) > 50 {
		session.History = session.History[len(session.History)-50:]
	}
}

// handleConfirmationState 处理确认状态
func (scm *SmartConversationManager) handleConfirmationState(session *SmartConversationSession, message string, intent *IntentResult) (*SmartConversationResponse, error) {
	if session.PendingAction == nil {
		// 没有待执行操作，重置状态
		session.State = StateIdle
		return &SmartConversationResponse{
			Content: "没有待确认的操作，请重新开始。",
			Action:  "reset",
			State:   StateIdle,
		}, nil
	}

	// 检查用户回复是否为确认
	if scm.isConfirmation(message) {
		// 用户确认，执行操作
		session.State = StateExecuting
		return &SmartConversationResponse{
			Content:              "正在执行操作...",
			Action:               "execute_pending_action",
			State:                StateExecuting,
			RequiresConfirmation: false,
			// 不包含PendingAction，避免循环引用
		}, nil
	} else if scm.isRejection(message) {
		// 用户拒绝，取消操作
		session.PendingAction = nil
		session.State = StateIdle
		return &SmartConversationResponse{
			Content: "操作已取消。",
			Action:  "cancel",
			State:   StateIdle,
		}, nil
	}

	// 用户回复不明确，再次询问
	return &SmartConversationResponse{
		Content:              "请回答\"是\"或\"否\"来确认操作。",
		Action:               "request_confirmation",
		State:                StateAwaitingConfirmation,
		RequiresConfirmation: true,
		// 不包含PendingAction，避免循环引用
	}, nil
}

// handleParamCollectionState 处理参数收集状态
func (scm *SmartConversationManager) handleParamCollectionState(session *SmartConversationSession, message string, intent *IntentResult) (*SmartConversationResponse, error) {
	if session.PendingAction == nil {
		// 没有待执行操作，重置状态
		session.State = StateIdle
		return &SmartConversationResponse{
			Content: "参数收集已重置，请重新开始。",
			Action:  "reset",
			State:   StateIdle,
		}, nil
	}

	// 尝试从用户输入中提取参数
	extracted := scm.extractParametersFromMessage(message, session.PendingAction.Missing)

	// 更新已收集的参数
	for key, value := range extracted {
		session.PendingAction.Parameters[key] = value
	}

	// 更新缺失参数列表
	session.PendingAction.Missing = scm.findMissingParameters(session.PendingAction.Parameters, session.PendingAction.Required)
	session.PendingAction.LastUpdated = time.Now()

	if len(session.PendingAction.Missing) == 0 {
		// 参数收集完成，进入确认状态
		session.State = StateAwaitingConfirmation

		ip := session.PendingAction.Parameters["ip"].(string)
		username := session.PendingAction.Parameters["username"].(string)

		return &SmartConversationResponse{
			Content:              fmt.Sprintf("是否要添加主机 %s (用户名:%s)？", ip, username),
			Action:               "request_confirmation",
			State:                StateAwaitingConfirmation,
			RequiresConfirmation: true,
			// 不包含PendingAction，避免循环引用
		}, nil
	}

	// 还有参数缺失，继续收集
	return &SmartConversationResponse{
		Content:              scm.generateParamCollectionPrompt(session.PendingAction.Missing),
		Action:               "collect_params",
		State:                StateCollectingParams,
		RequiresConfirmation: false,
		// 不包含PendingAction，避免循环引用
	}, nil
}

// isConfirmation 检查是否为确认回复
func (scm *SmartConversationManager) isConfirmation(message string) bool {
	confirmations := []string{"是", "yes", "y", "确认", "好的", "可以", "同意", "执行", "ok"}
	lowerMsg := strings.ToLower(strings.TrimSpace(message))
	for _, conf := range confirmations {
		if lowerMsg == strings.ToLower(conf) {
			return true
		}
	}
	return false
}

// isRejection 检查是否为拒绝回复
func (scm *SmartConversationManager) isRejection(message string) bool {
	rejections := []string{"否", "no", "n", "取消", "不要", "不行", "拒绝", "不"}
	lowerMsg := strings.ToLower(strings.TrimSpace(message))
	for _, rej := range rejections {
		if lowerMsg == strings.ToLower(rej) {
			return true
		}
	}
	return false
}

// extractParametersFromMessage 从消息中提取参数（增强版）
func (scm *SmartConversationManager) extractParametersFromMessage(message string, missing []string) map[string]interface{} {
	extracted := make(map[string]interface{})

	// 多种参数提取策略

	// 策略1: 结构化格式提取（IP 用户名 密码）
	if scm.extractStructuredParams(message, missing, extracted) {
		return extracted
	}

	// 策略2: 关键词模式提取
	scm.extractKeywordParams(message, missing, extracted)

	// 策略3: 正则表达式提取
	scm.extractRegexParams(message, missing, extracted)

	// 策略4: 智能推断提取
	scm.extractInferredParams(message, missing, extracted)

	return extracted
}

// extractStructuredParams 提取结构化参数
func (scm *SmartConversationManager) extractStructuredParams(message string, missing []string, extracted map[string]interface{}) bool {
	parts := strings.Fields(message)

	// 检查是否为标准的"IP 用户名 密码"格式
	if len(parts) >= 3 && len(missing) >= 3 {
		for i, part := range parts {
			if i >= len(missing) {
				break
			}

			switch missing[i] {
			case "ip":
				if scm.isValidIP(part) {
					extracted["ip"] = part
				} else {
					return false // 如果第一个不是IP，说明不是标准格式
				}
			case "username":
				if scm.isValidUsername(part) {
					extracted["username"] = part
				}
			case "password":
				extracted["password"] = part
			case "port":
				if scm.isValidPort(part) {
					extracted["port"] = part
				}
			case "service":
				if scm.isValidServiceName(part) {
					extracted["service"] = part
				}
			}
		}
		return len(extracted) > 0
	}
	return false
}

// extractKeywordParams 基于关键词提取参数
func (scm *SmartConversationManager) extractKeywordParams(message string, missing []string, extracted map[string]interface{}) {
	lowerMsg := strings.ToLower(message)

	for _, param := range missing {
		switch param {
		case "ip":
			// 查找IP地址模式
			if ip := scm.findIPInText(message); ip != "" {
				extracted["ip"] = ip
			}
		case "username":
			// 查找用户名关键词
			if username := scm.findUsernameInText(lowerMsg); username != "" {
				extracted["username"] = username
			}
		case "service":
			// 查找服务名
			if service := scm.findServiceInText(lowerMsg); service != "" {
				extracted["service"] = service
			}
		case "port":
			// 查找端口号
			if port := scm.findPortInText(message); port != "" {
				extracted["port"] = port
			}
		case "host":
			// 查找主机标识
			if host := scm.findHostInText(message); host != "" {
				extracted["host"] = host
			}
		}
	}
}

// extractRegexParams 使用正则表达式提取参数
func (scm *SmartConversationManager) extractRegexParams(message string, missing []string, extracted map[string]interface{}) {
	for _, param := range missing {
		if _, exists := extracted[param]; exists {
			continue // 已经提取到了
		}

		switch param {
		case "ip":
			if ip := scm.extractIPRegex(message); ip != "" {
				extracted["ip"] = ip
			}
		case "port":
			if port := scm.extractPortRegex(message); port != "" {
				extracted["port"] = port
			}
		case "path":
			if path := scm.extractPathRegex(message); path != "" {
				extracted["path"] = path
			}
		}
	}
}

// extractInferredParams 智能推断参数
func (scm *SmartConversationManager) extractInferredParams(message string, missing []string, extracted map[string]interface{}) {
	// 基于上下文和常见模式进行智能推断
	for _, param := range missing {
		if _, exists := extracted[param]; exists {
			continue
		}

		switch param {
		case "username":
			// 如果没有明确的用户名，尝试推断常见用户名
			if scm.containsRootKeywords(message) {
				extracted["username"] = "root"
			} else if scm.containsAdminKeywords(message) {
				extracted["username"] = "admin"
			}
		case "port":
			// 根据服务类型推断默认端口
			if service, ok := extracted["service"].(string); ok {
				if port := scm.getDefaultPortForService(service); port != "" {
					extracted["port"] = port
				}
			}
		}
	}
}

// findMissingParameters 查找缺失的参数
func (scm *SmartConversationManager) findMissingParameters(params map[string]interface{}, required []string) []string {
	missing := []string{}
	for _, req := range required {
		if _, exists := params[req]; !exists {
			missing = append(missing, req)
		}
	}
	return missing
}

// isValidIP 检查是否为有效IP地址
func (scm *SmartConversationManager) isValidIP(ip string) bool {
	// 简单的IP格式检查
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}
	for _, part := range parts {
		if num, err := strconv.Atoi(part); err != nil || num < 0 || num > 255 {
			return false
		}
	}
	return true
}

// isValidUsername 检查是否为有效用户名
func (scm *SmartConversationManager) isValidUsername(username string) bool {
	if len(username) == 0 || len(username) > 32 {
		return false
	}
	// 用户名只能包含字母、数字、下划线和连字符
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9_-]+$`, username)
	return matched
}

// isValidPort 检查是否为有效端口号
func (scm *SmartConversationManager) isValidPort(port string) bool {
	if num, err := strconv.Atoi(port); err != nil || num < 1 || num > 65535 {
		return false
	}
	return true
}

// isValidServiceName 检查是否为有效服务名
func (scm *SmartConversationManager) isValidServiceName(service string) bool {
	if len(service) == 0 || len(service) > 64 {
		return false
	}
	// 服务名通常包含字母、数字、连字符和点
	matched, _ := regexp.MatchString(`^[a-zA-Z0-9.-]+$`, service)
	return matched
}

// findIPInText 在文本中查找IP地址
func (scm *SmartConversationManager) findIPInText(text string) string {
	ipPattern := regexp.MustCompile(`\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b`)
	if matches := ipPattern.FindStringSubmatch(text); len(matches) > 1 {
		if scm.isValidIP(matches[1]) {
			return matches[1]
		}
	}
	return ""
}

// findUsernameInText 在文本中查找用户名
func (scm *SmartConversationManager) findUsernameInText(text string) string {
	// 查找常见的用户名关键词
	userPatterns := []string{
		`用户名[：:\s]*([a-zA-Z0-9_-]+)`,
		`用户[：:\s]*([a-zA-Z0-9_-]+)`,
		`user[：:\s]*([a-zA-Z0-9_-]+)`,
		`username[：:\s]*([a-zA-Z0-9_-]+)`,
	}

	for _, pattern := range userPatterns {
		if matches := regexp.MustCompile(`(?i)` + pattern).FindStringSubmatch(text); len(matches) > 1 {
			if scm.isValidUsername(matches[1]) {
				return matches[1]
			}
		}
	}

	// 查找常见用户名
	commonUsers := []string{"root", "admin", "administrator", "ubuntu", "centos", "user"}
	for _, user := range commonUsers {
		if strings.Contains(text, user) {
			return user
		}
	}

	return ""
}

// findServiceInText 在文本中查找服务名
func (scm *SmartConversationManager) findServiceInText(text string) string {
	// 常见服务名列表
	services := []string{
		"nginx", "apache", "httpd", "mysql", "redis", "docker", "ssh", "sshd",
		"postgresql", "postgres", "mongodb", "elasticsearch", "tomcat", "jenkins",
		"php-fpm", "node", "python", "java", "systemd", "cron", "rsyslog",
	}

	for _, service := range services {
		if strings.Contains(text, service) {
			return service
		}
	}

	// 查找服务关键词模式
	servicePatterns := []string{
		`服务[：:\s]*([a-zA-Z0-9.-]+)`,
		`service[：:\s]*([a-zA-Z0-9.-]+)`,
	}

	for _, pattern := range servicePatterns {
		if matches := regexp.MustCompile(`(?i)` + pattern).FindStringSubmatch(text); len(matches) > 1 {
			if scm.isValidServiceName(matches[1]) {
				return matches[1]
			}
		}
	}

	return ""
}

// findPortInText 在文本中查找端口号
func (scm *SmartConversationManager) findPortInText(text string) string {
	// 查找端口关键词模式
	portPatterns := []string{
		`端口[：:\s]*(\d+)`,
		`port[：:\s]*(\d+)`,
		`:(\d+)`,
	}

	for _, pattern := range portPatterns {
		if matches := regexp.MustCompile(`(?i)` + pattern).FindStringSubmatch(text); len(matches) > 1 {
			if scm.isValidPort(matches[1]) {
				return matches[1]
			}
		}
	}

	return ""
}

// findHostInText 在文本中查找主机标识
func (scm *SmartConversationManager) findHostInText(text string) string {
	// 首先尝试查找IP地址
	if ip := scm.findIPInText(text); ip != "" {
		return ip
	}

	// 查找主机名模式
	hostPatterns := []string{
		`主机[：:\s]*([a-zA-Z0-9.-]+)`,
		`host[：:\s]*([a-zA-Z0-9.-]+)`,
		`服务器[：:\s]*([a-zA-Z0-9.-]+)`,
		`server[：:\s]*([a-zA-Z0-9.-]+)`,
	}

	for _, pattern := range hostPatterns {
		if matches := regexp.MustCompile(`(?i)` + pattern).FindStringSubmatch(text); len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}

// extractIPRegex 使用正则表达式提取IP地址
func (scm *SmartConversationManager) extractIPRegex(text string) string {
	return scm.findIPInText(text)
}

// extractPortRegex 使用正则表达式提取端口号
func (scm *SmartConversationManager) extractPortRegex(text string) string {
	return scm.findPortInText(text)
}

// extractPathRegex 使用正则表达式提取路径
func (scm *SmartConversationManager) extractPathRegex(text string) string {
	// 查找文件路径模式
	pathPatterns := []string{
		`(/[^\s]+)`,           // Unix路径
		`([A-Za-z]:\\[^\s]+)`, // Windows路径
		`路径[：:\s]*([^\s]+)`,   // 中文路径关键词
		`path[：:\s]*([^\s]+)`, // 英文路径关键词
	}

	for _, pattern := range pathPatterns {
		if matches := regexp.MustCompile(`(?i)` + pattern).FindStringSubmatch(text); len(matches) > 1 {
			return matches[1]
		}
	}

	return ""
}

// containsRootKeywords 检查是否包含root相关关键词
func (scm *SmartConversationManager) containsRootKeywords(text string) bool {
	rootKeywords := []string{"root", "超级管理员", "管理员", "admin"}
	lowerText := strings.ToLower(text)

	for _, keyword := range rootKeywords {
		if strings.Contains(lowerText, strings.ToLower(keyword)) {
			return true
		}
	}
	return false
}

// containsAdminKeywords 检查是否包含admin相关关键词
func (scm *SmartConversationManager) containsAdminKeywords(text string) bool {
	adminKeywords := []string{"admin", "administrator", "管理员"}
	lowerText := strings.ToLower(text)

	for _, keyword := range adminKeywords {
		if strings.Contains(lowerText, strings.ToLower(keyword)) {
			return true
		}
	}
	return false
}

// getDefaultPortForService 根据服务获取默认端口
func (scm *SmartConversationManager) getDefaultPortForService(service string) string {
	servicePortMap := map[string]string{
		"ssh":           "22",
		"sshd":          "22",
		"http":          "80",
		"nginx":         "80",
		"apache":        "80",
		"httpd":         "80",
		"https":         "443",
		"mysql":         "3306",
		"postgresql":    "5432",
		"postgres":      "5432",
		"redis":         "6379",
		"mongodb":       "27017",
		"elasticsearch": "9200",
		"tomcat":        "8080",
		"jenkins":       "8080",
		"docker":        "2376",
		"ftp":           "21",
		"smtp":          "25",
		"dns":           "53",
		"dhcp":          "67",
		"ntp":           "123",
		"snmp":          "161",
	}

	if port, exists := servicePortMap[strings.ToLower(service)]; exists {
		return port
	}
	return ""
}

// generateHostManagementPrompt 生成主机管理参数提示
func (scm *SmartConversationManager) generateHostManagementPrompt(missing []string) string {
	if len(missing) == 3 && missing[0] == "ip" && missing[1] == "username" && missing[2] == "password" {
		return "请提供以下信息来添加主机：\n• IP地址（如：*************）\n• 用户名（如：root、admin）\n• 密码\n\n格式：IP地址 用户名 密码"
	}

	var prompt strings.Builder
	prompt.WriteString("请提供以下主机信息：\n")

	for _, param := range missing {
		switch param {
		case "ip":
			prompt.WriteString("• IP地址（如：*************、********）\n")
		case "username":
			prompt.WriteString("• 用户名（如：root、admin、ubuntu）\n")
		case "password":
			prompt.WriteString("• 密码（用于SSH连接认证）\n")
		case "port":
			prompt.WriteString("• 端口号（默认：22，范围：1-65535）\n")
		case "host":
			prompt.WriteString("• 主机标识（IP地址或主机名）\n")
		}
	}

	return prompt.String()
}

// generateServiceManagementPrompt 生成服务管理参数提示
func (scm *SmartConversationManager) generateServiceManagementPrompt(missing []string) string {
	var prompt strings.Builder
	prompt.WriteString("请提供以下服务管理信息：\n")

	for _, param := range missing {
		switch param {
		case "service":
			prompt.WriteString("• 服务名称（如：nginx、mysql、redis、docker）\n")
		case "host":
			prompt.WriteString("• 目标主机（IP地址或主机名）\n")
		case "action":
			prompt.WriteString("• 操作类型（启动、停止、重启、状态查询）\n")
		}
	}

	prompt.WriteString("\n示例：重启nginx服务、查看mysql状态")
	return prompt.String()
}

// generateLogAnalysisPrompt 生成日志分析参数提示
func (scm *SmartConversationManager) generateLogAnalysisPrompt(missing []string) string {
	var prompt strings.Builder
	prompt.WriteString("请提供以下日志分析信息：\n")

	for _, param := range missing {
		switch param {
		case "log_type":
			prompt.WriteString("• 日志类型（如：error、access、system、application）\n")
		case "host":
			prompt.WriteString("• 目标主机（IP地址或主机名）\n")
		case "keyword":
			prompt.WriteString("• 搜索关键词（用于日志内容搜索）\n")
		case "path":
			prompt.WriteString("• 日志文件路径（如：/var/log/nginx/error.log）\n")
		}
	}

	prompt.WriteString("\n示例：查看nginx错误日志、搜索关键词'timeout'")
	return prompt.String()
}

// generatePerformanceDiagnosisPrompt 生成性能诊断参数提示
func (scm *SmartConversationManager) generatePerformanceDiagnosisPrompt(missing []string) string {
	var prompt strings.Builder
	prompt.WriteString("请提供以下性能诊断信息：\n")

	for _, param := range missing {
		switch param {
		case "metric":
			prompt.WriteString("• 性能指标（如：cpu、memory、disk、network）\n")
		case "host":
			prompt.WriteString("• 目标主机（IP地址或主机名）\n")
		case "action":
			prompt.WriteString("• 诊断类型（监控、分析、报告）\n")
		}
	}

	prompt.WriteString("\n示例：分析CPU性能、监控内存使用率")
	return prompt.String()
}

// generateNetworkDiagnosisPrompt 生成网络诊断参数提示
func (scm *SmartConversationManager) generateNetworkDiagnosisPrompt(missing []string) string {
	var prompt strings.Builder
	prompt.WriteString("请提供以下网络诊断信息：\n")

	for _, param := range missing {
		switch param {
		case "target":
			prompt.WriteString("• 目标地址（IP地址或域名）\n")
		case "port":
			prompt.WriteString("• 端口号（如：80、443、22）\n")
		case "action":
			prompt.WriteString("• 诊断类型（连通性测试、端口检查、延迟测试）\n")
		}
	}

	prompt.WriteString("\n示例：测试到***********的连通性、检查80端口")
	return prompt.String()
}

// generateSecurityCheckPrompt 生成安全检查参数提示
func (scm *SmartConversationManager) generateSecurityCheckPrompt(missing []string) string {
	var prompt strings.Builder
	prompt.WriteString("请提供以下安全检查信息：\n")

	for _, param := range missing {
		switch param {
		case "host":
			prompt.WriteString("• 目标主机（IP地址或主机名）\n")
		case "action":
			prompt.WriteString("• 检查类型（权限审计、漏洞扫描、策略检查）\n")
		case "scope":
			prompt.WriteString("• 检查范围（系统、网络、应用）\n")
		}
	}

	prompt.WriteString("\n示例：执行权限审计、扫描系统漏洞")
	return prompt.String()
}

// generateBackupRestorePrompt 生成备份恢复参数提示
func (scm *SmartConversationManager) generateBackupRestorePrompt(missing []string) string {
	var prompt strings.Builder
	prompt.WriteString("请提供以下备份恢复信息：\n")

	for _, param := range missing {
		switch param {
		case "target":
			prompt.WriteString("• 备份目标（文件路径或数据库名）\n")
		case "source":
			prompt.WriteString("• 恢复源（备份文件路径）\n")
		case "action":
			prompt.WriteString("• 操作类型（备份、恢复、快照）\n")
		case "host":
			prompt.WriteString("• 目标主机（IP地址或主机名）\n")
		}
	}

	prompt.WriteString("\n示例：备份数据库、恢复系统快照")
	return prompt.String()
}

// generateAlertManagementPrompt 生成告警管理参数提示
func (scm *SmartConversationManager) generateAlertManagementPrompt(missing []string) string {
	var prompt strings.Builder
	prompt.WriteString("请提供以下告警管理信息：\n")

	for _, param := range missing {
		switch param {
		case "action":
			prompt.WriteString("• 操作类型（查看告警、配置规则、设置通知）\n")
		case "rule_type":
			prompt.WriteString("• 规则类型（CPU、内存、磁盘、网络）\n")
		case "threshold":
			prompt.WriteString("• 告警阈值（如：80%、90%）\n")
		}
	}

	prompt.WriteString("\n示例：查看当前告警、配置CPU告警规则")
	return prompt.String()
}

// generateDefaultPrompt 生成默认参数提示
func (scm *SmartConversationManager) generateDefaultPrompt(missing []string) string {
	return fmt.Sprintf("请提供以下参数：%s", strings.Join(missing, "、"))
}

// GetSessionPendingAction 获取会话的待处理操作
func (scm *SmartConversationManager) GetSessionPendingAction(sessionID string) *SmartPendingAction {
	scm.mutex.RLock()
	defer scm.mutex.RUnlock()

	session, exists := scm.sessions[sessionID]
	if !exists {
		return nil
	}

	session.mutex.RLock()
	defer session.mutex.RUnlock()

	return session.PendingAction
}

// matchKeywords 匹配关键词
func (scm *SmartConversationManager) matchKeywords(message string, keywordType string) bool {
	lowerMessage := strings.ToLower(message)
	keywords, exists := scm.keywordMap[keywordType]
	if !exists {
		return false
	}

	for _, keyword := range keywords {
		if strings.Contains(lowerMessage, strings.ToLower(keyword)) {
			return true
		}
	}
	return false
}

// detectOperationType 检测操作类型（支持新的4大类意图架构）
func (scm *SmartConversationManager) detectOperationType(message string, intent *IntentResult) string {
	// 根据意图类型和消息内容检测具体操作
	switch intent.Type {
	// 新的4大类意图架构
	case "database_operations":
		// 检查是否是主机相关操作
		if scm.matchKeywords(message, "host_add") {
			return "host_add"
		} else if scm.matchKeywords(message, "host_list") {
			return "host_list"
		} else if scm.matchKeywords(message, "host_delete") {
			return "host_delete"
		}
		// 检查是否是告警相关操作
		if scm.matchKeywords(message, "alert_view") {
			return "alert_view"
		}
		// 如果有SQL参数，根据operation类型判断
		if params := intent.Parameters; params != nil {
			if operation, ok := params["operation"].(string); ok {
				if table, ok := params["table"].(string); ok && table == "hosts" {
					switch operation {
					case "insert":
						return "host_add"
					case "select":
						return "host_list"
					case "delete":
						return "host_delete"
					case "update":
						return "host_update"
					}
				}
			}
		}
	case "ssh_operations":
		if scm.matchKeywords(message, "command_execute") {
			return "command_execute"
		} else if scm.matchKeywords(message, "log_view") {
			return "log_view"
		} else if scm.matchKeywords(message, "service_control") {
			return "service_control"
		} else if scm.matchKeywords(message, "service_status") {
			return "service_status"
		}
	case "monitoring_operations":
		if scm.matchKeywords(message, "monitor_status") {
			return "monitor_status"
		}
	case "general_chat":
		return "general_chat"

	// 兼容旧的意图类型（向后兼容）
	case "host_management":
		if scm.matchKeywords(message, "host_add") {
			return "host_add"
		} else if scm.matchKeywords(message, "host_list") {
			return "host_list"
		} else if scm.matchKeywords(message, "host_delete") {
			return "host_delete"
		}
	case "alert_management":
		if scm.matchKeywords(message, "alert_view") {
			return "alert_view"
		}
	case "performance_diagnosis":
		if scm.matchKeywords(message, "monitor_status") {
			return "monitor_status"
		}
	case "command_execution":
		if scm.matchKeywords(message, "command_execute") {
			return "command_execute"
		} else if scm.matchKeywords(message, "log_view") {
			return "log_view"
		}
	case "service_management":
		if scm.matchKeywords(message, "service_control") {
			return "service_control"
		} else if scm.matchKeywords(message, "service_status") {
			return "service_status"
		}
	}
	return "unknown"
}

// handleExecutingState 处理执行状态
func (scm *SmartConversationManager) handleExecutingState(session *SmartConversationSession, message string, intent *IntentResult) (*SmartConversationResponse, error) {
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"intent":     intent.Type,
		"message":    message,
	}).Info("SmartConversationManager: Handling executing state")

	// 执行状态下，清理之前的操作状态，重置为空闲状态
	session.State = StateIdle
	session.PendingAction = nil

	// 将新的消息作为新的请求处理
	return scm.handleIdleState(session, message, intent)
}

// extractHostIPFromMessage 从消息中提取主机IP
func (scm *SmartConversationManager) extractHostIPFromMessage(message string) string {
	// IP地址正则表达式
	ipPattern := `\b(?:\d{1,3}\.){3}\d{1,3}\b`
	re := regexp.MustCompile(ipPattern)
	matches := re.FindAllString(message, -1)
	if len(matches) > 0 {
		return matches[0]
	}
	return ""
}

// extractCommandFromMessage 从消息中提取命令
func (scm *SmartConversationManager) extractCommandFromMessage(message string) string {
	// 常见命令模式
	commandPatterns := []string{
		`uname\s*-[a-zA-Z]*`,
		`ls\s*-[a-zA-Z]*\s*\S*`,
		`ps\s*-[a-zA-Z]*`,
		`top\s*-[a-zA-Z]*`,
		`df\s*-[a-zA-Z]*`,
		`free\s*-[a-zA-Z]*`,
		`cat\s+\S+`,
		`grep\s+\S+`,
		`find\s+\S+`,
		`systemctl\s+\w+\s+\S+`,
		`service\s+\S+\s+\w+`,
		`netstat\s*-[a-zA-Z]*`,
		`ss\s*-[a-zA-Z]*`,
		`tail\s*-[a-zA-Z]*\s*\S*`,
		`head\s*-[a-zA-Z]*\s*\S*`,
		`chmod\s+\d+\s+\S+`,
		`chown\s+\S+\s+\S+`,
		`mkdir\s+\S+`,
		`rmdir\s+\S+`,
		`cp\s+\S+\s+\S+`,
		`mv\s+\S+\s+\S+`,
		`rm\s+-[a-zA-Z]*\s*\S+`,
		`wget\s+\S+`,
		`curl\s+\S+`,
		`ping\s+\S+`,
		`traceroute\s+\S+`,
		`nslookup\s+\S+`,
		`dig\s+\S+`,
	}

	for _, pattern := range commandPatterns {
		re := regexp.MustCompile("(?i)" + pattern)
		matches := re.FindAllString(message, -1)
		if len(matches) > 0 {
			return matches[0]
		}
	}

	// 尝试匹配"执行xxx命令"模式
	executePattern := `执行\s*([^命令\s]+)\s*命令?`
	re := regexp.MustCompile(executePattern)
	matches := re.FindStringSubmatch(message)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	// 尝试匹配"运行xxx"模式
	runPattern := `运行\s*([^命令\s]+)`
	re = regexp.MustCompile(runPattern)
	matches = re.FindStringSubmatch(message)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	// 如果没有匹配到具体命令，尝试提取引号中的内容
	quotedPattern := `["']([^"']+)["']`
	re = regexp.MustCompile(quotedPattern)
	matches = re.FindStringSubmatch(message)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

// SmartConversationResponse 智能对话响应
type SmartConversationResponse struct {
	Content              string                 `json:"content"`
	Action               string                 `json:"action"`
	State                ConversationState      `json:"state"`
	RequiresConfirmation bool                   `json:"requires_confirmation"`
	PendingAction        *SmartPendingAction    `json:"pending_action,omitempty"`
	Metadata             map[string]interface{} `json:"metadata,omitempty"`
}

// analyzeConversationContext 分析对话上下文
func (scm *SmartConversationManager) analyzeConversationContext(session *SmartConversationSession, message string, intent *IntentResult) *ContextAnalysis {
	analysis := &ContextAnalysis{
		ShouldContinuePreviousIntent: false,
		IsAnswerToPreviousQuestion:   false,
		CanInheritParameters:         false,
		InheritableParameters:        make(map[string]interface{}),
		ContextualHints:              []string{},
		RelatedHistory:               []ConversationTurn{},
		ConfidenceScore:              0.0,
	}

	// 分析历史对话
	if len(session.History) > 0 {
		lastTurn := session.History[len(session.History)-1]
		analysis.PreviousIntent = lastTurn.Intent

		// 检查是否是简单的确认/否定回答
		if scm.isSimpleAnswer(message) {
			analysis.IsAnswerToPreviousQuestion = true
			analysis.ConfidenceScore = 0.9
		}

		// 检查是否应该继续之前的意图
		if scm.shouldContinueIntent(session, intent, lastTurn) {
			analysis.ShouldContinuePreviousIntent = true
			analysis.ConfidenceScore = 0.8
		}

		// 检查是否可以继承参数
		if scm.canInheritParams(session, intent) {
			analysis.CanInheritParameters = true
			analysis.InheritableParameters = scm.extractInheritableParams(session, intent)
			analysis.ConfidenceScore = 0.7
		}

		// 提取相关历史
		analysis.RelatedHistory = scm.extractRelatedHistory(session, intent)
	}

	// 生成上下文提示
	analysis.ContextualHints = scm.generateContextualHints(session, intent, analysis)

	return analysis
}

// isSimpleAnswer 检查是否是简单的确认/否定回答
func (scm *SmartConversationManager) isSimpleAnswer(message string) bool {
	lowerMsg := strings.ToLower(strings.TrimSpace(message))
	simpleAnswers := []string{
		"是", "否", "好", "不", "对", "错", "可以", "不可以",
		"yes", "no", "ok", "okay", "sure", "nope", "yep",
		"确认", "取消", "同意", "拒绝", "继续", "停止",
	}

	for _, answer := range simpleAnswers {
		if lowerMsg == answer || strings.Contains(lowerMsg, answer) {
			return true
		}
	}

	return len(lowerMsg) <= 5 && (strings.Contains(lowerMsg, "是") || strings.Contains(lowerMsg, "否"))
}

// shouldContinueIntent 检查是否应该继续之前的意图
func (scm *SmartConversationManager) shouldContinueIntent(session *SmartConversationSession, currentIntent *IntentResult, lastTurn SmartConversationTurn) bool {
	// 如果当前意图置信度很低，且上一轮是参数收集状态
	if currentIntent.Confidence < 0.6 && session.State == StateCollectingParams {
		return true
	}

	// 如果当前意图是general_chat，但上一轮有明确的运维意图
	if currentIntent.Type == "general_chat" && scm.isOperationalIntent(lastTurn.Intent) {
		return true
	}

	// 如果是相同类型的意图，可能是在补充参数
	if currentIntent.Type == lastTurn.Intent && session.State == StateCollectingParams {
		return true
	}

	return false
}

// isOperationalIntent 检查是否是运维相关意图
func (scm *SmartConversationManager) isOperationalIntent(intent string) bool {
	operationalIntents := []string{
		"host_management", "service_management", "log_analysis",
		"performance_diagnosis", "network_diagnosis", "security_check",
		"backup_restore", "alert_management",
	}

	for _, opIntent := range operationalIntents {
		if intent == opIntent {
			return true
		}
	}
	return false
}

// canInheritParams 检查是否可以继承参数
func (scm *SmartConversationManager) canInheritParams(session *SmartConversationSession, currentIntent *IntentResult) bool {
	// 如果有待处理的操作，且当前意图类型相同
	if session.PendingAction != nil && session.PendingAction.Intent == currentIntent.Type {
		return true
	}

	// 如果历史中有相同类型的意图，且有可用参数
	for i := len(session.History) - 1; i >= 0 && i >= len(session.History)-5; i-- {
		turn := session.History[i]
		if turn.Intent == currentIntent.Type && len(turn.Metadata) > 0 {
			return true
		}
	}

	return false
}

// extractInheritableParams 提取可继承的参数
func (scm *SmartConversationManager) extractInheritableParams(session *SmartConversationSession, currentIntent *IntentResult) map[string]interface{} {
	inheritableParams := make(map[string]interface{})

	// 从待处理操作中继承参数
	if session.PendingAction != nil && session.PendingAction.Intent == currentIntent.Type {
		for key, value := range session.PendingAction.Parameters {
			inheritableParams[key] = value
		}
	}

	// 从历史对话中继承参数 - 安全地复制以避免循环引用
	for i := len(session.History) - 1; i >= 0 && i >= len(session.History)-5; i-- {
		turn := session.History[i]
		if turn.Intent == currentIntent.Type {
			for key, value := range turn.Metadata {
				if _, exists := inheritableParams[key]; !exists {
					// 只复制安全的基本类型，避免循环引用
					switch val := value.(type) {
					case string, int, int64, float64, bool:
						inheritableParams[key] = val
					case nil:
						inheritableParams[key] = nil
					default:
						// 跳过复杂类型以避免循环引用
						continue
					}
				}
			}
		}
	}

	return inheritableParams
}

// extractRelatedHistory 提取相关历史
func (scm *SmartConversationManager) extractRelatedHistory(session *SmartConversationSession, currentIntent *IntentResult) []ConversationTurn {
	var relatedHistory []ConversationTurn

	// 提取最近5轮相关对话
	for i := len(session.History) - 1; i >= 0 && len(relatedHistory) < 5; i-- {
		turn := session.History[i]
		if turn.Intent == currentIntent.Type || scm.isRelatedIntent(turn.Intent, currentIntent.Type) {
			// 转换SmartConversationTurn为ConversationTurn
			convertedTurn := ConversationTurn{
				TurnID:      fmt.Sprintf("turn_%d", i),
				UserMessage: turn.Content,
				AIResponse:  "",
				Intent:      turn.Intent,
				Success:     true,
				Timestamp:   turn.Timestamp,
				Metadata:    turn.Metadata,
			}
			relatedHistory = append([]ConversationTurn{convertedTurn}, relatedHistory...)
		}
	}

	return relatedHistory
}

// isRelatedIntent 检查意图是否相关
func (scm *SmartConversationManager) isRelatedIntent(intent1, intent2 string) bool {
	// 定义相关意图组
	relatedGroups := [][]string{
		{"host_management", "service_management", "performance_diagnosis"},
		{"log_analysis", "security_check"},
		{"backup_restore", "alert_management"},
		{"network_diagnosis", "performance_diagnosis"},
	}

	for _, group := range relatedGroups {
		hasIntent1 := false
		hasIntent2 := false
		for _, intent := range group {
			if intent == intent1 {
				hasIntent1 = true
			}
			if intent == intent2 {
				hasIntent2 = true
			}
		}
		if hasIntent1 && hasIntent2 {
			return true
		}
	}

	return false
}

// generateContextualHints 生成上下文提示
func (scm *SmartConversationManager) generateContextualHints(session *SmartConversationSession, intent *IntentResult, analysis *ContextAnalysis) []string {
	var hints []string

	if analysis.ShouldContinuePreviousIntent {
		hints = append(hints, "继续之前的操作")
	}

	if analysis.IsAnswerToPreviousQuestion {
		hints = append(hints, "回答之前的问题")
	}

	if analysis.CanInheritParameters {
		hints = append(hints, "可以使用之前的参数")
	}

	if len(analysis.RelatedHistory) > 0 {
		hints = append(hints, fmt.Sprintf("找到%d条相关历史", len(analysis.RelatedHistory)))
	}

	return hints
}

// handleIntentContinuation 处理意图延续
func (scm *SmartConversationManager) handleIntentContinuation(session *SmartConversationSession, message string, intent *IntentResult, analysis *ContextAnalysis) (*SmartConversationResponse, error) {
	// 使用之前的意图类型 - 创建安全的参数副本以避免循环引用
	safeParams := make(map[string]interface{})
	// 暂时不复制参数以避免循环引用

	previousIntent := &IntentResult{
		Type:       analysis.PreviousIntent,
		Confidence: 0.8, // 给予较高置信度
		Parameters: safeParams,
		Command:    "",
	}

	// 合并可继承的参数
	for key, value := range analysis.InheritableParameters {
		if _, exists := previousIntent.Parameters[key]; !exists {
			previousIntent.Parameters[key] = value
		}
	}

	scm.logger.WithFields(logrus.Fields{
		"session_id":      session.SessionID,
		"previous_intent": analysis.PreviousIntent,
		"current_message": message,
		"param_count":     len(analysis.InheritableParameters),
	}).Info("Continuing previous intent")

	// 使用之前的意图处理当前消息
	return scm.handleIdleState(session, message, previousIntent)
}

// handleAnswerToQuestion 处理对问题的回答
func (scm *SmartConversationManager) handleAnswerToQuestion(session *SmartConversationSession, message string, intent *IntentResult, analysis *ContextAnalysis) (*SmartConversationResponse, error) {
	// 检查是否是确认回答
	if scm.isPositiveAnswer(message) {
		if session.PendingAction != nil {
			// 执行待处理的操作
			return &SmartConversationResponse{
				Content:              "正在执行操作...",
				Action:               "execute_pending_action",
				State:                StateExecuting,
				RequiresConfirmation: false,
				// 不包含PendingAction，避免循环引用
			}, nil
		}
	} else if scm.isNegativeAnswer(message) {
		// 取消操作
		session.PendingAction = nil
		session.State = StateIdle
		return &SmartConversationResponse{
			Content: "操作已取消。",
			Action:  "cancel",
			State:   StateIdle,
		}, nil
	}

	// 如果是参数收集状态，尝试提取参数
	if session.State == StateCollectingParams && session.PendingAction != nil {
		return scm.handleParamCollectionState(session, message, intent)
	}

	// 默认处理
	return scm.handleIdleState(session, message, intent)
}

// inheritParametersFromContext 从上下文继承参数
func (scm *SmartConversationManager) inheritParametersFromContext(session *SmartConversationSession, intent *IntentResult, analysis *ContextAnalysis) {
	// 临时禁用参数继承以避免循环引用
	scm.logger.WithFields(logrus.Fields{
		"session_id": session.SessionID,
		"count":      len(analysis.InheritableParameters),
	}).Debug("Parameter inheritance temporarily disabled to avoid circular reference")
}

// isPositiveAnswer 检查是否是肯定回答
func (scm *SmartConversationManager) isPositiveAnswer(message string) bool {
	lowerMsg := strings.ToLower(strings.TrimSpace(message))
	positiveAnswers := []string{
		"是", "好", "对", "可以", "确认", "同意", "继续", "执行",
		"yes", "ok", "okay", "sure", "yep", "confirm", "proceed",
	}

	for _, answer := range positiveAnswers {
		if lowerMsg == answer || strings.Contains(lowerMsg, answer) {
			return true
		}
	}

	return false
}

// isNegativeAnswer 检查是否是否定回答
func (scm *SmartConversationManager) isNegativeAnswer(message string) bool {
	lowerMsg := strings.ToLower(strings.TrimSpace(message))
	negativeAnswers := []string{
		"否", "不", "错", "不可以", "取消", "拒绝", "停止",
		"no", "nope", "cancel", "stop", "abort", "refuse",
	}

	for _, answer := range negativeAnswers {
		if lowerMsg == answer || strings.Contains(lowerMsg, answer) {
			return true
		}
	}

	return false
}
