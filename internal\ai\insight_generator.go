package ai

import (
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// InsightGenerator 洞察生成器
type InsightGenerator struct {
	logger *logrus.Logger
	config *PredictiveConfig
	rules  []InsightRule
}

// InsightRule 洞察规则
type InsightRule struct {
	Name        string                 `json:"name"`
	Type        InsightType            `json:"type"`
	Condition   func(*MLPredictions, *OperationsData) bool `json:"-"`
	Generator   func(*MLPredictions, *OperationsData) *PredictiveInsight `json:"-"`
	Priority    int                    `json:"priority"`
	Enabled     bool                   `json:"enabled"`
}

// NewInsightGenerator 创建洞察生成器
func NewInsightGenerator(logger *logrus.Logger, config *PredictiveConfig) *InsightGenerator {
	generator := &InsightGenerator{
		logger: logger,
		config: config,
		rules:  make([]InsightRule, 0),
	}

	// 初始化洞察规则
	generator.initializeInsightRules()

	logger.Info("💡 洞察生成器初始化完成")
	return generator
}

// GenerateInsights 生成洞察
func (ig *InsightGenerator) GenerateInsights(predictions *MLPredictions, data *OperationsData) []PredictiveInsight {
	start := time.Now()
	
	ig.logger.Info("💡 开始生成预测洞察")

	insights := make([]PredictiveInsight, 0)

	// 遍历所有规则
	for _, rule := range ig.rules {
		if !rule.Enabled {
			continue
		}

		// 检查条件
		if rule.Condition(predictions, data) {
			insight := rule.Generator(predictions, data)
			if insight != nil {
				insight.ID = ig.generateInsightID(rule.Name)
				insight.CreatedAt = time.Now()
				insight.Status = InsightStatusActive
				insights = append(insights, *insight)
			}
		}
	}

	// 按严重程度和置信度排序
	ig.sortInsightsByPriority(insights)

	processingTime := time.Since(start)
	ig.logger.WithFields(logrus.Fields{
		"insights_count":   len(insights),
		"processing_time":  processingTime,
	}).Info("💡 预测洞察生成完成")

	return insights
}

// initializeInsightRules 初始化洞察规则
func (ig *InsightGenerator) initializeInsightRules() {
	// 1. 系统健康度下降洞察
	ig.rules = append(ig.rules, InsightRule{
		Name:     "system_health_degradation",
		Type:     InsightTypePerformanceDegradation,
		Priority: 1,
		Enabled:  true,
		Condition: func(pred *MLPredictions, data *OperationsData) bool {
			return pred.SystemHealth < 0.7
		},
		Generator: func(pred *MLPredictions, data *OperationsData) *PredictiveInsight {
			severity := SeverityMedium
			if pred.SystemHealth < 0.5 {
				severity = SeverityHigh
			}
			if pred.SystemHealth < 0.3 {
				severity = SeverityCritical
			}

			return &PredictiveInsight{
				Type:        InsightTypePerformanceDegradation,
				Title:       "系统健康度下降预警",
				Description: fmt.Sprintf("预测系统健康度将下降至 %.1f%%，建议立即检查系统状态", pred.SystemHealth*100),
				Severity:    severity,
				Confidence:  pred.Confidence,
				PredictedTime: time.Now().Add(time.Hour),
				AffectedSystems: ig.getAffectedSystems(data),
				Indicators: []PredictiveIndicator{
					{
						Name:           "系统健康度",
						CurrentValue:   0.8, // 假设当前值
						PredictedValue: pred.SystemHealth,
						Trend:          TrendDecreasing,
						Confidence:     pred.Confidence,
						Threshold:      0.7,
						Unit:           "百分比",
					},
				},
			}
		},
	})

	// 2. 资源耗尽预警
	ig.rules = append(ig.rules, InsightRule{
		Name:     "resource_exhaustion",
		Type:     InsightTypeResourceExhaustion,
		Priority: 2,
		Enabled:  true,
		Condition: func(pred *MLPredictions, data *OperationsData) bool {
			return pred.ResourceUsage > 0.85
		},
		Generator: func(pred *MLPredictions, data *OperationsData) *PredictiveInsight {
			severity := SeverityHigh
			if pred.ResourceUsage > 0.95 {
				severity = SeverityCritical
			}

			return &PredictiveInsight{
				Type:        InsightTypeResourceExhaustion,
				Title:       "资源即将耗尽",
				Description: fmt.Sprintf("预测资源使用率将达到 %.1f%%，可能导致系统性能下降", pred.ResourceUsage*100),
				Severity:    severity,
				Confidence:  pred.Confidence,
				PredictedTime: time.Now().Add(2 * time.Hour),
				AffectedSystems: ig.getHighUsageHosts(data),
				Indicators: []PredictiveIndicator{
					{
						Name:           "资源使用率",
						CurrentValue:   data.CurrentResourceUsage,
						PredictedValue: pred.ResourceUsage,
						Trend:          TrendIncreasing,
						Confidence:     pred.Confidence,
						Threshold:      0.85,
						Unit:           "百分比",
					},
				},
			}
		},
	})

	// 3. 故障风险预警
	ig.rules = append(ig.rules, InsightRule{
		Name:     "failure_risk",
		Type:     InsightTypeSystemFailure,
		Priority: 3,
		Enabled:  true,
		Condition: func(pred *MLPredictions, data *OperationsData) bool {
			return pred.FailureProbability > 0.3
		},
		Generator: func(pred *MLPredictions, data *OperationsData) *PredictiveInsight {
			severity := SeverityMedium
			if pred.FailureProbability > 0.6 {
				severity = SeverityHigh
			}
			if pred.FailureProbability > 0.8 {
				severity = SeverityCritical
			}

			return &PredictiveInsight{
				Type:        InsightTypeSystemFailure,
				Title:       "系统故障风险升高",
				Description: fmt.Sprintf("预测系统故障概率为 %.1f%%，建议进行预防性维护", pred.FailureProbability*100),
				Severity:    severity,
				Confidence:  pred.Confidence,
				PredictedTime: time.Now().Add(4 * time.Hour),
				AffectedSystems: ig.getUnstableHosts(data),
				Indicators: []PredictiveIndicator{
					{
						Name:           "故障概率",
						CurrentValue:   0.1, // 假设当前值
						PredictedValue: pred.FailureProbability,
						Trend:          TrendIncreasing,
						Confidence:     pred.Confidence,
						Threshold:      0.3,
						Unit:           "概率",
					},
				},
			}
		},
	})

	// 4. 异常行为检测
	ig.rules = append(ig.rules, InsightRule{
		Name:     "anomaly_detection",
		Type:     InsightTypeSecurityThreat,
		Priority: 4,
		Enabled:  true,
		Condition: func(pred *MLPredictions, data *OperationsData) bool {
			return pred.AnomalyScore > 0.5
		},
		Generator: func(pred *MLPredictions, data *OperationsData) *PredictiveInsight {
			severity := SeverityMedium
			if pred.AnomalyScore > 0.7 {
				severity = SeverityHigh
			}

			return &PredictiveInsight{
				Type:        InsightTypeSecurityThreat,
				Title:       "检测到异常行为模式",
				Description: fmt.Sprintf("系统行为异常评分为 %.1f，可能存在安全威胁或系统异常", pred.AnomalyScore*100),
				Severity:    severity,
				Confidence:  pred.Confidence,
				PredictedTime: time.Now().Add(30 * time.Minute),
				AffectedSystems: ig.getAnomalousHosts(data),
				Indicators: []PredictiveIndicator{
					{
						Name:           "异常评分",
						CurrentValue:   0.2, // 假设当前值
						PredictedValue: pred.AnomalyScore,
						Trend:          TrendIncreasing,
						Confidence:     pred.Confidence,
						Threshold:      0.5,
						Unit:           "评分",
					},
				},
			}
		},
	})

	// 5. 性能优化机会
	ig.rules = append(ig.rules, InsightRule{
		Name:     "optimization_opportunity",
		Type:     InsightTypeOptimizationOpportunity,
		Priority: 5,
		Enabled:  true,
		Condition: func(pred *MLPredictions, data *OperationsData) bool {
			return data.PerformanceMetrics.CacheHitRate < 0.8 || 
				   data.ApplicationMetrics.AverageResponseTime > 200
		},
		Generator: func(pred *MLPredictions, data *OperationsData) *PredictiveInsight {
			return &PredictiveInsight{
				Type:        InsightTypeOptimizationOpportunity,
				Title:       "发现性能优化机会",
				Description: "系统存在性能优化空间，可以通过调整配置提升性能",
				Severity:    SeverityLow,
				Confidence:  0.8,
				PredictedTime: time.Now().Add(24 * time.Hour),
				AffectedSystems: []string{"应用服务器", "缓存系统"},
				Indicators: []PredictiveIndicator{
					{
						Name:           "缓存命中率",
						CurrentValue:   data.PerformanceMetrics.CacheHitRate,
						PredictedValue: 0.9,
						Trend:          TrendIncreasing,
						Confidence:     0.8,
						Threshold:      0.8,
						Unit:           "百分比",
					},
				},
			}
		},
	})

	// 6. 容量规划建议
	ig.rules = append(ig.rules, InsightRule{
		Name:     "capacity_planning",
		Type:     InsightTypeCapacityPlanning,
		Priority: 6,
		Enabled:  true,
		Condition: func(pred *MLPredictions, data *OperationsData) bool {
			return data.UserActivityMetrics.SessionsPerHour > 40 && 
				   data.CurrentResourceUsage > 0.7
		},
		Generator: func(pred *MLPredictions, data *OperationsData) *PredictiveInsight {
			return &PredictiveInsight{
				Type:        InsightTypeCapacityPlanning,
				Title:       "需要进行容量规划",
				Description: "用户活动增长，建议考虑扩容以应对未来负载",
				Severity:    SeverityMedium,
				Confidence:  0.75,
				PredictedTime: time.Now().Add(7 * 24 * time.Hour),
				AffectedSystems: []string{"应用服务器", "数据库"},
				Indicators: []PredictiveIndicator{
					{
						Name:           "用户会话数",
						CurrentValue:   data.UserActivityMetrics.SessionsPerHour,
						PredictedValue: data.UserActivityMetrics.SessionsPerHour * 1.3,
						Trend:          TrendIncreasing,
						Confidence:     0.75,
						Threshold:      40,
						Unit:           "会话/小时",
					},
				},
			}
		},
	})
}

// 辅助方法

func (ig *InsightGenerator) generateInsightID(ruleName string) string {
	return fmt.Sprintf("%s_%d", ruleName, time.Now().Unix())
}

func (ig *InsightGenerator) sortInsightsByPriority(insights []PredictiveInsight) {
	// 简单的排序：按严重程度和置信度排序
	for i := 0; i < len(insights)-1; i++ {
		for j := i + 1; j < len(insights); j++ {
			if ig.getInsightPriority(insights[i]) < ig.getInsightPriority(insights[j]) {
				insights[i], insights[j] = insights[j], insights[i]
			}
		}
	}
}

func (ig *InsightGenerator) getInsightPriority(insight PredictiveInsight) float64 {
	severityWeight := map[SeverityLevel]float64{
		SeverityCritical: 4.0,
		SeverityHigh:     3.0,
		SeverityMedium:   2.0,
		SeverityLow:      1.0,
	}

	return severityWeight[insight.Severity] * insight.Confidence
}

func (ig *InsightGenerator) getAffectedSystems(data *OperationsData) []string {
	systems := make([]string, 0)
	
	for _, host := range data.HostMetrics {
		if host.CPUUsage > 0.8 || host.MemoryUsage > 0.8 || host.ErrorCount > 3 {
			systems = append(systems, host.HostName)
		}
	}

	if len(systems) == 0 {
		systems = append(systems, "整体系统")
	}

	return systems
}

func (ig *InsightGenerator) getHighUsageHosts(data *OperationsData) []string {
	hosts := make([]string, 0)
	
	for _, host := range data.HostMetrics {
		avgUsage := (host.CPUUsage + host.MemoryUsage + host.DiskUsage) / 3
		if avgUsage > 0.8 {
			hosts = append(hosts, host.HostName)
		}
	}

	if len(hosts) == 0 {
		hosts = append(hosts, "所有主机")
	}

	return hosts
}

func (ig *InsightGenerator) getUnstableHosts(data *OperationsData) []string {
	hosts := make([]string, 0)
	
	for _, host := range data.HostMetrics {
		if host.ErrorCount > 2 || host.ResponseTime > 500 || host.Uptime < 0.9 {
			hosts = append(hosts, host.HostName)
		}
	}

	if len(hosts) == 0 {
		hosts = append(hosts, "部分主机")
	}

	return hosts
}

func (ig *InsightGenerator) getAnomalousHosts(data *OperationsData) []string {
	hosts := make([]string, 0)
	
	for _, host := range data.HostMetrics {
		// 简单的异常检测逻辑
		if (host.CPUUsage > 0.9 && host.MemoryUsage < 0.3) || 
		   (host.NetworkIO > 90 && host.CPUUsage < 0.2) ||
		   host.ErrorCount > 5 {
			hosts = append(hosts, host.HostName)
		}
	}

	if len(hosts) == 0 {
		hosts = append(hosts, "监控系统")
	}

	return hosts
}
