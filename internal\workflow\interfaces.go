package workflow

import (
	"aiops-platform/internal/model"
	"context"
)

// HostServiceInterface 主机服务接口
type HostServiceInterface interface {
	TestConnection(hostID int64) error
	CreateHost(host *model.Host) error
	GetHosts() ([]*model.Host, error)
	ExecuteCommand(hostID int64, req *model.CommandExecuteRequest) (*model.CommandExecuteResponse, error)
}

// AIServiceInterface AI服务接口
type AIServiceInterface interface {
	ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error)
	ExtractIntent(ctx context.Context, message string, context *ConversationContext) (*IntentResult, error)
}

// ProcessMessageRequest 处理消息请求
type ProcessMessageRequest struct {
	SessionID string `json:"session_id"`
	UserID    int64  `json:"user_id"`
	Message   string `json:"message"`
}

// ProcessMessageResponse 处理消息响应
type ProcessMessageResponse struct {
	Content           string                 `json:"content"`
	Intent            string                 `json:"intent"`
	Confidence        float64                `json:"confidence"`
	Parameters        map[string]interface{} `json:"parameters"`
	RecognizedActions []string               `json:"recognized_actions"`
	ActionSuggestions []string               `json:"action_suggestions"`
	RequiresFollowUp  bool                   `json:"requires_follow_up"`
	NextSteps         []string               `json:"next_steps"`
	ContextVariables  map[string]interface{} `json:"context_variables"`
	TokenUsage        int                    `json:"token_usage"`
	ProcessingTime    int64                  `json:"processing_time"`
}

// ConversationContext 对话上下文
type ConversationContext struct {
	SessionID     string                 `json:"session_id"`
	UserID        int64                  `json:"user_id"`
	Messages      []ContextMessage       `json:"messages"`
	Variables     map[string]interface{} `json:"variables"`
	LastIntent    string                 `json:"last_intent"`
	IntentHistory []string               `json:"intent_history"`
	CreatedAt     string                 `json:"created_at"`
	UpdatedAt     string                 `json:"updated_at"`
	MaxMessages   int                    `json:"max_messages"`
	TokenCount    int                    `json:"token_count"`
	MaxTokens     int                    `json:"max_tokens"`
}

// ContextMessage 上下文消息
type ContextMessage struct {
	Role      string `json:"role"`
	Content   string `json:"content"`
	Timestamp string `json:"timestamp"`
	Intent    string `json:"intent,omitempty"`
}

// IntentResult 意图识别结果
type IntentResult struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Command    string                 `json:"command,omitempty"`
}
