package service

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// RiskEngine 风险评估引擎
type RiskEngine struct {
	db           *gorm.DB
	logger       *logrus.Logger
	policies     map[string]*SecurityPolicy
	riskRules    []*RiskRule
	userProfiles map[int64]*UserRiskProfile
	hostProfiles map[int64]*HostRiskProfile
}

// SecurityPolicy 安全策略
type SecurityPolicy struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Rules       []*PolicyRule          `json:"rules"`
	Actions     []*PolicyAction        `json:"actions"`
	Priority    int                    `json:"priority"`
	Enabled     bool                   `json:"enabled"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// PolicyRule 策略规则
type PolicyRule struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"` // command, user, host, time, resource
	Condition string                 `json:"condition"`
	Value     interface{}            `json:"value"`
	Operator  string                 `json:"operator"` // equals, contains, matches, greater_than, etc.
	Metadata  map[string]interface{} `json:"metadata"`
}

// PolicyAction 策略动作
type PolicyAction struct {
	Type       string                 `json:"type"` // block, require_approval, log, notify
	Parameters map[string]interface{} `json:"parameters"`
	Message    string                 `json:"message"`
}

// RiskRule 风险规则
type RiskRule struct {
	ID          string         `json:"id"`
	Name        string         `json:"name"`
	Pattern     *regexp.Regexp `json:"-"`
	PatternStr  string         `json:"pattern"`
	RiskLevel   RiskLevel      `json:"risk_level"`
	Category    string         `json:"category"`
	Description string         `json:"description"`
	Weight      float64        `json:"weight"`
	Enabled     bool           `json:"enabled"`
}

// UserRiskProfile 用户风险档案
type UserRiskProfile struct {
	UserID           int64                  `json:"user_id"`
	RiskScore        float64                `json:"risk_score"`
	TrustLevel       string                 `json:"trust_level"`
	RecentViolations int                    `json:"recent_violations"`
	LastActivity     time.Time              `json:"last_activity"`
	Permissions      []string               `json:"permissions"`
	Restrictions     []string               `json:"restrictions"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// HostRiskProfile 主机风险档案
type HostRiskProfile struct {
	HostID       int64                  `json:"host_id"`
	RiskScore    float64                `json:"risk_score"`
	Environment  string                 `json:"environment"`
	Criticality  string                 `json:"criticality"`
	LastIncident time.Time              `json:"last_incident"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// RiskAssessmentRequest 风险评估请求
type RiskAssessmentRequest struct {
	UserID   int64                    `json:"user_id"`
	HostID   int64                    `json:"host_id"`
	Command  string                   `json:"command"`
	Context  *CommandExecutionContext `json:"context"`
	Metadata map[string]interface{}   `json:"metadata"`
}

// RiskAssessmentResult 风险评估结果
type RiskAssessmentResult struct {
	OverallRisk      RiskLevel              `json:"overall_risk"`
	RiskScore        float64                `json:"risk_score"`
	RequiredActions  []string               `json:"required_actions"`
	Warnings         []string               `json:"warnings"`
	Recommendations  []string               `json:"recommendations"`
	MatchedRules     []*MatchedRule         `json:"matched_rules"`
	MatchedPolicies  []*MatchedPolicy       `json:"matched_policies"`
	ApprovalRequired bool                   `json:"approval_required"`
	BlockExecution   bool                   `json:"block_execution"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// MatchedRule 匹配的规则
type MatchedRule struct {
	Rule        *RiskRule `json:"rule"`
	Confidence  float64   `json:"confidence"`
	MatchedText string    `json:"matched_text"`
}

// MatchedPolicy 匹配的策略
type MatchedPolicy struct {
	Policy       *SecurityPolicy `json:"policy"`
	MatchedRules []*PolicyRule   `json:"matched_rules"`
	Actions      []*PolicyAction `json:"actions"`
}

// NewRiskEngine 创建风险评估引擎
func NewRiskEngine(db *gorm.DB, logger *logrus.Logger) *RiskEngine {
	engine := &RiskEngine{
		db:           db,
		logger:       logger,
		policies:     make(map[string]*SecurityPolicy),
		riskRules:    make([]*RiskRule, 0),
		userProfiles: make(map[int64]*UserRiskProfile),
		hostProfiles: make(map[int64]*HostRiskProfile),
	}

	// 初始化默认规则和策略
	engine.initializeDefaultRules()
	engine.initializeDefaultPolicies()

	return engine
}

// initializeDefaultRules 初始化默认风险规则
func (re *RiskEngine) initializeDefaultRules() {
	defaultRules := []*RiskRule{
		{
			ID:          "critical-system-commands",
			Name:        "关键系统命令",
			PatternStr:  `(rm\s+-rf|mkfs|fdisk|shutdown|reboot|halt)`,
			RiskLevel:   RiskLevelCritical,
			Category:    "system",
			Description: "可能导致系统损坏或服务中断的关键命令",
			Weight:      1.0,
			Enabled:     true,
		},
		{
			ID:          "user-management",
			Name:        "用户管理命令",
			PatternStr:  `(userdel|usermod|passwd|su\s|sudo\s)`,
			RiskLevel:   RiskLevelHigh,
			Category:    "security",
			Description: "用户账户管理相关命令",
			Weight:      0.8,
			Enabled:     true,
		},
		{
			ID:          "network-operations",
			Name:        "网络操作命令",
			PatternStr:  `(iptables|ufw|firewall-cmd|netstat|ss\s)`,
			RiskLevel:   RiskLevelMedium,
			Category:    "network",
			Description: "网络配置和监控命令",
			Weight:      0.6,
			Enabled:     true,
		},
		{
			ID:          "file-operations",
			Name:        "文件操作命令",
			PatternStr:  `(chmod\s+777|chown\s+-R|find.*-exec)`,
			RiskLevel:   RiskLevelMedium,
			Category:    "filesystem",
			Description: "可能影响文件权限和安全的操作",
			Weight:      0.5,
			Enabled:     true,
		},
		{
			ID:          "process-management",
			Name:        "进程管理命令",
			PatternStr:  `(kill\s+-9|killall|pkill)`,
			RiskLevel:   RiskLevelLow,
			Category:    "process",
			Description: "进程终止和管理命令",
			Weight:      0.3,
			Enabled:     true,
		},
	}

	for _, rule := range defaultRules {
		pattern, err := regexp.Compile(rule.PatternStr)
		if err != nil {
			re.logger.WithError(err).WithField("rule_id", rule.ID).Error("Failed to compile risk rule pattern")
			continue
		}
		rule.Pattern = pattern
		re.riskRules = append(re.riskRules, rule)
	}

	re.logger.WithField("rule_count", len(re.riskRules)).Info("Default risk rules initialized")
}

// initializeDefaultPolicies 初始化默认安全策略
func (re *RiskEngine) initializeDefaultPolicies() {
	// 生产环境严格策略
	productionPolicy := &SecurityPolicy{
		ID:          "production-strict",
		Name:        "生产环境严格策略",
		Description: "生产环境下的严格安全控制策略",
		Priority:    100,
		Enabled:     true,
		Rules: []*PolicyRule{
			{
				ID:        "prod-env-check",
				Type:      "host",
				Condition: "environment",
				Value:     "production",
				Operator:  "equals",
			},
			{
				ID:        "critical-command-check",
				Type:      "command",
				Condition: "risk_level",
				Value:     "critical",
				Operator:  "equals",
			},
		},
		Actions: []*PolicyAction{
			{
				Type:    "block",
				Message: "生产环境禁止执行高风险命令",
			},
			{
				Type: "notify",
				Parameters: map[string]interface{}{
					"channels": []string{"security-team", "ops-team"},
					"priority": "high",
				},
				Message: "生产环境高风险命令尝试被阻止",
			},
		},
	}

	// 新用户限制策略
	newUserPolicy := &SecurityPolicy{
		ID:          "new-user-restrictions",
		Name:        "新用户限制策略",
		Description: "对新用户的操作限制策略",
		Priority:    80,
		Enabled:     true,
		Rules: []*PolicyRule{
			{
				ID:        "user-trust-level",
				Type:      "user",
				Condition: "trust_level",
				Value:     "low",
				Operator:  "equals",
			},
			{
				ID:        "medium-risk-command",
				Type:      "command",
				Condition: "risk_level",
				Value:     "medium",
				Operator:  "greater_than_or_equal",
			},
		},
		Actions: []*PolicyAction{
			{
				Type:    "require_approval",
				Message: "新用户执行中等风险以上命令需要审批",
				Parameters: map[string]interface{}{
					"approvers": []string{"senior-ops", "team-lead"},
					"timeout":   "30m",
				},
			},
		},
	}

	re.policies[productionPolicy.ID] = productionPolicy
	re.policies[newUserPolicy.ID] = newUserPolicy

	re.logger.WithField("policy_count", len(re.policies)).Info("Default security policies initialized")
}

// AssessRisk 评估风险
func (re *RiskEngine) AssessRisk(ctx context.Context, req *RiskAssessmentRequest) (*RiskAssessmentResult, error) {
	result := &RiskAssessmentResult{
		OverallRisk:      RiskLevelSafe,
		RiskScore:        0.0,
		RequiredActions:  make([]string, 0),
		Warnings:         make([]string, 0),
		Recommendations:  make([]string, 0),
		MatchedRules:     make([]*MatchedRule, 0),
		MatchedPolicies:  make([]*MatchedPolicy, 0),
		ApprovalRequired: false,
		BlockExecution:   false,
		Metadata:         make(map[string]interface{}),
	}

	// 1. 获取用户和主机风险档案
	userProfile := re.getUserRiskProfile(req.UserID)
	hostProfile := re.getHostRiskProfile(req.HostID)

	// 2. 命令风险分析
	commandRisk := re.analyzeCommandRisk(req.Command)
	result.MatchedRules = commandRisk.MatchedRules
	result.RiskScore += commandRisk.Score

	// 3. 用户风险分析
	userRisk := re.analyzeUserRisk(userProfile, req)
	result.RiskScore += userRisk.Score
	result.Warnings = append(result.Warnings, userRisk.Warnings...)

	// 4. 主机风险分析
	hostRisk := re.analyzeHostRisk(hostProfile, req)
	result.RiskScore += hostRisk.Score
	result.Warnings = append(result.Warnings, hostRisk.Warnings...)

	// 5. 策略匹配和执行
	policyResult := re.evaluatePolicies(req, result)
	result.MatchedPolicies = policyResult.MatchedPolicies
	result.RequiredActions = append(result.RequiredActions, policyResult.Actions...)

	// 6. 确定最终风险等级
	result.OverallRisk = re.calculateOverallRisk(result.RiskScore)

	// 7. 确定是否需要审批或阻止
	for _, policy := range result.MatchedPolicies {
		for _, action := range policy.Actions {
			switch action.Type {
			case "block":
				result.BlockExecution = true
				result.RequiredActions = append(result.RequiredActions, "block_execution")
			case "require_approval":
				result.ApprovalRequired = true
				result.RequiredActions = append(result.RequiredActions, "require_approval")
			}
		}
	}

	// 8. 生成建议
	result.Recommendations = re.generateRecommendations(result)

	re.logger.WithFields(logrus.Fields{
		"user_id":      req.UserID,
		"host_id":      req.HostID,
		"command":      req.Command,
		"risk_score":   result.RiskScore,
		"overall_risk": result.OverallRisk,
		"blocked":      result.BlockExecution,
		"approval":     result.ApprovalRequired,
	}).Info("Risk assessment completed")

	return result, nil
}

// CommandRiskAnalysis 命令风险分析结果
type CommandRiskAnalysis struct {
	Score        float64        `json:"score"`
	MatchedRules []*MatchedRule `json:"matched_rules"`
}

// UserRiskAnalysis 用户风险分析结果
type UserRiskAnalysis struct {
	Score    float64  `json:"score"`
	Warnings []string `json:"warnings"`
}

// HostRiskAnalysis 主机风险分析结果
type HostRiskAnalysis struct {
	Score    float64  `json:"score"`
	Warnings []string `json:"warnings"`
}

// PolicyEvaluationResult 策略评估结果
type PolicyEvaluationResult struct {
	MatchedPolicies []*MatchedPolicy `json:"matched_policies"`
	Actions         []string         `json:"actions"`
}

// analyzeCommandRisk 分析命令风险
func (re *RiskEngine) analyzeCommandRisk(command string) *CommandRiskAnalysis {
	analysis := &CommandRiskAnalysis{
		Score:        0.0,
		MatchedRules: make([]*MatchedRule, 0),
	}

	for _, rule := range re.riskRules {
		if !rule.Enabled {
			continue
		}

		if rule.Pattern.MatchString(command) {
			match := &MatchedRule{
				Rule:        rule,
				Confidence:  1.0,
				MatchedText: rule.Pattern.FindString(command),
			}
			analysis.MatchedRules = append(analysis.MatchedRules, match)

			// 根据风险等级计算分数
			switch rule.RiskLevel {
			case RiskLevelCritical:
				analysis.Score += 10.0 * rule.Weight
			case RiskLevelHigh:
				analysis.Score += 7.0 * rule.Weight
			case RiskLevelMedium:
				analysis.Score += 4.0 * rule.Weight
			case RiskLevelLow:
				analysis.Score += 2.0 * rule.Weight
			}
		}
	}

	return analysis
}

// getUserRiskProfile 获取用户风险档案
func (re *RiskEngine) getUserRiskProfile(userID int64) *UserRiskProfile {
	if profile, exists := re.userProfiles[userID]; exists {
		return profile
	}

	// 创建默认档案
	profile := &UserRiskProfile{
		UserID:           userID,
		RiskScore:        0.0,
		TrustLevel:       "medium",
		RecentViolations: 0,
		LastActivity:     time.Now(),
		Permissions:      []string{"read", "execute"},
		Restrictions:     []string{},
		Metadata:         make(map[string]interface{}),
	}

	re.userProfiles[userID] = profile
	return profile
}

// getHostRiskProfile 获取主机风险档案
func (re *RiskEngine) getHostRiskProfile(hostID int64) *HostRiskProfile {
	if profile, exists := re.hostProfiles[hostID]; exists {
		return profile
	}

	// 从数据库获取主机信息
	var host model.Host
	if err := re.db.First(&host, hostID).Error; err != nil {
		re.logger.WithError(err).WithField("host_id", hostID).Warn("Failed to get host info for risk profile")
	}

	// 创建默认档案
	profile := &HostRiskProfile{
		HostID:      hostID,
		RiskScore:   0.0,
		Environment: host.Environment,
		Criticality: "medium",
		Metadata:    make(map[string]interface{}),
	}

	re.hostProfiles[hostID] = profile
	return profile
}

// analyzeUserRisk 分析用户风险
func (re *RiskEngine) analyzeUserRisk(profile *UserRiskProfile, req *RiskAssessmentRequest) *UserRiskAnalysis {
	analysis := &UserRiskAnalysis{
		Score:    profile.RiskScore,
		Warnings: make([]string, 0),
	}

	// 检查信任等级
	switch profile.TrustLevel {
	case "low":
		analysis.Score += 3.0
		analysis.Warnings = append(analysis.Warnings, "用户信任等级较低")
	case "high":
		analysis.Score -= 1.0
	}

	// 检查最近违规
	if profile.RecentViolations > 0 {
		analysis.Score += float64(profile.RecentViolations) * 2.0
		analysis.Warnings = append(analysis.Warnings, fmt.Sprintf("用户最近有%d次违规记录", profile.RecentViolations))
	}

	return analysis
}

// analyzeHostRisk 分析主机风险
func (re *RiskEngine) analyzeHostRisk(profile *HostRiskProfile, req *RiskAssessmentRequest) *HostRiskAnalysis {
	analysis := &HostRiskAnalysis{
		Score:    profile.RiskScore,
		Warnings: make([]string, 0),
	}

	// 检查环境类型
	switch profile.Environment {
	case "production":
		analysis.Score += 5.0
		analysis.Warnings = append(analysis.Warnings, "目标为生产环境主机")
	case "staging":
		analysis.Score += 2.0
	case "development":
		analysis.Score += 0.0
	}

	// 检查关键性
	switch profile.Criticality {
	case "critical":
		analysis.Score += 4.0
		analysis.Warnings = append(analysis.Warnings, "目标为关键业务主机")
	case "high":
		analysis.Score += 2.0
	}

	return analysis
}

// evaluatePolicies 评估策略
func (re *RiskEngine) evaluatePolicies(req *RiskAssessmentRequest, result *RiskAssessmentResult) *PolicyEvaluationResult {
	evalResult := &PolicyEvaluationResult{
		MatchedPolicies: make([]*MatchedPolicy, 0),
		Actions:         make([]string, 0),
	}

	userProfile := re.getUserRiskProfile(req.UserID)
	hostProfile := re.getHostRiskProfile(req.HostID)

	for _, policy := range re.policies {
		if !policy.Enabled {
			continue
		}

		matched := re.evaluatePolicy(policy, req, userProfile, hostProfile, result)
		if matched != nil {
			evalResult.MatchedPolicies = append(evalResult.MatchedPolicies, matched)
			for _, action := range matched.Actions {
				evalResult.Actions = append(evalResult.Actions, action.Type)
			}
		}
	}

	return evalResult
}

// evaluatePolicy 评估单个策略
func (re *RiskEngine) evaluatePolicy(policy *SecurityPolicy, req *RiskAssessmentRequest, userProfile *UserRiskProfile, hostProfile *HostRiskProfile, result *RiskAssessmentResult) *MatchedPolicy {
	matchedRules := make([]*PolicyRule, 0)

	for _, rule := range policy.Rules {
		if re.evaluatePolicyRule(rule, req, userProfile, hostProfile, result) {
			matchedRules = append(matchedRules, rule)
		}
	}

	// 如果所有规则都匹配，则策略生效
	if len(matchedRules) == len(policy.Rules) {
		return &MatchedPolicy{
			Policy:       policy,
			MatchedRules: matchedRules,
			Actions:      policy.Actions,
		}
	}

	return nil
}

// evaluatePolicyRule 评估策略规则
func (re *RiskEngine) evaluatePolicyRule(rule *PolicyRule, req *RiskAssessmentRequest, userProfile *UserRiskProfile, hostProfile *HostRiskProfile, result *RiskAssessmentResult) bool {
	switch rule.Type {
	case "command":
		return re.evaluateCommandRule(rule, req, result)
	case "user":
		return re.evaluateUserRule(rule, userProfile)
	case "host":
		return re.evaluateHostRule(rule, hostProfile)
	case "time":
		return re.evaluateTimeRule(rule)
	default:
		return false
	}
}

// evaluateCommandRule 评估命令规则
func (re *RiskEngine) evaluateCommandRule(rule *PolicyRule, req *RiskAssessmentRequest, result *RiskAssessmentResult) bool {
	switch rule.Condition {
	case "risk_level":
		expectedLevel := rule.Value.(string)
		switch rule.Operator {
		case "equals":
			return string(result.OverallRisk) == expectedLevel
		case "greater_than_or_equal":
			return re.compareRiskLevels(result.OverallRisk, RiskLevel(expectedLevel)) >= 0
		}
	}
	return false
}

// evaluateUserRule 评估用户规则
func (re *RiskEngine) evaluateUserRule(rule *PolicyRule, profile *UserRiskProfile) bool {
	switch rule.Condition {
	case "trust_level":
		return profile.TrustLevel == rule.Value.(string)
	case "recent_violations":
		threshold := int(rule.Value.(float64))
		switch rule.Operator {
		case "greater_than":
			return profile.RecentViolations > threshold
		case "equals":
			return profile.RecentViolations == threshold
		}
	}
	return false
}

// evaluateHostRule 评估主机规则
func (re *RiskEngine) evaluateHostRule(rule *PolicyRule, profile *HostRiskProfile) bool {
	switch rule.Condition {
	case "environment":
		return profile.Environment == rule.Value.(string)
	case "criticality":
		return profile.Criticality == rule.Value.(string)
	}
	return false
}

// evaluateTimeRule 评估时间规则
func (re *RiskEngine) evaluateTimeRule(rule *PolicyRule) bool {
	// 实现时间相关的规则评估
	return true
}

// compareRiskLevels 比较风险等级
func (re *RiskEngine) compareRiskLevels(level1, level2 RiskLevel) int {
	levels := map[RiskLevel]int{
		RiskLevelSafe:     0,
		RiskLevelLow:      1,
		RiskLevelMedium:   2,
		RiskLevelHigh:     3,
		RiskLevelCritical: 4,
	}

	return levels[level1] - levels[level2]
}

// calculateOverallRisk 计算总体风险等级
func (re *RiskEngine) calculateOverallRisk(score float64) RiskLevel {
	switch {
	case score >= 15.0:
		return RiskLevelCritical
	case score >= 10.0:
		return RiskLevelHigh
	case score >= 5.0:
		return RiskLevelMedium
	case score >= 2.0:
		return RiskLevelLow
	default:
		return RiskLevelSafe
	}
}

// generateRecommendations 生成建议
func (re *RiskEngine) generateRecommendations(result *RiskAssessmentResult) []string {
	recommendations := make([]string, 0)

	if result.OverallRisk >= RiskLevelHigh {
		recommendations = append(recommendations, "建议在测试环境中先验证此命令")
		recommendations = append(recommendations, "考虑使用更安全的替代命令")
	}

	if result.ApprovalRequired {
		recommendations = append(recommendations, "请联系管理员获取执行授权")
	}

	if len(result.MatchedRules) > 0 {
		recommendations = append(recommendations, "请仔细检查命令参数和执行环境")
	}

	return recommendations
}

// UpdateUserRiskProfile 更新用户风险档案
func (re *RiskEngine) UpdateUserRiskProfile(userID int64, updates map[string]interface{}) error {
	profile := re.getUserRiskProfile(userID)

	if trustLevel, ok := updates["trust_level"].(string); ok {
		profile.TrustLevel = trustLevel
	}

	if violations, ok := updates["recent_violations"].(int); ok {
		profile.RecentViolations = violations
	}

	if riskScore, ok := updates["risk_score"].(float64); ok {
		profile.RiskScore = riskScore
	}

	profile.LastActivity = time.Now()

	re.logger.WithFields(logrus.Fields{
		"user_id":     userID,
		"trust_level": profile.TrustLevel,
		"risk_score":  profile.RiskScore,
	}).Info("User risk profile updated")

	return nil
}

// UpdateHostRiskProfile 更新主机风险档案
func (re *RiskEngine) UpdateHostRiskProfile(hostID int64, updates map[string]interface{}) error {
	profile := re.getHostRiskProfile(hostID)

	if criticality, ok := updates["criticality"].(string); ok {
		profile.Criticality = criticality
	}

	if riskScore, ok := updates["risk_score"].(float64); ok {
		profile.RiskScore = riskScore
	}

	re.logger.WithFields(logrus.Fields{
		"host_id":     hostID,
		"criticality": profile.Criticality,
		"risk_score":  profile.RiskScore,
	}).Info("Host risk profile updated")

	return nil
}

// AddSecurityPolicy 添加安全策略
func (re *RiskEngine) AddSecurityPolicy(policy *SecurityPolicy) error {
	re.policies[policy.ID] = policy

	re.logger.WithFields(logrus.Fields{
		"policy_id":   policy.ID,
		"policy_name": policy.Name,
		"priority":    policy.Priority,
	}).Info("Security policy added")

	return nil
}

// RemoveSecurityPolicy 移除安全策略
func (re *RiskEngine) RemoveSecurityPolicy(policyID string) error {
	delete(re.policies, policyID)

	re.logger.WithField("policy_id", policyID).Info("Security policy removed")

	return nil
}

// GetRiskStatistics 获取风险统计
func (re *RiskEngine) GetRiskStatistics() map[string]interface{} {
	stats := map[string]interface{}{
		"total_policies":   len(re.policies),
		"total_rules":      len(re.riskRules),
		"user_profiles":    len(re.userProfiles),
		"host_profiles":    len(re.hostProfiles),
		"enabled_policies": 0,
		"enabled_rules":    0,
	}

	for _, policy := range re.policies {
		if policy.Enabled {
			stats["enabled_policies"] = stats["enabled_policies"].(int) + 1
		}
	}

	for _, rule := range re.riskRules {
		if rule.Enabled {
			stats["enabled_rules"] = stats["enabled_rules"].(int) + 1
		}
	}

	return stats
}
