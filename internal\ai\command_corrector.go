package ai

import (
	"fmt"
	"sort"
	"strings"

	"github.com/sirupsen/logrus"
)

// CommandCorrector 命令纠错器
type CommandCorrector struct {
	commandKnowledge *CommandKnowledge
	logger           *logrus.Logger
}

// CommandKnowledge 命令知识库
type CommandKnowledge struct {
	Commands   map[string]*CommandInfo `json:"commands"`
	Aliases    map[string]string       `json:"aliases"`
	Categories map[string][]string     `json:"categories"`
	Patterns   map[string][]string     `json:"patterns"`
}

// CommandInfo 命令信息
type CommandInfo struct {
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Usage       string            `json:"usage"`
	Options     map[string]string `json:"options"`
	Examples    []string          `json:"examples"`
	Category    string            `json:"category"`
	Aliases     []string          `json:"aliases"`
	Related     []string          `json:"related"`
	Frequency   int               `json:"frequency"` // 使用频率
}

// CorrectionResult 纠错结果
type CorrectionResult struct {
	OriginalCommand  string               `json:"original_command"`
	CorrectedCommand string               `json:"corrected_command"`
	Confidence       float64              `json:"confidence"`
	CorrectionType   string               `json:"correction_type"` // spelling, alias, suggestion
	Suggestions      []*CommandSuggestion `json:"suggestions"`
	Explanation      string               `json:"explanation"`
}

// CommandSuggestion 命令建议
type CommandSuggestion struct {
	Command     string  `json:"command"`
	Description string  `json:"description"`
	Similarity  float64 `json:"similarity"`
	Usage       string  `json:"usage"`
	Example     string  `json:"example"`
}

// NewCommandCorrector 创建命令纠错器
func NewCommandCorrector(logger *logrus.Logger) *CommandCorrector {
	corrector := &CommandCorrector{
		commandKnowledge: &CommandKnowledge{
			Commands:   make(map[string]*CommandInfo),
			Aliases:    make(map[string]string),
			Categories: make(map[string][]string),
			Patterns:   make(map[string][]string),
		},
		logger: logger,
	}

	corrector.initializeCommandKnowledge()
	return corrector
}

// initializeCommandKnowledge 初始化命令知识库
func (cc *CommandCorrector) initializeCommandKnowledge() {
	// 系统信息命令
	cc.addCommand(&CommandInfo{
		Name:        "uname",
		Description: "显示系统信息",
		Usage:       "uname [选项]",
		Options: map[string]string{
			"-a": "显示所有信息",
			"-s": "显示内核名称",
			"-r": "显示内核版本",
			"-m": "显示机器硬件名称",
		},
		Examples:  []string{"uname -a", "uname -r"},
		Category:  "system_info",
		Aliases:   []string{"username", "uname-a"},
		Related:   []string{"hostname", "whoami", "id"},
		Frequency: 100,
	})

	// 文件操作命令
	cc.addCommand(&CommandInfo{
		Name:        "ls",
		Description: "列出目录内容",
		Usage:       "ls [选项] [文件...]",
		Options: map[string]string{
			"-l": "使用长格式",
			"-a": "显示隐藏文件",
			"-h": "人类可读的文件大小",
			"-t": "按修改时间排序",
		},
		Examples:  []string{"ls -la", "ls -lh", "ls -lt"},
		Category:  "file_operations",
		Aliases:   []string{"ll", "la", "list"},
		Related:   []string{"dir", "find", "tree"},
		Frequency: 150,
	})

	cc.addCommand(&CommandInfo{
		Name:        "cat",
		Description: "显示文件内容",
		Usage:       "cat [选项] [文件...]",
		Options: map[string]string{
			"-n": "显示行号",
			"-b": "显示非空行号",
			"-s": "压缩多个空行",
		},
		Examples:  []string{"cat file.txt", "cat -n file.txt"},
		Category:  "file_operations",
		Aliases:   []string{"type", "more"},
		Related:   []string{"less", "head", "tail"},
		Frequency: 80,
	})

	// 进程管理命令
	cc.addCommand(&CommandInfo{
		Name:        "ps",
		Description: "显示进程信息",
		Usage:       "ps [选项]",
		Options: map[string]string{
			"-ef":  "显示所有进程详细信息",
			"-aux": "显示所有用户进程",
			"-f":   "显示完整格式",
		},
		Examples:  []string{"ps -ef", "ps aux", "ps -f"},
		Category:  "process_management",
		Aliases:   []string{"process", "proc"},
		Related:   []string{"top", "htop", "pgrep", "kill"},
		Frequency: 90,
	})

	cc.addCommand(&CommandInfo{
		Name:        "top",
		Description: "显示实时进程信息",
		Usage:       "top [选项]",
		Options: map[string]string{
			"-p": "监控指定进程",
			"-u": "监控指定用户",
			"-n": "指定更新次数",
		},
		Examples:  []string{"top", "top -p 1234", "top -u root"},
		Category:  "process_management",
		Aliases:   []string{"htop", "atop"},
		Related:   []string{"ps", "kill", "jobs"},
		Frequency: 70,
	})

	// 系统监控命令
	cc.addCommand(&CommandInfo{
		Name:        "free",
		Description: "显示内存使用情况",
		Usage:       "free [选项]",
		Options: map[string]string{
			"-h": "人类可读格式",
			"-m": "以MB为单位",
			"-g": "以GB为单位",
		},
		Examples:  []string{"free -h", "free -m"},
		Category:  "system_monitoring",
		Aliases:   []string{"memory", "mem"},
		Related:   []string{"top", "vmstat", "sar"},
		Frequency: 60,
	})

	cc.addCommand(&CommandInfo{
		Name:        "df",
		Description: "显示磁盘使用情况",
		Usage:       "df [选项] [文件系统...]",
		Options: map[string]string{
			"-h": "人类可读格式",
			"-T": "显示文件系统类型",
			"-i": "显示inode信息",
		},
		Examples:  []string{"df -h", "df -T"},
		Category:  "system_monitoring",
		Aliases:   []string{"disk", "diskfree"},
		Related:   []string{"du", "lsblk", "mount"},
		Frequency: 50,
	})

	// 网络命令
	cc.addCommand(&CommandInfo{
		Name:        "netstat",
		Description: "显示网络连接信息",
		Usage:       "netstat [选项]",
		Options: map[string]string{
			"-an": "显示所有连接和监听端口",
			"-l":  "只显示监听端口",
			"-p":  "显示进程ID和名称",
		},
		Examples:  []string{"netstat -an", "netstat -lnp"},
		Category:  "network",
		Aliases:   []string{"ss", "network"},
		Related:   []string{"ss", "lsof", "nmap"},
		Frequency: 40,
	})

	// 服务管理命令
	cc.addCommand(&CommandInfo{
		Name:        "systemctl",
		Description: "系统服务管理",
		Usage:       "systemctl [命令] [服务名]",
		Options: map[string]string{
			"start":   "启动服务",
			"stop":    "停止服务",
			"restart": "重启服务",
			"status":  "查看服务状态",
			"enable":  "启用服务",
			"disable": "禁用服务",
		},
		Examples:  []string{"systemctl start nginx", "systemctl status mysql"},
		Category:  "service_management",
		Aliases:   []string{"service", "svc"},
		Related:   []string{"service", "chkconfig"},
		Frequency: 85,
	})

	// 构建别名映射
	cc.buildAliasMapping()
	cc.buildCategoryMapping()
}

// addCommand 添加命令到知识库
func (cc *CommandCorrector) addCommand(cmd *CommandInfo) {
	cc.commandKnowledge.Commands[cmd.Name] = cmd
}

// buildAliasMapping 构建别名映射
func (cc *CommandCorrector) buildAliasMapping() {
	for cmdName, cmd := range cc.commandKnowledge.Commands {
		for _, alias := range cmd.Aliases {
			cc.commandKnowledge.Aliases[alias] = cmdName
		}
	}

	// 添加常见的命令别名
	commonAliases := map[string]string{
		"ll":       "ls",
		"la":       "ls",
		"l":        "ls",
		"username": "uname",
		"mem":      "free",
		"memory":   "free",
		"disk":     "df",
		"proc":     "ps",
		"process":  "ps",
		"svc":      "systemctl",
		"service":  "systemctl",
	}

	for alias, command := range commonAliases {
		cc.commandKnowledge.Aliases[alias] = command
	}
}

// buildCategoryMapping 构建分类映射
func (cc *CommandCorrector) buildCategoryMapping() {
	for cmdName, cmd := range cc.commandKnowledge.Commands {
		category := cmd.Category
		if cc.commandKnowledge.Categories[category] == nil {
			cc.commandKnowledge.Categories[category] = make([]string, 0)
		}
		cc.commandKnowledge.Categories[category] = append(cc.commandKnowledge.Categories[category], cmdName)
	}
}

// CorrectCommand 纠错命令
func (cc *CommandCorrector) CorrectCommand(input string) *CorrectionResult {
	input = strings.TrimSpace(input)
	if input == "" {
		return &CorrectionResult{
			OriginalCommand: input,
			Confidence:      0.0,
			CorrectionType:  "empty",
			Explanation:     "命令为空",
		}
	}

	// 提取命令部分（第一个词）
	parts := strings.Fields(input)
	if len(parts) == 0 {
		return &CorrectionResult{
			OriginalCommand: input,
			Confidence:      0.0,
			CorrectionType:  "invalid",
			Explanation:     "无效命令格式",
		}
	}

	command := parts[0]
	args := strings.Join(parts[1:], " ")

	// 1. 检查是否是已知命令
	if _, exists := cc.commandKnowledge.Commands[command]; exists {
		return &CorrectionResult{
			OriginalCommand:  input,
			CorrectedCommand: input,
			Confidence:       1.0,
			CorrectionType:   "correct",
			Explanation:      "命令正确",
		}
	}

	// 2. 检查别名
	if realCommand, exists := cc.commandKnowledge.Aliases[command]; exists {
		corrected := realCommand
		if args != "" {
			corrected += " " + args
		}
		return &CorrectionResult{
			OriginalCommand:  input,
			CorrectedCommand: corrected,
			Confidence:       0.95,
			CorrectionType:   "alias",
			Explanation:      fmt.Sprintf("'%s' 是 '%s' 的别名", command, realCommand),
		}
	}

	// 3. 拼写纠错
	suggestions := cc.findSimilarCommands(command)
	if len(suggestions) > 0 && suggestions[0].Similarity > 0.7 {
		corrected := suggestions[0].Command
		if args != "" {
			corrected += " " + args
		}
		return &CorrectionResult{
			OriginalCommand:  input,
			CorrectedCommand: corrected,
			Confidence:       suggestions[0].Similarity,
			CorrectionType:   "spelling",
			Suggestions:      suggestions,
			Explanation:      fmt.Sprintf("可能是想输入 '%s'", suggestions[0].Command),
		}
	}

	// 4. 返回建议
	return &CorrectionResult{
		OriginalCommand: input,
		Confidence:      0.0,
		CorrectionType:  "suggestion",
		Suggestions:     suggestions,
		Explanation:     "未找到匹配的命令，以下是相似的命令建议",
	}
}

// findSimilarCommands 查找相似命令
func (cc *CommandCorrector) findSimilarCommands(input string) []*CommandSuggestion {
	suggestions := make([]*CommandSuggestion, 0)

	for cmdName, cmd := range cc.commandKnowledge.Commands {
		similarity := cc.calculateSimilarity(input, cmdName)

		// 也检查别名的相似度
		for _, alias := range cmd.Aliases {
			aliasSimilarity := cc.calculateSimilarity(input, alias)
			if aliasSimilarity > similarity {
				similarity = aliasSimilarity
			}
		}

		if similarity > 0.3 { // 相似度阈值
			suggestion := &CommandSuggestion{
				Command:     cmdName,
				Description: cmd.Description,
				Similarity:  similarity,
				Usage:       cmd.Usage,
			}

			if len(cmd.Examples) > 0 {
				suggestion.Example = cmd.Examples[0]
			}

			suggestions = append(suggestions, suggestion)
		}
	}

	// 按相似度和使用频率排序
	sort.Slice(suggestions, func(i, j int) bool {
		// 综合考虑相似度和使用频率
		scoreI := suggestions[i].Similarity * 0.7
		scoreJ := suggestions[j].Similarity * 0.7

		if cmdI, exists := cc.commandKnowledge.Commands[suggestions[i].Command]; exists {
			scoreI += float64(cmdI.Frequency) / 1000.0 * 0.3
		}
		if cmdJ, exists := cc.commandKnowledge.Commands[suggestions[j].Command]; exists {
			scoreJ += float64(cmdJ.Frequency) / 1000.0 * 0.3
		}

		return scoreI > scoreJ
	})

	// 限制建议数量
	if len(suggestions) > 5 {
		suggestions = suggestions[:5]
	}

	return suggestions
}

// calculateSimilarity 计算字符串相似度（编辑距离）
func (cc *CommandCorrector) calculateSimilarity(s1, s2 string) float64 {
	s1 = strings.ToLower(s1)
	s2 = strings.ToLower(s2)

	if s1 == s2 {
		return 1.0
	}

	// 计算编辑距离
	distance := cc.editDistance(s1, s2)
	maxLen := len(s1)
	if len(s2) > maxLen {
		maxLen = len(s2)
	}

	if maxLen == 0 {
		return 0.0
	}

	similarity := 1.0 - float64(distance)/float64(maxLen)

	// 考虑前缀匹配
	if strings.HasPrefix(s2, s1) || strings.HasPrefix(s1, s2) {
		similarity += 0.2
	}

	// 考虑包含关系
	if strings.Contains(s2, s1) || strings.Contains(s1, s2) {
		similarity += 0.1
	}

	if similarity > 1.0 {
		similarity = 1.0
	}

	return similarity
}

// editDistance 计算编辑距离
func (cc *CommandCorrector) editDistance(s1, s2 string) int {
	m, n := len(s1), len(s2)
	dp := make([][]int, m+1)
	for i := range dp {
		dp[i] = make([]int, n+1)
	}

	// 初始化
	for i := 0; i <= m; i++ {
		dp[i][0] = i
	}
	for j := 0; j <= n; j++ {
		dp[0][j] = j
	}

	// 动态规划
	for i := 1; i <= m; i++ {
		for j := 1; j <= n; j++ {
			if s1[i-1] == s2[j-1] {
				dp[i][j] = dp[i-1][j-1]
			} else {
				dp[i][j] = 1 + min(dp[i-1][j], dp[i][j-1], dp[i-1][j-1])
			}
		}
	}

	return dp[m][n]
}

// min 返回三个数的最小值
func min(a, b, c int) int {
	if a < b {
		if a < c {
			return a
		}
		return c
	}
	if b < c {
		return b
	}
	return c
}

// GetCommandSuggestions 获取命令建议
func (cc *CommandCorrector) GetCommandSuggestions(category string) []*CommandSuggestion {
	suggestions := make([]*CommandSuggestion, 0)

	var commands []string
	if category == "" {
		// 返回最常用的命令
		for cmdName := range cc.commandKnowledge.Commands {
			commands = append(commands, cmdName)
		}
	} else {
		// 返回指定分类的命令
		if categoryCommands, exists := cc.commandKnowledge.Categories[category]; exists {
			commands = categoryCommands
		}
	}

	// 按使用频率排序
	sort.Slice(commands, func(i, j int) bool {
		freqI := cc.commandKnowledge.Commands[commands[i]].Frequency
		freqJ := cc.commandKnowledge.Commands[commands[j]].Frequency
		return freqI > freqJ
	})

	// 构建建议
	for _, cmdName := range commands {
		if cmd, exists := cc.commandKnowledge.Commands[cmdName]; exists {
			suggestion := &CommandSuggestion{
				Command:     cmdName,
				Description: cmd.Description,
				Similarity:  1.0,
				Usage:       cmd.Usage,
			}

			if len(cmd.Examples) > 0 {
				suggestion.Example = cmd.Examples[0]
			}

			suggestions = append(suggestions, suggestion)
		}
	}

	return suggestions
}

// UpdateCommandFrequency 更新命令使用频率
func (cc *CommandCorrector) UpdateCommandFrequency(command string) {
	if cmd, exists := cc.commandKnowledge.Commands[command]; exists {
		cmd.Frequency++
		cc.logger.WithFields(logrus.Fields{
			"command":   command,
			"frequency": cmd.Frequency,
		}).Debug("Command frequency updated")
	}
}
