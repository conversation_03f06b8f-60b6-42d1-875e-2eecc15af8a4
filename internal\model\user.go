package model

import (
	"time"

	"gorm.io/gorm"
)

// User 用户模型
type User struct {
	ID                int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	Username          string         `json:"username" gorm:"uniqueIndex;size:50;not null"`
	PasswordHash      string         `json:"-" gorm:"size:255;not null"`
	Email             string         `json:"email" gorm:"uniqueIndex;size:100;not null"`
	FullName          string         `json:"full_name" gorm:"size:100;not null"`
	Role              string         `json:"role" gorm:"size:20;not null;default:viewer"`
	IsActive          bool           `json:"is_active" gorm:"not null;default:true"`
	AvatarURL         string         `json:"avatar_url" gorm:"size:255"`
	Phone             string         `json:"phone" gorm:"size:20"`
	Department        string         `json:"department" gorm:"size:50"`
	CreatedAt         time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt         time.Time      `json:"updated_at" gorm:"not null"`
	LastLogin         *time.Time     `json:"last_login"`
	LoginCount        int            `json:"login_count" gorm:"default:0"`
	FailedLoginCount  int            `json:"failed_login_count" gorm:"default:0"`
	LockedUntil       *time.Time     `json:"locked_until"`
	PasswordChangedAt *time.Time     `json:"password_changed_at"`
	TwoFactorEnabled  bool           `json:"two_factor_enabled" gorm:"default:false"`
	TwoFactorSecret   string         `json:"-" gorm:"size:32"`
	DeletedAt         gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	CreatedHosts   []Host         `json:"-" gorm:"foreignKey:CreatedBy"`
	AssignedAlerts []Alert        `json:"-" gorm:"foreignKey:AssignedTo"`
	OperationLogs  []OperationLog `json:"-" gorm:"foreignKey:UserID"`
	ChatSessions   []ChatSession  `json:"-" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (User) TableName() string {
	return "users"
}

// BeforeCreate GORM钩子：创建前
func (u *User) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	u.CreatedAt = now
	u.UpdatedAt = now
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (u *User) BeforeUpdate(tx *gorm.DB) error {
	u.UpdatedAt = time.Now()
	return nil
}

// IsLocked 检查用户是否被锁定
func (u *User) IsLocked() bool {
	if u.LockedUntil == nil {
		return false
	}
	return time.Now().Before(*u.LockedUntil)
}

// CanLogin 检查用户是否可以登录
func (u *User) CanLogin() bool {
	return u.IsActive && !u.IsLocked()
}

// IsValidRole 检查角色是否有效
func IsValidRole(role string) bool {
	switch role {
	case RoleAdmin, RoleOperator, RoleViewer, RoleDeveloper, RoleGuest:
		return true
	default:
		return false
	}
}

// GetRoleLevel 获取角色级别（数字越大权限越高）
func GetRoleLevel(role string) int {
	switch role {
	case RoleGuest:
		return 1
	case RoleViewer:
		return 2
	case RoleDeveloper:
		return 3
	case RoleOperator:
		return 4
	case RoleAdmin:
		return 5
	default:
		return 0
	}
}

// HasHigherRole 检查是否有更高的角色权限
func (u *User) HasHigherRole(targetRole string) bool {
	return GetRoleLevel(u.Role) > GetRoleLevel(targetRole)
}

// UserCreateRequest 创建用户请求
type UserCreateRequest struct {
	Username   string `json:"username" binding:"required,min=3,max=50"`
	Password   string `json:"password" binding:"required,min=8"`
	Email      string `json:"email" binding:"required,email"`
	FullName   string `json:"full_name" binding:"required,min=2,max=100"`
	Role       string `json:"role" binding:"required,oneof=super_admin admin operator viewer"`
	Phone      string `json:"phone" binding:"omitempty,min=10,max=20"`
	Department string `json:"department" binding:"omitempty,max=50"`
}

// UserUpdateRequest 更新用户请求
type UserUpdateRequest struct {
	Email      string `json:"email" binding:"omitempty,email"`
	FullName   string `json:"full_name" binding:"omitempty,min=2,max=100"`
	Role       string `json:"role" binding:"omitempty,oneof=super_admin admin operator viewer"`
	IsActive   *bool  `json:"is_active"`
	Phone      string `json:"phone" binding:"omitempty,min=10,max=20"`
	Department string `json:"department" binding:"omitempty,max=50"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID         int64      `json:"id"`
	Username   string     `json:"username"`
	Email      string     `json:"email"`
	FullName   string     `json:"full_name"`
	Role       string     `json:"role"`
	IsActive   bool       `json:"is_active"`
	AvatarURL  string     `json:"avatar_url"`
	Phone      string     `json:"phone"`
	Department string     `json:"department"`
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
	LastLogin  *time.Time `json:"last_login"`
	LoginCount int        `json:"login_count"`
	IsLocked   bool       `json:"is_locked"`
}

// ToResponse 转换为响应格式
func (u *User) ToResponse() *UserResponse {
	return &UserResponse{
		ID:         u.ID,
		Username:   u.Username,
		Email:      u.Email,
		FullName:   u.FullName,
		Role:       u.Role,
		IsActive:   u.IsActive,
		AvatarURL:  u.AvatarURL,
		Phone:      u.Phone,
		Department: u.Department,
		CreatedAt:  u.CreatedAt,
		UpdatedAt:  u.UpdatedAt,
		LastLogin:  u.LastLogin,
		LoginCount: u.LoginCount,
		IsLocked:   u.IsLocked(),
	}
}

// PasswordChangeRequest 修改密码请求
type PasswordChangeRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

// UserListQuery 用户列表查询参数
type UserListQuery struct {
	Page       int    `form:"page,default=1" binding:"min=1"`
	Limit      int    `form:"limit,default=20" binding:"min=1,max=100"`
	Role       string `form:"role" binding:"omitempty,oneof=super_admin admin operator viewer"`
	IsActive   *bool  `form:"is_active"`
	Search     string `form:"search" binding:"omitempty,max=100"`
	Department string `form:"department" binding:"omitempty,max=50"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Users      []*UserResponse `json:"users"`
	Pagination *Pagination     `json:"pagination"`
}
