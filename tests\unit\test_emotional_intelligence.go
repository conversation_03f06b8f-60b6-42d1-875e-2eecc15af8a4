package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/ai"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockDeepSeekService 模拟DeepSeek服务
type MockDeepSeekService struct{}

func (m *MockDeepSeekService) Chat(ctx context.Context, messages []ai.EmotionMessage) (*ai.EmotionChatResponse, error) {
	// 模拟情感分析响应
	content := `{
		"primary_emotion": "frustration",
		"secondary_emotion": "anxiety",
		"intensity": 0.8,
		"confidence": 0.9,
		"context": "ai_enhanced",
		"reasoning": "用户使用了'错误'、'失败'等负面词汇，表现出明显的挫折情绪",
		"indicators": ["负面词汇", "问题描述", "求助语气"]
	}`

	return &ai.EmotionChatResponse{
		Choices: []ai.EmotionChoice{
			{
				Message: ai.EmotionChatMessage{
					Role:    "assistant",
					Content: content,
				},
			},
		},
		Usage: ai.EmotionUsage{
			TotalTokens: 100,
		},
	}, nil
}

func main() {
	fmt.Println("🧠 情感智能引擎测试开始...")

	// 1. 初始化日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 2. 初始化数据库
	db, err := gorm.Open(sqlite.Open("test_emotion.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect database:", err)
	}

	// 3. 创建模拟DeepSeek服务
	mockDeepSeek := &MockDeepSeekService{}

	// 4. 创建情感智能引擎配置
	config := &ai.EmotionalIntelligenceConfig{
		EnableEmotionRecognition: true,
		EnableEmotionExpression:  true,
		EnableEmotionMemory:      true,
		EmotionThreshold:         0.6,
		MemoryRetentionDays:      30,
		AdaptationRate:           0.1,
		MaxEmotionHistory:        100,
		EnableDeepSeekEmotion:    true,
		EmotionUpdateInterval:    time.Hour,
	}

	// 5. 创建情感智能引擎
	engine := ai.NewEmotionalIntelligenceEngine(db, logger, mockDeepSeek, config)

	// 6. 启动引擎
	if err := engine.Start(); err != nil {
		log.Fatal("Failed to start emotional intelligence engine:", err)
	}
	defer engine.Stop()

	fmt.Println("✅ 情感智能引擎启动成功")

	// 7. 测试情感分析
	testMessages := []struct {
		message string
		desc    string
	}{
		{
			message: "服务器又出错了，这个系统太不稳定了！",
			desc:    "挫折情感测试",
		},
		{
			message: "太好了！问题终于解决了，谢谢你的帮助！",
			desc:    "喜悦情感测试",
		},
		{
			message: "系统很慢，我很着急，能快点处理吗？",
			desc:    "焦虑情感测试",
		},
		{
			message: "请帮我查看一下服务器状态",
			desc:    "中性情感测试",
		},
	}

	ctx := context.Background()
	userID := int64(1)
	sessionID := "test_session_001"

	for i, test := range testMessages {
		fmt.Printf("\n🧪 测试 %d: %s\n", i+1, test.desc)
		fmt.Printf("📝 用户消息: %s\n", test.message)

		// 情感分析
		emotionAnalysis, err := engine.AnalyzeEmotion(
			ctx,
			userID,
			sessionID,
			test.message,
			map[string]interface{}{
				"test_context": true,
			},
		)

		if err != nil {
			fmt.Printf("❌ 情感分析失败: %v\n", err)
			continue
		}

		fmt.Printf("🎭 检测到的情感:\n")
		fmt.Printf("   主要情感: %s (强度: %.2f, 置信度: %.2f)\n",
			emotionAnalysis.UserEmotion.PrimaryEmotion,
			emotionAnalysis.UserEmotion.Intensity,
			emotionAnalysis.UserEmotion.Confidence,
		)
		fmt.Printf("   建议语调: %s\n", emotionAnalysis.SuggestedTone)
		fmt.Printf("   响应风格: %s\n", emotionAnalysis.ResponseStyle)

		// 测试响应增强
		originalResponse := "我来帮您处理这个问题。"
		enhancedResponse, err := engine.EnhanceResponse(
			ctx,
			originalResponse,
			emotionAnalysis,
			userID,
		)

		if err != nil {
			fmt.Printf("❌ 响应增强失败: %v\n", err)
		} else {
			fmt.Printf("💬 原始响应: %s\n", originalResponse)
			fmt.Printf("✨ 增强响应: %s\n", enhancedResponse)
		}

		// 短暂延迟
		time.Sleep(500 * time.Millisecond)
	}

	// 8. 测试用户情感画像
	fmt.Printf("\n📊 生成用户情感画像...\n")
	profile, err := engine.GetUserEmotionalProfile(userID)
	if err != nil {
		fmt.Printf("❌ 获取用户画像失败: %v\n", err)
	} else {
		fmt.Printf("👤 用户 %d 的情感画像:\n", userID)
		fmt.Printf("   主导情感: %v\n", profile.DominantEmotions)
		fmt.Printf("   偏好语调: %s\n", profile.PreferredTone)
		fmt.Printf("   交互历史: %d 条记录\n", len(profile.InteractionHistory))
	}

	// 9. 测试引擎指标
	fmt.Printf("\n📈 引擎运行指标:\n")
	metrics := engine.GetMetrics()
	for key, value := range metrics {
		fmt.Printf("   %s: %v\n", key, value)
	}

	fmt.Println("\n🎉 情感智能引擎测试完成！")
}
