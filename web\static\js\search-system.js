/* ========================================
   高级搜索系统JavaScript
   提供全局搜索和智能过滤功能
   ======================================== */

class SearchSystem {
    constructor() {
        this.searchData = {
            conversations: [],
            messages: [],
            commands: [],
            hosts: [],
            alerts: []
        };
        
        this.searchHistory = [];
        this.currentQuery = '';
        this.currentFilters = {
            type: [],
            timeRange: 'all',
            tags: [],
            status: []
        };
        
        this.searchResults = [];
        this.selectedIndex = -1;
        
        this.init();
    }
    
    init() {
        this.loadSearchData();
        this.loadSearchHistory();
        this.createSearchInterface();
        this.bindEvents();
        
        console.log('🔍 搜索系统已初始化');
    }
    
    // 加载搜索数据
    loadSearchData() {
        // 从对话历史管理器获取数据
        if (window.chatHistoryManager) {
            this.searchData.conversations = window.chatHistoryManager.conversations || [];
        }
        
        // 模拟其他数据
        this.searchData.messages = this.generateSampleMessages();
        this.searchData.commands = this.generateSampleCommands();
        this.searchData.hosts = this.generateSampleHosts();
        this.searchData.alerts = this.generateSampleAlerts();
    }
    
    generateSampleMessages() {
        return [
            {
                id: 'msg_1',
                type: 'message',
                title: '系统状态检查结果',
                content: '所有服务器运行正常，CPU使用率平均为45%',
                timestamp: new Date(Date.now() - 3600000),
                tags: ['系统', '状态'],
                conversationId: 'conv_1'
            },
            {
                id: 'msg_2',
                type: 'message',
                title: '数据库性能分析',
                content: '查询响应时间有所增加，建议优化索引',
                timestamp: new Date(Date.now() - 7200000),
                tags: ['数据库', '性能'],
                conversationId: 'conv_2'
            }
        ];
    }
    
    generateSampleCommands() {
        return [
            {
                id: 'cmd_1',
                type: 'command',
                title: 'ps aux',
                content: '查看系统进程列表',
                timestamp: new Date(Date.now() - 1800000),
                tags: ['进程', '监控'],
                host: 'web-server-01'
            },
            {
                id: 'cmd_2',
                type: 'command',
                title: 'df -h',
                content: '检查磁盘使用情况',
                timestamp: new Date(Date.now() - 3600000),
                tags: ['磁盘', '存储'],
                host: 'db-server-01'
            }
        ];
    }
    
    generateSampleHosts() {
        return [
            {
                id: 'host_1',
                type: 'host',
                title: 'web-server-01',
                content: 'Web服务器 - ************',
                timestamp: new Date(),
                tags: ['web', '生产环境'],
                status: 'online'
            },
            {
                id: 'host_2',
                type: 'host',
                title: 'db-server-01',
                content: '数据库服务器 - ************',
                timestamp: new Date(),
                tags: ['数据库', '生产环境'],
                status: 'online'
            }
        ];
    }
    
    generateSampleAlerts() {
        return [
            {
                id: 'alert_1',
                type: 'alert',
                title: 'CPU使用率过高',
                content: 'web-server-01 CPU使用率达到85%',
                timestamp: new Date(Date.now() - 900000),
                tags: ['CPU', '告警'],
                severity: 'warning'
            },
            {
                id: 'alert_2',
                type: 'alert',
                title: '磁盘空间不足',
                content: 'db-server-01 磁盘使用率达到90%',
                timestamp: new Date(Date.now() - 1800000),
                tags: ['磁盘', '告警'],
                severity: 'critical'
            }
        ];
    }
    
    // 创建搜索界面
    createSearchInterface() {
        const navbar = document.querySelector('.navbar-center');
        if (!navbar) return;
        
        const searchContainer = document.createElement('div');
        searchContainer.className = 'global-search';
        searchContainer.innerHTML = this.createSearchHTML();
        
        navbar.appendChild(searchContainer);
        
        this.searchInput = document.getElementById('global-search-input');
        this.searchResults = document.getElementById('search-results');
        this.filtersPanel = document.getElementById('search-filters-panel');
    }
    
    createSearchHTML() {
        return `
            <div class="search-input-wrapper" id="search-input-wrapper">
                <i class="bi bi-search search-icon"></i>
                <input 
                    type="text" 
                    class="search-input" 
                    id="global-search-input"
                    placeholder="搜索对话、命令、主机... (Ctrl+K)"
                    autocomplete="off"
                >
                <div class="search-actions">
                    <button class="search-filter-btn" id="search-filter-btn" title="过滤器">
                        <i class="bi bi-funnel"></i>
                    </button>
                    <button class="search-clear-btn" id="search-clear-btn" title="清空" style="display: none;">
                        <i class="bi bi-x"></i>
                    </button>
                </div>
            </div>
            
            <div class="search-results" id="search-results">
                <div class="search-results-header">
                    <span class="search-results-count" id="search-results-count">0 个结果</span>
                    <div class="search-results-actions">
                        <button class="search-action-btn" onclick="window.searchSystem.exportResults()">导出</button>
                        <button class="search-action-btn" onclick="window.searchSystem.saveSearch()">保存</button>
                    </div>
                </div>
                <div class="search-results-content" id="search-results-content"></div>
            </div>
            
            <div class="search-filters-panel" id="search-filters-panel">
                ${this.createFiltersHTML()}
            </div>
        `;
    }
    
    createFiltersHTML() {
        return `
            <div class="filters-header">
                <span class="filters-title">搜索过滤器</span>
                <a href="#" class="filters-clear" onclick="window.searchSystem.clearFilters()">清空</a>
            </div>
            
            <div class="filter-group">
                <div class="filter-group-title">内容类型</div>
                <div class="filter-options">
                    <div class="filter-option" data-filter="type" data-value="conversation">
                        <div class="filter-checkbox"></div>
                        <span class="filter-label">对话</span>
                        <span class="filter-count">${this.searchData.conversations.length}</span>
                    </div>
                    <div class="filter-option" data-filter="type" data-value="message">
                        <div class="filter-checkbox"></div>
                        <span class="filter-label">消息</span>
                        <span class="filter-count">${this.searchData.messages.length}</span>
                    </div>
                    <div class="filter-option" data-filter="type" data-value="command">
                        <div class="filter-checkbox"></div>
                        <span class="filter-label">命令</span>
                        <span class="filter-count">${this.searchData.commands.length}</span>
                    </div>
                    <div class="filter-option" data-filter="type" data-value="host">
                        <div class="filter-checkbox"></div>
                        <span class="filter-label">主机</span>
                        <span class="filter-count">${this.searchData.hosts.length}</span>
                    </div>
                    <div class="filter-option" data-filter="type" data-value="alert">
                        <div class="filter-checkbox"></div>
                        <span class="filter-label">告警</span>
                        <span class="filter-count">${this.searchData.alerts.length}</span>
                    </div>
                </div>
            </div>
            
            <div class="filter-group">
                <div class="filter-group-title">时间范围</div>
                <div class="filter-options">
                    <div class="filter-option" data-filter="timeRange" data-value="today">
                        <div class="filter-checkbox"></div>
                        <span class="filter-label">今天</span>
                    </div>
                    <div class="filter-option" data-filter="timeRange" data-value="week">
                        <div class="filter-checkbox"></div>
                        <span class="filter-label">本周</span>
                    </div>
                    <div class="filter-option" data-filter="timeRange" data-value="month">
                        <div class="filter-checkbox"></div>
                        <span class="filter-label">本月</span>
                    </div>
                    <div class="filter-option" data-filter="timeRange" data-value="all">
                        <div class="filter-checkbox checked"></div>
                        <span class="filter-label">全部</span>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 绑定事件
    bindEvents() {
        // 搜索输入事件
        this.searchInput.addEventListener('input', (e) => {
            this.handleSearchInput(e.target.value);
        });
        
        // 键盘导航
        this.searchInput.addEventListener('keydown', (e) => {
            this.handleKeyNavigation(e);
        });
        
        // 搜索焦点事件
        this.searchInput.addEventListener('focus', () => {
            this.showSearchHistory();
        });
        
        // 过滤器按钮
        document.getElementById('search-filter-btn').addEventListener('click', (e) => {
            e.stopPropagation();
            this.toggleFiltersPanel();
        });
        
        // 清空按钮
        document.getElementById('search-clear-btn').addEventListener('click', () => {
            this.clearSearch();
        });
        
        // 过滤器选项
        this.filtersPanel.addEventListener('click', (e) => {
            if (e.target.closest('.filter-option')) {
                this.handleFilterClick(e.target.closest('.filter-option'));
            }
        });
        
        // 点击外部关闭
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.global-search')) {
                this.hideSearchResults();
                this.hideFiltersPanel();
            }
        });
        
        // 全局搜索快捷键 (Ctrl+K)
        document.addEventListener('keydown', (e) => {
            if ((e.ctrlKey || e.metaKey) && e.key === 'k') {
                e.preventDefault();
                this.focusSearch();
            }
        });
    }
    
    // 处理搜索输入
    handleSearchInput(query) {
        this.currentQuery = query.trim();
        
        // 显示/隐藏清空按钮
        const clearBtn = document.getElementById('search-clear-btn');
        clearBtn.style.display = this.currentQuery ? 'flex' : 'none';
        
        if (this.currentQuery.length === 0) {
            this.showSearchHistory();
            return;
        }
        
        if (this.currentQuery.length < 2) {
            this.hideSearchResults();
            return;
        }
        
        // 执行搜索
        this.performSearch();
    }
    
    // 执行搜索
    performSearch() {
        const results = this.searchAllData(this.currentQuery);
        const filteredResults = this.applyFilters(results);
        
        this.displaySearchResults(filteredResults);
        this.selectedIndex = -1;
        
        // 保存到搜索历史
        this.addToSearchHistory(this.currentQuery);
    }
    
    // 搜索所有数据
    searchAllData(query) {
        const results = [];
        const queryLower = query.toLowerCase();

        // 搜索对话
        this.searchData.conversations.forEach(item => {
            if (this.matchesQuery(item, queryLower)) {
                results.push({ ...item, type: 'conversation' });
            }
        });

        // 搜索消息
        this.searchData.messages.forEach(item => {
            if (this.matchesQuery(item, queryLower)) {
                results.push(item);
            }
        });

        // 搜索命令
        this.searchData.commands.forEach(item => {
            if (this.matchesQuery(item, queryLower)) {
                results.push(item);
            }
        });

        // 搜索主机
        this.searchData.hosts.forEach(item => {
            if (this.matchesQuery(item, queryLower)) {
                results.push(item);
            }
        });

        // 搜索告警
        this.searchData.alerts.forEach(item => {
            if (this.matchesQuery(item, queryLower)) {
                results.push(item);
            }
        });

        return this.sortByRelevance(results, queryLower);
    }

    matchesQuery(item, query) {
        const searchFields = [
            item.title || '',
            item.content || item.preview || '',
            (item.tags || []).join(' ')
        ].join(' ').toLowerCase();

        return searchFields.includes(query);
    }

    sortByRelevance(results, query) {
        return results.sort((a, b) => {
            const scoreA = this.calculateRelevanceScore(a, query);
            const scoreB = this.calculateRelevanceScore(b, query);
            return scoreB - scoreA;
        });
    }

    calculateRelevanceScore(item, query) {
        let score = 0;
        const title = (item.title || '').toLowerCase();
        const content = (item.content || item.preview || '').toLowerCase();

        if (title.includes(query)) {
            score += title.startsWith(query) ? 100 : 50;
        }
        if (content.includes(query)) {
            score += 20;
        }
        if (item.tags && item.tags.some(tag => tag.toLowerCase().includes(query))) {
            score += 30;
        }

        return score;
    }

    applyFilters(results) {
        return results.filter(item => {
            if (this.currentFilters.type.length > 0 &&
                !this.currentFilters.type.includes(item.type)) {
                return false;
            }
            return true;
        });
    }

    displaySearchResults(results) {
        const wrapper = document.getElementById('search-input-wrapper');
        const resultsContainer = document.getElementById('search-results');
        const countElement = document.getElementById('search-results-count');
        const contentElement = document.getElementById('search-results-content');

        countElement.textContent = `${results.length} 个结果`;

        if (results.length === 0) {
            contentElement.innerHTML = '<div class="search-empty">未找到相关结果</div>';
        } else {
            contentElement.innerHTML = results.map(item =>
                `<div class="search-result-item">${item.title || item.content}</div>`
            ).join('');
        }

        wrapper.classList.add('has-results');
        resultsContainer.classList.add('show');
    }

    showSearchHistory() {
        if (this.searchHistory.length === 0) return;

        const contentElement = document.getElementById('search-results-content');
        contentElement.innerHTML = `
            <div class="search-history">
                <div class="search-history-header">
                    <span class="search-history-title">搜索历史</span>
                    <a href="#" class="search-history-clear" onclick="window.searchSystem.clearSearchHistory()">清空</a>
                </div>
                <div class="search-history-items">
                    ${this.searchHistory.slice(0, 5).map(item => `
                        <div class="search-history-item" onclick="window.searchSystem.selectHistoryItem('${item.query}')">
                            <i class="search-history-icon bi bi-clock"></i>
                            <span class="search-history-text">${item.query}</span>
                            <span class="search-history-time">${this.formatTime(item.timestamp)}</span>
                        </div>
                    `).join('')}
                </div>
            </div>
        `;

        document.getElementById('search-results').classList.add('show');
    }

    hideSearchResults() {
        const wrapper = document.getElementById('search-input-wrapper');
        const resultsContainer = document.getElementById('search-results');

        wrapper.classList.remove('has-results');
        resultsContainer.classList.remove('show');
    }

    toggleFiltersPanel() {
        this.filtersPanel.classList.toggle('show');
    }

    hideFiltersPanel() {
        this.filtersPanel.classList.remove('show');
    }

    handleFilterClick(option) {
        const filterType = option.dataset.filter;
        const filterValue = option.dataset.value;
        const checkbox = option.querySelector('.filter-checkbox');

        if (filterType === 'timeRange') {
            // 单选
            this.filtersPanel.querySelectorAll(`[data-filter="timeRange"] .filter-checkbox`).forEach(cb => {
                cb.classList.remove('checked');
            });
            checkbox.classList.add('checked');
            this.currentFilters.timeRange = filterValue;
        } else {
            // 多选
            checkbox.classList.toggle('checked');
            if (checkbox.classList.contains('checked')) {
                if (!this.currentFilters[filterType].includes(filterValue)) {
                    this.currentFilters[filterType].push(filterValue);
                }
            } else {
                this.currentFilters[filterType] = this.currentFilters[filterType].filter(v => v !== filterValue);
            }
        }

        // 重新执行搜索
        if (this.currentQuery) {
            this.performSearch();
        }
    }

    handleKeyNavigation(e) {
        const results = document.querySelectorAll('.search-result-item');

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            this.selectedIndex = Math.min(this.selectedIndex + 1, results.length - 1);
            this.updateSelection(results);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            this.selectedIndex = Math.max(this.selectedIndex - 1, -1);
            this.updateSelection(results);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (this.selectedIndex >= 0 && results[this.selectedIndex]) {
                results[this.selectedIndex].click();
            }
        } else if (e.key === 'Escape') {
            this.hideSearchResults();
            this.hideFiltersPanel();
        }
    }

    updateSelection(results) {
        results.forEach((item, index) => {
            item.classList.toggle('selected', index === this.selectedIndex);
        });

        if (this.selectedIndex >= 0 && results[this.selectedIndex]) {
            results[this.selectedIndex].scrollIntoView({ block: 'nearest' });
        }
    }

    clearSearch() {
        this.searchInput.value = '';
        this.currentQuery = '';
        this.hideSearchResults();
        document.getElementById('search-clear-btn').style.display = 'none';
    }

    focusSearch() {
        this.searchInput.focus();
        this.searchInput.select();
    }

    addToSearchHistory(query) {
        if (!query || query.length < 2) return;

        // 移除重复项
        this.searchHistory = this.searchHistory.filter(item => item.query !== query);

        // 添加到开头
        this.searchHistory.unshift({
            query: query,
            timestamp: new Date()
        });

        // 限制历史记录数量
        this.searchHistory = this.searchHistory.slice(0, 20);

        // 保存到本地存储
        localStorage.setItem('searchHistory', JSON.stringify(this.searchHistory));
    }

    loadSearchHistory() {
        const saved = localStorage.getItem('searchHistory');
        if (saved) {
            this.searchHistory = JSON.parse(saved);
        }
    }

    clearSearchHistory() {
        this.searchHistory = [];
        localStorage.removeItem('searchHistory');
        this.hideSearchResults();
    }

    selectHistoryItem(query) {
        this.searchInput.value = query;
        this.handleSearchInput(query);
    }

    clearFilters() {
        this.currentFilters = {
            type: [],
            timeRange: 'all',
            tags: [],
            status: []
        };

        // 重置UI
        this.filtersPanel.querySelectorAll('.filter-checkbox').forEach(cb => {
            cb.classList.remove('checked');
        });
        this.filtersPanel.querySelector('[data-value="all"] .filter-checkbox').classList.add('checked');

        if (this.currentQuery) {
            this.performSearch();
        }
    }

    exportResults() {
        if (window.animationController) {
            window.animationController.showNotification('导出功能开发中...', 'info');
        }
    }

    saveSearch() {
        if (window.animationController) {
            window.animationController.showNotification('保存功能开发中...', 'info');
        }
    }

    formatTime(timestamp) {
        if (!timestamp) return '';

        const now = new Date();
        const date = new Date(timestamp);
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));

        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;

        return date.toLocaleDateString('zh-CN');
    }
}

// 全局搜索系统实例
window.searchSystem = new SearchSystem();
