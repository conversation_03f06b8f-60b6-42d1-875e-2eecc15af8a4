package metrics

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	config  *MonitorConfig
	logger  *logrus.Logger
	metrics *SystemMetrics
	mutex   sync.RWMutex
	
	// 监控控制
	stopChan chan struct{}
	running  bool
}

// MonitorConfig 监控配置
type MonitorConfig struct {
	CollectionInterval time.Duration `json:"collection_interval"`
	RetentionPeriod    time.Duration `json:"retention_period"`
	EnableCPUMonitor   bool          `json:"enable_cpu_monitor"`
	EnableMemMonitor   bool          `json:"enable_mem_monitor"`
	EnableGCMonitor    bool          `json:"enable_gc_monitor"`
	EnableGoRoutines   bool          `json:"enable_goroutines"`
	AlertThresholds    *AlertThresholds `json:"alert_thresholds"`
}

// AlertThresholds 告警阈值
type AlertThresholds struct {
	CPUUsagePercent    float64 `json:"cpu_usage_percent"`
	MemoryUsageMB      int64   `json:"memory_usage_mb"`
	GoroutineCount     int     `json:"goroutine_count"`
	GCPauseTimeMS      float64 `json:"gc_pause_time_ms"`
	ResponseTimeMS     float64 `json:"response_time_ms"`
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	// CPU指标
	CPUUsage       float64   `json:"cpu_usage"`
	CPUCores       int       `json:"cpu_cores"`
	
	// 内存指标
	MemoryUsage    int64     `json:"memory_usage"`
	MemoryTotal    int64     `json:"memory_total"`
	MemoryPercent  float64   `json:"memory_percent"`
	
	// GC指标
	GCCount        uint32    `json:"gc_count"`
	GCPauseTime    float64   `json:"gc_pause_time_ms"`
	GCTotalPause   float64   `json:"gc_total_pause_ms"`
	
	// Goroutine指标
	GoroutineCount int       `json:"goroutine_count"`
	
	// 系统指标
	Uptime         time.Duration `json:"uptime"`
	StartTime      time.Time     `json:"start_time"`
	LastUpdated    time.Time     `json:"last_updated"`
	
	// 历史数据
	History        []*MetricSnapshot `json:"history"`
}

// MetricSnapshot 指标快照
type MetricSnapshot struct {
	Timestamp      time.Time `json:"timestamp"`
	CPUUsage       float64   `json:"cpu_usage"`
	MemoryUsage    int64     `json:"memory_usage"`
	MemoryPercent  float64   `json:"memory_percent"`
	GoroutineCount int       `json:"goroutine_count"`
	GCPauseTime    float64   `json:"gc_pause_time_ms"`
}

// RequestMetrics 请求指标
type RequestMetrics struct {
	TotalRequests     int64                    `json:"total_requests"`
	SuccessRequests   int64                    `json:"success_requests"`
	ErrorRequests     int64                    `json:"error_requests"`
	AverageLatency    float64                  `json:"average_latency_ms"`
	P95Latency        float64                  `json:"p95_latency_ms"`
	P99Latency        float64                  `json:"p99_latency_ms"`
	RequestsPerSecond float64                  `json:"requests_per_second"`
	LatencyHistory    []float64                `json:"latency_history"`
	RequestsByPath    map[string]int64         `json:"requests_by_path"`
	ErrorsByType      map[string]int64         `json:"errors_by_type"`
	LastUpdated       time.Time                `json:"last_updated"`
	mutex             sync.RWMutex             `json:"-"`
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor(config *MonitorConfig, logger *logrus.Logger) *PerformanceMonitor {
	if config == nil {
		config = &MonitorConfig{
			CollectionInterval: 10 * time.Second,
			RetentionPeriod:    24 * time.Hour,
			EnableCPUMonitor:   true,
			EnableMemMonitor:   true,
			EnableGCMonitor:    true,
			EnableGoRoutines:   true,
			AlertThresholds: &AlertThresholds{
				CPUUsagePercent: 80.0,
				MemoryUsageMB:   1024,
				GoroutineCount:  10000,
				GCPauseTimeMS:   100.0,
				ResponseTimeMS:  1000.0,
			},
		}
	}

	pm := &PerformanceMonitor{
		config:  config,
		logger:  logger,
		metrics: &SystemMetrics{
			StartTime:   time.Now(),
			LastUpdated: time.Now(),
			History:     make([]*MetricSnapshot, 0),
		},
		stopChan: make(chan struct{}),
	}

	return pm
}

// Start 启动性能监控
func (pm *PerformanceMonitor) Start() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if pm.running {
		return
	}

	pm.running = true
	go pm.collectMetrics()

	pm.logger.WithField("interval", pm.config.CollectionInterval).Info("Performance monitor started")
}

// Stop 停止性能监控
func (pm *PerformanceMonitor) Stop() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if !pm.running {
		return
	}

	pm.running = false
	close(pm.stopChan)

	pm.logger.Info("Performance monitor stopped")
}

// collectMetrics 收集指标
func (pm *PerformanceMonitor) collectMetrics() {
	ticker := time.NewTicker(pm.config.CollectionInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			pm.updateMetrics()
		case <-pm.stopChan:
			return
		}
	}
}

// updateMetrics 更新指标
func (pm *PerformanceMonitor) updateMetrics() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	var memStats runtime.MemStats
	runtime.ReadMemStats(&memStats)

	// 更新系统指标
	pm.metrics.CPUCores = runtime.NumCPU()
	pm.metrics.MemoryUsage = int64(memStats.Alloc)
	pm.metrics.MemoryTotal = int64(memStats.Sys)
	pm.metrics.MemoryPercent = float64(memStats.Alloc) / float64(memStats.Sys) * 100
	pm.metrics.GCCount = memStats.NumGC
	pm.metrics.GoroutineCount = runtime.NumGoroutine()
	pm.metrics.Uptime = time.Since(pm.metrics.StartTime)
	pm.metrics.LastUpdated = time.Now()

	// 计算GC暂停时间
	if len(memStats.PauseNs) > 0 {
		pm.metrics.GCPauseTime = float64(memStats.PauseNs[(memStats.NumGC+255)%256]) / 1e6 // 转换为毫秒
	}

	// 计算总GC暂停时间
	pm.metrics.GCTotalPause = float64(memStats.PauseTotalNs) / 1e6

	// 创建快照
	snapshot := &MetricSnapshot{
		Timestamp:      time.Now(),
		CPUUsage:       pm.metrics.CPUUsage,
		MemoryUsage:    pm.metrics.MemoryUsage,
		MemoryPercent:  pm.metrics.MemoryPercent,
		GoroutineCount: pm.metrics.GoroutineCount,
		GCPauseTime:    pm.metrics.GCPauseTime,
	}

	// 添加到历史记录
	pm.metrics.History = append(pm.metrics.History, snapshot)

	// 清理过期历史数据
	pm.cleanupHistory()

	// 检查告警阈值
	pm.checkAlerts()

	pm.logger.WithFields(logrus.Fields{
		"memory_usage_mb": pm.metrics.MemoryUsage / 1024 / 1024,
		"memory_percent":  pm.metrics.MemoryPercent,
		"goroutines":      pm.metrics.GoroutineCount,
		"gc_pause_ms":     pm.metrics.GCPauseTime,
	}).Debug("Performance metrics updated")
}

// cleanupHistory 清理历史数据
func (pm *PerformanceMonitor) cleanupHistory() {
	cutoff := time.Now().Add(-pm.config.RetentionPeriod)
	
	validHistory := make([]*MetricSnapshot, 0)
	for _, snapshot := range pm.metrics.History {
		if snapshot.Timestamp.After(cutoff) {
			validHistory = append(validHistory, snapshot)
		}
	}
	
	pm.metrics.History = validHistory
}

// checkAlerts 检查告警
func (pm *PerformanceMonitor) checkAlerts() {
	thresholds := pm.config.AlertThresholds
	if thresholds == nil {
		return
	}

	// 检查内存使用率
	if pm.metrics.MemoryUsage/1024/1024 > thresholds.MemoryUsageMB {
		pm.logger.WithFields(logrus.Fields{
			"current_mb":   pm.metrics.MemoryUsage / 1024 / 1024,
			"threshold_mb": thresholds.MemoryUsageMB,
		}).Warn("Memory usage alert triggered")
	}

	// 检查Goroutine数量
	if pm.metrics.GoroutineCount > thresholds.GoroutineCount {
		pm.logger.WithFields(logrus.Fields{
			"current_count":   pm.metrics.GoroutineCount,
			"threshold_count": thresholds.GoroutineCount,
		}).Warn("Goroutine count alert triggered")
	}

	// 检查GC暂停时间
	if pm.metrics.GCPauseTime > thresholds.GCPauseTimeMS {
		pm.logger.WithFields(logrus.Fields{
			"current_ms":   pm.metrics.GCPauseTime,
			"threshold_ms": thresholds.GCPauseTimeMS,
		}).Warn("GC pause time alert triggered")
	}
}

// GetMetrics 获取系统指标
func (pm *PerformanceMonitor) GetMetrics() *SystemMetrics {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 创建副本
	metrics := &SystemMetrics{
		CPUUsage:       pm.metrics.CPUUsage,
		CPUCores:       pm.metrics.CPUCores,
		MemoryUsage:    pm.metrics.MemoryUsage,
		MemoryTotal:    pm.metrics.MemoryTotal,
		MemoryPercent:  pm.metrics.MemoryPercent,
		GCCount:        pm.metrics.GCCount,
		GCPauseTime:    pm.metrics.GCPauseTime,
		GCTotalPause:   pm.metrics.GCTotalPause,
		GoroutineCount: pm.metrics.GoroutineCount,
		Uptime:         pm.metrics.Uptime,
		StartTime:      pm.metrics.StartTime,
		LastUpdated:    pm.metrics.LastUpdated,
		History:        make([]*MetricSnapshot, len(pm.metrics.History)),
	}

	// 复制历史数据
	copy(metrics.History, pm.metrics.History)

	return metrics
}

// NewRequestMetrics 创建请求指标
func NewRequestMetrics() *RequestMetrics {
	return &RequestMetrics{
		LatencyHistory:  make([]float64, 0, 1000), // 保留最近1000个请求的延迟
		RequestsByPath:  make(map[string]int64),
		ErrorsByType:    make(map[string]int64),
		LastUpdated:     time.Now(),
	}
}

// RecordRequest 记录请求
func (rm *RequestMetrics) RecordRequest(path string, latency time.Duration, success bool, errorType string) {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	rm.TotalRequests++
	latencyMs := float64(latency.Milliseconds())

	if success {
		rm.SuccessRequests++
	} else {
		rm.ErrorRequests++
		if errorType != "" {
			rm.ErrorsByType[errorType]++
		}
	}

	// 更新路径统计
	rm.RequestsByPath[path]++

	// 更新延迟历史
	rm.LatencyHistory = append(rm.LatencyHistory, latencyMs)
	if len(rm.LatencyHistory) > 1000 {
		rm.LatencyHistory = rm.LatencyHistory[1:]
	}

	// 计算平均延迟
	if rm.TotalRequests > 0 {
		totalLatency := 0.0
		for _, lat := range rm.LatencyHistory {
			totalLatency += lat
		}
		rm.AverageLatency = totalLatency / float64(len(rm.LatencyHistory))
	}

	// 计算P95和P99延迟
	rm.calculatePercentiles()

	rm.LastUpdated = time.Now()
}

// calculatePercentiles 计算百分位数
func (rm *RequestMetrics) calculatePercentiles() {
	if len(rm.LatencyHistory) == 0 {
		return
	}

	// 简化的百分位数计算
	sorted := make([]float64, len(rm.LatencyHistory))
	copy(sorted, rm.LatencyHistory)

	// 简单排序（实际应该使用更高效的算法）
	for i := 0; i < len(sorted); i++ {
		for j := i + 1; j < len(sorted); j++ {
			if sorted[i] > sorted[j] {
				sorted[i], sorted[j] = sorted[j], sorted[i]
			}
		}
	}

	// 计算P95
	p95Index := int(float64(len(sorted)) * 0.95)
	if p95Index < len(sorted) {
		rm.P95Latency = sorted[p95Index]
	}

	// 计算P99
	p99Index := int(float64(len(sorted)) * 0.99)
	if p99Index < len(sorted) {
		rm.P99Latency = sorted[p99Index]
	}
}

// GetRequestMetrics 获取请求指标
func (rm *RequestMetrics) GetRequestMetrics() *RequestMetrics {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	// 创建副本
	metrics := &RequestMetrics{
		TotalRequests:     rm.TotalRequests,
		SuccessRequests:   rm.SuccessRequests,
		ErrorRequests:     rm.ErrorRequests,
		AverageLatency:    rm.AverageLatency,
		P95Latency:        rm.P95Latency,
		P99Latency:        rm.P99Latency,
		RequestsPerSecond: rm.RequestsPerSecond,
		LatencyHistory:    make([]float64, len(rm.LatencyHistory)),
		RequestsByPath:    make(map[string]int64),
		ErrorsByType:      make(map[string]int64),
		LastUpdated:       rm.LastUpdated,
	}

	// 复制数据
	copy(metrics.LatencyHistory, rm.LatencyHistory)
	for k, v := range rm.RequestsByPath {
		metrics.RequestsByPath[k] = v
	}
	for k, v := range rm.ErrorsByType {
		metrics.ErrorsByType[k] = v
	}

	return metrics
}
