package model

import (
	"time"

	"gorm.io/gorm"
)

// OperationLog 操作日志模型
type OperationLog struct {
	ID               int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	OperationType    string         `json:"operation_type" gorm:"size:50;not null"`
	Command          string         `json:"command" gorm:"type:text"`
	Result           string         `json:"result" gorm:"type:text"`
	Status           string         `json:"status" gorm:"size:20;not null"`
	ExecutedAt       time.Time      `json:"executed_at" gorm:"not null"`
	DurationMs       int            `json:"duration_ms"`
	HostID           *int64         `json:"host_id"`
	UserID           int64          `json:"user_id" gorm:"not null"`
	SessionID        string         `json:"session_id" gorm:"size:36"`
	ClientIP         string         `json:"client_ip" gorm:"size:45"`
	UserAgent        string         `json:"user_agent" gorm:"type:text"`
	ErrorMessage     string         `json:"error_message" gorm:"type:text"`
	ExitCode         *int           `json:"exit_code"`
	WorkingDirectory string         `json:"working_directory" gorm:"size:255"`
	EnvironmentVars  string         `json:"-" gorm:"type:text"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Host *Host `json:"host,omitempty" gorm:"foreignKey:HostID"`
	User User  `json:"user" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (OperationLog) TableName() string {
	return "operations_log"
}

// BeforeCreate GORM钩子：创建前
func (ol *OperationLog) BeforeCreate(tx *gorm.DB) error {
	if ol.ExecutedAt.IsZero() {
		ol.ExecutedAt = time.Now()
	}
	return nil
}

// SystemConfig 系统配置模型
type SystemConfig struct {
	ID             int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	ConfigKey      string         `json:"config_key" gorm:"uniqueIndex;size:100;not null"`
	ConfigValue    string         `json:"config_value" gorm:"type:text;not null"`
	ConfigType     string         `json:"config_type" gorm:"size:20;not null;default:string"`
	Description    string         `json:"description" gorm:"type:text"`
	IsSensitive    bool           `json:"is_sensitive" gorm:"default:false"`
	Category       string         `json:"category" gorm:"size:50;default:general"`
	ValidationRule string         `json:"validation_rule" gorm:"type:text"`
	DefaultValue   string         `json:"default_value" gorm:"type:text"`
	UpdatedAt      time.Time      `json:"updated_at" gorm:"not null"`
	UpdatedBy      *int64         `json:"updated_by"`
	DeletedAt      gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	UpdatedByUser *User `json:"updated_by_user,omitempty" gorm:"foreignKey:UpdatedBy"`
}

// TableName 指定表名
func (SystemConfig) TableName() string {
	return "system_config"
}

// BeforeCreate GORM钩子：创建前
func (sc *SystemConfig) BeforeCreate(tx *gorm.DB) error {
	sc.UpdatedAt = time.Now()
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (sc *SystemConfig) BeforeUpdate(tx *gorm.DB) error {
	sc.UpdatedAt = time.Now()
	return nil
}

// DatabaseVersion 数据库版本模型
type DatabaseVersion struct {
	ID        int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	Version   int       `json:"version" gorm:"not null"`
	AppliedAt time.Time `json:"applied_at" gorm:"not null"`
}

// TableName 指定表名
func (DatabaseVersion) TableName() string {
	return "database_versions"
}

// OperationType 操作类型枚举
type OperationType string

const (
	OperationTypeSSHCommand   OperationType = "ssh_command"
	OperationTypeAPICall      OperationType = "api_call"
	OperationTypeSystemAction OperationType = "system_action"
	OperationTypeLogin        OperationType = "login"
	OperationTypeLogout       OperationType = "logout"
	OperationTypeConfigChange OperationType = "config_change"
)

// OperationStatus 操作状态枚举
type OperationStatus string

const (
	OperationStatusSuccess OperationStatus = "success"
	OperationStatusFailed  OperationStatus = "failed"
	OperationStatusTimeout OperationStatus = "timeout"
)

// ConfigType 配置类型枚举
type ConfigType string

const (
	ConfigTypeString  ConfigType = "string"
	ConfigTypeNumber  ConfigType = "number"
	ConfigTypeBoolean ConfigType = "boolean"
	ConfigTypeJSON    ConfigType = "json"
)

// IsValidOperationType 检查操作类型是否有效
func IsValidOperationType(opType string) bool {
	switch OperationType(opType) {
	case OperationTypeSSHCommand, OperationTypeAPICall, OperationTypeSystemAction,
		OperationTypeLogin, OperationTypeLogout, OperationTypeConfigChange:
		return true
	default:
		return false
	}
}

// IsValidOperationStatus 检查操作状态是否有效
func IsValidOperationStatus(status string) bool {
	switch OperationStatus(status) {
	case OperationStatusSuccess, OperationStatusFailed, OperationStatusTimeout:
		return true
	default:
		return false
	}
}

// IsValidConfigType 检查配置类型是否有效
func IsValidConfigType(configType string) bool {
	switch ConfigType(configType) {
	case ConfigTypeString, ConfigTypeNumber, ConfigTypeBoolean, ConfigTypeJSON:
		return true
	default:
		return false
	}
}

// OperationLogResponse 操作日志响应
type OperationLogResponse struct {
	ID               int64         `json:"id"`
	OperationType    string        `json:"operation_type"`
	Command          string        `json:"command"`
	Result           string        `json:"result"`
	Status           string        `json:"status"`
	ExecutedAt       time.Time     `json:"executed_at"`
	DurationMs       int           `json:"duration_ms"`
	HostID           *int64        `json:"host_id"`
	UserID           int64         `json:"user_id"`
	SessionID        string        `json:"session_id"`
	ClientIP         string        `json:"client_ip"`
	UserAgent        string        `json:"user_agent"`
	ErrorMessage     string        `json:"error_message"`
	ExitCode         *int          `json:"exit_code"`
	WorkingDirectory string        `json:"working_directory"`
	Host             *HostResponse `json:"host,omitempty"`
	User             *UserResponse `json:"user,omitempty"`
}

// ToResponse 转换为响应格式
func (ol *OperationLog) ToResponse() *OperationLogResponse {
	resp := &OperationLogResponse{
		ID:               ol.ID,
		OperationType:    ol.OperationType,
		Command:          ol.Command,
		Result:           ol.Result,
		Status:           ol.Status,
		ExecutedAt:       ol.ExecutedAt,
		DurationMs:       ol.DurationMs,
		HostID:           ol.HostID,
		UserID:           ol.UserID,
		SessionID:        ol.SessionID,
		ClientIP:         ol.ClientIP,
		UserAgent:        ol.UserAgent,
		ErrorMessage:     ol.ErrorMessage,
		ExitCode:         ol.ExitCode,
		WorkingDirectory: ol.WorkingDirectory,
		User:             ol.User.ToResponse(),
	}

	if ol.Host != nil {
		resp.Host = ol.Host.ToResponse()
	}

	return resp
}

// SystemConfigResponse 系统配置响应
type SystemConfigResponse struct {
	ID             int64         `json:"id"`
	ConfigKey      string        `json:"config_key"`
	ConfigValue    string        `json:"config_value"`
	ConfigType     string        `json:"config_type"`
	Description    string        `json:"description"`
	IsSensitive    bool          `json:"is_sensitive"`
	Category       string        `json:"category"`
	ValidationRule string        `json:"validation_rule"`
	DefaultValue   string        `json:"default_value"`
	UpdatedAt      time.Time     `json:"updated_at"`
	UpdatedBy      *int64        `json:"updated_by"`
	UpdatedByUser  *UserResponse `json:"updated_by_user,omitempty"`
}

// ToResponse 转换为响应格式
func (sc *SystemConfig) ToResponse() *SystemConfigResponse {
	resp := &SystemConfigResponse{
		ID:             sc.ID,
		ConfigKey:      sc.ConfigKey,
		ConfigValue:    sc.ConfigValue,
		ConfigType:     sc.ConfigType,
		Description:    sc.Description,
		IsSensitive:    sc.IsSensitive,
		Category:       sc.Category,
		ValidationRule: sc.ValidationRule,
		DefaultValue:   sc.DefaultValue,
		UpdatedAt:      sc.UpdatedAt,
		UpdatedBy:      sc.UpdatedBy,
	}

	// 敏感配置不返回值
	if sc.IsSensitive {
		resp.ConfigValue = "***"
	}

	if sc.UpdatedByUser != nil {
		resp.UpdatedByUser = sc.UpdatedByUser.ToResponse()
	}

	return resp
}

// OperationLogListQuery 操作日志列表查询参数
type OperationLogListQuery struct {
	Page          int    `form:"page,default=1" binding:"min=1"`
	Limit         int    `form:"limit,default=20" binding:"min=1,max=100"`
	OperationType string `form:"operation_type" binding:"omitempty"`
	Status        string `form:"status" binding:"omitempty,oneof=success failed timeout"`
	HostID        *int64 `form:"host_id" binding:"omitempty"`
	UserID        *int64 `form:"user_id" binding:"omitempty"`
	TimeRange     string `form:"time_range" binding:"omitempty,oneof=1h 6h 24h 7d 30d"`
	Search        string `form:"search" binding:"omitempty,max=100"`
}

// OperationLogListResponse 操作日志列表响应
type OperationLogListResponse struct {
	Logs       []*OperationLogResponse `json:"logs"`
	Pagination *Pagination             `json:"pagination"`
}

// SystemConfigListQuery 系统配置列表查询参数
type SystemConfigListQuery struct {
	Page     int    `form:"page,default=1" binding:"min=1"`
	Limit    int    `form:"limit,default=20" binding:"min=1,max=100"`
	Category string `form:"category" binding:"omitempty,max=50"`
	Search   string `form:"search" binding:"omitempty,max=100"`
}

// SystemConfigListResponse 系统配置列表响应
type SystemConfigListResponse struct {
	Configs    []*SystemConfigResponse `json:"configs"`
	Pagination *Pagination             `json:"pagination"`
}

// SystemConfigUpdateRequest 系统配置更新请求
type SystemConfigUpdateRequest struct {
	ConfigValue string `json:"config_value" binding:"required"`
}

// SecurityLog 安全日志模型
type SecurityLog struct {
	ID          int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID      int64          `json:"user_id" gorm:"not null"`
	Action      string         `json:"action" gorm:"size:50;not null"`
	Command     string         `json:"command" gorm:"type:text"`
	HostID      *int64         `json:"host_id"`
	SessionID   string         `json:"session_id" gorm:"size:100"`
	ClientIP    string         `json:"client_ip" gorm:"size:45"`
	UserAgent   string         `json:"user_agent" gorm:"type:text"`
	RiskLevel   string         `json:"risk_level" gorm:"size:20;not null"`
	RiskScore   float64        `json:"risk_score"`
	Allowed     bool           `json:"allowed" gorm:"not null"`
	Reason      string         `json:"reason" gorm:"type:text"`
	ValidatedAt time.Time      `json:"validated_at" gorm:"not null"`
	CreatedAt   time.Time      `json:"created_at" gorm:"autoCreateTime"`
	UpdatedAt   time.Time      `json:"updated_at" gorm:"autoUpdateTime"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Host *Host `json:"host,omitempty" gorm:"foreignKey:HostID"`
	User User  `json:"user" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (SecurityLog) TableName() string {
	return "security_logs"
}

// BeforeCreate GORM钩子：创建前
func (sl *SecurityLog) BeforeCreate(tx *gorm.DB) error {
	if sl.ValidatedAt.IsZero() {
		sl.ValidatedAt = time.Now()
	}
	return nil
}

// SecurityLogResponse 安全日志响应
type SecurityLogResponse struct {
	ID          int64         `json:"id"`
	UserID      int64         `json:"user_id"`
	Action      string        `json:"action"`
	Command     string        `json:"command"`
	HostID      *int64        `json:"host_id"`
	SessionID   string        `json:"session_id"`
	ClientIP    string        `json:"client_ip"`
	UserAgent   string        `json:"user_agent"`
	RiskLevel   string        `json:"risk_level"`
	RiskScore   float64       `json:"risk_score"`
	Allowed     bool          `json:"allowed"`
	Reason      string        `json:"reason"`
	ValidatedAt time.Time     `json:"validated_at"`
	CreatedAt   time.Time     `json:"created_at"`
	Host        *HostResponse `json:"host,omitempty"`
	User        *UserResponse `json:"user,omitempty"`
}

// ToResponse 转换为响应格式
func (sl *SecurityLog) ToResponse() *SecurityLogResponse {
	resp := &SecurityLogResponse{
		ID:          sl.ID,
		UserID:      sl.UserID,
		Action:      sl.Action,
		Command:     sl.Command,
		HostID:      sl.HostID,
		SessionID:   sl.SessionID,
		ClientIP:    sl.ClientIP,
		UserAgent:   sl.UserAgent,
		RiskLevel:   sl.RiskLevel,
		RiskScore:   sl.RiskScore,
		Allowed:     sl.Allowed,
		Reason:      sl.Reason,
		ValidatedAt: sl.ValidatedAt,
		CreatedAt:   sl.CreatedAt,
		User:        sl.User.ToResponse(),
	}

	if sl.Host != nil {
		resp.Host = sl.Host.ToResponse()
	}

	return resp
}
