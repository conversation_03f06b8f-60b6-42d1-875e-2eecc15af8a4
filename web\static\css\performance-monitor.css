/* ========================================
   性能监控与优化系统样式
   提供完整的前端性能监控功能
   ======================================== */

/* 性能监控面板 */
.performance-monitor {
  position: fixed;
  bottom: var(--space-4);
  left: var(--space-4);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-3);
  min-width: 280px;
  z-index: 9996;
  opacity: 0;
  transform: translateY(20px);
  transition: all var(--duration-normal) var(--easing-ease);
  pointer-events: none;
}

.performance-monitor.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.performance-monitor.collapsed {
  padding: var(--space-2);
  min-width: auto;
}

.performance-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-3);
}

.performance-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.performance-icon {
  font-size: 16px;
  color: var(--color-primary);
}

.performance-actions {
  display: flex;
  gap: var(--space-1);
}

.performance-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  font-size: 12px;
}

.performance-action-btn:hover {
  background: var(--bg-hover);
  color: var(--text-secondary);
}

.performance-action-btn.active {
  background: var(--color-primary);
  color: white;
}

/* 性能指标 */
.performance-metrics {
  display: grid;
  gap: var(--space-2);
}

.performance-metric {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-2);
  background: var(--bg-secondary);
  border-radius: var(--radius-md);
  border: 1px solid var(--border-primary);
}

.metric-info {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.metric-label {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  font-weight: var(--font-weight-medium);
}

.metric-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.metric-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  flex-shrink: 0;
}

.metric-status.good {
  background: var(--color-success);
}

.metric-status.warning {
  background: var(--color-warning);
}

.metric-status.poor {
  background: var(--color-error);
}

/* 性能图表 */
.performance-chart {
  height: 60px;
  margin-top: var(--space-2);
  position: relative;
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  overflow: hidden;
}

.chart-line {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--color-primary);
  transform-origin: left bottom;
  transition: all var(--duration-fast) var(--easing-ease);
}

.chart-bars {
  display: flex;
  align-items: end;
  height: 100%;
  gap: 1px;
  padding: var(--space-1);
}

.chart-bar {
  flex: 1;
  background: var(--color-primary);
  border-radius: 1px 1px 0 0;
  min-height: 2px;
  transition: all var(--duration-fast) var(--easing-ease);
  opacity: 0.7;
}

.chart-bar:hover {
  opacity: 1;
}

/* 性能警告 */
.performance-warning {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2);
  background: var(--color-warning);
  color: white;
  border-radius: var(--radius-md);
  margin-top: var(--space-2);
  font-size: var(--font-size-xs);
}

.performance-warning-icon {
  font-size: 14px;
}

/* 性能建议 */
.performance-suggestions {
  margin-top: var(--space-3);
  padding-top: var(--space-3);
  border-top: 1px solid var(--border-primary);
}

.suggestion-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  padding: var(--space-2);
  border-radius: var(--radius-md);
  margin-bottom: var(--space-2);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
}

.suggestion-icon {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  flex-shrink: 0;
  margin-top: 2px;
}

.suggestion-icon.tip {
  background: var(--color-info);
  color: white;
}

.suggestion-icon.warning {
  background: var(--color-warning);
  color: white;
}

.suggestion-content {
  flex: 1;
}

.suggestion-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.suggestion-description {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  line-height: 1.4;
}

/* 性能详情面板 */
.performance-details {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  width: 90%;
  max-width: 800px;
  max-height: 80vh;
  overflow: hidden;
  z-index: 10000;
  display: none;
}

.performance-details.show {
  display: block;
  animation: modalEnter var(--duration-normal) var(--easing-ease);
}

.performance-details-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.performance-details-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.performance-details-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.performance-details-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.performance-details-content {
  padding: var(--space-4);
  max-height: calc(80vh - 80px);
  overflow-y: auto;
}

.performance-tabs {
  display: flex;
  gap: var(--space-1);
  margin-bottom: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.performance-tab {
  padding: var(--space-2) var(--space-3);
  border: none;
  background: none;
  color: var(--text-secondary);
  border-radius: var(--radius-md) var(--radius-md) 0 0;
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  font-size: var(--font-size-sm);
}

.performance-tab:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.performance-tab.active {
  background: var(--color-primary);
  color: white;
}

.performance-tab-content {
  display: none;
}

.performance-tab-content.active {
  display: block;
}

/* 性能数据表格 */
.performance-table {
  width: 100%;
  border-collapse: collapse;
  margin-top: var(--space-3);
}

.performance-table th,
.performance-table td {
  padding: var(--space-2) var(--space-3);
  text-align: left;
  border-bottom: 1px solid var(--border-primary);
}

.performance-table th {
  background: var(--bg-secondary);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.performance-table td {
  font-size: var(--font-size-sm);
  color: var(--text-primary);
}

.performance-table tr:hover {
  background: var(--bg-hover);
}

/* 性能分数 */
.performance-score {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: white;
  position: relative;
  margin: 0 auto var(--space-3);
}

.performance-score.excellent {
  background: conic-gradient(var(--color-success) 0deg, var(--color-success) calc(var(--score) * 3.6deg), var(--bg-tertiary) calc(var(--score) * 3.6deg));
}

.performance-score.good {
  background: conic-gradient(var(--color-info) 0deg, var(--color-info) calc(var(--score) * 3.6deg), var(--bg-tertiary) calc(var(--score) * 3.6deg));
}

.performance-score.needs-improvement {
  background: conic-gradient(var(--color-warning) 0deg, var(--color-warning) calc(var(--score) * 3.6deg), var(--bg-tertiary) calc(var(--score) * 3.6deg));
}

.performance-score.poor {
  background: conic-gradient(var(--color-error) 0deg, var(--color-error) calc(var(--score) * 3.6deg), var(--bg-tertiary) calc(var(--score) * 3.6deg));
}

.performance-score::before {
  content: '';
  position: absolute;
  top: 6px;
  left: 6px;
  right: 6px;
  bottom: 6px;
  background: var(--bg-primary);
  border-radius: 50%;
}

.performance-score-value {
  position: relative;
  z-index: 1;
  color: var(--text-primary);
}

/* 懒加载指示器 */
.lazy-loading {
  position: relative;
  overflow: hidden;
}

.lazy-loading::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { left: -100%; }
  100% { left: 100%; }
}

/* 响应式调整 */
@media (max-width: 768px) {
  .performance-monitor {
    left: var(--space-2);
    right: var(--space-2);
    bottom: var(--space-2);
    min-width: auto;
  }
  
  .performance-details {
    width: 95%;
    max-height: 90vh;
  }
  
  .performance-metrics {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .performance-monitor {
    padding: var(--space-2);
  }
  
  .performance-metric {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-1);
  }
  
  .performance-details-content {
    padding: var(--space-3);
  }
  
  .performance-tabs {
    flex-wrap: wrap;
  }
  
  .performance-tab {
    flex: 1;
    min-width: 80px;
  }
}

/* 打印样式 */
@media print {
  .performance-monitor,
  .performance-details {
    display: none !important;
  }
}
