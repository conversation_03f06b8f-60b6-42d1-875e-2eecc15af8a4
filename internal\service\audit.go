package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// AuditService 审计服务
type AuditService struct {
	db         *gorm.DB
	logger     *logrus.Logger
	config     *AuditConfig
	processors map[string]AuditProcessor
	filters    []AuditFilter
	exporters  map[string]AuditExporter
}

// AuditConfig 审计配置
type AuditConfig struct {
	Enabled          bool          `json:"enabled"`
	RetentionDays    int           `json:"retention_days"`
	BatchSize        int           `json:"batch_size"`
	FlushInterval    time.Duration `json:"flush_interval"`
	ComplianceMode   bool          `json:"compliance_mode"`
	EncryptSensitive bool          `json:"encrypt_sensitive"`
	ExportFormats    []string      `json:"export_formats"`
}

// AuditEvent 审计事件
type AuditEvent struct {
	ID           string          `json:"id" gorm:"primaryKey"`
	Timestamp    time.Time       `json:"timestamp" gorm:"index"`
	EventType    string          `json:"event_type" gorm:"index"`
	Category     string          `json:"category" gorm:"index"`
	UserID       int64           `json:"user_id" gorm:"index"`
	Username     string          `json:"username"`
	SessionID    string          `json:"session_id" gorm:"index"`
	HostID       int64           `json:"host_id" gorm:"index"`
	HostName     string          `json:"host_name"`
	Command      string          `json:"command"`
	Result       string          `json:"result"`
	Success      bool            `json:"success" gorm:"index"`
	RiskLevel    string          `json:"risk_level" gorm:"index"`
	IPAddress    string          `json:"ip_address"`
	UserAgent    string          `json:"user_agent"`
	Duration     int64           `json:"duration"` // milliseconds
	ErrorMessage string          `json:"error_message"`
	Metadata     string          `json:"metadata"` // JSON string
	Compliance   *ComplianceInfo `json:"compliance" gorm:"embedded"`
	CreatedAt    time.Time       `json:"created_at"`
}

// ComplianceInfo 合规信息
type ComplianceInfo struct {
	PolicyID     string     `json:"policy_id"`
	Approved     bool       `json:"approved"`
	ApproverID   int64      `json:"approver_id"`
	ApprovalTime *time.Time `json:"approval_time"`
	Violations   string     `json:"violations"` // JSON array
	Remediation  string     `json:"remediation"`
}

// AuditProcessor 审计处理器接口
type AuditProcessor interface {
	Process(event *AuditEvent) error
	GetName() string
}

// AuditFilter 审计过滤器接口
type AuditFilter interface {
	ShouldAudit(event *AuditEvent) bool
	GetName() string
}

// AuditExporter 审计导出器接口
type AuditExporter interface {
	Export(events []*AuditEvent, format string) ([]byte, error)
	GetSupportedFormats() []string
}

// AuditQuery 审计查询
type AuditQuery struct {
	StartTime *time.Time `json:"start_time"`
	EndTime   *time.Time `json:"end_time"`
	UserID    *int64     `json:"user_id"`
	HostID    *int64     `json:"host_id"`
	EventType string     `json:"event_type"`
	Category  string     `json:"category"`
	Success   *bool      `json:"success"`
	RiskLevel string     `json:"risk_level"`
	Command   string     `json:"command"`
	Page      int        `json:"page"`
	Limit     int        `json:"limit"`
	OrderBy   string     `json:"order_by"`
	OrderDir  string     `json:"order_dir"`
}

// AuditReport 审计报告
type AuditReport struct {
	Events      []*AuditEvent   `json:"events"`
	Total       int64           `json:"total"`
	Summary     *AuditSummary   `json:"summary"`
	Pagination  *PaginationInfo `json:"pagination"`
	GeneratedAt time.Time       `json:"generated_at"`
}

// AuditSummary 审计摘要
type AuditSummary struct {
	TotalEvents      int64            `json:"total_events"`
	SuccessfulEvents int64            `json:"successful_events"`
	FailedEvents     int64            `json:"failed_events"`
	EventsByType     map[string]int64 `json:"events_by_type"`
	EventsByRisk     map[string]int64 `json:"events_by_risk"`
	TopUsers         []*UserActivity  `json:"top_users"`
	TopHosts         []*HostActivity  `json:"top_hosts"`
	TimeRange        *TimeRange       `json:"time_range"`
}

// UserActivity 用户活动
type UserActivity struct {
	UserID   int64     `json:"user_id"`
	Username string    `json:"username"`
	Count    int64     `json:"count"`
	LastSeen time.Time `json:"last_seen"`
}

// HostActivity 主机活动
type HostActivity struct {
	HostID   int64     `json:"host_id"`
	HostName string    `json:"host_name"`
	Count    int64     `json:"count"`
	LastSeen time.Time `json:"last_seen"`
}

// TimeRange 时间范围
type TimeRange struct {
	Start time.Time `json:"start"`
	End   time.Time `json:"end"`
}

// PaginationInfo 分页信息
type PaginationInfo struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// NewAuditService 创建审计服务
func NewAuditService(db *gorm.DB, logger *logrus.Logger, config *AuditConfig) *AuditService {
	if config == nil {
		config = &AuditConfig{
			Enabled:          true,
			RetentionDays:    90,
			BatchSize:        100,
			FlushInterval:    5 * time.Minute,
			ComplianceMode:   true,
			EncryptSensitive: true,
			ExportFormats:    []string{"json", "csv", "pdf"},
		}
	}

	service := &AuditService{
		db:         db,
		logger:     logger,
		config:     config,
		processors: make(map[string]AuditProcessor),
		filters:    make([]AuditFilter, 0),
		exporters:  make(map[string]AuditExporter),
	}

	// 初始化数据库表
	service.initializeDatabase()

	// 注册默认处理器和过滤器
	service.registerDefaultComponents()

	// 启动清理例程
	go service.startCleanupRoutine(context.Background())

	return service
}

// initializeDatabase 初始化数据库
func (as *AuditService) initializeDatabase() error {
	return as.db.AutoMigrate(&AuditEvent{})
}

// registerDefaultComponents 注册默认组件
func (as *AuditService) registerDefaultComponents() {
	// 注册默认处理器
	as.processors["database"] = &DatabaseProcessor{db: as.db, logger: as.logger}
	as.processors["file"] = &FileProcessor{logger: as.logger}

	// 注册默认过滤器
	as.filters = append(as.filters, &SensitiveDataFilter{})
	as.filters = append(as.filters, &ComplianceFilter{})

	// 注册默认导出器
	as.exporters["json"] = &JSONExporter{}
	as.exporters["csv"] = &CSVExporter{}
}

// LogEvent 记录审计事件
func (as *AuditService) LogEvent(ctx context.Context, event *AuditEvent) error {
	if !as.config.Enabled {
		return nil
	}

	// 设置基本信息
	if event.ID == "" {
		event.ID = generateEventID()
	}
	if event.Timestamp.IsZero() {
		event.Timestamp = time.Now()
	}
	event.CreatedAt = time.Now()

	// 应用过滤器
	for _, filter := range as.filters {
		if !filter.ShouldAudit(event) {
			as.logger.WithField("filter", filter.GetName()).Debug("Event filtered out")
			return nil
		}
	}

	// 处理敏感数据
	if as.config.EncryptSensitive {
		as.encryptSensitiveData(event)
	}

	// 应用处理器
	for name, processor := range as.processors {
		if err := processor.Process(event); err != nil {
			as.logger.WithError(err).WithField("processor", name).Error("Failed to process audit event")
		}
	}

	as.logger.WithFields(logrus.Fields{
		"event_id":   event.ID,
		"event_type": event.EventType,
		"user_id":    event.UserID,
		"host_id":    event.HostID,
	}).Info("Audit event logged")

	return nil
}

// LogCommandExecution 记录命令执行
func (as *AuditService) LogCommandExecution(ctx context.Context, userID, hostID int64, username, hostname, command string, result *CommandExecutionResult, sessionID, ipAddress string) error {
	metadata := map[string]interface{}{
		"execution_context": result.Metadata,
		"security_info":     result.SecurityInfo,
	}

	metadataJSON, _ := json.Marshal(metadata)

	event := &AuditEvent{
		EventType:    "command_execution",
		Category:     "operations",
		UserID:       userID,
		Username:     username,
		SessionID:    sessionID,
		HostID:       hostID,
		HostName:     hostname,
		Command:      command,
		Result:       result.Output,
		Success:      result.Success,
		RiskLevel:    string(result.SecurityInfo.RiskLevel),
		IPAddress:    ipAddress,
		Duration:     result.Duration.Milliseconds(),
		ErrorMessage: result.Error,
		Metadata:     string(metadataJSON),
	}

	return as.LogEvent(ctx, event)
}

// LogUserLogin 记录用户登录
func (as *AuditService) LogUserLogin(ctx context.Context, userID int64, username, ipAddress, userAgent string, success bool, errorMsg string) error {
	event := &AuditEvent{
		EventType:    "user_login",
		Category:     "authentication",
		UserID:       userID,
		Username:     username,
		Success:      success,
		IPAddress:    ipAddress,
		UserAgent:    userAgent,
		ErrorMessage: errorMsg,
	}

	return as.LogEvent(ctx, event)
}

// LogApprovalAction 记录审批操作
func (as *AuditService) LogApprovalAction(ctx context.Context, approverID int64, requestID, action, comment string, success bool) error {
	metadata := map[string]interface{}{
		"request_id": requestID,
		"action":     action,
		"comment":    comment,
	}

	metadataJSON, _ := json.Marshal(metadata)

	event := &AuditEvent{
		EventType: "approval_action",
		Category:  "governance",
		UserID:    approverID,
		Success:   success,
		Metadata:  string(metadataJSON),
	}

	return as.LogEvent(ctx, event)
}

// QueryEvents 查询审计事件
func (as *AuditService) QueryEvents(ctx context.Context, query *AuditQuery) (*AuditReport, error) {
	db := as.db.Model(&AuditEvent{})

	// 应用查询条件
	if query.StartTime != nil {
		db = db.Where("timestamp >= ?", *query.StartTime)
	}
	if query.EndTime != nil {
		db = db.Where("timestamp <= ?", *query.EndTime)
	}
	if query.UserID != nil {
		db = db.Where("user_id = ?", *query.UserID)
	}
	if query.HostID != nil {
		db = db.Where("host_id = ?", *query.HostID)
	}
	if query.EventType != "" {
		db = db.Where("event_type = ?", query.EventType)
	}
	if query.Category != "" {
		db = db.Where("category = ?", query.Category)
	}
	if query.Success != nil {
		db = db.Where("success = ?", *query.Success)
	}
	if query.RiskLevel != "" {
		db = db.Where("risk_level = ?", query.RiskLevel)
	}
	if query.Command != "" {
		db = db.Where("command LIKE ?", "%"+query.Command+"%")
	}

	// 获取总数
	var total int64
	if err := db.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count events: %w", err)
	}

	// 应用排序
	orderBy := "timestamp"
	orderDir := "DESC"
	if query.OrderBy != "" {
		orderBy = query.OrderBy
	}
	if query.OrderDir != "" {
		orderDir = query.OrderDir
	}
	db = db.Order(fmt.Sprintf("%s %s", orderBy, orderDir))

	// 应用分页
	page := 1
	limit := 50
	if query.Page > 0 {
		page = query.Page
	}
	if query.Limit > 0 && query.Limit <= 1000 {
		limit = query.Limit
	}
	offset := (page - 1) * limit
	db = db.Offset(offset).Limit(limit)

	// 查询事件
	var events []*AuditEvent
	if err := db.Find(&events).Error; err != nil {
		return nil, fmt.Errorf("failed to query events: %w", err)
	}

	// 生成摘要
	summary, err := as.generateSummary(query)
	if err != nil {
		as.logger.WithError(err).Warn("Failed to generate audit summary")
	}

	// 构建报告
	report := &AuditReport{
		Events:      events,
		Total:       total,
		Summary:     summary,
		GeneratedAt: time.Now(),
		Pagination: &PaginationInfo{
			Page:       page,
			Limit:      limit,
			Total:      total,
			TotalPages: int((total + int64(limit) - 1) / int64(limit)),
		},
	}

	return report, nil
}

// generateSummary 生成审计摘要
func (as *AuditService) generateSummary(query *AuditQuery) (*AuditSummary, error) {
	db := as.db.Model(&AuditEvent{})

	// 应用相同的查询条件
	if query.StartTime != nil {
		db = db.Where("timestamp >= ?", *query.StartTime)
	}
	if query.EndTime != nil {
		db = db.Where("timestamp <= ?", *query.EndTime)
	}

	summary := &AuditSummary{
		EventsByType: make(map[string]int64),
		EventsByRisk: make(map[string]int64),
		TopUsers:     make([]*UserActivity, 0),
		TopHosts:     make([]*HostActivity, 0),
	}

	// 总事件数
	db.Count(&summary.TotalEvents)

	// 成功/失败事件数
	db.Where("success = ?", true).Count(&summary.SuccessfulEvents)
	db.Where("success = ?", false).Count(&summary.FailedEvents)

	// 按类型统计
	var typeStats []struct {
		EventType string
		Count     int64
	}
	db.Select("event_type, COUNT(*) as count").Group("event_type").Scan(&typeStats)
	for _, stat := range typeStats {
		summary.EventsByType[stat.EventType] = stat.Count
	}

	// 按风险等级统计
	var riskStats []struct {
		RiskLevel string
		Count     int64
	}
	db.Select("risk_level, COUNT(*) as count").Group("risk_level").Scan(&riskStats)
	for _, stat := range riskStats {
		summary.EventsByRisk[stat.RiskLevel] = stat.Count
	}

	// 活跃用户
	var userStats []struct {
		UserID   int64
		Username string
		Count    int64
		LastSeen time.Time
	}
	db.Select("user_id, username, COUNT(*) as count, MAX(timestamp) as last_seen").
		Group("user_id, username").
		Order("count DESC").
		Limit(10).
		Scan(&userStats)

	for _, stat := range userStats {
		summary.TopUsers = append(summary.TopUsers, &UserActivity{
			UserID:   stat.UserID,
			Username: stat.Username,
			Count:    stat.Count,
			LastSeen: stat.LastSeen,
		})
	}

	// 活跃主机
	var hostStats []struct {
		HostID   int64
		HostName string
		Count    int64
		LastSeen time.Time
	}
	db.Select("host_id, host_name, COUNT(*) as count, MAX(timestamp) as last_seen").
		Where("host_id > 0").
		Group("host_id, host_name").
		Order("count DESC").
		Limit(10).
		Scan(&hostStats)

	for _, stat := range hostStats {
		summary.TopHosts = append(summary.TopHosts, &HostActivity{
			HostID:   stat.HostID,
			HostName: stat.HostName,
			Count:    stat.Count,
			LastSeen: stat.LastSeen,
		})
	}

	// 时间范围
	if query.StartTime != nil && query.EndTime != nil {
		summary.TimeRange = &TimeRange{
			Start: *query.StartTime,
			End:   *query.EndTime,
		}
	}

	return summary, nil
}

// ExportEvents 导出审计事件
func (as *AuditService) ExportEvents(ctx context.Context, query *AuditQuery, format string) ([]byte, error) {
	exporter, exists := as.exporters[format]
	if !exists {
		return nil, fmt.Errorf("unsupported export format: %s", format)
	}

	report, err := as.QueryEvents(ctx, query)
	if err != nil {
		return nil, fmt.Errorf("failed to query events for export: %w", err)
	}

	return exporter.Export(report.Events, format)
}

// encryptSensitiveData 加密敏感数据
func (as *AuditService) encryptSensitiveData(event *AuditEvent) {
	// 这里应该实现敏感数据加密逻辑
	// 例如加密命令中的密码、密钥等
	// 暂时使用简单的掩码处理
	if event.Command != "" {
		event.Command = maskSensitiveData(event.Command)
	}
}

// maskSensitiveData 掩码敏感数据
func maskSensitiveData(data string) string {
	// 简单的敏感数据掩码实现
	// 在实际应用中应该使用更复杂的模式匹配和加密
	return data // 暂时不做处理
}

// generateEventID 生成事件ID
func generateEventID() string {
	return fmt.Sprintf("audit_%d_%d", time.Now().Unix(), time.Now().Nanosecond())
}

// startCleanupRoutine 启动清理例程
func (as *AuditService) startCleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(24 * time.Hour) // 每天清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			as.logger.Info("Audit cleanup routine stopped")
			return
		case <-ticker.C:
			as.cleanupOldEvents()
		}
	}
}

// cleanupOldEvents 清理旧事件
func (as *AuditService) cleanupOldEvents() {
	if as.config.RetentionDays <= 0 {
		return
	}

	cutoffTime := time.Now().AddDate(0, 0, -as.config.RetentionDays)

	result := as.db.Where("created_at < ?", cutoffTime).Delete(&AuditEvent{})
	if result.Error != nil {
		as.logger.WithError(result.Error).Error("Failed to cleanup old audit events")
		return
	}

	if result.RowsAffected > 0 {
		as.logger.WithFields(logrus.Fields{
			"deleted_count": result.RowsAffected,
			"cutoff_time":   cutoffTime,
		}).Info("Cleaned up old audit events")
	}
}

// GetAuditStatistics 获取审计统计
func (as *AuditService) GetAuditStatistics() map[string]interface{} {
	stats := map[string]interface{}{
		"enabled":         as.config.Enabled,
		"retention_days":  as.config.RetentionDays,
		"compliance_mode": as.config.ComplianceMode,
		"processors":      len(as.processors),
		"filters":         len(as.filters),
		"exporters":       len(as.exporters),
	}

	// 获取事件统计
	var totalEvents int64
	as.db.Model(&AuditEvent{}).Count(&totalEvents)
	stats["total_events"] = totalEvents

	// 获取最近24小时的事件数
	var recentEvents int64
	as.db.Model(&AuditEvent{}).Where("created_at > ?", time.Now().Add(-24*time.Hour)).Count(&recentEvents)
	stats["recent_events"] = recentEvents

	return stats
}

// DatabaseProcessor 数据库处理器
type DatabaseProcessor struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func (dp *DatabaseProcessor) Process(event *AuditEvent) error {
	return dp.db.Create(event).Error
}

func (dp *DatabaseProcessor) GetName() string {
	return "database"
}

// FileProcessor 文件处理器
type FileProcessor struct {
	logger *logrus.Logger
}

func (fp *FileProcessor) Process(event *AuditEvent) error {
	// 这里可以实现写入文件的逻辑
	fp.logger.WithFields(logrus.Fields{
		"event_id":   event.ID,
		"event_type": event.EventType,
		"timestamp":  event.Timestamp,
	}).Info("Audit event processed by file processor")
	return nil
}

func (fp *FileProcessor) GetName() string {
	return "file"
}

// SensitiveDataFilter 敏感数据过滤器
type SensitiveDataFilter struct{}

func (sdf *SensitiveDataFilter) ShouldAudit(event *AuditEvent) bool {
	// 这里可以实现敏感数据过滤逻辑
	// 例如过滤掉包含密码的命令
	return true
}

func (sdf *SensitiveDataFilter) GetName() string {
	return "sensitive_data"
}

// ComplianceFilter 合规过滤器
type ComplianceFilter struct{}

func (cf *ComplianceFilter) ShouldAudit(event *AuditEvent) bool {
	// 这里可以实现合规性过滤逻辑
	return true
}

func (cf *ComplianceFilter) GetName() string {
	return "compliance"
}

// JSONExporter JSON导出器
type JSONExporter struct{}

func (je *JSONExporter) Export(events []*AuditEvent, format string) ([]byte, error) {
	return json.MarshalIndent(events, "", "  ")
}

func (je *JSONExporter) GetSupportedFormats() []string {
	return []string{"json"}
}

// CSVExporter CSV导出器
type CSVExporter struct{}

func (ce *CSVExporter) Export(events []*AuditEvent, format string) ([]byte, error) {
	// 简单的CSV导出实现
	csv := "ID,Timestamp,EventType,Category,UserID,Username,HostID,Command,Success,RiskLevel\n"
	for _, event := range events {
		csv += fmt.Sprintf("%s,%s,%s,%s,%d,%s,%d,%s,%t,%s\n",
			event.ID,
			event.Timestamp.Format(time.RFC3339),
			event.EventType,
			event.Category,
			event.UserID,
			event.Username,
			event.HostID,
			event.Command,
			event.Success,
			event.RiskLevel,
		)
	}
	return []byte(csv), nil
}

func (ce *CSVExporter) GetSupportedFormats() []string {
	return []string{"csv"}
}
