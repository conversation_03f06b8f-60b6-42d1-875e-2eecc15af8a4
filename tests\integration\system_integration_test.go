package integration

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"strings"
	"testing"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/integration"
	"aiops-platform/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// SystemIntegrationTestSuite 系统集成测试套件
type SystemIntegrationTestSuite struct {
	suite.Suite
	
	// 测试组件
	db         *gorm.DB
	config     *config.Config
	logger     *logrus.Logger
	integrator *integration.SystemIntegrator
	server     *httptest.Server
	router     *gin.Engine
	
	// 测试数据
	testTenantID string
	testUserID   int64
	testHostID   int64
}

// SetupSuite 设置测试套件
func (suite *SystemIntegrationTestSuite) SetupSuite() {
	// 设置日志
	suite.logger = logrus.New()
	suite.logger.SetLevel(logrus.DebugLevel)
	
	// 设置测试数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	require.NoError(suite.T(), err)
	suite.db = db
	
	// 设置测试配置
	suite.config = &config.Config{
		Server: config.ServerConfig{
			Port:         8080,
			Host:         "localhost",
			Environment:  "test",
			ReadTimeout:  30 * time.Second,
			WriteTimeout: 30 * time.Second,
			IdleTimeout:  60 * time.Second,
		},
		Database: config.DatabaseConfig{
			Type:            "sqlite",
			DSN:             ":memory:",
			MaxOpenConns:    10,
			MaxIdleConns:    5,
			ConnMaxLifetime: 5 * time.Minute,
		},
		DeepSeek: config.DeepSeekConfig{
			APIKey:     "test-api-key",
			BaseURL:    "https://api.deepseek.com",
			Model:      "deepseek-chat",
			Timeout:    15 * time.Second,
			MaxRetries: 3,
		},
	}
	
	// 初始化系统集成器
	integrationConfig := &integration.IntegrationConfig{
		EnableHA:            false, // 测试环境禁用HA
		EnableMultiTenant:   true,
		EnableSecurity:      true,
		EnableTracing:       true,
		EnableMetrics:       true,
		EnableCaching:       true,
		StartupTimeout:      1 * time.Minute,
		ShutdownTimeout:     30 * time.Second,
		HealthCheckInterval: 10 * time.Second,
	}
	
	suite.integrator = integration.NewSystemIntegrator(integrationConfig, suite.logger)
	
	// 初始化系统
	ctx := context.Background()
	err = suite.integrator.Initialize(ctx)
	require.NoError(suite.T(), err)
	
	// 启动系统
	err = suite.integrator.Start(ctx)
	require.NoError(suite.T(), err)
	
	// 设置HTTP路由
	suite.setupRouter()
	
	// 设置测试数据
	suite.setupTestData()
	
	suite.logger.Info("System integration test suite setup completed")
}

// TearDownSuite 清理测试套件
func (suite *SystemIntegrationTestSuite) TearDownSuite() {
	if suite.server != nil {
		suite.server.Close()
	}
	
	if suite.integrator != nil {
		ctx := context.Background()
		suite.integrator.Stop(ctx)
	}
	
	suite.logger.Info("System integration test suite teardown completed")
}

// setupRouter 设置HTTP路由
func (suite *SystemIntegrationTestSuite) setupRouter() {
	gin.SetMode(gin.TestMode)
	suite.router = gin.New()
	
	// 健康检查端点
	suite.router.GET("/health", func(c *gin.Context) {
		health := suite.integrator.GetSystemHealth(c.Request.Context())
		c.JSON(http.StatusOK, gin.H{
			"status": "healthy",
			"timestamp": time.Now(),
			"services": health,
		})
	})
	
	// API路由组
	api := suite.router.Group("/api/v1")
	{
		// 聊天端点
		api.POST("/chat", suite.handleChat)
		
		// 主机管理端点
		hosts := api.Group("/hosts")
		{
			hosts.GET("", suite.listHosts)
			hosts.POST("", suite.createHost)
			hosts.GET("/:id", suite.getHost)
			hosts.PUT("/:id", suite.updateHost)
			hosts.DELETE("/:id", suite.deleteHost)
		}
		
		// 监控端点
		api.GET("/metrics", suite.getMetrics)
		api.GET("/traces", suite.getTraces)
		
		// 租户管理端点
		tenants := api.Group("/tenants")
		{
			tenants.GET("", suite.listTenants)
			tenants.POST("", suite.createTenant)
			tenants.GET("/:id", suite.getTenant)
		}
	}
	
	suite.server = httptest.NewServer(suite.router)
}

// setupTestData 设置测试数据
func (suite *SystemIntegrationTestSuite) setupTestData() {
	suite.testTenantID = "test-tenant-001"
	suite.testUserID = 1
	suite.testHostID = 1
}

// TestSystemHealth 测试系统健康状态
func (suite *SystemIntegrationTestSuite) TestSystemHealth() {
	resp, err := http.Get(suite.server.URL + "/health")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var health map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&health)
	require.NoError(suite.T(), err)
	
	assert.Equal(suite.T(), "healthy", health["status"])
	assert.Contains(suite.T(), health, "services")
	
	services := health["services"].(map[string]interface{})
	
	// 验证核心服务健康状态
	expectedServices := []string{"cache", "security", "tracing", "metrics"}
	for _, serviceName := range expectedServices {
		assert.Contains(suite.T(), services, serviceName)
		
		serviceHealth := services[serviceName].(map[string]interface{})
		assert.Equal(suite.T(), "healthy", serviceHealth["status"])
	}
}

// TestChatIntegration 测试聊天集成
func (suite *SystemIntegrationTestSuite) TestChatIntegration() {
	chatRequest := map[string]interface{}{
		"message":    "查看主机列表",
		"session_id": "test-session-001",
		"user_id":    suite.testUserID,
	}
	
	body, _ := json.Marshal(chatRequest)
	resp, err := http.Post(
		suite.server.URL+"/api/v1/chat",
		"application/json",
		strings.NewReader(string(body)),
	)
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var chatResponse map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&chatResponse)
	require.NoError(suite.T(), err)
	
	assert.Contains(suite.T(), chatResponse, "content")
	assert.Contains(suite.T(), chatResponse, "intent")
	assert.Contains(suite.T(), chatResponse, "confidence")
}

// TestHostManagement 测试主机管理
func (suite *SystemIntegrationTestSuite) TestHostManagement() {
	// 创建主机
	createRequest := map[string]interface{}{
		"name":       "test-host-001",
		"ip_address": "*************",
		"username":   "admin",
		"password":   "password123",
		"port":       22,
	}
	
	body, _ := json.Marshal(createRequest)
	resp, err := http.Post(
		suite.server.URL+"/api/v1/hosts",
		"application/json",
		strings.NewReader(string(body)),
	)
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	
	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)
	
	var createResponse map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&createResponse)
	require.NoError(suite.T(), err)
	
	hostID := int64(createResponse["id"].(float64))
	
	// 获取主机列表
	resp, err = http.Get(suite.server.URL + "/api/v1/hosts")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var listResponse map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&listResponse)
	require.NoError(suite.T(), err)
	
	hosts := listResponse["hosts"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(hosts), 1)
	
	// 获取单个主机
	resp, err = http.Get(fmt.Sprintf("%s/api/v1/hosts/%d", suite.server.URL, hostID))
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var getResponse map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&getResponse)
	require.NoError(suite.T(), err)
	
	assert.Equal(suite.T(), "test-host-001", getResponse["name"])
	assert.Equal(suite.T(), "*************", getResponse["ip_address"])
}

// TestMetricsCollection 测试指标收集
func (suite *SystemIntegrationTestSuite) TestMetricsCollection() {
	resp, err := http.Get(suite.server.URL + "/api/v1/metrics")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var metrics map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&metrics)
	require.NoError(suite.T(), err)
	
	// 验证系统指标
	assert.Contains(suite.T(), metrics, "memory_usage")
	assert.Contains(suite.T(), metrics, "goroutine_count")
	assert.Contains(suite.T(), metrics, "uptime")
	
	// 验证缓存指标
	if cacheMetrics, ok := metrics["cache"]; ok {
		cache := cacheMetrics.(map[string]interface{})
		assert.Contains(suite.T(), cache, "hit_rate")
		assert.Contains(suite.T(), cache, "total_hits")
	}
}

// TestTracingIntegration 测试追踪集成
func (suite *SystemIntegrationTestSuite) TestTracingIntegration() {
	// 发送一个请求以生成追踪数据
	_, err := http.Get(suite.server.URL + "/health")
	require.NoError(suite.T(), err)
	
	// 等待追踪数据处理
	time.Sleep(100 * time.Millisecond)
	
	resp, err := http.Get(suite.server.URL + "/api/v1/traces")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var traces map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&traces)
	require.NoError(suite.T(), err)
	
	assert.Contains(suite.T(), traces, "traces")
}

// TestMultiTenantIsolation 测试多租户隔离
func (suite *SystemIntegrationTestSuite) TestMultiTenantIsolation() {
	// 创建租户
	createRequest := map[string]interface{}{
		"id":   "tenant-001",
		"name": "Test Tenant 001",
		"plan": "basic",
	}
	
	body, _ := json.Marshal(createRequest)
	resp, err := http.Post(
		suite.server.URL+"/api/v1/tenants",
		"application/json",
		strings.NewReader(string(body)),
	)
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	
	assert.Equal(suite.T(), http.StatusCreated, resp.StatusCode)
	
	// 获取租户列表
	resp, err = http.Get(suite.server.URL + "/api/v1/tenants")
	require.NoError(suite.T(), err)
	defer resp.Body.Close()
	
	assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	
	var listResponse map[string]interface{}
	err = json.NewDecoder(resp.Body).Decode(&listResponse)
	require.NoError(suite.T(), err)
	
	tenants := listResponse["tenants"].([]interface{})
	assert.GreaterOrEqual(suite.T(), len(tenants), 1)
}

// TestSecurityIntegration 测试安全集成
func (suite *SystemIntegrationTestSuite) TestSecurityIntegration() {
	// 测试加密功能
	testData := "sensitive data"
	
	// 这里应该调用安全管理器的加密功能
	// 由于是集成测试，我们验证安全组件是否正常工作
	
	health := suite.integrator.GetSystemHealth(context.Background())
	securityHealth := health["security"]
	
	assert.Equal(suite.T(), "healthy", securityHealth.Status)
	assert.Contains(suite.T(), securityHealth.Details, "encryption_enabled")
	assert.Equal(suite.T(), true, securityHealth.Details["encryption_enabled"])
}

// TestCacheIntegration 测试缓存集成
func (suite *SystemIntegrationTestSuite) TestCacheIntegration() {
	// 多次请求同一个端点，验证缓存效果
	for i := 0; i < 3; i++ {
		resp, err := http.Get(suite.server.URL + "/api/v1/hosts")
		require.NoError(suite.T(), err)
		resp.Body.Close()
		
		assert.Equal(suite.T(), http.StatusOK, resp.StatusCode)
	}
	
	// 验证缓存统计
	health := suite.integrator.GetSystemHealth(context.Background())
	cacheHealth := health["cache"]
	
	assert.Equal(suite.T(), "healthy", cacheHealth.Status)
	assert.Contains(suite.T(), cacheHealth.Details, "hit_rate")
}

// TestConcurrentRequests 测试并发请求
func (suite *SystemIntegrationTestSuite) TestConcurrentRequests() {
	const numRequests = 10
	
	results := make(chan error, numRequests)
	
	for i := 0; i < numRequests; i++ {
		go func() {
			resp, err := http.Get(suite.server.URL + "/health")
			if err != nil {
				results <- err
				return
			}
			resp.Body.Close()
			
			if resp.StatusCode != http.StatusOK {
				results <- fmt.Errorf("unexpected status code: %d", resp.StatusCode)
				return
			}
			
			results <- nil
		}()
	}
	
	// 等待所有请求完成
	for i := 0; i < numRequests; i++ {
		err := <-results
		assert.NoError(suite.T(), err)
	}
}

// HTTP处理器实现
func (suite *SystemIntegrationTestSuite) handleChat(c *gin.Context) {
	var request map[string]interface{}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	// 模拟AI响应
	response := map[string]interface{}{
		"content":    "这是一个测试响应",
		"intent":     "host_management",
		"confidence": 0.9,
		"timestamp":  time.Now(),
	}
	
	c.JSON(http.StatusOK, response)
}

func (suite *SystemIntegrationTestSuite) listHosts(c *gin.Context) {
	hosts := []map[string]interface{}{
		{
			"id":         1,
			"name":       "test-host",
			"ip_address": "*************",
			"status":     "online",
		},
	}
	
	c.JSON(http.StatusOK, gin.H{"hosts": hosts})
}

func (suite *SystemIntegrationTestSuite) createHost(c *gin.Context) {
	var request map[string]interface{}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	response := map[string]interface{}{
		"id":         1,
		"name":       request["name"],
		"ip_address": request["ip_address"],
		"status":     "online",
		"created_at": time.Now(),
	}
	
	c.JSON(http.StatusCreated, response)
}

func (suite *SystemIntegrationTestSuite) getHost(c *gin.Context) {
	host := map[string]interface{}{
		"id":         1,
		"name":       "test-host-001",
		"ip_address": "*************",
		"status":     "online",
	}
	
	c.JSON(http.StatusOK, host)
}

func (suite *SystemIntegrationTestSuite) updateHost(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Host updated successfully"})
}

func (suite *SystemIntegrationTestSuite) deleteHost(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Host deleted successfully"})
}

func (suite *SystemIntegrationTestSuite) getMetrics(c *gin.Context) {
	metrics := map[string]interface{}{
		"memory_usage":    1024,
		"goroutine_count": 100,
		"uptime":          "1h30m",
		"cache": map[string]interface{}{
			"hit_rate":    0.85,
			"total_hits":  1000,
			"total_misses": 150,
		},
	}
	
	c.JSON(http.StatusOK, metrics)
}

func (suite *SystemIntegrationTestSuite) getTraces(c *gin.Context) {
	traces := map[string]interface{}{
		"traces": []map[string]interface{}{
			{
				"trace_id": "trace-001",
				"spans":    []string{"span-001", "span-002"},
				"duration": "100ms",
			},
		},
	}
	
	c.JSON(http.StatusOK, traces)
}

func (suite *SystemIntegrationTestSuite) listTenants(c *gin.Context) {
	tenants := []map[string]interface{}{
		{
			"id":   "tenant-001",
			"name": "Test Tenant",
			"plan": "basic",
		},
	}
	
	c.JSON(http.StatusOK, gin.H{"tenants": tenants})
}

func (suite *SystemIntegrationTestSuite) createTenant(c *gin.Context) {
	var request map[string]interface{}
	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}
	
	response := map[string]interface{}{
		"id":         request["id"],
		"name":       request["name"],
		"plan":       request["plan"],
		"created_at": time.Now(),
	}
	
	c.JSON(http.StatusCreated, response)
}

func (suite *SystemIntegrationTestSuite) getTenant(c *gin.Context) {
	tenant := map[string]interface{}{
		"id":   "tenant-001",
		"name": "Test Tenant",
		"plan": "basic",
	}
	
	c.JSON(http.StatusOK, tenant)
}

// TestSystemIntegration 运行系统集成测试
func TestSystemIntegration(t *testing.T) {
	suite.Run(t, new(SystemIntegrationTestSuite))
}
