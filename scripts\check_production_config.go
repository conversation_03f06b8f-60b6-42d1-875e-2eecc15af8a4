package main

import (
	"fmt"
	"os"
	"regexp"
	"strings"
)

// 生产环境配置检查工具
// 确保所有配置都适合生产环境使用

type ConfigCheck struct {
	Name        string
	Description string
	Check       func() (bool, string)
	Critical    bool
}

func main() {
	fmt.Println("=== AI运维管理平台生产环境配置检查 ===")
	fmt.Println()

	checks := []ConfigCheck{
		{
			Name:        "环境变量检查",
			Description: "检查必要的环境变量是否设置",
			Check:       checkEnvironmentVariables,
			Critical:    true,
		},
		{
			Name:        "API密钥安全性",
			Description: "检查API密钥是否为生产环境密钥",
			Check:       checkAPIKeySecurity,
			Critical:    true,
		},
		{
			Name:        "JWT密钥强度",
			Description: "检查JWT密钥是否足够强",
			Check:       checkJWTSecurity,
			Critical:    true,
		},
		{
			Name:        "加密密钥检查",
			Description: "检查加密密钥是否符合要求",
			Check:       checkEncryptionKey,
			Critical:    true,
		},
		{
			Name:        "调试模式检查",
			Description: "确保生产环境未启用调试模式",
			Check:       checkDebugMode,
			Critical:    true,
		},
		{
			Name:        "数据库安全性",
			Description: "检查数据库配置安全性",
			Check:       checkDatabaseSecurity,
			Critical:    false,
		},
		{
			Name:        "日志配置检查",
			Description: "检查日志配置是否合适",
			Check:       checkLoggingConfig,
			Critical:    false,
		},
		{
			Name:        "缓存配置检查",
			Description: "检查缓存配置是否优化",
			Check:       checkCacheConfig,
			Critical:    false,
		},
		{
			Name:        "安全配置检查",
			Description: "检查安全相关配置",
			Check:       checkSecurityConfig,
			Critical:    false,
		},
		{
			Name:        "性能配置检查",
			Description: "检查性能相关配置",
			Check:       checkPerformanceConfig,
			Critical:    false,
		},
	}

	var criticalIssues []string
	var warnings []string

	for _, check := range checks {
		fmt.Printf("🔍 %s...", check.Name)
		
		passed, message := check.Check()
		
		if passed {
			fmt.Printf(" ✅\n")
			if message != "" {
				fmt.Printf("   %s\n", message)
			}
		} else {
			if check.Critical {
				fmt.Printf(" ❌\n")
				fmt.Printf("   %s\n", message)
				criticalIssues = append(criticalIssues, fmt.Sprintf("%s: %s", check.Name, message))
			} else {
				fmt.Printf(" ⚠️\n")
				fmt.Printf("   %s\n", message)
				warnings = append(warnings, fmt.Sprintf("%s: %s", check.Name, message))
			}
		}
		fmt.Println()
	}

	// 显示检查结果
	fmt.Println("=== 检查结果 ===")
	fmt.Println()

	if len(criticalIssues) == 0 {
		fmt.Println("✅ 所有关键配置检查通过！")
	} else {
		fmt.Println("❌ 发现关键配置问题：")
		for _, issue := range criticalIssues {
			fmt.Printf("   • %s\n", issue)
		}
		fmt.Println()
		fmt.Println("请修复这些关键问题后再部署到生产环境。")
	}

	if len(warnings) > 0 {
		fmt.Println()
		fmt.Println("⚠️ 建议优化的配置：")
		for _, warning := range warnings {
			fmt.Printf("   • %s\n", warning)
		}
	}

	fmt.Println()
	if len(criticalIssues) == 0 {
		fmt.Println("🚀 系统已准备好部署到生产环境！")
		os.Exit(0)
	} else {
		fmt.Println("🛑 请修复关键问题后重新检查。")
		os.Exit(1)
	}
}

// checkEnvironmentVariables 检查必要的环境变量
func checkEnvironmentVariables() (bool, string) {
	requiredVars := []string{
		"AIOPS_DEEPSEEK_API_KEY",
		"AIOPS_JWT_SECRET",
		"AIOPS_ENCRYPTION_KEY",
	}

	var missingVars []string
	for _, varName := range requiredVars {
		if os.Getenv(varName) == "" {
			missingVars = append(missingVars, varName)
		}
	}

	if len(missingVars) > 0 {
		return false, fmt.Sprintf("缺少必要的环境变量: %s", strings.Join(missingVars, ", "))
	}

	return true, "所有必要的环境变量都已设置"
}

// checkAPIKeySecurity 检查API密钥安全性
func checkAPIKeySecurity() (bool, string) {
	apiKey := os.Getenv("AIOPS_DEEPSEEK_API_KEY")
	
	if apiKey == "" {
		return false, "DeepSeek API密钥未设置"
	}

	// 检查是否为测试密钥
	testPatterns := []string{
		"test",
		"demo",
		"example",
		"your-api-key",
		"sk-3e9bebb943a546d6b60c3c24c7532fbc", // 示例密钥
	}

	apiKeyLower := strings.ToLower(apiKey)
	for _, pattern := range testPatterns {
		if strings.Contains(apiKeyLower, pattern) {
			return false, "检测到测试API密钥，请使用真实的生产环境API密钥"
		}
	}

	if len(apiKey) < 32 {
		return false, "API密钥长度过短，可能不是有效的生产环境密钥"
	}

	return true, "API密钥看起来是有效的生产环境密钥"
}

// checkJWTSecurity 检查JWT密钥强度
func checkJWTSecurity() (bool, string) {
	jwtSecret := os.Getenv("AIOPS_JWT_SECRET")
	
	if jwtSecret == "" {
		return false, "JWT密钥未设置"
	}

	// 检查是否为默认或测试密钥
	weakSecrets := []string{
		"dev-jwt-secret-key-for-local-development-only",
		"your-super-secret-jwt-key-change-in-production",
		"secret",
		"password",
		"123456",
	}

	for _, weak := range weakSecrets {
		if jwtSecret == weak {
			return false, "检测到弱JWT密钥，请使用强随机密钥"
		}
	}

	if len(jwtSecret) < 32 {
		return false, "JWT密钥长度不足32字符，建议使用更长的密钥"
	}

	// 检查密钥复杂性
	hasUpper := regexp.MustCompile(`[A-Z]`).MatchString(jwtSecret)
	hasLower := regexp.MustCompile(`[a-z]`).MatchString(jwtSecret)
	hasDigit := regexp.MustCompile(`[0-9]`).MatchString(jwtSecret)
	hasSpecial := regexp.MustCompile(`[^A-Za-z0-9]`).MatchString(jwtSecret)

	complexity := 0
	if hasUpper {
		complexity++
	}
	if hasLower {
		complexity++
	}
	if hasDigit {
		complexity++
	}
	if hasSpecial {
		complexity++
	}

	if complexity < 3 {
		return false, "JWT密钥复杂性不足，建议包含大小写字母、数字和特殊字符"
	}

	return true, "JWT密钥强度符合要求"
}

// checkEncryptionKey 检查加密密钥
func checkEncryptionKey() (bool, string) {
	encKey := os.Getenv("AIOPS_ENCRYPTION_KEY")
	
	if encKey == "" {
		return false, "加密密钥未设置"
	}

	// 检查是否为默认密钥
	defaultKeys := []string{
		"dev-encryption-key-32bytes-long!",
		"your-32-byte-encryption-key-here!!",
		"your-32-byte-encryption-key-here",
	}

	for _, defaultKey := range defaultKeys {
		if encKey == defaultKey {
			return false, "检测到默认加密密钥，请使用随机生成的密钥"
		}
	}

	if len(encKey) != 32 {
		return false, fmt.Sprintf("加密密钥长度必须为32字节，当前长度: %d", len(encKey))
	}

	return true, "加密密钥长度符合要求"
}

// checkDebugMode 检查调试模式
func checkDebugMode() (bool, string) {
	debug := strings.ToLower(os.Getenv("AIOPS_DEBUG"))
	env := strings.ToLower(os.Getenv("AIOPS_ENV"))
	
	if debug == "true" || debug == "1" {
		return false, "生产环境不应启用调试模式"
	}

	if env != "production" && env != "prod" {
		return false, fmt.Sprintf("环境变量AIOPS_ENV应设置为'production'，当前值: %s", env)
	}

	return true, "调试模式已正确禁用"
}

// checkDatabaseSecurity 检查数据库安全性
func checkDatabaseSecurity() (bool, string) {
	dbPath := os.Getenv("AIOPS_DB_PATH")
	if dbPath == "" {
		dbPath = "./data/aiops.db"
	}

	// 检查数据库文件权限
	if info, err := os.Stat(dbPath); err == nil {
		mode := info.Mode()
		if mode.Perm() > 0600 {
			return false, "数据库文件权限过于宽松，建议设置为600"
		}
	}

	return true, "数据库安全配置正常"
}

// checkLoggingConfig 检查日志配置
func checkLoggingConfig() (bool, string) {
	logLevel := strings.ToLower(os.Getenv("AIOPS_LOG_LEVEL"))
	
	if logLevel == "debug" || logLevel == "trace" {
		return false, "生产环境不建议使用debug或trace日志级别"
	}

	if logLevel == "" || logLevel == "info" || logLevel == "warn" || logLevel == "error" {
		return true, "日志级别配置合适"
	}

	return false, fmt.Sprintf("未知的日志级别: %s", logLevel)
}

// checkCacheConfig 检查缓存配置
func checkCacheConfig() (bool, string) {
	cacheEnabled := strings.ToLower(os.Getenv("AIOPS_CACHE_ENABLED"))
	
	if cacheEnabled == "false" || cacheEnabled == "0" {
		return false, "建议在生产环境启用缓存以提高性能"
	}

	return true, "缓存配置正常"
}

// checkSecurityConfig 检查安全配置
func checkSecurityConfig() (bool, string) {
	rateLimitEnabled := strings.ToLower(os.Getenv("AIOPS_RATE_LIMIT_ENABLED"))
	threatDetection := strings.ToLower(os.Getenv("AIOPS_THREAT_DETECTION"))
	
	issues := []string{}
	
	if rateLimitEnabled == "false" || rateLimitEnabled == "0" {
		issues = append(issues, "建议启用速率限制")
	}
	
	if threatDetection == "false" || threatDetection == "0" {
		issues = append(issues, "建议启用威胁检测")
	}

	if len(issues) > 0 {
		return false, strings.Join(issues, "; ")
	}

	return true, "安全配置正常"
}

// checkPerformanceConfig 检查性能配置
func checkPerformanceConfig() (bool, string) {
	intelligentMode := strings.ToLower(os.Getenv("AIOPS_INTELLIGENT_MODE"))
	performanceMonitoring := strings.ToLower(os.Getenv("AIOPS_PERFORMANCE_MONITORING"))
	
	issues := []string{}
	
	if intelligentMode == "false" || intelligentMode == "0" {
		issues = append(issues, "建议启用智能模式以获得最佳性能")
	}
	
	if performanceMonitoring == "false" || performanceMonitoring == "0" {
		issues = append(issues, "建议启用性能监控")
	}

	if len(issues) > 0 {
		return false, strings.Join(issues, "; ")
	}

	return true, "性能配置正常"
}
