package handler

import (
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"aiops-platform/internal/ai"
	"aiops-platform/internal/service"
)

// 🚀 报表API处理器 - 提供完整的报表生成和管理API
type ReportAPIHandler struct {
	db                *gorm.DB
	logger            *logrus.Logger
	reportExecutor    *service.IntelligentReportExecutor
	deepseekClient    *ai.DeepSeekClient
}

// ReportGenerationRequest 报表生成请求
type ReportGenerationRequest struct {
	ReportType      string                 `json:"report_type" binding:"required"`
	TimeRange       string                 `json:"time_range"`
	ExportFormat    string                 `json:"export_format"`
	AnalysisLevel   string                 `json:"analysis_level"`
	IncludeInsights bool                   `json:"include_insights"`
	CustomFilters   map[string]interface{} `json:"custom_filters"`
}

// ReportGenerationResponse 报表生成响应
type ReportGenerationResponse struct {
	Success       bool                   `json:"success"`
	ReportID      string                 `json:"report_id"`
	Title         string                 `json:"title"`
	Content       string                 `json:"content"`
	Summary       interface{}            `json:"summary"`
	Charts        []ai.ReportChart       `json:"charts"`
	Insights      []string               `json:"insights"`
	ExportFormat  string                 `json:"export_format"`
	GeneratedAt   time.Time              `json:"generated_at"`
	ExecutionTime string                 `json:"execution_time"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// ReportListResponse 报表列表响应
type ReportListResponse struct {
	Success bool           `json:"success"`
	Reports []ReportSummary `json:"reports"`
	Total   int            `json:"total"`
	Page    int            `json:"page"`
	Size    int            `json:"size"`
}

// ReportSummary 报表摘要
type ReportSummary struct {
	ReportID     string    `json:"report_id"`
	Title        string    `json:"title"`
	ReportType   string    `json:"report_type"`
	ExportFormat string    `json:"export_format"`
	GeneratedAt  time.Time `json:"generated_at"`
	Status       string    `json:"status"`
}

// NewReportAPIHandler 创建报表API处理器
func NewReportAPIHandler(
	db *gorm.DB,
	logger *logrus.Logger,
	deepseekClient *ai.DeepSeekClient,
) *ReportAPIHandler {
	handler := &ReportAPIHandler{
		db:             db,
		logger:         logger,
		deepseekClient: deepseekClient,
	}
	
	// 初始化报表执行器
	handler.reportExecutor = service.NewIntelligentReportExecutor(db, logger, deepseekClient)
	
	logger.Info("🚀 报表API处理器初始化完成")
	return handler
}

// RegisterRoutes 注册路由
func (rah *ReportAPIHandler) RegisterRoutes(router *gin.RouterGroup) {
	reports := router.Group("/reports")
	{
		reports.POST("/generate", rah.GenerateReport)
		reports.GET("/list", rah.ListReports)
		reports.GET("/:report_id", rah.GetReport)
		reports.GET("/:report_id/download", rah.DownloadReport)
		reports.DELETE("/:report_id", rah.DeleteReport)
		reports.GET("/types", rah.GetSupportedTypes)
		reports.GET("/formats", rah.GetSupportedFormats)
		reports.POST("/preview", rah.PreviewReport)
	}
}

// GenerateReport 生成报表
func (rah *ReportAPIHandler) GenerateReport(c *gin.Context) {
	start := time.Now()
	
	var req ReportGenerationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		rah.logger.WithError(err).Warn("报表生成请求参数错误")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}
	
	// 设置默认值
	if req.TimeRange == "" {
		req.TimeRange = "24h"
	}
	if req.ExportFormat == "" {
		req.ExportFormat = "json"
	}
	if req.AnalysisLevel == "" {
		req.AnalysisLevel = "detailed"
	}
	
	rah.logger.WithFields(logrus.Fields{
		"report_type":    req.ReportType,
		"time_range":     req.TimeRange,
		"export_format":  req.ExportFormat,
		"analysis_level": req.AnalysisLevel,
	}).Info("🚀 开始生成报表")
	
	// 获取用户ID（从JWT或session中）
	userID := int64(1) // 简化实现，实际应该从认证中获取
	
	// 构建执行请求
	execReq := &service.ExecutionRequest{
		UserID:      userID,
		SessionID:   "api_session_" + strconv.FormatInt(time.Now().UnixNano(), 10),
		OriginalMsg: rah.buildReportMessage(req),
	}
	
	// 执行报表生成
	result, err := rah.reportExecutor.Execute(c.Request.Context(), execReq)
	if err != nil {
		rah.logger.WithError(err).Error("报表生成失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "报表生成失败: " + err.Error(),
		})
		return
	}
	
	if !result.Success {
		rah.logger.Warn("报表生成执行失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "报表生成执行失败: " + result.Content,
		})
		return
	}
	
	// 构建响应
	response := rah.buildReportResponse(result, start)
	
	rah.logger.WithFields(logrus.Fields{
		"report_id":      response.ReportID,
		"execution_time": response.ExecutionTime,
	}).Info("🚀 报表生成完成")
	
	c.JSON(http.StatusOK, response)
}

// ListReports 获取报表列表
func (rah *ReportAPIHandler) ListReports(c *gin.Context) {
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	size, _ := strconv.Atoi(c.DefaultQuery("size", "10"))
	reportType := c.Query("type")
	
	if page < 1 {
		page = 1
	}
	if size < 1 || size > 100 {
		size = 10
	}
	
	rah.logger.WithFields(logrus.Fields{
		"page":        page,
		"size":        size,
		"report_type": reportType,
	}).Info("🚀 获取报表列表")
	
	// 简化实现：返回模拟数据
	reports := []ReportSummary{
		{
			ReportID:     "report_001",
			Title:        "运维操作报表 - 最近24小时",
			ReportType:   "operation",
			ExportFormat: "json",
			GeneratedAt:  time.Now().Add(-2 * time.Hour),
			Status:       "completed",
		},
		{
			ReportID:     "report_002",
			Title:        "系统健康报表 - 最近7天",
			ReportType:   "health",
			ExportFormat: "html",
			GeneratedAt:  time.Now().Add(-1 * time.Hour),
			Status:       "completed",
		},
	}
	
	// 过滤报表类型
	if reportType != "" {
		var filteredReports []ReportSummary
		for _, report := range reports {
			if report.ReportType == reportType {
				filteredReports = append(filteredReports, report)
			}
		}
		reports = filteredReports
	}
	
	response := ReportListResponse{
		Success: true,
		Reports: reports,
		Total:   len(reports),
		Page:    page,
		Size:    size,
	}
	
	c.JSON(http.StatusOK, response)
}

// GetReport 获取报表详情
func (rah *ReportAPIHandler) GetReport(c *gin.Context) {
	reportID := c.Param("report_id")
	
	rah.logger.WithField("report_id", reportID).Info("🚀 获取报表详情")
	
	// 简化实现：返回模拟数据
	response := ReportGenerationResponse{
		Success:       true,
		ReportID:      reportID,
		Title:         "运维操作报表 - 最近24小时",
		Content:       "这是一个示例报表内容...",
		ExportFormat:  "json",
		GeneratedAt:   time.Now(),
		ExecutionTime: "2.5s",
		Metadata: map[string]interface{}{
			"analysis_level": "detailed",
			"ai_enhanced":    true,
		},
	}
	
	c.JSON(http.StatusOK, response)
}

// DownloadReport 下载报表
func (rah *ReportAPIHandler) DownloadReport(c *gin.Context) {
	reportID := c.Param("report_id")
	format := c.DefaultQuery("format", "json")
	
	rah.logger.WithFields(logrus.Fields{
		"report_id": reportID,
		"format":    format,
	}).Info("🚀 下载报表")
	
	// 简化实现：返回示例内容
	filename := reportID + "." + format
	content := "这是报表内容..."
	
	c.Header("Content-Disposition", "attachment; filename="+filename)
	c.Header("Content-Type", "application/octet-stream")
	c.String(http.StatusOK, content)
}

// DeleteReport 删除报表
func (rah *ReportAPIHandler) DeleteReport(c *gin.Context) {
	reportID := c.Param("report_id")
	
	rah.logger.WithField("report_id", reportID).Info("🚀 删除报表")
	
	// 简化实现：直接返回成功
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "报表删除成功",
	})
}

// GetSupportedTypes 获取支持的报表类型
func (rah *ReportAPIHandler) GetSupportedTypes(c *gin.Context) {
	types := rah.reportExecutor.GetSupportedReportTypes()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"types":   types,
	})
}

// GetSupportedFormats 获取支持的导出格式
func (rah *ReportAPIHandler) GetSupportedFormats(c *gin.Context) {
	formats := rah.reportExecutor.GetSupportedExportFormats()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"formats": formats,
	})
}

// PreviewReport 预览报表
func (rah *ReportAPIHandler) PreviewReport(c *gin.Context) {
	var req ReportGenerationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}
	
	rah.logger.WithField("report_type", req.ReportType).Info("🚀 预览报表")
	
	// 简化实现：返回预览数据
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"preview": map[string]interface{}{
			"title":       "报表预览",
			"report_type": req.ReportType,
			"time_range":  req.TimeRange,
			"sample_data": "这是预览数据...",
		},
	})
}

// buildReportMessage 构建报表消息
func (rah *ReportAPIHandler) buildReportMessage(req ReportGenerationRequest) string {
	return "生成" + req.ReportType + "报表，时间范围：" + req.TimeRange + "，格式：" + req.ExportFormat
}

// buildReportResponse 构建报表响应
func (rah *ReportAPIHandler) buildReportResponse(result *service.UnifiedExecutionResult, start time.Time) *ReportGenerationResponse {
	response := &ReportGenerationResponse{
		Success:       result.Success,
		Content:       result.Content,
		ExportFormat:  "json",
		GeneratedAt:   time.Now(),
		ExecutionTime: time.Since(start).String(),
		Metadata:      result.Metadata,
	}
	
	// 从元数据中提取报表信息
	if metadata := result.Metadata; metadata != nil {
		if reportID, ok := metadata["report_id"].(string); ok {
			response.ReportID = reportID
		}
		if reportType, ok := metadata["report_type"].(string); ok {
			response.Title = rah.generateReportTitle(reportType)
		}
		if exportFormat, ok := metadata["export_format"].(string); ok {
			response.ExportFormat = exportFormat
		}
	}
	
	// 从原始数据中提取详细信息
	if rawData := result.RawData; rawData != nil {
		if reportResult, ok := rawData.(*ai.IntelligentReportResult); ok {
			response.ReportID = reportResult.ReportID
			response.Title = reportResult.Title
			response.Summary = reportResult.Summary
			response.Charts = reportResult.Charts
			response.Insights = reportResult.Insights
			response.ExportFormat = reportResult.ExportFormat
			response.GeneratedAt = reportResult.GeneratedAt
			response.ExecutionTime = reportResult.ExecutionTime.String()
		}
	}
	
	return response
}

// generateReportTitle 生成报表标题
func (rah *ReportAPIHandler) generateReportTitle(reportType string) string {
	titles := map[string]string{
		"operation": "运维操作报表",
		"health":    "系统健康报表",
		"ai_usage":  "AI使用报表",
		"custom":    "自定义报表",
	}
	
	if title, ok := titles[reportType]; ok {
		return title
	}
	return "运维报表"
}
