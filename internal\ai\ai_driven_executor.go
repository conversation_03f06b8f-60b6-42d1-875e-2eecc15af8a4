package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 🚀 AI驱动执行器 - 真正的AI驱动架构
// AIDrivenExecutor 让DeepSeek完全负责意图识别、内容生成和渲染
type AIDrivenExecutor struct {
	deepseekClient *DeepSeekClient
	db             *gorm.DB
	logger         *logrus.Logger
	
	// 纯执行器组件 - 不包含任何业务逻辑
	sqlExecutor    *PureSQLExecutor
	shellExecutor  *PureShellExecutor
	configExecutor *PureConfigExecutor
	
	// AI组件
	intentAnalyzer    *AIIntentAnalyzer
	contentGenerator  *AIContentGenerator
	responseRenderer  *AIResponseRenderer
	safetyValidator   *AISafetyValidator
}

// AIExecutionRequest AI执行请求
type AIExecutionRequest struct {
	UserInput   string                 `json:"user_input"`
	SessionID   string                 `json:"session_id"`
	UserID      int64                  `json:"user_id"`
	Context     *AIExecutionContext    `json:"context"`
	Preferences *UserPreferences       `json:"preferences"`
}

// AIExecutionContext AI执行上下文
type AIExecutionContext struct {
	DatabaseSchema  *DatabaseSchemaInfo `json:"database_schema"`
	HostInventory   *HostInventoryInfo  `json:"host_inventory"`
	SystemState     *SystemStateInfo    `json:"system_state"`
	UserHistory     []string            `json:"user_history"`
	SecurityLevel   string              `json:"security_level"`
}

// DatabaseSchemaInfo 数据库模式信息
type DatabaseSchemaInfo struct {
	Tables []TableSchema `json:"tables"`
}

// TableSchema 表模式
type TableSchema struct {
	Name        string         `json:"name"`
	Columns     []ColumnSchema `json:"columns"`
	Description string         `json:"description"`
}

// ColumnSchema 列模式
type ColumnSchema struct {
	Name        string `json:"name"`
	Type        string `json:"type"`
	Nullable    bool   `json:"nullable"`
	Description string `json:"description"`
}

// HostInventoryInfo 主机清单信息
type HostInventoryInfo struct {
	TotalHosts int        `json:"total_hosts"`
	Hosts      []HostInfo `json:"hosts"`
}

// HostInfo 主机信息
type HostInfo struct {
	ID          int64  `json:"id"`
	IPAddress   string `json:"ip_address"`
	Name        string `json:"name"`
	Status      string `json:"status"`
	Environment string `json:"environment"`
	OS          string `json:"os"`
}

// SystemStateInfo 系统状态信息
type SystemStateInfo struct {
	Timestamp    time.Time              `json:"timestamp"`
	Services     []string               `json:"services"`
	Metrics      map[string]interface{} `json:"metrics"`
	Alerts       []string               `json:"alerts"`
}

// UserPreferences 用户偏好
type UserPreferences struct {
	OutputFormat    string `json:"output_format"`    // table, json, markdown
	SafetyLevel     string `json:"safety_level"`     // strict, normal, relaxed
	ConfirmActions  bool   `json:"confirm_actions"`
	PreferredShell  string `json:"preferred_shell"`  // bash, zsh, powershell
}

// AIExecutionResult AI执行结果
type AIExecutionResult struct {
	Success         bool                   `json:"success"`
	Content         string                 `json:"content"`
	ExecutionType   string                 `json:"execution_type"`   // sql, shell, config, info
	GeneratedCode   string                 `json:"generated_code"`   // AI生成的代码
	ExecutionData   interface{}            `json:"execution_data"`   // 执行返回的原始数据
	Metadata        map[string]interface{} `json:"metadata"`
	SafetyInfo      *SafetyInfo            `json:"safety_info"`
	ExecutionTime   time.Duration          `json:"execution_time"`
	RequireConfirm  bool                   `json:"require_confirm"`
	ConfirmToken    string                 `json:"confirm_token"`
}

// SafetyInfo 安全信息
type SafetyInfo struct {
	RiskLevel   string   `json:"risk_level"`   // low, medium, high
	Warnings    []string `json:"warnings"`
	Suggestions []string `json:"suggestions"`
}

// NewAIDrivenExecutor 创建AI驱动执行器
func NewAIDrivenExecutor(
	deepseekClient *DeepSeekClient,
	db *gorm.DB,
	logger *logrus.Logger,
) *AIDrivenExecutor {
	executor := &AIDrivenExecutor{
		deepseekClient: deepseekClient,
		db:             db,
		logger:         logger,
	}

	// 初始化纯执行器组件
	executor.sqlExecutor = NewPureSQLExecutor(db, logger)
	executor.shellExecutor = NewPureShellExecutor(logger)
	executor.configExecutor = NewPureConfigExecutor(logger)

	// 初始化AI组件
	executor.intentAnalyzer = NewAIIntentAnalyzer(deepseekClient, logger)
	executor.contentGenerator = NewAIContentGenerator(deepseekClient, logger)
	executor.responseRenderer = NewAIResponseRenderer(deepseekClient, logger)
	executor.safetyValidator = NewAISafetyValidator(deepseekClient, logger)

	logger.Info("🚀 AI驱动执行器初始化完成 - DeepSeek完全驱动")
	return executor
}

// Execute 执行AI驱动的操作 - 核心方法
func (ade *AIDrivenExecutor) Execute(
	ctx context.Context,
	req *AIExecutionRequest,
) (*AIExecutionResult, error) {
	start := time.Now()

	ade.logger.WithFields(logrus.Fields{
		"user_input": req.UserInput,
		"session_id": req.SessionID,
		"user_id":    req.UserID,
	}).Info("🎯 开始AI驱动执行")

	// 第一步：AI意图分析和内容生成
	aiAnalysis, err := ade.analyzeAndGenerate(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("AI分析失败: %w", err)
	}

	// 第二步：安全验证
	safetyInfo, err := ade.validateSafety(ctx, aiAnalysis)
	if err != nil {
		return nil, fmt.Errorf("安全验证失败: %w", err)
	}

	// 第三步：纯执行
	executionData, err := ade.executeGenerated(ctx, aiAnalysis)
	if err != nil {
		return nil, fmt.Errorf("执行失败: %w", err)
	}

	// 第四步：AI渲染响应
	renderedContent, err := ade.renderResponse(ctx, aiAnalysis, executionData, req)
	if err != nil {
		return nil, fmt.Errorf("响应渲染失败: %w", err)
	}

	result := &AIExecutionResult{
		Success:       true,
		Content:       renderedContent,
		ExecutionType: aiAnalysis.ExecutionType,
		GeneratedCode: aiAnalysis.GeneratedCode,
		ExecutionData: executionData,
		SafetyInfo:    safetyInfo,
		ExecutionTime: time.Since(start),
	}

	ade.logger.WithFields(logrus.Fields{
		"execution_type": result.ExecutionType,
		"success":        result.Success,
		"execution_time": result.ExecutionTime,
	}).Info("✅ AI驱动执行完成")

	return result, nil
}

// AIAnalysisResult AI分析结果
type AIAnalysisResult struct {
	Intent          string                 `json:"intent"`
	ExecutionType   string                 `json:"execution_type"`   // sql, shell, config, info
	GeneratedCode   string                 `json:"generated_code"`
	Parameters      map[string]interface{} `json:"parameters"`
	Description     string                 `json:"description"`
	RequireConfirm  bool                   `json:"require_confirm"`
	EstimatedRisk   string                 `json:"estimated_risk"`
}

// analyzeAndGenerate AI分析和生成
func (ade *AIDrivenExecutor) analyzeAndGenerate(
	ctx context.Context,
	req *AIExecutionRequest,
) (*AIAnalysisResult, error) {
	// 构建AI提示词
	prompt := ade.buildAnalysisPrompt(req)

	// 调用DeepSeek进行分析和生成
	response, err := ade.deepseekClient.GenerateContent(ctx, &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: ade.buildSystemPrompt(),
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   4000,
		Temperature: 0.3,
	})

	if err != nil {
		return nil, fmt.Errorf("DeepSeek分析失败: %w", err)
	}

	// 解析AI响应
	var analysis AIAnalysisResult
	if err := json.Unmarshal([]byte(response.Content), &analysis); err != nil {
		// 如果JSON解析失败，尝试从文本中提取信息
		analysis = ade.parseTextResponse(response.Content)
	}

	return &analysis, nil
}

// buildAnalysisPrompt 构建分析提示词
func (ade *AIDrivenExecutor) buildAnalysisPrompt(req *AIExecutionRequest) string {
	prompt := fmt.Sprintf(`
用户输入: %s

请分析用户意图并生成相应的执行内容。

可用的数据库表结构:
%s

可用的主机信息:
%s

请返回JSON格式的分析结果，包含以下字段:
{
  "intent": "用户意图描述",
  "execution_type": "sql|shell|config|info",
  "generated_code": "生成的SQL/Shell命令/配置内容",
  "parameters": {},
  "description": "操作描述",
  "require_confirm": false,
  "estimated_risk": "low|medium|high"
}

注意:
1. 如果是数据库查询，生成标准SQL语句
2. 如果是系统操作，生成Shell命令
3. 如果是配置操作，生成配置内容
4. 确保生成的内容安全可执行
5. 对于危险操作，设置require_confirm为true
`, req.UserInput, ade.formatDatabaseSchema(req.Context), ade.formatHostInfo(req.Context))

	return prompt
}

// buildSystemPrompt 构建系统提示词
func (ade *AIDrivenExecutor) buildSystemPrompt() string {
	return `你是一个专业的AI运维助手，负责分析用户需求并生成相应的执行内容。

核心职责:
1. 准确理解用户意图
2. 生成安全、正确的SQL语句、Shell命令或配置内容
3. 评估操作风险
4. 提供清晰的操作描述

安全原则:
1. 绝不生成危险的删除或破坏性操作
2. 对于修改操作，必须设置确认机制
3. 优先使用只读查询
4. 包含适当的限制条件

输出要求:
1. 必须返回有效的JSON格式
2. 生成的代码必须语法正确
3. 包含详细的操作描述
4. 准确评估风险级别`
}

// formatDatabaseSchema 格式化数据库模式
func (ade *AIDrivenExecutor) formatDatabaseSchema(ctx *AIExecutionContext) string {
	if ctx == nil || ctx.DatabaseSchema == nil {
		return "数据库模式信息不可用"
	}

	var schema string
	for _, table := range ctx.DatabaseSchema.Tables {
		schema += fmt.Sprintf("表: %s\n", table.Name)
		for _, col := range table.Columns {
			schema += fmt.Sprintf("  - %s (%s)\n", col.Name, col.Type)
		}
		schema += "\n"
	}
	return schema
}

// formatHostInfo 格式化主机信息
func (ade *AIDrivenExecutor) formatHostInfo(ctx *AIExecutionContext) string {
	if ctx == nil || ctx.HostInventory == nil {
		return "主机信息不可用"
	}

	info := fmt.Sprintf("总主机数: %d\n", ctx.HostInventory.TotalHosts)
	for _, host := range ctx.HostInventory.Hosts {
		info += fmt.Sprintf("- %s (%s) - %s\n", host.Name, host.IPAddress, host.Status)
	}
	return info
}

// parseTextResponse 解析文本响应
func (ade *AIDrivenExecutor) parseTextResponse(content string) AIAnalysisResult {
	// 简单的文本解析逻辑，实际应该更复杂
	return AIAnalysisResult{
		Intent:        "通用查询",
		ExecutionType: "info",
		GeneratedCode: "",
		Description:   "AI响应解析",
		RequireConfirm: false,
		EstimatedRisk: "low",
	}
}

// validateSafety 安全验证
func (ade *AIDrivenExecutor) validateSafety(
	ctx context.Context,
	analysis *AIAnalysisResult,
) (*SafetyInfo, error) {
	return ade.safetyValidator.Validate(ctx, analysis)
}

// executeGenerated 执行生成的内容
func (ade *AIDrivenExecutor) executeGenerated(
	ctx context.Context,
	analysis *AIAnalysisResult,
) (interface{}, error) {
	switch analysis.ExecutionType {
	case "sql":
		return ade.sqlExecutor.Execute(ctx, analysis.GeneratedCode)
	case "shell":
		return ade.shellExecutor.Execute(ctx, analysis.GeneratedCode)
	case "config":
		return ade.configExecutor.Execute(ctx, analysis.GeneratedCode)
	default:
		return map[string]interface{}{
			"message": "信息查询完成",
			"data":    analysis.Description,
		}, nil
	}
}

// renderResponse AI渲染响应
func (ade *AIDrivenExecutor) renderResponse(
	ctx context.Context,
	analysis *AIAnalysisResult,
	executionData interface{},
	req *AIExecutionRequest,
) (string, error) {
	return ade.responseRenderer.Render(ctx, &RenderRequest{
		Analysis:      analysis,
		ExecutionData: executionData,
		UserInput:     req.UserInput,
		Preferences:   req.Preferences,
	})
}
