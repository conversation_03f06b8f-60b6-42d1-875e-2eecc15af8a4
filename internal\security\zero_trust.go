package security

import (
	"context"
	"crypto/rand"
	"crypto/sha256"
	"encoding/hex"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// TrustLevel 信任级别
type TrustLevel int

const (
	TrustNone TrustLevel = iota
	TrustLow
	TrustMedium
	TrustHigh
	TrustFull
)

func (tl TrustLevel) String() string {
	switch tl {
	case TrustNone:
		return "NONE"
	case TrustLow:
		return "LOW"
	case TrustMedium:
		return "MEDIUM"
	case TrustHigh:
		return "HIGH"
	case TrustFull:
		return "FULL"
	default:
		return "UNKNOWN"
	}
}

// AccessDecision 访问决策
type AccessDecision int

const (
	AccessDeny AccessDecision = iota
	AccessAllow
	AccessChallenge
	AccessMonitor
)

func (ad AccessDecision) String() string {
	switch ad {
	case AccessDeny:
		return "DENY"
	case AccessAllow:
		return "ALLOW"
	case AccessChallenge:
		return "CHALLENGE"
	case AccessMonitor:
		return "MONITOR"
	default:
		return "UNKNOWN"
	}
}

// Identity 身份信息
type Identity struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`        // user, service, device
	Name        string                 `json:"name"`
	Email       string                 `json:"email,omitempty"`
	Groups      []string               `json:"groups"`
	Roles       []string               `json:"roles"`
	Attributes  map[string]interface{} `json:"attributes"`
	CreatedAt   time.Time              `json:"created_at"`
	LastSeen    time.Time              `json:"last_seen"`
	IsActive    bool                   `json:"is_active"`
	TrustScore  float64                `json:"trust_score"`
}

// Device 设备信息
type Device struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"`         // laptop, mobile, server, iot
	OS           string                 `json:"os"`
	Version      string                 `json:"version"`
	Fingerprint  string                 `json:"fingerprint"`
	IPAddress    string                 `json:"ip_address"`
	MACAddress   string                 `json:"mac_address"`
	Location     string                 `json:"location"`
	IsManaged    bool                   `json:"is_managed"`
	IsCompliant  bool                   `json:"is_compliant"`
	TrustLevel   TrustLevel             `json:"trust_level"`
	LastSeen     time.Time              `json:"last_seen"`
	Attributes   map[string]interface{} `json:"attributes"`
}

// AccessRequest 访问请求
type AccessRequest struct {
	ID          string                 `json:"id"`
	Identity    *Identity              `json:"identity"`
	Device      *Device                `json:"device"`
	Resource    string                 `json:"resource"`
	Action      string                 `json:"action"`
	Context     *AccessContext         `json:"context"`
	Timestamp   time.Time              `json:"timestamp"`
	Decision    AccessDecision         `json:"decision"`
	Reason      string                 `json:"reason"`
	TrustScore  float64                `json:"trust_score"`
	RiskScore   float64                `json:"risk_score"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// AccessContext 访问上下文
type AccessContext struct {
	IPAddress     string            `json:"ip_address"`
	UserAgent     string            `json:"user_agent"`
	Location      string            `json:"location"`
	TimeOfDay     string            `json:"time_of_day"`
	DayOfWeek     string            `json:"day_of_week"`
	IsVPN         bool              `json:"is_vpn"`
	IsTor         bool              `json:"is_tor"`
	IsProxy       bool              `json:"is_proxy"`
	NetworkType   string            `json:"network_type"`   // corporate, public, home
	SecurityLevel string            `json:"security_level"` // high, medium, low
	Headers       map[string]string `json:"headers"`
}

// Policy 访问策略
type Policy struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Enabled     bool                   `json:"enabled"`
	Priority    int                    `json:"priority"`
	Conditions  []PolicyCondition      `json:"conditions"`
	Actions     []PolicyAction         `json:"actions"`
	Resources   []string               `json:"resources"`
	Subjects    []string               `json:"subjects"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// PolicyCondition 策略条件
type PolicyCondition struct {
	Field    string      `json:"field"`    // identity.role, device.type, context.location, etc.
	Operator string      `json:"operator"` // equals, contains, in, not_in, greater_than, etc.
	Value    interface{} `json:"value"`
	Negate   bool        `json:"negate"`
}

// PolicyAction 策略动作
type PolicyAction struct {
	Type       string                 `json:"type"`       // allow, deny, challenge, monitor, log
	Parameters map[string]interface{} `json:"parameters"`
}

// ZeroTrustEngine 零信任引擎
type ZeroTrustEngine struct {
	config           *ZeroTrustConfig
	policies         map[string]*Policy
	identities       map[string]*Identity
	devices          map[string]*Device
	accessRequests   []*AccessRequest
	trustCalculator  *TrustCalculator
	riskAssessor     *RiskAssessor
	policyEvaluator  *PolicyEvaluator
	logger           *logrus.Logger
	mutex            sync.RWMutex
	running          bool
	stopChan         chan struct{}
}

// ZeroTrustConfig 零信任配置
type ZeroTrustConfig struct {
	Enabled              bool          `json:"enabled"`
	DefaultDeny          bool          `json:"default_deny"`          // 默认拒绝
	ContinuousValidation bool          `json:"continuous_validation"` // 持续验证
	SessionTimeout       time.Duration `json:"session_timeout"`       // 会话超时
	RevalidationInterval time.Duration `json:"revalidation_interval"` // 重新验证间隔
	TrustDecayRate       float64       `json:"trust_decay_rate"`      // 信任衰减率
	RiskThreshold        float64       `json:"risk_threshold"`        // 风险阈值
	LogAllRequests       bool          `json:"log_all_requests"`      // 记录所有请求
	AuditEnabled         bool          `json:"audit_enabled"`         // 审计启用
	AlertEnabled         bool          `json:"alert_enabled"`         // 告警启用
}

// NewZeroTrustEngine 创建零信任引擎
func NewZeroTrustEngine(config *ZeroTrustConfig, logger *logrus.Logger) *ZeroTrustEngine {
	// 设置默认值
	if config.SessionTimeout <= 0 {
		config.SessionTimeout = 8 * time.Hour
	}
	if config.RevalidationInterval <= 0 {
		config.RevalidationInterval = 1 * time.Hour
	}
	if config.TrustDecayRate <= 0 {
		config.TrustDecayRate = 0.1
	}
	if config.RiskThreshold <= 0 {
		config.RiskThreshold = 0.7
	}

	zte := &ZeroTrustEngine{
		config:         config,
		policies:       make(map[string]*Policy),
		identities:     make(map[string]*Identity),
		devices:        make(map[string]*Device),
		accessRequests: make([]*AccessRequest, 0),
		logger:         logger,
		stopChan:       make(chan struct{}),
	}

	// 初始化组件
	zte.trustCalculator = NewTrustCalculator(logger)
	zte.riskAssessor = NewRiskAssessor(logger)
	zte.policyEvaluator = NewPolicyEvaluator(logger)

	// 注册默认策略
	zte.registerDefaultPolicies()

	return zte
}

// Start 启动零信任引擎
func (zte *ZeroTrustEngine) Start(ctx context.Context) error {
	zte.mutex.Lock()
	defer zte.mutex.Unlock()

	if zte.running {
		return fmt.Errorf("zero trust engine is already running")
	}

	if !zte.config.Enabled {
		zte.logger.Info("Zero trust engine is disabled")
		return nil
	}

	zte.running = true

	// 启动持续验证
	if zte.config.ContinuousValidation {
		go zte.continuousValidationLoop(ctx)
	}

	// 启动信任衰减
	go zte.trustDecayLoop(ctx)

	// 启动清理任务
	go zte.cleanupLoop(ctx)

	zte.logger.Info("Zero trust engine started")
	return nil
}

// Stop 停止零信任引擎
func (zte *ZeroTrustEngine) Stop() error {
	zte.mutex.Lock()
	defer zte.mutex.Unlock()

	if !zte.running {
		return nil
	}

	close(zte.stopChan)
	zte.running = false

	zte.logger.Info("Zero trust engine stopped")
	return nil
}

// EvaluateAccess 评估访问请求
func (zte *ZeroTrustEngine) EvaluateAccess(ctx context.Context, request *AccessRequest) (*AccessRequest, error) {
	startTime := time.Now()

	// 生成请求ID
	if request.ID == "" {
		request.ID = zte.generateRequestID()
	}
	request.Timestamp = startTime

	zte.logger.WithFields(logrus.Fields{
		"request_id": request.ID,
		"identity":   request.Identity.ID,
		"resource":   request.Resource,
		"action":     request.Action,
	}).Info("Evaluating access request")

	// 1. 身份验证和设备验证
	if err := zte.validateIdentity(request.Identity); err != nil {
		request.Decision = AccessDeny
		request.Reason = fmt.Sprintf("Identity validation failed: %v", err)
		zte.recordAccessRequest(request)
		return request, err
	}

	if err := zte.validateDevice(request.Device); err != nil {
		request.Decision = AccessDeny
		request.Reason = fmt.Sprintf("Device validation failed: %v", err)
		zte.recordAccessRequest(request)
		return request, err
	}

	// 2. 计算信任分数
	trustScore := zte.trustCalculator.CalculateTrustScore(request.Identity, request.Device, request.Context)
	request.TrustScore = trustScore

	// 3. 评估风险
	riskScore := zte.riskAssessor.AssessRisk(request)
	request.RiskScore = riskScore

	// 4. 策略评估
	decision, reason := zte.policyEvaluator.EvaluatePolicies(request, zte.getPolicies())
	request.Decision = decision
	request.Reason = reason

	// 5. 应用默认策略
	if request.Decision == AccessDeny && zte.config.DefaultDeny {
		if request.Reason == "" {
			request.Reason = "Default deny policy"
		}
	}

	// 6. 记录访问请求
	zte.recordAccessRequest(request)

	// 7. 更新身份和设备信息
	zte.updateIdentityLastSeen(request.Identity.ID)
	zte.updateDeviceLastSeen(request.Device.ID)

	duration := time.Since(startTime)
	zte.logger.WithFields(logrus.Fields{
		"request_id":   request.ID,
		"decision":     request.Decision.String(),
		"trust_score":  request.TrustScore,
		"risk_score":   request.RiskScore,
		"duration_ms":  duration.Milliseconds(),
		"reason":       request.Reason,
	}).Info("Access evaluation completed")

	return request, nil
}

// RegisterIdentity 注册身份
func (zte *ZeroTrustEngine) RegisterIdentity(identity *Identity) error {
	zte.mutex.Lock()
	defer zte.mutex.Unlock()

	if identity.ID == "" {
		return fmt.Errorf("identity ID cannot be empty")
	}

	identity.CreatedAt = time.Now()
	identity.LastSeen = time.Now()
	identity.IsActive = true

	zte.identities[identity.ID] = identity

	zte.logger.WithFields(logrus.Fields{
		"identity_id":   identity.ID,
		"identity_type": identity.Type,
		"identity_name": identity.Name,
	}).Info("Identity registered")

	return nil
}

// RegisterDevice 注册设备
func (zte *ZeroTrustEngine) RegisterDevice(device *Device) error {
	zte.mutex.Lock()
	defer zte.mutex.Unlock()

	if device.ID == "" {
		return fmt.Errorf("device ID cannot be empty")
	}

	device.LastSeen = time.Now()
	device.TrustLevel = TrustMedium // 默认中等信任

	zte.devices[device.ID] = device

	zte.logger.WithFields(logrus.Fields{
		"device_id":   device.ID,
		"device_type": device.Type,
		"device_os":   device.OS,
	}).Info("Device registered")

	return nil
}

// RegisterPolicy 注册策略
func (zte *ZeroTrustEngine) RegisterPolicy(policy *Policy) error {
	zte.mutex.Lock()
	defer zte.mutex.Unlock()

	if policy.ID == "" {
		return fmt.Errorf("policy ID cannot be empty")
	}

	policy.CreatedAt = time.Now()
	policy.UpdatedAt = time.Now()

	zte.policies[policy.ID] = policy

	zte.logger.WithFields(logrus.Fields{
		"policy_id":   policy.ID,
		"policy_name": policy.Name,
	}).Info("Policy registered")

	return nil
}

// validateIdentity 验证身份
func (zte *ZeroTrustEngine) validateIdentity(identity *Identity) error {
	zte.mutex.RLock()
	defer zte.mutex.RUnlock()

	storedIdentity, exists := zte.identities[identity.ID]
	if !exists {
		return fmt.Errorf("identity not found: %s", identity.ID)
	}

	if !storedIdentity.IsActive {
		return fmt.Errorf("identity is inactive: %s", identity.ID)
	}

	return nil
}

// validateDevice 验证设备
func (zte *ZeroTrustEngine) validateDevice(device *Device) error {
	zte.mutex.RLock()
	defer zte.mutex.RUnlock()

	storedDevice, exists := zte.devices[device.ID]
	if !exists {
		return fmt.Errorf("device not found: %s", device.ID)
	}

	if !storedDevice.IsCompliant {
		return fmt.Errorf("device is not compliant: %s", device.ID)
	}

	return nil
}

// recordAccessRequest 记录访问请求
func (zte *ZeroTrustEngine) recordAccessRequest(request *AccessRequest) {
	zte.mutex.Lock()
	defer zte.mutex.Unlock()

	zte.accessRequests = append(zte.accessRequests, request)

	// 限制记录数量
	if len(zte.accessRequests) > 10000 {
		zte.accessRequests = zte.accessRequests[1000:]
	}
}

// updateIdentityLastSeen 更新身份最后见到时间
func (zte *ZeroTrustEngine) updateIdentityLastSeen(identityID string) {
	zte.mutex.Lock()
	defer zte.mutex.Unlock()

	if identity, exists := zte.identities[identityID]; exists {
		identity.LastSeen = time.Now()
	}
}

// updateDeviceLastSeen 更新设备最后见到时间
func (zte *ZeroTrustEngine) updateDeviceLastSeen(deviceID string) {
	zte.mutex.Lock()
	defer zte.mutex.Unlock()

	if device, exists := zte.devices[deviceID]; exists {
		device.LastSeen = time.Now()
	}
}

// getPolicies 获取策略列表
func (zte *ZeroTrustEngine) getPolicies() []*Policy {
	zte.mutex.RLock()
	defer zte.mutex.RUnlock()

	policies := make([]*Policy, 0, len(zte.policies))
	for _, policy := range zte.policies {
		if policy.Enabled {
			policies = append(policies, policy)
		}
	}

	return policies
}

// generateRequestID 生成请求ID
func (zte *ZeroTrustEngine) generateRequestID() string {
	bytes := make([]byte, 16)
	rand.Read(bytes)
	hash := sha256.Sum256(bytes)
	return hex.EncodeToString(hash[:8])
}

// continuousValidationLoop 持续验证循环
func (zte *ZeroTrustEngine) continuousValidationLoop(ctx context.Context) {
	ticker := time.NewTicker(zte.config.RevalidationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-zte.stopChan:
			return
		case <-ticker.C:
			zte.performContinuousValidation()
		}
	}
}

// performContinuousValidation 执行持续验证
func (zte *ZeroTrustEngine) performContinuousValidation() {
	zte.logger.Debug("Performing continuous validation")
	// 简化实现：检查过期会话
	// 实际实现应该重新验证活跃会话
}

// trustDecayLoop 信任衰减循环
func (zte *ZeroTrustEngine) trustDecayLoop(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-zte.stopChan:
			return
		case <-ticker.C:
			zte.applyTrustDecay()
		}
	}
}

// applyTrustDecay 应用信任衰减
func (zte *ZeroTrustEngine) applyTrustDecay() {
	zte.mutex.Lock()
	defer zte.mutex.Unlock()

	for _, identity := range zte.identities {
		if time.Since(identity.LastSeen) > 24*time.Hour {
			identity.TrustScore *= (1 - zte.config.TrustDecayRate)
			if identity.TrustScore < 0 {
				identity.TrustScore = 0
			}
		}
	}
}

// cleanupLoop 清理循环
func (zte *ZeroTrustEngine) cleanupLoop(ctx context.Context) {
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-zte.stopChan:
			return
		case <-ticker.C:
			zte.cleanup()
		}
	}
}

// cleanup 清理过期数据
func (zte *ZeroTrustEngine) cleanup() {
	zte.mutex.Lock()
	defer zte.mutex.Unlock()

	// 清理过期的访问请求（保留7天）
	cutoff := time.Now().Add(-7 * 24 * time.Hour)
	filtered := make([]*AccessRequest, 0)
	for _, request := range zte.accessRequests {
		if request.Timestamp.After(cutoff) {
			filtered = append(filtered, request)
		}
	}
	zte.accessRequests = filtered

	zte.logger.Debug("Cleanup completed")
}

// registerDefaultPolicies 注册默认策略
func (zte *ZeroTrustEngine) registerDefaultPolicies() {
	// 管理员全权限策略
	adminPolicy := &Policy{
		ID:          "admin_full_access",
		Name:        "Administrator Full Access",
		Description: "Full access for administrators",
		Enabled:     true,
		Priority:    100,
		Conditions: []PolicyCondition{
			{
				Field:    "identity.roles",
				Operator: "contains",
				Value:    "admin",
			},
		},
		Actions: []PolicyAction{
			{
				Type: "allow",
			},
		},
		Resources: []string{"*"},
	}

	// 高风险拒绝策略
	highRiskPolicy := &Policy{
		ID:          "high_risk_deny",
		Name:        "High Risk Deny",
		Description: "Deny access for high risk requests",
		Enabled:     true,
		Priority:    200,
		Conditions: []PolicyCondition{
			{
				Field:    "risk_score",
				Operator: "greater_than",
				Value:    0.8,
			},
		},
		Actions: []PolicyAction{
			{
				Type: "deny",
			},
		},
		Resources: []string{"*"},
	}

	zte.policies[adminPolicy.ID] = adminPolicy
	zte.policies[highRiskPolicy.ID] = highRiskPolicy
}

// GetStats 获取统计信息
func (zte *ZeroTrustEngine) GetStats() map[string]interface{} {
	zte.mutex.RLock()
	defer zte.mutex.RUnlock()

	totalRequests := len(zte.accessRequests)
	allowedRequests := 0
	deniedRequests := 0

	for _, request := range zte.accessRequests {
		switch request.Decision {
		case AccessAllow:
			allowedRequests++
		case AccessDeny:
			deniedRequests++
		}
	}

	return map[string]interface{}{
		"enabled":           zte.config.Enabled,
		"total_identities":  len(zte.identities),
		"total_devices":     len(zte.devices),
		"total_policies":    len(zte.policies),
		"total_requests":    totalRequests,
		"allowed_requests":  allowedRequests,
		"denied_requests":   deniedRequests,
		"allow_rate":        float64(allowedRequests) / float64(totalRequests),
	}
}
