package service

import (
	"fmt"
	"strings"

	"aiops-platform/internal/config"
	"github.com/sirupsen/logrus"
)

// ModelManager 模型管理器
type ModelManager struct {
	config *config.AIModelsConfig
	logger *logrus.Logger
	models map[string]*config.AIModelConfig
	currentModel string
}

// NewModelManager 创建模型管理器
func NewModelManager(cfg *config.AIModelsConfig, logger *logrus.Logger) *ModelManager {
	mm := &ModelManager{
		config: cfg,
		logger: logger,
		models: make(map[string]*config.AIModelConfig),
		currentModel: cfg.DefaultModel,
	}

	// 初始化模型映射
	for i := range cfg.Models {
		model := &cfg.Models[i]
		// 默认启用，除非明确设置为false
		if model.Enabled == false && model.Name != cfg.DefaultModel {
			continue
		}
		mm.models[model.Name] = model
		logger.WithFields(logrus.Fields{
			"model": model.Name,
			"provider": model.Provider,
			"display_name": model.DisplayName,
		}).Info("Loaded AI model")
	}

	logger.WithField("default_model", cfg.DefaultModel).Info("Model manager initialized")
	return mm
}

// GetAvailableModels 获取可用模型列表
func (mm *ModelManager) GetAvailableModels() []ModelInfo {
	var models []ModelInfo
	for _, model := range mm.models {
		models = append(models, ModelInfo{
			Name:        model.Name,
			DisplayName: model.DisplayName,
			Provider:    model.Provider,
			Icon:        model.Icon,
			Description: model.Description,
			IsCurrent:   model.Name == mm.currentModel,
		})
	}
	return models
}

// GetCurrentModel 获取当前模型
func (mm *ModelManager) GetCurrentModel() *config.AIModelConfig {
	if model, exists := mm.models[mm.currentModel]; exists {
		return model
	}
	// 如果当前模型不存在，返回默认模型
	if model, exists := mm.models[mm.config.DefaultModel]; exists {
		mm.currentModel = mm.config.DefaultModel
		return model
	}
	// 如果默认模型也不存在，返回第一个可用模型
	for _, model := range mm.models {
		mm.currentModel = model.Name
		return model
	}
	return nil
}

// SetCurrentModel 设置当前模型
func (mm *ModelManager) SetCurrentModel(modelName string) error {
	if model, exists := mm.models[modelName]; exists {
		mm.currentModel = modelName
		mm.logger.WithFields(logrus.Fields{
			"model": modelName,
			"display_name": model.DisplayName,
		}).Info("Switched to model")
		return nil
	}
	return fmt.Errorf("model not found: %s", modelName)
}

// GetModelByName 根据名称获取模型
func (mm *ModelManager) GetModelByName(name string) (*config.AIModelConfig, error) {
	if model, exists := mm.models[name]; exists {
		return model, nil
	}
	return nil, fmt.Errorf("model not found: %s", name)
}

// IsModelAvailable 检查模型是否可用
func (mm *ModelManager) IsModelAvailable(name string) bool {
	_, exists := mm.models[name]
	return exists
}

// GetModelsByProvider 根据提供商获取模型
func (mm *ModelManager) GetModelsByProvider(provider string) []ModelInfo {
	var models []ModelInfo
	for _, model := range mm.models {
		if strings.EqualFold(model.Provider, provider) {
			models = append(models, ModelInfo{
				Name:        model.Name,
				DisplayName: model.DisplayName,
				Provider:    model.Provider,
				Icon:        model.Icon,
				Description: model.Description,
				IsCurrent:   model.Name == mm.currentModel,
			})
		}
	}
	return models
}

// ModelInfo 模型信息
type ModelInfo struct {
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Provider    string `json:"provider"`
	Icon        string `json:"icon"`
	Description string `json:"description"`
	IsCurrent   bool   `json:"is_current"`
}

// GetCurrentModelName 获取当前模型名称
func (mm *ModelManager) GetCurrentModelName() string {
	return mm.currentModel
}

// ValidateModel 验证模型配置
func (mm *ModelManager) ValidateModel(model *config.AIModelConfig) error {
	if model.Name == "" {
		return fmt.Errorf("model name is required")
	}
	if model.DisplayName == "" {
		return fmt.Errorf("model display name is required")
	}
	if model.Provider == "" {
		return fmt.Errorf("model provider is required")
	}
	if model.APIURL == "" {
		return fmt.Errorf("model API URL is required")
	}
	return nil
}

// ReloadModels 重新加载模型配置
func (mm *ModelManager) ReloadModels(cfg *config.AIModelsConfig) error {
	mm.config = cfg
	mm.models = make(map[string]*config.AIModelConfig)
	
	// 重新初始化模型映射
	for i := range cfg.Models {
		model := &cfg.Models[i]
		if err := mm.ValidateModel(model); err != nil {
			mm.logger.WithError(err).WithField("model", model.Name).Warn("Invalid model configuration")
			continue
		}
		
		// 默认启用，除非明确设置为false
		if model.Enabled == false && model.Name != cfg.DefaultModel {
			continue
		}
		mm.models[model.Name] = model
	}
	
	// 检查当前模型是否仍然可用
	if !mm.IsModelAvailable(mm.currentModel) {
		mm.currentModel = cfg.DefaultModel
		if !mm.IsModelAvailable(mm.currentModel) {
			// 如果默认模型也不可用，选择第一个可用模型
			for name := range mm.models {
				mm.currentModel = name
				break
			}
		}
	}
	
	mm.logger.WithField("model_count", len(mm.models)).Info("Models reloaded")
	return nil
}
