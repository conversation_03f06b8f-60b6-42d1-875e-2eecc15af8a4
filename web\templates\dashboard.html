<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仪表板 - AI对话运维管理平台</title>
    <link href="/static/css/design-system.css" rel="stylesheet">
    <link href="/static/css/interactions.css" rel="stylesheet">
    <link href="/static/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/fonts.css" rel="stylesheet">
    <link href="/static/css/main.css" rel="stylesheet">

    <script>
        // 防止重复初始化的标志
        window.chartJsInitialized = false;

        // Chart.js 本地实现 - 不依赖CDN
        function initializeLocalChart() {
            if (window.chartJsInitialized) {
                return; // 防止重复初始化
            }

            console.log('Initializing local Chart.js implementation');

            // 创建一个功能完整的Chart替代品
            window.Chart = function(ctx, config) {
                this.ctx = ctx;
                this.config = config;
                this.data = config.data || { labels: [], datasets: [] };

                // 渲染方法
                this.update = function() {
                    this.render();
                };

                this.destroy = function() {
                    if (this.ctx && this.ctx.canvas) {
                        this.ctx.clearRect(0, 0, this.ctx.canvas.width, this.ctx.canvas.height);
                    }
                };

                // 渲染图表
                this.render = function() {
                    if (!this.ctx || !this.ctx.canvas) return;

                    const canvas = this.ctx.canvas;
                    const width = canvas.width;
                    const height = canvas.height;

                    // 清空画布
                    this.ctx.clearRect(0, 0, width, height);

                    // 绘制背景
                    this.ctx.fillStyle = '#ffffff';
                    this.ctx.fillRect(0, 0, width, height);

                    // 绘制边框
                    this.ctx.strokeStyle = '#e9ecef';
                    this.ctx.lineWidth = 1;
                    this.ctx.strokeRect(0, 0, width, height);

                    // 绘制数据
                    if (this.data.labels && this.data.labels.length > 0 &&
                        this.data.datasets && this.data.datasets.length > 0) {
                        this.drawLineChart();
                    } else {
                        this.drawPlaceholder();
                    }
                };

                // 绘制折线图
                this.drawLineChart = function() {
                    const canvas = this.ctx.canvas;
                    const width = canvas.width;
                    const height = canvas.height;
                    const padding = 40;
                    const chartWidth = width - 2 * padding;
                    const chartHeight = height - 2 * padding;

                    const dataset = this.data.datasets[0];
                    const data = dataset.data || [];
                    const labels = this.data.labels || [];

                    if (data.length === 0) {
                        this.drawPlaceholder();
                        return;
                    }

                    // 计算数据范围
                    const maxValue = Math.max(...data, 1);
                    const minValue = Math.min(...data, 0);
                    const range = maxValue - minValue || 1;

                    // 绘制网格线
                    this.ctx.strokeStyle = '#f8f9fa';
                    this.ctx.lineWidth = 1;
                    for (let i = 0; i <= 5; i++) {
                        const y = padding + (chartHeight * i / 5);
                        this.ctx.beginPath();
                        this.ctx.moveTo(padding, y);
                        this.ctx.lineTo(width - padding, y);
                        this.ctx.stroke();
                    }

                    // 绘制数据线
                    this.ctx.strokeStyle = dataset.borderColor || '#007bff';
                    this.ctx.lineWidth = 2;
                    this.ctx.beginPath();

                    for (let i = 0; i < data.length; i++) {
                        const x = padding + (chartWidth * i / (data.length - 1));
                        const y = padding + chartHeight - ((data[i] - minValue) / range * chartHeight);

                        if (i === 0) {
                            this.ctx.moveTo(x, y);
                        } else {
                            this.ctx.lineTo(x, y);
                        }
                    }
                    this.ctx.stroke();

                    // 绘制数据点
                    this.ctx.fillStyle = dataset.borderColor || '#007bff';
                    for (let i = 0; i < data.length; i++) {
                        const x = padding + (chartWidth * i / (data.length - 1));
                        const y = padding + chartHeight - ((data[i] - minValue) / range * chartHeight);

                        this.ctx.beginPath();
                        this.ctx.arc(x, y, 3, 0, 2 * Math.PI);
                        this.ctx.fill();
                    }

                    // 绘制标题
                    if (dataset.label) {
                        this.ctx.fillStyle = '#495057';
                        this.ctx.font = '14px Arial';
                        this.ctx.textAlign = 'center';
                        this.ctx.fillText(dataset.label, width / 2, 20);
                    }
                };

                // 绘制占位符
                this.drawPlaceholder = function() {
                    const canvas = this.ctx.canvas;
                    this.ctx.fillStyle = '#6c757d';
                    this.ctx.font = '16px Arial';
                    this.ctx.textAlign = 'center';
                    this.ctx.fillText('暂无数据', canvas.width / 2, canvas.height / 2);
                };

                // 初始渲染
                this.render();
            };

            window.chartJsInitialized = true;
            window.chartJsLoaded = true;
            console.log('Local Chart.js implementation ready');
        }

        // 立即初始化本地Chart实现
        initializeLocalChart();
    </script>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-robot"></i>
                AI运维平台
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="/dashboard">
                            <i class="bi bi-speedometer2"></i>
                            仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/hosts">
                            <i class="bi bi-server"></i>
                            主机管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/alerts">
                            <i class="bi bi-exclamation-triangle"></i>
                            告警管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/chat">
                            <i class="bi bi-chat-dots"></i>
                            AI对话
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="/stats">
                            <i class="bi bi-graph-up"></i>
                            统计报表
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            <span id="nav-username">用户</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="/profile">个人资料</a></li>
                            <li><a class="dropdown-item" href="/settings">设置</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容 -->
    <div class="container-fluid mt-4">
        <!-- 欢迎信息 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card border-0 bg-gradient-primary text-white">
                    <div class="card-body">
                        <div class="row align-items-center">
                            <div class="col-md-8">
                                <h2 class="card-title mb-2">
                                    <i class="bi bi-sun"></i>
                                    欢迎回来，<span id="welcome-username">管理员</span>！
                                </h2>
                                <p class="card-text mb-0">
                                    今天是 <span id="current-date"></span>，系统运行正常。
                                    您可以通过AI对话来管理您的基础设施。
                                </p>
                                <div class="mt-3">
                                    <div class="quick-actions d-flex flex-wrap gap-2">
                                        <span class="badge bg-light text-primary px-3 py-2" style="cursor: pointer;" onclick="window.location.href='/chat'">
                                            <i class="bi bi-chat-dots me-1"></i>AI对话
                                        </span>
                                        <span class="badge bg-light text-success px-3 py-2" style="cursor: pointer;" onclick="window.location.href='/hosts'">
                                            <i class="bi bi-server me-1"></i>主机管理
                                        </span>
                                        <span class="badge bg-light text-warning px-3 py-2" style="cursor: pointer;" onclick="window.location.href='/alerts'">
                                            <i class="bi bi-exclamation-triangle me-1"></i>告警监控
                                        </span>
                                        <span class="badge bg-light text-info px-3 py-2" style="cursor: pointer;" onclick="window.location.href='/stats'">
                                            <i class="bi bi-graph-up me-1"></i>统计报表
                                        </span>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <a href="/chat" class="btn btn-light btn-lg">
                                    <i class="bi bi-chat-dots"></i>
                                    开始AI对话
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 功能指南 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-lightbulb"></i>
                            功能指南
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <div class="guide-item" onclick="window.location.href='/chat'">
                                    <div class="guide-icon bg-primary">
                                        <i class="bi bi-chat-dots text-white"></i>
                                    </div>
                                    <div class="guide-content">
                                        <h6 class="guide-title">AI智能对话</h6>
                                        <p class="guide-desc">通过自然语言与AI助手交互，执行各种运维操作</p>
                                        <small class="text-muted">支持命令执行、状态查询、问题诊断等</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="guide-item" onclick="window.location.href='/hosts'">
                                    <div class="guide-icon bg-success">
                                        <i class="bi bi-server text-white"></i>
                                    </div>
                                    <div class="guide-content">
                                        <h6 class="guide-title">主机管理</h6>
                                        <p class="guide-desc">统一管理多台服务器，监控状态和性能</p>
                                        <small class="text-muted">SSH连接、命令执行、资源监控</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="guide-item" onclick="window.location.href='/alerts'">
                                    <div class="guide-icon bg-warning">
                                        <i class="bi bi-exclamation-triangle text-white"></i>
                                    </div>
                                    <div class="guide-content">
                                        <h6 class="guide-title">监控告警</h6>
                                        <p class="guide-desc">实时监控系统状态，及时处理告警信息</p>
                                        <small class="text-muted">告警规则、通知推送、状态管理</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="guide-item" onclick="window.location.href='/stats'">
                                    <div class="guide-icon bg-info">
                                        <i class="bi bi-graph-up text-white"></i>
                                    </div>
                                    <div class="guide-content">
                                        <h6 class="guide-title">统计报表</h6>
                                        <p class="guide-desc">丰富的数据可视化和统计分析功能</p>
                                        <small class="text-muted">性能趋势、使用统计、报告生成</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row mt-3">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    <strong>新手提示：</strong>建议从"AI智能对话"开始，您可以用自然语言描述需求，AI助手会帮您完成相应操作。
                                    例如："查看所有主机状态"、"检查磁盘使用情况"、"显示最近的告警"等。
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 统计卡片 -->
        <div class="row mb-4">
            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-primary shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-primary text-uppercase mb-1">
                                    主机总数
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="total-hosts">
                                    <div class="loading"></div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-server fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-success shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-success text-uppercase mb-1">
                                    在线主机
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="online-hosts">
                                    <div class="loading"></div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-check-circle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-warning shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-warning text-uppercase mb-1">
                                    活跃告警
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="active-alerts">
                                    <div class="loading"></div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-exclamation-triangle fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-xl-3 col-md-6 mb-4">
                <div class="card border-left-info shadow h-100 py-2">
                    <div class="card-body">
                        <div class="row no-gutters align-items-center">
                            <div class="col mr-2">
                                <div class="text-xs font-weight-bold text-info text-uppercase mb-1">
                                    今日操作
                                </div>
                                <div class="h5 mb-0 font-weight-bold text-gray-800" id="today-operations">
                                    <div class="loading"></div>
                                </div>
                            </div>
                            <div class="col-auto">
                                <i class="bi bi-activity fa-2x text-gray-300"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 图表和活动 -->
        <div class="row">
            <!-- 主机状态图表 -->
            <div class="col-xl-8 col-lg-7">
                <div class="card shadow mb-4">
                    <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-bar-chart"></i>
                            系统活动趋势
                        </h6>
                        <div class="dropdown no-arrow">
                            <a class="dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-three-dots-vertical text-gray-400"></i>
                            </a>
                            <div class="dropdown-menu dropdown-menu-right shadow">
                                <a class="dropdown-item" href="#" onclick="refreshChart()">刷新数据</a>
                                <a class="dropdown-item" href="/stats">查看详细统计</a>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-area">
                            <canvas id="activityChart" width="100%" height="40"></canvas>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最近活动 -->
            <div class="col-xl-4 col-lg-5">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-clock-history"></i>
                            最近活动
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="recent-activities">
                            <div class="text-center">
                                <div class="loading"></div>
                                <div class="mt-2">加载中...</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 快速操作 -->
        <div class="row">
            <div class="col-12">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">
                            <i class="bi bi-lightning"></i>
                            快速操作
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <a href="/hosts/add" class="btn btn-outline-primary w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                    <i class="bi bi-plus-circle fa-2x mb-2"></i>
                                    <span>添加主机</span>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/chat" class="btn btn-outline-success w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                    <i class="bi bi-chat-square-text fa-2x mb-2"></i>
                                    <span>AI对话</span>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/alerts" class="btn btn-outline-warning w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                    <i class="bi bi-bell fa-2x mb-2"></i>
                                    <span>查看告警</span>
                                </a>
                            </div>
                            <div class="col-md-3 mb-3">
                                <a href="/stats" class="btn btn-outline-info w-100 h-100 d-flex flex-column align-items-center justify-content-center py-3">
                                    <i class="bi bi-graph-up fa-2x mb-2"></i>
                                    <span>统计报表</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/static/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script>
        let activityChart;

        document.addEventListener('DOMContentLoaded', function() {
            // 检查登录状态
            if (!localStorage.getItem('access_token')) {
                window.location.href = '/login';
                return;
            }

            // 初始化页面
            initializePage();
            loadSystemOverview();
            loadRecentActivities();

            // 直接初始化图表（本地Chart.js实现已就绪）
            initializeChart();

            // 设置定时刷新
            setInterval(loadSystemOverview, 30000); // 30秒刷新一次
            setInterval(loadRecentActivities, 60000); // 1分钟刷新一次
        });

        function initializePage() {
            // 设置当前日期
            const now = new Date();
            document.getElementById('current-date').textContent = now.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long'
            });

            // 设置用户名
            const username = localStorage.getItem('username') || '管理员';
            document.getElementById('nav-username').textContent = username;
            document.getElementById('welcome-username').textContent = username;
        }

        async function loadSystemOverview() {
            try {
                const response = await API.get('/stats/overview');
                const data = await response.json();

                if (data.code === 200) {
                    const overview = data.data;

                    // 适配当前API返回的数据格式
                    document.getElementById('total-hosts').textContent = overview.hosts || 0;
                    document.getElementById('online-hosts').textContent = overview.hosts || 0; // 暂时使用总数
                    document.getElementById('active-alerts').textContent = overview.alerts || 0;
                    document.getElementById('today-operations').textContent = overview.sessions || 0; // 使用会话数作为操作数
                }
            } catch (error) {
                console.error('Failed to load system overview:', error);
                // 设置默认值
                document.getElementById('total-hosts').textContent = '0';
                document.getElementById('online-hosts').textContent = '0';
                document.getElementById('active-alerts').textContent = '0';
                document.getElementById('today-operations').textContent = '0';
            }
        }

        function initializeChart() {
            try {
                const ctx = document.getElementById('activityChart').getContext('2d');

                // 检查Chart是否可用
                if (typeof Chart === 'undefined') {
                    console.error('Chart.js not available, this should not happen with local implementation');
                    return;
                }

                console.log('Initializing chart with local Chart.js implementation');

                activityChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: '操作次数',
                        data: [],
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // 加载图表数据
            loadChartData();
            } catch (error) {
                console.error('Failed to initialize chart:', error);
            }
        }

        async function loadChartData() {
            try {
                // 检查图表是否已初始化
                if (!activityChart) {
                    console.warn('Chart not initialized, skipping data load');
                    return;
                }

                const response = await API.get('/stats/operations?time_range=24h');
                const data = await response.json();

                if (data.code === 200 && data.data.operation_trend) {
                    const trend = data.data.operation_trend;
                    const labels = trend.map(item => item.hour);
                    const counts = trend.map(item => item.count);

                    activityChart.data.labels = labels;
                    activityChart.data.datasets[0].data = counts;
                    activityChart.update();
                } else {
                    // 如果没有数据，显示默认的空图表
                    const now = new Date();
                    const labels = [];
                    const data = [];

                    for (let i = 23; i >= 0; i--) {
                        const hour = new Date(now.getTime() - i * 60 * 60 * 1000);
                        labels.push(hour.getHours() + ':00');
                        data.push(0);
                    }

                    activityChart.data.labels = labels;
                    activityChart.data.datasets[0].data = data;
                    activityChart.update();
                }
            } catch (error) {
                console.error('Failed to load chart data:', error);
            }
        }

        async function loadRecentActivities() {
            try {
                const response = await API.get('/operations?limit=10');
                const data = await response.json();

                const container = document.getElementById('recent-activities');
                
                if (data.code === 200 && data.data.operations && data.data.operations.length > 0) {
                    container.innerHTML = data.data.operations.map(op => `
                        <div class="d-flex justify-content-between align-items-center border-bottom py-2">
                            <div>
                                <div class="fw-bold">${op.operation_type}</div>
                                <small class="text-muted">${op.command || op.description || ''}</small>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-${op.status === 'success' ? 'success' : 'danger'}">${op.status}</span>
                                <br>
                                <small class="text-muted">${new Date(op.executed_at).toLocaleString()}</small>
                            </div>
                        </div>
                    `).join('');
                } else {
                    container.innerHTML = '<div class="text-center text-muted">暂无活动记录</div>';
                }
            } catch (error) {
                console.error('Failed to load recent activities:', error);
                document.getElementById('recent-activities').innerHTML = 
                    '<div class="text-center text-muted">加载失败</div>';
            }
        }

        function refreshChart() {
            loadChartData();
            Utils.showNotification('图表数据已刷新', 'success');
        }

        function logout() {
            if (confirm('确定要退出登录吗？')) {
                API.post('/auth/logout')
                    .then(() => {
                        localStorage.removeItem('access_token');
                        localStorage.removeItem('refresh_token');
                        window.location.href = '/login';
                    })
                    .catch(error => {
                        console.error('Logout failed:', error);
                        localStorage.removeItem('access_token');
                        localStorage.removeItem('refresh_token');
                        window.location.href = '/login';
                    });
            }
        }
    </script>
</body>
</html>
