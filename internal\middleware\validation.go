package middleware

import (
	"bytes"
	"fmt"
	"io"
	"strings"

	"aiops-platform/internal/model"
	"aiops-platform/internal/security"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// ValidationConfig 验证配置
type ValidationConfig struct {
	MaxBodySize      int64    // 最大请求体大小
	AllowedMimeTypes []string // 允许的MIME类型
	SanitizeInput    bool     // 是否清理输入
	StrictValidation bool     // 是否启用严格验证
}

// DefaultValidationConfig 默认验证配置
func DefaultValidationConfig() ValidationConfig {
	return ValidationConfig{
		MaxBodySize: 10 * 1024 * 1024, // 10MB
		AllowedMimeTypes: []string{
			"application/json",
			"application/x-www-form-urlencoded",
			"multipart/form-data",
			"text/plain",
		},
		SanitizeInput:    true,
		StrictValidation: true,
	}
}

// InputValidationMiddleware 输入验证中间件
func InputValidationMiddleware(config ValidationConfig, logger *logrus.Logger) gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := GetRequestID(c)

		// 1. 检查请求体大小
		if c.Request.ContentLength > config.MaxBodySize {
			logger.WithFields(logrus.Fields{
				"request_id":     requestID,
				"content_length": c.Request.ContentLength,
				"max_size":       config.MaxBodySize,
			}).Warn("Request body too large")

			AbortWithAppError(c, model.ErrCodeInvalidParams, "Request body too large", nil)
			return
		}

		// 2. 检查Content-Type
		contentType := c.GetHeader("Content-Type")
		if contentType != "" && !isAllowedMimeType(contentType, config.AllowedMimeTypes) {
			logger.WithFields(logrus.Fields{
				"request_id":   requestID,
				"content_type": contentType,
			}).Warn("Unsupported content type")

			AbortWithAppError(c, model.ErrCodeInvalidParams, "Unsupported content type", nil)
			return
		}

		// 3. 验证和清理请求体
		if c.Request.Body != nil && c.Request.ContentLength > 0 {
			if err := validateAndSanitizeBody(c, config, logger); err != nil {
				logger.WithFields(logrus.Fields{
					"request_id": requestID,
					"error":      err.Error(),
				}).Warn("Request body validation failed")

				AbortWithAppError(c, model.ErrCodeValidationFailed, "Request validation failed", err)
				return
			}
		}

		// 4. 验证查询参数
		validator := security.NewSecurityValidator()
		if err := validateQueryParams(c, validator, config, logger); err != nil {
			logger.WithFields(logrus.Fields{
				"request_id": requestID,
				"error":      err.Error(),
			}).Warn("Query parameters validation failed")

			AbortWithAppError(c, model.ErrCodeValidationFailed, "Query parameters validation failed", err)
			return
		}

		// 5. 验证请求头
		if err := validateHeaders(c, validator, config, logger); err != nil {
			logger.WithFields(logrus.Fields{
				"request_id": requestID,
				"error":      err.Error(),
			}).Warn("Headers validation failed")

			AbortWithAppError(c, model.ErrCodeValidationFailed, "Headers validation failed", err)
			return
		}

		c.Next()
	}
}

// validateAndSanitizeBody 验证和清理请求体
func validateAndSanitizeBody(c *gin.Context, config ValidationConfig, logger *logrus.Logger) error {
	// 读取请求体
	bodyBytes, err := io.ReadAll(c.Request.Body)
	if err != nil {
		return err
	}

	// 恢复请求体，以便后续处理器可以读取
	c.Request.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))

	// 基本的安全检查
	bodyStr := string(bodyBytes)
	if config.SanitizeInput {
		// 检查是否包含危险内容
		if containsDangerousContent(bodyStr) {
			return fmt.Errorf("request body contains dangerous content")
		}
	}

	return nil
}

// containsDangerousContent 检查是否包含危险内容
func containsDangerousContent(input string) bool {
	dangerousPatterns := []string{
		"<script", "javascript:", "vbscript:", "onload=", "onerror=",
		"onclick=", "SELECT ", "INSERT ", "UPDATE ", "DELETE ", "DROP ",
		"--", "/*", "*/", "xp_", "sp_",
	}

	lowerInput := strings.ToLower(input)
	for _, pattern := range dangerousPatterns {
		if strings.Contains(lowerInput, strings.ToLower(pattern)) {
			return true
		}
	}

	return false
}

// validateJSONValue 递归验证JSON值
func validateJSONValue(value interface{}, validator *security.SecurityValidator, config ValidationConfig) error {
	switch v := value.(type) {
	case string:
		if config.SanitizeInput {
			// 检查是否包含危险内容
			if _, err := validator.ValidateAndSanitize(v, "safe_string", "no_sql_injection", "no_script_tags"); err != nil {
				return err
			}
		}
	case map[string]interface{}:
		for _, val := range v {
			if err := validateJSONValue(val, validator, config); err != nil {
				return err
			}
		}
	case []interface{}:
		for _, val := range v {
			if err := validateJSONValue(val, validator, config); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateFormBody 验证表单请求体
func validateFormBody(body string, validator *security.SecurityValidator, config ValidationConfig) error {
	if !config.SanitizeInput {
		return nil
	}

	// 检查表单数据中的危险内容
	if _, err := validator.ValidateAndSanitize(body, "safe_string", "no_sql_injection", "no_script_tags"); err != nil {
		return err
	}

	return nil
}

// validateTextBody 验证文本请求体
func validateTextBody(body string, validator *security.SecurityValidator, config ValidationConfig) error {
	if !config.SanitizeInput {
		return nil
	}

	// 检查文本中的危险内容
	if _, err := validator.ValidateAndSanitize(body, "safe_string", "no_sql_injection", "no_script_tags"); err != nil {
		return err
	}

	return nil
}

// validateGenericBody 验证通用请求体
func validateGenericBody(body string, validator *security.SecurityValidator, config ValidationConfig) error {
	if !config.SanitizeInput {
		return nil
	}

	// 基本的安全检查
	if _, err := validator.ValidateAndSanitize(body, "no_script_tags"); err != nil {
		return err
	}

	return nil
}

// validateQueryParams 验证查询参数
func validateQueryParams(c *gin.Context, validator *security.SecurityValidator, config ValidationConfig, logger *logrus.Logger) error {
	if !config.SanitizeInput {
		return nil
	}

	for key, values := range c.Request.URL.Query() {
		for _, value := range values {
			// 验证参数名
			if _, err := validator.ValidateAndSanitize(key, "safe_string"); err != nil {
				return err
			}

			// 验证参数值
			if _, err := validator.ValidateAndSanitize(value, "safe_string", "no_sql_injection", "no_script_tags"); err != nil {
				return err
			}
		}
	}

	return nil
}

// validateHeaders 验证请求头
func validateHeaders(c *gin.Context, validator *security.SecurityValidator, config ValidationConfig, logger *logrus.Logger) error {
	if !config.StrictValidation {
		return nil
	}

	// 检查危险的请求头
	dangerousHeaders := []string{
		"X-Forwarded-For",
		"X-Real-IP",
		"X-Originating-IP",
		"X-Remote-IP",
		"X-Client-IP",
	}

	for _, header := range dangerousHeaders {
		if value := c.GetHeader(header); value != "" {
			// 验证IP地址格式
			if !isValidIPAddress(value) {
				logger.WithFields(logrus.Fields{
					"header": header,
					"value":  value,
				}).Warn("Invalid IP address in header")
			}
		}
	}

	// 检查User-Agent
	userAgent := c.GetHeader("User-Agent")
	if userAgent != "" {
		if _, err := validator.ValidateAndSanitize(userAgent, "safe_string", "no_script_tags"); err != nil {
			return err
		}
	}

	return nil
}

// isAllowedMimeType 检查是否为允许的MIME类型
func isAllowedMimeType(contentType string, allowedTypes []string) bool {
	// 提取主要的MIME类型（忽略参数）
	mainType := strings.Split(contentType, ";")[0]
	mainType = strings.TrimSpace(mainType)

	for _, allowed := range allowedTypes {
		if strings.EqualFold(mainType, allowed) {
			return true
		}
	}

	return false
}

// isValidIPAddress 检查是否为有效的IP地址
func isValidIPAddress(ip string) bool {
	// 简单的IP地址格式检查
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}

	for _, part := range parts {
		if len(part) == 0 || len(part) > 3 {
			return false
		}

		// 检查是否为数字
		for _, char := range part {
			if char < '0' || char > '9' {
				return false
			}
		}
	}

	return true
}

// SanitizeMiddleware 输入清理中间件
func SanitizeMiddleware() gin.HandlerFunc {
	validator := security.NewSecurityValidator()

	return func(c *gin.Context) {
		// 清理查询参数
		for _, values := range c.Request.URL.Query() {
			for i, value := range values {
				sanitized := validator.SanitizeInput(value)
				values[i] = sanitized
			}
		}

		c.Next()
	}
}

// FileUploadValidationMiddleware 文件上传验证中间件
func FileUploadValidationMiddleware(maxSize int64, allowedTypes []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否为文件上传请求
		if !strings.Contains(c.GetHeader("Content-Type"), "multipart/form-data") {
			c.Next()
			return
		}

		// 解析multipart表单
		if err := c.Request.ParseMultipartForm(maxSize); err != nil {
			AbortWithAppError(c, model.ErrCodeInvalidParams, "Failed to parse multipart form", err)
			return
		}

		// 验证上传的文件
		if c.Request.MultipartForm != nil && c.Request.MultipartForm.File != nil {
			for _, files := range c.Request.MultipartForm.File {
				for _, file := range files {
					// 检查文件大小
					if file.Size > maxSize {
						AbortWithAppError(c, model.ErrCodeInvalidParams,
							fmt.Sprintf("File %s is too large", file.Filename), nil)
						return
					}

					// 检查文件类型
					if !isAllowedFileType(file.Header.Get("Content-Type"), allowedTypes) {
						AbortWithAppError(c, model.ErrCodeInvalidParams,
							fmt.Sprintf("File type not allowed for %s", file.Filename), nil)
						return
					}

					// 验证文件名
					validator := security.NewSecurityValidator()
					if _, err := validator.ValidateAndSanitize(file.Filename, "safe_filename"); err != nil {
						AbortWithAppError(c, model.ErrCodeInvalidParams,
							fmt.Sprintf("Invalid filename: %s", file.Filename), err)
						return
					}
				}
			}
		}

		c.Next()
	}
}

// isAllowedFileType 检查是否为允许的文件类型
func isAllowedFileType(contentType string, allowedTypes []string) bool {
	for _, allowed := range allowedTypes {
		if strings.EqualFold(contentType, allowed) {
			return true
		}
	}
	return false
}
