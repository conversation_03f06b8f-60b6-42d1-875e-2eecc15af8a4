package ai

import (
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ContextType 上下文类型
type ContextType string

const (
	ContextTypeHost         ContextType = "host"         // 主机上下文
	ContextTypeCommand      ContextType = "command"      // 命令上下文
	ContextTypeWorkflow     ContextType = "workflow"     // 工作流上下文
	ContextTypeTroubleshoot ContextType = "troubleshoot" // 故障排查上下文
	ContextTypeMonitoring   ContextType = "monitoring"   // 监控上下文
)

// ConversationContext 对话上下文
type ConversationContext struct {
	SessionID   string                 `json:"session_id"`
	UserID      int64                  `json:"user_id"`
	Type        ContextType            `json:"type"`
	CurrentHost *HostContext           `json:"current_host"`
	ActiveTasks []*TaskContext         `json:"active_tasks"`
	History     []*ContextMessage      `json:"history"`
	Variables   map[string]interface{} `json:"variables"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	ExpiresAt   time.Time              `json:"expires_at"`
	mutex       sync.RWMutex           `json:"-"`
}

// HostContext 主机上下文
type HostContext struct {
	HostID       int64             `json:"host_id"`
	IPAddress    string            `json:"ip_address"`
	Hostname     string            `json:"hostname"`
	OS           string            `json:"os"`
	Status       string            `json:"status"`
	LastCommand  string            `json:"last_command"`
	WorkingDir   string            `json:"working_dir"`
	Environment  map[string]string `json:"environment"`
	Capabilities []string          `json:"capabilities"`
}

// TaskContext 任务上下文
type TaskContext struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Status      string                 `json:"status"`
	Description string                 `json:"description"`
	Steps       []*TaskStep            `json:"steps"`
	Variables   map[string]interface{} `json:"variables"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// TaskStep 任务步骤
type TaskStep struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Command    string                 `json:"command"`
	Status     string                 `json:"status"`
	Result     string                 `json:"result"`
	Error      string                 `json:"error"`
	Variables  map[string]interface{} `json:"variables"`
	ExecutedAt *time.Time             `json:"executed_at"`
}

// ContextMessage 上下文消息
type ContextMessage struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"` // user, assistant, system
	Content   string                 `json:"content"`
	Intent    string                 `json:"intent"`
	Entities  map[string]interface{} `json:"entities"`
	Timestamp time.Time              `json:"timestamp"`
}

// ContextManager 上下文管理器
type ContextManager struct {
	contexts map[string]*ConversationContext
	mutex    sync.RWMutex
	logger   *logrus.Logger
	config   *ContextConfig
}

// ContextConfig 上下文配置
type ContextConfig struct {
	MaxContexts        int           `json:"max_contexts"`
	ContextTTL         time.Duration `json:"context_ttl"`
	MaxHistorySize     int           `json:"max_history_size"`
	MaxActiveTasksSize int           `json:"max_active_tasks_size"`
	CleanupInterval    time.Duration `json:"cleanup_interval"`
	PersistContext     bool          `json:"persist_context"`
}

// DefaultContextConfig 默认上下文配置
func DefaultContextConfig() *ContextConfig {
	return &ContextConfig{
		MaxContexts:        1000,
		ContextTTL:         2 * time.Hour,
		MaxHistorySize:     50,
		MaxActiveTasksSize: 10,
		CleanupInterval:    10 * time.Minute,
		PersistContext:     true,
	}
}

// NewContextManager 创建上下文管理器
func NewContextManager(logger *logrus.Logger, config *ContextConfig) *ContextManager {
	if config == nil {
		config = DefaultContextConfig()
	}

	cm := &ContextManager{
		contexts: make(map[string]*ConversationContext),
		logger:   logger,
		config:   config,
	}

	// 启动清理协程
	go cm.startCleanupRoutine()

	return cm
}

// GetOrCreateContext 获取或创建上下文
func (cm *ContextManager) GetOrCreateContext(sessionID string, userID int64) *ConversationContext {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	if ctx, exists := cm.contexts[sessionID]; exists {
		ctx.mutex.Lock()
		ctx.UpdatedAt = time.Now()
		ctx.ExpiresAt = time.Now().Add(cm.config.ContextTTL)
		ctx.mutex.Unlock()
		return ctx
	}

	// 检查上下文数量限制
	if len(cm.contexts) >= cm.config.MaxContexts {
		cm.cleanupOldestContext()
	}

	ctx := &ConversationContext{
		SessionID:   sessionID,
		UserID:      userID,
		Type:        ContextTypeHost,
		ActiveTasks: make([]*TaskContext, 0),
		History:     make([]*ContextMessage, 0),
		Variables:   make(map[string]interface{}),
		Metadata:    make(map[string]interface{}),
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		ExpiresAt:   time.Now().Add(cm.config.ContextTTL),
	}

	cm.contexts[sessionID] = ctx
	return ctx
}

// UpdateContext 更新上下文
func (cm *ContextManager) UpdateContext(sessionID string, updates func(*ConversationContext)) error {
	cm.mutex.RLock()
	ctx, exists := cm.contexts[sessionID]
	cm.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("context not found: %s", sessionID)
	}

	ctx.mutex.Lock()
	defer ctx.mutex.Unlock()

	updates(ctx)
	ctx.UpdatedAt = time.Now()

	return nil
}

// AddMessage 添加消息到上下文
func (cm *ContextManager) AddMessage(sessionID string, msgType, content, intent string, entities map[string]interface{}) error {
	return cm.UpdateContext(sessionID, func(ctx *ConversationContext) {
		message := &ContextMessage{
			ID:        fmt.Sprintf("msg_%d", time.Now().UnixNano()),
			Type:      msgType,
			Content:   content,
			Intent:    intent,
			Entities:  entities,
			Timestamp: time.Now(),
		}

		ctx.History = append(ctx.History, message)

		// 限制历史记录大小
		if len(ctx.History) > cm.config.MaxHistorySize {
			ctx.History = ctx.History[len(ctx.History)-cm.config.MaxHistorySize:]
		}
	})
}

// SetCurrentHost 设置当前主机
func (cm *ContextManager) SetCurrentHost(sessionID string, hostContext *HostContext) error {
	return cm.UpdateContext(sessionID, func(ctx *ConversationContext) {
		ctx.CurrentHost = hostContext
		ctx.Type = ContextTypeHost
	})
}

// AddTask 添加任务
func (cm *ContextManager) AddTask(sessionID string, task *TaskContext) error {
	return cm.UpdateContext(sessionID, func(ctx *ConversationContext) {
		ctx.ActiveTasks = append(ctx.ActiveTasks, task)

		// 限制活跃任务数量
		if len(ctx.ActiveTasks) > cm.config.MaxActiveTasksSize {
			ctx.ActiveTasks = ctx.ActiveTasks[1:]
		}
	})
}

// UpdateTask 更新任务
func (cm *ContextManager) UpdateTask(sessionID, taskID string, updates func(*TaskContext)) error {
	return cm.UpdateContext(sessionID, func(ctx *ConversationContext) {
		for _, task := range ctx.ActiveTasks {
			if task.ID == taskID {
				updates(task)
				task.UpdatedAt = time.Now()
				break
			}
		}
	})
}

// SetVariable 设置变量
func (cm *ContextManager) SetVariable(sessionID, key string, value interface{}) error {
	return cm.UpdateContext(sessionID, func(ctx *ConversationContext) {
		ctx.Variables[key] = value
	})
}

// GetVariable 获取变量
func (cm *ContextManager) GetVariable(sessionID, key string) (interface{}, bool) {
	cm.mutex.RLock()
	ctx, exists := cm.contexts[sessionID]
	cm.mutex.RUnlock()

	if !exists {
		return nil, false
	}

	ctx.mutex.RLock()
	defer ctx.mutex.RUnlock()

	value, exists := ctx.Variables[key]
	return value, exists
}

// GetContextSummary 获取上下文摘要
func (cm *ContextManager) GetContextSummary(sessionID string) string {
	cm.mutex.RLock()
	ctx, exists := cm.contexts[sessionID]
	cm.mutex.RUnlock()

	if !exists {
		return "无活跃上下文"
	}

	ctx.mutex.RLock()
	defer ctx.mutex.RUnlock()

	var summary strings.Builder
	summary.WriteString(fmt.Sprintf("📋 **当前上下文摘要**\n\n"))

	// 当前主机信息
	if ctx.CurrentHost != nil {
		summary.WriteString(fmt.Sprintf("🖥️ **当前主机**: %s (%s)\n", ctx.CurrentHost.Hostname, ctx.CurrentHost.IPAddress))
		summary.WriteString(fmt.Sprintf("📁 **工作目录**: %s\n", ctx.CurrentHost.WorkingDir))
		if ctx.CurrentHost.LastCommand != "" {
			summary.WriteString(fmt.Sprintf("⚡ **上次命令**: %s\n", ctx.CurrentHost.LastCommand))
		}
	}

	// 活跃任务
	if len(ctx.ActiveTasks) > 0 {
		summary.WriteString(fmt.Sprintf("\n📝 **活跃任务** (%d个):\n", len(ctx.ActiveTasks)))
		for i, task := range ctx.ActiveTasks {
			summary.WriteString(fmt.Sprintf("%d. %s (%s)\n", i+1, task.Description, task.Status))
		}
	}

	// 重要变量
	if len(ctx.Variables) > 0 {
		summary.WriteString(fmt.Sprintf("\n🔧 **上下文变量** (%d个):\n", len(ctx.Variables)))
		for key, value := range ctx.Variables {
			summary.WriteString(fmt.Sprintf("- %s: %v\n", key, value))
		}
	}

	return summary.String()
}

// startCleanupRoutine 启动清理协程
func (cm *ContextManager) startCleanupRoutine() {
	ticker := time.NewTicker(cm.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		cm.cleanupExpiredContexts()
	}
}

// cleanupExpiredContexts 清理过期上下文
func (cm *ContextManager) cleanupExpiredContexts() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	now := time.Now()
	expiredSessions := make([]string, 0)

	for sessionID, ctx := range cm.contexts {
		if now.After(ctx.ExpiresAt) {
			expiredSessions = append(expiredSessions, sessionID)
		}
	}

	for _, sessionID := range expiredSessions {
		delete(cm.contexts, sessionID)
		cm.logger.WithField("session_id", sessionID).Info("Context expired and cleaned up")
	}

	if len(expiredSessions) > 0 {
		cm.logger.WithField("cleaned_count", len(expiredSessions)).Info("Cleaned up expired contexts")
	}
}

// cleanupOldestContext 清理最旧的上下文
func (cm *ContextManager) cleanupOldestContext() {
	var oldestSessionID string
	var oldestTime time.Time

	for sessionID, ctx := range cm.contexts {
		if oldestSessionID == "" || ctx.CreatedAt.Before(oldestTime) {
			oldestSessionID = sessionID
			oldestTime = ctx.CreatedAt
		}
	}

	if oldestSessionID != "" {
		delete(cm.contexts, oldestSessionID)
		cm.logger.WithField("session_id", oldestSessionID).Info("Oldest context cleaned up due to limit")
	}
}

// ExportContext 导出上下文（用于持久化）
func (cm *ContextManager) ExportContext(sessionID string) ([]byte, error) {
	cm.mutex.RLock()
	ctx, exists := cm.contexts[sessionID]
	cm.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("context not found: %s", sessionID)
	}

	ctx.mutex.RLock()
	defer ctx.mutex.RUnlock()

	return json.Marshal(ctx)
}

// ImportContext 导入上下文（用于恢复）
func (cm *ContextManager) ImportContext(data []byte) error {
	var ctx ConversationContext
	if err := json.Unmarshal(data, &ctx); err != nil {
		return err
	}

	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	cm.contexts[ctx.SessionID] = &ctx
	return nil
}
