package resilience

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// DegradationLevel 降级级别
type DegradationLevel int

const (
	LevelNormal DegradationLevel = iota
	LevelPartial
	LevelSevere
	LevelEmergency
)

func (l DegradationLevel) String() string {
	switch l {
	case LevelNormal:
		return "NORMAL"
	case LevelPartial:
		return "PARTIAL"
	case LevelSevere:
		return "SEVERE"
	case LevelEmergency:
		return "EMERGENCY"
	default:
		return "UNKNOWN"
	}
}

// DegradationConfig 降级配置
type DegradationConfig struct {
	ServiceName     string                           `json:"service_name"`
	Enabled         bool                             `json:"enabled"`
	CheckInterval   time.Duration                    `json:"check_interval"`
	Thresholds      map[DegradationLevel]Threshold   `json:"thresholds"`
	Strategies      map[DegradationLevel]Strategy    `json:"strategies"`
	OnLevelChange   func(from, to DegradationLevel)  `json:"-"`
	HealthCheckers  []HealthChecker                  `json:"-"`
}

// Threshold 降级阈值
type Threshold struct {
	ErrorRate       float64 `json:"error_rate"`        // 错误率阈值
	ResponseTime    time.Duration `json:"response_time"` // 响应时间阈值
	CPUUsage        float64 `json:"cpu_usage"`         // CPU使用率阈值
	MemoryUsage     float64 `json:"memory_usage"`      // 内存使用率阈值
	ActiveRequests  int     `json:"active_requests"`   // 活跃请求数阈值
}

// Strategy 降级策略
type Strategy struct {
	DisableFeatures    []string      `json:"disable_features"`     // 禁用的功能
	CacheOnly          bool          `json:"cache_only"`           // 仅使用缓存
	ReadOnly           bool          `json:"read_only"`            // 只读模式
	RateLimitFactor    float64       `json:"rate_limit_factor"`    // 限流因子
	TimeoutReduction   time.Duration `json:"timeout_reduction"`    // 超时时间减少
	FallbackResponse   interface{}   `json:"fallback_response"`    // 降级响应
	CustomHandler      func(ctx context.Context, request interface{}) (interface{}, error) `json:"-"`
}

// HealthChecker 健康检查器
type HealthChecker interface {
	Check(ctx context.Context) HealthStatus
	Name() string
}

// HealthStatus 健康状态
type HealthStatus struct {
	Healthy      bool                   `json:"healthy"`
	ErrorRate    float64                `json:"error_rate"`
	ResponseTime time.Duration          `json:"response_time"`
	CPUUsage     float64                `json:"cpu_usage"`
	MemoryUsage  float64                `json:"memory_usage"`
	ActiveRequests int                  `json:"active_requests"`
	Details      map[string]interface{} `json:"details"`
}

// GracefulDegradation 优雅降级管理器
type GracefulDegradation struct {
	config         DegradationConfig
	currentLevel   DegradationLevel
	lastCheck      time.Time
	healthStatus   HealthStatus
	featureFlags   map[string]bool
	mutex          sync.RWMutex
	logger         *logrus.Logger
	stopChan       chan struct{}
	running        bool
}

// NewGracefulDegradation 创建优雅降级管理器
func NewGracefulDegradation(config DegradationConfig, logger *logrus.Logger) *GracefulDegradation {
	// 设置默认值
	if config.CheckInterval <= 0 {
		config.CheckInterval = 30 * time.Second
	}

	// 设置默认阈值
	if config.Thresholds == nil {
		config.Thresholds = map[DegradationLevel]Threshold{
			LevelPartial: {
				ErrorRate:      0.05, // 5%
				ResponseTime:   2 * time.Second,
				CPUUsage:       0.7,  // 70%
				MemoryUsage:    0.8,  // 80%
				ActiveRequests: 1000,
			},
			LevelSevere: {
				ErrorRate:      0.15, // 15%
				ResponseTime:   5 * time.Second,
				CPUUsage:       0.85, // 85%
				MemoryUsage:    0.9,  // 90%
				ActiveRequests: 2000,
			},
			LevelEmergency: {
				ErrorRate:      0.3,  // 30%
				ResponseTime:   10 * time.Second,
				CPUUsage:       0.95, // 95%
				MemoryUsage:    0.95, // 95%
				ActiveRequests: 5000,
			},
		}
	}

	// 设置默认策略
	if config.Strategies == nil {
		config.Strategies = map[DegradationLevel]Strategy{
			LevelPartial: {
				DisableFeatures:  []string{"analytics", "recommendations"},
				RateLimitFactor:  0.8,
				TimeoutReduction: 1 * time.Second,
			},
			LevelSevere: {
				DisableFeatures:  []string{"analytics", "recommendations", "notifications"},
				CacheOnly:        true,
				RateLimitFactor:  0.5,
				TimeoutReduction: 3 * time.Second,
			},
			LevelEmergency: {
				DisableFeatures:  []string{"analytics", "recommendations", "notifications", "reports"},
				ReadOnly:         true,
				RateLimitFactor:  0.2,
				TimeoutReduction: 5 * time.Second,
				FallbackResponse: map[string]interface{}{"status": "service_unavailable"},
			},
		}
	}

	gd := &GracefulDegradation{
		config:       config,
		currentLevel: LevelNormal,
		featureFlags: make(map[string]bool),
		logger:       logger,
		stopChan:     make(chan struct{}),
	}

	return gd
}

// Start 启动降级管理器
func (gd *GracefulDegradation) Start(ctx context.Context) error {
	if !gd.config.Enabled {
		gd.logger.Info("Graceful degradation is disabled")
		return nil
	}

	gd.mutex.Lock()
	if gd.running {
		gd.mutex.Unlock()
		return fmt.Errorf("graceful degradation is already running")
	}
	gd.running = true
	gd.mutex.Unlock()

	// 启动健康检查协程
	go gd.healthCheckLoop(ctx)

	gd.logger.WithField("service", gd.config.ServiceName).Info("Graceful degradation started")
	return nil
}

// Stop 停止降级管理器
func (gd *GracefulDegradation) Stop() error {
	gd.mutex.Lock()
	defer gd.mutex.Unlock()

	if !gd.running {
		return nil
	}

	close(gd.stopChan)
	gd.running = false

	gd.logger.WithField("service", gd.config.ServiceName).Info("Graceful degradation stopped")
	return nil
}

// healthCheckLoop 健康检查循环
func (gd *GracefulDegradation) healthCheckLoop(ctx context.Context) {
	ticker := time.NewTicker(gd.config.CheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-gd.stopChan:
			return
		case <-ticker.C:
			gd.performHealthCheck(ctx)
		}
	}
}

// performHealthCheck 执行健康检查
func (gd *GracefulDegradation) performHealthCheck(ctx context.Context) {
	gd.mutex.Lock()
	defer gd.mutex.Unlock()

	// 收集健康状态
	var totalErrorRate float64
	var maxResponseTime time.Duration
	var maxCPUUsage float64
	var maxMemoryUsage float64
	var totalActiveRequests int

	for _, checker := range gd.config.HealthCheckers {
		status := checker.Check(ctx)
		
		totalErrorRate += status.ErrorRate
		if status.ResponseTime > maxResponseTime {
			maxResponseTime = status.ResponseTime
		}
		if status.CPUUsage > maxCPUUsage {
			maxCPUUsage = status.CPUUsage
		}
		if status.MemoryUsage > maxMemoryUsage {
			maxMemoryUsage = status.MemoryUsage
		}
		totalActiveRequests += status.ActiveRequests
	}

	// 计算平均错误率
	if len(gd.config.HealthCheckers) > 0 {
		totalErrorRate /= float64(len(gd.config.HealthCheckers))
	}

	gd.healthStatus = HealthStatus{
		Healthy:        true,
		ErrorRate:      totalErrorRate,
		ResponseTime:   maxResponseTime,
		CPUUsage:       maxCPUUsage,
		MemoryUsage:    maxMemoryUsage,
		ActiveRequests: totalActiveRequests,
	}

	// 确定降级级别
	newLevel := gd.determineDegradationLevel()
	if newLevel != gd.currentLevel {
		gd.changeDegradationLevel(newLevel)
	}

	gd.lastCheck = time.Now()
}

// determineDegradationLevel 确定降级级别
func (gd *GracefulDegradation) determineDegradationLevel() DegradationLevel {
	status := gd.healthStatus

	// 检查紧急级别
	if threshold, exists := gd.config.Thresholds[LevelEmergency]; exists {
		if gd.exceedsThreshold(status, threshold) {
			return LevelEmergency
		}
	}

	// 检查严重级别
	if threshold, exists := gd.config.Thresholds[LevelSevere]; exists {
		if gd.exceedsThreshold(status, threshold) {
			return LevelSevere
		}
	}

	// 检查部分级别
	if threshold, exists := gd.config.Thresholds[LevelPartial]; exists {
		if gd.exceedsThreshold(status, threshold) {
			return LevelPartial
		}
	}

	return LevelNormal
}

// exceedsThreshold 检查是否超过阈值
func (gd *GracefulDegradation) exceedsThreshold(status HealthStatus, threshold Threshold) bool {
	return status.ErrorRate >= threshold.ErrorRate ||
		status.ResponseTime >= threshold.ResponseTime ||
		status.CPUUsage >= threshold.CPUUsage ||
		status.MemoryUsage >= threshold.MemoryUsage ||
		status.ActiveRequests >= threshold.ActiveRequests
}

// changeDegradationLevel 改变降级级别
func (gd *GracefulDegradation) changeDegradationLevel(newLevel DegradationLevel) {
	oldLevel := gd.currentLevel
	gd.currentLevel = newLevel

	// 应用降级策略
	if strategy, exists := gd.config.Strategies[newLevel]; exists {
		gd.applyStrategy(strategy)
	}

	gd.logger.WithFields(logrus.Fields{
		"service":    gd.config.ServiceName,
		"from_level": oldLevel.String(),
		"to_level":   newLevel.String(),
		"health":     gd.healthStatus,
	}).Warn("Degradation level changed")

	// 调用回调函数
	if gd.config.OnLevelChange != nil {
		gd.config.OnLevelChange(oldLevel, newLevel)
	}
}

// applyStrategy 应用降级策略
func (gd *GracefulDegradation) applyStrategy(strategy Strategy) {
	// 禁用功能
	for _, feature := range strategy.DisableFeatures {
		gd.featureFlags[feature] = false
	}

	// 其他策略的应用由具体的服务实现
}

// IsFeatureEnabled 检查功能是否启用
func (gd *GracefulDegradation) IsFeatureEnabled(feature string) bool {
	gd.mutex.RLock()
	defer gd.mutex.RUnlock()

	if enabled, exists := gd.featureFlags[feature]; exists {
		return enabled
	}
	return true // 默认启用
}

// GetCurrentLevel 获取当前降级级别
func (gd *GracefulDegradation) GetCurrentLevel() DegradationLevel {
	gd.mutex.RLock()
	defer gd.mutex.RUnlock()
	return gd.currentLevel
}

// GetHealthStatus 获取健康状态
func (gd *GracefulDegradation) GetHealthStatus() HealthStatus {
	gd.mutex.RLock()
	defer gd.mutex.RUnlock()
	return gd.healthStatus
}

// GetCurrentStrategy 获取当前策略
func (gd *GracefulDegradation) GetCurrentStrategy() (Strategy, bool) {
	gd.mutex.RLock()
	defer gd.mutex.RUnlock()

	strategy, exists := gd.config.Strategies[gd.currentLevel]
	return strategy, exists
}

// ShouldUseCache 是否应该使用缓存
func (gd *GracefulDegradation) ShouldUseCache() bool {
	strategy, exists := gd.GetCurrentStrategy()
	return exists && strategy.CacheOnly
}

// IsReadOnly 是否为只读模式
func (gd *GracefulDegradation) IsReadOnly() bool {
	strategy, exists := gd.GetCurrentStrategy()
	return exists && strategy.ReadOnly
}

// GetRateLimitFactor 获取限流因子
func (gd *GracefulDegradation) GetRateLimitFactor() float64 {
	strategy, exists := gd.GetCurrentStrategy()
	if exists && strategy.RateLimitFactor > 0 {
		return strategy.RateLimitFactor
	}
	return 1.0
}

// GetTimeoutReduction 获取超时时间减少量
func (gd *GracefulDegradation) GetTimeoutReduction() time.Duration {
	strategy, exists := gd.GetCurrentStrategy()
	if exists {
		return strategy.TimeoutReduction
	}
	return 0
}

// GetFallbackResponse 获取降级响应
func (gd *GracefulDegradation) GetFallbackResponse() interface{} {
	strategy, exists := gd.GetCurrentStrategy()
	if exists {
		return strategy.FallbackResponse
	}
	return nil
}

// ExecuteWithDegradation 带降级的执行
func (gd *GracefulDegradation) ExecuteWithDegradation(ctx context.Context, request interface{}, normalHandler func(ctx context.Context, request interface{}) (interface{}, error)) (interface{}, error) {
	// 检查是否为只读模式
	if gd.IsReadOnly() {
		if fallback := gd.GetFallbackResponse(); fallback != nil {
			return fallback, nil
		}
		return nil, fmt.Errorf("service is in read-only mode")
	}

	// 检查是否有自定义处理器
	strategy, exists := gd.GetCurrentStrategy()
	if exists && strategy.CustomHandler != nil {
		return strategy.CustomHandler(ctx, request)
	}

	// 调整超时时间
	if reduction := gd.GetTimeoutReduction(); reduction > 0 {
		deadline, ok := ctx.Deadline()
		if ok {
			newDeadline := deadline.Add(-reduction)
			if newDeadline.After(time.Now()) {
				var cancel context.CancelFunc
				ctx, cancel = context.WithDeadline(ctx, newDeadline)
				defer cancel()
			}
		}
	}

	// 执行正常处理
	return normalHandler(ctx, request)
}
