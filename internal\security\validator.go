package security

import (
	"fmt"
	"html"
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
)

// SecurityValidator 安全验证器
type SecurityValidator struct {
	validator *validator.Validate
}

// NewSecurityValidator 创建安全验证器
func NewSecurityValidator() *SecurityValidator {
	v := validator.New()

	return &SecurityValidator{
		validator: v,
	}
}

// Validate 验证结构体
func (sv *SecurityValidator) Validate(s interface{}) error {
	return sv.validator.Struct(s)
}

// SanitizeInput 清理输入数据
func (sv *SecurityValidator) SanitizeInput(input string) string {
	// 1. 去除前后空白
	input = strings.TrimSpace(input)

	// 2. HTML转义
	input = html.EscapeString(input)

	// 3. 移除潜在的脚本标签
	input = removeScriptTags(input)

	// 4. 移除SQL注入关键词
	input = removeSQLInjectionKeywords(input)

	return input
}

// ValidateAndSanitize 验证并清理输入
func (sv *SecurityValidator) ValidateAndSanitize(input string, rules ...string) (string, error) {
	// 先清理
	sanitized := sv.SanitizeInput(input)

	// 再验证
	for _, rule := range rules {
		if err := sv.validateByRule(sanitized, rule); err != nil {
			return "", err
		}
	}

	return sanitized, nil
}

// validateByRule 根据规则验证
func (sv *SecurityValidator) validateByRule(input, rule string) error {
	switch rule {
	case "safe_string":
		return sv.ValidateSafeString(input)
	case "no_sql_injection":
		return sv.ValidateNoSQLInjection(input)
	case "safe_filename":
		return sv.ValidateSafeFilename(input)
	case "no_script_tags":
		return sv.ValidateNoScriptTags(input)
	default:
		return fmt.Errorf("unknown validation rule: %s", rule)
	}
}

// ValidateSafeString 验证安全字符串
func (sv *SecurityValidator) ValidateSafeString(input string) error {
	// 检查是否包含危险字符
	dangerousChars := []string{"<", ">", "\"", "'", "&", ";"}
	for _, char := range dangerousChars {
		if strings.Contains(input, char) {
			return fmt.Errorf("contains dangerous character: %s", char)
		}
	}

	return nil
}

// ValidateNoSQLInjection 验证SQL注入
func (sv *SecurityValidator) ValidateNoSQLInjection(input string) error {
	// SQL注入关键词
	sqlKeywords := []string{
		"SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER",
		"UNION", "OR", "AND", "WHERE", "FROM", "JOIN", "EXEC", "EXECUTE",
		"--", "/*", "*/", "xp_", "sp_", "@@",
	}

	upperStr := strings.ToUpper(input)
	for _, keyword := range sqlKeywords {
		if strings.Contains(upperStr, keyword) {
			return fmt.Errorf("contains SQL injection keyword: %s", keyword)
		}
	}

	return nil
}

// ValidateSafeFilename 验证安全文件名
func (sv *SecurityValidator) ValidateSafeFilename(input string) error {
	// 文件名不能包含路径分隔符和特殊字符
	invalidChars := []string{"/", "\\", ":", "*", "?", "\"", "<", ">", "|", ".."}
	for _, char := range invalidChars {
		if strings.Contains(input, char) {
			return fmt.Errorf("filename contains invalid character: %s", char)
		}
	}

	// 不能以点开头（隐藏文件）
	if strings.HasPrefix(input, ".") {
		return fmt.Errorf("filename cannot start with dot")
	}

	return nil
}

// ValidateNoScriptTags 验证无脚本标签
func (sv *SecurityValidator) ValidateNoScriptTags(input string) error {
	scriptPattern := regexp.MustCompile(`(?i)<\s*script[^>]*>.*?<\s*/\s*script\s*>`)
	if scriptPattern.MatchString(input) {
		return fmt.Errorf("contains script tags")
	}

	return nil
}

// removeScriptTags 移除脚本标签
func removeScriptTags(input string) string {
	scriptPattern := regexp.MustCompile(`(?i)<\s*script[^>]*>.*?<\s*/\s*script\s*>`)
	return scriptPattern.ReplaceAllString(input, "")
}

// removeSQLInjectionKeywords 移除SQL注入关键词
func removeSQLInjectionKeywords(input string) string {
	sqlKeywords := []string{
		"SELECT", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE", "ALTER",
		"UNION", "OR", "AND", "WHERE", "FROM", "JOIN", "EXEC", "EXECUTE",
		"--", "/*", "*/", "xp_", "sp_", "@@",
	}

	result := input
	for _, keyword := range sqlKeywords {
		pattern := regexp.MustCompile(`(?i)\b` + regexp.QuoteMeta(keyword) + `\b`)
		result = pattern.ReplaceAllString(result, "")
	}

	return result
}
