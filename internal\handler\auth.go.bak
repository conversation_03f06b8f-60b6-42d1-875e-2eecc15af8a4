package handler

import (
	"net/http"

	"aiops-platform/internal/auth"
	"aiops-platform/internal/middleware"
	"aiops-platform/internal/model"
	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
	Remember bool   `json:"remember_me"`
}

// RegisterRequest 注册请求
type RegisterRequest struct {
	Username   string `json:"username" binding:"required,min=3,max=50"`
	Password   string `json:"password" binding:"required,min=8"`
	Email      string `json:"email" binding:"required,email"`
	FullName   string `json:"full_name" binding:"required,min=2,max=100"`
	Phone      string `json:"phone" binding:"omitempty,min=10,max=20"`
	Department string `json:"department" binding:"omitempty,max=50"`
}

// LoginResponse 登录响应
type LoginResponse struct {
	AccessToken  string              `json:"access_token"`
	RefreshToken string              `json:"refresh_token"`
	ExpiresIn    int64               `json:"expires_in"`
	TokenType    string              `json:"token_type"`
	User         *model.UserResponse `json:"user"`
}

// Login 用户登录
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 记录登录尝试
	h.logger.WithFields(logrus.Fields{
		"username":   req.Username,
		"client_ip":  c.ClientIP(),
		"user_agent": c.Request.UserAgent(),
	}).Info("Login attempt")

	// TODO: 调用认证服务 (暂时注释)
	// loginReq := &service.LoginRequest{
	// 	Username: req.Username,
	// 	Password: req.Password,
	// 	ClientIP: c.ClientIP(),
	// 	UserAgent: c.Request.UserAgent(),
	// }

	// result, err := h.services.Auth.Login(loginReq)
	// if err != nil {
	if true { // 暂时返回错误
		h.logger.WithFields(logrus.Fields{
			"username": req.Username,
			"error":    err.Error(),
		}).Warn("Login failed")

		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Invalid username or password",
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"user_id":  result.User.ID,
		"username": result.User.Username,
	}).Info("Login successful")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Login successful",
		"data": LoginResponse{
			AccessToken:  result.AccessToken,
			RefreshToken: result.RefreshToken,
			ExpiresIn:    result.ExpiresIn,
			TokenType:    result.TokenType,
			User:         result.User,
		},
	})
}

// Register 用户注册
func (h *AuthHandler) Register(c *gin.Context) {
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 创建用户请求
	createReq := &service.CreateUserRequest{
		Username:   req.Username,
		Password:   req.Password,
		Email:      req.Email,
		FullName:   req.FullName,
		Role:       "viewer", // 默认角色
		Phone:      req.Phone,
		Department: req.Department,
	}

	user, err := h.services.User.CreateUser(createReq)
	if err != nil {
		h.logger.WithFields(logrus.Fields{
			"username": req.Username,
			"email":    req.Email,
			"error":    err.Error(),
		}).Error("Registration failed")

		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	}).Info("User registered successfully")

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "User registered successfully",
		"data":    user,
	})
}

// RefreshToken 刷新Token
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req struct {
		RefreshToken string `json:"refresh_token" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	result, err := h.services.Auth.RefreshToken(req.RefreshToken)
	if err != nil {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Invalid refresh token",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Token refreshed successfully",
		"data":    result,
	})
}

// Logout 用户登出
func (h *AuthHandler) Logout(c *gin.Context) {
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Authentication required",
		})
		return
	}

	sessionID, _ := middleware.GetSessionIDFromContext(c)

	if err := h.services.Auth.Logout(userID, sessionID); err != nil {
		h.logger.WithFields(logrus.Fields{
			"user_id": userID,
			"error":   err.Error(),
		}).Error("Logout failed")

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Logout failed",
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"user_id": userID,
	}).Info("User logged out successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Logout successful",
	})
}

// GetProfile 获取用户资料
func (h *AuthHandler) GetProfile(c *gin.Context) {
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Authentication required",
		})
		return
	}

	user, err := h.services.User.GetUserByID(userID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"code":    404,
			"message": "User not found",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Profile retrieved successfully",
		"data":    user,
	})
}

// UpdateProfile 更新用户资料
func (h *AuthHandler) UpdateProfile(c *gin.Context) {
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Authentication required",
		})
		return
	}

	var req service.UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 普通用户不能修改角色
	req.Role = ""
	req.IsActive = nil

	user, err := h.services.User.UpdateUser(userID, &req)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Profile updated successfully",
		"data":    user,
	})
}

// ChangePassword 修改密码
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Authentication required",
		})
		return
	}

	var req service.ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	if err := h.services.User.ChangePassword(userID, &req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"user_id": userID,
	}).Info("Password changed successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Password changed successfully",
	})
}

// GetJWTManager 获取JWT管理器（用于中间件）
func (h *AuthHandler) GetJWTManager() *auth.JWTManager {
	// 这里需要从认证服务获取JWT管理器
	// 暂时返回nil，后续实现
	return nil
}
