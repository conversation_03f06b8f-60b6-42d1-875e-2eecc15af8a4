package service

import (
	"context"
	"fmt"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
)

// AlertChannel 告警通道接口
type AlertChannel interface {
	Send(ctx context.Context, alert *model.Alert) error
	GetName() string
	IsEnabled() bool
}

// WebSocketAlertChannel WebSocket告警通道
type WebSocketAlertChannel struct {
	wsManager *WebSocketManager
	logger    *logrus.Logger
	enabled   bool
}

func (wac *WebSocketAlertChannel) Send(ctx context.Context, alert *model.Alert) error {
	if wac.wsManager == nil {
		return fmt.Errorf("websocket manager not available")
	}

	message := &WSMessage{
		Type: "alert_notification",
		Data: map[string]interface{}{
			"alert_id":   alert.ID,
			"host_id":    alert.HostID,
			"title":      alert.Title,
			"message":    alert.Message,
			"level":      alert.Level,
			"source":     alert.Source,
			"status":     alert.Status,
			"alert_time": alert.AlertTime,
		},
		Timestamp: time.Now(),
	}

	return wac.wsManager.BroadcastToAll(message)
}

func (wac *WebSocketAlertChannel) GetName() string {
	return "websocket"
}

func (wac *WebSocketAlertChannel) IsEnabled() bool {
	return wac.enabled
}

// LogAlertChannel 日志告警通道
type LogAlertChannel struct {
	logger  *logrus.Logger
	enabled bool
}

func (lac *LogAlertChannel) Send(ctx context.Context, alert *model.Alert) error {
	lac.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"host_id":  alert.HostID,
		"level":    alert.Level,
		"source":   alert.Source,
		"status":   alert.Status,
	}).Warn(fmt.Sprintf("ALERT: %s - %s", alert.Title, alert.Message))

	return nil
}

func (lac *LogAlertChannel) GetName() string {
	return "log"
}

func (lac *LogAlertChannel) IsEnabled() bool {
	return lac.enabled
}

// EmailAlertChannel 邮件告警通道
type EmailAlertChannel struct {
	logger  *logrus.Logger
	enabled bool
	config  *EmailConfig
}

type EmailConfig struct {
	SMTPHost     string   `json:"smtp_host"`
	SMTPPort     int      `json:"smtp_port"`
	Username     string   `json:"username"`
	Password     string   `json:"password"`
	FromAddress  string   `json:"from_address"`
	ToAddresses  []string `json:"to_addresses"`
	UseTLS       bool     `json:"use_tls"`
	Subject      string   `json:"subject"`
	TemplateFile string   `json:"template_file"`
}

func (eac *EmailAlertChannel) Send(ctx context.Context, alert *model.Alert) error {
	// 模拟邮件发送
	eac.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"host_id":  alert.HostID,
		"level":    alert.Level,
		"title":    alert.Title,
	}).Info("Email alert sent (simulated)")

	// 在实际实现中，这里应该使用SMTP发送邮件
	// 可以使用 net/smtp 包或第三方库如 gomail

	return nil
}

func (eac *EmailAlertChannel) GetName() string {
	return "email"
}

func (eac *EmailAlertChannel) IsEnabled() bool {
	return eac.enabled
}

// SlackAlertChannel Slack告警通道
type SlackAlertChannel struct {
	logger     *logrus.Logger
	enabled    bool
	webhookURL string
}

func (sac *SlackAlertChannel) Send(ctx context.Context, alert *model.Alert) error {
	// 模拟Slack通知发送
	sac.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"host_id":  alert.HostID,
		"level":    alert.Level,
		"title":    alert.Title,
	}).Info("Slack alert sent (simulated)")

	// 在实际实现中，这里应该向Slack Webhook发送HTTP请求
	// 可以使用标准的HTTP客户端发送JSON格式的消息

	return nil
}

func (sac *SlackAlertChannel) GetName() string {
	return "slack"
}

func (sac *SlackAlertChannel) IsEnabled() bool {
	return sac.enabled
}

// DingTalkAlertChannel 钉钉告警通道
type DingTalkAlertChannel struct {
	logger     *logrus.Logger
	enabled    bool
	webhookURL string
	secret     string
}

func (dtac *DingTalkAlertChannel) Send(ctx context.Context, alert *model.Alert) error {
	// 模拟钉钉通知发送
	dtac.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"host_id":  alert.HostID,
		"level":    alert.Level,
		"title":    alert.Title,
	}).Info("DingTalk alert sent (simulated)")

	// 在实际实现中，这里应该向钉钉机器人Webhook发送HTTP请求
	// 需要按照钉钉的消息格式构造JSON数据

	return nil
}

func (dtac *DingTalkAlertChannel) GetName() string {
	return "dingtalk"
}

func (dtac *DingTalkAlertChannel) IsEnabled() bool {
	return dtac.enabled
}

// WeChatAlertChannel 企业微信告警通道
type WeChatAlertChannel struct {
	logger     *logrus.Logger
	enabled    bool
	webhookURL string
}

func (wcac *WeChatAlertChannel) Send(ctx context.Context, alert *model.Alert) error {
	// 模拟企业微信通知发送
	wcac.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"host_id":  alert.HostID,
		"level":    alert.Level,
		"title":    alert.Title,
	}).Info("WeChat Work alert sent (simulated)")

	// 在实际实现中，这里应该向企业微信机器人Webhook发送HTTP请求
	// 需要按照企业微信的消息格式构造JSON数据

	return nil
}

func (wcac *WeChatAlertChannel) GetName() string {
	return "wechat"
}

func (wcac *WeChatAlertChannel) IsEnabled() bool {
	return wcac.enabled
}

// SMSAlertChannel 短信告警通道
type SMSAlertChannel struct {
	logger  *logrus.Logger
	enabled bool
	config  *SMSConfig
}

type SMSConfig struct {
	Provider     string   `json:"provider"` // aliyun, tencent, etc.
	AccessKey    string   `json:"access_key"`
	SecretKey    string   `json:"secret_key"`
	SignName     string   `json:"sign_name"`
	TemplateCode string   `json:"template_code"`
	PhoneNumbers []string `json:"phone_numbers"`
}

func (sac *SMSAlertChannel) Send(ctx context.Context, alert *model.Alert) error {
	// 模拟短信发送
	sac.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"host_id":  alert.HostID,
		"level":    alert.Level,
		"title":    alert.Title,
	}).Info("SMS alert sent (simulated)")

	// 在实际实现中，这里应该调用短信服务商的API发送短信
	// 如阿里云短信、腾讯云短信等

	return nil
}

func (sac *SMSAlertChannel) GetName() string {
	return "sms"
}

func (sac *SMSAlertChannel) IsEnabled() bool {
	return sac.enabled
}

// WebhookAlertChannel 自定义Webhook告警通道
type WebhookAlertChannel struct {
	logger  *logrus.Logger
	enabled bool
	config  *WebhookConfig
}

type WebhookConfig struct {
	URL     string            `json:"url"`
	Method  string            `json:"method"`
	Headers map[string]string `json:"headers"`
	Timeout time.Duration     `json:"timeout"`
	Retries int               `json:"retries"`
}

func (wac *WebhookAlertChannel) Send(ctx context.Context, alert *model.Alert) error {
	// 模拟Webhook发送
	wac.logger.WithFields(logrus.Fields{
		"alert_id":    alert.ID,
		"host_id":     alert.HostID,
		"level":       alert.Level,
		"title":       alert.Title,
		"webhook_url": wac.config.URL,
	}).Info("Webhook alert sent (simulated)")

	// 在实际实现中，这里应该向指定的URL发送HTTP请求
	// 可以使用标准的HTTP客户端，支持重试和超时机制

	return nil
}

func (wac *WebhookAlertChannel) GetName() string {
	return "webhook"
}

func (wac *WebhookAlertChannel) IsEnabled() bool {
	return wac.enabled
}

// AlertChannelFactory 告警通道工厂
type AlertChannelFactory struct {
	logger *logrus.Logger
}

func NewAlertChannelFactory(logger *logrus.Logger) *AlertChannelFactory {
	return &AlertChannelFactory{
		logger: logger,
	}
}

func (acf *AlertChannelFactory) CreateChannel(channelType string, config map[string]interface{}) (AlertChannel, error) {
	switch channelType {
	case "websocket":
		return &WebSocketAlertChannel{
			logger:  acf.logger,
			enabled: true,
		}, nil
	case "log":
		return &LogAlertChannel{
			logger:  acf.logger,
			enabled: true,
		}, nil
	case "email":
		return &EmailAlertChannel{
			logger:  acf.logger,
			enabled: false, // 默认禁用，需要配置
		}, nil
	case "slack":
		return &SlackAlertChannel{
			logger:  acf.logger,
			enabled: false,
		}, nil
	case "dingtalk":
		return &DingTalkAlertChannel{
			logger:  acf.logger,
			enabled: false,
		}, nil
	case "wechat":
		return &WeChatAlertChannel{
			logger:  acf.logger,
			enabled: false,
		}, nil
	case "sms":
		return &SMSAlertChannel{
			logger:  acf.logger,
			enabled: false,
		}, nil
	case "webhook":
		return &WebhookAlertChannel{
			logger:  acf.logger,
			enabled: false,
		}, nil
	default:
		return nil, fmt.Errorf("unsupported alert channel type: %s", channelType)
	}
}

func (acf *AlertChannelFactory) GetSupportedChannels() []string {
	return []string{
		"websocket",
		"log",
		"email",
		"slack",
		"dingtalk",
		"wechat",
		"sms",
		"webhook",
	}
}
