@echo off
echo ========================================
echo 🚀 AI运维管理平台 - 统一执行引擎测试
echo ========================================
echo.

echo 📋 检查Go环境...
go version
if %errorlevel% neq 0 (
    echo ❌ Go环境未安装或配置错误
    pause
    exit /b 1
)

echo.
echo 📦 下载依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo ❌ 依赖下载失败
    pause
    exit /b 1
)

echo.
echo 🔧 编译测试服务器...
go build -o test_unified_execution.exe integration_test_unified_execution.go
if %errorlevel% neq 0 (
    echo ❌ 编译失败
    pause
    exit /b 1
)

echo.
echo ✅ 编译成功！
echo.
echo 🚀 启动统一执行引擎测试服务器...
echo 📡 服务器将在 http://localhost:8081 启动
echo 🔗 测试页面: http://localhost:8081
echo.
echo 按 Ctrl+C 停止服务器
echo ========================================
echo.

test_unified_execution.exe

echo.
echo 🧹 清理临时文件...
if exist test_unified_execution.exe del test_unified_execution.exe

echo.
echo ✅ 测试完成
pause
