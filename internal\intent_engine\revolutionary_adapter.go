package intent_engine

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// RevolutionaryIntentAdapter 革命性意图识别适配器
// 将革命性意图系统集成到现有的AI服务架构中
type RevolutionaryIntentAdapter struct {
	revolutionarySystem *RevolutionaryIntentSystem
	logger              *logrus.Logger

	// 兼容性配置
	enableLegacySupport bool
	fallbackEnabled     bool
}

// LegacyIntentRequest 传统意图请求格式
type LegacyIntentRequest struct {
	SessionID string `json:"session_id"`
	UserID    int64  `json:"user_id"`
	Message   string `json:"message"`
}

// LegacyIntentResponse 传统意图响应格式
type LegacyIntentResponse struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Success    bool                   `json:"success"`
	Error      string                 `json:"error,omitempty"`
}

// NewRevolutionaryIntentAdapter 创建革命性意图识别适配器
func NewRevolutionaryIntentAdapter(deepseekService interface{}, logger *logrus.Logger) *RevolutionaryIntentAdapter {
	revolutionarySystem := NewRevolutionaryIntentSystem(deepseekService, logger)

	return &RevolutionaryIntentAdapter{
		revolutionarySystem: revolutionarySystem,
		logger:              logger,
		enableLegacySupport: true,
		fallbackEnabled:     true,
	}
}

// ProcessLegacyIntent 处理传统格式的意图识别请求
func (ria *RevolutionaryIntentAdapter) ProcessLegacyIntent(ctx context.Context, req *LegacyIntentRequest) (*LegacyIntentResponse, error) {
	ria.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("Processing legacy intent request with revolutionary system")

	// 转换为革命性系统的请求格式
	revolutionaryReq := &RevolutionaryIntentRequest{
		ID:                  fmt.Sprintf("legacy_%d", time.Now().UnixNano()),
		SessionID:           req.SessionID,
		UserID:              req.UserID,
		Message:             req.Message,
		Timestamp:           time.Now(),
		Context:             make(map[string]interface{}),
		ConversationHistory: []ConversationTurn{},
		CurrentState:        make(map[string]interface{}),
		RequireHighAccuracy: false,
		MaxResponseTime:     30 * time.Second,
		EnableLearning:      true,
		SecurityLevel:       "standard",
		Source:              "legacy_adapter",
	}

	// 调用革命性意图识别系统
	revolutionaryResp, err := ria.revolutionarySystem.ProcessIntent(ctx, revolutionaryReq)
	if err != nil {
		ria.logger.WithError(err).Error("Revolutionary intent processing failed")

		// 如果启用了降级，返回简化结果
		if ria.fallbackEnabled {
			return ria.createFallbackResponse(req.Message), nil
		}

		return &LegacyIntentResponse{
			Success: false,
			Error:   fmt.Sprintf("Intent processing failed: %v", err),
		}, err
	}

	// 转换为传统格式的响应
	legacyResp := &LegacyIntentResponse{
		Success:    revolutionaryResp.Success,
		Confidence: revolutionaryResp.Confidence,
		Parameters: revolutionaryResp.Parameters,
	}

	if revolutionaryResp.Intent != nil {
		// 映射革命性意图类型到传统类型
		legacyResp.Type = ria.mapToLegacyType(revolutionaryResp.Intent)
	}

	if !revolutionaryResp.Success {
		legacyResp.Error = revolutionaryResp.Error
	}

	ria.logger.WithFields(logrus.Fields{
		"session_id":  req.SessionID,
		"intent_type": legacyResp.Type,
		"confidence":  legacyResp.Confidence,
		"success":     legacyResp.Success,
	}).Info("Legacy intent processing completed")

	return legacyResp, nil
}

// mapToLegacyType 将革命性意图类型映射到传统类型
func (ria *RevolutionaryIntentAdapter) mapToLegacyType(intent *RevolutionaryRecognizedIntent) string {
	ria.logger.WithFields(logrus.Fields{
		"intent_name":  intent.Name,
		"category":     intent.Category,
		"sub_category": intent.SubCategory,
		"description":  intent.Description,
	}).Debug("Mapping revolutionary intent to legacy type")

	// 根据革命性系统的分类结果，映射到现有系统的4种基础类型
	switch intent.Category {
	case "ops_operations":
		// 智能判断主机管理操作类型
		intentName := strings.ToLower(intent.Name)
		description := strings.ToLower(intent.Description)

		// 主机数据相关操作（CRUD）映射到database_operations
		if strings.Contains(intentName, "host") || strings.Contains(description, "主机") ||
			strings.Contains(description, "密码") || strings.Contains(description, "列出") ||
			strings.Contains(description, "查看") || strings.Contains(description, "添加") ||
			strings.Contains(description, "修改") || strings.Contains(description, "删除") {
			ria.logger.Debug("Mapping ops_operations to database_operations (host management)")
			return "database_operations"
		}

		// 其他运维操作映射到SSH操作
		ria.logger.Debug("Mapping ops_operations to ssh_operations (other operations)")
		return "ssh_operations"

	case "system_monitoring", "network_diagnostics", "security_audit":
		ria.logger.Debug("Mapping to monitoring_operations")
		return "monitoring_operations"

	case "data_analysis":
		ria.logger.Debug("Mapping data_analysis to database_operations")
		return "database_operations"

	case "conversational":
		ria.logger.Debug("Mapping to general_chat")
		return "general_chat"

	default:
		ria.logger.WithField("category", intent.Category).Debug("Unknown category, mapping to general_chat")
		return "general_chat"
	}
}

// createFallbackResponse 创建降级响应
func (ria *RevolutionaryIntentAdapter) createFallbackResponse(message string) *LegacyIntentResponse {
	ria.logger.Warn("Creating fallback response for failed intent processing")

	// 简单的规则匹配作为降级方案
	lowerMessage := message

	if containsAny(lowerMessage, []string{"主机", "服务器", "添加", "删除"}) {
		return &LegacyIntentResponse{
			Type:       "database_operations",
			Confidence: 0.6,
			Parameters: map[string]interface{}{
				"fallback":         true,
				"original_message": message,
			},
			Success: true,
		}
	}

	if containsAny(lowerMessage, []string{"监控", "状态", "检查", "性能"}) {
		return &LegacyIntentResponse{
			Type:       "monitoring_operations",
			Confidence: 0.6,
			Parameters: map[string]interface{}{
				"fallback":         true,
				"original_message": message,
			},
			Success: true,
		}
	}

	if containsAny(lowerMessage, []string{"连接", "SSH", "命令", "执行"}) {
		return &LegacyIntentResponse{
			Type:       "ssh_operations",
			Confidence: 0.6,
			Parameters: map[string]interface{}{
				"fallback":         true,
				"original_message": message,
			},
			Success: true,
		}
	}

	// 默认返回通用聊天
	return &LegacyIntentResponse{
		Type:       "general_chat",
		Confidence: 0.5,
		Parameters: map[string]interface{}{
			"fallback":         true,
			"original_message": message,
		},
		Success: true,
	}
}

// containsAny 检查字符串是否包含任意一个关键词
func containsAny(text string, keywords []string) bool {
	for _, keyword := range keywords {
		if len(text) > 0 && len(keyword) > 0 {
			// 简单的包含检查
			for i := 0; i <= len(text)-len(keyword); i++ {
				if text[i:i+len(keyword)] == keyword {
					return true
				}
			}
		}
	}
	return false
}

// GetSystemMetrics 获取系统指标
func (ria *RevolutionaryIntentAdapter) GetSystemMetrics() *RevolutionaryIntentMetrics {
	return ria.revolutionarySystem.metrics
}

// GetSupportedIntents 获取支持的意图列表
func (ria *RevolutionaryIntentAdapter) GetSupportedIntents() []string {
	intents := make([]string, 0, len(ria.revolutionarySystem.intentRegistry))
	for intentID := range ria.revolutionarySystem.intentRegistry {
		intents = append(intents, intentID)
	}
	return intents
}

// GetIntentDefinition 获取意图定义
func (ria *RevolutionaryIntentAdapter) GetIntentDefinition(intentID string) (*AdvancedIntentDefinition, bool) {
	definition, exists := ria.revolutionarySystem.intentRegistry[intentID]
	return definition, exists
}

// UpdateIntentDefinition 更新意图定义
func (ria *RevolutionaryIntentAdapter) UpdateIntentDefinition(intentID string, definition *AdvancedIntentDefinition) error {
	if definition == nil {
		return fmt.Errorf("intent definition cannot be nil")
	}

	definition.UpdatedAt = time.Now()
	ria.revolutionarySystem.intentRegistry[intentID] = definition

	ria.logger.WithFields(logrus.Fields{
		"intent_id": intentID,
		"name":      definition.Name,
		"category":  definition.Category,
	}).Info("Intent definition updated")

	return nil
}

// EnableLearning 启用学习模式
func (ria *RevolutionaryIntentAdapter) EnableLearning(enabled bool) {
	ria.revolutionarySystem.config.ContinuousLearningEnabled = enabled
	ria.logger.WithField("enabled", enabled).Info("Learning mode updated")
}

// SetAccuracyTarget 设置准确率目标
func (ria *RevolutionaryIntentAdapter) SetAccuracyTarget(target float64) {
	if target >= 0.0 && target <= 1.0 {
		ria.revolutionarySystem.config.AccuracyTarget = target
		ria.logger.WithField("target", target).Info("Accuracy target updated")
	}
}

// SetResponseTimeTarget 设置响应时间目标
func (ria *RevolutionaryIntentAdapter) SetResponseTimeTarget(target time.Duration) {
	ria.revolutionarySystem.config.ResponseTimeTarget = target
	ria.logger.WithField("target", target).Info("Response time target updated")
}

// IsHealthy 检查系统健康状态
func (ria *RevolutionaryIntentAdapter) IsHealthy() bool {
	metrics := ria.revolutionarySystem.metrics

	// 检查准确率是否达标
	if metrics.AccuracyRate < ria.revolutionarySystem.config.AccuracyTarget {
		return false
	}

	// 检查响应时间是否达标
	if metrics.AverageResponseTime > float64(ria.revolutionarySystem.config.ResponseTimeTarget.Milliseconds()) {
		return false
	}

	// 检查错误率
	if metrics.TotalRequests > 0 {
		errorRate := float64(metrics.FailedRequests) / float64(metrics.TotalRequests)
		if errorRate > 0.1 { // 错误率超过10%
			return false
		}
	}

	return true
}

// GetHealthStatus 获取详细健康状态
func (ria *RevolutionaryIntentAdapter) GetHealthStatus() map[string]interface{} {
	metrics := ria.revolutionarySystem.metrics
	config := ria.revolutionarySystem.config

	return map[string]interface{}{
		"healthy": ria.IsHealthy(),
		"metrics": map[string]interface{}{
			"total_requests":           metrics.TotalRequests,
			"successful_requests":      metrics.SuccessfulRequests,
			"failed_requests":          metrics.FailedRequests,
			"accuracy_rate":            metrics.AccuracyRate,
			"average_response_time_ms": metrics.AverageResponseTime,
		},
		"targets": map[string]interface{}{
			"accuracy_target":         config.AccuracyTarget,
			"response_time_target_ms": config.ResponseTimeTarget.Milliseconds(),
		},
		"status": map[string]interface{}{
			"is_running":      ria.revolutionarySystem.isRunning,
			"start_time":      ria.revolutionarySystem.startTime,
			"processed_count": ria.revolutionarySystem.processedCount,
		},
	}
}
