/* ========================================
   对话历史管理样式
   提供完整的对话管理功能
   ======================================== */

/* 对话列表容器 */
.chat-list {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
  max-height: 400px;
  overflow-y: auto;
}

/* 对话项 */
.chat-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  position: relative;
  border: 1px solid transparent;
}

.chat-item:hover {
  background-color: var(--bg-hover);
  border-color: var(--border-secondary);
}

.chat-item.active {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.chat-item.pinned {
  border-left: 3px solid var(--color-warning);
}

/* 对话图标 */
.chat-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: var(--font-size-xs);
  flex-shrink: 0;
}

.chat-icon.group {
  background: linear-gradient(135deg, var(--color-success), #047857);
}

.chat-icon.archived {
  background: linear-gradient(135deg, var(--color-secondary), #475569);
}

/* 对话内容 */
.chat-content {
  flex: 1;
  min-width: 0;
}

.chat-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-preview {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.chat-meta {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: var(--space-1);
}

.chat-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.chat-count {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  background-color: var(--bg-tertiary);
  padding: 1px var(--space-1);
  border-radius: var(--radius-sm);
}

/* 对话操作按钮 */
.chat-actions {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  opacity: 0;
  transition: opacity var(--duration-fast) var(--easing-ease);
}

.chat-item:hover .chat-actions {
  opacity: 1;
}

.chat-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  font-size: var(--font-size-xs);
}

.chat-action-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-secondary);
}

.chat-action-btn.pin.active {
  color: var(--color-warning);
}

.chat-action-btn.archive.active {
  color: var(--color-secondary);
}

/* 对话标签 */
.chat-tags {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  margin-top: var(--space-1);
  flex-wrap: wrap;
}

.chat-tag {
  font-size: var(--font-size-xs);
  padding: 1px var(--space-1);
  background-color: var(--bg-tertiary);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  border: 1px solid var(--border-primary);
}

.chat-tag.system {
  background-color: var(--color-info);
  color: white;
  border-color: var(--color-info);
}

.chat-tag.important {
  background-color: var(--color-warning);
  color: white;
  border-color: var(--color-warning);
}

.chat-tag.error {
  background-color: var(--color-error);
  color: white;
  border-color: var(--color-error);
}

/* 对话分组 */
.chat-group {
  margin-bottom: var(--space-4);
}

.chat-group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-2) var(--space-1);
  margin-bottom: var(--space-2);
  cursor: pointer;
  border-radius: var(--radius-md);
  transition: background-color var(--duration-fast) var(--easing-ease);
}

.chat-group-header:hover {
  background-color: var(--bg-hover);
}

.chat-group-title {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
}

.chat-group-toggle {
  color: var(--text-tertiary);
  transition: transform var(--duration-fast) var(--easing-ease);
}

.chat-group.collapsed .chat-group-toggle {
  transform: rotate(-90deg);
}

.chat-group-count {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  background-color: var(--bg-tertiary);
  padding: 1px var(--space-1);
  border-radius: var(--radius-sm);
}

.chat-group-content {
  overflow: hidden;
  transition: max-height var(--duration-normal) var(--easing-ease);
}

.chat-group.collapsed .chat-group-content {
  max-height: 0;
}

/* 空状态 */
.chat-list-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-4);
  text-align: center;
  color: var(--text-tertiary);
}

.empty-icon {
  font-size: 2rem;
  margin-bottom: var(--space-3);
  opacity: 0.5;
}

.empty-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--space-2);
}

.empty-description {
  font-size: var(--font-size-xs);
  line-height: 1.4;
}

/* 搜索结果高亮 */
.search-highlight {
  background-color: var(--color-warning);
  color: var(--text-primary);
  padding: 0 2px;
  border-radius: 2px;
}

/* 对话过滤器 */
.chat-filters {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  margin-bottom: var(--space-3);
  padding: 0 var(--space-1);
}

.filter-btn {
  padding: var(--space-1) var(--space-2);
  border: 1px solid var(--border-primary);
  background-color: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.filter-btn:hover {
  background-color: var(--bg-hover);
  border-color: var(--color-primary);
}

.filter-btn.active {
  background-color: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

/* 对话导出 */
.export-options {
  position: absolute;
  top: 100%;
  right: 0;
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-lg);
  padding: var(--space-2);
  z-index: 100;
  min-width: 120px;
  display: none;
}

.export-options.show {
  display: block;
}

.export-option {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2);
  border-radius: var(--radius-sm);
  cursor: pointer;
  font-size: var(--font-size-sm);
  color: var(--text-primary);
  transition: background-color var(--duration-fast) var(--easing-ease);
}

.export-option:hover {
  background-color: var(--bg-hover);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .chat-item {
    padding: var(--space-3);
  }
  
  .chat-icon {
    width: 20px;
    height: 20px;
  }
  
  .chat-actions {
    opacity: 1;
  }
  
  .chat-action-btn {
    width: 24px;
    height: 24px;
  }
}

@media (max-width: 480px) {
  .chat-filters {
    flex-wrap: wrap;
    gap: var(--space-1);
  }
  
  .filter-btn {
    flex: 1;
    text-align: center;
    min-width: 60px;
  }
}
