package ai

import (
	"context"
	"time"

	"aiops-platform/internal/config"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ServiceAdapter 服务适配器，实现AIService接口
type ServiceAdapter struct {
	simpleService *SimpleAIService
	logger        *logrus.Logger
}

// NewServiceAdapter 创建服务适配器
func NewServiceAdapter(db *gorm.DB, cfg *config.Config, logger *logrus.Logger, hostService interface{}) *ServiceAdapter {
	simpleService := NewSimpleAIService(db, hostService, logger)
	
	return &ServiceAdapter{
		simpleService: simpleService,
		logger:        logger,
	}
}

// ProcessMessage 处理消息 - 实现AIService接口
func (sa *ServiceAdapter) ProcessMessage(ctx context.Context, req *ServiceProcessMessageRequest) (*ServiceProcessMessageResponse, error) {
	// 转换请求格式
	simpleReq := &SimpleMessageRequest{
		Message:   req.Message,
		SessionID: req.SessionID,
		UserID:    req.UserID,
	}
	
	// 调用简单服务
	simpleResp, err := sa.simpleService.ProcessMessage(ctx, simpleReq)
	if err != nil {
		return nil, err
	}
	
	// 转换响应格式
	return &ServiceProcessMessageResponse{
		Content:        simpleResp.Content,
		Intent:         simpleResp.Intent,
		Confidence:     simpleResp.Confidence,
		Parameters:     simpleResp.Parameters,
		TokenCount:     simpleResp.TokenCount,
		ProcessingTime: simpleResp.ProcessingTime,
		Timestamp:      simpleResp.Timestamp,
		Source:         simpleResp.Source,
		Success:        simpleResp.Success,
	}, nil
}

// ProcessMessageWithTools 使用工具处理消息 - 实现AIService接口
func (sa *ServiceAdapter) ProcessMessageWithTools(ctx context.Context, req *ServiceProcessMessageRequest) (*ServiceProcessMessageResponse, error) {
	return sa.ProcessMessage(ctx, req)
}

// AnalyzeWorkflowIntent 分析工作流意图 - 实现AIService接口
func (sa *ServiceAdapter) AnalyzeWorkflowIntent(ctx context.Context, message string, sessionID string) (*ServiceWorkflowIntentAnalysis, error) {
	return &ServiceWorkflowIntentAnalysis{
		NeedsWorkflow:   false,
		WorkflowType:    "none",
		Confidence:      0.5,
		Intent:          "general",
		SuggestedAction: "continue_conversation",
		Parameters:      make(map[string]interface{}),
		Context:         "normal_conversation",
	}, nil
}

// GenerateWorkflowGuidance 生成工作流指导 - 实现AIService接口
func (sa *ServiceAdapter) GenerateWorkflowGuidance(ctx context.Context, workflowState *ServiceWorkflowState) (*ServiceWorkflowGuidance, error) {
	return &ServiceWorkflowGuidance{
		Message:           "继续当前操作",
		Suggestions:       []string{"输入具体命令"},
		NextAction:        "continue",
		RequiredInputs:    []string{"请提供更多信息"},
		ProgressIndicator: "进行中",
		HelpText:          "如需帮助，请输入'帮助'",
	}, nil
}

// CreateContext 创建上下文 - 实现AIService接口
func (sa *ServiceAdapter) CreateContext(sessionID string, userID int64) error {
	sa.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("ServiceAdapter: 创建上下文")
	return nil
}

// GetContext 获取上下文 - 实现AIService接口
func (sa *ServiceAdapter) GetContext(sessionID string) (*ServiceConversationContext, error) {
	return &ServiceConversationContext{
		SessionID: sessionID,
		UserID:    1,
		Messages:  []ServiceConversationMessage{},
		Variables: make(map[string]interface{}),
	}, nil
}

// UpdateContext 更新上下文 - 实现AIService接口
func (sa *ServiceAdapter) UpdateContext(sessionID string, updates map[string]interface{}) error {
	sa.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"updates":    updates,
	}).Info("ServiceAdapter: 更新上下文")
	return nil
}

// ClearContext 清除上下文 - 实现AIService接口
func (sa *ServiceAdapter) ClearContext(sessionID string) error {
	sa.logger.WithField("session_id", sessionID).Info("ServiceAdapter: 清除上下文")
	return nil
}

// ExtractIntent 提取意图 - 实现AIService接口
func (sa *ServiceAdapter) ExtractIntent(ctx context.Context, message string, context *ServiceConversationContext) (*ServiceIntentResult, error) {
	intent, err := sa.simpleService.intentEngine.RecognizeIntent(ctx, message)
	if err != nil {
		return nil, err
	}
	
	return &ServiceIntentResult{
		Type:       intent.Type,
		Confidence: intent.Confidence,
		Parameters: intent.Parameters,
		Command:    intent.Command,
	}, nil
}

// GetAvailableTools 获取可用工具 - 实现AIService接口
func (sa *ServiceAdapter) GetAvailableTools(userID int64) ([]ServiceToolDefinition, error) {
	return []ServiceToolDefinition{
		{
			Name:        "host_management",
			Description: "主机管理工具",
			Parameters:  map[string]interface{}{},
		},
		{
			Name:        "database_query",
			Description: "数据库查询工具",
			Parameters:  map[string]interface{}{},
		},
	}, nil
}

// ExecuteTool 执行工具 - 实现AIService接口
func (sa *ServiceAdapter) ExecuteTool(ctx context.Context, toolCall *ServiceToolCall, context *ServiceConversationContext) (*ServiceToolResult, error) {
	return &ServiceToolResult{
		Success: true,
		Result:  "工具执行成功",
		Data:    make(map[string]interface{}),
	}, nil
}

// GenerateResponse 生成响应 - 实现AIService接口
func (sa *ServiceAdapter) GenerateResponse(ctx context.Context, req *ServiceGenerateResponseRequest) (*ServiceGenerateResponseResult, error) {
	return &ServiceGenerateResponseResult{
		Content:   "这是生成的响应",
		TokenUsed: 10,
	}, nil
}

// SummarizeConversation 总结对话 - 实现AIService接口
func (sa *ServiceAdapter) SummarizeConversation(ctx context.Context, sessionID string) (*ServiceConversationSummary, error) {
	return &ServiceConversationSummary{
		Summary:     "对话总结",
		KeyPoints:   []string{"要点1", "要点2"},
		ActionItems: []string{"行动项1", "行动项2"},
	}, nil
}

// ValidateCommand 验证命令 - 实现AIService接口
func (sa *ServiceAdapter) ValidateCommand(ctx context.Context, command string, context *ServiceConversationContext) (*ServiceCommandValidation, error) {
	return &ServiceCommandValidation{
		Command:     command,
		IsSafe:      true,
		RiskLevel:   "low",
		Risks:       []string{},
		Suggestions: []string{"命令看起来安全"},
		Alternative: "",
		ValidatedAt: time.Now(),
	}, nil
}

// ProcessMessageWithWorkflow 使用工作流处理消息 - 实现AIService接口
func (sa *ServiceAdapter) ProcessMessageWithWorkflow(ctx context.Context, req *ServiceProcessMessageRequest) (*ServiceProcessMessageResponse, error) {
	return sa.ProcessMessage(ctx, req)
}

// GetSystemStatus 获取系统状态 - 实现AIService接口
func (sa *ServiceAdapter) GetSystemStatus() map[string]interface{} {
	status := sa.simpleService.GetSystemStatus()
	status["adapter_type"] = "service_adapter"
	return status
}

// HealthCheck 健康检查 - 实现AIService接口
func (sa *ServiceAdapter) HealthCheck() bool {
	return sa.simpleService.HealthCheck()
}

// Close 关闭服务 - 实现AIService接口
func (sa *ServiceAdapter) Close() error {
	return sa.simpleService.Close()
}

// GetMetrics 获取指标 - 实现AIService接口
func (sa *ServiceAdapter) GetMetrics() map[string]interface{} {
	metrics := sa.simpleService.GetMetrics()
	metrics["adapter_type"] = "service_adapter"
	return metrics
}

// ValidateMessage 验证消息 - 实现AIService接口
func (sa *ServiceAdapter) ValidateMessage(message string) error {
	return sa.simpleService.ValidateMessage(message)
}

// PreprocessMessage 预处理消息 - 实现AIService接口
func (sa *ServiceAdapter) PreprocessMessage(message string) string {
	return sa.simpleService.PreprocessMessage(message)
}

// GetSupportedIntents 获取支持的意图 - 实现AIService接口
func (sa *ServiceAdapter) GetSupportedIntents() []string {
	return sa.simpleService.GetSupportedIntents()
}

// GetSupportedOperations 获取支持的操作 - 实现AIService接口
func (sa *ServiceAdapter) GetSupportedOperations() map[string][]string {
	return sa.simpleService.GetSupportedOperations()
}

// GetServiceInfo 获取服务信息 - 实现AIService接口
func (sa *ServiceAdapter) GetServiceInfo() map[string]interface{} {
	return map[string]interface{}{
		"name":        "service_adapter",
		"version":     "1.0.0",
		"description": "服务AI适配器",
		"features": []string{
			"类型转换",
			"接口适配",
			"主机管理",
			"意图识别",
		},
		"status":    "running",
		"timestamp": time.Now(),
	}
}

// IsReady 检查服务是否就绪 - 实现AIService接口
func (sa *ServiceAdapter) IsReady() bool {
	return sa.HealthCheck()
}

// GetVersion 获取版本信息 - 实现AIService接口
func (sa *ServiceAdapter) GetVersion() string {
	return "1.0.0"
}

// GetCapabilities 获取能力列表 - 实现AIService接口
func (sa *ServiceAdapter) GetCapabilities() []string {
	return []string{
		"interface_adaptation",
		"type_conversion",
		"intent_recognition",
		"database_operations",
		"host_management",
		"general_chat",
	}
}

// SetConfiguration 设置配置 - 实现AIService接口
func (sa *ServiceAdapter) SetConfiguration(config map[string]interface{}) error {
	sa.logger.WithField("config", config).Info("ServiceAdapter: 配置更新")
	return nil
}

// GetConfiguration 获取配置 - 实现AIService接口
func (sa *ServiceAdapter) GetConfiguration() map[string]interface{} {
	return map[string]interface{}{
		"service_type":         "service_adapter",
		"intent_engine":        "simple_intent_engine",
		"execution_engine":     "simple_execution_engine",
		"type_conversion":      "enabled",
		"interface_adaptation": "enabled",
	}
}

// Reset 重置服务状态 - 实现AIService接口
func (sa *ServiceAdapter) Reset() error {
	sa.logger.Info("ServiceAdapter: 服务重置")
	return nil
}

// GetStatistics 获取统计信息 - 实现AIService接口
func (sa *ServiceAdapter) GetStatistics() map[string]interface{} {
	return map[string]interface{}{
		"total_requests":        0, // 可以添加计数器
		"successful_requests":   0,
		"failed_requests":       0,
		"type_conversions":      0,
		"average_response_time": "0ms",
		"uptime":                time.Now().Format("2006-01-02 15:04:05"),
	}
}
