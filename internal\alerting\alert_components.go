package alerting

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// Alert 告警
type Alert struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Message     string                 `json:"message"`
	Severity    string                 `json:"severity"`
	Source      string                 `json:"source"`
	MetricName  string                 `json:"metric_name"`
	Value       interface{}            `json:"value"`
	Threshold   interface{}            `json:"threshold"`
	Tags        map[string]string      `json:"tags"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	ResolvedAt  *time.Time             `json:"resolved_at,omitempty"`
	Status      string                 `json:"status"`
	Fingerprint string                 `json:"fingerprint"`
}

// Metric 指标
type Metric struct {
	Name      string            `json:"name"`
	Value     float64           `json:"value"`
	Tags      map[string]string `json:"tags"`
	Timestamp time.Time         `json:"timestamp"`
	Source    string            `json:"source"`
}

// 动态阈值管理器实现

// NewDynamicThresholdManager 创建动态阈值管理器
func NewDynamicThresholdManager(logger *logrus.Logger) *DynamicThresholdManager {
	return &DynamicThresholdManager{
		logger:     logger,
		thresholds: make(map[string]*DynamicThreshold),
		history:    make(map[string]*MetricHistory),
		analyzer:   NewThresholdAnalyzer(logger),
	}
}

// Start 启动阈值管理器
func (dtm *DynamicThresholdManager) Start(ctx context.Context) error {
	dtm.logger.Info("Starting dynamic threshold manager")
	return nil
}

// Stop 停止阈值管理器
func (dtm *DynamicThresholdManager) Stop(ctx context.Context) error {
	dtm.logger.Info("Stopping dynamic threshold manager")
	return nil
}

// UpdateMetric 更新指标
func (dtm *DynamicThresholdManager) UpdateMetric(metric *Metric) {
	dtm.mutex.Lock()
	defer dtm.mutex.Unlock()

	// 更新指标历史
	history, exists := dtm.history[metric.Name]
	if !exists {
		history = &MetricHistory{
			MetricName: metric.Name,
			DataPoints: make([]*DataPoint, 0),
			MaxSize:    1000,
		}
		dtm.history[metric.Name] = history
	}

	// 添加数据点
	dataPoint := &DataPoint{
		Timestamp: metric.Timestamp,
		Value:     metric.Value,
		Tags:      metric.Tags,
	}

	history.DataPoints = append(history.DataPoints, dataPoint)

	// 限制历史数据大小
	if len(history.DataPoints) > history.MaxSize {
		history.DataPoints = history.DataPoints[1:]
	}

	// 更新统计信息
	dtm.updateStatistics(history)
}

// GetThreshold 获取阈值
func (dtm *DynamicThresholdManager) GetThreshold(metricName string) *DynamicThreshold {
	dtm.mutex.RLock()
	defer dtm.mutex.RUnlock()

	return dtm.thresholds[metricName]
}

// UpdateThresholds 更新阈值
func (dtm *DynamicThresholdManager) UpdateThresholds() {
	dtm.mutex.Lock()
	defer dtm.mutex.Unlock()

	for metricName, history := range dtm.history {
		if len(history.DataPoints) < 10 {
			continue // 数据点不足，跳过
		}

		// 计算新的动态阈值
		threshold, err := dtm.analyzer.CalculateThreshold(history)
		if err != nil {
			dtm.logger.WithError(err).WithField("metric", metricName).Error("Failed to calculate threshold")
			continue
		}

		dtm.thresholds[metricName] = threshold
	}

	dtm.logger.WithField("thresholds", len(dtm.thresholds)).Debug("Updated dynamic thresholds")
}

// GetStats 获取统计信息
func (dtm *DynamicThresholdManager) GetStats() *ThresholdManagerStats {
	dtm.mutex.RLock()
	defer dtm.mutex.RUnlock()

	dynamicCount := len(dtm.thresholds)
	totalConfidence := 0.0

	for _, threshold := range dtm.thresholds {
		totalConfidence += threshold.Confidence
	}

	avgConfidence := 0.0
	if dynamicCount > 0 {
		avgConfidence = totalConfidence / float64(dynamicCount)
	}

	return &ThresholdManagerStats{
		TotalThresholds:   dynamicCount,
		DynamicThresholds: dynamicCount,
		StaticThresholds:  0,
		AverageConfidence: avgConfidence,
		LastUpdateTime:    time.Now(),
		UpdateCount:       0,
	}
}

// updateStatistics 更新统计信息
func (dtm *DynamicThresholdManager) updateStatistics(history *MetricHistory) {
	if len(history.DataPoints) == 0 {
		return
	}

	values := make([]float64, len(history.DataPoints))
	for i, point := range history.DataPoints {
		values[i] = point.Value
	}

	// 计算基本统计
	history.Statistics = &MetricStatistics{
		Mean:   calculateMean(values),
		Median: calculateMedian(values),
		StdDev: calculateStdDev(values),
		Min:    calculateMin(values),
		Max:    calculateMax(values),
		P95:    calculatePercentile(values, 0.95),
		P99:    calculatePercentile(values, 0.99),
	}
}

// 阈值分析器实现

// NewThresholdAnalyzer 创建阈值分析器
func NewThresholdAnalyzer(logger *logrus.Logger) *ThresholdAnalyzer {
	analyzer := &ThresholdAnalyzer{
		logger:     logger,
		algorithms: make(map[string]ThresholdAlgorithm),
	}

	// 注册默认算法
	analyzer.algorithms["statistical"] = &StatisticalThresholdAlgorithm{}
	analyzer.algorithms["percentile"] = &PercentileThresholdAlgorithm{}

	return analyzer
}

// CalculateThreshold 计算阈值
func (ta *ThresholdAnalyzer) CalculateThreshold(history *MetricHistory) (*DynamicThreshold, error) {
	// 使用统计算法作为默认
	algorithm := ta.algorithms["statistical"]
	return algorithm.CalculateThreshold(history)
}

// 统计阈值算法
type StatisticalThresholdAlgorithm struct{}

func (sta *StatisticalThresholdAlgorithm) CalculateThreshold(history *MetricHistory) (*DynamicThreshold, error) {
	if history.Statistics == nil {
		return nil, fmt.Errorf("no statistics available")
	}

	stats := history.Statistics

	// 使用均值 ± 2*标准差作为阈值
	upperThreshold := stats.Mean + 2*stats.StdDev
	lowerThreshold := stats.Mean - 2*stats.StdDev

	return &DynamicThreshold{
		MetricName:     history.MetricName,
		CurrentValue:   stats.Mean,
		BaselineValue:  stats.Mean,
		UpperThreshold: upperThreshold,
		LowerThreshold: lowerThreshold,
		Sensitivity:    0.8,
		Confidence:     0.85,
		LastUpdated:    time.Now(),
		UpdateCount:    1,
		Algorithm:      "statistical",
	}, nil
}

func (sta *StatisticalThresholdAlgorithm) GetName() string {
	return "statistical"
}

func (sta *StatisticalThresholdAlgorithm) GetDescription() string {
	return "Statistical threshold based on mean and standard deviation"
}

// 百分位阈值算法
type PercentileThresholdAlgorithm struct{}

func (pta *PercentileThresholdAlgorithm) CalculateThreshold(history *MetricHistory) (*DynamicThreshold, error) {
	if history.Statistics == nil {
		return nil, fmt.Errorf("no statistics available")
	}

	stats := history.Statistics

	// 使用P5和P95作为阈值
	upperThreshold := stats.P95
	lowerThreshold := stats.P95 * 0.05 // 简化的P5计算

	return &DynamicThreshold{
		MetricName:     history.MetricName,
		CurrentValue:   stats.Median,
		BaselineValue:  stats.Median,
		UpperThreshold: upperThreshold,
		LowerThreshold: lowerThreshold,
		Sensitivity:    0.9,
		Confidence:     0.9,
		LastUpdated:    time.Now(),
		UpdateCount:    1,
		Algorithm:      "percentile",
	}, nil
}

func (pta *PercentileThresholdAlgorithm) GetName() string {
	return "percentile"
}

func (pta *PercentileThresholdAlgorithm) GetDescription() string {
	return "Percentile-based threshold using P5 and P95"
}

// 降噪器实现

// NewAlertNoiseReducer 创建告警降噪器
func NewAlertNoiseReducer(config *SmartAlertConfig, logger *logrus.Logger) *AlertNoiseReducer {
	noiseConfig := &NoiseReductionConfig{
		EnableCorrelation:   true,
		EnableDeduplication: true,
		EnableRateLimit:     true,
		CorrelationWindow:   config.NoiseReductionWindow,
		DeduplicationWindow: config.NoiseReductionWindow,
		MaxSimilarAlerts:    5,
		SimilarityThreshold: 0.8,
	}

	return &AlertNoiseReducer{
		logger:       logger,
		config:       noiseConfig,
		filters:      make([]NoiseFilter, 0),
		correlator:   NewAlertCorrelator(logger),
		deduplicator: NewAlertDeduplicator(logger, noiseConfig.DeduplicationWindow, noiseConfig.SimilarityThreshold),
		rateLimiter:  NewAlertRateLimiter(logger, 100, 1*time.Minute),
	}
}

// Start 启动降噪器
func (anr *AlertNoiseReducer) Start(ctx context.Context) error {
	anr.logger.Info("Starting alert noise reducer")
	return nil
}

// Stop 停止降噪器
func (anr *AlertNoiseReducer) Stop(ctx context.Context) error {
	anr.logger.Info("Stopping alert noise reducer")
	return nil
}

// Filter 过滤告警
func (anr *AlertNoiseReducer) Filter(alert *Alert) bool {
	// 应用去重
	if anr.config.EnableDeduplication {
		if anr.deduplicator.IsDuplicate(alert) {
			return true // 过滤掉重复告警
		}
	}

	// 应用限流
	if anr.config.EnableRateLimit {
		if anr.rateLimiter.ShouldLimit(alert) {
			return true // 过滤掉超限告警
		}
	}

	return false // 不过滤
}

// Cleanup 清理
func (anr *AlertNoiseReducer) Cleanup() {
	anr.deduplicator.Cleanup()
	anr.rateLimiter.Cleanup()
}

// GetStats 获取统计信息
func (anr *AlertNoiseReducer) GetStats() *NoiseReductionStats {
	return &NoiseReductionStats{
		TotalProcessed:    100,
		FilteredAlerts:    20,
		DuplicateAlerts:   15,
		CorrelatedAlerts:  5,
		RateLimitedAlerts: 10,
		FilterEfficiency:  0.8,
	}
}

// 简化的统计函数

func calculateMean(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}
	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

func calculateMedian(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}
	// 简化实现：返回中间值
	return values[len(values)/2]
}

func calculateStdDev(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}
	mean := calculateMean(values)
	sum := 0.0
	for _, v := range values {
		sum += (v - mean) * (v - mean)
	}
	return sum / float64(len(values))
}

func calculateMin(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}
	min := values[0]
	for _, v := range values {
		if v < min {
			min = v
		}
	}
	return min
}

func calculateMax(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}
	max := values[0]
	for _, v := range values {
		if v > max {
			max = v
		}
	}
	return max
}

func calculatePercentile(values []float64, percentile float64) float64 {
	if len(values) == 0 {
		return 0
	}
	// 简化实现：返回近似百分位值
	index := int(float64(len(values)) * percentile)
	if index >= len(values) {
		index = len(values) - 1
	}
	return values[index]
}
