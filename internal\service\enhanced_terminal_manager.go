package service

import (
	"context"
	"fmt"
	"io"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/gorilla/websocket"
	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/ssh"
	"gorm.io/gorm"
)

// EnhancedTerminalManager 增强终端管理器
type EnhancedTerminalManager struct {
	db            *gorm.DB
	logger        *logrus.Logger
	wsManager     *WebSocketManager
	sshPool       *SSHConnectionPool
	config        *TerminalConfig
	sessions      map[string]*TerminalSession
	history       *CommandHistoryManager
	security      *TerminalSecurityManager
	monitor       *TerminalMonitor
	running       bool
	stopChan      chan struct{}
	mutex         sync.RWMutex
	metrics       *TerminalMetrics
}

// TerminalConfig 终端配置
type TerminalConfig struct {
	Enabled                bool          `json:"enabled"`
	MaxSessions            int           `json:"max_sessions"`
	SessionTimeout         time.Duration `json:"session_timeout"`
	MaxHistorySize         int           `json:"max_history_size"`
	EnableRecording        bool          `json:"enable_recording"`
	EnableSecurity         bool          `json:"enable_security"`
	AllowedCommands        []string      `json:"allowed_commands"`
	BlockedCommands        []string      `json:"blocked_commands"`
	MaxCommandLength       int           `json:"max_command_length"`
	IdleTimeout            time.Duration `json:"idle_timeout"`
	EnableMultiplexing     bool          `json:"enable_multiplexing"`
	EnableFileTransfer     bool          `json:"enable_file_transfer"`
	MaxConcurrentCommands  int           `json:"max_concurrent_commands"`
}

// TerminalSession 终端会话
type TerminalSession struct {
	ID            string                 `json:"id"`
	UserID        int64                  `json:"user_id"`
	HostID        int64                  `json:"host_id"`
	HostName      string                 `json:"host_name"`
	IPAddress     string                 `json:"ip_address"`
	Status        string                 `json:"status"` // connecting, connected, disconnected, error
	CreatedAt     time.Time              `json:"created_at"`
	LastActivity  time.Time              `json:"last_activity"`
	WorkingDir    string                 `json:"working_dir"`
	Environment   map[string]string      `json:"environment"`
	SSHSession    *ssh.Session           `json:"-"`
	WSConnection  *websocket.Conn        `json:"-"`
	StdinPipe     io.WriteCloser         `json:"-"`
	StdoutPipe    io.Reader              `json:"-"`
	StderrPipe    io.Reader              `json:"-"`
	CommandQueue  chan *TerminalCommand  `json:"-"`
	OutputBuffer  *CircularBuffer        `json:"-"`
	History       []*CommandRecord       `json:"history"`
	Metadata      map[string]interface{} `json:"metadata"`
	mutex         sync.RWMutex           `json:"-"`
}

// TerminalCommand 终端命令
type TerminalCommand struct {
	ID        string                 `json:"id"`
	Command   string                 `json:"command"`
	Type      string                 `json:"type"` // input, resize, signal
	Timestamp time.Time              `json:"timestamp"`
	UserID    int64                  `json:"user_id"`
	SessionID string                 `json:"session_id"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// CommandRecord 命令记录
type CommandRecord struct {
	ID         string    `json:"id"`
	Command    string    `json:"command"`
	Output     string    `json:"output"`
	ExitCode   int       `json:"exit_code"`
	StartTime  time.Time `json:"start_time"`
	EndTime    time.Time `json:"end_time"`
	Duration   int64     `json:"duration_ms"`
	WorkingDir string    `json:"working_dir"`
	Success    bool      `json:"success"`
}

// CircularBuffer 循环缓冲区
type CircularBuffer struct {
	buffer   []byte
	size     int
	head     int
	tail     int
	full     bool
	mutex    sync.RWMutex
}

// TerminalMetrics 终端指标
type TerminalMetrics struct {
	TotalSessions     int64     `json:"total_sessions"`
	ActiveSessions    int64     `json:"active_sessions"`
	TotalCommands     int64     `json:"total_commands"`
	FailedCommands    int64     `json:"failed_commands"`
	AvgSessionTime    float64   `json:"avg_session_time_ms"`
	AvgCommandTime    float64   `json:"avg_command_time_ms"`
	LastActivity      time.Time `json:"last_activity"`
	DataTransferred   int64     `json:"data_transferred_bytes"`
}

// NewEnhancedTerminalManager 创建增强终端管理器
func NewEnhancedTerminalManager(db *gorm.DB, logger *logrus.Logger, wsManager *WebSocketManager, sshPool *SSHConnectionPool) *EnhancedTerminalManager {
	config := &TerminalConfig{
		Enabled:               true,
		MaxSessions:           50,
		SessionTimeout:        2 * time.Hour,
		MaxHistorySize:        1000,
		EnableRecording:       true,
		EnableSecurity:        true,
		AllowedCommands:       []string{}, // 空表示允许所有
		BlockedCommands:       []string{"rm -rf /", "mkfs", "dd if=/dev/zero", ":(){ :|:& };:"},
		MaxCommandLength:      1000,
		IdleTimeout:           30 * time.Minute,
		EnableMultiplexing:    true,
		EnableFileTransfer:    true,
		MaxConcurrentCommands: 5,
	}

	manager := &EnhancedTerminalManager{
		db:        db,
		logger:    logger,
		wsManager: wsManager,
		sshPool:   sshPool,
		config:    config,
		sessions:  make(map[string]*TerminalSession),
		stopChan:  make(chan struct{}),
		metrics:   &TerminalMetrics{},
	}

	// 初始化组件
	manager.history = NewCommandHistoryManager(db, logger)
	manager.security = NewTerminalSecurityManager(logger, config)
	manager.monitor = NewTerminalMonitor(logger, manager)

	return manager
}

// Start 启动增强终端管理器
func (etm *EnhancedTerminalManager) Start(ctx context.Context) error {
	etm.mutex.Lock()
	defer etm.mutex.Unlock()

	if etm.running {
		return fmt.Errorf("enhanced terminal manager is already running")
	}

	if !etm.config.Enabled {
		etm.logger.Info("Enhanced terminal manager is disabled")
		return nil
	}

	etm.running = true

	// 启动历史管理器
	if err := etm.history.Start(ctx); err != nil {
		return fmt.Errorf("failed to start history manager: %w", err)
	}

	// 启动安全管理器
	if etm.config.EnableSecurity {
		if err := etm.security.Start(ctx); err != nil {
			return fmt.Errorf("failed to start security manager: %w", err)
		}
	}

	// 启动监控器
	if err := etm.monitor.Start(ctx); err != nil {
		return fmt.Errorf("failed to start monitor: %w", err)
	}

	// 启动会话清理协程
	go etm.sessionCleanupRoutine(ctx)

	etm.logger.Info("💻 Enhanced terminal manager started")
	return nil
}

// Stop 停止增强终端管理器
func (etm *EnhancedTerminalManager) Stop() error {
	etm.mutex.Lock()
	defer etm.mutex.Unlock()

	if !etm.running {
		return nil
	}

	close(etm.stopChan)
	etm.running = false

	// 关闭所有活跃会话
	for _, session := range etm.sessions {
		etm.closeSession(session)
	}

	etm.logger.Info("Enhanced terminal manager stopped")
	return nil
}

// CreateSession 创建终端会话
func (etm *EnhancedTerminalManager) CreateSession(ctx context.Context, req *TerminalCreateSessionRequest) (*TerminalSession, error) {
	if !etm.running {
		return nil, fmt.Errorf("enhanced terminal manager is not running")
	}

	// 检查会话数量限制
	etm.mutex.RLock()
	activeCount := len(etm.sessions)
	etm.mutex.RUnlock()

	if activeCount >= etm.config.MaxSessions {
		return nil, fmt.Errorf("maximum sessions limit reached: %d", etm.config.MaxSessions)
	}

	// 获取主机信息
	var host model.Host
	if err := etm.db.First(&host, req.HostID).Error; err != nil {
		return nil, fmt.Errorf("host not found: %w", err)
	}

	// 创建会话ID
	sessionID := fmt.Sprintf("term_%d_%d_%d", req.UserID, req.HostID, time.Now().UnixNano())

	// 创建会话对象
	session := &TerminalSession{
		ID:           sessionID,
		UserID:       req.UserID,
		HostID:       req.HostID,
		HostName:     host.Name,
		IPAddress:    host.IPAddress,
		Status:       "connecting",
		CreatedAt:    time.Now(),
		LastActivity: time.Now(),
		WorkingDir:   "/",
		Environment:  make(map[string]string),
		CommandQueue: make(chan *TerminalCommand, 100),
		OutputBuffer: NewCircularBuffer(64 * 1024), // 64KB缓冲区
		History:      make([]*CommandRecord, 0),
		Metadata:     req.Metadata,
	}

	// 建立SSH连接
	if err := etm.establishSSHConnection(session, &host); err != nil {
		return nil, fmt.Errorf("failed to establish SSH connection: %w", err)
	}

	// 保存会话
	etm.mutex.Lock()
	etm.sessions[sessionID] = session
	etm.mutex.Unlock()

	// 启动会话处理协程
	go etm.handleSession(ctx, session)

	// 更新指标
	etm.updateMetrics(func(m *TerminalMetrics) {
		m.TotalSessions++
		m.ActiveSessions++
		m.LastActivity = time.Now()
	})

	etm.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    req.UserID,
		"host_id":    req.HostID,
		"host_name":  host.Name,
	}).Info("✅ Terminal session created")

	return session, nil
}

// TerminalCreateSessionRequest 创建终端会话请求
type TerminalCreateSessionRequest struct {
	UserID   int64                  `json:"user_id"`
	HostID   int64                  `json:"host_id"`
	Cols     int                    `json:"cols"`
	Rows     int                    `json:"rows"`
	Metadata map[string]interface{} `json:"metadata"`
}

// establishSSHConnection 建立SSH连接
func (etm *EnhancedTerminalManager) establishSSHConnection(session *TerminalSession, host *model.Host) error {
	// 从SSH连接池获取连接
	sshConn, err := etm.sshPool.GetConnection(host)
	if err != nil {
		return fmt.Errorf("failed to get SSH connection: %w", err)
	}

	// 创建SSH会话
	sshSession, err := sshConn.Client.NewSession()
	if err != nil {
		return fmt.Errorf("failed to create SSH session: %w", err)
	}

	// 设置终端模式
	modes := ssh.TerminalModes{
		ssh.ECHO:          1,     // 启用回显
		ssh.TTY_OP_ISPEED: 14400, // 输入速度
		ssh.TTY_OP_OSPEED: 14400, // 输出速度
	}

	// 请求伪终端
	if err := sshSession.RequestPty("xterm-256color", 24, 80, modes); err != nil {
		sshSession.Close()
		return fmt.Errorf("failed to request pty: %w", err)
	}

	// 获取输入输出管道
	stdinPipe, err := sshSession.StdinPipe()
	if err != nil {
		sshSession.Close()
		return fmt.Errorf("failed to get stdin pipe: %w", err)
	}

	stdoutPipe, err := sshSession.StdoutPipe()
	if err != nil {
		sshSession.Close()
		return fmt.Errorf("failed to get stdout pipe: %w", err)
	}

	stderrPipe, err := sshSession.StderrPipe()
	if err != nil {
		sshSession.Close()
		return fmt.Errorf("failed to get stderr pipe: %w", err)
	}

	// 启动shell
	if err := sshSession.Shell(); err != nil {
		sshSession.Close()
		return fmt.Errorf("failed to start shell: %w", err)
	}

	// 更新会话
	session.mutex.Lock()
	session.SSHSession = sshSession
	session.StdinPipe = stdinPipe
	session.StdoutPipe = stdoutPipe
	session.StderrPipe = stderrPipe
	session.Status = "connected"
	session.mutex.Unlock()

	return nil
}

// handleSession 处理会话
func (etm *EnhancedTerminalManager) handleSession(ctx context.Context, session *TerminalSession) {
	defer func() {
		etm.closeSession(session)
		etm.removeSession(session.ID)
	}()

	// 启动输出读取协程
	go etm.readOutput(ctx, session)

	// 启动命令处理协程
	go etm.processCommands(ctx, session)

	// 等待会话结束
	select {
	case <-ctx.Done():
		return
	case <-etm.stopChan:
		return
	case <-time.After(etm.config.SessionTimeout):
		etm.logger.WithField("session_id", session.ID).Info("Session timeout")
		return
	}
}

// readOutput 读取输出
func (etm *EnhancedTerminalManager) readOutput(ctx context.Context, session *TerminalSession) {
	buffer := make([]byte, 4096)

	for {
		select {
		case <-ctx.Done():
			return
		case <-etm.stopChan:
			return
		default:
			session.mutex.RLock()
			stdoutPipe := session.StdoutPipe
			session.mutex.RUnlock()

			if stdoutPipe == nil {
				return
			}

			n, err := stdoutPipe.Read(buffer)
			if err != nil {
				if err != io.EOF {
					etm.logger.WithError(err).WithField("session_id", session.ID).Error("Failed to read output")
				}
				return
			}

			if n > 0 {
				data := buffer[:n]
				
				// 写入缓冲区
				session.OutputBuffer.Write(data)
				
				// 发送到WebSocket
				etm.sendToWebSocket(session, "output", string(data))
				
				// 更新活动时间
				session.mutex.Lock()
				session.LastActivity = time.Now()
				session.mutex.Unlock()
				
				// 更新指标
				etm.updateMetrics(func(m *TerminalMetrics) {
					m.DataTransferred += int64(n)
				})
			}
		}
	}
}

// processCommands 处理命令
func (etm *EnhancedTerminalManager) processCommands(ctx context.Context, session *TerminalSession) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-etm.stopChan:
			return
		case cmd := <-session.CommandQueue:
			etm.executeCommand(session, cmd)
		}
	}
}

// executeCommand 执行命令
func (etm *EnhancedTerminalManager) executeCommand(session *TerminalSession, cmd *TerminalCommand) {
	startTime := time.Now()

	// 安全检查
	if etm.config.EnableSecurity {
		if blocked, reason := etm.security.ValidateCommand(cmd.Command); blocked {
			etm.sendToWebSocket(session, "error", fmt.Sprintf("Command blocked: %s", reason))
			return
		}
	}

	// 记录命令
	record := &CommandRecord{
		ID:         fmt.Sprintf("cmd_%d", time.Now().UnixNano()),
		Command:    cmd.Command,
		StartTime:  startTime,
		WorkingDir: session.WorkingDir,
	}

	// 执行命令
	session.mutex.RLock()
	stdinPipe := session.StdinPipe
	session.mutex.RUnlock()

	if stdinPipe == nil {
		return
	}

	_, err := stdinPipe.Write([]byte(cmd.Command + "\n"))
	if err != nil {
		etm.logger.WithError(err).WithField("session_id", session.ID).Error("Failed to write command")
		record.Success = false
		record.EndTime = time.Now()
		record.Duration = time.Since(startTime).Milliseconds()
	} else {
		record.Success = true
		record.EndTime = time.Now()
		record.Duration = time.Since(startTime).Milliseconds()
	}

	// 保存命令记录
	session.mutex.Lock()
	session.History = append(session.History, record)
	if len(session.History) > etm.config.MaxHistorySize {
		session.History = session.History[1:]
	}
	session.LastActivity = time.Now()
	session.mutex.Unlock()

	// 保存到历史管理器
	etm.history.SaveCommand(session.UserID, session.HostID, record)

	// 更新指标
	etm.updateMetrics(func(m *TerminalMetrics) {
		m.TotalCommands++
		if !record.Success {
			m.FailedCommands++
		}
		if m.TotalCommands > 0 {
			totalTime := m.AvgCommandTime * float64(m.TotalCommands-1)
			m.AvgCommandTime = (totalTime + float64(record.Duration)) / float64(m.TotalCommands)
		} else {
			m.AvgCommandTime = float64(record.Duration)
		}
		m.LastActivity = time.Now()
	})
}

// sendToWebSocket 发送到WebSocket
func (etm *EnhancedTerminalManager) sendToWebSocket(session *TerminalSession, msgType string, data interface{}) {
	if etm.wsManager == nil {
		return
	}

	message := &WSMessage{
		ID:       fmt.Sprintf("terminal_%s_%d", msgType, time.Now().UnixNano()),
		Type:     "terminal_" + msgType,
		Channel:  "terminal",
		Priority: "normal",
		Data: map[string]interface{}{
			"session_id": session.ID,
			"type":       msgType,
			"data":       data,
			"timestamp":  time.Now(),
		},
		Timestamp: time.Now(),
		UserID:    session.UserID,
		SessionID: session.ID,
		Metadata: map[string]interface{}{
			"source": "enhanced_terminal_manager",
			"version": "v1",
		},
	}

	etm.wsManager.BroadcastToAll(message)
}

// closeSession 关闭会话
func (etm *EnhancedTerminalManager) closeSession(session *TerminalSession) {
	session.mutex.Lock()
	defer session.mutex.Unlock()

	if session.SSHSession != nil {
		session.SSHSession.Close()
		session.SSHSession = nil
	}

	if session.StdinPipe != nil {
		session.StdinPipe.Close()
		session.StdinPipe = nil
	}

	if session.CommandQueue != nil {
		close(session.CommandQueue)
		session.CommandQueue = nil
	}

	session.Status = "disconnected"

	etm.logger.WithField("session_id", session.ID).Info("Terminal session closed")
}

// removeSession 移除会话
func (etm *EnhancedTerminalManager) removeSession(sessionID string) {
	etm.mutex.Lock()
	defer etm.mutex.Unlock()

	delete(etm.sessions, sessionID)

	etm.updateMetrics(func(m *TerminalMetrics) {
		m.ActiveSessions--
	})
}

// sessionCleanupRoutine 会话清理协程
func (etm *EnhancedTerminalManager) sessionCleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-etm.stopChan:
			return
		case <-ticker.C:
			etm.cleanupIdleSessions()
		}
	}
}

// cleanupIdleSessions 清理空闲会话
func (etm *EnhancedTerminalManager) cleanupIdleSessions() {
	etm.mutex.RLock()
	sessions := make([]*TerminalSession, 0, len(etm.sessions))
	for _, session := range etm.sessions {
		sessions = append(sessions, session)
	}
	etm.mutex.RUnlock()

	now := time.Now()
	for _, session := range sessions {
		session.mutex.RLock()
		lastActivity := session.LastActivity
		session.mutex.RUnlock()

		if now.Sub(lastActivity) > etm.config.IdleTimeout {
			etm.logger.WithField("session_id", session.ID).Info("Closing idle session")
			etm.closeSession(session)
			etm.removeSession(session.ID)
		}
	}
}

// updateMetrics 更新指标
func (etm *EnhancedTerminalManager) updateMetrics(updateFunc func(*TerminalMetrics)) {
	etm.mutex.Lock()
	defer etm.mutex.Unlock()
	updateFunc(etm.metrics)
}

// GetMetrics 获取指标
func (etm *EnhancedTerminalManager) GetMetrics() *TerminalMetrics {
	etm.mutex.RLock()
	defer etm.mutex.RUnlock()

	metrics := *etm.metrics
	return &metrics
}

// 支持组件实现

// NewCircularBuffer 创建循环缓冲区
func NewCircularBuffer(size int) *CircularBuffer {
	return &CircularBuffer{
		buffer: make([]byte, size),
		size:   size,
	}
}

// Write 写入数据
func (cb *CircularBuffer) Write(data []byte) {
	cb.mutex.Lock()
	defer cb.mutex.Unlock()

	for _, b := range data {
		cb.buffer[cb.head] = b
		cb.head = (cb.head + 1) % cb.size

		if cb.full {
			cb.tail = (cb.tail + 1) % cb.size
		}

		if cb.head == cb.tail {
			cb.full = true
		}
	}
}

// Read 读取数据
func (cb *CircularBuffer) Read() []byte {
	cb.mutex.RLock()
	defer cb.mutex.RUnlock()

	if !cb.full && cb.head == cb.tail {
		return []byte{}
	}

	var result []byte
	if cb.full {
		result = make([]byte, cb.size)
		copy(result, cb.buffer[cb.tail:])
		copy(result[cb.size-cb.tail:], cb.buffer[:cb.head])
	} else {
		result = make([]byte, cb.head-cb.tail)
		copy(result, cb.buffer[cb.tail:cb.head])
	}

	return result
}

// CommandHistoryManager 命令历史管理器
type CommandHistoryManager struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewCommandHistoryManager 创建命令历史管理器
func NewCommandHistoryManager(db *gorm.DB, logger *logrus.Logger) *CommandHistoryManager {
	return &CommandHistoryManager{
		db:     db,
		logger: logger,
	}
}

// Start 启动历史管理器
func (chm *CommandHistoryManager) Start(ctx context.Context) error {
	// 自动迁移数据库表
	if err := chm.db.AutoMigrate(&TerminalCommandHistory{}); err != nil {
		return fmt.Errorf("failed to migrate terminal command history table: %w", err)
	}

	chm.logger.Info("Command history manager started")
	return nil
}

// TerminalCommandHistory 终端命令历史
type TerminalCommandHistory struct {
	ID         uint      `json:"id" gorm:"primaryKey"`
	UserID     int64     `json:"user_id" gorm:"not null;index"`
	HostID     int64     `json:"host_id" gorm:"not null;index"`
	SessionID  string    `json:"session_id" gorm:"not null;index"`
	Command    string    `json:"command" gorm:"not null"`
	Output     string    `json:"output" gorm:"type:text"`
	ExitCode   int       `json:"exit_code"`
	StartTime  time.Time `json:"start_time" gorm:"not null;index"`
	EndTime    time.Time `json:"end_time"`
	Duration   int64     `json:"duration_ms"`
	WorkingDir string    `json:"working_dir"`
	Success    bool      `json:"success" gorm:"index"`
	CreatedAt  time.Time `json:"created_at"`
}

// SaveCommand 保存命令
func (chm *CommandHistoryManager) SaveCommand(userID, hostID int64, record *CommandRecord) error {
	history := &TerminalCommandHistory{
		UserID:     userID,
		HostID:     hostID,
		Command:    record.Command,
		Output:     record.Output,
		ExitCode:   record.ExitCode,
		StartTime:  record.StartTime,
		EndTime:    record.EndTime,
		Duration:   record.Duration,
		WorkingDir: record.WorkingDir,
		Success:    record.Success,
		CreatedAt:  time.Now(),
	}

	if err := chm.db.Create(history).Error; err != nil {
		chm.logger.WithError(err).Error("Failed to save command history")
		return err
	}

	return nil
}

// GetHistory 获取历史记录
func (chm *CommandHistoryManager) GetHistory(userID, hostID int64, limit int) ([]*TerminalCommandHistory, error) {
	var history []*TerminalCommandHistory

	query := chm.db.Where("user_id = ?", userID)
	if hostID > 0 {
		query = query.Where("host_id = ?", hostID)
	}

	if err := query.Order("created_at DESC").Limit(limit).Find(&history).Error; err != nil {
		return nil, err
	}

	return history, nil
}

// TerminalSecurityManager 终端安全管理器
type TerminalSecurityManager struct {
	logger *logrus.Logger
	config *TerminalConfig
}

// NewTerminalSecurityManager 创建终端安全管理器
func NewTerminalSecurityManager(logger *logrus.Logger, config *TerminalConfig) *TerminalSecurityManager {
	return &TerminalSecurityManager{
		logger: logger,
		config: config,
	}
}

// Start 启动安全管理器
func (tsm *TerminalSecurityManager) Start(ctx context.Context) error {
	tsm.logger.Info("Terminal security manager started")
	return nil
}

// ValidateCommand 验证命令
func (tsm *TerminalSecurityManager) ValidateCommand(command string) (bool, string) {
	// 检查命令长度
	if len(command) > tsm.config.MaxCommandLength {
		return true, "Command too long"
	}

	// 检查阻止列表
	for _, blocked := range tsm.config.BlockedCommands {
		if command == blocked || (len(blocked) > 0 && len(command) >= len(blocked) && command[:len(blocked)] == blocked) {
			return true, fmt.Sprintf("Command '%s' is blocked", blocked)
		}
	}

	// 检查允许列表（如果配置了）
	if len(tsm.config.AllowedCommands) > 0 {
		allowed := false
		for _, allowedCmd := range tsm.config.AllowedCommands {
			if command == allowedCmd || (len(allowedCmd) > 0 && len(command) >= len(allowedCmd) && command[:len(allowedCmd)] == allowedCmd) {
				allowed = true
				break
			}
		}
		if !allowed {
			return true, "Command not in allowed list"
		}
	}

	return false, ""
}

// TerminalMonitor 终端监控器
type TerminalMonitor struct {
	logger  *logrus.Logger
	manager *EnhancedTerminalManager
}

// NewTerminalMonitor 创建终端监控器
func NewTerminalMonitor(logger *logrus.Logger, manager *EnhancedTerminalManager) *TerminalMonitor {
	return &TerminalMonitor{
		logger:  logger,
		manager: manager,
	}
}

// Start 启动监控器
func (tm *TerminalMonitor) Start(ctx context.Context) error {
	// 启动监控协程
	go tm.monitorRoutine(ctx)

	tm.logger.Info("Terminal monitor started")
	return nil
}

// monitorRoutine 监控协程
func (tm *TerminalMonitor) monitorRoutine(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			tm.collectMetrics()
		}
	}
}

// collectMetrics 收集指标
func (tm *TerminalMonitor) collectMetrics() {
	metrics := tm.manager.GetMetrics()

	tm.logger.WithFields(logrus.Fields{
		"active_sessions":    metrics.ActiveSessions,
		"total_sessions":     metrics.TotalSessions,
		"total_commands":     metrics.TotalCommands,
		"failed_commands":    metrics.FailedCommands,
		"avg_session_time":   metrics.AvgSessionTime,
		"avg_command_time":   metrics.AvgCommandTime,
		"data_transferred":   metrics.DataTransferred,
	}).Debug("Terminal metrics collected")
}
