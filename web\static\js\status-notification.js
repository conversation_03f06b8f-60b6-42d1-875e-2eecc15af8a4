/* ========================================
   实时状态与通知系统JavaScript
   提供完整的状态监控和通知功能
   ======================================== */

class StatusNotificationSystem {
    constructor() {
        this.notifications = [];
        this.connectionStatus = 'connecting';
        this.healthMetrics = {
            cpu: { value: 0, status: 'normal', change: 0 },
            memory: { value: 0, status: 'normal', change: 0 },
            disk: { value: 0, status: 'normal', change: 0 },
            network: { value: 0, status: 'normal', change: 0 }
        };
        
        this.toastQueue = [];
        this.maxToasts = 5;
        this.toastDuration = 5000;
        
        this.init();
    }
    
    init() {
        this.createNotificationCenter();
        this.createToastContainer();
        this.createConnectionStatus();
        this.createHealthMonitor();
        this.bindEvents();
        this.startHealthMonitoring();
        
        console.log('📊 状态通知系统已初始化');
    }
    
    // 创建通知中心
    createNotificationCenter() {
        const notificationCenter = document.createElement('div');
        notificationCenter.className = 'notification-center';
        notificationCenter.id = 'notification-center';
        notificationCenter.innerHTML = this.createNotificationCenterHTML();
        
        document.body.appendChild(notificationCenter);
        
        // 添加通知按钮到导航栏
        const navbar = document.querySelector('.navbar-right');
        if (navbar) {
            const notificationBtn = document.createElement('button');
            notificationBtn.className = 'notification-toggle-btn';
            notificationBtn.innerHTML = `
                <i class="bi bi-bell"></i>
                <span class="notification-badge" id="notification-badge" style="display: none;">0</span>
            `;
            notificationBtn.title = '通知中心';
            notificationBtn.onclick = () => this.toggleNotificationCenter();
            
            navbar.appendChild(notificationBtn);
        }
    }
    
    createNotificationCenterHTML() {
        return `
            <div class="notification-header">
                <h3 class="notification-title">通知中心</h3>
                <div class="notification-actions">
                    <button class="notification-action-btn" onclick="window.statusNotificationSystem.markAllAsRead()" title="全部标记为已读">
                        <i class="bi bi-check2-all"></i>
                    </button>
                    <button class="notification-action-btn" onclick="window.statusNotificationSystem.clearAllNotifications()" title="清空所有通知">
                        <i class="bi bi-trash"></i>
                    </button>
                    <button class="notification-action-btn" onclick="window.statusNotificationSystem.closeNotificationCenter()" title="关闭">
                        <i class="bi bi-x-lg"></i>
                    </button>
                </div>
            </div>
            
            <div class="notification-filters">
                <button class="notification-filter active" data-filter="all">全部</button>
                <button class="notification-filter" data-filter="unread">未读</button>
                <button class="notification-filter" data-filter="system">系统</button>
                <button class="notification-filter" data-filter="alert">告警</button>
                <button class="notification-filter" data-filter="info">信息</button>
            </div>
            
            <div class="notification-list" id="notification-list">
                <div class="notification-empty" style="text-align: center; padding: 40px; color: var(--text-tertiary);">
                    <i class="bi bi-bell" style="font-size: 2rem; margin-bottom: 16px; opacity: 0.5;"></i>
                    <div>暂无通知</div>
                </div>
            </div>
        `;
    }
    
    // 创建浮动通知容器
    createToastContainer() {
        const toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container';
        toastContainer.id = 'toast-container';
        
        document.body.appendChild(toastContainer);
    }
    
    // 创建连接状态栏
    createConnectionStatus() {
        const connectionStatus = document.createElement('div');
        connectionStatus.className = 'connection-status';
        connectionStatus.id = 'connection-status';
        
        document.body.appendChild(connectionStatus);
        
        this.updateConnectionStatus('connecting', '正在连接到服务器...');
    }
    
    // 创建健康监控面板
    createHealthMonitor() {
        const assistantPanel = document.getElementById('assistant-panel');
        if (!assistantPanel) return;
        
        const healthMonitor = document.createElement('div');
        healthMonitor.className = 'health-monitor';
        healthMonitor.innerHTML = this.createHealthMonitorHTML();
        
        assistantPanel.querySelector('.panel-content').appendChild(healthMonitor);
    }
    
    createHealthMonitorHTML() {
        return `
            <div class="health-header">
                <h4 class="health-title">系统健康</h4>
                <div class="health-status">
                    <div class="status-dot online"></div>
                    <span class="status-text">运行正常</span>
                </div>
            </div>
            
            <div class="health-metrics" id="health-metrics">
                <div class="health-metric">
                    <div class="metric-header">
                        <span class="metric-label">CPU使用率</span>
                        <div class="metric-status online"></div>
                    </div>
                    <div class="metric-value" id="cpu-value">0%</div>
                    <div class="metric-change neutral" id="cpu-change">--</div>
                    <div class="metric-chart">
                        <div class="metric-chart-bar" id="cpu-chart" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="health-metric">
                    <div class="metric-header">
                        <span class="metric-label">内存使用率</span>
                        <div class="metric-status online"></div>
                    </div>
                    <div class="metric-value" id="memory-value">0%</div>
                    <div class="metric-change neutral" id="memory-change">--</div>
                    <div class="metric-chart">
                        <div class="metric-chart-bar" id="memory-chart" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="health-metric">
                    <div class="metric-header">
                        <span class="metric-label">磁盘使用率</span>
                        <div class="metric-status online"></div>
                    </div>
                    <div class="metric-value" id="disk-value">0%</div>
                    <div class="metric-change neutral" id="disk-change">--</div>
                    <div class="metric-chart">
                        <div class="metric-chart-bar" id="disk-chart" style="width: 0%"></div>
                    </div>
                </div>
                
                <div class="health-metric">
                    <div class="metric-header">
                        <span class="metric-label">网络延迟</span>
                        <div class="metric-status online"></div>
                    </div>
                    <div class="metric-value" id="network-value">0ms</div>
                    <div class="metric-change neutral" id="network-change">--</div>
                    <div class="metric-chart">
                        <div class="metric-chart-bar" id="network-chart" style="width: 0%"></div>
                    </div>
                </div>
            </div>
        `;
    }
    
    // 绑定事件
    bindEvents() {
        // 通知过滤器
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('notification-filter')) {
                this.handleFilterClick(e.target);
            }
        });
        
        // 点击外部关闭通知中心
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.notification-center') && 
                !e.target.closest('.notification-toggle-btn')) {
                this.closeNotificationCenter();
            }
        });
        
        // 监听WebSocket连接状态
        if (window.websocket) {
            window.websocket.addEventListener('open', () => {
                this.updateConnectionStatus('connected', '已连接到服务器');
            });
            
            window.websocket.addEventListener('close', () => {
                this.updateConnectionStatus('disconnected', '与服务器连接断开');
            });
            
            window.websocket.addEventListener('error', () => {
                this.updateConnectionStatus('error', '连接错误');
            });
        }
    }
    
    // 添加通知
    addNotification(type, title, message, options = {}) {
        const notification = {
            id: 'notif_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
            type: type, // info, success, warning, error
            title: title,
            message: message,
            timestamp: new Date(),
            read: false,
            category: options.category || 'system',
            actions: options.actions || [],
            persistent: options.persistent || false
        };
        
        this.notifications.unshift(notification);
        this.updateNotificationCenter();
        this.updateNotificationBadge();
        
        // 显示浮动通知
        if (!options.silent) {
            this.showToast(notification);
        }
        
        // 限制通知数量
        if (this.notifications.length > 100) {
            this.notifications = this.notifications.slice(0, 100);
        }
        
        return notification.id;
    }
    
    // 显示浮动通知
    showToast(notification) {
        const toast = document.createElement('div');
        toast.className = `toast ${notification.type}`;
        toast.innerHTML = this.createToastHTML(notification);
        
        const container = document.getElementById('toast-container');
        container.appendChild(toast);
        
        // 动画显示
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        // 自动隐藏
        if (!notification.persistent) {
            setTimeout(() => {
                this.hideToast(toast);
            }, this.toastDuration);
        }
        
        // 绑定关闭事件
        const closeBtn = toast.querySelector('.toast-close');
        if (closeBtn) {
            closeBtn.onclick = () => this.hideToast(toast);
        }
        
        // 限制浮动通知数量
        const toasts = container.querySelectorAll('.toast');
        if (toasts.length > this.maxToasts) {
            this.hideToast(toasts[0]);
        }
    }
    
    createToastHTML(notification) {
        const iconMap = {
            info: 'bi-info-circle',
            success: 'bi-check-circle',
            warning: 'bi-exclamation-triangle',
            error: 'bi-x-circle'
        };
        
        return `
            <div class="toast-icon">
                <i class="bi ${iconMap[notification.type] || 'bi-info-circle'}"></i>
            </div>
            <div class="toast-content">
                <div class="toast-title">${notification.title}</div>
                <div class="toast-message">${notification.message}</div>
            </div>
            <button class="toast-close">
                <i class="bi bi-x"></i>
            </button>
        `;
    }
    
    hideToast(toast) {
        toast.classList.add('hide');
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 300);
    }
    
    // 更新连接状态
    updateConnectionStatus(status, message) {
        this.connectionStatus = status;
        const statusBar = document.getElementById('connection-status');
        
        statusBar.className = `connection-status ${status}`;
        statusBar.textContent = message;
        
        if (status === 'connected') {
            statusBar.classList.add('show');
            setTimeout(() => {
                statusBar.classList.remove('show');
            }, 3000);
        } else {
            statusBar.classList.add('show');
        }
        
        // 添加连接状态通知
        if (status === 'connected') {
            this.addNotification('success', '连接成功', message, { silent: true });
        } else if (status === 'disconnected' || status === 'error') {
            this.addNotification('error', '连接问题', message);
        }
    }
    
    // 更新健康指标
    updateHealthMetrics(metrics) {
        Object.keys(metrics).forEach(key => {
            if (this.healthMetrics[key]) {
                const oldValue = this.healthMetrics[key].value;
                this.healthMetrics[key] = { ...this.healthMetrics[key], ...metrics[key] };
                
                // 计算变化
                const change = metrics[key].value - oldValue;
                this.healthMetrics[key].change = change;
                
                // 更新UI
                this.updateMetricUI(key, this.healthMetrics[key]);
                
                // 检查告警阈值
                this.checkHealthAlerts(key, this.healthMetrics[key]);
            }
        });
    }
    
    updateMetricUI(metricKey, metric) {
        const valueElement = document.getElementById(`${metricKey}-value`);
        const changeElement = document.getElementById(`${metricKey}-change`);
        const chartElement = document.getElementById(`${metricKey}-chart`);
        
        if (valueElement) {
            const unit = metricKey === 'network' ? 'ms' : '%';
            valueElement.textContent = `${metric.value}${unit}`;
        }
        
        if (changeElement) {
            const changeText = metric.change > 0 ? `+${metric.change}` : `${metric.change}`;
            changeElement.textContent = changeText;
            changeElement.className = `metric-change ${metric.change > 0 ? 'positive' : metric.change < 0 ? 'negative' : 'neutral'}`;
        }
        
        if (chartElement) {
            const percentage = metricKey === 'network' ? Math.min(metric.value / 1000 * 100, 100) : metric.value;
            chartElement.style.width = `${percentage}%`;
        }
    }
    
    checkHealthAlerts(metricKey, metric) {
        const thresholds = {
            cpu: { warning: 70, critical: 90 },
            memory: { warning: 80, critical: 95 },
            disk: { warning: 85, critical: 95 },
            network: { warning: 500, critical: 1000 }
        };
        
        const threshold = thresholds[metricKey];
        if (!threshold) return;
        
        if (metric.value >= threshold.critical) {
            this.addNotification('error', `${metricKey.toUpperCase()}告警`, 
                `${metricKey}使用率达到${metric.value}%，已超过临界值`, 
                { category: 'alert' });
        } else if (metric.value >= threshold.warning) {
            this.addNotification('warning', `${metricKey.toUpperCase()}警告`, 
                `${metricKey}使用率达到${metric.value}%，接近告警阈值`, 
                { category: 'alert' });
        }
    }
    
    // 开始健康监控
    startHealthMonitoring() {
        // 模拟健康指标数据
        setInterval(() => {
            const metrics = {
                cpu: { 
                    value: Math.floor(Math.random() * 100),
                    status: 'normal'
                },
                memory: { 
                    value: Math.floor(Math.random() * 100),
                    status: 'normal'
                },
                disk: { 
                    value: Math.floor(Math.random() * 100),
                    status: 'normal'
                },
                network: { 
                    value: Math.floor(Math.random() * 200),
                    status: 'normal'
                }
            };
            
            this.updateHealthMetrics(metrics);
        }, 5000);
        
        // 模拟系统通知
        setTimeout(() => {
            this.addNotification('info', '系统启动', '系统已成功启动，所有服务运行正常');
        }, 2000);
        
        setTimeout(() => {
            this.addNotification('success', '备份完成', '数据库备份已成功完成');
        }, 10000);
    }
    
    // 通知中心操作
    toggleNotificationCenter() {
        const center = document.getElementById('notification-center');
        center.classList.toggle('open');
    }
    
    closeNotificationCenter() {
        const center = document.getElementById('notification-center');
        center.classList.remove('open');
    }
    
    updateNotificationCenter() {
        const list = document.getElementById('notification-list');
        if (!list) return;
        
        if (this.notifications.length === 0) {
            list.innerHTML = `
                <div class="notification-empty" style="text-align: center; padding: 40px; color: var(--text-tertiary);">
                    <i class="bi bi-bell" style="font-size: 2rem; margin-bottom: 16px; opacity: 0.5;"></i>
                    <div>暂无通知</div>
                </div>
            `;
            return;
        }
        
        list.innerHTML = this.notifications.map(notification => 
            this.createNotificationItemHTML(notification)
        ).join('');
    }
    
    createNotificationItemHTML(notification) {
        const iconMap = {
            info: 'bi-info-circle',
            success: 'bi-check-circle',
            warning: 'bi-exclamation-triangle',
            error: 'bi-x-circle'
        };
        
        return `
            <div class="notification-item ${notification.read ? '' : 'unread'}" 
                 data-id="${notification.id}"
                 onclick="window.statusNotificationSystem.markAsRead('${notification.id}')">
                <div class="notification-icon ${notification.type}">
                    <i class="bi ${iconMap[notification.type] || 'bi-info-circle'}"></i>
                </div>
                <div class="notification-content">
                    <div class="notification-title-text">${notification.title}</div>
                    <div class="notification-message">${notification.message}</div>
                    <div class="notification-meta">
                        <div class="notification-time">
                            <i class="bi bi-clock"></i>
                            <span>${this.formatTime(notification.timestamp)}</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }
    
    updateNotificationBadge() {
        const badge = document.getElementById('notification-badge');
        if (!badge) return;
        
        const unreadCount = this.notifications.filter(n => !n.read).length;
        
        if (unreadCount > 0) {
            badge.textContent = unreadCount > 99 ? '99+' : unreadCount;
            badge.style.display = 'block';
        } else {
            badge.style.display = 'none';
        }
    }
    
    markAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification) {
            notification.read = true;
            this.updateNotificationCenter();
            this.updateNotificationBadge();
        }
    }
    
    markAllAsRead() {
        this.notifications.forEach(n => n.read = true);
        this.updateNotificationCenter();
        this.updateNotificationBadge();
    }
    
    clearAllNotifications() {
        if (confirm('确定要清空所有通知吗？')) {
            this.notifications = [];
            this.updateNotificationCenter();
            this.updateNotificationBadge();
        }
    }
    
    handleFilterClick(filterBtn) {
        // 更新过滤器状态
        document.querySelectorAll('.notification-filter').forEach(btn => {
            btn.classList.remove('active');
        });
        filterBtn.classList.add('active');
        
        // 应用过滤器
        const filter = filterBtn.dataset.filter;
        this.applyNotificationFilter(filter);
    }
    
    applyNotificationFilter(filter) {
        let filteredNotifications = this.notifications;
        
        switch (filter) {
            case 'unread':
                filteredNotifications = this.notifications.filter(n => !n.read);
                break;
            case 'system':
                filteredNotifications = this.notifications.filter(n => n.category === 'system');
                break;
            case 'alert':
                filteredNotifications = this.notifications.filter(n => n.category === 'alert');
                break;
            case 'info':
                filteredNotifications = this.notifications.filter(n => n.type === 'info');
                break;
        }
        
        const list = document.getElementById('notification-list');
        if (filteredNotifications.length === 0) {
            list.innerHTML = `
                <div class="notification-empty" style="text-align: center; padding: 40px; color: var(--text-tertiary);">
                    <i class="bi bi-bell" style="font-size: 2rem; margin-bottom: 16px; opacity: 0.5;"></i>
                    <div>没有符合条件的通知</div>
                </div>
            `;
        } else {
            list.innerHTML = filteredNotifications.map(notification => 
                this.createNotificationItemHTML(notification)
            ).join('');
        }
    }
    
    formatTime(timestamp) {
        const now = new Date();
        const date = new Date(timestamp);
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        
        if (diffMins < 1) return '刚刚';
        if (diffMins < 60) return `${diffMins}分钟前`;
        if (diffHours < 24) return `${diffHours}小时前`;
        
        return date.toLocaleDateString('zh-CN');
    }
}

// 全局状态通知系统实例
window.statusNotificationSystem = new StatusNotificationSystem();
