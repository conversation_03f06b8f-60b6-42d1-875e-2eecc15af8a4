/* ========================================
   键盘快捷键系统样式
   提供完整的键盘导航和快捷键支持
   ======================================== */

/* 快捷键帮助面板 */
.shortcuts-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  max-width: 700px;
  max-height: 80vh;
  overflow: hidden;
  z-index: 10000;
  display: none;
}

.shortcuts-panel.show {
  display: block;
  animation: modalEnter var(--duration-normal) var(--easing-ease);
}

@keyframes modalEnter {
  0% {
    opacity: 0;
    transform: translate(-50%, -50%) scale(0.9);
  }
  100% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1);
  }
}

.shortcuts-backdrop {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 9999;
  display: none;
}

.shortcuts-backdrop.show {
  display: block;
  animation: backdropEnter var(--duration-fast) ease-out;
}

@keyframes backdropEnter {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

.shortcuts-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.shortcuts-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.shortcuts-title-icon {
  font-size: 20px;
  color: var(--color-primary);
}

.shortcuts-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  background: none;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.shortcuts-close:hover {
  background: var(--bg-hover);
  color: var(--text-primary);
}

.shortcuts-content {
  max-height: calc(80vh - 80px);
  overflow-y: auto;
  padding: var(--space-4);
}

.shortcuts-search {
  position: relative;
  margin-bottom: var(--space-4);
}

.shortcuts-search-input {
  width: 100%;
  padding: var(--space-2) var(--space-3) var(--space-2) var(--space-8);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--duration-fast) var(--easing-ease);
}

.shortcuts-search-input:focus {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

.shortcuts-search-icon {
  position: absolute;
  left: var(--space-3);
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-tertiary);
  font-size: 14px;
}

.shortcuts-section {
  margin-bottom: var(--space-6);
}

.shortcuts-section:last-child {
  margin-bottom: 0;
}

.shortcuts-section-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-3);
  padding-bottom: var(--space-2);
  border-bottom: 1px solid var(--border-primary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.shortcuts-section-icon {
  font-size: 16px;
  color: var(--color-primary);
}

.shortcuts-list {
  display: grid;
  gap: var(--space-2);
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--easing-ease);
  border: 1px solid transparent;
}

.shortcut-item:hover {
  background: var(--bg-hover);
  border-color: var(--border-secondary);
}

.shortcut-item.highlighted {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
}

.shortcut-description {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

.shortcut-description-icon {
  font-size: 14px;
  color: var(--text-tertiary);
}

.shortcut-keys {
  display: flex;
  gap: var(--space-1);
  align-items: center;
}

.shortcut-key {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 28px;
  height: 28px;
  padding: 0 var(--space-2);
  background: var(--bg-tertiary);
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  font-family: var(--font-family-mono);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shortcut-plus {
  color: var(--text-tertiary);
  font-size: var(--font-size-xs);
  margin: 0 var(--space-1);
  font-weight: var(--font-weight-normal);
}

/* 键盘导航指示器 */
.keyboard-navigation-indicator {
  position: fixed;
  top: var(--space-4);
  left: var(--space-4);
  background: var(--color-primary);
  color: white;
  padding: var(--space-2) var(--space-3);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  z-index: 9998;
  opacity: 0;
  transform: translateY(-10px);
  transition: all var(--duration-normal) var(--easing-ease);
  pointer-events: none;
}

.keyboard-navigation-indicator.show {
  opacity: 1;
  transform: translateY(0);
}

/* 快捷键提示 */
.shortcut-hint {
  position: absolute;
  top: -30px;
  right: 0;
  background: var(--text-primary);
  color: var(--bg-primary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  opacity: 0;
  transform: translateY(5px);
  transition: all var(--duration-fast) var(--easing-ease);
  pointer-events: none;
  z-index: 1000;
}

.shortcut-hint::after {
  content: '';
  position: absolute;
  top: 100%;
  right: 8px;
  border: 4px solid transparent;
  border-top-color: var(--text-primary);
}

.has-shortcut:hover .shortcut-hint,
.has-shortcut:focus .shortcut-hint {
  opacity: 1;
  transform: translateY(0);
}

/* 键盘模式样式 */
.keyboard-mode {
  --focus-ring-width: 3px;
  --focus-ring-color: var(--color-primary);
}

.keyboard-mode *:focus {
  outline: var(--focus-ring-width) solid var(--focus-ring-color);
  outline-offset: 2px;
}

.keyboard-mode .btn:focus,
.keyboard-mode .action-btn:focus {
  transform: scale(1.05);
  box-shadow: 0 0 0 var(--focus-ring-width) var(--focus-ring-color);
}

.keyboard-mode .message:focus {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
}

.keyboard-mode .chat-item:focus {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
}

/* 快捷键状态指示器 */
.shortcuts-status {
  position: fixed;
  bottom: var(--space-4);
  right: var(--space-4);
  background: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-2) var(--space-3);
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  z-index: 9997;
  opacity: 0;
  transform: translateY(10px);
  transition: all var(--duration-normal) var(--easing-ease);
  pointer-events: none;
}

.shortcuts-status.show {
  opacity: 1;
  transform: translateY(0);
}

.shortcuts-status-text {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.shortcuts-status-icon {
  font-size: 12px;
  color: var(--color-primary);
}

/* 命令面板样式 */
.command-palette {
  position: fixed;
  top: 20%;
  left: 50%;
  transform: translateX(-50%);
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  width: 90%;
  max-width: 600px;
  z-index: 10001;
  display: none;
}

.command-palette.show {
  display: block;
  animation: commandPaletteEnter var(--duration-normal) var(--easing-ease);
}

@keyframes commandPaletteEnter {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(-20px) scale(0.95);
  }
  100% {
    opacity: 1;
    transform: translateX(-50%) translateY(0) scale(1);
  }
}

.command-input {
  width: 100%;
  padding: var(--space-4);
  border: none;
  background: none;
  font-size: var(--font-size-lg);
  color: var(--text-primary);
  outline: none;
  border-bottom: 1px solid var(--border-primary);
}

.command-input::placeholder {
  color: var(--text-tertiary);
}

.command-results {
  max-height: 400px;
  overflow-y: auto;
  padding: var(--space-2);
}

.command-item {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.command-item:hover,
.command-item.selected {
  background: var(--bg-hover);
}

.command-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--color-primary-light);
  color: var(--color-primary);
  font-size: 16px;
}

.command-content {
  flex: 1;
}

.command-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.command-description {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.command-shortcut {
  display: flex;
  gap: var(--space-1);
}

/* 响应式调整 */
@media (max-width: 768px) {
  .shortcuts-panel {
    max-width: 95vw;
    max-height: 90vh;
  }
  
  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--space-2);
  }
  
  .shortcut-keys {
    align-self: flex-end;
  }
  
  .command-palette {
    width: 95%;
    top: 10%;
  }
  
  .keyboard-navigation-indicator {
    top: var(--space-2);
    left: var(--space-2);
    right: var(--space-2);
    text-align: center;
  }
}

@media (max-width: 480px) {
  .shortcuts-content {
    padding: var(--space-3);
  }
  
  .shortcuts-header {
    padding: var(--space-3);
  }
  
  .shortcut-key {
    min-width: 24px;
    height: 24px;
    font-size: 10px;
  }
  
  .shortcuts-status {
    bottom: var(--space-2);
    right: var(--space-2);
    left: var(--space-2);
    text-align: center;
  }
}

/* 打印样式 */
@media print {
  .shortcuts-panel,
  .shortcuts-backdrop,
  .keyboard-navigation-indicator,
  .shortcuts-status,
  .command-palette {
    display: none !important;
  }
}
