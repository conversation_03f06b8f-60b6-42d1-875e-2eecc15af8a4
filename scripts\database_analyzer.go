package main

import (
	"fmt"
	"log"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DatabaseAnalyzer 数据库分析器
type DatabaseAnalyzer struct {
	db *gorm.DB
}

// IndexInfo 索引信息
type IndexInfo struct {
	Name     string `json:"name"`
	Table    string `json:"table"`
	Columns  string `json:"columns"`
	Unique   bool   `json:"unique"`
	Partial  bool   `json:"partial"`
}

// TableStats 表统计信息
type TableStats struct {
	Name     string `json:"name"`
	RowCount int64  `json:"row_count"`
	Size     int64  `json:"size"`
}

// QueryStats 查询统计
type QueryStats struct {
	SQL           string        `json:"sql"`
	ExecutionTime time.Duration `json:"execution_time"`
	RowsAffected  int64         `json:"rows_affected"`
}

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("aiops.db"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Silent),
	})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	analyzer := &DatabaseAnalyzer{db: db}

	fmt.Println("🔍 AI运维管理平台数据库性能分析报告")
	fmt.Println("=" + string(make([]byte, 50)))

	// 1. 分析索引
	fmt.Println("\n📊 1. 索引分析")
	analyzer.analyzeIndexes()

	// 2. 分析表统计
	fmt.Println("\n📈 2. 表统计分析")
	analyzer.analyzeTableStats()

	// 3. 分析查询性能
	fmt.Println("\n⚡ 3. 查询性能分析")
	analyzer.analyzeQueryPerformance()

	// 4. 生成优化建议
	fmt.Println("\n💡 4. 优化建议")
	analyzer.generateOptimizationSuggestions()

	fmt.Println("\n✅ 数据库分析完成!")
}

// analyzeIndexes 分析索引
func (da *DatabaseAnalyzer) analyzeIndexes() {
	// 查询所有索引
	rows, err := da.db.Raw("SELECT name, tbl_name, sql FROM sqlite_master WHERE type='index' AND name NOT LIKE 'sqlite_%'").Rows()
	if err != nil {
		fmt.Printf("❌ 查询索引失败: %v\n", err)
		return
	}
	defer rows.Close()

	fmt.Println("当前索引列表:")
	indexCount := 0
	for rows.Next() {
		var name, table, sql string
		if err := rows.Scan(&name, &table, &sql); err != nil {
			continue
		}
		fmt.Printf("  - %s (表: %s)\n", name, table)
		indexCount++
	}

	fmt.Printf("总计: %d 个索引\n", indexCount)
}

// analyzeTableStats 分析表统计
func (da *DatabaseAnalyzer) analyzeTableStats() {
	tables := []string{"users", "hosts", "alerts", "operation_logs", "chat_sessions", "chat_messages"}

	fmt.Println("表数据统计:")
	totalRows := int64(0)

	for _, table := range tables {
		var count int64
		if err := da.db.Raw(fmt.Sprintf("SELECT COUNT(*) FROM %s", table)).Scan(&count).Error; err != nil {
			fmt.Printf("  - %s: 查询失败 (%v)\n", table, err)
			continue
		}
		fmt.Printf("  - %s: %d 行\n", table, count)
		totalRows += count
	}

	fmt.Printf("总计: %d 行数据\n", totalRows)
}

// analyzeQueryPerformance 分析查询性能
func (da *DatabaseAnalyzer) analyzeQueryPerformance() {
	testQueries := []string{
		"SELECT * FROM hosts WHERE status = 'active'",
		"SELECT * FROM users WHERE role = 'admin'",
		"SELECT * FROM alerts WHERE created_at > datetime('now', '-1 day')",
		"SELECT * FROM operation_logs ORDER BY created_at DESC LIMIT 100",
		"SELECT COUNT(*) FROM chat_messages WHERE session_id = 1",
	}

	fmt.Println("查询性能测试:")
	for _, query := range testQueries {
		start := time.Now()

		rows, err := da.db.Raw(query).Rows()
		if err != nil {
			fmt.Printf("  - 查询失败: %s\n", query)
			continue
		}

		// 计算行数
		rowCount := 0
		for rows.Next() {
			rowCount++
		}
		rows.Close()

		duration := time.Since(start)
		fmt.Printf("  - %s: %v (%d行)\n", query, duration, rowCount)
	}
}

// generateOptimizationSuggestions 生成优化建议
func (da *DatabaseAnalyzer) generateOptimizationSuggestions() {
	fmt.Println("数据库优化建议:")
	
	// 检查缺失的索引
	suggestions := []string{
		"为 chat_messages.session_id 添加索引以优化会话查询",
		"为 operation_logs.created_at 添加索引以优化时间范围查询",
		"为 alerts.severity 添加索引以优化告警级别查询",
		"为 hosts.environment 添加复合索引以优化环境筛选",
		"考虑为 chat_messages.created_at 添加索引以优化时间排序",
	}

	for i, suggestion := range suggestions {
		fmt.Printf("  %d. %s\n", i+1, suggestion)
	}

	fmt.Println("\n性能优化建议:")
	performanceTips := []string{
		"启用 WAL 模式以提高并发性能",
		"增加缓存大小到 128MB",
		"使用 ANALYZE 命令更新统计信息",
		"定期执行 VACUUM 清理数据库",
		"考虑分区大表以提高查询性能",
	}

	for i, tip := range performanceTips {
		fmt.Printf("  %d. %s\n", i+1, tip)
	}
}
