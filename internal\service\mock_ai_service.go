package service

import (
	"context"
	"fmt"
	"regexp"
	"sort"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// MockAIService 模拟AI服务，用于演示和测试
type MockAIService struct {
	logger *logrus.Logger
}

// NewMockAIService 创建模拟AI服务
func NewMockAIService(logger *logrus.Logger) *MockAIService {
	return &MockAIService{
		logger: logger,
	}
}

// MockIntentResult 模拟意图识别结果
type MockIntentResult struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
}

// RecognizeIntent 增强的意图识别
func (m *MockAIService) RecognizeIntent(ctx context.Context, userInput string) (*MockIntentResult, error) {
	m.logger.WithField("input", userInput).Info("Enhanced AI: Recognizing intent")

	// 清理输入
	input := strings.TrimSpace(userInput)

	// 使用增强的意图识别引擎
	return m.enhancedIntentRecognition(input)
}

// enhancedIntentRecognition 增强的意图识别引擎（第四阶段：语义理解增强）
func (m *MockAIService) enhancedIntentRecognition(input string) (*MockIntentResult, error) {
	// 多层次意图识别
	candidates := make([]*MockIntentResult, 0)

	// L0: 语义预处理和标准化
	normalizedInput := m.semanticPreprocessing(input)

	// L1: 精确关键词匹配（最高优先级）
	if result := m.exactKeywordMatching(normalizedInput); result != nil {
		candidates = append(candidates, result)
	}

	// L1.5: 语义扩展匹配（新增）
	if result := m.semanticExpansionMatching(normalizedInput); result != nil {
		candidates = append(candidates, result)
	}

	// L2: 运维场景意图识别（增强版）
	intentCheckers := []func(string) *MockIntentResult{
		m.recognizeHostManagement,       // 主机管理
		m.recognizeCommandExecution,     // 命令执行
		m.recognizeServiceManagement,    // 服务管理
		m.recognizeLogAnalysis,          // 日志分析
		m.recognizePerformanceDiagnosis, // 性能诊断
		m.recognizeNetworkDiagnosis,     // 网络诊断
		m.recognizeSecurityCheck,        // 安全检查
		m.recognizeBackupRestore,        // 备份恢复
		m.recognizeAlertManagement,      // 告警管理
		m.recognizeSystemMonitoring,     // 系统监控
	}

	for _, checker := range intentCheckers {
		if result := checker(normalizedInput); result != nil {
			candidates = append(candidates, result)
		}
	}

	// L3: 语义相似度匹配（新增）
	if result := m.semanticSimilarityMatching(normalizedInput); result != nil {
		candidates = append(candidates, result)
	}

	// L4: 上下文语义推理（新增）
	if result := m.contextualSemanticReasoning(normalizedInput); result != nil {
		candidates = append(candidates, result)
	}

	// L5: 选择最佳候选（增强版）
	if len(candidates) > 0 {
		result := m.selectBestCandidateWithSemantics(candidates, normalizedInput, input)
		return result, nil
	}

	// 默认为一般对话
	return &MockIntentResult{
		Type:       "general_chat",
		Confidence: 0.5,
		Parameters: make(map[string]interface{}),
	}, nil
}

// exactKeywordMatching 精确关键词匹配
func (m *MockAIService) exactKeywordMatching(input string) *MockIntentResult {
	// 高优先级精确匹配模式 - 更新为新的四大类意图
	exactPatterns := map[string]*MockIntentResult{
		"添加主机": {
			Type:       "database_operations",
			Confidence: 0.98,
			Parameters: map[string]interface{}{"operation": "insert", "table": "hosts"},
		},
		"删除主机": {
			Type:       "database_operations",
			Confidence: 0.98,
			Parameters: map[string]interface{}{"operation": "delete", "table": "hosts"},
		},
		"移除主机": {
			Type:       "database_operations",
			Confidence: 0.98,
			Parameters: map[string]interface{}{"operation": "delete", "table": "hosts"},
		},
		"列出主机": {
			Type:       "database_operations",
			Confidence: 0.98,
			Parameters: map[string]interface{}{
				"operation":   "select",
				"table":       "hosts",
				"sql":         "SELECT id, name, ip_address, username, status, environment, created_at FROM hosts WHERE deleted_at IS NULL ORDER BY created_at DESC",
				"description": "查询所有主机列表",
			},
		},
		"主机列表": {
			Type:       "database_operations",
			Confidence: 0.98,
			Parameters: map[string]interface{}{
				"operation":   "select",
				"table":       "hosts",
				"sql":         "SELECT id, name, ip_address, username, status, environment, created_at FROM hosts WHERE deleted_at IS NULL ORDER BY created_at DESC",
				"description": "查询所有主机列表",
			},
		},
		"重启服务": {
			Type:       "ssh_operations",
			Confidence: 0.98,
			Parameters: map[string]interface{}{"operation": "manage_service", "action": "restart"},
		},
		"查看日志": {
			Type:       "monitoring_operations",
			Confidence: 0.98,
			Parameters: map[string]interface{}{"operation": "log_analysis"},
		},
		"性能监控": {
			Type:       "monitoring_operations",
			Confidence: 0.98,
			Parameters: map[string]interface{}{"operation": "performance_analysis"},
		},
		"帮助": {
			Type:       "general_chat",
			Confidence: 0.98,
			Parameters: map[string]interface{}{"intent": "help"},
		},
		"你好": {
			Type:       "general_chat",
			Confidence: 0.95,
			Parameters: map[string]interface{}{"intent": "greeting"},
		},
	}

	for pattern, result := range exactPatterns {
		if strings.Contains(input, pattern) {
			return result
		}
	}

	return nil
}

// selectBestCandidate 选择最佳候选意图
func (m *MockAIService) selectBestCandidate(candidates []*MockIntentResult, input string) *MockIntentResult {
	if len(candidates) == 0 {
		return nil
	}

	// 按置信度排序，选择最高的
	best := candidates[0]
	for _, candidate := range candidates[1:] {
		if candidate.Confidence > best.Confidence {
			best = candidate
		}
	}

	// 如果置信度相同，优先选择参数更完整的
	if len(candidates) > 1 {
		for _, candidate := range candidates {
			if candidate.Confidence == best.Confidence &&
				len(candidate.Parameters) > len(best.Parameters) {
				best = candidate
			}
		}
	}

	return best
}

// recognizeHostAddition 识别主机添加意图
func (m *MockAIService) recognizeHostAddition(input string) *MockIntentResult {
	// 去除首尾空格
	input = strings.TrimSpace(input)

	// 模式1: "添加主机 IP 用户名 密码"
	pattern1 := regexp.MustCompile(`(?i)(添加主机|主机添加|新增主机)\s+(\d+\.\d+\.\d+\.\d+)\s+(\S+)\s+(.+)`)
	if matches := pattern1.FindStringSubmatch(input); len(matches) == 5 {
		return &MockIntentResult{
			Type:       "host_management",
			Confidence: 0.98,
			Parameters: map[string]interface{}{
				"action":   "add_host",
				"ip":       strings.TrimSpace(matches[2]),
				"username": strings.TrimSpace(matches[3]),
				"password": strings.TrimSpace(matches[4]),
			},
		}
	}

	// 模式2: "IP 用户名 密码" (纯参数格式) - 支持特殊字符密码
	pattern2 := regexp.MustCompile(`^(\d+\.\d+\.\d+\.\d+)\s+(\S+)\s+(.+)$`)
	if matches := pattern2.FindStringSubmatch(input); len(matches) == 4 {
		return &MockIntentResult{
			Type:       "host_management",
			Confidence: 0.95,
			Parameters: map[string]interface{}{
				"action":   "add_host",
				"ip":       strings.TrimSpace(matches[1]),
				"username": strings.TrimSpace(matches[2]),
				"password": strings.TrimSpace(matches[3]),
			},
		}
	}

	// 模式3: 更灵活的IP用户名密码格式（用空格分隔的三个部分）
	parts := strings.Fields(input)
	if len(parts) == 3 {
		// 验证第一个部分是否为IP地址
		if matched, _ := regexp.MatchString(`^\d+\.\d+\.\d+\.\d+$`, parts[0]); matched {
			return &MockIntentResult{
				Type:       "host_management",
				Confidence: 0.92,
				Parameters: map[string]interface{}{
					"action":   "add_host",
					"ip":       parts[0],
					"username": parts[1],
					"password": parts[2],
				},
			}
		}
	}

	// 模式4: 只有"添加主机"或"主机添加"
	pattern4 := regexp.MustCompile(`(?i)^(添加主机|主机添加|新增主机)$`)
	if pattern4.MatchString(input) {
		return &MockIntentResult{
			Type:       "host_management",
			Confidence: 0.8,
			Parameters: map[string]interface{}{
				"action": "add_host",
			},
		}
	}

	return nil
}

// recognizeHostManagement 识别主机管理意图（更新为数据库操作）
func (m *MockAIService) recognizeHostManagement(input string) *MockIntentResult {
	// 删除主机模式
	deletePatterns := []string{
		"删除.*主机", "移除.*主机", "删掉.*主机", "去掉.*主机",
		"删除.*这个主机", "移除.*这个主机", "删掉.*这个主机",
		"主机.*删除", "主机.*移除", "主机.*删掉",
	}

	for _, pattern := range deletePatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			confidence := 0.95
			params := map[string]interface{}{
				"operation": "delete",
				"table":     "hosts",
				"target":    make(map[string]interface{}),
			}

			target := params["target"].(map[string]interface{})
			// 提取IP地址或主机名
			extractedIP := m.extractHostIP(input)
			extractedName := m.extractHostName(input)

			// 添加调试日志
			logrus.WithFields(logrus.Fields{
				"input":          input,
				"extracted_ip":   extractedIP,
				"extracted_name": extractedName,
				"target_before":  target,
			}).Info("Mock AI: Extracting host info for delete operation")

			if extractedIP != "" {
				target["ip"] = extractedIP
				confidence = 0.98
				logrus.WithField("ip", extractedIP).Info("Mock AI: IP extracted successfully")
			} else if extractedName != "" {
				target["name"] = extractedName
				confidence = 0.95
				logrus.WithField("name", extractedName).Info("Mock AI: Name extracted successfully")
			} else {
				logrus.Warn("Mock AI: No IP or name extracted from input")
			}

			return &MockIntentResult{
				Type:       "database_operations",
				Confidence: confidence,
				Parameters: params,
			}
		}
	}

	// 添加主机模式
	addPatterns := []string{
		"添加.*主机", "新增.*主机", "创建.*主机", "主机.*添加",
	}

	for _, pattern := range addPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			confidence := 0.95
			params := map[string]interface{}{
				"operation": "insert",
				"table":     "hosts",
				"data":      make(map[string]interface{}),
			}

			data := params["data"].(map[string]interface{})
			// 提取主机信息
			if ip := m.extractHostIP(input); ip != "" {
				data["ip"] = ip
				confidence = 0.98
			}
			// 可以进一步提取用户名和密码
			if username := m.extractUsername(input); username != "" {
				data["username"] = username
			}
			if password := m.extractPassword(input); password != "" {
				data["password"] = password
			}

			return &MockIntentResult{
				Type:       "database_operations",
				Confidence: confidence,
				Parameters: params,
			}
		}
	}

	// 查看主机列表
	listPatterns := []string{
		"查看.*主机", "主机.*列表", "显示.*主机", "现有主机", "主机.*状态",
		"查询.*主机", "主机.*信息", "主机.*账号", "列出主机", "主机列表",
		"所有主机", "全部主机", "主机清单", "主机概览",
	}

	for _, pattern := range listPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			confidence := 0.9
			params := map[string]interface{}{
				"operation":   "select",
				"table":       "hosts",
				"sql":         "SELECT id, name, ip_address, username, status, environment, created_at FROM hosts WHERE deleted_at IS NULL ORDER BY created_at DESC",
				"description": "查询主机列表",
				"conditions":  make(map[string]interface{}),
			}

			conditions := params["conditions"].(map[string]interface{})
			// 检查是否有特定过滤条件
			if strings.Contains(input, "状态") {
				conditions["filter"] = "status"
			} else if strings.Contains(input, "账号") {
				conditions["filter"] = "account"
			} else if strings.Contains(input, "信息") {
				conditions["filter"] = "info"
			}

			return &MockIntentResult{
				Type:       "database_operations",
				Confidence: confidence,
				Parameters: params,
			}
		}
	}

	return nil
}

// recognizeCommandExecution 识别命令执行意图
func (m *MockAIService) recognizeCommandExecution(input string) *MockIntentResult {
	// 命令执行模式 - 检测各种命令执行表达
	commandPatterns := []string{
		// 直接命令执行
		"执行.*命令", "运行.*命令", "执行.*指令", "运行.*指令",
		"execute.*command", "run.*command", "exec.*command",

		// 连接并执行
		"连接.*执行", "登录.*执行", "ssh.*执行", "远程.*执行",
		"连接.*并.*执行", "登录.*并.*执行", "ssh.*并.*执行",

		// 在主机上执行
		"在.*主机.*执行", "在.*服务器.*执行", "在.*机器.*执行",
		"主机.*执行", "服务器.*执行", "机器.*执行",

		// 具体命令模式
		"uname", "ls", "ps", "top", "df", "free", "cat", "grep", "find",
		"systemctl", "service", "netstat", "ss", "iptables", "crontab",
		"tail", "head", "less", "more", "vi", "vim", "nano",
		"chmod", "chown", "mkdir", "rmdir", "cp", "mv", "rm",
		"wget", "curl", "ping", "traceroute", "nslookup", "dig",
	}

	for _, pattern := range commandPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			// 提取主机信息和命令
			hostIP := m.extractHostIP(input)
			command := m.extractCommand(input)

			confidence := 0.9
			if hostIP != "" && command != "" {
				confidence = 0.95
			} else if command != "" {
				confidence = 0.85
			}

			return &MockIntentResult{
				Type:       "command_execution",
				Confidence: confidence,
				Parameters: map[string]interface{}{
					"action":  "execute",
					"host_ip": hostIP,
					"command": command,
				},
			}
		}
	}

	return nil
}

// extractHostIP 从输入中提取主机IP
func (m *MockAIService) extractHostIP(input string) string {
	// IP地址正则表达式
	ipPattern := `\b(?:\d{1,3}\.){3}\d{1,3}\b`
	re := regexp.MustCompile(ipPattern)
	matches := re.FindAllString(input, -1)
	if len(matches) > 0 {
		return matches[0]
	}
	return ""
}

// extractHostName 从输入中提取主机名
func (m *MockAIService) extractHostName(input string) string {
	// 主机名模式：匹配常见的主机名格式
	hostNamePatterns := []string{
		`主机\s*([a-zA-Z0-9\-_]+)`,
		`名为\s*([a-zA-Z0-9\-_]+)`,
		`([a-zA-Z0-9\-_]+)\s*主机`,
		`test-[a-zA-Z0-9\-_]+`,
		`web-[a-zA-Z0-9\-_]+`,
		`db-[a-zA-Z0-9\-_]+`,
		`server-[a-zA-Z0-9\-_]+`,
	}

	for _, pattern := range hostNamePatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(input)
		if len(matches) > 1 {
			return matches[1]
		} else if len(matches) > 0 {
			return matches[0]
		}
	}
	return ""
}

// extractUsername 从输入中提取用户名
func (m *MockAIService) extractUsername(input string) string {
	// 用户名模式：匹配常见的用户名格式
	usernamePatterns := []string{
		`\b(root|admin|ubuntu|user|centos|debian)\b`,
		`用户名\s*[:：]?\s*([a-zA-Z0-9_]+)`,
		`\s+([a-zA-Z0-9_]+)\s+\S+\s*$`, // IP 用户名 密码格式中的用户名
	}

	for _, pattern := range usernamePatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(input)
		if len(matches) > 1 {
			return matches[1]
		}
	}
	return ""
}

// extractPassword 从输入中提取密码
func (m *MockAIService) extractPassword(input string) string {
	// 密码模式：匹配IP 用户名 密码格式中的密码
	passwordPattern := `\d+\.\d+\.\d+\.\d+\s+[a-zA-Z0-9_]+\s+(\S+)`
	re := regexp.MustCompile(passwordPattern)
	matches := re.FindStringSubmatch(input)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// extractCommand 从输入中提取命令
func (m *MockAIService) extractCommand(input string) string {
	// 常见命令模式
	commandPatterns := []string{
		`uname\s*-[a-zA-Z]*`,
		`ls\s*-[a-zA-Z]*\s*\S*`,
		`ps\s*-[a-zA-Z]*`,
		`top\s*-[a-zA-Z]*`,
		`df\s*-[a-zA-Z]*`,
		`free\s*-[a-zA-Z]*`,
		`cat\s+\S+`,
		`grep\s+\S+`,
		`find\s+\S+`,
		`systemctl\s+\w+\s+\S+`,
		`service\s+\S+\s+\w+`,
		`netstat\s*-[a-zA-Z]*`,
		`ss\s*-[a-zA-Z]*`,
		`tail\s*-[a-zA-Z]*\s*\S*`,
		`head\s*-[a-zA-Z]*\s*\S*`,
	}

	for _, pattern := range commandPatterns {
		re := regexp.MustCompile("(?i)" + pattern)
		matches := re.FindAllString(input, -1)
		if len(matches) > 0 {
			return matches[0]
		}
	}

	// 如果没有匹配到具体命令，尝试提取引号中的内容
	quotedPattern := `["']([^"']+)["']`
	re := regexp.MustCompile(quotedPattern)
	matches := re.FindStringSubmatch(input)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

// recognizeServiceManagement 识别服务管理意图
func (m *MockAIService) recognizeServiceManagement(input string) *MockIntentResult {
	// 服务启动模式
	startPatterns := []string{
		"启动.*服务", "开启.*服务", "start.*service", "服务.*启动", "运行.*服务",
	}
	for _, pattern := range startPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "service_management",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"action":  "start",
					"service": m.extractServiceName(input),
				},
			}
		}
	}

	// 服务停止模式
	stopPatterns := []string{
		"停止.*服务", "关闭.*服务", "stop.*service", "服务.*停止", "终止.*服务",
	}
	for _, pattern := range stopPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "service_management",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"action":  "stop",
					"service": m.extractServiceName(input),
				},
			}
		}
	}

	// 服务重启模式
	restartPatterns := []string{
		"重启.*服务", "重新启动.*服务", "restart.*service", "服务.*重启",
	}
	for _, pattern := range restartPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "service_management",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"action":  "restart",
					"service": m.extractServiceName(input),
				},
			}
		}
	}

	// 服务状态查询模式
	statusPatterns := []string{
		"查看.*服务.*状态", "服务.*状态", "service.*status", "检查.*服务",
	}
	for _, pattern := range statusPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "service_management",
				Confidence: 0.85,
				Parameters: map[string]interface{}{
					"action":  "status",
					"service": m.extractServiceName(input),
				},
			}
		}
	}

	return nil
}

// extractServiceName 提取服务名称
func (m *MockAIService) extractServiceName(input string) string {
	// 常见服务名称模式
	servicePatterns := []string{
		"nginx", "apache", "mysql", "redis", "docker", "ssh", "sshd",
		"postgresql", "mongodb", "elasticsearch", "tomcat", "jenkins",
	}

	for _, service := range servicePatterns {
		if matched, _ := regexp.MatchString("(?i)"+service, input); matched {
			return service
		}
	}

	// 尝试提取引号中的服务名
	if matches := regexp.MustCompile(`["']([^"']+)["']`).FindStringSubmatch(input); len(matches) > 1 {
		return matches[1]
	}

	return "unknown"
}

// recognizeLogAnalysis 识别日志分析意图
func (m *MockAIService) recognizeLogAnalysis(input string) *MockIntentResult {
	// 查看日志模式
	viewPatterns := []string{
		"查看.*日志", "显示.*日志", "日志.*查看", "show.*log", "view.*log",
	}
	for _, pattern := range viewPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "log_analysis",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"action":   "view",
					"log_type": m.extractLogType(input),
				},
			}
		}
	}

	// 错误日志模式
	errorPatterns := []string{
		"错误.*日志", "error.*log", "异常.*日志", "故障.*日志", "失败.*日志",
	}
	for _, pattern := range errorPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "log_analysis",
				Confidence: 0.95,
				Parameters: map[string]interface{}{
					"action":   "error_analysis",
					"log_type": "error",
				},
			}
		}
	}

	// 日志搜索模式
	searchPatterns := []string{
		"搜索.*日志", "查找.*日志", "search.*log", "grep.*log",
	}
	for _, pattern := range searchPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "log_analysis",
				Confidence: 0.85,
				Parameters: map[string]interface{}{
					"action":  "search",
					"keyword": m.extractSearchKeyword(input),
				},
			}
		}
	}

	return nil
}

// extractLogType 提取日志类型
func (m *MockAIService) extractLogType(input string) string {
	logTypes := []string{
		"access", "error", "system", "application", "security", "audit",
		"nginx", "apache", "mysql", "redis", "docker",
	}

	for _, logType := range logTypes {
		if matched, _ := regexp.MatchString("(?i)"+logType, input); matched {
			return logType
		}
	}

	return "system"
}

// extractSearchKeyword 提取搜索关键词
func (m *MockAIService) extractSearchKeyword(input string) string {
	// 尝试提取引号中的关键词
	if matches := regexp.MustCompile(`["']([^"']+)["']`).FindStringSubmatch(input); len(matches) > 1 {
		return matches[1]
	}

	// 提取常见错误关键词
	errorKeywords := []string{
		"error", "exception", "failed", "timeout", "connection", "404", "500",
	}

	for _, keyword := range errorKeywords {
		if matched, _ := regexp.MatchString("(?i)"+keyword, input); matched {
			return keyword
		}
	}

	return ""
}

// recognizeSystemMonitoring 识别系统监控意图
func (m *MockAIService) recognizeSystemMonitoring(input string) *MockIntentResult {
	// CPU监控
	if matched, _ := regexp.MatchString("(?i)(cpu|处理器).*使用率", input); matched {
		return &MockIntentResult{
			Type:       "system_monitoring",
			Confidence: 0.9,
			Parameters: map[string]interface{}{
				"metric": "cpu",
				"action": "check",
			},
		}
	}

	// 内存监控
	if matched, _ := regexp.MatchString("(?i)(内存|memory).*使用率", input); matched {
		return &MockIntentResult{
			Type:       "system_monitoring",
			Confidence: 0.9,
			Parameters: map[string]interface{}{
				"metric": "memory",
				"action": "check",
			},
		}
	}

	// 磁盘监控
	if matched, _ := regexp.MatchString("(?i)(磁盘|disk).*使用率", input); matched {
		return &MockIntentResult{
			Type:       "system_monitoring",
			Confidence: 0.9,
			Parameters: map[string]interface{}{
				"metric": "disk",
				"action": "check",
			},
		}
	}

	return nil
}

// recognizePerformanceDiagnosis 识别性能诊断意图
func (m *MockAIService) recognizePerformanceDiagnosis(input string) *MockIntentResult {
	// 性能监控模式
	performancePatterns := []string{
		"性能.*监控", "性能.*分析", "performance.*monitor", "系统.*性能",
		"资源.*使用", "负载.*分析", "瓶颈.*分析",
	}
	for _, pattern := range performancePatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "performance_diagnosis",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"action": "monitor",
					"metric": m.extractPerformanceMetric(input),
				},
			}
		}
	}

	// CPU分析模式
	cpuPatterns := []string{
		"cpu.*分析", "处理器.*分析", "cpu.*占用", "cpu.*负载",
	}
	for _, pattern := range cpuPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "performance_diagnosis",
				Confidence: 0.95,
				Parameters: map[string]interface{}{
					"action": "analyze",
					"metric": "cpu",
				},
			}
		}
	}

	// 内存分析模式
	memoryPatterns := []string{
		"内存.*分析", "memory.*analysis", "内存.*占用", "内存.*泄漏",
	}
	for _, pattern := range memoryPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "performance_diagnosis",
				Confidence: 0.95,
				Parameters: map[string]interface{}{
					"action": "analyze",
					"metric": "memory",
				},
			}
		}
	}

	return nil
}

// extractPerformanceMetric 提取性能指标
func (m *MockAIService) extractPerformanceMetric(input string) string {
	metrics := []string{
		"cpu", "memory", "disk", "network", "io", "load", "throughput",
	}

	for _, metric := range metrics {
		if matched, _ := regexp.MatchString("(?i)"+metric, input); matched {
			return metric
		}
	}

	return "overall"
}

// recognizeNetworkDiagnosis 识别网络诊断意图
func (m *MockAIService) recognizeNetworkDiagnosis(input string) *MockIntentResult {
	// 网络连通性测试
	connectivityPatterns := []string{
		"ping", "连通性.*测试", "网络.*连接", "connectivity.*test",
	}
	for _, pattern := range connectivityPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "network_diagnosis",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"action": "connectivity",
					"target": m.extractNetworkTarget(input),
				},
			}
		}
	}

	// 端口检查
	portPatterns := []string{
		"端口.*检查", "port.*check", "端口.*扫描", "port.*scan",
	}
	for _, pattern := range portPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "network_diagnosis",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"action": "port_check",
					"target": m.extractNetworkTarget(input),
					"port":   m.extractPort(input),
				},
			}
		}
	}

	// 网络延迟测试
	latencyPatterns := []string{
		"网络.*延迟", "latency.*test", "延迟.*测试", "网络.*速度",
	}
	for _, pattern := range latencyPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "network_diagnosis",
				Confidence: 0.85,
				Parameters: map[string]interface{}{
					"action": "latency",
					"target": m.extractNetworkTarget(input),
				},
			}
		}
	}

	return nil
}

// extractNetworkTarget 提取网络目标
func (m *MockAIService) extractNetworkTarget(input string) string {
	// 提取IP地址
	ipPattern := regexp.MustCompile(`\b(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})\b`)
	if matches := ipPattern.FindStringSubmatch(input); len(matches) > 1 {
		return matches[1]
	}

	// 提取域名
	domainPattern := regexp.MustCompile(`\b([a-zA-Z0-9-]+\.)+[a-zA-Z]{2,}\b`)
	if matches := domainPattern.FindStringSubmatch(input); len(matches) > 0 {
		return matches[0]
	}

	return "*******" // 默认目标
}

// extractPort 提取端口号
func (m *MockAIService) extractPort(input string) string {
	portPattern := regexp.MustCompile(`\b(\d{1,5})\b`)
	matches := portPattern.FindAllStringSubmatch(input, -1)

	for _, match := range matches {
		if len(match) > 1 {
			port := match[1]
			// 验证端口范围
			if len(port) <= 5 && port != "0" {
				return port
			}
		}
	}

	return "80" // 默认端口
}

// recognizeSecurityCheck 识别安全检查意图
func (m *MockAIService) recognizeSecurityCheck(input string) *MockIntentResult {
	// 权限审计
	permissionPatterns := []string{
		"权限.*审计", "permission.*audit", "权限.*检查", "用户.*权限",
	}
	for _, pattern := range permissionPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "security_check",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"action": "permission_audit",
				},
			}
		}
	}

	// 漏洞扫描
	vulnerabilityPatterns := []string{
		"漏洞.*扫描", "vulnerability.*scan", "安全.*扫描", "security.*scan",
	}
	for _, pattern := range vulnerabilityPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "security_check",
				Confidence: 0.95,
				Parameters: map[string]interface{}{
					"action": "vulnerability_scan",
				},
			}
		}
	}

	// 安全策略检查
	policyPatterns := []string{
		"安全.*策略", "security.*policy", "防火墙.*规则", "firewall.*rules",
	}
	for _, pattern := range policyPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "security_check",
				Confidence: 0.85,
				Parameters: map[string]interface{}{
					"action": "policy_check",
				},
			}
		}
	}

	return nil
}

// recognizeBackupRestore 识别备份恢复意图
func (m *MockAIService) recognizeBackupRestore(input string) *MockIntentResult {
	// 数据备份
	backupPatterns := []string{
		"数据.*备份", "backup.*data", "备份.*文件", "创建.*备份",
	}
	for _, pattern := range backupPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "backup_restore",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"action": "backup",
					"target": m.extractBackupTarget(input),
				},
			}
		}
	}

	// 系统恢复
	restorePatterns := []string{
		"系统.*恢复", "restore.*system", "恢复.*数据", "数据.*恢复",
	}
	for _, pattern := range restorePatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "backup_restore",
				Confidence: 0.95,
				Parameters: map[string]interface{}{
					"action": "restore",
					"source": m.extractRestoreSource(input),
				},
			}
		}
	}

	// 快照管理
	snapshotPatterns := []string{
		"快照.*管理", "snapshot.*manage", "创建.*快照", "删除.*快照",
	}
	for _, pattern := range snapshotPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "backup_restore",
				Confidence: 0.85,
				Parameters: map[string]interface{}{
					"action": "snapshot",
				},
			}
		}
	}

	return nil
}

// extractBackupTarget 提取备份目标
func (m *MockAIService) extractBackupTarget(input string) string {
	// 提取路径
	pathPattern := regexp.MustCompile(`(/[^\s]+)`)
	if matches := pathPattern.FindStringSubmatch(input); len(matches) > 1 {
		return matches[1]
	}

	return "/data" // 默认备份目标
}

// extractRestoreSource 提取恢复源
func (m *MockAIService) extractRestoreSource(input string) string {
	// 提取备份文件路径
	pathPattern := regexp.MustCompile(`(/[^\s]+\.tar\.gz|/[^\s]+\.zip|/[^\s]+\.bak)`)
	if matches := pathPattern.FindStringSubmatch(input); len(matches) > 1 {
		return matches[1]
	}

	return "/backup/latest.tar.gz" // 默认恢复源
}

// recognizeAlertManagement 识别告警管理意图
func (m *MockAIService) recognizeAlertManagement(input string) *MockIntentResult {
	// 查看告警
	viewPatterns := []string{
		"查看.*告警", "显示.*告警", "告警.*列表", "alert.*list",
	}
	for _, pattern := range viewPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "alert_management",
				Confidence: 0.9,
				Parameters: map[string]interface{}{
					"action": "view",
				},
			}
		}
	}

	// 告警规则配置
	rulePatterns := []string{
		"告警.*规则", "alert.*rule", "配置.*告警", "设置.*告警",
	}
	for _, pattern := range rulePatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "alert_management",
				Confidence: 0.85,
				Parameters: map[string]interface{}{
					"action": "configure",
				},
			}
		}
	}

	// 通知设置
	notificationPatterns := []string{
		"通知.*设置", "notification.*setting", "告警.*通知", "消息.*推送",
	}
	for _, pattern := range notificationPatterns {
		if matched, _ := regexp.MatchString("(?i)"+pattern, input); matched {
			return &MockIntentResult{
				Type:       "alert_management",
				Confidence: 0.8,
				Parameters: map[string]interface{}{
					"action": "notification",
				},
			}
		}
	}

	return nil
}

// GenerateResponse 生成增强的模拟响应
func (m *MockAIService) GenerateResponse(ctx context.Context, intent *MockIntentResult, context string) (string, error) {
	switch intent.Type {
	case "host_management":
		return m.generateHostManagementResponse(intent)
	case "service_management":
		return m.generateServiceManagementResponse(intent)
	case "log_analysis":
		return m.generateLogAnalysisResponse(intent)
	case "performance_diagnosis":
		return m.generatePerformanceDiagnosisResponse(intent)
	case "network_diagnosis":
		return m.generateNetworkDiagnosisResponse(intent)
	case "security_check":
		return m.generateSecurityCheckResponse(intent)
	case "backup_restore":
		return m.generateBackupRestoreResponse(intent)
	case "alert_management":
		return m.generateAlertManagementResponse(intent)
	case "system_monitoring":
		return m.generateSystemMonitoringResponse(intent)
	case "general_chat":
		return "我是AI运维助手，可以帮助您管理主机、监控系统状态、分析日志、诊断性能等。请告诉我您需要什么帮助？", nil
	default:
		return "我理解了您的需求，让我来帮助您处理。", nil
	}
}

// generateHostManagementResponse 生成主机管理响应
func (m *MockAIService) generateHostManagementResponse(intent *MockIntentResult) (string, error) {
	action, _ := intent.Parameters["action"].(string)

	switch action {
	case "add_host":
		ip, hasIP := intent.Parameters["ip"].(string)
		username, hasUsername := intent.Parameters["username"].(string)

		if hasIP && hasUsername {
			return fmt.Sprintf("是否要添加主机 %s (用户名:%s)？", ip, username), nil
		} else {
			return "请提供主机信息：IP地址、用户名和密码\n格式：IP地址 用户名 密码", nil
		}

	case "list":
		filter, _ := intent.Parameters["filter"].(string)
		switch filter {
		case "status":
			return "当前主机状态：\n• ************* - 在线\n• ************* - 离线\n• ************* - 在线", nil
		case "account":
			return "主机账号信息：\n• ************* - root\n• ************* - admin\n• ************* - ubuntu", nil
		default:
			return "主机列表：\n• ************* (root) - 在线\n• ************* (admin) - 离线\n• ************* (ubuntu) - 在线", nil
		}

	default:
		return "主机管理功能正在处理中...", nil
	}
}

// generateSystemMonitoringResponse 生成系统监控响应
func (m *MockAIService) generateSystemMonitoringResponse(intent *MockIntentResult) (string, error) {
	metric, _ := intent.Parameters["metric"].(string)

	switch metric {
	case "cpu":
		return "CPU使用率：\n• 主机1: 45%\n• 主机2: 23%\n• 主机3: 67%", nil
	case "memory":
		return "内存使用率：\n• 主机1: 78%\n• 主机2: 56%\n• 主机3: 89%", nil
	case "disk":
		return "磁盘使用率：\n• 主机1: 34%\n• 主机2: 67%\n• 主机3: 45%", nil
	default:
		return "系统监控数据正在获取中...", nil
	}
}

// IsConfirmation 检查是否为确认回复
func (m *MockAIService) IsConfirmation(input string) bool {
	confirmations := []string{"是", "yes", "y", "确认", "好的", "可以", "同意", "执行", "ok"}
	lowerInput := strings.ToLower(strings.TrimSpace(input))

	for _, conf := range confirmations {
		if lowerInput == strings.ToLower(conf) {
			return true
		}
	}
	return false
}

// generateServiceManagementResponse 生成服务管理响应
func (m *MockAIService) generateServiceManagementResponse(intent *MockIntentResult) (string, error) {
	action, _ := intent.Parameters["action"].(string)
	service, _ := intent.Parameters["service"].(string)

	switch action {
	case "start":
		return fmt.Sprintf("正在启动 %s 服务...\n\n✅ 服务启动成功！\n📊 状态：运行中\n🔧 建议：定期检查服务状态", service), nil
	case "stop":
		return fmt.Sprintf("正在停止 %s 服务...\n\n⏹️ 服务已停止\n📊 状态：已停止\n⚠️ 提醒：确保没有依赖此服务的应用", service), nil
	case "restart":
		return fmt.Sprintf("正在重启 %s 服务...\n\n🔄 服务重启成功！\n📊 状态：运行中\n💡 提示：重启已清理内存和连接", service), nil
	case "status":
		return fmt.Sprintf("📋 %s 服务状态：\n\n🟢 状态：运行中\n⏱️ 运行时间：2天3小时\n💾 内存使用：128MB\n🔗 端口：80,443\n📈 连接数：45", service), nil
	default:
		return fmt.Sprintf("正在处理 %s 服务的 %s 操作...", service, action), nil
	}
}

// generateLogAnalysisResponse 生成日志分析响应
func (m *MockAIService) generateLogAnalysisResponse(intent *MockIntentResult) (string, error) {
	action, _ := intent.Parameters["action"].(string)
	logType, _ := intent.Parameters["log_type"].(string)

	switch action {
	case "view":
		return fmt.Sprintf("📄 %s 日志内容：\n\n```\n[2025-07-28 10:30:15] INFO: 系统启动完成\n[2025-07-28 10:30:16] INFO: 服务初始化成功\n[2025-07-28 10:30:17] WARN: 连接池达到80%%\n[2025-07-28 10:30:18] INFO: 处理请求 /api/status\n```\n\n📊 统计：INFO(75%%) WARN(20%%) ERROR(5%%)", logType), nil
	case "error_analysis":
		return "🔍 错误日志分析结果：\n\n❌ **发现 3 个错误**：\n• 数据库连接超时 (2次)\n• API调用失败 (1次)\n\n🎯 **建议修复**：\n• 检查数据库连接池配置\n• 验证API端点可用性\n• 增加重试机制", nil
	case "search":
		keyword, _ := intent.Parameters["keyword"].(string)
		return fmt.Sprintf("🔍 搜索关键词 '%s' 的结果：\n\n找到 5 条匹配记录：\n• [10:30:15] ERROR: %s in database connection\n• [10:31:22] WARN: %s threshold exceeded\n• [10:32:45] INFO: %s operation completed\n\n💡 建议：关注错误频率和模式", keyword, keyword, keyword, keyword), nil
	default:
		return "正在分析日志，请稍候...", nil
	}
}

// generatePerformanceDiagnosisResponse 生成性能诊断响应
func (m *MockAIService) generatePerformanceDiagnosisResponse(intent *MockIntentResult) (string, error) {
	action, _ := intent.Parameters["action"].(string)
	metric, _ := intent.Parameters["metric"].(string)

	switch action {
	case "monitor":
		return fmt.Sprintf("📊 %s 性能监控报告：\n\n🖥️ **系统概览**：\n• CPU使用率：75.5%%\n• 内存使用率：68.2%%\n• 磁盘使用率：45.8%%\n• 网络IO：125MB/s\n\n⚠️ **性能警告**：\nCPU使用率偏高，建议检查进程", metric), nil
	case "analyze":
		if metric == "cpu" {
			return "🔍 CPU性能分析：\n\n📈 **使用率趋势**：\n• 当前：75.5%%\n• 平均：65.2%%\n• 峰值：89.1%%\n\n🔥 **高负载进程**：\n• java (PID:1234) - 35.2%%\n• mysql (PID:5678) - 18.7%%\n\n💡 **优化建议**：\n• 优化Java应用内存配置\n• 考虑数据库查询优化", nil
		} else if metric == "memory" {
			return "🔍 内存性能分析：\n\n📊 **内存使用详情**：\n• 总内存：16GB\n• 已使用：10.9GB (68.2%%)\n• 可用：5.1GB\n• 缓存：2.8GB\n\n🔥 **内存占用TOP3**：\n• java - 4.2GB\n• mysql - 2.1GB\n• chrome - 1.8GB\n\n💡 **优化建议**：\n• 调整JVM堆内存大小\n• 清理不必要的进程", nil
		}
		return fmt.Sprintf("正在分析 %s 性能指标...", metric), nil
	default:
		return "性能诊断功能正在处理中...", nil
	}
}

// generateNetworkDiagnosisResponse 生成网络诊断响应
func (m *MockAIService) generateNetworkDiagnosisResponse(intent *MockIntentResult) (string, error) {
	action, _ := intent.Parameters["action"].(string)
	target, _ := intent.Parameters["target"].(string)

	switch action {
	case "connectivity":
		return fmt.Sprintf("🌐 网络连通性测试 - %s：\n\n```\nPING %s (64 bytes)\n64 bytes from %s: icmp_seq=1 time=12.3ms\n64 bytes from %s: icmp_seq=2 time=11.8ms\n64 bytes from %s: icmp_seq=3 time=13.1ms\n```\n\n✅ **测试结果**：连接正常\n📊 **统计**：丢包率 0%%, 平均延迟 12.4ms", target, target, target, target, target), nil
	case "port_check":
		port, _ := intent.Parameters["port"].(string)
		return fmt.Sprintf("🔌 端口检查 - %s:%s：\n\n✅ 端口 %s 开放\n🔗 服务：HTTP/HTTPS\n⏱️ 响应时间：45ms\n🛡️ 防火墙：允许\n\n💡 建议：端口状态正常，可以正常访问", target, port, port), nil
	case "latency":
		return fmt.Sprintf("⚡ 网络延迟测试 - %s：\n\n📊 **延迟统计**：\n• 最小延迟：11.2ms\n• 最大延迟：15.8ms\n• 平均延迟：13.1ms\n• 抖动：2.3ms\n\n🎯 **网络质量**：良好\n💡 建议：延迟在正常范围内", target), nil
	default:
		return fmt.Sprintf("正在诊断网络连接到 %s...", target), nil
	}
}

// generateSecurityCheckResponse 生成安全检查响应
func (m *MockAIService) generateSecurityCheckResponse(intent *MockIntentResult) (string, error) {
	action, _ := intent.Parameters["action"].(string)

	switch action {
	case "permission_audit":
		return "🔒 权限审计报告：\n\n👥 **用户权限**：\n• root: 超级管理员 ⚠️\n• admin: 系统管理员 ✅\n• user1: 普通用户 ✅\n\n📁 **文件权限**：\n• /etc/passwd: 644 ✅\n• /etc/shadow: 640 ✅\n• /var/log: 755 ✅\n\n⚠️ **安全建议**：\n• 限制root用户直接登录\n• 定期审查用户权限", nil
	case "vulnerability_scan":
		return "🛡️ 漏洞扫描报告：\n\n🔍 **扫描结果**：\n• 高危漏洞：0个 ✅\n• 中危漏洞：2个 ⚠️\n• 低危漏洞：5个 ℹ️\n\n⚠️ **需要关注**：\n• OpenSSL版本过旧\n• Apache配置存在信息泄露风险\n\n🔧 **修复建议**：\n• 升级OpenSSL到最新版本\n• 配置Apache隐藏版本信息", nil
	case "policy_check":
		return "📋 安全策略检查：\n\n🛡️ **防火墙状态**：启用 ✅\n🔐 **SSH配置**：\n• 禁用root登录：是 ✅\n• 密钥认证：启用 ✅\n• 端口：22 (默认) ⚠️\n\n🔒 **密码策略**：\n• 最小长度：8位 ✅\n• 复杂度要求：启用 ✅\n• 过期时间：90天 ✅\n\n💡 建议：修改SSH默认端口提高安全性", nil
	default:
		return "正在执行安全检查...", nil
	}
}

// generateBackupRestoreResponse 生成备份恢复响应
func (m *MockAIService) generateBackupRestoreResponse(intent *MockIntentResult) (string, error) {
	action, _ := intent.Parameters["action"].(string)

	switch action {
	case "backup":
		target, _ := intent.Parameters["target"].(string)
		return fmt.Sprintf("💾 数据备份操作：\n\n📁 备份目标：%s\n⏱️ 开始时间：%s\n📊 进度：100%%\n💾 备份大小：2.3GB\n📍 备份位置：/backup/data_20250728.tar.gz\n\n✅ 备份完成！\n🔒 备份已加密\n☁️ 已同步到云存储", target, time.Now().Format("2006-01-02 15:04:05")), nil
	case "restore":
		source, _ := intent.Parameters["source"].(string)
		return fmt.Sprintf("🔄 数据恢复操作：\n\n📁 恢复源：%s\n⏱️ 开始时间：%s\n📊 进度：100%%\n📋 恢复项目：\n• 数据库文件 ✅\n• 配置文件 ✅\n• 用户数据 ✅\n\n✅ 恢复完成！\n🔍 数据完整性验证通过", source, time.Now().Format("2006-01-02 15:04:05")), nil
	case "snapshot":
		return fmt.Sprintf("📸 快照管理：\n\n📋 **现有快照**：\n• snapshot_20250728_1030 (2.1GB)\n• snapshot_20250727_1030 (2.0GB)\n• snapshot_20250726_1030 (1.9GB)\n\n✅ 新快照创建成功\n📍 位置：/snapshots/snapshot_%s\n💾 大小：2.3GB\n\n🗑️ 自动清理：保留最近7个快照", time.Now().Format("20060102_1504")), nil
	default:
		return "正在处理备份恢复操作...", nil
	}
}

// generateAlertManagementResponse 生成告警管理响应
func (m *MockAIService) generateAlertManagementResponse(intent *MockIntentResult) (string, error) {
	action, _ := intent.Parameters["action"].(string)

	switch action {
	case "view":
		return "🚨 告警列表：\n\n🔴 **严重告警** (2个)：\n• CPU使用率超过90% - 主机*************\n• 磁盘空间不足 - 主机*************\n\n🟡 **警告告警** (3个)：\n• 内存使用率超过80% - 主机*************\n• 网络延迟较高 - 主机192.168.1.103\n• 服务响应缓慢 - 主机192.168.1.104\n\n📊 **统计**：\n• 今日新增：5个\n• 已处理：12个\n• 待处理：5个", nil
	case "configure":
		return "⚙️ 告警规则配置：\n\n📋 **当前规则**：\n• CPU使用率 > 80% (警告)\n• CPU使用率 > 90% (严重)\n• 内存使用率 > 85% (警告)\n• 磁盘使用率 > 90% (严重)\n• 服务停止 (严重)\n\n✅ 规则配置已更新\n🔔 通知方式：邮件 + 短信\n⏰ 检查频率：每30秒\n\n💡 建议：根据业务需求调整阈值", nil
	case "notification":
		return "📢 通知设置：\n\n📧 **邮件通知**：\n• 状态：启用 ✅\n• 收件人：<EMAIL>\n• 发送频率：立即\n\n📱 **短信通知**：\n• 状态：启用 ✅\n• 手机号：138****1234\n• 仅严重告警\n\n🔔 **企业微信**：\n• 状态：启用 ✅\n• 群组：运维告警群\n• 发送时间：工作时间\n\n✅ 通知设置已保存", nil
	default:
		return "正在处理告警管理操作...", nil
	}
}

// IsRejection 检查是否为拒绝回复
func (m *MockAIService) IsRejection(input string) bool {
	rejections := []string{"否", "no", "n", "取消", "不要", "不行", "拒绝", "不"}
	lowerInput := strings.ToLower(strings.TrimSpace(input))

	for _, rej := range rejections {
		if lowerInput == strings.ToLower(rej) {
			return true
		}
	}
	return false
}

// ==================== 第四阶段：语义理解和自然语言处理 ====================

// semanticPreprocessing 语义预处理和标准化
func (m *MockAIService) semanticPreprocessing(input string) string {
	// 1. 基础清理
	normalized := strings.TrimSpace(input)
	normalized = strings.ToLower(normalized)

	// 2. 标点符号标准化
	normalized = regexp.MustCompile(`[，。！？；：""''（）【】]`).ReplaceAllString(normalized, " ")
	normalized = regexp.MustCompile(`[,\.!\?;:"'()\[\]]`).ReplaceAllString(normalized, " ")

	// 3. 多空格合并
	normalized = regexp.MustCompile(`\s+`).ReplaceAllString(normalized, " ")

	// 4. 同义词标准化
	normalized = m.normalizeSynonyms(normalized)

	// 5. 动词时态标准化
	normalized = m.normalizeVerbs(normalized)

	return strings.TrimSpace(normalized)
}

// normalizeSynonyms 同义词标准化
func (m *MockAIService) normalizeSynonyms(input string) string {
	// 定义同义词映射表
	synonymMap := map[string]string{
		// 动作同义词
		"启动": "start", "开启": "start", "打开": "start", "运行": "start",
		"停止": "stop", "关闭": "stop", "终止": "stop", "结束": "stop",
		"重启": "restart", "重新启动": "restart", "重新开始": "restart",
		"查看": "view", "看": "view", "显示": "view", "展示": "view",
		"检查": "check", "检测": "check", "测试": "check", "验证": "check",
		"分析": "analyze", "诊断": "analyze",
		"监控": "monitor", "监测": "monitor", "观察": "monitor",
		"备份": "backup", "保存": "backup", "存储": "backup",
		"恢复": "restore", "还原": "restore", "修复": "restore",

		// 对象同义词
		"主机": "host", "服务器": "host", "机器": "host", "节点": "host",
		"服务": "service", "进程": "service", "程序": "service",
		"日志": "log", "记录": "log", "文件": "log",
		"性能": "performance", "效率": "performance", "速度": "performance",
		"网络": "network", "连接": "network", "通信": "network",
		"安全": "security", "权限": "security", "防护": "security",
		"告警": "alert", "警告": "alert", "报警": "alert", "通知": "alert",

		// 状态同义词
		"列表": "list", "清单": "list", "目录": "list",
		"状态": "status", "情况": "status", "信息": "status",
		"配置": "config", "设置": "config", "参数": "config",

		// 技术术语同义词
		"cpu": "cpu", "处理器": "cpu", "中央处理器": "cpu",
		"内存": "memory", "ram": "memory", "存储器": "memory",
		"磁盘": "disk", "硬盘": "disk",
		"端口": "port", "接口": "port",
		"ip": "ip", "地址": "ip", "ip地址": "ip",
	}

	result := input
	for synonym, standard := range synonymMap {
		// 使用词边界匹配，避免部分匹配
		pattern := `\b` + regexp.QuoteMeta(synonym) + `\b`
		result = regexp.MustCompile(pattern).ReplaceAllString(result, standard)
	}

	return result
}

// normalizeVerbs 动词时态标准化
func (m *MockAIService) normalizeVerbs(input string) string {
	// 动词时态映射
	verbMap := map[string]string{
		"启动了": "start", "启动中": "start", "正在启动": "start",
		"停止了": "stop", "停止中": "stop", "正在停止": "stop",
		"重启了": "restart", "重启中": "restart", "正在重启": "restart",
		"查看了": "view", "查看中": "view", "正在查看": "view",
		"检查了": "check", "检查中": "check", "正在检查": "check",
		"分析了": "analyze", "分析中": "analyze", "正在分析": "analyze",
		"监控了": "monitor", "监控中": "monitor", "正在监控": "monitor",
		"备份了": "backup", "备份中": "backup", "正在备份": "backup",
		"恢复了": "restore", "恢复中": "restore", "正在恢复": "restore",
	}

	result := input
	for verb, standard := range verbMap {
		pattern := `\b` + regexp.QuoteMeta(verb) + `\b`
		result = regexp.MustCompile(pattern).ReplaceAllString(result, standard)
	}

	return result
}

// semanticExpansionMatching 语义扩展匹配
func (m *MockAIService) semanticExpansionMatching(input string) *MockIntentResult {
	// 语义扩展模式 - 基于语义理解的灵活匹配
	semanticPatterns := []struct {
		patterns   []string
		intent     string
		action     string
		confidence float64
	}{
		// 主机管理语义扩展
		{
			patterns:   []string{"新增.*host", "增加.*host", "创建.*host", "添加.*机器", "加入.*服务器"},
			intent:     "host_management",
			action:     "add_host",
			confidence: 0.9,
		},
		{
			patterns:   []string{"host.*list", "机器.*清单", "服务器.*目录", "所有.*host", "全部.*机器"},
			intent:     "host_management",
			action:     "list",
			confidence: 0.9,
		},
		{
			patterns:   []string{"删除.*host", "移除.*host", "去掉.*机器", "卸载.*服务器"},
			intent:     "host_management",
			action:     "remove",
			confidence: 0.9,
		},

		// 服务管理语义扩展
		{
			patterns:   []string{"service.*start", "启用.*service", "开始.*服务", "运行.*程序"},
			intent:     "service_management",
			action:     "start",
			confidence: 0.9,
		},
		{
			patterns:   []string{"service.*stop", "停用.*service", "关闭.*服务", "终止.*程序"},
			intent:     "service_management",
			action:     "stop",
			confidence: 0.9,
		},
		{
			patterns:   []string{"service.*restart", "重新.*service", "重启.*服务", "重新启动.*程序"},
			intent:     "service_management",
			action:     "restart",
			confidence: 0.9,
		},

		// 日志分析语义扩展
		{
			patterns:   []string{"log.*view", "查阅.*log", "浏览.*日志", "读取.*记录"},
			intent:     "log_analysis",
			action:     "view",
			confidence: 0.85,
		},
		{
			patterns:   []string{"log.*error", "错误.*log", "异常.*日志", "故障.*记录"},
			intent:     "log_analysis",
			action:     "error_analysis",
			confidence: 0.9,
		},
		{
			patterns:   []string{"log.*search", "搜索.*log", "查找.*日志", "检索.*记录"},
			intent:     "log_analysis",
			action:     "search",
			confidence: 0.85,
		},

		// 性能诊断语义扩展
		{
			patterns:   []string{"performance.*monitor", "性能.*观察", "效率.*检测", "速度.*监控"},
			intent:     "performance_diagnosis",
			action:     "monitor",
			confidence: 0.85,
		},
		{
			patterns:   []string{"cpu.*analyze", "处理器.*分析", "cpu.*诊断", "处理器.*检查"},
			intent:     "performance_diagnosis",
			action:     "analyze",
			confidence: 0.9,
		},
		{
			patterns:   []string{"memory.*analyze", "内存.*分析", "ram.*诊断", "存储器.*检查"},
			intent:     "performance_diagnosis",
			action:     "analyze",
			confidence: 0.9,
		},
	}

	for _, pattern := range semanticPatterns {
		for _, p := range pattern.patterns {
			if matched, _ := regexp.MatchString("(?i)"+p, input); matched {
				return &MockIntentResult{
					Type:       pattern.intent,
					Confidence: pattern.confidence,
					Parameters: map[string]interface{}{
						"action": pattern.action,
					},
				}
			}
		}
	}

	return nil
}

// semanticSimilarityMatching 语义相似度匹配
func (m *MockAIService) semanticSimilarityMatching(input string) *MockIntentResult {
	// 基于语义相似度的模糊匹配
	semanticTemplates := []struct {
		template   string
		intent     string
		action     string
		confidence float64
		keywords   []string
	}{
		{
			template:   "管理主机相关操作",
			intent:     "host_management",
			action:     "manage",
			confidence: 0.7,
			keywords:   []string{"host", "机器", "服务器", "节点", "管理", "操作"},
		},
		{
			template:   "服务控制相关操作",
			intent:     "service_management",
			action:     "manage",
			confidence: 0.7,
			keywords:   []string{"service", "服务", "进程", "程序", "控制", "管理"},
		},
		{
			template:   "日志查看相关操作",
			intent:     "log_analysis",
			action:     "view",
			confidence: 0.7,
			keywords:   []string{"log", "日志", "记录", "文件", "查看", "分析"},
		},
		{
			template:   "性能监控相关操作",
			intent:     "performance_diagnosis",
			action:     "monitor",
			confidence: 0.7,
			keywords:   []string{"performance", "性能", "监控", "诊断", "分析", "检查"},
		},
		{
			template:   "网络诊断相关操作",
			intent:     "network_diagnosis",
			action:     "check",
			confidence: 0.7,
			keywords:   []string{"network", "网络", "连接", "通信", "诊断", "测试"},
		},
	}

	for _, template := range semanticTemplates {
		matchCount := 0
		for _, keyword := range template.keywords {
			if strings.Contains(input, keyword) {
				matchCount++
			}
		}

		// 如果匹配到足够多的关键词，认为语义相似
		if matchCount >= 2 {
			confidence := template.confidence * (float64(matchCount) / float64(len(template.keywords)))
			return &MockIntentResult{
				Type:       template.intent,
				Confidence: confidence,
				Parameters: map[string]interface{}{
					"action": template.action,
				},
			}
		}
	}

	return nil
}

// contextualSemanticReasoning 上下文语义推理
func (m *MockAIService) contextualSemanticReasoning(input string) *MockIntentResult {
	// 基于上下文的语义推理
	contextualPatterns := []struct {
		condition  func(string) bool
		intent     string
		action     string
		confidence float64
	}{
		{
			condition: func(input string) bool {
				// 包含IP地址模式，可能是主机相关操作
				return regexp.MustCompile(`\d+\.\d+\.\d+\.\d+`).MatchString(input)
			},
			intent:     "host_management",
			action:     "manage",
			confidence: 0.6,
		},
		{
			condition: func(input string) bool {
				// 包含常见服务名，可能是服务管理
				services := []string{"nginx", "apache", "mysql", "redis", "docker"}
				for _, service := range services {
					if strings.Contains(input, service) {
						return true
					}
				}
				return false
			},
			intent:     "service_management",
			action:     "manage",
			confidence: 0.6,
		},
		{
			condition: func(input string) bool {
				// 包含错误相关词汇，可能是日志分析
				errorWords := []string{"error", "错误", "异常", "失败", "故障"}
				for _, word := range errorWords {
					if strings.Contains(input, word) {
						return true
					}
				}
				return false
			},
			intent:     "log_analysis",
			action:     "error_analysis",
			confidence: 0.65,
		},
		{
			condition: func(input string) bool {
				// 包含百分比，可能是性能相关
				return regexp.MustCompile(`\d+%`).MatchString(input)
			},
			intent:     "performance_diagnosis",
			action:     "monitor",
			confidence: 0.6,
		},
	}

	for _, pattern := range contextualPatterns {
		if pattern.condition(input) {
			return &MockIntentResult{
				Type:       pattern.intent,
				Confidence: pattern.confidence,
				Parameters: map[string]interface{}{
					"action": pattern.action,
				},
			}
		}
	}

	return nil
}

// selectBestCandidateWithSemantics 选择最佳候选（语义增强版）
func (m *MockAIService) selectBestCandidateWithSemantics(candidates []*MockIntentResult, normalizedInput, originalInput string) *MockIntentResult {
	if len(candidates) == 0 {
		return nil
	}

	// 按置信度排序
	best := candidates[0]
	for _, candidate := range candidates[1:] {
		if candidate.Confidence > best.Confidence {
			best = candidate
		}
	}

	// 语义一致性检查
	if best.Confidence < 0.8 {
		// 如果置信度不够高，进行语义一致性验证
		if m.validateSemanticConsistency(best, normalizedInput, originalInput) {
			best.Confidence += 0.1 // 提升置信度
		}
	}

	// 参数完整性加权
	if len(candidates) > 1 {
		for _, candidate := range candidates {
			if candidate.Confidence >= best.Confidence-0.1 &&
				len(candidate.Parameters) > len(best.Parameters) {
				best = candidate
			}
		}
	}

	return best
}

// validateSemanticConsistency 验证语义一致性
func (m *MockAIService) validateSemanticConsistency(intent *MockIntentResult, normalizedInput, originalInput string) bool {
	// 验证意图与输入的语义一致性
	intentKeywords := map[string][]string{
		"host_management":       {"host", "主机", "服务器", "机器", "节点"},
		"service_management":    {"service", "服务", "进程", "程序"},
		"log_analysis":          {"log", "日志", "记录", "文件"},
		"performance_diagnosis": {"performance", "性能", "监控", "诊断"},
		"network_diagnosis":     {"network", "网络", "连接", "通信"},
		"security_check":        {"security", "安全", "权限", "防护"},
		"backup_restore":        {"backup", "备份", "恢复", "还原"},
		"alert_management":      {"alert", "告警", "警告", "通知"},
	}

	keywords, exists := intentKeywords[intent.Type]
	if !exists {
		return false
	}

	// 检查是否包含相关关键词
	for _, keyword := range keywords {
		if strings.Contains(normalizedInput, keyword) || strings.Contains(originalInput, keyword) {
			return true
		}
	}

	return false
}

// ==================== 第五阶段：智能建议和预测 ====================

// MockIntelligentSuggestion Mock智能建议结构（避免与其他文件中的定义冲突）
type MockIntelligentSuggestion struct {
	Type        string                 `json:"type"`        // 建议类型
	Priority    string                 `json:"priority"`    // 优先级：high, medium, low
	Title       string                 `json:"title"`       // 建议标题
	Description string                 `json:"description"` // 详细描述
	Action      string                 `json:"action"`      // 建议的操作
	Parameters  map[string]interface{} `json:"parameters"`  // 操作参数
	Confidence  float64                `json:"confidence"`  // 置信度
	Reasoning   string                 `json:"reasoning"`   // 推理过程
	Impact      string                 `json:"impact"`      // 预期影响
}

// OperationPrediction 操作预测结构
type OperationPrediction struct {
	NextLikelyAction string                  `json:"next_likely_action"` // 下一步可能的操作
	Probability      float64                 `json:"probability"`        // 概率
	Context          string                  `json:"context"`            // 上下文
	Suggestions      []MockIntelligentSuggestion `json:"suggestions"`        // 相关建议
	RiskLevel        string                  `json:"risk_level"`         // 风险等级
	Prerequisites    []string                `json:"prerequisites"`      // 前置条件
}

// GenerateIntelligentSuggestions 生成智能建议
func (m *MockAIService) GenerateIntelligentSuggestions(ctx context.Context, intent *MockIntentResult, context string) ([]MockIntelligentSuggestion, error) {
	var suggestions []MockIntelligentSuggestion

	// 基于意图类型生成建议
	switch intent.Type {
	case "host_management":
		suggestions = append(suggestions, m.generateHostManagementSuggestions(intent, context)...)
	case "service_management":
		suggestions = append(suggestions, m.generateServiceManagementSuggestions(intent, context)...)
	case "log_analysis":
		suggestions = append(suggestions, m.generateLogAnalysisSuggestions(intent, context)...)
	case "performance_diagnosis":
		suggestions = append(suggestions, m.generatePerformanceSuggestions(intent, context)...)
	case "network_diagnosis":
		suggestions = append(suggestions, m.generateNetworkSuggestions(intent, context)...)
	case "security_check":
		suggestions = append(suggestions, m.generateSecuritySuggestions(intent, context)...)
	case "backup_restore":
		suggestions = append(suggestions, m.generateBackupSuggestions(intent, context)...)
	case "alert_management":
		suggestions = append(suggestions, m.generateAlertSuggestions(intent, context)...)
	}

	// 添加通用智能建议
	suggestions = append(suggestions, m.generateContextualSuggestions(intent, context)...)

	// 按优先级和置信度排序
	suggestions = m.prioritizeSuggestions(suggestions)

	return suggestions, nil
}

// generateHostManagementSuggestions 生成主机管理建议
func (m *MockAIService) generateHostManagementSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion
	action, _ := intent.Parameters["action"].(string)

	switch action {
	case "add_host":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "security",
			Priority:    "high",
			Title:       "配置SSH密钥认证",
			Description: "建议为新主机配置SSH密钥认证，提高安全性",
			Action:      "setup_ssh_key",
			Parameters:  map[string]interface{}{"auth_method": "key"},
			Confidence:  0.9,
			Reasoning:   "密钥认证比密码认证更安全，可以防止暴力破解",
			Impact:      "显著提升主机安全性，降低被攻击风险",
		})

		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "monitoring",
			Priority:    "medium",
			Title:       "启用主机监控",
			Description: "为新添加的主机启用性能和状态监控",
			Action:      "enable_monitoring",
			Parameters:  map[string]interface{}{"metrics": []string{"cpu", "memory", "disk"}},
			Confidence:  0.85,
			Reasoning:   "新主机需要监控以确保正常运行和及时发现问题",
			Impact:      "提供实时状态信息，便于运维管理",
		})

	case "list":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "optimization",
			Priority:    "low",
			Title:       "主机资源优化分析",
			Description: "分析主机资源使用情况，识别优化机会",
			Action:      "analyze_resources",
			Parameters:  map[string]interface{}{"scope": "all_hosts"},
			Confidence:  0.75,
			Reasoning:   "定期分析可以发现资源浪费和性能瓶颈",
			Impact:      "优化资源配置，降低运营成本",
		})
	}

	return suggestions
}

// generateServiceManagementSuggestions 生成服务管理建议
func (m *MockAIService) generateServiceManagementSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion
	action, _ := intent.Parameters["action"].(string)
	service, _ := intent.Parameters["service"].(string)

	switch action {
	case "restart":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "monitoring",
			Priority:    "high",
			Title:       "监控服务重启后状态",
			Description: fmt.Sprintf("重启%s服务后，建议监控其运行状态和性能指标", service),
			Action:      "monitor_service_health",
			Parameters:  map[string]interface{}{"service": service, "duration": "10m"},
			Confidence:  0.95,
			Reasoning:   "服务重启后可能出现异常，需要密切监控",
			Impact:      "及时发现重启后的问题，确保服务稳定运行",
		})

		if service == "nginx" || service == "apache" {
			suggestions = append(suggestions, MockIntelligentSuggestion{
				Type:        "performance",
				Priority:    "medium",
				Title:       "检查Web服务配置",
				Description: "建议检查Web服务器配置是否优化",
				Action:      "check_web_config",
				Parameters:  map[string]interface{}{"service": service},
				Confidence:  0.8,
				Reasoning:   "Web服务器配置直接影响性能和稳定性",
				Impact:      "优化配置可以提升响应速度和并发能力",
			})
		}

	case "stop":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "dependency",
			Priority:    "high",
			Title:       "检查服务依赖关系",
			Description: fmt.Sprintf("停止%s服务前，建议检查依赖此服务的其他组件", service),
			Action:      "check_dependencies",
			Parameters:  map[string]interface{}{"service": service},
			Confidence:  0.9,
			Reasoning:   "停止服务可能影响依赖它的其他服务",
			Impact:      "避免因服务停止导致的连锁故障",
		})
	}

	return suggestions
}

// generateLogAnalysisSuggestions 生成日志分析建议
func (m *MockAIService) generateLogAnalysisSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion
	action, _ := intent.Parameters["action"].(string)

	switch action {
	case "error_analysis":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "automation",
			Priority:    "medium",
			Title:       "设置错误日志告警",
			Description: "为检测到的错误模式设置自动告警规则",
			Action:      "setup_log_alerts",
			Parameters:  map[string]interface{}{"log_type": "error", "threshold": 5},
			Confidence:  0.85,
			Reasoning:   "自动告警可以及时发现和响应错误",
			Impact:      "提高问题响应速度，减少故障影响",
		})

		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "analysis",
			Priority:    "high",
			Title:       "深度错误分析",
			Description: "建议进行更深入的错误根因分析",
			Action:      "deep_error_analysis",
			Parameters:  map[string]interface{}{"timeframe": "24h"},
			Confidence:  0.9,
			Reasoning:   "深度分析可以找到错误的根本原因",
			Impact:      "从根源解决问题，防止错误重复发生",
		})

	case "view":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "optimization",
			Priority:    "low",
			Title:       "日志轮转配置检查",
			Description: "检查日志轮转配置，防止日志文件过大",
			Action:      "check_log_rotation",
			Parameters:  map[string]interface{}{},
			Confidence:  0.7,
			Reasoning:   "合理的日志轮转可以节省磁盘空间",
			Impact:      "优化存储使用，提高系统性能",
		})
	}

	return suggestions
}

// generatePerformanceSuggestions 生成性能诊断建议
func (m *MockAIService) generatePerformanceSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion
	action, _ := intent.Parameters["action"].(string)
	metric, _ := intent.Parameters["metric"].(string)

	switch action {
	case "monitor":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "optimization",
			Priority:    "medium",
			Title:       "性能基线建立",
			Description: "建议建立性能基线，用于异常检测和趋势分析",
			Action:      "establish_baseline",
			Parameters:  map[string]interface{}{"metric": metric, "period": "7d"},
			Confidence:  0.8,
			Reasoning:   "性能基线是异常检测和容量规划的基础",
			Impact:      "提高异常检测准确性，支持容量规划",
		})

	case "analyze":
		if metric == "cpu" {
			suggestions = append(suggestions, MockIntelligentSuggestion{
				Type:        "optimization",
				Priority:    "high",
				Title:       "CPU优化建议",
				Description: "基于CPU分析结果，建议优化高负载进程",
				Action:      "optimize_cpu_usage",
				Parameters:  map[string]interface{}{"target_processes": []string{"java", "mysql"}},
				Confidence:  0.85,
				Reasoning:   "优化高负载进程可以显著降低CPU使用率",
				Impact:      "提升系统响应速度，降低资源消耗",
			})
		} else if metric == "memory" {
			suggestions = append(suggestions, MockIntelligentSuggestion{
				Type:        "optimization",
				Priority:    "high",
				Title:       "内存优化建议",
				Description: "建议调整内存配置和清理不必要的进程",
				Action:      "optimize_memory_usage",
				Parameters:  map[string]interface{}{"cleanup": true, "tune_jvm": true},
				Confidence:  0.9,
				Reasoning:   "内存优化可以防止OOM和提升性能",
				Impact:      "减少内存压力，提高系统稳定性",
			})
		}
	}

	return suggestions
}

// generateNetworkSuggestions 生成网络诊断建议
func (m *MockAIService) generateNetworkSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion
	action, _ := intent.Parameters["action"].(string)

	switch action {
	case "connectivity":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "monitoring",
			Priority:    "medium",
			Title:       "网络连通性监控",
			Description: "建议设置定期网络连通性监控",
			Action:      "setup_connectivity_monitoring",
			Parameters:  map[string]interface{}{"interval": "5m", "alert_threshold": 3},
			Confidence:  0.8,
			Reasoning:   "定期监控可以及时发现网络问题",
			Impact:      "提高网络问题的发现和响应速度",
		})

	case "port_check":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "security",
			Priority:    "high",
			Title:       "端口安全检查",
			Description: "建议检查开放端口的安全配置",
			Action:      "security_port_audit",
			Parameters:  map[string]interface{}{"check_firewall": true},
			Confidence:  0.9,
			Reasoning:   "开放端口可能存在安全风险",
			Impact:      "提高网络安全性，防止未授权访问",
		})

	case "latency":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "optimization",
			Priority:    "medium",
			Title:       "网络延迟优化",
			Description: "如果延迟较高，建议检查网络路径和配置",
			Action:      "optimize_network_latency",
			Parameters:  map[string]interface{}{"check_routing": true},
			Confidence:  0.75,
			Reasoning:   "网络延迟影响用户体验和系统性能",
			Impact:      "改善网络性能，提升用户体验",
		})
	}

	return suggestions
}

// generateSecuritySuggestions 生成安全检查建议
func (m *MockAIService) generateSecuritySuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion
	action, _ := intent.Parameters["action"].(string)

	switch action {
	case "permission_audit":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "security",
			Priority:    "high",
			Title:       "权限最小化原则",
			Description: "建议实施权限最小化原则，定期审查用户权限",
			Action:      "implement_least_privilege",
			Parameters:  map[string]interface{}{"review_cycle": "monthly"},
			Confidence:  0.95,
			Reasoning:   "权限最小化是安全的基本原则",
			Impact:      "显著降低安全风险，防止权限滥用",
		})

	case "vulnerability_scan":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "security",
			Priority:    "high",
			Title:       "漏洞修复计划",
			Description: "建议制定漏洞修复计划，优先处理高危漏洞",
			Action:      "create_patch_plan",
			Parameters:  map[string]interface{}{"priority": "high_critical"},
			Confidence:  0.9,
			Reasoning:   "及时修复漏洞可以防止安全事件",
			Impact:      "提高系统安全性，降低被攻击风险",
		})

	case "policy_check":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "security",
			Priority:    "medium",
			Title:       "安全策略优化",
			Description: "建议根据检查结果优化安全策略配置",
			Action:      "optimize_security_policies",
			Parameters:  map[string]interface{}{"areas": []string{"ssh", "firewall", "password"}},
			Confidence:  0.85,
			Reasoning:   "优化安全策略可以提高整体安全水平",
			Impact:      "加强安全防护，提升合规性",
		})
	}

	return suggestions
}

// generateBackupSuggestions 生成备份恢复建议
func (m *MockAIService) generateBackupSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion
	action, _ := intent.Parameters["action"].(string)

	switch action {
	case "backup":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "reliability",
			Priority:    "high",
			Title:       "备份验证测试",
			Description: "建议定期验证备份文件的完整性和可恢复性",
			Action:      "verify_backup_integrity",
			Parameters:  map[string]interface{}{"test_restore": true},
			Confidence:  0.95,
			Reasoning:   "备份验证确保数据可以成功恢复",
			Impact:      "提高数据恢复成功率，降低数据丢失风险",
		})

		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "automation",
			Priority:    "medium",
			Title:       "自动化备份策略",
			Description: "建议设置自动化备份策略，确保数据定期备份",
			Action:      "setup_automated_backup",
			Parameters:  map[string]interface{}{"schedule": "daily", "retention": "30d"},
			Confidence:  0.9,
			Reasoning:   "自动化备份减少人为错误，确保备份连续性",
			Impact:      "提高备份可靠性，减少运维工作量",
		})

	case "restore":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "verification",
			Priority:    "high",
			Title:       "恢复后验证",
			Description: "建议在数据恢复后进行完整性验证",
			Action:      "post_restore_verification",
			Parameters:  map[string]interface{}{"check_data": true, "test_services": true},
			Confidence:  0.9,
			Reasoning:   "恢复后验证确保数据完整性和服务正常",
			Impact:      "确保恢复质量，避免数据不一致问题",
		})
	}

	return suggestions
}

// generateAlertSuggestions 生成告警管理建议
func (m *MockAIService) generateAlertSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion
	action, _ := intent.Parameters["action"].(string)

	switch action {
	case "view":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "optimization",
			Priority:    "medium",
			Title:       "告警降噪优化",
			Description: "建议分析告警模式，优化告警规则以减少误报",
			Action:      "optimize_alert_rules",
			Parameters:  map[string]interface{}{"analyze_patterns": true},
			Confidence:  0.8,
			Reasoning:   "告警降噪可以提高运维效率",
			Impact:      "减少告警疲劳，提高重要告警的关注度",
		})

	case "configure":
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "automation",
			Priority:    "high",
			Title:       "智能告警分级",
			Description: "建议实施智能告警分级，根据影响程度自动分类",
			Action:      "implement_smart_alerting",
			Parameters:  map[string]interface{}{"severity_levels": 4},
			Confidence:  0.85,
			Reasoning:   "智能分级可以优化告警响应优先级",
			Impact:      "提高告警处理效率，确保关键问题优先处理",
		})
	}

	return suggestions
}

// generateContextualSuggestions 生成上下文相关建议
func (m *MockAIService) generateContextualSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion

	// 基于时间的建议
	suggestions = append(suggestions, m.generateTimeBasedSuggestions(intent, context)...)

	// 基于系统状态的建议
	suggestions = append(suggestions, m.generateSystemStateSuggestions(intent, context)...)

	// 基于最佳实践的建议
	suggestions = append(suggestions, m.generateBestPracticeSuggestions(intent, context)...)

	return suggestions
}

// generateTimeBasedSuggestions 生成基于时间的建议
func (m *MockAIService) generateTimeBasedSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion
	currentHour := time.Now().Hour()

	// 工作时间外的建议
	if currentHour < 8 || currentHour > 18 {
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "scheduling",
			Priority:    "low",
			Title:       "非工作时间操作提醒",
			Description: "当前为非工作时间，建议谨慎执行重要操作",
			Action:      "schedule_operation",
			Parameters:  map[string]interface{}{"defer_to": "business_hours"},
			Confidence:  0.7,
			Reasoning:   "非工作时间执行操作可能影响问题响应",
			Impact:      "降低操作风险，确保及时支持",
		})
	}

	// 周末建议
	if time.Now().Weekday() == time.Saturday || time.Now().Weekday() == time.Sunday {
		suggestions = append(suggestions, MockIntelligentSuggestion{
			Type:        "planning",
			Priority:    "low",
			Title:       "周末维护窗口",
			Description: "周末是执行系统维护的好时机",
			Action:      "plan_maintenance",
			Parameters:  map[string]interface{}{"window": "weekend"},
			Confidence:  0.6,
			Reasoning:   "周末用户活动较少，适合维护操作",
			Impact:      "减少对业务的影响",
		})
	}

	return suggestions
}

// generateSystemStateSuggestions 生成基于系统状态的建议
func (m *MockAIService) generateSystemStateSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion

	// 模拟系统状态检查
	suggestions = append(suggestions, MockIntelligentSuggestion{
		Type:        "monitoring",
		Priority:    "medium",
		Title:       "系统健康检查",
		Description: "建议在执行操作前进行系统健康检查",
		Action:      "health_check",
		Parameters:  map[string]interface{}{"scope": "full"},
		Confidence:  0.8,
		Reasoning:   "健康检查可以发现潜在问题",
		Impact:      "提高操作成功率，避免系统故障",
	})

	return suggestions
}

// generateBestPracticeSuggestions 生成基于最佳实践的建议
func (m *MockAIService) generateBestPracticeSuggestions(intent *MockIntentResult, context string) []MockIntelligentSuggestion {
	var suggestions []MockIntelligentSuggestion

	suggestions = append(suggestions, MockIntelligentSuggestion{
		Type:        "documentation",
		Priority:    "low",
		Title:       "操作文档记录",
		Description: "建议记录操作过程和结果，便于后续参考",
		Action:      "document_operation",
		Parameters:  map[string]interface{}{"include_screenshots": true},
		Confidence:  0.7,
		Reasoning:   "文档记录有助于知识积累和问题排查",
		Impact:      "提高团队协作效率，便于知识传承",
	})

	return suggestions
}

// prioritizeSuggestions 按优先级和置信度排序建议
func (m *MockAIService) prioritizeSuggestions(suggestions []MockIntelligentSuggestion) []MockIntelligentSuggestion {
	// 定义优先级权重
	priorityWeight := map[string]int{
		"high":   3,
		"medium": 2,
		"low":    1,
	}

	// 按优先级和置信度排序
	sort.Slice(suggestions, func(i, j int) bool {
		weightI := priorityWeight[suggestions[i].Priority]
		weightJ := priorityWeight[suggestions[j].Priority]

		if weightI != weightJ {
			return weightI > weightJ
		}

		return suggestions[i].Confidence > suggestions[j].Confidence
	})

	return suggestions
}

// PredictNextOperation 预测下一步操作
func (m *MockAIService) PredictNextOperation(ctx context.Context, intent *MockIntentResult, context string) (*OperationPrediction, error) {
	prediction := &OperationPrediction{
		RiskLevel:     "low",
		Prerequisites: []string{},
		Suggestions:   []MockIntelligentSuggestion{},
	}

	// 基于当前意图预测下一步操作
	switch intent.Type {
	case "host_management":
		action, _ := intent.Parameters["action"].(string)
		if action == "add_host" {
			prediction.NextLikelyAction = "configure_monitoring"
			prediction.Probability = 0.8
			prediction.Context = "新主机添加后通常需要配置监控"
			prediction.RiskLevel = "low"
			prediction.Prerequisites = []string{"主机连接正常", "SSH访问可用"}
		} else if action == "list" {
			prediction.NextLikelyAction = "check_host_status"
			prediction.Probability = 0.7
			prediction.Context = "查看主机列表后通常会检查具体状态"
			prediction.RiskLevel = "low"
		}

	case "service_management":
		action, _ := intent.Parameters["action"].(string)
		service, _ := intent.Parameters["service"].(string)
		if action == "restart" {
			prediction.NextLikelyAction = "verify_service_status"
			prediction.Probability = 0.9
			prediction.Context = fmt.Sprintf("重启%s服务后需要验证状态", service)
			prediction.RiskLevel = "medium"
			prediction.Prerequisites = []string{"服务重启完成"}
		} else if action == "stop" {
			prediction.NextLikelyAction = "check_dependent_services"
			prediction.Probability = 0.8
			prediction.Context = "停止服务后需要检查依赖服务"
			prediction.RiskLevel = "high"
			prediction.Prerequisites = []string{"确认无业务影响"}
		}

	case "log_analysis":
		action, _ := intent.Parameters["action"].(string)
		if action == "error_analysis" {
			prediction.NextLikelyAction = "implement_fix"
			prediction.Probability = 0.75
			prediction.Context = "错误分析后通常需要实施修复"
			prediction.RiskLevel = "medium"
			prediction.Prerequisites = []string{"确定错误根因", "制定修复方案"}
		}

	case "performance_diagnosis":
		prediction.NextLikelyAction = "optimize_performance"
		prediction.Probability = 0.7
		prediction.Context = "性能诊断后通常需要优化"
		prediction.RiskLevel = "medium"
		prediction.Prerequisites = []string{"确定性能瓶颈", "评估优化影响"}

	case "security_check":
		prediction.NextLikelyAction = "apply_security_patches"
		prediction.Probability = 0.8
		prediction.Context = "安全检查后通常需要应用补丁"
		prediction.RiskLevel = "high"
		prediction.Prerequisites = []string{"备份重要数据", "制定回滚计划"}

	default:
		prediction.NextLikelyAction = "monitor_results"
		prediction.Probability = 0.6
		prediction.Context = "操作后建议监控结果"
		prediction.RiskLevel = "low"
	}

	// 生成相关建议
	suggestions, err := m.GenerateIntelligentSuggestions(ctx, intent, context)
	if err == nil {
		// 只取前3个最相关的建议
		if len(suggestions) > 3 {
			suggestions = suggestions[:3]
		}
		prediction.Suggestions = suggestions
	}

	return prediction, nil
}

// AnalyzeOperationPattern 分析操作模式
func (m *MockAIService) AnalyzeOperationPattern(ctx context.Context, operations []string) (*OperationPatternAnalysis, error) {
	analysis := &OperationPatternAnalysis{
		CommonPatterns:          []string{},
		RecommendedFlow:         []string{},
		RiskAssessment:          "low",
		OptimizationTips:        []string{},
		AutomationOpportunities: []string{},
	}

	// 分析常见模式
	if m.containsPattern(operations, []string{"add_host", "configure_monitoring"}) {
		analysis.CommonPatterns = append(analysis.CommonPatterns, "主机添加后配置监控")
		analysis.AutomationOpportunities = append(analysis.AutomationOpportunities, "自动化主机初始化流程")
	}

	if m.containsPattern(operations, []string{"restart_service", "check_status"}) {
		analysis.CommonPatterns = append(analysis.CommonPatterns, "服务重启后状态检查")
		analysis.AutomationOpportunities = append(analysis.AutomationOpportunities, "服务重启后自动状态验证")
	}

	// 推荐操作流程
	analysis.RecommendedFlow = []string{
		"1. 执行预检查",
		"2. 备份相关配置",
		"3. 执行主要操作",
		"4. 验证操作结果",
		"5. 监控系统状态",
	}

	// 优化建议
	analysis.OptimizationTips = []string{
		"使用批量操作提高效率",
		"建立操作检查清单",
		"实施操作自动化",
		"加强操作监控和告警",
	}

	return analysis, nil
}

// OperationPatternAnalysis 操作模式分析结果
type OperationPatternAnalysis struct {
	CommonPatterns          []string `json:"common_patterns"`
	RecommendedFlow         []string `json:"recommended_flow"`
	RiskAssessment          string   `json:"risk_assessment"`
	OptimizationTips        []string `json:"optimization_tips"`
	AutomationOpportunities []string `json:"automation_opportunities"`
}

// containsPattern 检查是否包含特定模式
func (m *MockAIService) containsPattern(operations []string, pattern []string) bool {
	if len(operations) < len(pattern) {
		return false
	}

	for i := 0; i <= len(operations)-len(pattern); i++ {
		match := true
		for j, p := range pattern {
			if operations[i+j] != p {
				match = false
				break
			}
		}
		if match {
			return true
		}
	}

	return false
}
