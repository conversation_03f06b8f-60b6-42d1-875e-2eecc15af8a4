/* AI运维管理平台 - 全新设计系统 V2.0 */
/* 参考Claude、<PERSON>t<PERSON><PERSON>、<PERSON>ana等顶级平台的现代化设计语言 */

/* ========================================
   设计令牌 (Design Tokens) - 全面升级
   ======================================== */

:root {
  /* 主色彩系统 - 现代化蓝紫渐变 */
  --color-primary-50: #f0f4ff;
  --color-primary-100: #e0e7ff;
  --color-primary-200: #c7d2fe;
  --color-primary-300: #a5b4fc;
  --color-primary-400: #818cf8;
  --color-primary-500: #6366f1;
  --color-primary-600: #4f46e5;
  --color-primary-700: #4338ca;
  --color-primary-800: #3730a3;
  --color-primary-900: #312e81;
  --color-primary-950: #1e1b4b;

  /* 辅助色彩 - AI主题渐变 */
  --color-accent-50: #fdf4ff;
  --color-accent-100: #fae8ff;
  --color-accent-200: #f5d0fe;
  --color-accent-300: #f0abfc;
  --color-accent-400: #e879f9;
  --color-accent-500: #d946ef;
  --color-accent-600: #c026d3;
  --color-accent-700: #a21caf;
  --color-accent-800: #86198f;
  --color-accent-900: #701a75;

  /* 中性色系 - 优化对比度 */
  --color-gray-50: #fafafa;
  --color-gray-100: #f4f4f5;
  --color-gray-200: #e4e4e7;
  --color-gray-300: #d4d4d8;
  --color-gray-400: #a1a1aa;
  --color-gray-500: #71717a;
  --color-gray-600: #52525b;
  --color-gray-700: #3f3f46;
  --color-gray-800: #27272a;
  --color-gray-900: #18181b;
  --color-gray-950: #09090b;

  /* 语义化颜色 - 增强版 */
  --color-success-50: #f0fdf4;
  --color-success-100: #dcfce7;
  --color-success-200: #bbf7d0;
  --color-success-300: #86efac;
  --color-success-400: #4ade80;
  --color-success-500: #22c55e;
  --color-success-600: #16a34a;
  --color-success-700: #15803d;
  --color-success-800: #166534;
  --color-success-900: #14532d;

  --color-warning-50: #fffbeb;
  --color-warning-100: #fef3c7;
  --color-warning-200: #fde68a;
  --color-warning-300: #fcd34d;
  --color-warning-400: #fbbf24;
  --color-warning-500: #f59e0b;
  --color-warning-600: #d97706;
  --color-warning-700: #b45309;
  --color-warning-800: #92400e;
  --color-warning-900: #78350f;

  --color-error-50: #fef2f2;
  --color-error-100: #fee2e2;
  --color-error-200: #fecaca;
  --color-error-300: #fca5a5;
  --color-error-400: #f87171;
  --color-error-500: #ef4444;
  --color-error-600: #dc2626;
  --color-error-700: #b91c1c;
  --color-error-800: #991b1b;
  --color-error-900: #7f1d1d;

  --color-info-50: #eff6ff;
  --color-info-100: #dbeafe;
  --color-info-200: #bfdbfe;
  --color-info-300: #93c5fd;
  --color-info-400: #60a5fa;
  --color-info-500: #3b82f6;
  --color-info-600: #2563eb;
  --color-info-700: #1d4ed8;
  --color-info-800: #1e40af;
  --color-info-900: #1e3a8a;

  /* 间距系统 - 精确的4px网格系统 */
  --spacing-0: 0;
  --spacing-px: 1px;
  --spacing-0_5: 0.125rem; /* 2px */
  --spacing-1: 0.25rem;    /* 4px */
  --spacing-1_5: 0.375rem; /* 6px */
  --spacing-2: 0.5rem;     /* 8px */
  --spacing-2_5: 0.625rem; /* 10px */
  --spacing-3: 0.75rem;    /* 12px */
  --spacing-3_5: 0.875rem; /* 14px */
  --spacing-4: 1rem;       /* 16px */
  --spacing-5: 1.25rem;    /* 20px */
  --spacing-6: 1.5rem;     /* 24px */
  --spacing-7: 1.75rem;    /* 28px */
  --spacing-8: 2rem;       /* 32px */
  --spacing-9: 2.25rem;    /* 36px */
  --spacing-10: 2.5rem;    /* 40px */
  --spacing-11: 2.75rem;   /* 44px */
  --spacing-12: 3rem;      /* 48px */
  --spacing-14: 3.5rem;    /* 56px */
  --spacing-16: 4rem;      /* 64px */
  --spacing-20: 5rem;      /* 80px */
  --spacing-24: 6rem;      /* 96px */
  --spacing-28: 7rem;      /* 112px */
  --spacing-32: 8rem;      /* 128px */
  --spacing-36: 9rem;      /* 144px */
  --spacing-40: 10rem;     /* 160px */
  --spacing-44: 11rem;     /* 176px */
  --spacing-48: 12rem;     /* 192px */
  --spacing-52: 13rem;     /* 208px */
  --spacing-56: 14rem;     /* 224px */
  --spacing-60: 15rem;     /* 240px */
  --spacing-64: 16rem;     /* 256px */
  --spacing-72: 18rem;     /* 288px */
  --spacing-80: 20rem;     /* 320px */
  --spacing-96: 24rem;     /* 384px */

  /* 现代化字体系统 */
  --font-family-sans: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Arial', sans-serif;
  --font-family-mono: 'JetBrains Mono', 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', 'Source Code Pro', monospace;
  --font-family-display: 'Inter', 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;

  /* 字体大小 - 完整比例系统 */
  --font-size-xs: 0.75rem;     /* 12px */
  --font-size-sm: 0.875rem;    /* 14px */
  --font-size-base: 1rem;      /* 16px */
  --font-size-lg: 1.125rem;    /* 18px */
  --font-size-xl: 1.25rem;     /* 20px */
  --font-size-2xl: 1.5rem;     /* 24px */
  --font-size-3xl: 1.875rem;   /* 30px */
  --font-size-4xl: 2.25rem;    /* 36px */
  --font-size-5xl: 3rem;       /* 48px */
  --font-size-6xl: 3.75rem;    /* 60px */
  --font-size-7xl: 4.5rem;     /* 72px */
  --font-size-8xl: 6rem;       /* 96px */
  --font-size-9xl: 8rem;       /* 128px */

  /* 字重 - 扩展版本 */
  --font-weight-thin: 100;
  --font-weight-extralight: 200;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;
  --font-weight-extrabold: 800;
  --font-weight-black: 900;

  /* 行高 - 精确控制 */
  --line-height-none: 1;
  --line-height-tight: 1.25;
  --line-height-snug: 1.375;
  --line-height-normal: 1.5;
  --line-height-relaxed: 1.625;
  --line-height-loose: 2;
  --line-height-3: 0.75rem;    /* 12px */
  --line-height-4: 1rem;       /* 16px */
  --line-height-5: 1.25rem;    /* 20px */
  --line-height-6: 1.5rem;     /* 24px */
  --line-height-7: 1.75rem;    /* 28px */
  --line-height-8: 2rem;       /* 32px */
  --line-height-9: 2.25rem;    /* 36px */
  --line-height-10: 2.5rem;    /* 40px */

  /* 字母间距 */
  --letter-spacing-tighter: -0.05em;
  --letter-spacing-tight: -0.025em;
  --letter-spacing-normal: 0em;
  --letter-spacing-wide: 0.025em;
  --letter-spacing-wider: 0.05em;
  --letter-spacing-widest: 0.1em;

  /* 圆角系统 - 现代化设计 */
  --radius-none: 0;
  --radius-sm: 0.125rem;    /* 2px */
  --radius-base: 0.25rem;   /* 4px */
  --radius-md: 0.375rem;    /* 6px */
  --radius-lg: 0.5rem;      /* 8px */
  --radius-xl: 0.75rem;     /* 12px */
  --radius-2xl: 1rem;       /* 16px */
  --radius-3xl: 1.5rem;     /* 24px */
  --radius-4xl: 2rem;       /* 32px */
  --radius-full: 9999px;

  /* 现代化阴影系统 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.05);

  /* 彩色阴影 - AI主题 */
  --shadow-primary: 0 10px 15px -3px rgba(99, 102, 241, 0.1), 0 4px 6px -4px rgba(99, 102, 241, 0.1);
  --shadow-accent: 0 10px 15px -3px rgba(217, 70, 239, 0.1), 0 4px 6px -4px rgba(217, 70, 239, 0.1);
  --shadow-success: 0 10px 15px -3px rgba(34, 197, 94, 0.1), 0 4px 6px -4px rgba(34, 197, 94, 0.1);
  --shadow-warning: 0 10px 15px -3px rgba(251, 191, 36, 0.1), 0 4px 6px -4px rgba(251, 191, 36, 0.1);
  --shadow-error: 0 10px 15px -3px rgba(239, 68, 68, 0.1), 0 4px 6px -4px rgba(239, 68, 68, 0.1);

  /* 过渡动画系统 - 精确控制 */
  --duration-75: 75ms;
  --duration-100: 100ms;
  --duration-150: 150ms;
  --duration-200: 200ms;
  --duration-300: 300ms;
  --duration-500: 500ms;
  --duration-700: 700ms;
  --duration-1000: 1000ms;

  --ease-linear: linear;
  --ease-in: cubic-bezier(0.4, 0, 1, 1);
  --ease-out: cubic-bezier(0, 0, 0.2, 1);
  --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);
  --ease-bounce: cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --ease-elastic: cubic-bezier(0.175, 0.885, 0.32, 1.275);

  --transition-fast: var(--duration-150) var(--ease-out);
  --transition-base: var(--duration-200) var(--ease-out);
  --transition-slow: var(--duration-300) var(--ease-out);
  --transition-slower: var(--duration-500) var(--ease-out);

  /* Z-index层级系统 */
  --z-auto: auto;
  --z-0: 0;
  --z-10: 10;
  --z-20: 20;
  --z-30: 30;
  --z-40: 40;
  --z-50: 50;
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;

  /* 响应式断点 */
  --breakpoint-xs: 475px;
  --breakpoint-sm: 640px;
  --breakpoint-md: 768px;
  --breakpoint-lg: 1024px;
  --breakpoint-xl: 1280px;
  --breakpoint-2xl: 1536px;

  /* 容器最大宽度 */
  --container-xs: 475px;
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;

  /* 模糊效果 */
  --blur-none: 0;
  --blur-sm: 4px;
  --blur-base: 8px;
  --blur-md: 12px;
  --blur-lg: 16px;
  --blur-xl: 24px;
  --blur-2xl: 40px;
  --blur-3xl: 64px;
}

/* ========================================
   基础重置和全局样式
   ======================================== */

* {
  box-sizing: border-box;
}

html {
  font-size: 16px;
  line-height: 1.5;
  -webkit-text-size-adjust: 100%;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

body {
  margin: 0;
  padding: 0;
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
  background-color: var(--color-gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* ========================================
   字体层次系统
   ======================================== */

.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-base { font-size: var(--font-size-base); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }
.text-2xl { font-size: var(--font-size-2xl); }
.text-3xl { font-size: var(--font-size-3xl); }
.text-4xl { font-size: var(--font-size-4xl); }
.text-5xl { font-size: var(--font-size-5xl); }

.font-light { font-weight: var(--font-weight-light); }
.font-normal { font-weight: var(--font-weight-normal); }
.font-medium { font-weight: var(--font-weight-medium); }
.font-semibold { font-weight: var(--font-weight-semibold); }
.font-bold { font-weight: var(--font-weight-bold); }

.leading-tight { line-height: var(--line-height-tight); }
.leading-snug { line-height: var(--line-height-snug); }
.leading-normal { line-height: var(--line-height-normal); }
.leading-relaxed { line-height: var(--line-height-relaxed); }
.leading-loose { line-height: var(--line-height-loose); }

/* ========================================
   间距工具类
   ======================================== */

.m-0 { margin: var(--spacing-0); }
.m-1 { margin: var(--spacing-1); }
.m-2 { margin: var(--spacing-2); }
.m-3 { margin: var(--spacing-3); }
.m-4 { margin: var(--spacing-4); }
.m-5 { margin: var(--spacing-5); }
.m-6 { margin: var(--spacing-6); }
.m-8 { margin: var(--spacing-8); }
.m-10 { margin: var(--spacing-10); }
.m-12 { margin: var(--spacing-12); }
.m-16 { margin: var(--spacing-16); }
.m-20 { margin: var(--spacing-20); }
.m-24 { margin: var(--spacing-24); }
.m-32 { margin: var(--spacing-32); }

.p-0 { padding: var(--spacing-0); }
.p-1 { padding: var(--spacing-1); }
.p-2 { padding: var(--spacing-2); }
.p-3 { padding: var(--spacing-3); }
.p-4 { padding: var(--spacing-4); }
.p-5 { padding: var(--spacing-5); }
.p-6 { padding: var(--spacing-6); }
.p-8 { padding: var(--spacing-8); }
.p-10 { padding: var(--spacing-10); }
.p-12 { padding: var(--spacing-12); }
.p-16 { padding: var(--spacing-16); }
.p-20 { padding: var(--spacing-20); }
.p-24 { padding: var(--spacing-24); }
.p-32 { padding: var(--spacing-32); }

/* 方向性间距 */
.mt-0 { margin-top: var(--spacing-0); }
.mt-1 { margin-top: var(--spacing-1); }
.mt-2 { margin-top: var(--spacing-2); }
.mt-3 { margin-top: var(--spacing-3); }
.mt-4 { margin-top: var(--spacing-4); }
.mt-6 { margin-top: var(--spacing-6); }
.mt-8 { margin-top: var(--spacing-8); }
.mt-12 { margin-top: var(--spacing-12); }
.mt-16 { margin-top: var(--spacing-16); }
.mt-24 { margin-top: var(--spacing-24); }

.mb-0 { margin-bottom: var(--spacing-0); }
.mb-1 { margin-bottom: var(--spacing-1); }
.mb-2 { margin-bottom: var(--spacing-2); }
.mb-3 { margin-bottom: var(--spacing-3); }
.mb-4 { margin-bottom: var(--spacing-4); }
.mb-6 { margin-bottom: var(--spacing-6); }
.mb-8 { margin-bottom: var(--spacing-8); }
.mb-12 { margin-bottom: var(--spacing-12); }
.mb-16 { margin-bottom: var(--spacing-16); }
.mb-24 { margin-bottom: var(--spacing-24); }

.ml-0 { margin-left: var(--spacing-0); }
.ml-1 { margin-left: var(--spacing-1); }
.ml-2 { margin-left: var(--spacing-2); }
.ml-3 { margin-left: var(--spacing-3); }
.ml-4 { margin-left: var(--spacing-4); }
.ml-6 { margin-left: var(--spacing-6); }
.ml-8 { margin-left: var(--spacing-8); }

.mr-0 { margin-right: var(--spacing-0); }
.mr-1 { margin-right: var(--spacing-1); }
.mr-2 { margin-right: var(--spacing-2); }
.mr-3 { margin-right: var(--spacing-3); }
.mr-4 { margin-right: var(--spacing-4); }
.mr-6 { margin-right: var(--spacing-6); }
.mr-8 { margin-right: var(--spacing-8); }

.pt-0 { padding-top: var(--spacing-0); }
.pt-1 { padding-top: var(--spacing-1); }
.pt-2 { padding-top: var(--spacing-2); }
.pt-3 { padding-top: var(--spacing-3); }
.pt-4 { padding-top: var(--spacing-4); }
.pt-6 { padding-top: var(--spacing-6); }
.pt-8 { padding-top: var(--spacing-8); }
.pt-12 { padding-top: var(--spacing-12); }
.pt-16 { padding-top: var(--spacing-16); }
.pt-24 { padding-top: var(--spacing-24); }

.pb-0 { padding-bottom: var(--spacing-0); }
.pb-1 { padding-bottom: var(--spacing-1); }
.pb-2 { padding-bottom: var(--spacing-2); }
.pb-3 { padding-bottom: var(--spacing-3); }
.pb-4 { padding-bottom: var(--spacing-4); }
.pb-6 { padding-bottom: var(--spacing-6); }
.pb-8 { padding-bottom: var(--spacing-8); }
.pb-12 { padding-bottom: var(--spacing-12); }
.pb-16 { padding-bottom: var(--spacing-16); }
.pb-24 { padding-bottom: var(--spacing-24); }

.pl-0 { padding-left: var(--spacing-0); }
.pl-1 { padding-left: var(--spacing-1); }
.pl-2 { padding-left: var(--spacing-2); }
.pl-3 { padding-left: var(--spacing-3); }
.pl-4 { padding-left: var(--spacing-4); }
.pl-6 { padding-left: var(--spacing-6); }
.pl-8 { padding-left: var(--spacing-8); }

.pr-0 { padding-right: var(--spacing-0); }
.pr-1 { padding-right: var(--spacing-1); }
.pr-2 { padding-right: var(--spacing-2); }
.pr-3 { padding-right: var(--spacing-3); }
.pr-4 { padding-right: var(--spacing-4); }
.pr-6 { padding-right: var(--spacing-6); }
.pr-8 { padding-right: var(--spacing-8); }

/* ========================================
   色彩工具类
   ======================================== */

.text-primary-500 { color: var(--color-primary-500); }
.text-primary-600 { color: var(--color-primary-600); }
.text-gray-400 { color: var(--color-gray-400); }
.text-gray-500 { color: var(--color-gray-500); }
.text-gray-600 { color: var(--color-gray-600); }
.text-gray-700 { color: var(--color-gray-700); }
.text-gray-800 { color: var(--color-gray-800); }
.text-gray-900 { color: var(--color-gray-900); }
.text-success-500 { color: var(--color-success-500); }
.text-warning-500 { color: var(--color-warning-500); }
.text-error-500 { color: var(--color-error-500); }
.text-info-500 { color: var(--color-info-500); }

.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: var(--color-gray-50); }
.bg-gray-100 { background-color: var(--color-gray-100); }
.bg-gray-200 { background-color: var(--color-gray-200); }
.bg-primary-50 { background-color: var(--color-primary-50); }
.bg-primary-500 { background-color: var(--color-primary-500); }
.bg-primary-600 { background-color: var(--color-primary-600); }
.bg-success-50 { background-color: var(--color-success-50); }
.bg-success-500 { background-color: var(--color-success-500); }
.bg-warning-50 { background-color: var(--color-warning-50); }
.bg-warning-500 { background-color: var(--color-warning-500); }
.bg-error-50 { background-color: var(--color-error-50); }
.bg-error-500 { background-color: var(--color-error-500); }

/* ========================================
   布局工具类
   ======================================== */

.flex { display: flex; }
.inline-flex { display: inline-flex; }
.grid { display: grid; }
.hidden { display: none; }
.block { display: block; }
.inline-block { display: inline-block; }

.flex-row { flex-direction: row; }
.flex-col { flex-direction: column; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

.flex-1 { flex: 1 1 0%; }
.flex-auto { flex: 1 1 auto; }
.flex-none { flex: none; }

.gap-0 { gap: var(--spacing-0); }
.gap-1 { gap: var(--spacing-1); }
.gap-2 { gap: var(--spacing-2); }
.gap-3 { gap: var(--spacing-3); }
.gap-4 { gap: var(--spacing-4); }
.gap-6 { gap: var(--spacing-6); }
.gap-8 { gap: var(--spacing-8); }
.gap-12 { gap: var(--spacing-12); }
.gap-16 { gap: var(--spacing-16); }

/* ========================================
   圆角工具类
   ======================================== */

.rounded-none { border-radius: var(--radius-none); }
.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-base); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-3xl { border-radius: var(--radius-3xl); }
.rounded-full { border-radius: var(--radius-full); }

/* ========================================
   阴影工具类
   ======================================== */

.shadow-none { box-shadow: none; }
.shadow-xs { box-shadow: var(--shadow-xs); }
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-base); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-2xl { box-shadow: var(--shadow-2xl); }

/* ========================================
   边框工具类
   ======================================== */

.border-0 { border-width: 0; }
.border { border-width: 1px; }
.border-2 { border-width: 2px; }

.border-gray-200 { border-color: var(--color-gray-200); }
.border-gray-300 { border-color: var(--color-gray-300); }
.border-primary-200 { border-color: var(--color-primary-200); }
.border-primary-500 { border-color: var(--color-primary-500); }

/* ========================================
   过渡动画工具类
   ======================================== */

.transition-none { transition: none; }
.transition-all { transition: all var(--transition-base); }
.transition-colors { transition: color var(--transition-base), background-color var(--transition-base), border-color var(--transition-base); }
.transition-opacity { transition: opacity var(--transition-base); }
.transition-transform { transition: transform var(--transition-base); }

.duration-75 { transition-duration: 75ms; }
.duration-100 { transition-duration: 100ms; }
.duration-150 { transition-duration: 150ms; }
.duration-200 { transition-duration: 200ms; }
.duration-300 { transition-duration: 300ms; }
.duration-500 { transition-duration: 500ms; }

.ease-linear { transition-timing-function: linear; }
.ease-in { transition-timing-function: cubic-bezier(0.4, 0, 1, 1); }
.ease-out { transition-timing-function: cubic-bezier(0, 0, 0.2, 1); }
.ease-in-out { transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); }

/* ========================================
   变换工具类
   ======================================== */

.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }

.scale-95 { --tw-scale-x: 0.95; --tw-scale-y: 0.95; }
.scale-100 { --tw-scale-x: 1; --tw-scale-y: 1; }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }

.translate-y-0 { --tw-translate-y: 0px; }
.translate-y-1 { --tw-translate-y: 0.25rem; }
.-translate-y-1 { --tw-translate-y: -0.25rem; }
.-translate-y-2 { --tw-translate-y: -0.5rem; }

/* ========================================
   响应式工具类
   ======================================== */

@media (min-width: 640px) {
  .sm\:block { display: block; }
  .sm\:flex { display: flex; }
  .sm\:hidden { display: none; }
  .sm\:text-sm { font-size: var(--font-size-sm); }
  .sm\:text-base { font-size: var(--font-size-base); }
  .sm\:p-6 { padding: var(--spacing-6); }
  .sm\:p-8 { padding: var(--spacing-8); }
}

@media (min-width: 768px) {
  .md\:block { display: block; }
  .md\:flex { display: flex; }
  .md\:hidden { display: none; }
  .md\:text-lg { font-size: var(--font-size-lg); }
  .md\:text-xl { font-size: var(--font-size-xl); }
  .md\:p-8 { padding: var(--spacing-8); }
  .md\:p-12 { padding: var(--spacing-12); }
}

@media (min-width: 1024px) {
  .lg\:block { display: block; }
  .lg\:flex { display: flex; }
  .lg\:hidden { display: none; }
  .lg\:text-xl { font-size: var(--font-size-xl); }
  .lg\:text-2xl { font-size: var(--font-size-2xl); }
  .lg\:p-12 { padding: var(--spacing-12); }
  .lg\:p-16 { padding: var(--spacing-16); }
}

/* ========================================
   现代化组件系统
   ======================================== */

/* 按钮组件 */
.btn-modern {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-tight);
  border-radius: var(--radius-md);
  border: 1px solid transparent;
  transition: all var(--transition-base);
  cursor: pointer;
  text-decoration: none;
  white-space: nowrap;
  user-select: none;
}

.btn-modern:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.btn-modern:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 按钮变体 */
.btn-primary {
  background-color: var(--color-primary-600);
  color: white;
  border-color: var(--color-primary-600);
}

.btn-primary:hover:not(:disabled) {
  background-color: var(--color-primary-700);
  border-color: var(--color-primary-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-secondary {
  background-color: white;
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
}

.btn-secondary:hover:not(:disabled) {
  background-color: var(--color-gray-50);
  border-color: var(--color-gray-400);
  transform: translateY(-1px);
  box-shadow: var(--shadow-sm);
}

.btn-success {
  background-color: var(--color-success-600);
  color: white;
  border-color: var(--color-success-600);
}

.btn-success:hover:not(:disabled) {
  background-color: var(--color-success-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-warning {
  background-color: var(--color-warning-500);
  color: white;
  border-color: var(--color-warning-500);
}

.btn-warning:hover:not(:disabled) {
  background-color: var(--color-warning-600);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-danger {
  background-color: var(--color-error-600);
  color: white;
  border-color: var(--color-error-600);
}

.btn-danger:hover:not(:disabled) {
  background-color: var(--color-error-700);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

/* 按钮大小 */
.btn-xs {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
}

.btn-lg {
  padding: var(--spacing-4) var(--spacing-6);
  font-size: var(--font-size-lg);
}

.btn-xl {
  padding: var(--spacing-5) var(--spacing-8);
  font-size: var(--font-size-xl);
}

/* 卡片组件 */
.card-modern {
  background-color: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--color-gray-200);
  overflow: hidden;
  transition: all var(--transition-base);
}

.card-modern:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
}

.card-header-modern {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
  background-color: var(--color-gray-50);
}

.card-body-modern {
  padding: var(--spacing-6);
}

.card-footer-modern {
  padding: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
  background-color: var(--color-gray-50);
}

/* 输入框组件 */
.input-modern {
  display: block;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  line-height: var(--line-height-tight);
  color: var(--color-gray-900);
  background-color: white;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  transition: all var(--transition-base);
}

.input-modern:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-modern:disabled {
  background-color: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
}

.input-modern::placeholder {
  color: var(--color-gray-400);
}

/* 标签组件 */
.label-modern {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
}

/* 徽章组件 */
.badge-modern {
  display: inline-flex;
  align-items: center;
  padding: var(--spacing-1) var(--spacing-3);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  border-radius: var(--radius-full);
  white-space: nowrap;
}

.badge-primary {
  background-color: var(--color-primary-100);
  color: var(--color-primary-800);
}

.badge-success {
  background-color: var(--color-success-100);
  color: var(--color-success-800);
}

.badge-warning {
  background-color: var(--color-warning-100);
  color: var(--color-warning-800);
}

.badge-error {
  background-color: var(--color-error-100);
  color: var(--color-error-800);
}

.badge-gray {
  background-color: var(--color-gray-100);
  color: var(--color-gray-800);
}

/* 导航组件 */
.nav-modern {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) var(--spacing-6);
  background-color: white;
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
}

.nav-brand-modern {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  text-decoration: none;
}

.nav-brand-modern:hover {
  color: var(--color-primary-600);
}

.nav-links-modern {
  display: flex;
  align-items: center;
  gap: var(--spacing-6);
}

.nav-link-modern {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-600);
  text-decoration: none;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  transition: all var(--transition-base);
}

.nav-link-modern:hover {
  color: var(--color-primary-600);
  background-color: var(--color-primary-50);
}

.nav-link-modern.active {
  color: var(--color-primary-600);
  background-color: var(--color-primary-100);
}

/* 容器组件 */
.container-modern {
  width: 100%;
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--spacing-4);
}

@media (min-width: 640px) {
  .container-modern {
    padding: 0 var(--spacing-6);
  }
}

@media (min-width: 1024px) {
  .container-modern {
    padding: 0 var(--spacing-8);
  }
}

/* 网格系统 */
.grid-modern {
  display: grid;
  gap: var(--spacing-6);
}

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
.grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
.grid-cols-12 { grid-template-columns: repeat(12, minmax(0, 1fr)); }

.col-span-1 { grid-column: span 1 / span 1; }
.col-span-2 { grid-column: span 2 / span 2; }
.col-span-3 { grid-column: span 3 / span 3; }
.col-span-4 { grid-column: span 4 / span 4; }
.col-span-6 { grid-column: span 6 / span 6; }
.col-span-12 { grid-column: span 12 / span 12; }

@media (min-width: 768px) {
  .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
  .md\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .md\:col-span-2 { grid-column: span 2 / span 2; }
  .md\:col-span-3 { grid-column: span 3 / span 3; }
  .md\:col-span-4 { grid-column: span 4 / span 4; }
}

@media (min-width: 1024px) {
  .lg\:grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
  .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
  .lg\:grid-cols-6 { grid-template-columns: repeat(6, minmax(0, 1fr)); }
  .lg\:col-span-2 { grid-column: span 2 / span 2; }
  .lg\:col-span-3 { grid-column: span 3 / span 3; }
  .lg\:col-span-4 { grid-column: span 4 / span 4; }
}

/* 状态指示器 */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.status-indicator::before {
  content: '';
  width: var(--spacing-2);
  height: var(--spacing-2);
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.status-online::before {
  background-color: var(--color-success-500);
  box-shadow: 0 0 0 2px var(--color-success-100);
}

.status-offline::before {
  background-color: var(--color-gray-400);
  box-shadow: 0 0 0 2px var(--color-gray-100);
}

.status-warning::before {
  background-color: var(--color-warning-500);
  box-shadow: 0 0 0 2px var(--color-warning-100);
}

.status-error::before {
  background-color: var(--color-error-500);
  box-shadow: 0 0 0 2px var(--color-error-100);
}

/* 加载状态 */
.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-gray-200);
  border-top: 2px solid var(--color-primary-500);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-dots {
  display: inline-flex;
  gap: var(--spacing-1);
}

.loading-dots::before,
.loading-dots::after {
  content: '';
  width: var(--spacing-1);
  height: var(--spacing-1);
  background-color: var(--color-primary-500);
  border-radius: var(--radius-full);
  animation: pulse 1.5s ease-in-out infinite;
}

.loading-dots::after {
  animation-delay: 0.5s;
}

@keyframes pulse {
  0%, 100% { opacity: 0.4; }
  50% { opacity: 1; }
}

/* 工具提示 */
.tooltip-modern {
  position: relative;
  display: inline-block;
}

.tooltip-modern::after {
  content: attr(data-tooltip);
  position: absolute;
  bottom: 100%;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--color-gray-900);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity var(--transition-base);
  z-index: var(--z-tooltip);
}

.tooltip-modern:hover::after {
  opacity: 1;
}
