package auth

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"sync"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedJWTManager 增强的JWT管理器
type EnhancedJWTManager struct {
	*JWTManager
	db                    *gorm.DB
	logger                *logrus.Logger
	sessionManager        *SessionManager
	deviceManager         *DeviceManager
	maxConcurrentSessions int
}

// SessionManager 会话管理器
type SessionManager struct {
	db     *gorm.DB
	logger *logrus.Logger
	mu     sync.RWMutex
}

// DeviceManager 设备管理器
type DeviceManager struct {
	db     *gorm.DB
	logger *logrus.Logger
	mu     sync.RWMutex
}

// UserSession 用户会话
type UserSession struct {
	model.BaseModel
	UserID       int64     `json:"user_id" gorm:"index"`
	SessionID    string    `json:"session_id" gorm:"uniqueIndex"`
	DeviceID     string    `json:"device_id" gorm:"index"`
	DeviceInfo   string    `json:"device_info"`
	IPAddress    string    `json:"ip_address"`
	UserAgent    string    `json:"user_agent"`
	RefreshToken string    `json:"refresh_token" gorm:"index"`
	ExpiresAt    time.Time `json:"expires_at"`
	LastActivity time.Time `json:"last_activity"`
	IsActive     bool      `json:"is_active" gorm:"default:true"`
}

// UserDevice 用户设备
type UserDevice struct {
	model.BaseModel
	UserID     int64     `json:"user_id" gorm:"index"`
	DeviceID   string    `json:"device_id" gorm:"uniqueIndex"`
	DeviceName string    `json:"device_name"`
	DeviceType string    `json:"device_type"` // web, mobile, desktop
	Platform   string    `json:"platform"`    // windows, linux, android, ios
	UserAgent  string    `json:"user_agent"`
	IPAddress  string    `json:"ip_address"`
	LastSeen   time.Time `json:"last_seen"`
	IsActive   bool      `json:"is_active" gorm:"default:true"`
	IsTrusted  bool      `json:"is_trusted" gorm:"default:false"`
}

// TokenBlacklistEntry 令牌黑名单条目
type TokenBlacklistEntry struct {
	model.BaseModel
	TokenID   string    `json:"token_id" gorm:"uniqueIndex"`
	UserID    int64     `json:"user_id" gorm:"index"`
	TokenType string    `json:"token_type"` // access, refresh
	ExpiresAt time.Time `json:"expires_at"`
	Reason    string    `json:"reason"`
}

// NewEnhancedJWTManager 创建增强的JWT管理器
func NewEnhancedJWTManager(cfg config.JWTConfig, db *gorm.DB, logger *logrus.Logger) *EnhancedJWTManager {
	baseManager := NewJWTManager(cfg, &DatabaseTokenBlacklist{db: db})

	enhanced := &EnhancedJWTManager{
		JWTManager:            baseManager,
		db:                    db,
		logger:                logger,
		sessionManager:        NewSessionManager(db, logger),
		deviceManager:         NewDeviceManager(db, logger),
		maxConcurrentSessions: cfg.MaxConcurrentSessions,
	}

	// 自动迁移数据库表
	enhanced.migrate()

	return enhanced
}

// migrate 迁移数据库表
func (m *EnhancedJWTManager) migrate() {
	if err := m.db.AutoMigrate(
		&UserSession{},
		&UserDevice{},
		&TokenBlacklistEntry{},
	); err != nil {
		m.logger.WithError(err).Error("Failed to migrate JWT tables")
	}
}

// GenerateTokenPairWithDevice 生成带设备信息的令牌对
func (m *EnhancedJWTManager) GenerateTokenPairWithDevice(ctx context.Context, userID int64, username, role string, deviceInfo DeviceInfo) (*TokenPair, error) {
	// 生成会话ID
	sessionID, err := generateSessionID()
	if err != nil {
		return nil, fmt.Errorf("failed to generate session ID: %w", err)
	}

	// 注册或更新设备
	device, err := m.deviceManager.RegisterDevice(ctx, userID, deviceInfo)
	if err != nil {
		m.logger.WithError(err).Error("Failed to register device")
		// 不阻断登录流程，继续执行
	}

	// 检查并清理超出限制的会话
	if err := m.sessionManager.CleanupExcessSessions(ctx, userID, m.maxConcurrentSessions); err != nil {
		m.logger.WithError(err).Error("Failed to cleanup excess sessions")
	}

	// 生成令牌对
	tokenPair, err := m.GenerateTokenPair(userID, username, role, sessionID)
	if err != nil {
		return nil, err
	}

	// 创建会话记录
	session := &UserSession{
		UserID:       userID,
		SessionID:    sessionID,
		DeviceID:     device.DeviceID,
		DeviceInfo:   deviceInfo.String(),
		IPAddress:    deviceInfo.IPAddress,
		UserAgent:    deviceInfo.UserAgent,
		RefreshToken: tokenPair.RefreshToken,
		ExpiresAt:    time.Now().Add(m.refreshTTL),
		LastActivity: time.Now(),
		IsActive:     true,
	}

	if err := m.sessionManager.CreateSession(ctx, session); err != nil {
		m.logger.WithError(err).Error("Failed to create session")
		// 不阻断登录流程
	}

	return tokenPair, nil
}

// RefreshTokenPair 刷新令牌对
func (m *EnhancedJWTManager) RefreshTokenPair(ctx context.Context, refreshToken string) (*TokenPair, error) {
	// 验证刷新令牌
	claims, err := m.ValidateToken(refreshToken)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	if claims.TokenType != "refresh" {
		return nil, fmt.Errorf("invalid token type")
	}

	// 检查会话是否存在且有效
	session, err := m.sessionManager.GetSessionByID(ctx, claims.SessionID)
	if err != nil {
		return nil, fmt.Errorf("session not found: %w", err)
	}

	if !session.IsActive || session.ExpiresAt.Before(time.Now()) {
		return nil, fmt.Errorf("session expired or inactive")
	}

	// 将旧的刷新令牌加入黑名单
	if err := m.blacklist.Add(claims.ID, claims.ExpiresAt.Time); err != nil {
		m.logger.WithError(err).Error("Failed to blacklist old refresh token")
	}

	// 生成新的令牌对
	newTokenPair, err := m.GenerateTokenPair(claims.UserID, claims.Username, claims.Role, claims.SessionID)
	if err != nil {
		return nil, err
	}

	// 更新会话信息
	session.RefreshToken = newTokenPair.RefreshToken
	session.LastActivity = time.Now()
	session.ExpiresAt = time.Now().Add(m.refreshTTL)

	if err := m.sessionManager.UpdateSession(ctx, session); err != nil {
		m.logger.WithError(err).Error("Failed to update session")
	}

	return newTokenPair, nil
}

// RevokeSession 撤销会话
func (m *EnhancedJWTManager) RevokeSession(ctx context.Context, sessionID string) error {
	session, err := m.sessionManager.GetSessionByID(ctx, sessionID)
	if err != nil {
		return err
	}

	// 将相关令牌加入黑名单
	if err := m.blacklist.Add(session.SessionID+"_access", time.Now().Add(m.accessTTL)); err != nil {
		m.logger.WithError(err).Error("Failed to blacklist access token")
	}

	if err := m.blacklist.Add(session.SessionID+"_refresh", session.ExpiresAt); err != nil {
		m.logger.WithError(err).Error("Failed to blacklist refresh token")
	}

	// 标记会话为非活跃
	session.IsActive = false
	return m.sessionManager.UpdateSession(ctx, session)
}

// RevokeAllUserSessions 撤销用户的所有会话
func (m *EnhancedJWTManager) RevokeAllUserSessions(ctx context.Context, userID int64) error {
	sessions, err := m.sessionManager.GetUserSessions(ctx, userID)
	if err != nil {
		return err
	}

	for _, session := range sessions {
		if err := m.RevokeSession(ctx, session.SessionID); err != nil {
			m.logger.WithError(err).Errorf("Failed to revoke session %s", session.SessionID)
		}
	}

	return nil
}

// GetUserSessions 获取用户会话列表
func (m *EnhancedJWTManager) GetUserSessions(ctx context.Context, userID int64) ([]*UserSession, error) {
	return m.sessionManager.GetUserSessions(ctx, userID)
}

// GetUserDevices 获取用户设备列表
func (m *EnhancedJWTManager) GetUserDevices(ctx context.Context, userID int64) ([]*UserDevice, error) {
	return m.deviceManager.GetUserDevices(ctx, userID)
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	DeviceID   string
	DeviceName string
	DeviceType string
	Platform   string
	UserAgent  string
	IPAddress  string
}

// String 返回设备信息的字符串表示
func (d DeviceInfo) String() string {
	return fmt.Sprintf("%s (%s) - %s", d.DeviceName, d.Platform, d.DeviceType)
}

// generateSessionID 生成会话ID
func generateSessionID() (string, error) {
	bytes := make([]byte, 32)
	if _, err := rand.Read(bytes); err != nil {
		return "", err
	}
	return hex.EncodeToString(bytes), nil
}

// DatabaseTokenBlacklist 基于数据库的令牌黑名单
type DatabaseTokenBlacklist struct {
	db *gorm.DB
}

// Add 添加令牌到黑名单
func (b *DatabaseTokenBlacklist) Add(tokenID string, expiry time.Time) error {
	entry := &TokenBlacklistEntry{
		TokenID:   tokenID,
		ExpiresAt: expiry,
		Reason:    "revoked",
	}
	return b.db.Create(entry).Error
}

// IsBlacklisted 检查令牌是否在黑名单中
func (b *DatabaseTokenBlacklist) IsBlacklisted(tokenID string) bool {
	var count int64
	b.db.Model(&TokenBlacklistEntry{}).
		Where("token_id = ? AND expires_at > ?", tokenID, time.Now()).
		Count(&count)
	return count > 0
}

// Cleanup 清理过期的黑名单条目
func (b *DatabaseTokenBlacklist) Cleanup() error {
	return b.db.Where("expires_at < ?", time.Now()).Delete(&TokenBlacklistEntry{}).Error
}

// NewSessionManager 创建会话管理器
func NewSessionManager(db *gorm.DB, logger *logrus.Logger) *SessionManager {
	return &SessionManager{
		db:     db,
		logger: logger,
	}
}

// CreateSession 创建会话
func (sm *SessionManager) CreateSession(ctx context.Context, session *UserSession) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	return sm.db.WithContext(ctx).Create(session).Error
}

// GetSessionByID 根据ID获取会话
func (sm *SessionManager) GetSessionByID(ctx context.Context, sessionID string) (*UserSession, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	var session UserSession
	err := sm.db.WithContext(ctx).Where("session_id = ?", sessionID).First(&session).Error
	if err != nil {
		return nil, err
	}
	return &session, nil
}

// UpdateSession 更新会话
func (sm *SessionManager) UpdateSession(ctx context.Context, session *UserSession) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()
	return sm.db.WithContext(ctx).Save(session).Error
}

// GetUserSessions 获取用户的所有会话
func (sm *SessionManager) GetUserSessions(ctx context.Context, userID int64) ([]*UserSession, error) {
	sm.mu.RLock()
	defer sm.mu.RUnlock()

	var sessions []*UserSession
	err := sm.db.WithContext(ctx).
		Where("user_id = ? AND is_active = ?", userID, true).
		Order("last_activity DESC").
		Find(&sessions).Error
	return sessions, err
}

// CleanupExcessSessions 清理超出限制的会话
func (sm *SessionManager) CleanupExcessSessions(ctx context.Context, userID int64, maxSessions int) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	var sessions []*UserSession
	err := sm.db.WithContext(ctx).
		Where("user_id = ? AND is_active = ?", userID, true).
		Order("last_activity DESC").
		Find(&sessions).Error
	if err != nil {
		return err
	}

	if len(sessions) >= maxSessions {
		// 保留最新的 maxSessions-1 个会话，删除其余的
		sessionsToDeactivate := sessions[maxSessions-1:]
		for _, session := range sessionsToDeactivate {
			session.IsActive = false
			if err := sm.db.WithContext(ctx).Save(session).Error; err != nil {
				sm.logger.WithError(err).Errorf("Failed to deactivate session %s", session.SessionID)
			}
		}
	}

	return nil
}

// CleanupExpiredSessions 清理过期会话
func (sm *SessionManager) CleanupExpiredSessions(ctx context.Context) error {
	sm.mu.Lock()
	defer sm.mu.Unlock()

	return sm.db.WithContext(ctx).
		Model(&UserSession{}).
		Where("expires_at < ? OR last_activity < ?", time.Now(), time.Now().Add(-24*time.Hour)).
		Update("is_active", false).Error
}

// NewDeviceManager 创建设备管理器
func NewDeviceManager(db *gorm.DB, logger *logrus.Logger) *DeviceManager {
	return &DeviceManager{
		db:     db,
		logger: logger,
	}
}

// RegisterDevice 注册或更新设备
func (dm *DeviceManager) RegisterDevice(ctx context.Context, userID int64, deviceInfo DeviceInfo) (*UserDevice, error) {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	var device UserDevice
	err := dm.db.WithContext(ctx).Where("device_id = ?", deviceInfo.DeviceID).First(&device).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新设备
		device = UserDevice{
			UserID:     userID,
			DeviceID:   deviceInfo.DeviceID,
			DeviceName: deviceInfo.DeviceName,
			DeviceType: deviceInfo.DeviceType,
			Platform:   deviceInfo.Platform,
			UserAgent:  deviceInfo.UserAgent,
			IPAddress:  deviceInfo.IPAddress,
			LastSeen:   time.Now(),
			IsActive:   true,
			IsTrusted:  false,
		}
		err = dm.db.WithContext(ctx).Create(&device).Error
	} else if err == nil {
		// 更新现有设备
		device.LastSeen = time.Now()
		device.IPAddress = deviceInfo.IPAddress
		device.UserAgent = deviceInfo.UserAgent
		device.IsActive = true
		err = dm.db.WithContext(ctx).Save(&device).Error
	}

	return &device, err
}

// GetUserDevices 获取用户设备列表
func (dm *DeviceManager) GetUserDevices(ctx context.Context, userID int64) ([]*UserDevice, error) {
	dm.mu.RLock()
	defer dm.mu.RUnlock()

	var devices []*UserDevice
	err := dm.db.WithContext(ctx).
		Where("user_id = ? AND is_active = ?", userID, true).
		Order("last_seen DESC").
		Find(&devices).Error
	return devices, err
}

// TrustDevice 信任设备
func (dm *DeviceManager) TrustDevice(ctx context.Context, deviceID string) error {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	return dm.db.WithContext(ctx).
		Model(&UserDevice{}).
		Where("device_id = ?", deviceID).
		Update("is_trusted", true).Error
}

// RevokeDevice 撤销设备
func (dm *DeviceManager) RevokeDevice(ctx context.Context, deviceID string) error {
	dm.mu.Lock()
	defer dm.mu.Unlock()

	return dm.db.WithContext(ctx).
		Model(&UserDevice{}).
		Where("device_id = ?", deviceID).
		Update("is_active", false).Error
}

// TrustDevice 信任设备 (EnhancedJWTManager方法)
func (m *EnhancedJWTManager) TrustDevice(ctx context.Context, deviceID string) error {
	return m.deviceManager.TrustDevice(ctx, deviceID)
}

// RevokeDevice 撤销设备 (EnhancedJWTManager方法)
func (m *EnhancedJWTManager) RevokeDevice(ctx context.Context, deviceID string) error {
	return m.deviceManager.RevokeDevice(ctx, deviceID)
}
