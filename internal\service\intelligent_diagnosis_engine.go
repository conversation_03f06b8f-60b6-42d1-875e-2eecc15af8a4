package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// IntelligentDiagnosisEngine 智能诊断引擎
type IntelligentDiagnosisEngine struct {
	db          *gorm.DB
	logger      *logrus.Logger
	hostService HostService

	// 诊断工具注册表
	diagnosticTools map[string]DiagnosticTool

	// 诊断结果缓存
	resultCache map[string]*DiagnosisResult
	cacheMutex  sync.RWMutex

	// 配置
	config *DiagnosisConfig
}

// DiagnosticTool 诊断工具接口
type DiagnosticTool interface {
	GetName() string
	GetDescription() string
	GetCategory() string
	Diagnose(ctx context.Context, target *DiagnosisTarget) (*DiagnosisResult, error)
	GetSupportedTargets() []string
}

// DiagnosisTarget 诊断目标
type DiagnosisTarget struct {
	Type       string                 `json:"type"`       // host, service, network, etc.
	Identifier string                 `json:"identifier"` // IP, hostname, service name, etc.
	Parameters map[string]interface{} `json:"parameters"` // 额外参数
	Context    map[string]interface{} `json:"context"`    // 上下文信息
}

// DiagnosisResult 诊断结果
type DiagnosisResult struct {
	ToolName    string                 `json:"tool_name"`
	Target      *DiagnosisTarget       `json:"target"`
	Status      string                 `json:"status"`      // success, warning, error, unknown
	Summary     string                 `json:"summary"`     // 诊断摘要
	Details     []DiagnosisDetail      `json:"details"`     // 详细结果
	Suggestions []string               `json:"suggestions"` // 建议解决方案
	Metadata    map[string]interface{} `json:"metadata"`    // 元数据
	Timestamp   time.Time              `json:"timestamp"`
	Duration    time.Duration          `json:"duration"`
}

// DiagnosisDetail 诊断详情
type DiagnosisDetail struct {
	Category string      `json:"category"` // connectivity, authentication, performance, etc.
	Item     string      `json:"item"`     // 检查项目
	Status   string      `json:"status"`   // pass, fail, warning, skip
	Message  string      `json:"message"`  // 详细信息
	Value    interface{} `json:"value"`    // 检查值
	Expected interface{} `json:"expected"` // 期望值
	Severity string      `json:"severity"` // low, medium, high, critical
}

// DiagnosisConfig 诊断配置
type DiagnosisConfig struct {
	EnableCache     bool          `json:"enable_cache"`
	CacheExpiration time.Duration `json:"cache_expiration"`
	MaxConcurrent   int           `json:"max_concurrent"`
	DefaultTimeout  time.Duration `json:"default_timeout"`
	RetryAttempts   int           `json:"retry_attempts"`
	RetryDelay      time.Duration `json:"retry_delay"`
}

// NewIntelligentDiagnosisEngine 创建智能诊断引擎
func NewIntelligentDiagnosisEngine(db *gorm.DB, logger *logrus.Logger, hostService HostService) *IntelligentDiagnosisEngine {
	config := &DiagnosisConfig{
		EnableCache:     true,
		CacheExpiration: 5 * time.Minute,
		MaxConcurrent:   10,
		DefaultTimeout:  30 * time.Second,
		RetryAttempts:   3,
		RetryDelay:      2 * time.Second,
	}

	engine := &IntelligentDiagnosisEngine{
		db:              db,
		logger:          logger,
		hostService:     hostService,
		diagnosticTools: make(map[string]DiagnosticTool),
		resultCache:     make(map[string]*DiagnosisResult),
		config:          config,
	}

	// 注册内置诊断工具
	engine.registerBuiltinTools()

	return engine
}

// registerBuiltinTools 注册内置诊断工具
func (ide *IntelligentDiagnosisEngine) registerBuiltinTools() {
	// 主机连接诊断工具
	ide.RegisterTool(&HostConnectivityDiagnosticTool{
		logger:      ide.logger,
		hostService: ide.hostService,
		timeout:     ide.config.DefaultTimeout,
	})

	// SSH认证诊断工具
	ide.RegisterTool(&SSHAuthenticationDiagnosticTool{
		logger:      ide.logger,
		hostService: ide.hostService,
		timeout:     ide.config.DefaultTimeout,
	})

	// 网络连通性诊断工具
	ide.RegisterTool(&NetworkConnectivityDiagnosticTool{
		logger:  ide.logger,
		timeout: ide.config.DefaultTimeout,
	})

	// 主机状态诊断工具
	ide.RegisterTool(&HostStatusDiagnosticTool{
		logger:      ide.logger,
		hostService: ide.hostService,
		timeout:     ide.config.DefaultTimeout,
	})

	ide.logger.WithField("tools_count", len(ide.diagnosticTools)).Info("Diagnostic tools registered")
}

// RegisterTool 注册诊断工具
func (ide *IntelligentDiagnosisEngine) RegisterTool(tool DiagnosticTool) {
	ide.diagnosticTools[tool.GetName()] = tool
	ide.logger.WithFields(logrus.Fields{
		"tool_name":   tool.GetName(),
		"description": tool.GetDescription(),
		"category":    tool.GetCategory(),
	}).Info("Diagnostic tool registered")
}

// DiagnoseHost 诊断主机
func (ide *IntelligentDiagnosisEngine) DiagnoseHost(ctx context.Context, hostIP string, diagnosisType string) (*DiagnosisResult, error) {
	target := &DiagnosisTarget{
		Type:       "host",
		Identifier: hostIP,
		Parameters: map[string]interface{}{
			"diagnosis_type": diagnosisType,
		},
		Context: map[string]interface{}{
			"timestamp": time.Now(),
		},
	}

	// 根据诊断类型选择合适的工具
	var toolName string
	switch diagnosisType {
	case "connectivity":
		toolName = "host_connectivity"
	case "authentication":
		toolName = "ssh_authentication"
	case "status":
		toolName = "host_status"
	case "comprehensive":
		// 综合诊断，使用多个工具
		return ide.comprehensiveDiagnosis(ctx, target)
	default:
		toolName = "host_connectivity" // 默认使用连接诊断
	}

	return ide.runDiagnosis(ctx, toolName, target)
}

// runDiagnosis 运行诊断
func (ide *IntelligentDiagnosisEngine) runDiagnosis(ctx context.Context, toolName string, target *DiagnosisTarget) (*DiagnosisResult, error) {
	// 检查缓存
	if ide.config.EnableCache {
		if cached := ide.getCachedResult(toolName, target); cached != nil {
			ide.logger.WithFields(logrus.Fields{
				"tool_name": toolName,
				"target":    target.Identifier,
			}).Info("Using cached diagnosis result")
			return cached, nil
		}
	}

	// 获取诊断工具
	tool, exists := ide.diagnosticTools[toolName]
	if !exists {
		return nil, fmt.Errorf("diagnostic tool '%s' not found", toolName)
	}

	ide.logger.WithFields(logrus.Fields{
		"tool_name": toolName,
		"target":    target.Identifier,
	}).Info("Starting diagnosis")

	start := time.Now()

	// 执行诊断
	result, err := tool.Diagnose(ctx, target)
	if err != nil {
		ide.logger.WithError(err).WithFields(logrus.Fields{
			"tool_name": toolName,
			"target":    target.Identifier,
		}).Error("Diagnosis failed")
		return nil, fmt.Errorf("diagnosis failed: %w", err)
	}

	result.Duration = time.Since(start)
	result.Timestamp = time.Now()

	// 缓存结果
	if ide.config.EnableCache {
		ide.cacheResult(toolName, target, result)
	}

	ide.logger.WithFields(logrus.Fields{
		"tool_name": toolName,
		"target":    target.Identifier,
		"status":    result.Status,
		"duration":  result.Duration,
	}).Info("Diagnosis completed")

	return result, nil
}

// comprehensiveDiagnosis 综合诊断
func (ide *IntelligentDiagnosisEngine) comprehensiveDiagnosis(ctx context.Context, target *DiagnosisTarget) (*DiagnosisResult, error) {
	// 运行多个诊断工具
	tools := []string{"network_connectivity", "host_connectivity", "ssh_authentication", "host_status"}

	var allDetails []DiagnosisDetail
	var allSuggestions []string
	overallStatus := "success"

	for _, toolName := range tools {
		if tool, exists := ide.diagnosticTools[toolName]; exists {
			result, err := tool.Diagnose(ctx, target)
			if err != nil {
				ide.logger.WithError(err).WithField("tool", toolName).Warn("Tool diagnosis failed")
				continue
			}

			allDetails = append(allDetails, result.Details...)
			allSuggestions = append(allSuggestions, result.Suggestions...)

			// 更新整体状态
			if result.Status == "error" {
				overallStatus = "error"
			} else if result.Status == "warning" && overallStatus == "success" {
				overallStatus = "warning"
			}
		}
	}

	return &DiagnosisResult{
		ToolName:    "comprehensive_diagnosis",
		Target:      target,
		Status:      overallStatus,
		Summary:     ide.generateComprehensiveSummary(allDetails),
		Details:     allDetails,
		Suggestions: ide.deduplicateSuggestions(allSuggestions),
		Metadata: map[string]interface{}{
			"tools_used": tools,
		},
		Timestamp: time.Now(),
	}, nil
}

// getCachedResult 获取缓存结果
func (ide *IntelligentDiagnosisEngine) getCachedResult(toolName string, target *DiagnosisTarget) *DiagnosisResult {
	ide.cacheMutex.RLock()
	defer ide.cacheMutex.RUnlock()

	key := fmt.Sprintf("%s:%s:%s", toolName, target.Type, target.Identifier)
	if result, exists := ide.resultCache[key]; exists {
		if time.Since(result.Timestamp) < ide.config.CacheExpiration {
			return result
		}
		// 过期，删除缓存
		delete(ide.resultCache, key)
	}
	return nil
}

// cacheResult 缓存结果
func (ide *IntelligentDiagnosisEngine) cacheResult(toolName string, target *DiagnosisTarget, result *DiagnosisResult) {
	ide.cacheMutex.Lock()
	defer ide.cacheMutex.Unlock()

	key := fmt.Sprintf("%s:%s:%s", toolName, target.Type, target.Identifier)
	ide.resultCache[key] = result
}

// generateComprehensiveSummary 生成综合摘要
func (ide *IntelligentDiagnosisEngine) generateComprehensiveSummary(details []DiagnosisDetail) string {
	var issues []string
	var warnings []string

	for _, detail := range details {
		if detail.Status == "fail" {
			issues = append(issues, detail.Message)
		} else if detail.Status == "warning" {
			warnings = append(warnings, detail.Message)
		}
	}

	if len(issues) > 0 {
		return fmt.Sprintf("发现 %d 个问题：%s", len(issues), strings.Join(issues, "; "))
	} else if len(warnings) > 0 {
		return fmt.Sprintf("发现 %d 个警告：%s", len(warnings), strings.Join(warnings, "; "))
	}

	return "所有检查项目均正常"
}

// deduplicateSuggestions 去重建议
func (ide *IntelligentDiagnosisEngine) deduplicateSuggestions(suggestions []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, suggestion := range suggestions {
		if !seen[suggestion] {
			seen[suggestion] = true
			result = append(result, suggestion)
		}
	}

	return result
}

// GetAvailableTools 获取可用工具列表
func (ide *IntelligentDiagnosisEngine) GetAvailableTools() []DiagnosticTool {
	var tools []DiagnosticTool
	for _, tool := range ide.diagnosticTools {
		tools = append(tools, tool)
	}
	return tools
}
