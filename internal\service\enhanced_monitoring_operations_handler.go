package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedMonitoringOperationsHandler 增强的监控操作处理器
type EnhancedMonitoringOperationsHandler struct {
	db              *gorm.DB
	logger          *logrus.Logger
	hostService     HostService
	diagnosisEngine *IntelligentDiagnosisEngine
}

// NewEnhancedMonitoringOperationsHandler 创建增强的监控操作处理器
func NewEnhancedMonitoringOperationsHandler(db *gorm.DB, logger *logrus.Logger, hostService HostService) *EnhancedMonitoringOperationsHandler {
	diagnosisEngine := NewIntelligentDiagnosisEngine(db, logger, hostService)

	return &EnhancedMonitoringOperationsHandler{
		db:              db,
		logger:          logger,
		hostService:     hostService,
		diagnosisEngine: diagnosisEngine,
	}
}

// Handle 处理监控操作
func (h *EnhancedMonitoringOperationsHandler) Handle(ctx context.Context, intent *IntentResult, userID int64) (*SimplifiedIntentResponse, error) {
	params := intent.Parameters

	// 获取操作类型
	operation, ok := params["operation"].(string)
	if !ok {
		return h.createErrorResponse("未指定操作类型"), nil
	}

	h.logger.WithFields(logrus.Fields{
		"operation": operation,
		"user_id":   userID,
		"params":    params,
	}).Info("Processing monitoring operation")

	switch operation {
	case "host_diagnosis":
		return h.handleHostDiagnosis(ctx, params, userID)
	case "connectivity_diagnosis":
		return h.handleConnectivityDiagnosis(ctx, params, userID)
	case "connectivity_test":
		return h.handleConnectivityTest(ctx, params, userID)
	case "troubleshooting":
		return h.handleTroubleshooting(ctx, params, userID)
	case "system_monitor":
		return h.handleSystemMonitor(ctx, params, userID)
	case "log_analysis":
		return h.handleLogAnalysis(ctx, params, userID)
	case "performance_analysis":
		return h.handlePerformanceAnalysis(ctx, params, userID)
	default:
		return h.createErrorResponse(fmt.Sprintf("不支持的监控操作: %s", operation)), nil
	}
}

// handleHostDiagnosis 处理主机诊断
func (h *EnhancedMonitoringOperationsHandler) handleHostDiagnosis(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	// 解析目标主机
	targetHost, err := h.parseTargetHost(params)
	if err != nil {
		return h.createErrorResponse(fmt.Sprintf("解析目标主机失败: %v", err)), nil
	}

	// 获取诊断类型
	diagnosisType := "comprehensive" // 默认综合诊断
	if dt, ok := params["diagnosis_type"].(string); ok {
		diagnosisType = dt
	}

	h.logger.WithFields(logrus.Fields{
		"target_host":    targetHost,
		"diagnosis_type": diagnosisType,
		"user_id":        userID,
	}).Info("Starting host diagnosis")

	// 执行诊断
	result, err := h.diagnosisEngine.DiagnoseHost(ctx, targetHost, diagnosisType)
	if err != nil {
		h.logger.WithError(err).Error("Host diagnosis failed")
		return h.createErrorResponse(fmt.Sprintf("主机诊断失败: %v", err)), nil
	}

	// 格式化诊断结果
	content := h.formatDiagnosisResult(result)

	return &SimplifiedIntentResponse{
		Success: result.Status != "error",
		Content: content,
		Action:  "diagnosis_completed",
		Data:    result,
		Metadata: map[string]interface{}{
			"target_host":    targetHost,
			"diagnosis_type": diagnosisType,
			"tool_name":      result.ToolName,
			"status":         result.Status,
			"duration":       result.Duration.String(),
		},
	}, nil
}

// handleConnectivityDiagnosis 处理连接诊断
func (h *EnhancedMonitoringOperationsHandler) handleConnectivityDiagnosis(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	// 解析目标主机
	targetHost, err := h.parseTargetHost(params)
	if err != nil {
		return h.createErrorResponse(fmt.Sprintf("解析目标主机失败: %v", err)), nil
	}

	h.logger.WithFields(logrus.Fields{
		"target_host": targetHost,
		"user_id":     userID,
	}).Info("Starting connectivity diagnosis")

	// 执行连接诊断
	result, err := h.diagnosisEngine.DiagnoseHost(ctx, targetHost, "connectivity")
	if err != nil {
		h.logger.WithError(err).Error("Connectivity diagnosis failed")
		return h.createErrorResponse(fmt.Sprintf("连接诊断失败: %v", err)), nil
	}

	// 格式化诊断结果
	content := h.formatConnectivityDiagnosisResult(result)

	return &SimplifiedIntentResponse{
		Success: result.Status != "error",
		Content: content,
		Action:  "connectivity_diagnosis_completed",
		Data:    result,
		Metadata: map[string]interface{}{
			"target_host": targetHost,
			"tool_name":   result.ToolName,
			"status":      result.Status,
		},
	}, nil
}

// handleConnectivityTest 处理连接测试
func (h *EnhancedMonitoringOperationsHandler) handleConnectivityTest(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	// 解析目标主机
	targetHost, err := h.parseTargetHost(params)
	if err != nil {
		return h.createErrorResponse(fmt.Sprintf("解析目标主机失败: %v", err)), nil
	}

	// 获取测试类型
	testType := "basic" // 默认基本测试
	if tt, ok := params["test_type"].(string); ok {
		testType = tt
	}

	h.logger.WithFields(logrus.Fields{
		"target_host": targetHost,
		"test_type":   testType,
		"user_id":     userID,
	}).Info("Starting connectivity test")

	// 根据测试类型选择诊断工具
	var diagnosisType string
	switch testType {
	case "ssh":
		diagnosisType = "authentication"
	case "network":
		diagnosisType = "connectivity"
	default:
		diagnosisType = "connectivity"
	}

	// 执行连接测试
	result, err := h.diagnosisEngine.DiagnoseHost(ctx, targetHost, diagnosisType)
	if err != nil {
		h.logger.WithError(err).Error("Connectivity test failed")
		return h.createErrorResponse(fmt.Sprintf("连接测试失败: %v", err)), nil
	}

	// 格式化测试结果
	content := h.formatConnectivityTestResult(result, testType)

	return &SimplifiedIntentResponse{
		Success: result.Status != "error",
		Content: content,
		Action:  "connectivity_test_completed",
		Data:    result,
		Metadata: map[string]interface{}{
			"target_host": targetHost,
			"test_type":   testType,
			"status":      result.Status,
		},
	}, nil
}

// handleTroubleshooting 处理故障排查
func (h *EnhancedMonitoringOperationsHandler) handleTroubleshooting(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	// 解析目标主机
	targetHost, err := h.parseTargetHost(params)
	if err != nil {
		return h.createErrorResponse(fmt.Sprintf("解析目标主机失败: %v", err)), nil
	}

	// 获取问题类型
	issueType := "general" // 默认通用故障排查
	if it, ok := params["issue_type"].(string); ok {
		issueType = it
	}

	h.logger.WithFields(logrus.Fields{
		"target_host": targetHost,
		"issue_type":  issueType,
		"user_id":     userID,
	}).Info("Starting troubleshooting")

	// 执行综合诊断进行故障排查
	result, err := h.diagnosisEngine.DiagnoseHost(ctx, targetHost, "comprehensive")
	if err != nil {
		h.logger.WithError(err).Error("Troubleshooting failed")
		return h.createErrorResponse(fmt.Sprintf("故障排查失败: %v", err)), nil
	}

	// 格式化故障排查结果
	content := h.formatTroubleshootingResult(result, issueType)

	return &SimplifiedIntentResponse{
		Success: true, // 故障排查总是成功，即使发现问题
		Content: content,
		Action:  "troubleshooting_completed",
		Data:    result,
		Metadata: map[string]interface{}{
			"target_host": targetHost,
			"issue_type":  issueType,
			"status":      result.Status,
		},
	}, nil
}

// handleSystemMonitor 处理系统监控
func (h *EnhancedMonitoringOperationsHandler) handleSystemMonitor(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	// 解析目标主机
	targetHost, err := h.parseTargetHost(params)
	if err != nil {
		return h.createErrorResponse(fmt.Sprintf("解析目标主机失败: %v", err)), nil
	}

	// 获取监控指标
	metric := "all" // 默认所有指标
	if m, ok := params["metric"].(string); ok {
		metric = m
	}

	h.logger.WithFields(logrus.Fields{
		"target_host": targetHost,
		"metric":      metric,
		"user_id":     userID,
	}).Info("Starting system monitoring")

	// 目前返回模拟的监控数据
	content := h.generateSystemMonitorContent(targetHost, metric)

	return &SimplifiedIntentResponse{
		Success: true,
		Content: content,
		Action:  "system_monitor_completed",
		Metadata: map[string]interface{}{
			"target_host": targetHost,
			"metric":      metric,
		},
	}, nil
}

// handleLogAnalysis 处理日志分析
func (h *EnhancedMonitoringOperationsHandler) handleLogAnalysis(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "📋 日志分析功能正在开发中，敬请期待",
		Action:  "feature_not_implemented",
	}, nil
}

// handlePerformanceAnalysis 处理性能分析
func (h *EnhancedMonitoringOperationsHandler) handlePerformanceAnalysis(ctx context.Context, params map[string]interface{}, userID int64) (*SimplifiedIntentResponse, error) {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: "📊 性能分析功能正在开发中，敬请期待",
		Action:  "feature_not_implemented",
	}, nil
}

// parseTargetHost 解析目标主机
func (h *EnhancedMonitoringOperationsHandler) parseTargetHost(params map[string]interface{}) (string, error) {
	targetHostParam, ok := params["target_host"]
	if !ok {
		return "", fmt.Errorf("未指定目标主机")
	}

	switch th := targetHostParam.(type) {
	case string:
		return th, nil
	case map[string]interface{}:
		if ip, ok := th["ip"].(string); ok {
			return ip, nil
		}
		if name, ok := th["name"].(string); ok {
			// 通过主机名查找IP（这里简化处理）
			return name, nil
		}
		return "", fmt.Errorf("目标主机参数格式错误")
	default:
		return "", fmt.Errorf("不支持的目标主机参数类型")
	}
}

// createErrorResponse 创建错误响应
func (h *EnhancedMonitoringOperationsHandler) createErrorResponse(message string) *SimplifiedIntentResponse {
	return &SimplifiedIntentResponse{
		Success: false,
		Content: fmt.Sprintf("❌ %s", message),
		Action:  "error",
	}
}

// formatDiagnosisResult 格式化诊断结果
func (h *EnhancedMonitoringOperationsHandler) formatDiagnosisResult(result *DiagnosisResult) string {
	var content strings.Builder

	// 标题和摘要
	statusIcon := h.getStatusIcon(result.Status)
	content.WriteString(fmt.Sprintf("%s **主机诊断报告**\n\n", statusIcon))
	content.WriteString(fmt.Sprintf("🎯 **目标主机**: %s\n", result.Target.Identifier))
	content.WriteString(fmt.Sprintf("📋 **诊断摘要**: %s\n", result.Summary))
	content.WriteString(fmt.Sprintf("⏱️ **诊断耗时**: %s\n\n", result.Duration.String()))

	// 详细检查结果
	if len(result.Details) > 0 {
		content.WriteString("🔍 **详细检查结果**:\n\n")

		// 按类别分组显示
		categories := make(map[string][]DiagnosisDetail)
		for _, detail := range result.Details {
			categories[detail.Category] = append(categories[detail.Category], detail)
		}

		for category, details := range categories {
			content.WriteString(fmt.Sprintf("**%s**:\n", h.getCategoryDisplayName(category)))
			for _, detail := range details {
				icon := h.getDetailStatusIcon(detail.Status)
				content.WriteString(fmt.Sprintf("  %s %s: %s\n", icon, detail.Item, detail.Message))
			}
			content.WriteString("\n")
		}
	}

	// 建议解决方案
	if len(result.Suggestions) > 0 {
		content.WriteString("💡 **建议解决方案**:\n")
		for i, suggestion := range result.Suggestions {
			content.WriteString(fmt.Sprintf("%d. %s\n", i+1, suggestion))
		}
	}

	return content.String()
}

// formatConnectivityDiagnosisResult 格式化连接诊断结果
func (h *EnhancedMonitoringOperationsHandler) formatConnectivityDiagnosisResult(result *DiagnosisResult) string {
	var content strings.Builder

	statusIcon := h.getStatusIcon(result.Status)
	content.WriteString(fmt.Sprintf("%s **连接诊断报告**\n\n", statusIcon))
	content.WriteString(fmt.Sprintf("🎯 **目标主机**: %s\n", result.Target.Identifier))
	content.WriteString(fmt.Sprintf("📋 **诊断结果**: %s\n\n", result.Summary))

	// 连接状态详情
	var networkOK, sshOK bool
	for _, detail := range result.Details {
		if detail.Category == "network" && detail.Status == "pass" {
			networkOK = true
		}
		if detail.Category == "ssh" && detail.Status == "pass" {
			sshOK = true
		}
	}

	content.WriteString("🔗 **连接状态检查**:\n")
	content.WriteString(fmt.Sprintf("  %s 网络连通性: %s\n",
		h.getBoolIcon(networkOK), h.getBoolStatus(networkOK)))
	content.WriteString(fmt.Sprintf("  %s SSH服务: %s\n\n",
		h.getBoolIcon(sshOK), h.getBoolStatus(sshOK)))

	// 问题分析
	if result.Status == "error" {
		content.WriteString("❗ **问题分析**:\n")
		for _, detail := range result.Details {
			if detail.Status == "fail" {
				content.WriteString(fmt.Sprintf("  • %s\n", detail.Message))
			}
		}
		content.WriteString("\n")
	}

	// 建议解决方案
	if len(result.Suggestions) > 0 {
		content.WriteString("💡 **解决建议**:\n")
		for i, suggestion := range result.Suggestions {
			content.WriteString(fmt.Sprintf("%d. %s\n", i+1, suggestion))
		}
	}

	return content.String()
}

// formatConnectivityTestResult 格式化连接测试结果
func (h *EnhancedMonitoringOperationsHandler) formatConnectivityTestResult(result *DiagnosisResult, testType string) string {
	var content strings.Builder

	statusIcon := h.getStatusIcon(result.Status)
	testTypeDisplay := h.getTestTypeDisplay(testType)

	content.WriteString(fmt.Sprintf("%s **%s连接测试**\n\n", statusIcon, testTypeDisplay))
	content.WriteString(fmt.Sprintf("🎯 **目标主机**: %s\n", result.Target.Identifier))
	content.WriteString(fmt.Sprintf("📋 **测试结果**: %s\n", result.Summary))
	content.WriteString(fmt.Sprintf("⏱️ **测试耗时**: %s\n\n", result.Duration.String()))

	// 测试详情
	if len(result.Details) > 0 {
		content.WriteString("📊 **测试详情**:\n")
		for _, detail := range result.Details {
			icon := h.getDetailStatusIcon(detail.Status)
			content.WriteString(fmt.Sprintf("  %s %s: %s\n", icon, detail.Item, detail.Message))
		}
		content.WriteString("\n")
	}

	return content.String()
}

// formatTroubleshootingResult 格式化故障排查结果
func (h *EnhancedMonitoringOperationsHandler) formatTroubleshootingResult(result *DiagnosisResult, issueType string) string {
	var content strings.Builder

	content.WriteString("🔧 **故障排查报告**\n\n")
	content.WriteString(fmt.Sprintf("🎯 **目标主机**: %s\n", result.Target.Identifier))
	content.WriteString(fmt.Sprintf("🔍 **问题类型**: %s\n", h.getIssueTypeDisplay(issueType)))
	content.WriteString(fmt.Sprintf("📋 **排查结果**: %s\n\n", result.Summary))

	// 问题发现
	var issues []DiagnosisDetail
	var warnings []DiagnosisDetail
	var normal []DiagnosisDetail

	for _, detail := range result.Details {
		switch detail.Status {
		case "fail":
			issues = append(issues, detail)
		case "warning":
			warnings = append(warnings, detail)
		case "pass":
			normal = append(normal, detail)
		}
	}

	if len(issues) > 0 {
		content.WriteString("❌ **发现问题**:\n")
		for _, issue := range issues {
			content.WriteString(fmt.Sprintf("  • %s: %s\n", issue.Item, issue.Message))
		}
		content.WriteString("\n")
	}

	if len(warnings) > 0 {
		content.WriteString("⚠️ **警告信息**:\n")
		for _, warning := range warnings {
			content.WriteString(fmt.Sprintf("  • %s: %s\n", warning.Item, warning.Message))
		}
		content.WriteString("\n")
	}

	if len(normal) > 0 {
		content.WriteString("✅ **正常项目**:\n")
		for _, item := range normal {
			content.WriteString(fmt.Sprintf("  • %s\n", item.Item))
		}
		content.WriteString("\n")
	}

	// 解决建议
	if len(result.Suggestions) > 0 {
		content.WriteString("💡 **解决建议**:\n")
		for i, suggestion := range result.Suggestions {
			content.WriteString(fmt.Sprintf("%d. %s\n", i+1, suggestion))
		}
	}

	return content.String()
}

// generateSystemMonitorContent 生成系统监控内容
func (h *EnhancedMonitoringOperationsHandler) generateSystemMonitorContent(targetHost, metric string) string {
	var content strings.Builder

	content.WriteString("📊 **系统监控报告**\n\n")
	content.WriteString(fmt.Sprintf("🎯 **目标主机**: %s\n", targetHost))
	content.WriteString(fmt.Sprintf("📈 **监控指标**: %s\n", h.getMetricDisplayName(metric)))
	content.WriteString(fmt.Sprintf("⏰ **监控时间**: %s\n\n", time.Now().Format("2006-01-02 15:04:05")))

	// 模拟监控数据
	switch metric {
	case "cpu":
		content.WriteString("🖥️ **CPU使用率**:\n")
		content.WriteString("  • 当前使用率: 45.2%\n")
		content.WriteString("  • 平均负载: 1.23\n")
		content.WriteString("  • 状态: 正常\n\n")
	case "memory":
		content.WriteString("💾 **内存使用情况**:\n")
		content.WriteString("  • 总内存: 8.0 GB\n")
		content.WriteString("  • 已使用: 5.2 GB (65%)\n")
		content.WriteString("  • 可用内存: 2.8 GB\n")
		content.WriteString("  • 状态: 正常\n\n")
	case "disk":
		content.WriteString("💿 **磁盘使用情况**:\n")
		content.WriteString("  • 根分区(/): 78% (156GB/200GB)\n")
		content.WriteString("  • 数据分区(/data): 45% (450GB/1TB)\n")
		content.WriteString("  • 状态: 正常\n\n")
	default:
		content.WriteString("📊 **系统概览**:\n")
		content.WriteString("  • CPU使用率: 45.2%\n")
		content.WriteString("  • 内存使用率: 65%\n")
		content.WriteString("  • 磁盘使用率: 78%\n")
		content.WriteString("  • 网络状态: 正常\n")
		content.WriteString("  • 系统负载: 正常\n\n")
	}

	content.WriteString("💡 **提示**: 这是模拟数据，实际监控功能正在开发中")

	return content.String()
}

// 辅助方法：获取状态图标
func (h *EnhancedMonitoringOperationsHandler) getStatusIcon(status string) string {
	switch status {
	case "success":
		return "✅"
	case "warning":
		return "⚠️"
	case "error":
		return "❌"
	default:
		return "ℹ️"
	}
}

// 辅助方法：获取详细状态图标
func (h *EnhancedMonitoringOperationsHandler) getDetailStatusIcon(status string) string {
	switch status {
	case "pass":
		return "✅"
	case "fail":
		return "❌"
	case "warning":
		return "⚠️"
	case "skip":
		return "⏭️"
	default:
		return "ℹ️"
	}
}

// 辅助方法：获取类别显示名称
func (h *EnhancedMonitoringOperationsHandler) getCategoryDisplayName(category string) string {
	switch category {
	case "database":
		return "数据库检查"
	case "network":
		return "网络连通性"
	case "ssh":
		return "SSH服务"
	case "authentication":
		return "认证检查"
	case "configuration":
		return "配置检查"
	case "live_test":
		return "实时测试"
	case "consistency":
		return "状态一致性"
	case "dns":
		return "DNS解析"
	default:
		return category
	}
}

// 辅助方法：获取布尔值图标
func (h *EnhancedMonitoringOperationsHandler) getBoolIcon(value bool) string {
	if value {
		return "✅"
	}
	return "❌"
}

// 辅助方法：获取布尔值状态
func (h *EnhancedMonitoringOperationsHandler) getBoolStatus(value bool) string {
	if value {
		return "正常"
	}
	return "异常"
}

// 辅助方法：获取测试类型显示名称
func (h *EnhancedMonitoringOperationsHandler) getTestTypeDisplay(testType string) string {
	switch testType {
	case "ssh":
		return "SSH"
	case "network":
		return "网络"
	case "basic":
		return "基本"
	default:
		return testType
	}
}

// 辅助方法：获取问题类型显示名称
func (h *EnhancedMonitoringOperationsHandler) getIssueTypeDisplay(issueType string) string {
	switch issueType {
	case "connectivity":
		return "连接问题"
	case "performance":
		return "性能问题"
	case "authentication":
		return "认证问题"
	case "general":
		return "通用故障"
	default:
		return issueType
	}
}

// 辅助方法：获取指标显示名称
func (h *EnhancedMonitoringOperationsHandler) getMetricDisplayName(metric string) string {
	switch metric {
	case "cpu":
		return "CPU使用率"
	case "memory":
		return "内存使用情况"
	case "disk":
		return "磁盘使用情况"
	case "network":
		return "网络状态"
	case "all":
		return "系统概览"
	default:
		return metric
	}
}
