package ai_brain

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// KnowledgeGraphEngine 知识图谱引擎
type KnowledgeGraphEngine struct {
	config *KnowledgeGraphConfig
	logger *logrus.Logger

	// 图数据结构
	nodes map[string]*KnowledgeNode
	edges map[string]*KnowledgeEdge
	mutex sync.RWMutex

	// 索引
	typeIndex     map[string][]*KnowledgeNode
	relationIndex map[string][]*KnowledgeEdge
	
	// 统计信息
	nodeCount int64
	edgeCount int64
	lastUpdate time.Time
}

// KnowledgeGraphConfig 知识图谱配置
type KnowledgeGraphConfig struct {
	Enabled    bool `json:"enabled"`
	MaxNodes   int  `json:"max_nodes"`
	MaxEdges   int  `json:"max_edges"`
	CacheSize  int  `json:"cache_size"`
	PersistenceEnabled bool `json:"persistence_enabled"`
	AutoLearningEnabled bool `json:"auto_learning_enabled"`
}

// KnowledgeNode 知识节点
type KnowledgeNode struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Label       string                 `json:"label"`
	Properties  map[string]interface{} `json:"properties"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Weight      float64                `json:"weight"`
	Confidence  float64                `json:"confidence"`
}

// KnowledgeEdge 知识边
type KnowledgeEdge struct {
	ID         string                 `json:"id"`
	FromNodeID string                 `json:"from_node_id"`
	ToNodeID   string                 `json:"to_node_id"`
	Relation   string                 `json:"relation"`
	Properties map[string]interface{} `json:"properties"`
	Weight     float64                `json:"weight"`
	Confidence float64                `json:"confidence"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

// KnowledgeQuery 知识查询
type KnowledgeQuery struct {
	Type        string                 `json:"type"`
	NodeTypes   []string               `json:"node_types"`
	Relations   []string               `json:"relations"`
	Filters     map[string]interface{} `json:"filters"`
	Limit       int                    `json:"limit"`
	Depth       int                    `json:"depth"`
	IncludeMetadata bool               `json:"include_metadata"`
}

// KnowledgeQueryResult 知识查询结果
type KnowledgeQueryResult struct {
	Nodes       []*KnowledgeNode `json:"nodes"`
	Edges       []*KnowledgeEdge `json:"edges"`
	Paths       []*KnowledgePath `json:"paths"`
	TotalCount  int              `json:"total_count"`
	QueryTime   time.Duration    `json:"query_time"`
	Confidence  float64          `json:"confidence"`
}

// KnowledgePath 知识路径
type KnowledgePath struct {
	Nodes    []*KnowledgeNode `json:"nodes"`
	Edges    []*KnowledgeEdge `json:"edges"`
	Length   int              `json:"length"`
	Weight   float64          `json:"weight"`
	Confidence float64        `json:"confidence"`
}

// NewKnowledgeGraphEngine 创建知识图谱引擎
func NewKnowledgeGraphEngine(config *KnowledgeGraphConfig, logger *logrus.Logger) (*KnowledgeGraphEngine, error) {
	if config == nil {
		config = &KnowledgeGraphConfig{
			Enabled:    true,
			MaxNodes:   100000,
			MaxEdges:   1000000,
			CacheSize:  10000,
			PersistenceEnabled: true,
			AutoLearningEnabled: true,
		}
	}

	engine := &KnowledgeGraphEngine{
		config:        config,
		logger:        logger,
		nodes:         make(map[string]*KnowledgeNode),
		edges:         make(map[string]*KnowledgeEdge),
		typeIndex:     make(map[string][]*KnowledgeNode),
		relationIndex: make(map[string][]*KnowledgeEdge),
		nodeCount:     0,
		edgeCount:     0,
		lastUpdate:    time.Now(),
	}

	// 初始化基础知识图谱
	if err := engine.initializeBaseKnowledge(); err != nil {
		return nil, fmt.Errorf("failed to initialize base knowledge: %w", err)
	}

	logger.WithFields(logrus.Fields{
		"max_nodes": config.MaxNodes,
		"max_edges": config.MaxEdges,
	}).Info("Knowledge Graph Engine initialized successfully")

	return engine, nil
}

// EnhanceContext 增强操作上下文
func (kg *KnowledgeGraphEngine) EnhanceContext(ctx context.Context, operationContext *OperationContext, intent string, parameters map[string]interface{}) (*OperationContext, error) {
	kg.logger.WithFields(logrus.Fields{
		"intent": intent,
		"environment": operationContext.Environment,
		"target_systems": operationContext.TargetSystems,
	}).Debug("Enhancing operation context with knowledge graph")

	// 创建增强的上下文副本
	enhancedContext := &OperationContext{
		Environment:     operationContext.Environment,
		TargetSystems:   operationContext.TargetSystems,
		Dependencies:    operationContext.Dependencies,
		HistoryOps:      operationContext.HistoryOps,
		UserPreferences: operationContext.UserPreferences,
		SecurityLevel:   operationContext.SecurityLevel,
		ComplianceReqs:  operationContext.ComplianceReqs,
	}

	// 1. 查询相关的系统知识
	systemKnowledge, err := kg.querySystemKnowledge(intent, operationContext.TargetSystems)
	if err != nil {
		kg.logger.WithError(err).Warn("Failed to query system knowledge")
	} else {
		// 添加系统依赖关系
		for _, node := range systemKnowledge.Nodes {
			if node.Type == "dependency" {
				if depName, ok := node.Properties["name"].(string); ok {
					enhancedContext.Dependencies = append(enhancedContext.Dependencies, depName)
				}
			}
		}
	}

	// 2. 查询历史操作模式
	historyPatterns, err := kg.queryHistoryPatterns(intent, operationContext.HistoryOps)
	if err != nil {
		kg.logger.WithError(err).Warn("Failed to query history patterns")
	} else {
		// 添加历史操作建议
		for _, pattern := range historyPatterns.Nodes {
			if pattern.Type == "operation_pattern" {
				if suggestion, ok := pattern.Properties["suggestion"].(string); ok {
					if enhancedContext.UserPreferences == nil {
						enhancedContext.UserPreferences = make(map[string]interface{})
					}
					enhancedContext.UserPreferences["history_suggestion"] = suggestion
				}
			}
		}
	}

	// 3. 查询安全和合规要求
	securityKnowledge, err := kg.querySecurityRequirements(intent, operationContext.SecurityLevel)
	if err != nil {
		kg.logger.WithError(err).Warn("Failed to query security requirements")
	} else {
		// 添加合规要求
		for _, node := range securityKnowledge.Nodes {
			if node.Type == "compliance_requirement" {
				if reqName, ok := node.Properties["name"].(string); ok {
					enhancedContext.ComplianceReqs = append(enhancedContext.ComplianceReqs, reqName)
				}
			}
		}
	}

	// 4. 查询最佳实践
	bestPractices, err := kg.queryBestPractices(intent, operationContext.Environment)
	if err != nil {
		kg.logger.WithError(err).Warn("Failed to query best practices")
	} else {
		// 添加最佳实践建议
		if enhancedContext.UserPreferences == nil {
			enhancedContext.UserPreferences = make(map[string]interface{})
		}
		var practices []string
		for _, node := range bestPractices.Nodes {
			if node.Type == "best_practice" {
				if practice, ok := node.Properties["description"].(string); ok {
					practices = append(practices, practice)
				}
			}
		}
		if len(practices) > 0 {
			enhancedContext.UserPreferences["best_practices"] = practices
		}
	}

	kg.logger.WithFields(logrus.Fields{
		"original_dependencies": len(operationContext.Dependencies),
		"enhanced_dependencies": len(enhancedContext.Dependencies),
		"original_compliance": len(operationContext.ComplianceReqs),
		"enhanced_compliance": len(enhancedContext.ComplianceReqs),
	}).Debug("Context enhancement completed")

	return enhancedContext, nil
}

// AddNode 添加知识节点
func (kg *KnowledgeGraphEngine) AddNode(node *KnowledgeNode) error {
	kg.mutex.Lock()
	defer kg.mutex.Unlock()

	if kg.nodeCount >= int64(kg.config.MaxNodes) {
		return fmt.Errorf("maximum node count reached: %d", kg.config.MaxNodes)
	}

	node.CreatedAt = time.Now()
	node.UpdatedAt = time.Now()
	
	kg.nodes[node.ID] = node
	
	// 更新类型索引
	kg.typeIndex[node.Type] = append(kg.typeIndex[node.Type], node)
	
	kg.nodeCount++
	kg.lastUpdate = time.Now()

	kg.logger.WithFields(logrus.Fields{
		"node_id": node.ID,
		"node_type": node.Type,
		"total_nodes": kg.nodeCount,
	}).Debug("Knowledge node added")

	return nil
}

// AddEdge 添加知识边
func (kg *KnowledgeGraphEngine) AddEdge(edge *KnowledgeEdge) error {
	kg.mutex.Lock()
	defer kg.mutex.Unlock()

	if kg.edgeCount >= int64(kg.config.MaxEdges) {
		return fmt.Errorf("maximum edge count reached: %d", kg.config.MaxEdges)
	}

	// 验证节点存在
	if _, exists := kg.nodes[edge.FromNodeID]; !exists {
		return fmt.Errorf("from node not found: %s", edge.FromNodeID)
	}
	if _, exists := kg.nodes[edge.ToNodeID]; !exists {
		return fmt.Errorf("to node not found: %s", edge.ToNodeID)
	}

	edge.CreatedAt = time.Now()
	edge.UpdatedAt = time.Now()
	
	kg.edges[edge.ID] = edge
	
	// 更新关系索引
	kg.relationIndex[edge.Relation] = append(kg.relationIndex[edge.Relation], edge)
	
	kg.edgeCount++
	kg.lastUpdate = time.Now()

	kg.logger.WithFields(logrus.Fields{
		"edge_id": edge.ID,
		"relation": edge.Relation,
		"from_node": edge.FromNodeID,
		"to_node": edge.ToNodeID,
		"total_edges": kg.edgeCount,
	}).Debug("Knowledge edge added")

	return nil
}

// Query 查询知识图谱
func (kg *KnowledgeGraphEngine) Query(query *KnowledgeQuery) (*KnowledgeQueryResult, error) {
	start := time.Now()
	
	kg.mutex.RLock()
	defer kg.mutex.RUnlock()

	kg.logger.WithFields(logrus.Fields{
		"query_type": query.Type,
		"node_types": query.NodeTypes,
		"relations": query.Relations,
		"limit": query.Limit,
		"depth": query.Depth,
	}).Debug("Executing knowledge graph query")

	result := &KnowledgeQueryResult{
		Nodes: []*KnowledgeNode{},
		Edges: []*KnowledgeEdge{},
		Paths: []*KnowledgePath{},
	}

	switch query.Type {
	case "nodes_by_type":
		result.Nodes = kg.queryNodesByType(query.NodeTypes, query.Filters, query.Limit)
	case "edges_by_relation":
		result.Edges = kg.queryEdgesByRelation(query.Relations, query.Filters, query.Limit)
	case "path_finding":
		result.Paths = kg.findPaths(query.Filters, query.Depth, query.Limit)
	case "neighborhood":
		result = kg.queryNeighborhood(query.Filters, query.Depth, query.Limit)
	default:
		return nil, fmt.Errorf("unsupported query type: %s", query.Type)
	}

	result.TotalCount = len(result.Nodes) + len(result.Edges) + len(result.Paths)
	result.QueryTime = time.Since(start)
	result.Confidence = kg.calculateQueryConfidence(result)

	kg.logger.WithFields(logrus.Fields{
		"query_type": query.Type,
		"result_count": result.TotalCount,
		"query_time": result.QueryTime,
		"confidence": result.Confidence,
	}).Debug("Knowledge graph query completed")

	return result, nil
}

// initializeBaseKnowledge 初始化基础知识
func (kg *KnowledgeGraphEngine) initializeBaseKnowledge() error {
	// 添加基础运维概念节点
	baseNodes := []*KnowledgeNode{
		{
			ID: "concept_host", Type: "concept", Label: "主机",
			Properties: map[string]interface{}{
				"description": "计算机主机或服务器",
				"category": "infrastructure",
			},
			Weight: 1.0, Confidence: 1.0,
		},
		{
			ID: "concept_service", Type: "concept", Label: "服务",
			Properties: map[string]interface{}{
				"description": "系统服务或应用服务",
				"category": "application",
			},
			Weight: 1.0, Confidence: 1.0,
		},
		{
			ID: "concept_monitoring", Type: "concept", Label: "监控",
			Properties: map[string]interface{}{
				"description": "系统监控和告警",
				"category": "monitoring",
			},
			Weight: 1.0, Confidence: 1.0,
		},
		{
			ID: "concept_security", Type: "concept", Label: "安全",
			Properties: map[string]interface{}{
				"description": "系统安全和权限管理",
				"category": "security",
			},
			Weight: 1.0, Confidence: 1.0,
		},
	}

	for _, node := range baseNodes {
		if err := kg.AddNode(node); err != nil {
			return fmt.Errorf("failed to add base node %s: %w", node.ID, err)
		}
	}

	// 添加基础关系
	baseEdges := []*KnowledgeEdge{
		{
			ID: "edge_host_service", FromNodeID: "concept_host", ToNodeID: "concept_service",
			Relation: "runs", Properties: map[string]interface{}{"type": "hosting"},
			Weight: 0.9, Confidence: 1.0,
		},
		{
			ID: "edge_service_monitoring", FromNodeID: "concept_service", ToNodeID: "concept_monitoring",
			Relation: "monitored_by", Properties: map[string]interface{}{"type": "observation"},
			Weight: 0.8, Confidence: 1.0,
		},
		{
			ID: "edge_host_security", FromNodeID: "concept_host", ToNodeID: "concept_security",
			Relation: "protected_by", Properties: map[string]interface{}{"type": "protection"},
			Weight: 0.9, Confidence: 1.0,
		},
	}

	for _, edge := range baseEdges {
		if err := kg.AddEdge(edge); err != nil {
			return fmt.Errorf("failed to add base edge %s: %w", edge.ID, err)
		}
	}

	return nil
}

// querySystemKnowledge 查询系统知识
func (kg *KnowledgeGraphEngine) querySystemKnowledge(intent string, targetSystems []string) (*KnowledgeQueryResult, error) {
	query := &KnowledgeQuery{
		Type: "neighborhood",
		Filters: map[string]interface{}{
			"intent": intent,
			"systems": targetSystems,
		},
		Depth: 2,
		Limit: 50,
	}
	return kg.Query(query)
}

// queryHistoryPatterns 查询历史模式
func (kg *KnowledgeGraphEngine) queryHistoryPatterns(intent string, historyOps []string) (*KnowledgeQueryResult, error) {
	query := &KnowledgeQuery{
		Type: "nodes_by_type",
		NodeTypes: []string{"operation_pattern"},
		Filters: map[string]interface{}{
			"intent": intent,
			"history": historyOps,
		},
		Limit: 20,
	}
	return kg.Query(query)
}

// querySecurityRequirements 查询安全要求
func (kg *KnowledgeGraphEngine) querySecurityRequirements(intent string, securityLevel string) (*KnowledgeQueryResult, error) {
	query := &KnowledgeQuery{
		Type: "nodes_by_type",
		NodeTypes: []string{"compliance_requirement", "security_policy"},
		Filters: map[string]interface{}{
			"intent": intent,
			"security_level": securityLevel,
		},
		Limit: 30,
	}
	return kg.Query(query)
}

// queryBestPractices 查询最佳实践
func (kg *KnowledgeGraphEngine) queryBestPractices(intent string, environment string) (*KnowledgeQueryResult, error) {
	query := &KnowledgeQuery{
		Type: "nodes_by_type",
		NodeTypes: []string{"best_practice"},
		Filters: map[string]interface{}{
			"intent": intent,
			"environment": environment,
		},
		Limit: 10,
	}
	return kg.Query(query)
}

// queryNodesByType 按类型查询节点
func (kg *KnowledgeGraphEngine) queryNodesByType(nodeTypes []string, filters map[string]interface{}, limit int) []*KnowledgeNode {
	var result []*KnowledgeNode
	count := 0
	
	for _, nodeType := range nodeTypes {
		if nodes, exists := kg.typeIndex[nodeType]; exists {
			for _, node := range nodes {
				if count >= limit {
					break
				}
				if kg.matchesFilters(node, filters) {
					result = append(result, node)
					count++
				}
			}
		}
	}
	
	return result
}

// queryEdgesByRelation 按关系查询边
func (kg *KnowledgeGraphEngine) queryEdgesByRelation(relations []string, filters map[string]interface{}, limit int) []*KnowledgeEdge {
	var result []*KnowledgeEdge
	count := 0
	
	for _, relation := range relations {
		if edges, exists := kg.relationIndex[relation]; exists {
			for _, edge := range edges {
				if count >= limit {
					break
				}
				if kg.matchesFilters(edge, filters) {
					result = append(result, edge)
					count++
				}
			}
		}
	}
	
	return result
}

// findPaths 查找路径
func (kg *KnowledgeGraphEngine) findPaths(filters map[string]interface{}, maxDepth, limit int) []*KnowledgePath {
	// 简化实现：返回空路径列表
	// 实际实现中应该使用图算法（如Dijkstra、A*等）
	return []*KnowledgePath{}
}

// queryNeighborhood 查询邻域
func (kg *KnowledgeGraphEngine) queryNeighborhood(filters map[string]interface{}, depth, limit int) *KnowledgeQueryResult {
	// 简化实现：返回基础结果
	return &KnowledgeQueryResult{
		Nodes: []*KnowledgeNode{},
		Edges: []*KnowledgeEdge{},
		Paths: []*KnowledgePath{},
	}
}

// matchesFilters 检查是否匹配过滤器
func (kg *KnowledgeGraphEngine) matchesFilters(item interface{}, filters map[string]interface{}) bool {
	// 简化实现：总是返回true
	// 实际实现中应该根据具体的过滤条件进行匹配
	return true
}

// calculateQueryConfidence 计算查询置信度
func (kg *KnowledgeGraphEngine) calculateQueryConfidence(result *KnowledgeQueryResult) float64 {
	if result.TotalCount == 0 {
		return 0.0
	}
	
	totalConfidence := 0.0
	count := 0
	
	for _, node := range result.Nodes {
		totalConfidence += node.Confidence
		count++
	}
	
	for _, edge := range result.Edges {
		totalConfidence += edge.Confidence
		count++
	}
	
	if count == 0 {
		return 0.0
	}
	
	return totalConfidence / float64(count)
}
