package main

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"fmt"
	"log"
	"net"
	"time"

	"golang.org/x/crypto/ssh"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Host 主机模型
type Host struct {
	ID                int64  `json:"id" gorm:"primaryKey;autoIncrement"`
	Name              string `json:"name" gorm:"uniqueIndex;size:100;not null"`
	IPAddress         string `json:"ip_address" gorm:"size:45;not null"`
	Port              int    `json:"port" gorm:"not null;default:22"`
	Username          string `json:"username" gorm:"size:50;not null"`
	PasswordEncrypted string `json:"-" gorm:"type:text"`
	Status            string `json:"status" gorm:"size:20;not null;default:unknown"`
}

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("data/aiops.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 查询**************主机
	var host Host
	if err := db.Where("ip_address = ?", "**************").First(&host).Error; err != nil {
		log.Fatal("Host not found:", err)
	}

	fmt.Printf("🔧 测试SSH连接到 %s\n", host.IPAddress)
	fmt.Printf("主机信息: %s@%s:%d\n", host.Username, host.IPAddress, host.Port)

	// 解密密码
	encryptionKey := "aiops-dev-encryption-key-32byte!"
	password, err := decryptData(host.PasswordEncrypted, encryptionKey)
	if err != nil {
		log.Fatal("Failed to decrypt password:", err)
	}

	fmt.Printf("密码解密成功: %s\n", password)
	fmt.Printf("预期密码: 1qaz#EDC\n")
	if password == "1qaz#EDC" {
		fmt.Printf("✅ 密码匹配！\n")
	} else {
		fmt.Printf("❌ 密码不匹配！\n")
	}

	// 测试网络连通性
	fmt.Println("\n1. 测试网络连通性...")
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:%d", host.IPAddress, host.Port), 5*time.Second)
	if err != nil {
		fmt.Printf("❌ 网络连接失败: %v\n", err)
		fmt.Println("建议:")
		fmt.Println("- 检查IP地址是否正确")
		fmt.Println("- 检查目标主机是否在线")
		fmt.Println("- 检查防火墙设置")
		return
	}
	conn.Close()
	fmt.Printf("✅ 网络连接成功\n")

	// 测试SSH连接
	fmt.Println("\n2. 测试SSH连接...")
	config := &ssh.ClientConfig{
		User: host.Username,
		Auth: []ssh.AuthMethod{
			ssh.Password(password),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         10 * time.Second,
	}

	client, err := ssh.Dial("tcp", fmt.Sprintf("%s:%d", host.IPAddress, host.Port), config)
	if err != nil {
		fmt.Printf("❌ SSH连接失败: %v\n", err)
		fmt.Println("可能的原因:")
		fmt.Println("- 用户名或密码错误")
		fmt.Println("- SSH服务未启动")
		fmt.Println("- SSH配置不允许密码认证")
		fmt.Println("- 用户账户被锁定")
		return
	}
	defer client.Close()

	fmt.Printf("✅ SSH连接成功\n")

	// 测试命令执行
	fmt.Println("\n3. 测试命令执行...")
	session, err := client.NewSession()
	if err != nil {
		fmt.Printf("❌ 创建会话失败: %v\n", err)
		return
	}
	defer session.Close()

	output, err := session.Output("echo 'Hello from SSH'; uname -a")
	if err != nil {
		fmt.Printf("❌ 命令执行失败: %v\n", err)
		return
	}

	fmt.Printf("✅ 命令执行成功\n")
	fmt.Printf("输出:\n%s\n", string(output))

	fmt.Println("\n🎉 SSH连接测试完全成功！")
	fmt.Println("现在可以正常使用AI运维管理平台的SSH功能了。")
}

// decryptData 解密数据
func decryptData(encryptedData, key string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := aead.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := aead.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}
