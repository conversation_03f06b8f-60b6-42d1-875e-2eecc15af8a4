/* ========================================
   智能主题与个性化系统
   支持多主题切换和个性化定制
   ======================================== */

/* 基础主题变量 */
:root {
  /* 主题标识 */
  --theme-name: 'light';
  
  /* 基础颜色 */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: #dbeafe;
  --color-secondary: #64748b;
  --color-success: #059669;
  --color-warning: #d97706;
  --color-error: #dc2626;
  --color-info: #0891b2;

  /* 背景颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-hover: #f3f4f6;
  --bg-active: #e5e7eb;

  /* 文字颜色 */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #94a3b8;
  --text-inverse: #ffffff;

  /* 边框颜色 */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: #2563eb;

  /* 阴影 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);
}

/* 深色主题 */
[data-theme="dark"] {
  --theme-name: 'dark';
  
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-hover: #475569;
  --bg-active: #64748b;

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;

  --border-primary: #334155;
  --border-secondary: #475569;

  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-primary-light: #1e3a8a;
}

/* 蓝色主题 */
[data-theme="blue"] {
  --theme-name: 'blue';
  
  --color-primary: #0ea5e9;
  --color-primary-hover: #0284c7;
  --color-primary-light: #e0f2fe;
  
  --bg-primary: #f0f9ff;
  --bg-secondary: #e0f2fe;
  --bg-tertiary: #bae6fd;
}

/* 绿色主题 */
[data-theme="green"] {
  --theme-name: 'green';
  
  --color-primary: #059669;
  --color-primary-hover: #047857;
  --color-primary-light: #d1fae5;
  
  --bg-primary: #f0fdf4;
  --bg-secondary: #dcfce7;
  --bg-tertiary: #bbf7d0;
}

/* 紫色主题 */
[data-theme="purple"] {
  --theme-name: 'purple';
  
  --color-primary: #7c3aed;
  --color-primary-hover: #6d28d9;
  --color-primary-light: #ede9fe;
  
  --bg-primary: #faf5ff;
  --bg-secondary: #f3e8ff;
  --bg-tertiary: #e9d5ff;
}

/* 橙色主题 */
[data-theme="orange"] {
  --theme-name: 'orange';
  
  --color-primary: #ea580c;
  --color-primary-hover: #dc2626;
  --color-primary-light: #fed7aa;
  
  --bg-primary: #fff7ed;
  --bg-secondary: #ffedd5;
  --bg-tertiary: #fed7aa;
}

/* 高对比度主题 */
[data-theme="high-contrast"] {
  --theme-name: 'high-contrast';
  
  --bg-primary: #ffffff;
  --bg-secondary: #f5f5f5;
  --bg-tertiary: #e0e0e0;
  --bg-hover: #d0d0d0;
  --bg-active: #c0c0c0;

  --text-primary: #000000;
  --text-secondary: #333333;
  --text-tertiary: #666666;

  --border-primary: #000000;
  --border-secondary: #333333;
  --border-focus: #0066cc;

  --color-primary: #0066cc;
  --color-primary-hover: #0052a3;
  --color-success: #008000;
  --color-warning: #ff8c00;
  --color-error: #cc0000;
}

/* 深色高对比度主题 */
[data-theme="dark-high-contrast"] {
  --theme-name: 'dark-high-contrast';
  
  --bg-primary: #000000;
  --bg-secondary: #1a1a1a;
  --bg-tertiary: #333333;
  --bg-hover: #4d4d4d;
  --bg-active: #666666;

  --text-primary: #ffffff;
  --text-secondary: #cccccc;
  --text-tertiary: #999999;

  --border-primary: #ffffff;
  --border-secondary: #cccccc;
  --border-focus: #66b3ff;

  --color-primary: #66b3ff;
  --color-primary-hover: #4da6ff;
  --color-success: #00ff00;
  --color-warning: #ffaa00;
  --color-error: #ff3333;
}

/* 护眼主题 */
[data-theme="eye-care"] {
  --theme-name: 'eye-care';
  
  --bg-primary: #f5f3f0;
  --bg-secondary: #ede8e3;
  --bg-tertiary: #e6ddd6;
  --bg-hover: #dfd4cb;
  --bg-active: #d8cbc0;

  --text-primary: #3c3530;
  --text-secondary: #5d5651;
  --text-tertiary: #7e7772;

  --border-primary: #d0c4b8;
  --border-secondary: #c7b9ab;

  --color-primary: #8b5a3c;
  --color-primary-hover: #7a4d33;
  --color-primary-light: #f0e6d2;
}

/* 主题切换动画 */
.theme-transition {
  transition:
    background-color 0.3s ease,
    color 0.3s ease,
    border-color 0.3s ease,
    box-shadow 0.3s ease;
}

/* 主题切换按钮 */
.theme-toggle-btn,
.settings-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  font-size: 18px;
}

.theme-toggle-btn:hover,
.settings-toggle-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
  transform: scale(1.05);
}

/* 主题选择器 */
.theme-selector {
  position: relative;
  display: inline-block;
}

.theme-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  box-shadow: var(--shadow-lg);
  padding: 8px;
  min-width: 200px;
  z-index: 1000;
  display: none;
}

.theme-dropdown.show {
  display: block;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.theme-option:hover {
  background-color: var(--bg-hover);
}

.theme-option.active {
  background-color: var(--color-primary-light);
  color: var(--color-primary);
}

.theme-preview {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid var(--border-primary);
  position: relative;
  overflow: hidden;
}

.theme-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 50%;
  height: 100%;
  background: var(--preview-bg, #ffffff);
}

.theme-preview::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 50%;
  height: 100%;
  background: var(--preview-accent, #2563eb);
}

/* 主题预览颜色 */
.theme-option[data-theme="light"] .theme-preview {
  --preview-bg: #ffffff;
  --preview-accent: #2563eb;
}

.theme-option[data-theme="dark"] .theme-preview {
  --preview-bg: #0f172a;
  --preview-accent: #3b82f6;
}

.theme-option[data-theme="blue"] .theme-preview {
  --preview-bg: #f0f9ff;
  --preview-accent: #0ea5e9;
}

.theme-option[data-theme="green"] .theme-preview {
  --preview-bg: #f0fdf4;
  --preview-accent: #059669;
}

.theme-option[data-theme="purple"] .theme-preview {
  --preview-bg: #faf5ff;
  --preview-accent: #7c3aed;
}

.theme-option[data-theme="orange"] .theme-preview {
  --preview-bg: #fff7ed;
  --preview-accent: #ea580c;
}

.theme-option[data-theme="high-contrast"] .theme-preview {
  --preview-bg: #ffffff;
  --preview-accent: #000000;
}

.theme-option[data-theme="dark-high-contrast"] .theme-preview {
  --preview-bg: #000000;
  --preview-accent: #ffffff;
}

.theme-option[data-theme="eye-care"] .theme-preview {
  --preview-bg: #f5f3f0;
  --preview-accent: #8b5a3c;
}

/* 个性化设置面板 */
.settings-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: var(--bg-primary);
  border-left: 1px solid var(--border-primary);
  box-shadow: var(--shadow-xl);
  z-index: 10000;
  transition: right 0.3s ease;
  overflow-y: auto;
}

.settings-panel.open {
  right: 0;
}

.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px;
  border-bottom: 1px solid var(--border-primary);
}

.settings-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.settings-close {
  background: none;
  border: none;
  color: var(--text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: background-color 0.2s ease;
}

.settings-close:hover {
  background-color: var(--bg-hover);
}

.settings-content {
  padding: 20px;
}

.settings-section {
  margin-bottom: 24px;
}

.settings-section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 12px;
}

.settings-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 0;
  border-bottom: 1px solid var(--border-primary);
}

.settings-option:last-child {
  border-bottom: none;
}

.settings-option-label {
  font-size: 0.875rem;
  color: var(--text-secondary);
}

.settings-toggle {
  position: relative;
  width: 44px;
  height: 24px;
  background: var(--bg-tertiary);
  border-radius: 12px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.settings-toggle.active {
  background: var(--color-primary);
}

.settings-toggle::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  transition: transform 0.2s ease;
}

.settings-toggle.active::before {
  transform: translateX(20px);
}

/* 字体大小调节 */
.font-size-control {
  display: flex;
  align-items: center;
  gap: 12px;
}

.font-size-slider {
  flex: 1;
  height: 4px;
  background: var(--bg-tertiary);
  border-radius: 2px;
  outline: none;
  cursor: pointer;
}

.font-size-slider::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: var(--color-primary);
  border-radius: 50%;
  cursor: pointer;
}

.font-size-value {
  font-size: 0.75rem;
  color: var(--text-tertiary);
  min-width: 30px;
  text-align: center;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .settings-panel {
    width: 100vw;
    right: -100vw;
  }
  
  .theme-dropdown {
    right: -50px;
    min-width: 180px;
  }
}

/* 自动主题切换 */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme]) {
    --bg-primary: #0f172a;
    --bg-secondary: #1e293b;
    --bg-tertiary: #334155;
    --bg-hover: #475569;
    --bg-active: #64748b;

    --text-primary: #f8fafc;
    --text-secondary: #cbd5e1;
    --text-tertiary: #94a3b8;

    --border-primary: #334155;
    --border-secondary: #475569;

    --color-primary: #3b82f6;
    --color-primary-hover: #2563eb;
    --color-primary-light: #1e3a8a;
  }
}
