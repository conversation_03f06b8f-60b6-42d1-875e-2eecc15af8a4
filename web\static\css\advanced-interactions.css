/* AI运维管理平台 - 高级交互功能 */
/* 拖拽、快捷键、右键菜单、工具提示等高级交互 */

/* ========================================
   工具提示系统
   ======================================== */

.tooltip {
  position: relative;
  display: inline-block;
}

.tooltip::before,
.tooltip::after {
  position: absolute;
  opacity: 0;
  pointer-events: none;
  transition: all var(--transition-base);
  z-index: var(--z-tooltip);
}

.tooltip::before {
  content: attr(data-tooltip);
  background: var(--color-gray-900);
  color: white;
  padding: var(--spacing-2) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  white-space: nowrap;
  box-shadow: var(--shadow-lg);
  transform: translateX(-50%) translateY(-100%);
  top: -var(--spacing-2);
  left: 50%;
}

.tooltip::after {
  content: '';
  border: 4px solid transparent;
  border-top-color: var(--color-gray-900);
  transform: translateX(-50%);
  top: -var(--spacing-1);
  left: 50%;
}

.tooltip:hover::before,
.tooltip:hover::after {
  opacity: 1;
  transform: translateX(-50%) translateY(-100%);
}

/* 工具提示位置变体 */
.tooltip-bottom::before {
  transform: translateX(-50%) translateY(100%);
  top: calc(100% + var(--spacing-2));
}

.tooltip-bottom::after {
  border-top-color: transparent;
  border-bottom-color: var(--color-gray-900);
  top: calc(100% + var(--spacing-1));
}

.tooltip-left::before {
  transform: translateX(-100%) translateY(-50%);
  top: 50%;
  left: -var(--spacing-2);
}

.tooltip-left::after {
  border-top-color: transparent;
  border-left-color: var(--color-gray-900);
  transform: translateY(-50%);
  top: 50%;
  left: -var(--spacing-1);
}

.tooltip-right::before {
  transform: translateX(100%) translateY(-50%);
  top: 50%;
  left: calc(100% + var(--spacing-2));
}

.tooltip-right::after {
  border-top-color: transparent;
  border-right-color: var(--color-gray-900);
  transform: translateY(-50%);
  top: 50%;
  left: calc(100% + var(--spacing-1));
}

/* ========================================
   右键菜单系统
   ======================================== */

.context-menu {
  position: fixed;
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  padding: var(--spacing-2);
  min-width: 200px;
  z-index: var(--z-modal);
  opacity: 0;
  transform: scale(0.95);
  transition: all var(--transition-fast);
  pointer-events: none;
}

.context-menu.show {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}

.context-menu-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-2_5) var(--spacing-3);
  border-radius: var(--radius-md);
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
  cursor: pointer;
  transition: all var(--transition-fast);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.context-menu-item:hover {
  background: var(--color-primary-50);
  color: var(--color-primary-700);
}

.context-menu-item:active {
  background: var(--color-primary-100);
}

.context-menu-item.disabled {
  color: var(--color-gray-400);
  cursor: not-allowed;
}

.context-menu-item.disabled:hover {
  background: none;
  color: var(--color-gray-400);
}

.context-menu-divider {
  height: 1px;
  background: var(--color-gray-200);
  margin: var(--spacing-2) 0;
}

.context-menu-icon {
  width: var(--spacing-4);
  height: var(--spacing-4);
  display: flex;
  align-items: center;
  justify-content: center;
}

.context-menu-shortcut {
  margin-left: auto;
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

/* ========================================
   拖拽排序系统
   ======================================== */

.sortable-container {
  position: relative;
}

.sortable-item {
  position: relative;
  transition: all var(--transition-base);
  cursor: grab;
}

.sortable-item:active {
  cursor: grabbing;
}

.sortable-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg) scale(1.05);
  z-index: var(--z-modal);
  box-shadow: var(--shadow-2xl);
}

.sortable-item.drag-over {
  transform: translateY(-var(--spacing-2));
}

.sortable-placeholder {
  background: var(--color-primary-100);
  border: 2px dashed var(--color-primary-400);
  border-radius: var(--radius-lg);
  opacity: 0.7;
  transition: all var(--transition-base);
}

.drag-handle {
  cursor: grab;
  color: var(--color-gray-400);
  transition: color var(--transition-base);
}

.drag-handle:hover {
  color: var(--color-gray-600);
}

.drag-handle:active {
  cursor: grabbing;
}

/* ========================================
   快捷键提示
   ======================================== */

.keyboard-shortcut {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
}

.key {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: var(--spacing-5);
  height: var(--spacing-5);
  padding: 0 var(--spacing-1);
  background: var(--color-gray-100);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  box-shadow: 0 1px 0 var(--color-gray-300);
}

.key + .key {
  margin-left: var(--spacing-1);
}

/* 快捷键帮助面板 */
.shortcuts-panel {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  padding: var(--spacing-8);
  max-width: 600px;
  max-height: 80vh;
  overflow-y: auto;
  z-index: var(--z-modal);
  opacity: 0;
  transform: translate(-50%, -50%) scale(0.9);
  transition: all var(--transition-base);
  pointer-events: none;
}

.shortcuts-panel.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  pointer-events: auto;
}

.shortcuts-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-6);
  text-align: center;
}

.shortcuts-group {
  margin-bottom: var(--spacing-6);
}

.shortcuts-group-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-3);
  border-bottom: 1px solid var(--color-gray-200);
  padding-bottom: var(--spacing-2);
}

.shortcut-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-2) 0;
}

.shortcut-description {
  font-size: var(--font-size-sm);
  color: var(--color-gray-700);
}

/* ========================================
   模态框增强
   ======================================== */

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(var(--blur-sm));
  z-index: var(--z-modal-backdrop);
  opacity: 0;
  transition: all var(--transition-base);
  pointer-events: none;
}

.modal-overlay.show {
  opacity: 1;
  pointer-events: auto;
}

.modal {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0.9);
  background: white;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  max-width: 90vw;
  max-height: 90vh;
  overflow: hidden;
  z-index: var(--z-modal);
  opacity: 0;
  transition: all var(--transition-base);
  pointer-events: none;
}

.modal.show {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1);
  pointer-events: auto;
}

.modal-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-gray-50) 100%);
}

.modal-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin: 0;
}

.modal-close {
  position: absolute;
  top: var(--spacing-4);
  right: var(--spacing-4);
  width: var(--spacing-8);
  height: var(--spacing-8);
  border: none;
  background: var(--color-gray-100);
  border-radius: var(--radius-full);
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all var(--transition-base);
}

.modal-close:hover {
  background: var(--color-gray-200);
  transform: scale(1.1);
}

.modal-body {
  padding: var(--spacing-6);
  overflow-y: auto;
}

.modal-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
  background: var(--color-gray-50);
  display: flex;
  justify-content: flex-end;
  gap: var(--spacing-3);
}

/* ========================================
   通知系统
   ======================================== */

.notification-container {
  position: fixed;
  top: var(--spacing-6);
  right: var(--spacing-6);
  z-index: var(--z-toast);
  pointer-events: none;
}

.notification {
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--spacing-4);
  margin-bottom: var(--spacing-3);
  min-width: 300px;
  max-width: 400px;
  opacity: 0;
  transform: translateX(100%);
  transition: all var(--transition-base);
  pointer-events: auto;
  position: relative;
}

.notification.show {
  opacity: 1;
  transform: translateX(0);
}

.notification.success {
  border-left: 4px solid var(--color-success-500);
}

.notification.warning {
  border-left: 4px solid var(--color-warning-500);
}

.notification.error {
  border-left: 4px solid var(--color-error-500);
}

.notification.info {
  border-left: 4px solid var(--color-info-500);
}

.notification-content {
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-3);
}

.notification-icon {
  width: var(--spacing-5);
  height: var(--spacing-5);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: var(--radius-full);
  flex-shrink: 0;
}

.notification.success .notification-icon {
  background: var(--color-success-100);
  color: var(--color-success-600);
}

.notification.warning .notification-icon {
  background: var(--color-warning-100);
  color: var(--color-warning-600);
}

.notification.error .notification-icon {
  background: var(--color-error-100);
  color: var(--color-error-600);
}

.notification.info .notification-icon {
  background: var(--color-info-100);
  color: var(--color-info-600);
}

.notification-text {
  flex: 1;
}

.notification-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-1);
}

.notification-message {
  font-size: var(--font-size-xs);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
}

.notification-close {
  position: absolute;
  top: var(--spacing-2);
  right: var(--spacing-2);
  width: var(--spacing-5);
  height: var(--spacing-5);
  border: none;
  background: none;
  color: var(--color-gray-400);
  cursor: pointer;
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--transition-base);
}

.notification-close:hover {
  background: var(--color-gray-100);
  color: var(--color-gray-600);
}
