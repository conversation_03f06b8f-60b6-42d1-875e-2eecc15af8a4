# 安全架构设计

## 🛡️ 安全概述

AI对话运维管理平台采用多层防护的安全架构，确保系统在商用环境下的安全性和可靠性。

### 安全目标
- **机密性**：敏感数据加密保护，防止未授权访问
- **完整性**：数据完整性校验，防止篡改
- **可用性**：系统高可用，防止拒绝服务攻击
- **可审计性**：完整的操作审计和安全事件记录

## 🔐 安全架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                      网络安全层                              │
│  HTTPS/TLS 1.3 │ 防火墙规则 │ IP白名单 │ DDoS防护           │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     应用安全层                               │
│  JWT认证 │ RBAC权限 │ API限流 │ 输入验证 │ XSS/CSRF防护     │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     数据安全层                               │
│  字段加密 │ 传输加密 │ 存储加密 │ 密钥管理 │ 数据脱敏         │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     访问控制层                               │
│  身份认证 │ 角色管理 │ 权限矩阵 │ 会话管理 │ 多因子认证       │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     审计监控层                               │
│  操作日志 │ 安全事件 │ 异常检测 │ 合规检查 │ 威胁情报         │
└─────────────────────────────────────────────────────────────┘
```

## 🔒 身份认证与授权

### JWT认证机制
```go
type JWTManager struct {
    secretKey     []byte
    accessTTL     time.Duration
    refreshTTL    time.Duration
    issuer        string
    blacklist     TokenBlacklist
}

type TokenClaims struct {
    UserID    int64  `json:"user_id"`
    Username  string `json:"username"`
    Role      string `json:"role"`
    SessionID string `json:"session_id"`
    jwt.RegisteredClaims
}

// 双Token机制
type TokenPair struct {
    AccessToken  string `json:"access_token"`
    RefreshToken string `json:"refresh_token"`
    ExpiresIn    int64  `json:"expires_in"`
}
```

**安全特性**：
- **短期访问Token**：15分钟有效期，降低泄露风险
- **长期刷新Token**：7天有效期，支持自动续期
- **Token黑名单**：支持Token主动失效
- **会话绑定**：Token与会话ID绑定，防止重放攻击

### RBAC权限模型
```go
type Role struct {
    Name        string       `json:"name"`
    Description string       `json:"description"`
    Permissions []Permission `json:"permissions"`
}

type Permission struct {
    Resource string `json:"resource"` // hosts, alerts, users, config
    Action   string `json:"action"`   // read, write, delete, execute
}

// 权限矩阵
var RolePermissions = map[string][]Permission{
    "super_admin": {
        {Resource: "*", Action: "*"},
    },
    "admin": {
        {Resource: "users", Action: "read"},
        {Resource: "users", Action: "write"},
        {Resource: "config", Action: "read"},
        {Resource: "config", Action: "write"},
        {Resource: "hosts", Action: "read"},
        {Resource: "alerts", Action: "read"},
        {Resource: "stats", Action: "read"},
    },
    "operator": {
        {Resource: "hosts", Action: "read"},
        {Resource: "hosts", Action: "write"},
        {Resource: "hosts", Action: "execute"},
        {Resource: "alerts", Action: "read"},
        {Resource: "alerts", Action: "write"},
        {Resource: "stats", Action: "read"},
    },
    "viewer": {
        {Resource: "hosts", Action: "read"},
        {Resource: "alerts", Action: "read"},
        {Resource: "stats", Action: "read"},
    },
}
```

### 多因子认证(2FA)
```go
type TwoFactorAuth struct {
    Secret    string    `json:"secret"`
    QRCode    string    `json:"qr_code"`
    BackupCodes []string `json:"backup_codes"`
    Enabled   bool      `json:"enabled"`
}

// TOTP验证
func (tfa *TwoFactorAuth) VerifyTOTP(code string) bool {
    return totp.Validate(code, tfa.Secret)
}
```

## 🔐 数据加密策略

### 密钥管理架构
```go
type KeyManager struct {
    masterKey    []byte           // 主密钥(KEK)
    dataKeys     map[string][]byte // 数据加密密钥(DEK)
    keyRotation  *KeyRotationManager
    hsm          HSMInterface     // 硬件安全模块
}

type EncryptionConfig struct {
    Algorithm    string `json:"algorithm"`    // AES-256-GCM
    KeySize      int    `json:"key_size"`     // 256
    IVSize       int    `json:"iv_size"`      // 12
    TagSize      int    `json:"tag_size"`     // 16
    Iterations   int    `json:"iterations"`   // PBKDF2迭代次数
}
```

### 字段级加密
```go
type EncryptedField struct {
    Version    int    `json:"version"`
    Algorithm  string `json:"algorithm"`
    KeyID      string `json:"key_id"`
    IV         []byte `json:"iv"`
    Ciphertext []byte `json:"ciphertext"`
    Tag        []byte `json:"tag"`
    Timestamp  int64  `json:"timestamp"`
}

// 敏感字段加密
type Host struct {
    ID                int    `json:"id"`
    Name              string `json:"name"`
    IPAddress         string `json:"ip_address"`
    Username          string `json:"username"`
    PasswordEncrypted string `json:"-"` // 加密存储，不返回给前端
    SSHKeyPath        string `json:"ssh_key_path"`
    // ... 其他字段
}
```

### 传输加密
- **TLS 1.3**：所有HTTP通信强制HTTPS
- **证书管理**：自动证书更新和轮换
- **HSTS**：强制安全传输
- **证书透明度**：CT日志监控

## 🛡️ 应用安全防护

### 输入验证与过滤
```go
type Validator struct {
    rules map[string][]ValidationRule
}

type ValidationRule interface {
    Validate(value interface{}) error
}

// SQL注入防护
func (v *Validator) ValidateSQL(input string) error {
    // 检测SQL注入模式
    sqlPatterns := []string{
        `(?i)(union|select|insert|update|delete|drop|create|alter)`,
        `(?i)(script|javascript|vbscript)`,
        `(?i)(onload|onerror|onclick)`,
    }
    
    for _, pattern := range sqlPatterns {
        if matched, _ := regexp.MatchString(pattern, input); matched {
            return errors.New("potential SQL injection detected")
        }
    }
    return nil
}

// XSS防护
func (v *Validator) SanitizeHTML(input string) string {
    p := bluemonday.UGCPolicy()
    return p.Sanitize(input)
}
```

### API安全防护
```go
type SecurityMiddleware struct {
    rateLimiter   *RateLimiter
    replayGuard   *ReplayGuard
    ipWhitelist   *IPWhitelist
    csrfProtector *CSRFProtector
}

// 防重放攻击
type ReplayGuard struct {
    cache      cache.Cache
    timeWindow time.Duration
    nonceSize  int
}

func (rg *ReplayGuard) ValidateRequest(r *http.Request) error {
    timestamp := r.Header.Get("X-Timestamp")
    nonce := r.Header.Get("X-Nonce")
    signature := r.Header.Get("X-Signature")
    
    // 验证时间戳
    reqTime, err := time.Parse(time.RFC3339, timestamp)
    if err != nil || time.Since(reqTime) > rg.timeWindow {
        return errors.New("invalid or expired timestamp")
    }
    
    // 验证nonce唯一性
    nonceKey := fmt.Sprintf("nonce:%s", nonce)
    if rg.cache.Exists(nonceKey) {
        return errors.New("nonce already used")
    }
    
    // 验证签名
    if !rg.verifySignature(r, signature) {
        return errors.New("invalid signature")
    }
    
    // 缓存nonce
    rg.cache.Set(nonceKey, true, rg.timeWindow)
    return nil
}

// 智能限流
type RateLimiter struct {
    rules    []RateLimitRule
    limiters map[string]*rate.Limiter
    mutex    sync.RWMutex
}

type RateLimitRule struct {
    Pattern     string     `json:"pattern"`
    Rate        rate.Limit `json:"rate"`
    Burst       int        `json:"burst"`
    UserBased   bool       `json:"user_based"`
    IPBased     bool       `json:"ip_based"`
    Whitelist   []string   `json:"whitelist"`
}
```

## 📊 安全审计与监控

### 审计日志系统
```go
type AuditLogger struct {
    storage   AuditStorage
    encryptor *EncryptionManager
    signer    *DigitalSigner
}

type AuditEvent struct {
    ID          string                 `json:"id"`
    Timestamp   time.Time             `json:"timestamp"`
    EventType   string                `json:"event_type"`
    UserID      string                `json:"user_id"`
    Username    string                `json:"username"`
    Action      string                `json:"action"`
    Resource    string                `json:"resource"`
    ResourceID  string                `json:"resource_id"`
    Result      string                `json:"result"`
    IPAddress   string                `json:"ip_address"`
    UserAgent   string                `json:"user_agent"`
    SessionID   string                `json:"session_id"`
    Details     map[string]interface{} `json:"details"`
    RiskLevel   string                `json:"risk_level"`
    Signature   string                `json:"signature"`
}

// 审计事件类型
const (
    EventTypeLogin          = "user_login"
    EventTypeLogout         = "user_logout"
    EventTypePasswordChange = "password_change"
    EventTypeHostAccess     = "host_access"
    EventTypeCommandExec    = "command_execution"
    EventTypeConfigChange   = "config_change"
    EventTypeDataAccess     = "data_access"
    EventTypeSecurityAlert  = "security_alert"
)
```

### 安全事件检测
```go
type SecurityDetector struct {
    rules       []SecurityRule
    ml          *MLDetector
    threatIntel *ThreatIntelligence
}

type SecurityRule struct {
    Name        string        `json:"name"`
    Pattern     string        `json:"pattern"`
    Threshold   int           `json:"threshold"`
    TimeWindow  time.Duration `json:"time_window"`
    Severity    string        `json:"severity"`
    Action      string        `json:"action"`
}

// 预定义安全规则
var DefaultSecurityRules = []SecurityRule{
    {
        Name:       "多次登录失败",
        Pattern:    "event_type:user_login AND result:failed",
        Threshold:  5,
        TimeWindow: 15 * time.Minute,
        Severity:   "medium",
        Action:     "lock_account",
    },
    {
        Name:       "异常IP登录",
        Pattern:    "event_type:user_login AND result:success",
        Threshold:  1,
        TimeWindow: 1 * time.Hour,
        Severity:   "high",
        Action:     "require_2fa",
    },
    {
        Name:       "权限提升尝试",
        Pattern:    "action:privilege_escalation",
        Threshold:  1,
        TimeWindow: 1 * time.Minute,
        Severity:   "critical",
        Action:     "immediate_alert",
    },
}

// 机器学习异常检测
type MLDetector struct {
    model     MLModel
    features  []string
    threshold float64
}

type AnomalyResult struct {
    Score       float64                `json:"score"`
    Anomalous   bool                   `json:"anomalous"`
    Features    map[string]float64     `json:"features"`
    Explanation string                 `json:"explanation"`
}
```

## 🔍 威胁情报集成
```go
type ThreatIntelligence struct {
    providers []ThreatProvider
    cache     *ThreatCache
    updater   *ThreatUpdater
}

type ThreatProvider interface {
    GetThreatData(ctx context.Context) (*ThreatData, error)
    CheckIP(ip string) (*ThreatInfo, error)
    CheckDomain(domain string) (*ThreatInfo, error)
}

type ThreatInfo struct {
    Malicious   bool     `json:"malicious"`
    Categories  []string `json:"categories"`
    Confidence  float64  `json:"confidence"`
    LastSeen    time.Time `json:"last_seen"`
    Source      string   `json:"source"`
}
```

## 🛠️ 安全配置管理

### 安全配置项
```yaml
security:
  authentication:
    jwt:
      access_token_ttl: "15m"
      refresh_token_ttl: "7d"
      secret_rotation_interval: "30d"
    session:
      timeout: "24h"
      max_concurrent: 5
    password:
      min_length: 8
      require_uppercase: true
      require_lowercase: true
      require_numbers: true
      require_symbols: true
      history_count: 5
      max_age: "90d"
    
  authorization:
    rbac:
      strict_mode: true
      default_role: "viewer"
    
  encryption:
    algorithm: "AES-256-GCM"
    key_rotation_interval: "90d"
    backup_encryption: true
    
  network:
    tls:
      min_version: "1.3"
      cipher_suites: ["TLS_AES_256_GCM_SHA384"]
    rate_limiting:
      global_rate: "1000/min"
      per_user_rate: "100/min"
      per_ip_rate: "200/min"
    
  monitoring:
    audit_log:
      retention_days: 365
      encryption: true
      integrity_check: true
    security_events:
      real_time_alerts: true
      ml_detection: true
      threat_intel: true
```

## 🚨 安全事件响应

### 事件响应流程
1. **检测**：实时监控和异常检测
2. **分析**：事件分类和风险评估
3. **响应**：自动化响应和人工干预
4. **恢复**：系统恢复和加固
5. **总结**：事后分析和改进

### 自动化响应
```go
type IncidentResponse struct {
    detector  *SecurityDetector
    responder *AutoResponder
    notifier  *NotificationManager
}

type ResponseAction interface {
    Execute(ctx context.Context, event *SecurityEvent) error
}

// 自动响应动作
type LockAccountAction struct{}
func (a *LockAccountAction) Execute(ctx context.Context, event *SecurityEvent) error {
    // 锁定用户账户
    return nil
}

type BlockIPAction struct{}
func (a *BlockIPAction) Execute(ctx context.Context, event *SecurityEvent) error {
    // 封禁IP地址
    return nil
}

type RequireMFAAction struct{}
func (a *RequireMFAAction) Execute(ctx context.Context, event *SecurityEvent) error {
    // 强制多因子认证
    return nil
}
```

## 📋 安全合规

### 合规要求
- **数据保护**：符合GDPR、CCPA等数据保护法规
- **访问控制**：符合SOX、HIPAA等访问控制要求
- **审计日志**：符合PCI DSS等审计要求
- **加密标准**：符合FIPS 140-2等加密标准

### 安全评估
- **漏洞扫描**：定期自动化漏洞扫描
- **渗透测试**：定期第三方渗透测试
- **代码审计**：静态和动态代码安全分析
- **配置检查**：安全配置基线检查
