/* ========================================
   智能主题管理器
   处理主题切换和个性化设置
   ======================================== */

class ThemeManager {
    constructor() {
        this.themes = {
            light: { name: '浅色主题', icon: '☀️' },
            dark: { name: '深色主题', icon: '🌙' },
            blue: { name: '海洋蓝', icon: '🌊' },
            green: { name: '森林绿', icon: '🌲' },
            purple: { name: '优雅紫', icon: '🔮' },
            orange: { name: '活力橙', icon: '🔥' },
            'high-contrast': { name: '高对比度', icon: '⚫' },
            'dark-high-contrast': { name: '深色高对比度', icon: '⚪' },
            'eye-care': { name: '护眼模式', icon: '👁️' }
        };
        
        this.settings = {
            theme: 'auto',
            fontSize: 16,
            autoTheme: true,
            animations: true,
            compactMode: false,
            highContrast: false,
            reducedMotion: false
        };
        
        this.currentTheme = 'light';
        this.systemTheme = 'light';
        
        this.init();
    }
    
    init() {
        this.loadSettings();
        this.detectSystemTheme();
        this.applyTheme();
        this.createThemeSelector();

        // 确保DOM完全就绪后再创建设置面板
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                this.createSettingsPanel();
                this.bindEvents();
            });
        } else {
            // DOM已就绪，延迟一点确保所有元素都渲染完成
            setTimeout(() => {
                this.createSettingsPanel();
                this.bindEvents();
            }, 50);
        }

        console.log('🎨 主题管理器已初始化');
    }
    
    // 加载用户设置
    loadSettings() {
        const saved = localStorage.getItem('themeSettings');
        if (saved) {
            this.settings = { ...this.settings, ...JSON.parse(saved) };
        }
    }
    
    // 保存用户设置
    saveSettings() {
        localStorage.setItem('themeSettings', JSON.stringify(this.settings));
    }
    
    // 检测系统主题
    detectSystemTheme() {
        const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
        this.systemTheme = mediaQuery.matches ? 'dark' : 'light';
        
        // 监听系统主题变化
        mediaQuery.addListener((e) => {
            this.systemTheme = e.matches ? 'dark' : 'light';
            if (this.settings.autoTheme) {
                this.applyTheme();
            }
        });
    }
    
    // 应用主题
    applyTheme(themeName = null) {
        let targetTheme = themeName || this.settings.theme;
        
        // 自动主题模式
        if (targetTheme === 'auto') {
            targetTheme = this.systemTheme;
        }
        
        // 高对比度覆盖
        if (this.settings.highContrast) {
            targetTheme = targetTheme === 'dark' ? 'dark-high-contrast' : 'high-contrast';
        }
        
        this.currentTheme = targetTheme;
        
        // 应用主题到文档
        document.documentElement.setAttribute('data-theme', targetTheme);
        
        // 添加过渡效果
        if (!document.body.classList.contains('theme-transition')) {
            document.body.classList.add('theme-transition');
        }
        
        // 应用其他设置
        this.applyFontSize();
        this.applyAnimationSettings();
        this.applyCompactMode();
        
        // 触发主题变化事件
        this.triggerThemeChange(targetTheme);
        
        console.log(`🎨 主题已切换到: ${this.themes[targetTheme]?.name || targetTheme}`);
    }
    
    // 应用字体大小
    applyFontSize() {
        document.documentElement.style.fontSize = this.settings.fontSize + 'px';
    }
    
    // 应用动画设置
    applyAnimationSettings() {
        if (this.settings.reducedMotion || !this.settings.animations) {
            document.documentElement.style.setProperty('--duration-fast', '0.01ms');
            document.documentElement.style.setProperty('--duration-normal', '0.01ms');
            document.documentElement.style.setProperty('--duration-slow', '0.01ms');
        } else {
            document.documentElement.style.removeProperty('--duration-fast');
            document.documentElement.style.removeProperty('--duration-normal');
            document.documentElement.style.removeProperty('--duration-slow');
        }
    }
    
    // 应用紧凑模式
    applyCompactMode() {
        if (this.settings.compactMode) {
            document.body.classList.add('compact-mode');
        } else {
            document.body.classList.remove('compact-mode');
        }
    }
    
    // 创建主题选择器
    createThemeSelector() {
        const navbar = document.querySelector('.navbar-right');
        if (!navbar) return;
        
        const themeButton = document.createElement('button');
        themeButton.className = 'theme-toggle-btn';
        themeButton.innerHTML = '<i class="bi bi-palette"></i>';
        themeButton.title = '主题设置';
        
        const dropdown = document.createElement('div');
        dropdown.className = 'theme-dropdown';
        dropdown.innerHTML = this.createThemeOptions();
        
        const container = document.createElement('div');
        container.className = 'theme-selector';
        container.appendChild(themeButton);
        container.appendChild(dropdown);
        
        navbar.insertBefore(container, navbar.firstChild);
        
        // 绑定事件
        themeButton.addEventListener('click', (e) => {
            e.stopPropagation();
            dropdown.classList.toggle('show');
        });
        
        // 点击外部关闭
        document.addEventListener('click', () => {
            dropdown.classList.remove('show');
        });
    }
    
    createThemeOptions() {
        return Object.keys(this.themes).map(themeKey => {
            const theme = this.themes[themeKey];
            const isActive = this.currentTheme === themeKey || 
                           (this.settings.theme === 'auto' && this.systemTheme === themeKey);
            
            return `
                <div class="theme-option ${isActive ? 'active' : ''}" 
                     data-theme="${themeKey}" 
                     onclick="window.themeManager.setTheme('${themeKey}')">
                    <div class="theme-preview"></div>
                    <span>${theme.icon} ${theme.name}</span>
                </div>
            `;
        }).join('') + `
            <div class="theme-option ${this.settings.theme === 'auto' ? 'active' : ''}" 
                 data-theme="auto" 
                 onclick="window.themeManager.setTheme('auto')">
                <div class="theme-preview"></div>
                <span>🤖 自动切换</span>
            </div>
        `;
    }
    
    // 创建设置面板
    createSettingsPanel() {
        try {
            // 检查是否已存在设置面板
            const existingPanel = document.getElementById('settings-panel');
            if (existingPanel) {
                console.log('⚠️ 设置面板已存在，跳过创建');
                return;
            }

            const panel = document.createElement('div');
            panel.className = 'settings-panel';
            panel.id = 'settings-panel';
            panel.innerHTML = this.createSettingsPanelHTML();

            document.body.appendChild(panel);

            // 添加设置按钮到导航栏
            const navbar = document.querySelector('.navbar-right');
            if (navbar) {
                // 检查是否已存在设置按钮
                const existingBtn = navbar.querySelector('.settings-toggle-btn');
                if (!existingBtn) {
                    const settingsBtn = document.createElement('button');
                    settingsBtn.className = 'settings-toggle-btn';
                    settingsBtn.innerHTML = '<i class="bi bi-gear"></i>';
                    settingsBtn.title = '个性化设置';
                    settingsBtn.onclick = () => this.toggleSettingsPanel();

                    navbar.appendChild(settingsBtn);
                }
            } else {
                console.warn('⚠️ 导航栏未找到，设置按钮将无法添加');
            }

            // 延迟绑定事件，确保DOM完全渲染
            setTimeout(() => {
                this.bindSettingsEvents();
            }, 100);

            console.log('✅ 设置面板创建成功');

        } catch (error) {
            console.error('❌ 创建设置面板失败:', error);
        }
    }
    
    createSettingsPanelHTML() {
        return `
            <div class="settings-header">
                <h3 class="settings-title">个性化设置</h3>
                <button class="settings-close" onclick="window.themeManager.closeSettingsPanel()">
                    <i class="bi bi-x-lg"></i>
                </button>
            </div>
            <div class="settings-content">
                <div class="settings-section">
                    <h4 class="settings-section-title">外观设置</h4>
                    
                    <div class="settings-option">
                        <span class="settings-option-label">自动主题切换</span>
                        <div class="settings-toggle ${this.settings.autoTheme ? 'active' : ''}" 
                             data-setting="autoTheme">
                        </div>
                    </div>
                    
                    <div class="settings-option">
                        <span class="settings-option-label">高对比度模式</span>
                        <div class="settings-toggle ${this.settings.highContrast ? 'active' : ''}" 
                             data-setting="highContrast">
                        </div>
                    </div>
                    
                    <div class="settings-option">
                        <span class="settings-option-label">紧凑模式</span>
                        <div class="settings-toggle ${this.settings.compactMode ? 'active' : ''}" 
                             data-setting="compactMode">
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4 class="settings-section-title">字体设置</h4>
                    
                    <div class="settings-option">
                        <span class="settings-option-label">字体大小</span>
                        <div class="font-size-control">
                            <input type="range" class="font-size-slider" 
                                   min="12" max="20" value="${this.settings.fontSize}"
                                   data-setting="fontSize">
                            <span class="font-size-value">${this.settings.fontSize}px</span>
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4 class="settings-section-title">动画设置</h4>
                    
                    <div class="settings-option">
                        <span class="settings-option-label">启用动画效果</span>
                        <div class="settings-toggle ${this.settings.animations ? 'active' : ''}" 
                             data-setting="animations">
                        </div>
                    </div>
                    
                    <div class="settings-option">
                        <span class="settings-option-label">减少动画</span>
                        <div class="settings-toggle ${this.settings.reducedMotion ? 'active' : ''}" 
                             data-setting="reducedMotion">
                        </div>
                    </div>
                </div>
                
                <div class="settings-section">
                    <h4 class="settings-section-title">数据管理</h4>
                    
                    <button class="btn btn-outline" onclick="window.themeManager.resetSettings()">
                        重置所有设置
                    </button>
                    
                    <button class="btn btn-outline" onclick="window.themeManager.exportSettings()">
                        导出设置
                    </button>
                    
                    <input type="file" id="import-settings" accept=".json" style="display: none;"
                           onchange="window.themeManager.importSettings(event)">
                    <button class="btn btn-outline" onclick="document.getElementById('import-settings').click()">
                        导入设置
                    </button>
                </div>
            </div>
        `;
    }
    
    // 绑定设置面板事件
    bindSettingsEvents() {
        const panel = document.getElementById('settings-panel');

        if (!panel) {
            console.error('❌ 设置面板未找到，延迟重试绑定事件');
            setTimeout(() => this.bindSettingsEvents(), 100);
            return;
        }

        // 切换开关
        panel.querySelectorAll('.settings-toggle').forEach(toggle => {
            toggle.addEventListener('click', () => {
                const setting = toggle.dataset.setting;
                this.settings[setting] = !this.settings[setting];
                toggle.classList.toggle('active');

                this.saveSettings();
                this.applyTheme();
            });
        });

        // 字体大小滑块 - 添加防御性检查
        const fontSlider = panel.querySelector('.font-size-slider');
        const fontValue = panel.querySelector('.font-size-value');

        if (fontSlider && fontValue) {
            fontSlider.addEventListener('input', (e) => {
                this.settings.fontSize = parseInt(e.target.value);
                fontValue.textContent = this.settings.fontSize + 'px';
                this.applyFontSize();
                this.saveSettings();
            });
        } else {
            console.error('❌ 字体滑块元素未找到:', {
                fontSlider: !!fontSlider,
                fontValue: !!fontValue
            });

            // 延迟重试
            setTimeout(() => {
                console.log('🔄 重试绑定字体滑块事件');
                this.bindFontSliderEvents();
            }, 200);
        }
    }

    // 单独绑定字体滑块事件的方法
    bindFontSliderEvents() {
        const panel = document.getElementById('settings-panel');
        if (!panel) return;

        const fontSlider = panel.querySelector('.font-size-slider');
        const fontValue = panel.querySelector('.font-size-value');

        if (fontSlider && fontValue) {
            fontSlider.addEventListener('input', (e) => {
                this.settings.fontSize = parseInt(e.target.value);
                fontValue.textContent = this.settings.fontSize + 'px';
                this.applyFontSize();
                this.saveSettings();
            });
            console.log('✅ 字体滑块事件绑定成功');
        } else {
            console.error('❌ 字体滑块元素仍未找到，跳过绑定');
        }
    }
    
    // 设置主题
    setTheme(themeName) {
        this.settings.theme = themeName;
        this.saveSettings();
        this.applyTheme();
        
        // 更新主题选择器
        this.updateThemeSelector();
    }
    
    // 更新主题选择器
    updateThemeSelector() {
        const dropdown = document.querySelector('.theme-dropdown');
        if (dropdown) {
            dropdown.innerHTML = this.createThemeOptions();
        }
    }
    
    // 切换设置面板
    toggleSettingsPanel() {
        const panel = document.getElementById('settings-panel');
        panel.classList.toggle('open');
    }
    
    closeSettingsPanel() {
        const panel = document.getElementById('settings-panel');
        panel.classList.remove('open');
    }
    
    // 重置设置
    resetSettings() {
        if (confirm('确定要重置所有个性化设置吗？')) {
            localStorage.removeItem('themeSettings');
            this.settings = {
                theme: 'auto',
                fontSize: 16,
                autoTheme: true,
                animations: true,
                compactMode: false,
                highContrast: false,
                reducedMotion: false
            };
            this.applyTheme();
            this.closeSettingsPanel();
            
            // 重新创建设置面板
            document.getElementById('settings-panel').remove();
            this.createSettingsPanel();
            
            if (window.animationController) {
                window.animationController.showNotification('设置已重置', 'success');
            }
        }
    }
    
    // 导出设置
    exportSettings() {
        const data = {
            version: '1.0',
            timestamp: new Date().toISOString(),
            settings: this.settings
        };
        
        const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `ai-ops-settings-${new Date().toISOString().split('T')[0]}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        
        if (window.animationController) {
            window.animationController.showNotification('设置已导出', 'success');
        }
    }
    
    // 导入设置
    importSettings(event) {
        const file = event.target.files[0];
        if (!file) return;
        
        const reader = new FileReader();
        reader.onload = (e) => {
            try {
                const data = JSON.parse(e.target.result);
                if (data.settings) {
                    this.settings = { ...this.settings, ...data.settings };
                    this.saveSettings();
                    this.applyTheme();
                    
                    // 重新创建设置面板
                    document.getElementById('settings-panel').remove();
                    this.createSettingsPanel();
                    
                    if (window.animationController) {
                        window.animationController.showNotification('设置已导入', 'success');
                    }
                }
            } catch (error) {
                if (window.animationController) {
                    window.animationController.showNotification('导入失败：文件格式错误', 'error');
                }
            }
        };
        reader.readAsText(file);
        
        // 清空文件输入
        event.target.value = '';
    }
    
    // 绑定事件
    bindEvents() {
        // 监听系统偏好变化
        const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
        reducedMotionQuery.addListener((e) => {
            if (e.matches && !this.settings.reducedMotion) {
                this.settings.reducedMotion = true;
                this.saveSettings();
                this.applyAnimationSettings();
            }
        });
        
        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            // 在移动设备上自动关闭设置面板
            if (window.innerWidth <= 768) {
                this.closeSettingsPanel();
            }
        });
    }
    
    // 触发主题变化事件
    triggerThemeChange(themeName) {
        const event = new CustomEvent('themeChanged', {
            detail: { 
                theme: themeName, 
                settings: this.settings 
            }
        });
        document.dispatchEvent(event);
    }
    
    // 获取当前主题信息
    getCurrentTheme() {
        return {
            name: this.currentTheme,
            displayName: this.themes[this.currentTheme]?.name || this.currentTheme,
            settings: this.settings
        };
    }
}

// 全局主题管理器实例
window.themeManager = new ThemeManager();
