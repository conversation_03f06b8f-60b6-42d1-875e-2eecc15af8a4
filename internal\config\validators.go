package config

import (
	"fmt"
	"net"
	"net/url"
	"os"
	"path/filepath"
	"time"
)

// BasicConfigValidator 基础配置验证器
type BasicConfigValidator struct{}

func (v *BasicConfigValidator) Validate(config *Config) error {
	// 验证应用配置
	if err := v.validateApp(&config.App); err != nil {
		return fmt.Errorf("app config validation failed: %w", err)
	}

	// 验证数据库配置
	if err := v.validateDatabase(&config.Database); err != nil {
		return fmt.Errorf("database config validation failed: %w", err)
	}

	// 验证JWT配置
	if err := v.validateJWT(&config.JWT); err != nil {
		return fmt.Errorf("JWT config validation failed: %w", err)
	}

	// 验证安全配置
	if err := v.validateSecurity(&config.Security); err != nil {
		return fmt.Errorf("security config validation failed: %w", err)
	}

	// 验证日志配置
	if err := v.validateLog(&config.Log); err != nil {
		return fmt.Errorf("log config validation failed: %w", err)
	}

	return nil
}

func (v *BasicConfigValidator) validateApp(app *AppConfig) error {
	if app.Name == "" {
		return fmt.Errorf("app name cannot be empty")
	}

	if app.Port <= 0 || app.Port > 65535 {
		return fmt.Errorf("invalid port number: %d", app.Port)
	}

	validEnvs := []string{"development", "testing", "staging", "production"}
	if !contains(validEnvs, app.Env) {
		return fmt.Errorf("invalid environment: %s, must be one of %v", app.Env, validEnvs)
	}

	return nil
}

func (v *BasicConfigValidator) validateDatabase(db *DatabaseConfig) error {
	if db.Path == "" {
		return fmt.Errorf("database path cannot be empty")
	}

	// 检查数据库目录是否可写
	dir := filepath.Dir(db.Path)
	if err := os.MkdirAll(dir, 0755); err != nil {
		return fmt.Errorf("cannot create database directory %s: %w", dir, err)
	}

	if db.MaxOpenConns <= 0 {
		return fmt.Errorf("max_open_conns must be positive")
	}

	if db.MaxIdleConns <= 0 {
		return fmt.Errorf("max_idle_conns must be positive")
	}

	if db.MaxIdleConns > db.MaxOpenConns {
		return fmt.Errorf("max_idle_conns cannot be greater than max_open_conns")
	}

	return nil
}

func (v *BasicConfigValidator) validateJWT(jwt *JWTConfig) error {
	if jwt.Secret == "" {
		return fmt.Errorf("JWT secret cannot be empty")
	}

	if len(jwt.Secret) < 32 {
		return fmt.Errorf("JWT secret must be at least 32 characters long")
	}

	if jwt.AccessTokenTTL <= 0 {
		return fmt.Errorf("access token TTL must be positive")
	}

	if jwt.RefreshTokenTTL <= 0 {
		return fmt.Errorf("refresh token TTL must be positive")
	}

	if jwt.AccessTokenTTL >= jwt.RefreshTokenTTL {
		return fmt.Errorf("access token TTL must be less than refresh token TTL")
	}

	if jwt.MaxConcurrentSessions <= 0 {
		return fmt.Errorf("max concurrent sessions must be positive")
	}

	return nil
}

func (v *BasicConfigValidator) validateSecurity(security *SecurityConfig) error {
	if security.PasswordHashCost < 4 || security.PasswordHashCost > 31 {
		return fmt.Errorf("password hash cost must be between 4 and 31")
	}

	if security.SessionTimeout <= 0 {
		return fmt.Errorf("session timeout must be positive")
	}

	return nil
}

func (v *BasicConfigValidator) validateLog(log *LogConfig) error {
	validLevels := []string{"debug", "info", "warn", "error", "fatal"}
	if !contains(validLevels, log.Level) {
		return fmt.Errorf("invalid log level: %s, must be one of %v", log.Level, validLevels)
	}

	validFormats := []string{"text", "json"}
	if !contains(validFormats, log.Format) {
		return fmt.Errorf("invalid log format: %s, must be one of %v", log.Format, validFormats)
	}

	if log.File != "" {
		dir := filepath.Dir(log.File)
		if err := os.MkdirAll(dir, 0755); err != nil {
			return fmt.Errorf("cannot create log directory %s: %w", dir, err)
		}
	}

	if log.MaxSize <= 0 {
		return fmt.Errorf("log max size must be positive")
	}

	if log.RetentionDays <= 0 {
		return fmt.Errorf("log retention days must be positive")
	}

	return nil
}

// NetworkConfigValidator 网络配置验证器
type NetworkConfigValidator struct{}

func (v *NetworkConfigValidator) Validate(config *Config) error {
	// 验证Redis配置
	if config.Redis.Host != "" {
		if err := v.validateHost(config.Redis.Host); err != nil {
			return fmt.Errorf("invalid Redis host: %w", err)
		}

		if err := v.validatePort(config.Redis.Port); err != nil {
			return fmt.Errorf("invalid Redis port: %w", err)
		}
	}

	// 验证DeepSeek配置
	if config.DeepSeek.APIURL != "" {
		if err := v.validateURL(config.DeepSeek.APIURL); err != nil {
			return fmt.Errorf("invalid DeepSeek API URL: %w", err)
		}
	}

	return nil
}

func (v *NetworkConfigValidator) validateHost(host string) error {
	if host == "" {
		return fmt.Errorf("host cannot be empty")
	}

	// 检查是否为有效的IP地址或主机名
	if ip := net.ParseIP(host); ip == nil {
		// 不是IP地址，检查是否为有效主机名
		if !isValidHostname(host) {
			return fmt.Errorf("invalid hostname: %s", host)
		}
	}

	return nil
}

func (v *NetworkConfigValidator) validatePort(port int) error {
	if port <= 0 || port > 65535 {
		return fmt.Errorf("port must be between 1 and 65535, got %d", port)
	}
	return nil
}

func (v *NetworkConfigValidator) validateURL(urlStr string) error {
	if urlStr == "" {
		return fmt.Errorf("URL cannot be empty")
	}

	parsedURL, err := url.Parse(urlStr)
	if err != nil {
		return fmt.Errorf("invalid URL format: %w", err)
	}

	if parsedURL.Scheme == "" {
		return fmt.Errorf("URL must have a scheme (http/https)")
	}

	if parsedURL.Host == "" {
		return fmt.Errorf("URL must have a host")
	}

	return nil
}

// EnvironmentConfigValidator 环境特定配置验证器
type EnvironmentConfigValidator struct {
	Environment string
}

func (v *EnvironmentConfigValidator) Validate(config *Config) error {
	switch v.Environment {
	case "production":
		return v.validateProduction(config)
	case "staging":
		return v.validateStaging(config)
	case "development":
		return v.validateDevelopment(config)
	default:
		return nil
	}
}

func (v *EnvironmentConfigValidator) validateProduction(config *Config) error {
	// 生产环境必须关闭调试模式
	if config.App.Debug {
		return fmt.Errorf("debug mode must be disabled in production")
	}

	// 生产环境必须使用强JWT密钥
	if len(config.JWT.Secret) < 64 {
		return fmt.Errorf("JWT secret must be at least 64 characters in production")
	}

	// 生产环境必须使用高强度密码哈希
	if config.Security.PasswordHashCost < 12 {
		return fmt.Errorf("password hash cost must be at least 12 in production")
	}

	// 生产环境必须配置日志文件
	if config.Log.File == "" {
		return fmt.Errorf("log file must be configured in production")
	}

	return nil
}

func (v *EnvironmentConfigValidator) validateStaging(config *Config) error {
	// 预发布环境的验证规则
	if config.Security.PasswordHashCost < 10 {
		return fmt.Errorf("password hash cost must be at least 10 in staging")
	}

	return nil
}

func (v *EnvironmentConfigValidator) validateDevelopment(config *Config) error {
	// 开发环境的验证规则（相对宽松）
	return nil
}

// 辅助函数
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

func isValidHostname(hostname string) bool {
	if len(hostname) == 0 || len(hostname) > 253 {
		return false
	}

	// 简单的主机名验证
	for _, char := range hostname {
		if !((char >= 'a' && char <= 'z') ||
			(char >= 'A' && char <= 'Z') ||
			(char >= '0' && char <= '9') ||
			char == '-' || char == '.') {
			return false
		}
	}

	return true
}

// ConfigChangeLogger 配置变更日志记录器
type ConfigChangeLogger struct{}

func (l *ConfigChangeLogger) OnConfigChanged(oldConfig, newConfig *Config) error {
	// 记录配置变更
	fmt.Printf("[CONFIG] Configuration changed at %s\n", time.Now().Format(time.RFC3339))

	// 这里可以添加更详细的变更记录逻辑
	// 比如比较具体字段的变化，记录到审计日志等

	return nil
}
