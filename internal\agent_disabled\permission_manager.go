package agent

import (
	"fmt"
	"path/filepath"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// PermissionManager 权限管理器
type PermissionManager struct {
	config      *PermissionConfig
	logger      *logrus.Logger
	userRoles   map[int64]*UserRole
	permissions map[string]*Permission
	mutex       sync.RWMutex
}

// PermissionConfig 权限配置
type PermissionConfig struct {
	EnableRBAC          bool     `json:"enable_rbac"`
	DefaultRole         string   `json:"default_role"`
	AdminUsers          []int64  `json:"admin_users"`
	GuestUsers          []int64  `json:"guest_users"`
	MaxSessionDuration  int      `json:"max_session_duration"` // 分钟
	RequireApproval     bool     `json:"require_approval"`
	ApprovalTimeout     int      `json:"approval_timeout"` // 分钟
	AuditAllCommands    bool     `json:"audit_all_commands"`
	RestrictedTimeSlots []string `json:"restricted_time_slots"`
}

// UserRole 用户角色
type UserRole struct {
	UserID       int64                  `json:"user_id"`
	Role         string                 `json:"role"`
	Permissions  []string               `json:"permissions"`
	Restrictions map[string]interface{} `json:"restrictions"`
	CreatedAt    time.Time              `json:"created_at"`
	ExpiresAt    *time.Time             `json:"expires_at,omitempty"`
	IsActive     bool                   `json:"is_active"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// Permission 权限定义
type Permission struct {
	Name         string                 `json:"name"`
	Description  string                 `json:"description"`
	Category     string                 `json:"category"`
	Level        string                 `json:"level"` // read, write, execute, admin
	Commands     []string               `json:"commands"`
	Paths        []string               `json:"paths"`
	Restrictions map[string]interface{} `json:"restrictions"`
	CreatedAt    time.Time              `json:"created_at"`
}

// PermissionCheck 权限检查结果
type PermissionCheck struct {
	Allowed          bool                   `json:"allowed"`
	Reason           string                 `json:"reason"`
	UserID           int64                  `json:"user_id"`
	Role             string                 `json:"role"`
	Permission       string                 `json:"permission"`
	Command          string                 `json:"command"`
	Path             string                 `json:"path"`
	RiskLevel        string                 `json:"risk_level"`
	RequiresApproval bool                   `json:"requires_approval"`
	Metadata         map[string]interface{} `json:"metadata"`
	CheckedAt        time.Time              `json:"checked_at"`
}

// NewPermissionManager 创建权限管理器
func NewPermissionManager(config *PermissionConfig, logger *logrus.Logger) *PermissionManager {
	if config == nil {
		config = DefaultPermissionConfig()
	}

	pm := &PermissionManager{
		config:      config,
		logger:      logger,
		userRoles:   make(map[int64]*UserRole),
		permissions: make(map[string]*Permission),
	}

	// 初始化默认权限
	pm.initializeDefaultPermissions()

	// 初始化默认用户角色
	pm.initializeDefaultRoles()

	return pm
}

// DefaultPermissionConfig 默认权限配置
func DefaultPermissionConfig() *PermissionConfig {
	return &PermissionConfig{
		EnableRBAC:          true,
		DefaultRole:         "user",
		AdminUsers:          []int64{1},
		GuestUsers:          []int64{},
		MaxSessionDuration:  480, // 8小时
		RequireApproval:     true,
		ApprovalTimeout:     30, // 30分钟
		AuditAllCommands:    true,
		RestrictedTimeSlots: []string{"22:00-06:00"}, // 夜间限制
	}
}

// initializeDefaultPermissions 初始化默认权限
func (pm *PermissionManager) initializeDefaultPermissions() {
	permissions := []*Permission{
		{
			Name:        "system.read",
			Description: "系统信息读取权限",
			Category:    "system",
			Level:       "read",
			Commands:    []string{"ps", "top", "free", "df", "uptime", "whoami", "id"},
			Paths:       []string{"/proc", "/sys"},
			Restrictions: map[string]interface{}{
				"max_execution_time": 30,
				"max_output_size":    1024 * 1024, // 1MB
			},
			CreatedAt: time.Now(),
		},
		{
			Name:        "network.read",
			Description: "网络信息读取权限",
			Category:    "network",
			Level:       "read",
			Commands:    []string{"ping", "netstat", "ss", "ip"},
			Paths:       []string{"/proc/net"},
			Restrictions: map[string]interface{}{
				"max_execution_time": 60,
				"ping_count_limit":   10,
			},
			CreatedAt: time.Now(),
		},
		{
			Name:        "file.read",
			Description: "文件读取权限",
			Category:    "file",
			Level:       "read",
			Commands:    []string{"ls", "cat", "head", "tail", "grep", "find"},
			Paths:       []string{"/home", "/tmp", "/var/log"},
			Restrictions: map[string]interface{}{
				"max_file_size":     10 * 1024 * 1024, // 10MB
				"max_search_depth":  5,
				"excluded_patterns": []string{"*.key", "*.pem", "*password*"},
			},
			CreatedAt: time.Now(),
		},
		{
			Name:        "service.read",
			Description: "服务状态读取权限",
			Category:    "service",
			Level:       "read",
			Commands:    []string{"systemctl status", "service status"},
			Paths:       []string{"/etc/systemd"},
			Restrictions: map[string]interface{}{
				"allowed_services": []string{"nginx", "apache2", "mysql", "postgresql"},
			},
			CreatedAt: time.Now(),
		},
		{
			Name:        "log.read",
			Description: "日志读取权限",
			Category:    "log",
			Level:       "read",
			Commands:    []string{"journalctl", "tail", "grep"},
			Paths:       []string{"/var/log"},
			Restrictions: map[string]interface{}{
				"max_lines":        1000,
				"time_range_limit": "24h",
				"excluded_logs":    []string{"auth.log", "secure"},
			},
			CreatedAt: time.Now(),
		},
		{
			Name:        "performance.read",
			Description: "性能监控权限",
			Category:    "performance",
			Level:       "read",
			Commands:    []string{"vmstat", "iostat", "sar", "htop"},
			Paths:       []string{"/proc"},
			Restrictions: map[string]interface{}{
				"max_monitoring_time": 300, // 5分钟
				"sample_interval":     1,
			},
			CreatedAt: time.Now(),
		},
		{
			Name:        "system.admin",
			Description: "系统管理权限",
			Category:    "system",
			Level:       "admin",
			Commands:    []string{"systemctl", "service", "mount", "umount"},
			Paths:       []string{"/etc", "/usr", "/var"},
			Restrictions: map[string]interface{}{
				"requires_approval": true,
				"approval_timeout":  30,
				"audit_required":    true,
			},
			CreatedAt: time.Now(),
		},
	}

	for _, perm := range permissions {
		pm.permissions[perm.Name] = perm
	}
}

// initializeDefaultRoles 初始化默认用户角色
func (pm *PermissionManager) initializeDefaultRoles() {
	// 管理员角色
	for _, adminID := range pm.config.AdminUsers {
		pm.userRoles[adminID] = &UserRole{
			UserID: adminID,
			Role:   "admin",
			Permissions: []string{
				"system.read", "network.read", "file.read",
				"service.read", "log.read", "performance.read",
				"system.admin",
			},
			Restrictions: map[string]interface{}{
				"max_concurrent_commands": 10,
				"session_timeout":         480, // 8小时
			},
			CreatedAt: time.Now(),
			IsActive:  true,
			Metadata:  make(map[string]interface{}),
		}
	}

	// 访客角色
	for _, guestID := range pm.config.GuestUsers {
		pm.userRoles[guestID] = &UserRole{
			UserID: guestID,
			Role:   "guest",
			Permissions: []string{
				"system.read", "network.read",
			},
			Restrictions: map[string]interface{}{
				"max_concurrent_commands": 2,
				"session_timeout":         60, // 1小时
				"requires_approval":       true,
			},
			CreatedAt: time.Now(),
			IsActive:  true,
			Metadata:  make(map[string]interface{}),
		}
	}
}

// CheckPermission 检查权限
func (pm *PermissionManager) CheckPermission(userID int64, command string, args []string, path string) (*PermissionCheck, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	check := &PermissionCheck{
		UserID:    userID,
		Command:   command,
		Path:      path,
		CheckedAt: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	// 获取用户角色
	userRole, exists := pm.userRoles[userID]
	if !exists {
		// 使用默认角色
		userRole = pm.getDefaultRole(userID)
		pm.userRoles[userID] = userRole
	}

	check.Role = userRole.Role

	// 检查角色是否激活
	if !userRole.IsActive {
		check.Allowed = false
		check.Reason = "User role is inactive"
		return check, nil
	}

	// 检查角色是否过期
	if userRole.ExpiresAt != nil && time.Now().After(*userRole.ExpiresAt) {
		check.Allowed = false
		check.Reason = "User role has expired"
		return check, nil
	}

	// 检查时间限制
	if pm.isRestrictedTime() {
		check.Allowed = false
		check.Reason = "Command execution is restricted during this time"
		return check, nil
	}

	// 检查命令权限
	allowed, permission, reason := pm.checkCommandPermission(userRole, command, args, path)
	check.Allowed = allowed
	check.Permission = permission
	check.Reason = reason

	if allowed {
		// 检查是否需要审批
		check.RequiresApproval = pm.requiresApproval(userRole, command, permission)

		// 设置风险等级
		check.RiskLevel = pm.calculateRiskLevel(command, args, permission)

		// 记录审计日志
		if pm.config.AuditAllCommands {
			pm.auditPermissionCheck(check)
		}
	}

	return check, nil
}

// checkCommandPermission 检查命令权限
func (pm *PermissionManager) checkCommandPermission(userRole *UserRole, command string, args []string, path string) (bool, string, string) {
	for _, permName := range userRole.Permissions {
		perm, exists := pm.permissions[permName]
		if !exists {
			continue
		}

		// 检查命令是否在权限列表中
		if pm.commandMatches(command, perm.Commands) {
			// 检查路径权限
			if path != "" && !pm.pathAllowed(path, perm.Paths) {
				continue
			}

			// 检查参数限制
			if !pm.checkArgumentRestrictions(args, perm.Restrictions) {
				return false, permName, "Command arguments violate restrictions"
			}

			return true, permName, "Permission granted"
		}
	}

	return false, "", "No matching permission found"
}

// commandMatches 检查命令是否匹配
func (pm *PermissionManager) commandMatches(command string, allowedCommands []string) bool {
	for _, allowed := range allowedCommands {
		if command == allowed {
			return true
		}

		// 支持通配符匹配
		if strings.Contains(allowed, "*") {
			if matched, _ := filepath.Match(allowed, command); matched {
				return true
			}
		}

		// 支持前缀匹配
		if strings.HasSuffix(allowed, " ") {
			if strings.HasPrefix(command, strings.TrimSpace(allowed)) {
				return true
			}
		}
	}

	return false
}

// pathAllowed 检查路径是否允许
func (pm *PermissionManager) pathAllowed(path string, allowedPaths []string) bool {
	if len(allowedPaths) == 0 {
		return true // 没有路径限制
	}

	absPath, err := filepath.Abs(path)
	if err != nil {
		return false
	}

	for _, allowed := range allowedPaths {
		allowedAbs, err := filepath.Abs(allowed)
		if err != nil {
			continue
		}

		// 检查是否在允许的路径下
		if strings.HasPrefix(absPath, allowedAbs) {
			return true
		}
	}

	return false
}

// checkArgumentRestrictions 检查参数限制
func (pm *PermissionManager) checkArgumentRestrictions(args []string, restrictions map[string]interface{}) bool {
	// 检查排除的模式
	if excludedPatterns, ok := restrictions["excluded_patterns"].([]string); ok {
		for _, arg := range args {
			for _, pattern := range excludedPatterns {
				if matched, _ := filepath.Match(pattern, arg); matched {
					return false
				}
			}
		}
	}

	// 检查ping计数限制
	if pingLimit, ok := restrictions["ping_count_limit"].(int); ok {
		for i, arg := range args {
			if arg == "-c" && i+1 < len(args) {
				if count := parseInt(args[i+1]); count > pingLimit {
					return false
				}
			}
		}
	}

	return true
}

// requiresApproval 检查是否需要审批
func (pm *PermissionManager) requiresApproval(userRole *UserRole, command string, permission string) bool {
	// 检查全局配置
	if pm.config.RequireApproval {
		return true
	}

	// 检查用户角色限制
	if requiresApproval, ok := userRole.Restrictions["requires_approval"].(bool); ok && requiresApproval {
		return true
	}

	// 检查权限级别
	if perm, exists := pm.permissions[permission]; exists {
		if requiresApproval, ok := perm.Restrictions["requires_approval"].(bool); ok && requiresApproval {
			return true
		}
	}

	return false
}

// calculateRiskLevel 计算风险等级
func (pm *PermissionManager) calculateRiskLevel(command string, args []string, permission string) string {
	// 基于权限级别
	if perm, exists := pm.permissions[permission]; exists {
		switch perm.Level {
		case "read":
			return "low"
		case "write":
			return "medium"
		case "execute":
			return "high"
		case "admin":
			return "critical"
		}
	}

	// 基于命令类型
	dangerousCommands := []string{"rm", "dd", "mkfs", "fdisk", "iptables"}
	for _, dangerous := range dangerousCommands {
		if command == dangerous {
			return "critical"
		}
	}

	return "medium"
}

// isRestrictedTime 检查是否在限制时间内
func (pm *PermissionManager) isRestrictedTime() bool {
	if len(pm.config.RestrictedTimeSlots) == 0 {
		return false
	}

	now := time.Now()
	currentTime := now.Format("15:04")

	for _, slot := range pm.config.RestrictedTimeSlots {
		parts := strings.Split(slot, "-")
		if len(parts) == 2 {
			start := strings.TrimSpace(parts[0])
			end := strings.TrimSpace(parts[1])

			if pm.timeInRange(currentTime, start, end) {
				return true
			}
		}
	}

	return false
}

// timeInRange 检查时间是否在范围内
func (pm *PermissionManager) timeInRange(current, start, end string) bool {
	// 简化实现，假设都在同一天内
	return current >= start && current <= end
}

// getDefaultRole 获取默认角色
func (pm *PermissionManager) getDefaultRole(userID int64) *UserRole {
	return &UserRole{
		UserID: userID,
		Role:   pm.config.DefaultRole,
		Permissions: []string{
			"system.read", "network.read", "file.read",
		},
		Restrictions: map[string]interface{}{
			"max_concurrent_commands": 3,
			"session_timeout":         120, // 2小时
		},
		CreatedAt: time.Now(),
		IsActive:  true,
		Metadata:  make(map[string]interface{}),
	}
}

// auditPermissionCheck 审计权限检查
func (pm *PermissionManager) auditPermissionCheck(check *PermissionCheck) {
	pm.logger.WithFields(logrus.Fields{
		"user_id":           check.UserID,
		"role":              check.Role,
		"command":           check.Command,
		"path":              check.Path,
		"permission":        check.Permission,
		"allowed":           check.Allowed,
		"reason":            check.Reason,
		"requires_approval": check.RequiresApproval,
		"risk_level":        check.RiskLevel,
	}).Info("Permission check audit")
}

// AddUserRole 添加用户角色
func (pm *PermissionManager) AddUserRole(userRole *UserRole) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.userRoles[userRole.UserID] = userRole

	pm.logger.WithFields(logrus.Fields{
		"user_id": userRole.UserID,
		"role":    userRole.Role,
	}).Info("User role added")

	return nil
}

// RemoveUserRole 移除用户角色
func (pm *PermissionManager) RemoveUserRole(userID int64) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	delete(pm.userRoles, userID)

	pm.logger.WithField("user_id", userID).Info("User role removed")

	return nil
}

// GetUserRole 获取用户角色
func (pm *PermissionManager) GetUserRole(userID int64) (*UserRole, error) {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	role, exists := pm.userRoles[userID]
	if !exists {
		return nil, fmt.Errorf("user role not found: %d", userID)
	}

	return role, nil
}

// GetPermissionStatus 获取权限状态
func (pm *PermissionManager) GetPermissionStatus() *PermissionStatus {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	userRoleList := make([]*UserRole, 0, len(pm.userRoles))
	for _, role := range pm.userRoles {
		userRoleList = append(userRoleList, role)
	}

	permissionList := make([]*Permission, 0, len(pm.permissions))
	for _, perm := range pm.permissions {
		permissionList = append(permissionList, perm)
	}

	return &PermissionStatus{
		RBACEnabled:      pm.config.EnableRBAC,
		TotalUsers:       len(pm.userRoles),
		TotalPermissions: len(pm.permissions),
		UserRoles:        userRoleList,
		Permissions:      permissionList,
		Config:           pm.config,
	}
}

// PermissionStatus 权限状态
type PermissionStatus struct {
	RBACEnabled      bool              `json:"rbac_enabled"`
	TotalUsers       int               `json:"total_users"`
	TotalPermissions int               `json:"total_permissions"`
	UserRoles        []*UserRole       `json:"user_roles"`
	Permissions      []*Permission     `json:"permissions"`
	Config           *PermissionConfig `json:"config"`
}

// 辅助函数
func parseInt(s string) int {
	// 简化实现
	if s == "1" {
		return 1
	}
	if s == "5" {
		return 5
	}
	if s == "10" {
		return 10
	}
	return 0
}
