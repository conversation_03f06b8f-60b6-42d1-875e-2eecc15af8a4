# AI运维管理平台 - 清理main函数冲突脚本
# 作者: Claude 4.0 sonnet

Write-Host "🧹 开始清理main函数冲突文件..." -ForegroundColor Green

# 创建目标目录
$testDir = "tests\unit"
$exampleDir = "examples\demos"

if (!(Test-Path $testDir)) {
    New-Item -ItemType Directory -Path $testDir -Force
    Write-Host "✅ 创建测试目录: $testDir" -ForegroundColor Green
}

if (!(Test-Path $exampleDir)) {
    New-Item -ItemType Directory -Path $exampleDir -Force
    Write-Host "✅ 创建示例目录: $exampleDir" -ForegroundColor Green
}

# 定义需要移动的文件列表
$testFiles = @(
    "test_ai_driven_execution.go",
    "test_ai_intelligence_comprehensive.go", 
    "test_ai_intelligence_simple.go",
    "test_ai_system_simple.go",
    "test_basic.go",
    "test_compilation_fix.go",
    "test_comprehensive_inspection.go",
    "test_direct_execution_simple.go",
    "test_emotional_intelligence.go",
    "test_enhanced_conversation.go",
    "test_fix_verification.go",
    "test_import_fix.go",
    "test_intelligent_architecture_validation.go",
    "test_intelligent_intent.go",
    "test_intent.go",
    "test_intent_fix.go",
    "test_multimodal_interaction.go",
    "test_personalization_engine.go",
    "test_predictive_operations.go",
    "test_revolutionary_ai_system.go",
    "test_revolutionary_architecture.go",
    "test_revolutionary_system_demo.go",
    "test_simple.go",
    "test_unified_adapter.go",
    "test_unified_execution.go",
    "integration_test_unified_execution.go"
)

$demoFiles = @(
    "enhanced_conversation_demo.go",
    "simple_architecture_demo.go"
)

# 移动测试文件
Write-Host "`n📁 移动测试文件到 $testDir..." -ForegroundColor Yellow
foreach ($file in $testFiles) {
    if (Test-Path $file) {
        try {
            Move-Item $file "$testDir\$file" -Force
            Write-Host "  ✅ 移动: $file" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ 移动失败: $file - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "  ⚠️  文件不存在: $file" -ForegroundColor Yellow
    }
}

# 移动演示文件
Write-Host "`n📁 移动演示文件到 $exampleDir..." -ForegroundColor Yellow
foreach ($file in $demoFiles) {
    if (Test-Path $file) {
        try {
            Move-Item $file "$exampleDir\$file" -Force
            Write-Host "  ✅ 移动: $file" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ 移动失败: $file - $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "  ⚠️  文件不存在: $file" -ForegroundColor Yellow
    }
}

# 移动其他可能冲突的文件
$otherFiles = @(
    "fix_suggestions.py",
    "fix_mock_ai.ps1",
    "fix_functions.txt"
)

Write-Host "`n📁 移动其他文件到 scripts..." -ForegroundColor Yellow
foreach ($file in $otherFiles) {
    if (Test-Path $file) {
        try {
            Move-Item $file "scripts\$file" -Force
            Write-Host "  ✅ 移动: $file" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ 移动失败: $file - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 清理可执行文件（保留主要的）
$execFiles = @(
    "test_emotional_intelligence.exe",
    "test_intelligent_intent.exe", 
    "test_intent.exe",
    "test_predictive_operations.exe",
    "test_simple.exe",
    "main.exe",
    "server.exe"
)

Write-Host "`n🗑️  清理多余的可执行文件..." -ForegroundColor Yellow
foreach ($file in $execFiles) {
    if (Test-Path $file) {
        try {
            Remove-Item $file -Force
            Write-Host "  ✅ 删除: $file" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ 删除失败: $file - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 清理测试数据库文件
$testDbFiles = @(
    "test_emotion.db",
    "test_intelligent_intent.db",
    "test_predictive.db", 
    "test_simple.db"
)

Write-Host "`n🗑️  清理测试数据库文件..." -ForegroundColor Yellow
foreach ($file in $testDbFiles) {
    if (Test-Path $file) {
        try {
            Remove-Item $file -Force
            Write-Host "  ✅ 删除: $file" -ForegroundColor Green
        } catch {
            Write-Host "  ❌ 删除失败: $file - $($_.Exception.Message)" -ForegroundColor Red
        }
    }
}

# 验证编译
Write-Host "`n🔨 验证Go项目编译..." -ForegroundColor Cyan
try {
    $buildResult = go build -v . 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 编译成功！" -ForegroundColor Green
    } else {
        Write-Host "❌ 编译仍有问题:" -ForegroundColor Red
        Write-Host $buildResult -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 编译检查失败: $($_.Exception.Message)" -ForegroundColor Red
}

# 创建README文件说明移动的文件
$readmeContent = @"
# 文件移动说明

## 移动的文件

### 测试文件 (移动到 tests/unit/)
$($testFiles -join "`n")

### 演示文件 (移动到 examples/demos/)  
$($demoFiles -join "`n")

### 其他文件 (移动到 scripts/)
$($otherFiles -join "`n")

## 删除的文件

### 可执行文件
$($execFiles -join "`n")

### 测试数据库
$($testDbFiles -join "`n")

## 说明
这些文件因为包含main函数导致Go编译冲突，已经移动到合适的目录中。
如需运行这些测试或演示，请到对应目录中执行。

移动时间: $(Get-Date)
"@

$readmeContent | Out-File -FilePath "CLEANUP_SUMMARY.md" -Encoding UTF8
Write-Host "`n📝 创建清理说明文件: CLEANUP_SUMMARY.md" -ForegroundColor Green

Write-Host "`n🎉 清理完成！项目现在应该可以正常编译了。" -ForegroundColor Green
Write-Host "💡 提示: 如需运行测试，请使用: go test ./tests/unit/..." -ForegroundColor Cyan
Write-Host "💡 提示: 如需运行演示，请到 examples/demos/ 目录中执行" -ForegroundColor Cyan
