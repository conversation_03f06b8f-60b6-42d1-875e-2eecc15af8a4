package service

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"golang.org/x/crypto/ssh"
)

// HostConnectivityDiagnosticTool 主机连接诊断工具
type HostConnectivityDiagnosticTool struct {
	logger      *logrus.Logger
	hostService HostService
	timeout     time.Duration
}

func (tool *HostConnectivityDiagnosticTool) GetName() string {
	return "host_connectivity"
}

func (tool *HostConnectivityDiagnosticTool) GetDescription() string {
	return "诊断主机网络连接状态和SSH服务可用性"
}

func (tool *HostConnectivityDiagnosticTool) GetCategory() string {
	return "connectivity"
}

func (tool *HostConnectivityDiagnosticTool) GetSupportedTargets() []string {
	return []string{"host"}
}

func (tool *HostConnectivityDiagnosticTool) Diagnose(ctx context.Context, target *DiagnosisTarget) (*DiagnosisResult, error) {
	hostIP := target.Identifier
	var details []DiagnosisDetail
	var suggestions []string

	tool.logger.WithField("host_ip", hostIP).Info("Starting host connectivity diagnosis")

	// 1. 获取主机信息
	host, err := tool.getHostByIP(hostIP)
	if err != nil {
		details = append(details, DiagnosisDetail{
			Category: "database",
			Item:     "主机信息查询",
			Status:   "fail",
			Message:  fmt.Sprintf("无法从数据库获取主机信息: %v", err),
			Severity: "high",
		})
		suggestions = append(suggestions, "请检查主机IP是否正确，或者该主机是否已添加到系统中")
	} else {
		details = append(details, DiagnosisDetail{
			Category: "database",
			Item:     "主机信息查询",
			Status:   "pass",
			Message:  fmt.Sprintf("主机信息: %s (%s)", host.Name, host.IPAddress),
			Value:    host,
			Severity: "low",
		})
	}

	// 2. 网络连通性检查
	connectivityResult := tool.checkNetworkConnectivity(hostIP)
	details = append(details, connectivityResult...)

	// 3. SSH端口检查
	var port int = 22
	if host != nil {
		port = host.Port
	}

	sshPortResult := tool.checkSSHPort(hostIP, port)
	details = append(details, sshPortResult...)

	// 4. SSH服务检查
	if host != nil {
		sshServiceResult := tool.checkSSHService(host)
		details = append(details, sshServiceResult...)
	}

	// 分析整体状态
	status := "success"
	var failedChecks []string
	var warningChecks []string

	for _, detail := range details {
		if detail.Status == "fail" {
			status = "error"
			failedChecks = append(failedChecks, detail.Item)
		} else if detail.Status == "warning" && status == "success" {
			status = "warning"
			warningChecks = append(warningChecks, detail.Item)
		}
	}

	// 生成建议
	if len(failedChecks) > 0 {
		suggestions = append(suggestions, "检查网络连接和防火墙设置")
		suggestions = append(suggestions, "确认SSH服务是否正在运行")
		suggestions = append(suggestions, "验证主机IP地址和端口配置")
	}

	summary := tool.generateSummary(status, failedChecks, warningChecks)

	return &DiagnosisResult{
		ToolName:    tool.GetName(),
		Target:      target,
		Status:      status,
		Summary:     summary,
		Details:     details,
		Suggestions: suggestions,
		Metadata: map[string]interface{}{
			"host_ip": hostIP,
			"port":    port,
		},
	}, nil
}

// checkNetworkConnectivity 检查网络连通性
func (tool *HostConnectivityDiagnosticTool) checkNetworkConnectivity(hostIP string) []DiagnosisDetail {
	var details []DiagnosisDetail

	// ICMP Ping检查
	// 注意：在某些环境中ping可能被禁用，这里我们主要检查TCP连接

	// 检查基本网络可达性（通过尝试建立TCP连接）
	conn, err := net.DialTimeout("tcp", fmt.Sprintf("%s:22", hostIP), 5*time.Second)
	if err != nil {
		details = append(details, DiagnosisDetail{
			Category: "network",
			Item:     "网络连通性",
			Status:   "fail",
			Message:  fmt.Sprintf("无法连接到主机 %s: %v", hostIP, err),
			Severity: "high",
		})
	} else {
		conn.Close()
		details = append(details, DiagnosisDetail{
			Category: "network",
			Item:     "网络连通性",
			Status:   "pass",
			Message:  fmt.Sprintf("主机 %s 网络连通正常", hostIP),
			Severity: "low",
		})
	}

	return details
}

// checkSSHPort 检查SSH端口
func (tool *HostConnectivityDiagnosticTool) checkSSHPort(hostIP string, port int) []DiagnosisDetail {
	var details []DiagnosisDetail

	address := fmt.Sprintf("%s:%d", hostIP, port)
	conn, err := net.DialTimeout("tcp", address, tool.timeout)
	if err != nil {
		details = append(details, DiagnosisDetail{
			Category: "ssh",
			Item:     "SSH端口检查",
			Status:   "fail",
			Message:  fmt.Sprintf("SSH端口 %d 无法连接: %v", port, err),
			Value:    port,
			Severity: "high",
		})
	} else {
		conn.Close()
		details = append(details, DiagnosisDetail{
			Category: "ssh",
			Item:     "SSH端口检查",
			Status:   "pass",
			Message:  fmt.Sprintf("SSH端口 %d 连接正常", port),
			Value:    port,
			Severity: "low",
		})
	}

	return details
}

// checkSSHService 检查SSH服务
func (tool *HostConnectivityDiagnosticTool) checkSSHService(host *model.Host) []DiagnosisDetail {
	var details []DiagnosisDetail

	// 尝试SSH握手
	config := &ssh.ClientConfig{
		User:            host.Username,
		Auth:            []ssh.AuthMethod{},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         tool.timeout,
	}

	address := fmt.Sprintf("%s:%d", host.IPAddress, host.Port)
	client, err := ssh.Dial("tcp", address, config)
	if err != nil {
		// 分析错误类型
		if strings.Contains(err.Error(), "no supported authentication") {
			details = append(details, DiagnosisDetail{
				Category: "ssh",
				Item:     "SSH服务检查",
				Status:   "pass",
				Message:  "SSH服务运行正常（认证方法不匹配是正常的）",
				Severity: "low",
			})
		} else if strings.Contains(err.Error(), "connection refused") {
			details = append(details, DiagnosisDetail{
				Category: "ssh",
				Item:     "SSH服务检查",
				Status:   "fail",
				Message:  "SSH服务未运行或端口被阻止",
				Severity: "high",
			})
		} else {
			details = append(details, DiagnosisDetail{
				Category: "ssh",
				Item:     "SSH服务检查",
				Status:   "warning",
				Message:  fmt.Sprintf("SSH连接异常: %v", err),
				Severity: "medium",
			})
		}
	} else {
		client.Close()
		details = append(details, DiagnosisDetail{
			Category: "ssh",
			Item:     "SSH服务检查",
			Status:   "pass",
			Message:  "SSH服务运行正常",
			Severity: "low",
		})
	}

	return details
}

// generateSummary 生成摘要
func (tool *HostConnectivityDiagnosticTool) generateSummary(status string, failedChecks, warningChecks []string) string {
	switch status {
	case "error":
		return fmt.Sprintf("主机连接诊断失败，%d个检查项目未通过: %s", len(failedChecks), strings.Join(failedChecks, ", "))
	case "warning":
		return fmt.Sprintf("主机连接基本正常，但有%d个警告: %s", len(warningChecks), strings.Join(warningChecks, ", "))
	default:
		return "主机连接诊断通过，所有检查项目正常"
	}
}

// SSHAuthenticationDiagnosticTool SSH认证诊断工具
type SSHAuthenticationDiagnosticTool struct {
	logger      *logrus.Logger
	hostService HostService
	timeout     time.Duration
}

func (tool *SSHAuthenticationDiagnosticTool) GetName() string {
	return "ssh_authentication"
}

func (tool *SSHAuthenticationDiagnosticTool) GetDescription() string {
	return "诊断SSH认证问题，包括密码认证和密钥认证"
}

func (tool *SSHAuthenticationDiagnosticTool) GetCategory() string {
	return "authentication"
}

func (tool *SSHAuthenticationDiagnosticTool) GetSupportedTargets() []string {
	return []string{"host"}
}

func (tool *SSHAuthenticationDiagnosticTool) Diagnose(ctx context.Context, target *DiagnosisTarget) (*DiagnosisResult, error) {
	hostIP := target.Identifier
	var details []DiagnosisDetail
	var suggestions []string

	tool.logger.WithField("host_ip", hostIP).Info("Starting SSH authentication diagnosis")

	// 获取主机信息
	host, err := tool.getHostByIP(hostIP)
	if err != nil {
		return nil, fmt.Errorf("无法获取主机信息: %w", err)
	}

	// 1. 检查认证配置
	authConfigResult := tool.checkAuthConfiguration(host)
	details = append(details, authConfigResult...)

	// 2. 测试密码认证
	if host.PasswordEncrypted != "" {
		passwordAuthResult := tool.testPasswordAuthentication(host)
		details = append(details, passwordAuthResult...)
	}

	// 3. 测试密钥认证
	if host.SSHKeyPath != "" {
		keyAuthResult := tool.testKeyAuthentication(host)
		details = append(details, keyAuthResult...)
	}

	// 分析结果
	status := "success"
	var authMethods []string
	var failedMethods []string

	for _, detail := range details {
		if detail.Category == "authentication" {
			if detail.Status == "pass" {
				authMethods = append(authMethods, detail.Item)
			} else if detail.Status == "fail" {
				failedMethods = append(failedMethods, detail.Item)
				status = "error"
			}
		}
	}

	if len(authMethods) == 0 && len(failedMethods) > 0 {
		suggestions = append(suggestions, "检查用户名和密码是否正确")
		suggestions = append(suggestions, "验证SSH密钥是否有效")
		suggestions = append(suggestions, "确认目标主机的SSH配置允许相应的认证方法")
	}

	summary := tool.generateAuthSummary(status, authMethods, failedMethods)

	return &DiagnosisResult{
		ToolName:    tool.GetName(),
		Target:      target,
		Status:      status,
		Summary:     summary,
		Details:     details,
		Suggestions: suggestions,
		Metadata: map[string]interface{}{
			"host_ip":      hostIP,
			"username":     host.Username,
			"auth_methods": authMethods,
		},
	}, nil
}

// checkAuthConfiguration 检查认证配置
func (tool *SSHAuthenticationDiagnosticTool) checkAuthConfiguration(host *model.Host) []DiagnosisDetail {
	var details []DiagnosisDetail

	// 检查用户名
	if host.Username == "" {
		details = append(details, DiagnosisDetail{
			Category: "configuration",
			Item:     "用户名配置",
			Status:   "fail",
			Message:  "用户名未配置",
			Severity: "high",
		})
	} else {
		details = append(details, DiagnosisDetail{
			Category: "configuration",
			Item:     "用户名配置",
			Status:   "pass",
			Message:  fmt.Sprintf("用户名: %s", host.Username),
			Value:    host.Username,
			Severity: "low",
		})
	}

	// 检查认证方法
	hasPassword := host.PasswordEncrypted != ""
	hasPrivateKey := host.SSHKeyPath != ""

	if !hasPassword && !hasPrivateKey {
		details = append(details, DiagnosisDetail{
			Category: "configuration",
			Item:     "认证方法配置",
			Status:   "fail",
			Message:  "未配置任何认证方法（密码或私钥）",
			Severity: "high",
		})
	} else {
		var methods []string
		if hasPassword {
			methods = append(methods, "密码认证")
		}
		if hasPrivateKey {
			methods = append(methods, "密钥认证")
		}
		details = append(details, DiagnosisDetail{
			Category: "configuration",
			Item:     "认证方法配置",
			Status:   "pass",
			Message:  fmt.Sprintf("已配置认证方法: %s", strings.Join(methods, ", ")),
			Value:    methods,
			Severity: "low",
		})
	}

	return details
}

// testPasswordAuthentication 测试密码认证
func (tool *SSHAuthenticationDiagnosticTool) testPasswordAuthentication(host *model.Host) []DiagnosisDetail {
	var details []DiagnosisDetail

	// 注意：由于密码是加密存储的，我们无法直接解密进行测试
	// 这里我们只能检查是否配置了密码，实际的认证测试需要通过其他方式
	details = append(details, DiagnosisDetail{
		Category: "authentication",
		Item:     "密码认证配置",
		Status:   "pass",
		Message:  "密码已配置（加密存储，无法直接测试）",
		Severity: "low",
	})

	// 尝试基本的SSH连接测试（不进行认证）
	address := fmt.Sprintf("%s:%d", host.IPAddress, host.Port)
	config := &ssh.ClientConfig{
		User:            host.Username,
		Auth:            []ssh.AuthMethod{}, // 空认证方法，只测试连接
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
		Timeout:         tool.timeout,
	}

	client, err := ssh.Dial("tcp", address, config)
	if err != nil {
		// 分析错误类型
		if strings.Contains(err.Error(), "no supported authentication") {
			details = append(details, DiagnosisDetail{
				Category: "authentication",
				Item:     "SSH服务可用性",
				Status:   "pass",
				Message:  "SSH服务正常（需要认证是正常的）",
				Severity: "low",
			})
		} else {
			details = append(details, DiagnosisDetail{
				Category: "authentication",
				Item:     "SSH服务可用性",
				Status:   "warning",
				Message:  fmt.Sprintf("SSH连接异常: %v", err),
				Severity: "medium",
			})
		}
	} else {
		client.Close()
		details = append(details, DiagnosisDetail{
			Category: "authentication",
			Item:     "SSH服务可用性",
			Status:   "pass",
			Message:  "SSH服务正常",
			Severity: "low",
		})
	}

	return details
}

// testKeyAuthentication 测试密钥认证
func (tool *SSHAuthenticationDiagnosticTool) testKeyAuthentication(host *model.Host) []DiagnosisDetail {
	var details []DiagnosisDetail

	// 注意：Host模型中的SSHKeyPath是密钥文件路径，不是密钥内容
	// 在实际环境中，我们需要读取密钥文件内容
	// 这里我们只能检查是否配置了密钥路径
	details = append(details, DiagnosisDetail{
		Category: "authentication",
		Item:     "密钥配置检查",
		Status:   "pass",
		Message:  fmt.Sprintf("SSH密钥路径已配置: %s", host.SSHKeyPath),
		Value:    host.SSHKeyPath,
		Severity: "low",
	})

	// 由于我们无法直接读取密钥文件（可能涉及文件系统访问权限），
	// 这里我们只进行基本的SSH连接测试
	details = append(details, DiagnosisDetail{
		Category: "authentication",
		Item:     "密钥认证测试",
		Status:   "warning",
		Message:  "密钥认证测试需要读取密钥文件，当前版本暂不支持",
		Severity: "medium",
	})

	return details
}

// generateAuthSummary 生成认证摘要
func (tool *SSHAuthenticationDiagnosticTool) generateAuthSummary(status string, authMethods, failedMethods []string) string {
	if status == "error" {
		if len(failedMethods) > 0 {
			return fmt.Sprintf("SSH认证失败，以下认证方法未通过: %s", strings.Join(failedMethods, ", "))
		}
		return "SSH认证失败，无可用的认证方法"
	}

	if len(authMethods) > 0 {
		return fmt.Sprintf("SSH认证成功，可用认证方法: %s", strings.Join(authMethods, ", "))
	}

	return "SSH认证配置正常"
}

// NetworkConnectivityDiagnosticTool 网络连通性诊断工具
type NetworkConnectivityDiagnosticTool struct {
	logger  *logrus.Logger
	timeout time.Duration
}

func (tool *NetworkConnectivityDiagnosticTool) GetName() string {
	return "network_connectivity"
}

func (tool *NetworkConnectivityDiagnosticTool) GetDescription() string {
	return "诊断网络连通性，包括TCP连接、端口扫描等"
}

func (tool *NetworkConnectivityDiagnosticTool) GetCategory() string {
	return "network"
}

func (tool *NetworkConnectivityDiagnosticTool) GetSupportedTargets() []string {
	return []string{"host", "network"}
}

func (tool *NetworkConnectivityDiagnosticTool) Diagnose(ctx context.Context, target *DiagnosisTarget) (*DiagnosisResult, error) {
	hostIP := target.Identifier
	var details []DiagnosisDetail
	var suggestions []string

	tool.logger.WithField("host_ip", hostIP).Info("Starting network connectivity diagnosis")

	// 1. 基本网络连通性检查
	basicConnResult := tool.checkBasicConnectivity(hostIP)
	details = append(details, basicConnResult...)

	// 2. 常用端口扫描
	portScanResult := tool.scanCommonPorts(hostIP)
	details = append(details, portScanResult...)

	// 3. DNS解析检查（如果是域名）
	if !tool.isIPAddress(hostIP) {
		dnsResult := tool.checkDNSResolution(hostIP)
		details = append(details, dnsResult...)
	}

	// 分析结果
	status := "success"
	var failedChecks []string

	for _, detail := range details {
		if detail.Status == "fail" {
			status = "error"
			failedChecks = append(failedChecks, detail.Item)
		}
	}

	if len(failedChecks) > 0 {
		suggestions = append(suggestions, "检查网络连接和路由配置")
		suggestions = append(suggestions, "验证防火墙规则是否阻止了连接")
		suggestions = append(suggestions, "确认目标主机是否在线")
	}

	summary := fmt.Sprintf("网络连通性诊断完成，检查了%d个项目", len(details))
	if status == "error" {
		summary = fmt.Sprintf("网络连通性诊断发现问题，%d个检查项目失败", len(failedChecks))
	}

	return &DiagnosisResult{
		ToolName:    tool.GetName(),
		Target:      target,
		Status:      status,
		Summary:     summary,
		Details:     details,
		Suggestions: suggestions,
		Metadata: map[string]interface{}{
			"host_ip": hostIP,
		},
	}, nil
}

// checkBasicConnectivity 检查基本连通性
func (tool *NetworkConnectivityDiagnosticTool) checkBasicConnectivity(hostIP string) []DiagnosisDetail {
	var details []DiagnosisDetail

	// 尝试TCP连接到常用端口
	testPorts := []int{22, 80, 443}

	for _, port := range testPorts {
		address := fmt.Sprintf("%s:%d", hostIP, port)
		conn, err := net.DialTimeout("tcp", address, 3*time.Second)
		if err == nil {
			conn.Close()
			details = append(details, DiagnosisDetail{
				Category: "network",
				Item:     fmt.Sprintf("TCP连接测试(端口%d)", port),
				Status:   "pass",
				Message:  fmt.Sprintf("端口 %d 连接成功", port),
				Value:    port,
				Severity: "low",
			})
			break // 只要有一个端口通就认为网络连通
		} else {
			details = append(details, DiagnosisDetail{
				Category: "network",
				Item:     fmt.Sprintf("TCP连接测试(端口%d)", port),
				Status:   "fail",
				Message:  fmt.Sprintf("端口 %d 连接失败: %v", port, err),
				Value:    port,
				Severity: "medium",
			})
		}
	}

	return details
}

// scanCommonPorts 扫描常用端口
func (tool *NetworkConnectivityDiagnosticTool) scanCommonPorts(hostIP string) []DiagnosisDetail {
	var details []DiagnosisDetail

	commonPorts := map[int]string{
		22:   "SSH",
		23:   "Telnet",
		80:   "HTTP",
		443:  "HTTPS",
		3389: "RDP",
		5432: "PostgreSQL",
		3306: "MySQL",
		6379: "Redis",
	}

	var openPorts []string
	var closedPorts []string

	for port, service := range commonPorts {
		address := fmt.Sprintf("%s:%d", hostIP, port)
		conn, err := net.DialTimeout("tcp", address, 2*time.Second)
		if err == nil {
			conn.Close()
			openPorts = append(openPorts, fmt.Sprintf("%d(%s)", port, service))
		} else {
			closedPorts = append(closedPorts, fmt.Sprintf("%d(%s)", port, service))
		}
	}

	if len(openPorts) > 0 {
		details = append(details, DiagnosisDetail{
			Category: "network",
			Item:     "端口扫描",
			Status:   "pass",
			Message:  fmt.Sprintf("发现开放端口: %s", strings.Join(openPorts, ", ")),
			Value:    openPorts,
			Severity: "low",
		})
	}

	if len(closedPorts) > 0 {
		details = append(details, DiagnosisDetail{
			Category: "network",
			Item:     "端口扫描",
			Status:   "warning",
			Message:  fmt.Sprintf("关闭的端口: %s", strings.Join(closedPorts, ", ")),
			Value:    closedPorts,
			Severity: "low",
		})
	}

	return details
}

// checkDNSResolution 检查DNS解析
func (tool *NetworkConnectivityDiagnosticTool) checkDNSResolution(hostname string) []DiagnosisDetail {
	var details []DiagnosisDetail

	ips, err := net.LookupIP(hostname)
	if err != nil {
		details = append(details, DiagnosisDetail{
			Category: "dns",
			Item:     "DNS解析",
			Status:   "fail",
			Message:  fmt.Sprintf("DNS解析失败: %v", err),
			Severity: "high",
		})
	} else {
		var ipStrings []string
		for _, ip := range ips {
			ipStrings = append(ipStrings, ip.String())
		}
		details = append(details, DiagnosisDetail{
			Category: "dns",
			Item:     "DNS解析",
			Status:   "pass",
			Message:  fmt.Sprintf("DNS解析成功，IP地址: %s", strings.Join(ipStrings, ", ")),
			Value:    ipStrings,
			Severity: "low",
		})
	}

	return details
}

// isIPAddress 检查是否为IP地址
func (tool *NetworkConnectivityDiagnosticTool) isIPAddress(host string) bool {
	return net.ParseIP(host) != nil
}

// HostStatusDiagnosticTool 主机状态诊断工具
type HostStatusDiagnosticTool struct {
	logger      *logrus.Logger
	hostService HostService
	timeout     time.Duration
}

func (tool *HostStatusDiagnosticTool) GetName() string {
	return "host_status"
}

func (tool *HostStatusDiagnosticTool) GetDescription() string {
	return "诊断主机状态，包括在线状态、系统信息等"
}

func (tool *HostStatusDiagnosticTool) GetCategory() string {
	return "status"
}

func (tool *HostStatusDiagnosticTool) GetSupportedTargets() []string {
	return []string{"host"}
}

func (tool *HostStatusDiagnosticTool) Diagnose(ctx context.Context, target *DiagnosisTarget) (*DiagnosisResult, error) {
	hostIP := target.Identifier
	var details []DiagnosisDetail
	var suggestions []string

	tool.logger.WithField("host_ip", hostIP).Info("Starting host status diagnosis")

	// 获取主机信息
	host, err := tool.getHostByIP(hostIP)
	if err != nil {
		return nil, fmt.Errorf("无法获取主机信息: %w", err)
	}

	// 1. 检查数据库中的状态
	dbStatusResult := tool.checkDatabaseStatus(host)
	details = append(details, dbStatusResult...)

	// 2. 实时连接测试
	liveTestResult := tool.performLiveTest(host)
	details = append(details, liveTestResult...)

	// 3. 状态一致性检查
	consistencyResult := tool.checkStatusConsistency(host, details)
	details = append(details, consistencyResult...)

	// 分析结果
	status := "success"
	var issues []string

	for _, detail := range details {
		if detail.Status == "fail" {
			status = "error"
			issues = append(issues, detail.Message)
		} else if detail.Status == "warning" && status == "success" {
			status = "warning"
		}
	}

	if status == "error" {
		suggestions = append(suggestions, "检查主机是否在线")
		suggestions = append(suggestions, "验证网络连接和SSH服务")
		suggestions = append(suggestions, "更新主机状态信息")
	}

	summary := tool.generateStatusSummary(host, status, issues)

	return &DiagnosisResult{
		ToolName:    tool.GetName(),
		Target:      target,
		Status:      status,
		Summary:     summary,
		Details:     details,
		Suggestions: suggestions,
		Metadata: map[string]interface{}{
			"host_ip":   hostIP,
			"host_name": host.Name,
			"db_status": host.Status,
		},
	}, nil
}

// checkDatabaseStatus 检查数据库状态
func (tool *HostStatusDiagnosticTool) checkDatabaseStatus(host *model.Host) []DiagnosisDetail {
	var details []DiagnosisDetail

	details = append(details, DiagnosisDetail{
		Category: "database",
		Item:     "数据库状态",
		Status:   "pass",
		Message:  fmt.Sprintf("数据库中记录的状态: %s", host.Status),
		Value:    host.Status,
		Severity: "low",
	})

	// 检查最后更新时间
	timeSinceUpdate := time.Since(host.UpdatedAt)
	if timeSinceUpdate > 10*time.Minute {
		details = append(details, DiagnosisDetail{
			Category: "database",
			Item:     "状态更新时间",
			Status:   "warning",
			Message:  fmt.Sprintf("状态信息已过期，最后更新: %s", host.UpdatedAt.Format("2006-01-02 15:04:05")),
			Value:    timeSinceUpdate,
			Severity: "medium",
		})
	} else {
		details = append(details, DiagnosisDetail{
			Category: "database",
			Item:     "状态更新时间",
			Status:   "pass",
			Message:  fmt.Sprintf("状态信息较新，最后更新: %s", host.UpdatedAt.Format("2006-01-02 15:04:05")),
			Value:    timeSinceUpdate,
			Severity: "low",
		})
	}

	return details
}

// performLiveTest 执行实时测试
func (tool *HostStatusDiagnosticTool) performLiveTest(host *model.Host) []DiagnosisDetail {
	var details []DiagnosisDetail

	// 测试SSH连接
	address := fmt.Sprintf("%s:%d", host.IPAddress, host.Port)
	conn, err := net.DialTimeout("tcp", address, tool.timeout)
	if err != nil {
		details = append(details, DiagnosisDetail{
			Category: "live_test",
			Item:     "实时连接测试",
			Status:   "fail",
			Message:  fmt.Sprintf("实时连接失败: %v", err),
			Severity: "high",
		})
	} else {
		conn.Close()
		details = append(details, DiagnosisDetail{
			Category: "live_test",
			Item:     "实时连接测试",
			Status:   "pass",
			Message:  "实时连接成功，主机在线",
			Severity: "low",
		})
	}

	return details
}

// checkStatusConsistency 检查状态一致性
func (tool *HostStatusDiagnosticTool) checkStatusConsistency(host *model.Host, allDetails []DiagnosisDetail) []DiagnosisDetail {
	var details []DiagnosisDetail

	// 查找实时测试结果
	var liveTestPassed bool
	for _, detail := range allDetails {
		if detail.Category == "live_test" && detail.Item == "实时连接测试" {
			liveTestPassed = (detail.Status == "pass")
			break
		}
	}

	dbStatus := host.Status
	actualStatus := "offline"
	if liveTestPassed {
		actualStatus = "online"
	}

	if dbStatus != actualStatus {
		details = append(details, DiagnosisDetail{
			Category: "consistency",
			Item:     "状态一致性检查",
			Status:   "warning",
			Message:  fmt.Sprintf("状态不一致：数据库显示 %s，实际状态 %s", dbStatus, actualStatus),
			Value: map[string]string{
				"database_status": dbStatus,
				"actual_status":   actualStatus,
			},
			Severity: "medium",
		})
	} else {
		details = append(details, DiagnosisDetail{
			Category: "consistency",
			Item:     "状态一致性检查",
			Status:   "pass",
			Message:  fmt.Sprintf("状态一致：%s", actualStatus),
			Value:    actualStatus,
			Severity: "low",
		})
	}

	return details
}

// generateStatusSummary 生成状态摘要
func (tool *HostStatusDiagnosticTool) generateStatusSummary(host *model.Host, status string, issues []string) string {
	if status == "error" {
		return fmt.Sprintf("主机 %s (%s) 状态异常：%s", host.Name, host.IPAddress, strings.Join(issues, "; "))
	} else if status == "warning" {
		return fmt.Sprintf("主机 %s (%s) 状态基本正常，但存在一些警告", host.Name, host.IPAddress)
	}

	return fmt.Sprintf("主机 %s (%s) 状态正常", host.Name, host.IPAddress)
}

// getHostByIP 通过IP地址获取主机信息的辅助方法
func (tool *HostConnectivityDiagnosticTool) getHostByIP(ipAddress string) (*model.Host, error) {
	// 通过HostService获取主机列表，然后筛选
	listReq := &model.HostListQuery{
		Page:  1,
		Limit: 1000, // 设置一个较大的值来获取所有主机
	}

	response, err := tool.hostService.ListHosts(listReq)
	if err != nil {
		return nil, fmt.Errorf("failed to list hosts: %w", err)
	}

	// 在结果中查找匹配的IP
	for _, hostResp := range response.Hosts {
		if hostResp.IPAddress == ipAddress {
			// 转换为model.Host
			host := &model.Host{
				ID:          hostResp.ID,
				Name:        hostResp.Name,
				IPAddress:   hostResp.IPAddress,
				Port:        hostResp.Port,
				Username:    hostResp.Username,
				Description: hostResp.Description,
				Status:      hostResp.Status,
				OSType:      hostResp.OSType,
				OSVersion:   hostResp.OSVersion,
				Environment: hostResp.Environment,
				GroupName:   hostResp.GroupName,
				CreatedAt:   hostResp.CreatedAt,
				UpdatedAt:   hostResp.UpdatedAt,
				CreatedBy:   hostResp.CreatedBy,
			}
			return host, nil
		}
	}

	return nil, fmt.Errorf("host with IP %s not found", ipAddress)
}

// getHostByIP 通过IP地址获取主机信息的辅助方法 (SSHAuthenticationDiagnosticTool)
func (tool *SSHAuthenticationDiagnosticTool) getHostByIP(ipAddress string) (*model.Host, error) {
	// 通过HostService获取主机列表，然后筛选
	listReq := &model.HostListQuery{
		Page:  1,
		Limit: 1000, // 设置一个较大的值来获取所有主机
	}

	response, err := tool.hostService.ListHosts(listReq)
	if err != nil {
		return nil, fmt.Errorf("failed to list hosts: %w", err)
	}

	// 在结果中查找匹配的IP
	for _, hostResp := range response.Hosts {
		if hostResp.IPAddress == ipAddress {
			// 转换为model.Host
			host := &model.Host{
				ID:          hostResp.ID,
				Name:        hostResp.Name,
				IPAddress:   hostResp.IPAddress,
				Port:        hostResp.Port,
				Username:    hostResp.Username,
				Description: hostResp.Description,
				Status:      hostResp.Status,
				OSType:      hostResp.OSType,
				OSVersion:   hostResp.OSVersion,
				Environment: hostResp.Environment,
				GroupName:   hostResp.GroupName,
				CreatedAt:   hostResp.CreatedAt,
				UpdatedAt:   hostResp.UpdatedAt,
				CreatedBy:   hostResp.CreatedBy,
			}
			return host, nil
		}
	}

	return nil, fmt.Errorf("host with IP %s not found", ipAddress)
}

// getHostByIP 通过IP地址获取主机信息的辅助方法 (HostStatusDiagnosticTool)
func (tool *HostStatusDiagnosticTool) getHostByIP(ipAddress string) (*model.Host, error) {
	// 通过HostService获取主机列表，然后筛选
	listReq := &model.HostListQuery{
		Page:  1,
		Limit: 1000, // 设置一个较大的值来获取所有主机
	}

	response, err := tool.hostService.ListHosts(listReq)
	if err != nil {
		return nil, fmt.Errorf("failed to list hosts: %w", err)
	}

	// 在结果中查找匹配的IP
	for _, hostResp := range response.Hosts {
		if hostResp.IPAddress == ipAddress {
			// 转换为model.Host
			host := &model.Host{
				ID:          hostResp.ID,
				Name:        hostResp.Name,
				IPAddress:   hostResp.IPAddress,
				Port:        hostResp.Port,
				Username:    hostResp.Username,
				Description: hostResp.Description,
				Status:      hostResp.Status,
				OSType:      hostResp.OSType,
				OSVersion:   hostResp.OSVersion,
				Environment: hostResp.Environment,
				GroupName:   hostResp.GroupName,
				CreatedAt:   hostResp.CreatedAt,
				UpdatedAt:   hostResp.UpdatedAt,
				CreatedBy:   hostResp.CreatedBy,
			}
			return host, nil
		}
	}

	return nil, fmt.Errorf("host with IP %s not found", ipAddress)
}
