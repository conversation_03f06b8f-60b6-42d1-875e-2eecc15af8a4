# 简化意图识别架构设计

## 🎯 设计目标

将现有的13+种细分意图重新归类为4个主要操作类别，大幅简化DeepSeek的识别难度，提高准确率和系统可维护性。

## 📋 新的四大类意图架构

### 1. database_operations - 数据库操作类
**职责**：处理所有与数据库CRUD相关的操作
**包含的原有意图**：
- host_management (主机增删改查)
- alert_management (告警管理)
- user_management (用户管理)
- report_generation (报表生成的数据查询部分)

**参数结构**：
```json
{
  "type": "database_operations",
  "confidence": 0.95,
  "parameters": {
    "operation": "delete|insert|update|select",
    "table": "hosts|users|alerts|operation_logs",
    "target": {
      "ip": "*************",
      "name": "test-server",
      "id": 123
    },
    "data": {
      "field1": "value1",
      "field2": "value2"
    },
    "conditions": {
      "environment": "production",
      "status": "active"
    }
  }
}
```

### 2. ssh_operations - SSH远程操作类
**职责**：处理所有需要SSH连接的远程操作
**包含的原有意图**：
- command_execution (远程命令执行)
- service_management (服务启停)
- file_operations (文件操作)
- security_check (安全检查)
- backup_restore (备份恢复)

**参数结构**：
```json
{
  "type": "ssh_operations", 
  "confidence": 0.92,
  "parameters": {
    "operation": "execute_command|manage_service|file_operation|security_check",
    "target_host": {
      "ip": "*************",
      "name": "web-server"
    },
    "command": "systemctl restart nginx",
    "service": "nginx",
    "action": "restart|start|stop|status",
    "file_path": "/var/log/nginx/access.log",
    "timeout": 30
  }
}
```

### 3. monitoring_operations - 监控统计类
**职责**：处理系统监控、性能分析、统计报表等只读操作
**包含的原有意图**：
- system_monitoring (系统监控)
- performance_analysis (性能分析)
- log_analysis (日志分析)
- network_diagnostics (网络诊断)
- report_generation (报表生成的展示部分)

**参数结构**：
```json
{
  "type": "monitoring_operations",
  "confidence": 0.88,
  "parameters": {
    "operation": "system_monitor|performance_analysis|log_analysis|network_diagnosis",
    "metric": "cpu|memory|disk|network|load",
    "target_host": {
      "ip": "*************",
      "scope": "all|specific"
    },
    "time_range": "1h|24h|7d",
    "log_type": "system|error|access",
    "keyword": "failed|error|warning"
  }
}
```

### 4. general_chat - 通用对话类
**职责**：处理一般性对话、帮助信息、不明确的请求
**包含的原有意图**：
- general_chat (一般对话)
- help (帮助信息)
- unknown (未知意图)

**参数结构**：
```json
{
  "type": "general_chat",
  "confidence": 0.6,
  "parameters": {
    "intent": "help|greeting|unclear|question",
    "topic": "host_management|monitoring|commands",
    "original_input": "用户的原始输入"
  }
}
```

## 🔄 意图映射表

| 原有意图 | 新意图类别 | 操作参数 |
|---------|-----------|----------|
| host_management (add_host) | database_operations | operation: "insert", table: "hosts" |
| host_management (delete_host) | database_operations | operation: "delete", table: "hosts" |
| host_management (list_hosts) | database_operations | operation: "select", table: "hosts" |
| command_execution | ssh_operations | operation: "execute_command" |
| service_management | ssh_operations | operation: "manage_service" |
| system_monitoring | monitoring_operations | operation: "system_monitor" |
| log_analysis | monitoring_operations | operation: "log_analysis" |
| alert_management | database_operations | operation: "select/update", table: "alerts" |

## 💡 架构优势

1. **简化识别**：DeepSeek只需要区分4种主要类别，而不是13+种细分意图
2. **参数化处理**：具体的子操作通过参数传递，在处理器内部分发
3. **易于维护**：减少了大量重复代码，集中处理相似逻辑
4. **扩展性强**：新增操作只需要在对应类别内添加参数支持
5. **降级友好**：Mock AI服务也更容易实现这4种基础分类

## 🚀 实施计划

1. **重构DeepSeek提示词** - 更新系统提示词，专注于4大类识别
2. **实现新的处理器** - 为每个类别实现统一的处理器
3. **更新Mock AI** - 同步更新降级服务的识别逻辑
4. **测试验证** - 确保所有原有功能在新架构下正常工作
