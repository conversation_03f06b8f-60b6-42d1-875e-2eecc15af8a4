package model

import (
	"time"

	"gorm.io/gorm"
)

// WorkflowDefinition 工作流定义模型
type WorkflowDefinition struct {
	ID          string         `json:"id" gorm:"primaryKey;size:100"`
	Name        string         `json:"name" gorm:"size:200;not null"`
	Description string         `json:"description" gorm:"type:text"`
	Version     string         `json:"version" gorm:"size:20;not null"`
	Category    string         `json:"category" gorm:"size:50;not null"`
	Definition  string         `json:"-" gorm:"type:text;not null"` // JSON格式的工作流定义
	Enabled     bool           `json:"enabled" gorm:"default:true"`
	CreatedAt   time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt   time.Time      `json:"updated_at" gorm:"not null"`
	CreatedBy   int64          `json:"created_by" gorm:"not null"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Creator   User               `json:"creator" gorm:"foreignKey:CreatedBy"`
	Instances []WorkflowInstance `json:"-" gorm:"foreignKey:DefinitionID"`
}

// TableName 指定表名
func (WorkflowDefinition) TableName() string {
	return "workflow_definitions"
}

// BeforeCreate GORM钩子：创建前
func (wd *WorkflowDefinition) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if wd.CreatedAt.IsZero() {
		wd.CreatedAt = now
	}
	wd.UpdatedAt = now
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (wd *WorkflowDefinition) BeforeUpdate(tx *gorm.DB) error {
	wd.UpdatedAt = time.Now()
	return nil
}

// WorkflowInstance 工作流实例模型
type WorkflowInstance struct {
	ID            string         `json:"id" gorm:"primaryKey;size:100"`
	DefinitionID  string         `json:"definition_id" gorm:"size:100;not null"`
	SessionID     string         `json:"session_id" gorm:"size:36;not null"`
	UserID        int64          `json:"user_id" gorm:"not null"`
	Status        string         `json:"status" gorm:"size:20;not null"`
	CurrentStep   string         `json:"current_step" gorm:"size:100"`
	Variables     string         `json:"-" gorm:"type:text"` // JSON格式的变量
	CollectedData string         `json:"-" gorm:"type:text"` // JSON格式的收集数据
	ExecutionPath string         `json:"-" gorm:"type:text"` // JSON格式的执行路径
	StartTime     time.Time      `json:"start_time" gorm:"not null"`
	EndTime       *time.Time     `json:"end_time"`
	LastActivity  time.Time      `json:"last_activity" gorm:"not null"`
	ErrorMessage  string         `json:"error_message" gorm:"type:text"`
	RetryCount    int            `json:"retry_count" gorm:"default:0"`
	Priority      int            `json:"priority" gorm:"default:0"`
	Metadata      string         `json:"-" gorm:"type:text"` // JSON格式的元数据
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Definition WorkflowDefinition      `json:"definition" gorm:"foreignKey:DefinitionID"`
	User       User                    `json:"user" gorm:"foreignKey:UserID"`
	Events     []WorkflowEvent         `json:"-" gorm:"foreignKey:InstanceID"`
	Steps      []WorkflowStepExecution `json:"-" gorm:"foreignKey:InstanceID"`
}

// TableName 指定表名
func (WorkflowInstance) TableName() string {
	return "workflow_instances"
}

// BeforeCreate GORM钩子：创建前
func (wi *WorkflowInstance) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if wi.StartTime.IsZero() {
		wi.StartTime = now
	}
	wi.LastActivity = now
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (wi *WorkflowInstance) BeforeUpdate(tx *gorm.DB) error {
	wi.LastActivity = time.Now()
	return nil
}

// WorkflowEvent 工作流事件模型
type WorkflowEvent struct {
	ID         int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	InstanceID string         `json:"instance_id" gorm:"size:100;not null"`
	StepID     string         `json:"step_id" gorm:"size:100"`
	EventType  string         `json:"event_type" gorm:"size:50;not null"`
	Status     string         `json:"status" gorm:"size:20"`
	Data       string         `json:"-" gorm:"type:text"` // JSON格式的事件数据
	Message    string         `json:"message" gorm:"type:text"`
	CreatedAt  time.Time      `json:"created_at" gorm:"not null"`
	UserID     int64          `json:"user_id"`
	SessionID  string         `json:"session_id" gorm:"size:36"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Instance WorkflowInstance `json:"instance" gorm:"foreignKey:InstanceID"`
	User     User             `json:"user" gorm:"foreignKey:UserID"`
}

// TableName 指定表名
func (WorkflowEvent) TableName() string {
	return "workflow_events"
}

// BeforeCreate GORM钩子：创建前
func (we *WorkflowEvent) BeforeCreate(tx *gorm.DB) error {
	if we.CreatedAt.IsZero() {
		we.CreatedAt = time.Now()
	}
	return nil
}

// WorkflowStepExecution 工作流步骤执行记录
type WorkflowStepExecution struct {
	ID         int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	InstanceID string         `json:"instance_id" gorm:"size:100;not null"`
	StepID     string         `json:"step_id" gorm:"size:100;not null"`
	StepName   string         `json:"step_name" gorm:"size:200"`
	StepType   string         `json:"step_type" gorm:"size:50"`
	Status     string         `json:"status" gorm:"size:20;not null"`
	StartTime  time.Time      `json:"start_time" gorm:"not null"`
	EndTime    *time.Time     `json:"end_time"`
	Duration   int64          `json:"duration"`           // 毫秒
	InputData  string         `json:"-" gorm:"type:text"` // JSON格式的输入数据
	OutputData string         `json:"-" gorm:"type:text"` // JSON格式的输出数据
	ErrorMsg   string         `json:"error_message" gorm:"type:text"`
	RetryCount int            `json:"retry_count" gorm:"default:0"`
	CreatedAt  time.Time      `json:"created_at" gorm:"not null"`
	DeletedAt  gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Instance WorkflowInstance `json:"instance" gorm:"foreignKey:InstanceID"`
}

// TableName 指定表名
func (WorkflowStepExecution) TableName() string {
	return "workflow_step_executions"
}

// BeforeCreate GORM钩子：创建前
func (wse *WorkflowStepExecution) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if wse.StartTime.IsZero() {
		wse.StartTime = now
	}
	if wse.CreatedAt.IsZero() {
		wse.CreatedAt = now
	}
	return nil
}

// WorkflowTemplate 工作流模板模型
type WorkflowTemplate struct {
	ID          string         `json:"id" gorm:"primaryKey;size:100"`
	Name        string         `json:"name" gorm:"size:200;not null"`
	Description string         `json:"description" gorm:"type:text"`
	Category    string         `json:"category" gorm:"size:50;not null"`
	Icon        string         `json:"icon" gorm:"size:100"`
	Tags        string         `json:"-" gorm:"type:text"`          // JSON格式的标签数组
	Variables   string         `json:"-" gorm:"type:text"`          // JSON格式的变量
	Definition  string         `json:"-" gorm:"type:text;not null"` // JSON格式的工作流定义
	IsPublic    bool           `json:"is_public" gorm:"default:false"`
	UsageCount  int64          `json:"usage_count" gorm:"default:0"`
	Rating      float64        `json:"rating" gorm:"default:0"`
	RatingCount int64          `json:"rating_count" gorm:"default:0"`
	CreatedAt   time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt   time.Time      `json:"updated_at" gorm:"not null"`
	CreatedBy   int64          `json:"created_by" gorm:"not null"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Creator User `json:"creator" gorm:"foreignKey:CreatedBy"`
}

// TableName 指定表名
func (WorkflowTemplate) TableName() string {
	return "workflow_templates"
}

// BeforeCreate GORM钩子：创建前
func (wt *WorkflowTemplate) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if wt.CreatedAt.IsZero() {
		wt.CreatedAt = now
	}
	wt.UpdatedAt = now
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (wt *WorkflowTemplate) BeforeUpdate(tx *gorm.DB) error {
	wt.UpdatedAt = time.Now()
	return nil
}

// WorkflowStatistics 工作流统计模型
type WorkflowStatistics struct {
	ID              int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	DefinitionID    string         `json:"definition_id" gorm:"size:100;not null;uniqueIndex"`
	TotalExecutions int64          `json:"total_executions" gorm:"default:0"`
	SuccessfulRuns  int64          `json:"successful_runs" gorm:"default:0"`
	FailedRuns      int64          `json:"failed_runs" gorm:"default:0"`
	AverageRuntime  int64          `json:"average_runtime" gorm:"default:0"` // 毫秒
	LastExecution   *time.Time     `json:"last_execution"`
	PopularSteps    string         `json:"-" gorm:"type:text"` // JSON格式的热门步骤
	CommonFailures  string         `json:"-" gorm:"type:text"` // JSON格式的常见失败原因
	CreatedAt       time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt       time.Time      `json:"updated_at" gorm:"not null"`
	DeletedAt       gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Definition WorkflowDefinition `json:"definition" gorm:"foreignKey:DefinitionID"`
}

// TableName 指定表名
func (WorkflowStatistics) TableName() string {
	return "workflow_statistics"
}

// BeforeCreate GORM钩子：创建前
func (ws *WorkflowStatistics) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if ws.CreatedAt.IsZero() {
		ws.CreatedAt = now
	}
	ws.UpdatedAt = now
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (ws *WorkflowStatistics) BeforeUpdate(tx *gorm.DB) error {
	ws.UpdatedAt = time.Now()
	return nil
}

// WorkflowUserPreference 用户工作流偏好
type WorkflowUserPreference struct {
	ID           int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID       int64          `json:"user_id" gorm:"not null"`
	DefinitionID string         `json:"definition_id" gorm:"size:100;not null"`
	IsFavorite   bool           `json:"is_favorite" gorm:"default:false"`
	UsageCount   int64          `json:"usage_count" gorm:"default:0"`
	LastUsed     *time.Time     `json:"last_used"`
	Settings     string         `json:"-" gorm:"type:text"` // JSON格式的个人设置
	CreatedAt    time.Time      `json:"created_at" gorm:"not null"`
	UpdatedAt    time.Time      `json:"updated_at" gorm:"not null"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	User       User               `json:"user" gorm:"foreignKey:UserID"`
	Definition WorkflowDefinition `json:"definition" gorm:"foreignKey:DefinitionID"`
}

// TableName 指定表名
func (WorkflowUserPreference) TableName() string {
	return "workflow_user_preferences"
}

// BeforeCreate GORM钩子：创建前
func (wup *WorkflowUserPreference) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if wup.CreatedAt.IsZero() {
		wup.CreatedAt = now
	}
	wup.UpdatedAt = now
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (wup *WorkflowUserPreference) BeforeUpdate(tx *gorm.DB) error {
	wup.UpdatedAt = time.Now()
	return nil
}

// 添加索引
func init() {
	// 这些索引将在数据库迁移时创建
	// workflow_instances 表的索引
	// - idx_workflow_instances_session_id
	// - idx_workflow_instances_user_id
	// - idx_workflow_instances_status
	// - idx_workflow_instances_definition_id
	// - idx_workflow_instances_last_activity

	// workflow_events 表的索引
	// - idx_workflow_events_instance_id
	// - idx_workflow_events_event_type
	// - idx_workflow_events_created_at

	// workflow_step_executions 表的索引
	// - idx_workflow_step_executions_instance_id
	// - idx_workflow_step_executions_step_id
	// - idx_workflow_step_executions_status
}
