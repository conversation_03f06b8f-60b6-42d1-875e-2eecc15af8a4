/* AI运维管理平台 - 高级动画效果系统 */
/* 现代化视觉增强和微交互动画 */

/* ========================================
   粒子背景效果
   ======================================== */

.particles-container {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: var(--z-0);
  overflow: hidden;
}

.particle {
  position: absolute;
  width: 2px;
  height: 2px;
  background: var(--color-primary-400);
  border-radius: var(--radius-full);
  opacity: 0.6;
  animation: float 20s infinite linear;
}

.particle:nth-child(odd) {
  background: var(--color-accent-400);
  animation-duration: 25s;
}

.particle:nth-child(3n) {
  background: var(--color-primary-300);
  animation-duration: 30s;
  width: 1px;
  height: 1px;
}

@keyframes float {
  0% {
    transform: translateY(100vh) translateX(0) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 0.6;
  }
  90% {
    opacity: 0.6;
  }
  100% {
    transform: translateY(-100px) translateX(100px) rotate(360deg);
    opacity: 0;
  }
}

/* ========================================
   渐变背景动画
   ======================================== */

.animated-gradient {
  background: linear-gradient(
    -45deg,
    var(--color-primary-100),
    var(--color-primary-200),
    var(--color-accent-100),
    var(--color-primary-300)
  );
  background-size: 400% 400%;
  animation: gradientShift 15s ease infinite;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

/* ========================================
   高级按钮动画
   ======================================== */

.btn-animated {
  position: relative;
  overflow: hidden;
  transform: translateZ(0);
}

.btn-animated::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.3),
    transparent
  );
  transition: left var(--duration-700) var(--ease-out);
}

.btn-animated:hover::before {
  left: 100%;
}

/* 脉冲效果 */
.btn-pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(99, 102, 241, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(99, 102, 241, 0);
  }
}

/* 弹跳效果 */
.btn-bounce:hover {
  animation: bounce 0.6s ease-in-out;
}

@keyframes bounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-10px);
  }
  80% {
    transform: translateY(-5px);
  }
}

/* ========================================
   卡片高级动画
   ======================================== */

.card-flip {
  perspective: 1000px;
}

.card-flip-inner {
  position: relative;
  width: 100%;
  height: 100%;
  text-align: center;
  transition: transform var(--duration-700) var(--ease-out);
  transform-style: preserve-3d;
}

.card-flip:hover .card-flip-inner {
  transform: rotateY(180deg);
}

.card-flip-front,
.card-flip-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: inherit;
}

.card-flip-back {
  transform: rotateY(180deg);
}

/* 卡片悬浮效果 */
.card-float {
  animation: float-gentle 6s ease-in-out infinite;
}

@keyframes float-gentle {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* ========================================
   文字动画效果
   ======================================== */

.text-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    text-shadow: 0 0 5px var(--color-primary-400),
                 0 0 10px var(--color-primary-400),
                 0 0 15px var(--color-primary-400);
  }
  to {
    text-shadow: 0 0 10px var(--color-primary-500),
                 0 0 20px var(--color-primary-500),
                 0 0 30px var(--color-primary-500);
  }
}

.text-typewriter {
  overflow: hidden;
  border-right: 2px solid var(--color-primary-500);
  white-space: nowrap;
  animation: typewriter 3s steps(40, end), blink-caret 0.75s step-end infinite;
}

@keyframes typewriter {
  from {
    width: 0;
  }
  to {
    width: 100%;
  }
}

@keyframes blink-caret {
  from, to {
    border-color: transparent;
  }
  50% {
    border-color: var(--color-primary-500);
  }
}

/* ========================================
   加载动画增强
   ======================================== */

.loading-wave {
  display: inline-flex;
  gap: var(--spacing-1);
}

.loading-wave .dot {
  width: var(--spacing-2);
  height: var(--spacing-2);
  background: var(--color-primary-500);
  border-radius: var(--radius-full);
  animation: wave 1.4s ease-in-out infinite both;
}

.loading-wave .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-wave .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-wave .dot:nth-child(3) { animation-delay: 0s; }

@keyframes wave {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

/* 骨架屏动画 */
.skeleton {
  background: linear-gradient(
    90deg,
    var(--color-gray-200) 25%,
    var(--color-gray-100) 50%,
    var(--color-gray-200) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* ========================================
   页面转场动画
   ======================================== */

.page-enter {
  animation: pageEnter var(--duration-700) var(--ease-out);
}

@keyframes pageEnter {
  from {
    opacity: 0;
    transform: translateY(var(--spacing-8)) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.page-exit {
  animation: pageExit var(--duration-500) var(--ease-in);
}

@keyframes pageExit {
  from {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
  to {
    opacity: 0;
    transform: translateY(-var(--spacing-8)) scale(0.95);
  }
}

/* ========================================
   滚动触发动画
   ======================================== */

.scroll-reveal {
  opacity: 0;
  transform: translateY(var(--spacing-8));
  transition: all var(--duration-700) var(--ease-out);
}

.scroll-reveal.revealed {
  opacity: 1;
  transform: translateY(0);
}

.scroll-reveal-left {
  opacity: 0;
  transform: translateX(-var(--spacing-8));
  transition: all var(--duration-700) var(--ease-out);
}

.scroll-reveal-left.revealed {
  opacity: 1;
  transform: translateX(0);
}

.scroll-reveal-right {
  opacity: 0;
  transform: translateX(var(--spacing-8));
  transition: all var(--duration-700) var(--ease-out);
}

.scroll-reveal-right.revealed {
  opacity: 1;
  transform: translateX(0);
}

/* ========================================
   特殊效果
   ======================================== */

/* 霓虹灯效果 */
.neon-glow {
  color: var(--color-primary-400);
  text-shadow: 
    0 0 5px var(--color-primary-400),
    0 0 10px var(--color-primary-400),
    0 0 20px var(--color-primary-400),
    0 0 40px var(--color-primary-400);
  animation: neon-flicker 2s infinite alternate;
}

@keyframes neon-flicker {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

/* 玻璃态效果 */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(var(--blur-lg)) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
}

/* 磁性悬停效果 */
.magnetic-hover {
  transition: transform var(--duration-300) var(--ease-out);
}

.magnetic-hover:hover {
  transform: scale(1.05) rotate(1deg);
}

/* ========================================
   性能优化
   ======================================== */

/* 硬件加速 */
.gpu-accelerated {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* 减少重绘 */
.will-change-transform {
  will-change: transform;
}

.will-change-opacity {
  will-change: opacity;
}

/* 动画性能优化 */
@media (prefers-reduced-motion: reduce) {
  .particles-container,
  .animated-gradient,
  .card-float,
  .text-glow,
  .neon-glow {
    animation: none !important;
  }
  
  .btn-animated::before,
  .card-flip-inner,
  .scroll-reveal,
  .scroll-reveal-left,
  .scroll-reveal-right {
    transition: none !important;
  }
}
