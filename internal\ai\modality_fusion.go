package ai

import (
	"fmt"
	"math"
	"time"

	"github.com/sirupsen/logrus"
)

// ModalityFusion 模态融合器
type ModalityFusion struct {
	logger         *logrus.Logger
	config         *MultimodalConfig
	fusionStrategy FusionStrategy
	weights        map[InteractionMode]float64
}

// FusionStrategy 融合策略
type FusionStrategy string

const (
	FusionWeightedAverage FusionStrategy = "weighted_average"
	FusionMaxConfidence   FusionStrategy = "max_confidence"
	FusionVoting          FusionStrategy = "voting"
	FusionDeepFusion      FusionStrategy = "deep_fusion"
	FusionAdaptive        FusionStrategy = "adaptive"
)

// FusedInput 融合后的输入
type FusedInput struct {
	Content        string                 `json:"content"`
	Confidence     float64                `json:"confidence"`
	PrimaryMode    InteractionMode        `json:"primary_mode"`
	ContributingModes []InteractionMode   `json:"contributing_modes"`
	FusionMethod   FusionStrategy         `json:"fusion_method"`
	Weights        map[InteractionMode]float64 `json:"weights"`
	Metadata       map[string]interface{} `json:"metadata"`
	ProcessingTime time.Duration          `json:"processing_time"`
}

// ModalInput 模态输入
type ModalInput struct {
	Mode       InteractionMode        `json:"mode"`
	Content    interface{}            `json:"content"`
	Confidence float64                `json:"confidence"`
	Weight     float64                `json:"weight"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// NewModalityFusion 创建模态融合器
func NewModalityFusion(logger *logrus.Logger, config *MultimodalConfig) *ModalityFusion {
	fusion := &ModalityFusion{
		logger:         logger,
		config:         config,
		fusionStrategy: FusionStrategy(config.FusionStrategy),
		weights:        make(map[InteractionMode]float64),
	}

	// 初始化默认权重
	fusion.initializeWeights()

	logger.Info("🔀 模态融合器初始化完成")
	return fusion
}

// FuseInputs 融合多模态输入
func (mf *ModalityFusion) FuseInputs(inputs map[InteractionMode]interface{}) (interface{}, error) {
	start := time.Now()

	mf.logger.WithFields(logrus.Fields{
		"input_modes": len(inputs),
		"strategy":    mf.fusionStrategy,
	}).Info("🔀 开始模态融合")

	if len(inputs) == 0 {
		return nil, fmt.Errorf("no inputs to fuse")
	}

	// 如果只有一个输入，直接返回
	if len(inputs) == 1 {
		for mode, input := range inputs {
			return mf.createSingleModalFusedInput(mode, input, time.Since(start)), nil
		}
	}

	// 转换为标准化的模态输入
	modalInputs, err := mf.normalizeInputs(inputs)
	if err != nil {
		return nil, fmt.Errorf("failed to normalize inputs: %w", err)
	}

	// 根据策略进行融合
	var fusedInput *FusedInput
	switch mf.fusionStrategy {
	case FusionWeightedAverage:
		fusedInput = mf.weightedAverageFusion(modalInputs)
	case FusionMaxConfidence:
		fusedInput = mf.maxConfidenceFusion(modalInputs)
	case FusionVoting:
		fusedInput = mf.votingFusion(modalInputs)
	case FusionDeepFusion:
		fusedInput = mf.deepFusion(modalInputs)
	case FusionAdaptive:
		fusedInput = mf.adaptiveFusion(modalInputs)
	default:
		fusedInput = mf.weightedAverageFusion(modalInputs)
	}

	fusedInput.ProcessingTime = time.Since(start)

	mf.logger.WithFields(logrus.Fields{
		"fusion_method":      fusedInput.FusionMethod,
		"primary_mode":       fusedInput.PrimaryMode,
		"contributing_modes": fusedInput.ContributingModes,
		"confidence":         fusedInput.Confidence,
		"processing_time":    fusedInput.ProcessingTime,
	}).Info("🔀 模态融合完成")

	return fusedInput, nil
}

// SetWeights 设置模态权重
func (mf *ModalityFusion) SetWeights(weights map[InteractionMode]float64) {
	mf.weights = weights
	mf.logger.WithField("weights", weights).Info("更新模态权重")
}

// GetWeights 获取当前权重
func (mf *ModalityFusion) GetWeights() map[InteractionMode]float64 {
	return mf.weights
}

// 私有方法

func (mf *ModalityFusion) initializeWeights() {
	// 设置默认权重
	mf.weights[ModeText] = 1.0
	mf.weights[ModeSpeech] = 0.9
	mf.weights[ModeImage] = 0.8
	mf.weights[ModeGesture] = 0.6
	mf.weights[ModeVideo] = 0.7
}

func (mf *ModalityFusion) normalizeInputs(inputs map[InteractionMode]interface{}) ([]ModalInput, error) {
	modalInputs := make([]ModalInput, 0, len(inputs))

	for mode, input := range inputs {
		modalInput, err := mf.convertToModalInput(mode, input)
		if err != nil {
			mf.logger.WithError(err).WithField("mode", mode).Warn("Failed to convert input")
			continue
		}
		modalInputs = append(modalInputs, *modalInput)
	}

	if len(modalInputs) == 0 {
		return nil, fmt.Errorf("no valid modal inputs after normalization")
	}

	return modalInputs, nil
}

func (mf *ModalityFusion) convertToModalInput(mode InteractionMode, input interface{}) (*ModalInput, error) {
	modalInput := &ModalInput{
		Mode:     mode,
		Content:  input,
		Weight:   mf.weights[mode],
		Metadata: make(map[string]interface{}),
	}

	// 根据模态类型提取内容和置信度
	switch mode {
	case ModeText:
		if textData, ok := input.(map[string]interface{}); ok {
			modalInput.Content = textData["content"]
			if conf, exists := textData["confidence"]; exists {
				if confFloat, ok := conf.(float64); ok {
					modalInput.Confidence = confFloat
				} else {
					modalInput.Confidence = 1.0 // 文本默认高置信度
				}
			} else {
				modalInput.Confidence = 1.0
			}
		}

	case ModeSpeech:
		if speechData, ok := input.(map[string]interface{}); ok {
			modalInput.Content = speechData["transcription"]
			if conf, exists := speechData["confidence"]; exists {
				if confFloat, ok := conf.(float64); ok {
					modalInput.Confidence = confFloat
				} else {
					modalInput.Confidence = 0.8
				}
			} else {
				modalInput.Confidence = 0.8
			}
		}

	case ModeImage:
		if imageData, ok := input.(*ImageProcessingResult); ok {
			// 从图像处理结果中提取文本内容
			content := ""
			for _, text := range imageData.ExtractedText {
				content += text.Text + " "
			}
			modalInput.Content = content
			modalInput.Confidence = imageData.Confidence
		}

	case ModeGesture:
		if gestureData, ok := input.(map[string]interface{}); ok {
			modalInput.Content = gestureData["gesture_type"]
			if conf, exists := gestureData["confidence"]; exists {
				if confFloat, ok := conf.(float64); ok {
					modalInput.Confidence = confFloat
				} else {
					modalInput.Confidence = 0.7
				}
			} else {
				modalInput.Confidence = 0.7
			}
		}

	case ModeVideo:
		if videoData, ok := input.(map[string]interface{}); ok {
			modalInput.Content = videoData["description"]
			if conf, exists := videoData["confidence"]; exists {
				if confFloat, ok := conf.(float64); ok {
					modalInput.Confidence = confFloat
				} else {
					modalInput.Confidence = 0.6
				}
			} else {
				modalInput.Confidence = 0.6
			}
		}

	default:
		return nil, fmt.Errorf("unsupported interaction mode: %s", mode)
	}

	return modalInput, nil
}

func (mf *ModalityFusion) createSingleModalFusedInput(mode InteractionMode, input interface{}, processingTime time.Duration) *FusedInput {
	modalInput, _ := mf.convertToModalInput(mode, input)
	
	return &FusedInput{
		Content:           fmt.Sprintf("%v", modalInput.Content),
		Confidence:        modalInput.Confidence,
		PrimaryMode:       mode,
		ContributingModes: []InteractionMode{mode},
		FusionMethod:      "single_modal",
		Weights:           map[InteractionMode]float64{mode: 1.0},
		Metadata: map[string]interface{}{
			"single_modal": true,
		},
		ProcessingTime: processingTime,
	}
}

func (mf *ModalityFusion) weightedAverageFusion(inputs []ModalInput) *FusedInput {
	totalWeight := 0.0
	weightedConfidence := 0.0
	contents := make([]string, 0)
	contributingModes := make([]InteractionMode, 0)
	weights := make(map[InteractionMode]float64)

	for _, input := range inputs {
		weight := input.Weight * input.Confidence
		totalWeight += weight
		weightedConfidence += input.Confidence * weight
		contents = append(contents, fmt.Sprintf("%v", input.Content))
		contributingModes = append(contributingModes, input.Mode)
		weights[input.Mode] = weight
	}

	// 标准化权重
	if totalWeight > 0 {
		for mode := range weights {
			weights[mode] /= totalWeight
		}
		weightedConfidence /= totalWeight
	}

	// 选择主要模态（权重最高的）
	primaryMode := mf.selectPrimaryMode(weights)

	// 合并内容
	fusedContent := mf.combineContents(contents, weights, contributingModes)

	return &FusedInput{
		Content:           fusedContent,
		Confidence:        weightedConfidence,
		PrimaryMode:       primaryMode,
		ContributingModes: contributingModes,
		FusionMethod:      FusionWeightedAverage,
		Weights:           weights,
		Metadata: map[string]interface{}{
			"total_weight": totalWeight,
			"input_count":  len(inputs),
		},
	}
}

func (mf *ModalityFusion) maxConfidenceFusion(inputs []ModalInput) *FusedInput {
	var bestInput *ModalInput
	maxConfidence := 0.0

	for i, input := range inputs {
		if input.Confidence > maxConfidence {
			maxConfidence = input.Confidence
			bestInput = &inputs[i]
		}
	}

	contributingModes := make([]InteractionMode, len(inputs))
	weights := make(map[InteractionMode]float64)
	for i, input := range inputs {
		contributingModes[i] = input.Mode
		if input.Mode == bestInput.Mode {
			weights[input.Mode] = 1.0
		} else {
			weights[input.Mode] = 0.0
		}
	}

	return &FusedInput{
		Content:           fmt.Sprintf("%v", bestInput.Content),
		Confidence:        bestInput.Confidence,
		PrimaryMode:       bestInput.Mode,
		ContributingModes: contributingModes,
		FusionMethod:      FusionMaxConfidence,
		Weights:           weights,
		Metadata: map[string]interface{}{
			"max_confidence": maxConfidence,
			"selected_mode":  bestInput.Mode,
		},
	}
}

func (mf *ModalityFusion) votingFusion(inputs []ModalInput) *FusedInput {
	// 简化的投票融合：基于内容相似性
	contentVotes := make(map[string]float64)
	modeVotes := make(map[InteractionMode]float64)

	for _, input := range inputs {
		content := fmt.Sprintf("%v", input.Content)
		contentVotes[content] += input.Confidence * input.Weight
		modeVotes[input.Mode] += input.Confidence * input.Weight
	}

	// 选择得票最高的内容
	var winningContent string
	maxVotes := 0.0
	for content, votes := range contentVotes {
		if votes > maxVotes {
			maxVotes = votes
			winningContent = content
		}
	}

	// 选择主要模态
	primaryMode := mf.selectPrimaryMode(modeVotes)

	contributingModes := make([]InteractionMode, len(inputs))
	weights := make(map[InteractionMode]float64)
	for i, input := range inputs {
		contributingModes[i] = input.Mode
		weights[input.Mode] = modeVotes[input.Mode] / maxVotes
	}

	return &FusedInput{
		Content:           winningContent,
		Confidence:        maxVotes / float64(len(inputs)),
		PrimaryMode:       primaryMode,
		ContributingModes: contributingModes,
		FusionMethod:      FusionVoting,
		Weights:           weights,
		Metadata: map[string]interface{}{
			"max_votes":     maxVotes,
			"content_votes": len(contentVotes),
		},
	}
}

func (mf *ModalityFusion) deepFusion(inputs []ModalInput) *FusedInput {
	// 深度融合：考虑模态间的互补性和一致性
	
	// 计算模态间的一致性
	consistency := mf.calculateModalConsistency(inputs)
	
	// 基于一致性调整权重
	adjustedWeights := make(map[InteractionMode]float64)
	totalWeight := 0.0
	
	for _, input := range inputs {
		adjustedWeight := input.Weight * input.Confidence * consistency
		adjustedWeights[input.Mode] = adjustedWeight
		totalWeight += adjustedWeight
	}

	// 标准化权重
	if totalWeight > 0 {
		for mode := range adjustedWeights {
			adjustedWeights[mode] /= totalWeight
		}
	}

	// 融合内容
	contents := make([]string, len(inputs))
	contributingModes := make([]InteractionMode, len(inputs))
	for i, input := range inputs {
		contents[i] = fmt.Sprintf("%v", input.Content)
		contributingModes[i] = input.Mode
	}

	fusedContent := mf.combineContents(contents, adjustedWeights, contributingModes)
	primaryMode := mf.selectPrimaryMode(adjustedWeights)

	// 计算融合置信度
	fusedConfidence := 0.0
	for _, input := range inputs {
		fusedConfidence += input.Confidence * adjustedWeights[input.Mode]
	}

	return &FusedInput{
		Content:           fusedContent,
		Confidence:        fusedConfidence * consistency, // 一致性影响最终置信度
		PrimaryMode:       primaryMode,
		ContributingModes: contributingModes,
		FusionMethod:      FusionDeepFusion,
		Weights:           adjustedWeights,
		Metadata: map[string]interface{}{
			"consistency":    consistency,
			"total_weight":   totalWeight,
		},
	}
}

func (mf *ModalityFusion) adaptiveFusion(inputs []ModalInput) *FusedInput {
	// 自适应融合：根据输入质量动态选择融合策略
	
	avgConfidence := 0.0
	for _, input := range inputs {
		avgConfidence += input.Confidence
	}
	avgConfidence /= float64(len(inputs))

	// 根据平均置信度选择策略
	if avgConfidence > 0.8 {
		// 高置信度：使用加权平均
		return mf.weightedAverageFusion(inputs)
	} else if avgConfidence > 0.6 {
		// 中等置信度：使用投票
		return mf.votingFusion(inputs)
	} else {
		// 低置信度：选择最高置信度
		return mf.maxConfidenceFusion(inputs)
	}
}

func (mf *ModalityFusion) calculateModalConsistency(inputs []ModalInput) float64 {
	if len(inputs) < 2 {
		return 1.0
	}

	// 简化的一致性计算：基于置信度的方差
	confidences := make([]float64, len(inputs))
	for i, input := range inputs {
		confidences[i] = input.Confidence
	}

	mean := 0.0
	for _, conf := range confidences {
		mean += conf
	}
	mean /= float64(len(confidences))

	variance := 0.0
	for _, conf := range confidences {
		variance += math.Pow(conf-mean, 2)
	}
	variance /= float64(len(confidences))

	// 一致性与方差成反比
	consistency := 1.0 / (1.0 + variance)
	return consistency
}

func (mf *ModalityFusion) selectPrimaryMode(weights map[InteractionMode]float64) InteractionMode {
	var primaryMode InteractionMode
	maxWeight := 0.0

	for mode, weight := range weights {
		if weight > maxWeight {
			maxWeight = weight
			primaryMode = mode
		}
	}

	return primaryMode
}

func (mf *ModalityFusion) combineContents(
	contents []string,
	weights map[InteractionMode]float64,
	modes []InteractionMode,
) string {
	if len(contents) == 0 {
		return ""
	}

	if len(contents) == 1 {
		return contents[0]
	}

	// 选择权重最高的内容作为主要内容
	maxWeight := 0.0
	primaryContent := contents[0]
	
	for i, mode := range modes {
		if weight, exists := weights[mode]; exists && weight > maxWeight {
			maxWeight = weight
			primaryContent = contents[i]
		}
	}

	// 可以在这里实现更复杂的内容合并逻辑
	// 目前简单返回主要内容
	return primaryContent
}
