package handler

import (
	"net/http"

	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// EnhancedAIHandler 增强的AI处理器
type EnhancedAIHandler struct {
	aiService *service.EnhancedAIService
	logger    *logrus.Logger
}

// NewEnhancedAIHandler 创建增强的AI处理器
func NewEnhancedAIHandler(aiService *service.EnhancedAIService, logger *logrus.Logger) *EnhancedAIHandler {
	return &EnhancedAIHandler{
		aiService: aiService,
		logger:    logger,
	}
}

// ProcessMessage 处理消息
func (h *EnhancedAIHandler) ProcessMessage(c *gin.Context) {
	var req service.ProcessMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind request")
		c.<PERSON>(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"details": err.<PERSON>rror(),
		})
		return
	}

	// 从上下文获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	req.UserID = userID.(int64)

	h.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("EnhancedAIHandler: Processing message")

	// 检查是否是确认消息
	if h.aiService.IsConfirmationMessage(req.Message) {
		h.handleConfirmationMessage(c, &req)
		return
	}

	// 处理普通消息
	response, err := h.aiService.ProcessMessage(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process message")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to process message",
			"details": err.Error(),
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"intent":          response.Intent,
		"confidence":      response.Confidence,
		"processing_time": response.ProcessingTime,
	}).Info("EnhancedAIHandler: Message processed successfully")

	c.JSON(http.StatusOK, response)
}

// handleConfirmationMessage 处理确认消息
func (h *EnhancedAIHandler) handleConfirmationMessage(c *gin.Context, req *service.ProcessMessageRequest) {
	h.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("EnhancedAIHandler: Processing confirmation message")

	// 获取用户的待确认操作
	pendingConfirmations := h.aiService.GetPendingConfirmations(req.UserID)
	if len(pendingConfirmations) == 0 {
		c.JSON(http.StatusOK, &service.ProcessMessageResponse{
			Content:    "❌ 没有找到待确认的操作",
			Intent:     "no_pending_confirmation",
			Confidence: 1.0,
			TokenCount: 10,
		})
		return
	}

	// 使用最新的确认令牌
	latestConfirmation := pendingConfirmations[len(pendingConfirmations)-1]

	response, err := h.aiService.ProcessConfirmation(c.Request.Context(), latestConfirmation.SessionID, req.UserID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process confirmation")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to process confirmation",
			"details": err.Error(),
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"session_id":      req.SessionID,
		"confirm_token":   latestConfirmation.SessionID,
		"operation":       latestConfirmation.Operation,
		"processing_time": response.ProcessingTime,
	}).Info("EnhancedAIHandler: Confirmation processed successfully")

	c.JSON(http.StatusOK, response)
}

// GetPendingConfirmations 获取待确认操作
func (h *EnhancedAIHandler) GetPendingConfirmations(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	pendingConfirmations := h.aiService.GetPendingConfirmations(userID.(int64))

	h.logger.WithFields(logrus.Fields{
		"user_id": userID,
		"count":   len(pendingConfirmations),
	}).Info("EnhancedAIHandler: Retrieved pending confirmations")

	c.JSON(http.StatusOK, gin.H{
		"pending_confirmations": pendingConfirmations,
		"count":                 len(pendingConfirmations),
	})
}

// ProcessConfirmation 处理确认操作
func (h *EnhancedAIHandler) ProcessConfirmation(c *gin.Context) {
	confirmToken := c.Param("token")
	if confirmToken == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Confirmation token is required",
		})
		return
	}

	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"confirm_token": confirmToken,
		"user_id":       userID,
	}).Info("EnhancedAIHandler: Processing confirmation by token")

	response, err := h.aiService.ProcessConfirmation(c.Request.Context(), confirmToken, userID.(int64))
	if err != nil {
		h.logger.WithError(err).Error("Failed to process confirmation")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to process confirmation",
			"details": err.Error(),
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"confirm_token":   confirmToken,
		"user_id":         userID,
		"processing_time": response.ProcessingTime,
	}).Info("EnhancedAIHandler: Confirmation processed successfully")

	c.JSON(http.StatusOK, response)
}

// CleanupExpiredConfirmations 清理过期确认操作
func (h *EnhancedAIHandler) CleanupExpiredConfirmations(c *gin.Context) {
	h.aiService.CleanupExpiredConfirmations()

	h.logger.Info("EnhancedAIHandler: Cleaned up expired confirmations")

	c.JSON(http.StatusOK, gin.H{
		"message": "Expired confirmations cleaned up successfully",
	})
}

// GetSystemStatus 获取系统状态
func (h *EnhancedAIHandler) GetSystemStatus(c *gin.Context) {
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error": "User not authenticated",
		})
		return
	}

	pendingCount := len(h.aiService.GetPendingConfirmations(userID.(int64)))

	status := gin.H{
		"status":                   "healthy",
		"enhanced_ai_service":      "active",
		"unified_execution_engine": "active",
		"pending_confirmations":    pendingCount,
		"features": gin.H{
			"intent_recognition":    true,
			"unified_execution":     true,
			"confirmation_system":   true,
			"database_operations":   true,
			"ssh_operations":        false, // 待实现
			"monitoring_operations": false, // 待实现
		},
	}

	h.logger.WithFields(logrus.Fields{
		"user_id":       userID,
		"pending_count": pendingCount,
	}).Info("EnhancedAIHandler: System status retrieved")

	c.JSON(http.StatusOK, status)
}

// RegisterRoutes 注册路由
func (h *EnhancedAIHandler) RegisterRoutes(router *gin.RouterGroup) {
	ai := router.Group("/ai/enhanced")
	{
		ai.POST("/message", h.ProcessMessage)
		ai.GET("/confirmations", h.GetPendingConfirmations)
		ai.POST("/confirm/:token", h.ProcessConfirmation)
		ai.POST("/cleanup", h.CleanupExpiredConfirmations)
		ai.GET("/status", h.GetSystemStatus)
	}

	h.logger.Info("EnhancedAIHandler: Routes registered successfully")
}
