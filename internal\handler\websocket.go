package handler

import (
	"net/http"
	"strconv"

	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// WebSocketHandler WebSocket处理器
type WebSocketHandler struct {
	logger    *logrus.Logger
	wsManager *service.WebSocketManager
}

// NewWebSocketHandler 创建WebSocket处理器
func NewWebSocketHandler(logger *logrus.Logger, wsManager *service.WebSocketManager) *WebSocketHandler {
	return &WebSocketHandler{
		logger:    logger,
		wsManager: wsManager,
	}
}

// HandleWebSocket 处理WebSocket连接
func (h *WebSocketHandler) HandleWebSocket(c *gin.Context) {
	// 获取用户信息
	userIDStr := c.Query("user_id")
	sessionID := c.Query("session_id")

	if userIDStr == "" || sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "user_id and session_id are required",
		})
		return
	}

	userID, err := strconv.ParseInt(userIDStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "invalid user_id",
		})
		return
	}

	// 升级到WebSocket连接
	err = h.wsManager.HandleWebSocket(c.Writer, c.Request, userID, sessionID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to handle WebSocket connection")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "Failed to establish WebSocket connection",
		})
		return
	}
}

// GetConnectionStats 获取连接统计
func (h *WebSocketHandler) GetConnectionStats(c *gin.Context) {
	stats := h.wsManager.GetConnectionStats()
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    stats,
	})
}

// SendTestMessage 发送测试消息
func (h *WebSocketHandler) SendTestMessage(c *gin.Context) {
	var req struct {
		UserID    int64  `json:"user_id"`
		SessionID string `json:"session_id"`
		Message   string `json:"message"`
		Type      string `json:"type"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Invalid request body",
		})
		return
	}

	message := &service.WSMessage{
		Type: req.Type,
		Data: req.Message,
	}

	var err error
	if req.SessionID != "" {
		err = h.wsManager.SendToSession(req.SessionID, message)
	} else if req.UserID > 0 {
		err = h.wsManager.SendToUser(req.UserID, message)
	} else {
		err = h.wsManager.BroadcastToAll(message)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Message sent successfully",
	})
}
