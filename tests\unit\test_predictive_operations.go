package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/ai"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

func main() {
	fmt.Println("🔮 预测性运维引擎测试开始...")

	// 1. 初始化日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 2. 初始化数据库
	db, err := gorm.Open(sqlite.Open("test_predictive.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect database:", err)
	}

	// 3. 自动迁移数据库表
	if err := db.AutoMigrate(
		&ai.PredictiveInsightRecord{},
		&ai.PredictiveAnalysisRecord{},
		&ai.MLModelRecord{},
		&ai.OperationsDataRecord{},
		&ai.AlertRecordDB{},
		&ai.RecommendationRecord{},
		&ai.PredictiveMetricsRecord{},
	); err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// 4. 创建预测性运维引擎配置
	config := &ai.PredictiveConfig{
		EnablePrediction:        true,
		AnalysisInterval:        1 * time.Minute, // 测试用短间隔
		PredictionHorizon:       24 * time.Hour,
		MinDataPoints:           5,
		ConfidenceThreshold:     0.6,
		AlertThreshold:          0.7,
		MaxRecommendations:      15,
		EnableAutoRemediation:   false,
		LearningRate:            0.01,
		ModelUpdateInterval:     30 * time.Minute,
	}

	// 5. 创建预测性运维引擎
	engine := ai.NewPredictiveOperationsEngine(db, logger, config)

	// 6. 启动引擎
	if err := engine.Start(); err != nil {
		log.Fatal("Failed to start predictive operations engine:", err)
	}
	defer engine.Stop()

	fmt.Println("✅ 预测性运维引擎启动成功")

	// 7. 测试预测分析
	ctx := context.Background()

	fmt.Println("\n🧪 测试 1: 执行预测分析")
	result, err := engine.AnalyzeAndPredict(ctx)
	if err != nil {
		fmt.Printf("❌ 预测分析失败: %v\n", err)
	} else {
		fmt.Printf("✅ 预测分析成功:\n")
		fmt.Printf("   洞察数量: %d\n", len(result.Insights))
		fmt.Printf("   建议数量: %d\n", len(result.Recommendations))
		fmt.Printf("   关键洞察: %d\n", result.Summary.CriticalInsights)
		fmt.Printf("   系统健康: %s\n", result.Summary.SystemHealth)
		fmt.Printf("   风险等级: %s\n", result.Summary.RiskLevel)
		fmt.Printf("   预测准确率: %.1f%%\n", result.Summary.PredictionAccuracy*100)

		// 显示洞察详情
		if len(result.Insights) > 0 {
			fmt.Printf("\n📊 洞察详情:\n")
			for i, insight := range result.Insights {
				if i >= 3 { // 只显示前3个
					break
				}
				fmt.Printf("   %d. [%s] %s\n", i+1, insight.Severity, insight.Title)
				fmt.Printf("      描述: %s\n", insight.Description)
				fmt.Printf("      置信度: %.1f%%\n", insight.Confidence*100)
				fmt.Printf("      影响系统: %v\n", insight.AffectedSystems)
				fmt.Printf("      预测时间: %s\n", insight.PredictedTime.Format("2006-01-02 15:04:05"))
			}
		}

		// 显示建议详情
		if len(result.Recommendations) > 0 {
			fmt.Printf("\n🎯 建议详情:\n")
			for i, rec := range result.Recommendations {
				if i >= 3 { // 只显示前3个
					break
				}
				fmt.Printf("   %d. [%s] %s\n", i+1, rec.Priority, rec.Title)
				fmt.Printf("      描述: %s\n", rec.Description)
				fmt.Printf("      动作: %s\n", rec.Action)
				fmt.Printf("      影响: %s | 工作量: %s\n", rec.Impact, rec.Effort)
				fmt.Printf("      时间线: %s\n", rec.Timeline)
				if len(rec.Benefits) > 0 {
					fmt.Printf("      收益: %v\n", rec.Benefits)
				}
			}
		}
	}

	// 8. 测试获取活跃洞察
	fmt.Println("\n🧪 测试 2: 获取活跃洞察")
	activeInsights, err := engine.GetActiveInsights()
	if err != nil {
		fmt.Printf("❌ 获取活跃洞察失败: %v\n", err)
	} else {
		fmt.Printf("✅ 获取到 %d 个活跃洞察\n", len(activeInsights))
		for i, insight := range activeInsights {
			if i >= 2 { // 只显示前2个
				break
			}
			fmt.Printf("   %d. [%s] %s (置信度: %.1f%%)\n", 
				i+1, insight.Severity, insight.Title, insight.Confidence*100)
		}
	}

	// 9. 测试获取建议
	fmt.Println("\n🧪 测试 3: 获取运维建议")
	filters := map[string]interface{}{
		"priority": "high",
	}
	recommendations, err := engine.GetRecommendations(ctx, filters)
	if err != nil {
		fmt.Printf("❌ 获取建议失败: %v\n", err)
	} else {
		fmt.Printf("✅ 获取到 %d 个高优先级建议\n", len(recommendations))
		for i, rec := range recommendations {
			if i >= 2 { // 只显示前2个
				break
			}
			fmt.Printf("   %d. [%s] %s\n", i+1, rec.Priority, rec.Title)
			fmt.Printf("      动作: %s | 影响: %s\n", rec.Action, rec.Impact)
		}
	}

	// 10. 测试引擎指标
	fmt.Println("\n🧪 测试 4: 获取引擎指标")
	metrics := engine.GetMetrics()
	fmt.Printf("✅ 引擎运行指标:\n")
	for key, value := range metrics {
		fmt.Printf("   %s: %v\n", key, value)
	}

	// 11. 模拟运行一段时间
	fmt.Println("\n🧪 测试 5: 模拟持续运行")
	fmt.Println("引擎将在后台持续运行 30 秒...")
	
	// 启动一个goroutine来定期显示状态
	go func() {
		ticker := time.NewTicker(10 * time.Second)
		defer ticker.Stop()
		
		for i := 0; i < 3; i++ {
			<-ticker.C
			fmt.Printf("⏰ 运行状态检查 %d/3\n", i+1)
			
			// 获取最新指标
			currentMetrics := engine.GetMetrics()
			if isRunning, ok := currentMetrics["is_running"].(bool); ok && isRunning {
				fmt.Printf("   引擎状态: 运行中 ✅\n")
				if lastAnalysis, ok := currentMetrics["last_analysis"].(time.Time); ok {
					fmt.Printf("   最后分析: %s\n", lastAnalysis.Format("15:04:05"))
				}
			} else {
				fmt.Printf("   引擎状态: 已停止 ❌\n")
			}
		}
	}()

	// 等待30秒
	time.Sleep(30 * time.Second)

	// 12. 最终状态检查
	fmt.Println("\n📈 最终状态报告:")
	finalMetrics := engine.GetMetrics()
	fmt.Printf("引擎运行状态: %v\n", finalMetrics["is_running"])
	fmt.Printf("预测准确率: %.1f%%\n", finalMetrics["prediction_accuracy"].(float64)*100)
	fmt.Printf("总预测次数: %d\n", finalMetrics["total_predictions"])
	fmt.Printf("正确预测次数: %d\n", finalMetrics["correct_predictions"])

	// 13. 测试数据库记录
	fmt.Println("\n🧪 测试 6: 检查数据库记录")
	var insightCount int64
	db.Model(&ai.PredictiveInsightRecord{}).Count(&insightCount)
	fmt.Printf("数据库中的洞察记录: %d 条\n", insightCount)

	var analysisCount int64
	db.Model(&ai.PredictiveAnalysisRecord{}).Count(&analysisCount)
	fmt.Printf("数据库中的分析记录: %d 条\n", analysisCount)

	fmt.Println("\n🎉 预测性运维引擎测试完成！")
	fmt.Println("\n📊 测试总结:")
	fmt.Println("✅ 引擎启动和停止")
	fmt.Println("✅ 预测分析执行")
	fmt.Println("✅ 洞察生成")
	fmt.Println("✅ 建议生成")
	fmt.Println("✅ 告警处理")
	fmt.Println("✅ 数据库集成")
	fmt.Println("✅ 指标监控")
	fmt.Println("✅ 后台任务运行")
}
