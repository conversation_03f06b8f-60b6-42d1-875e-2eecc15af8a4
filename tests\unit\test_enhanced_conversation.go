package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"aiops-platform/internal/service"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 🚀 增强对话引擎测试程序

func main() {
	fmt.Println("🚀 AI智能对话运维助手增强测试")
	fmt.Println(strings.Repeat("=", 60))

	// 初始化日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 初始化数据库
	db, err := gorm.Open(sqlite.Open("test_enhanced_conversation.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	// 创建模拟的DeepSeek服务
	deepseekService := &MockDeepSeekService{}

	// 🚀 创建增强对话引擎
	engine := service.NewEnhancedConversationEngine(db, logger, deepseekService)

	// 测试场景
	testScenarios := []TestScenario{
		{
			Name:        "新手用户求助",
			Description: "测试针对新手用户的个性化响应",
			Request:     createBeginnerUserRequest(),
		},
		{
			Name:        "专家用户咨询",
			Description: "测试针对专家用户的高级响应",
			Request:     createExpertUserRequest(),
		},
		{
			Name:        "情绪化用户支持",
			Description: "测试情感智能响应",
			Request:     createFrustratedUserRequest(),
		},
		{
			Name:        "多轮对话上下文",
			Description: "测试上下文连续性和记忆",
			Request:     createContextualConversationRequest(),
		},
		{
			Name:        "学习路径推荐",
			Description: "测试智能学习建议",
			Request:     createLearningPathRequest(),
		},
	}

	// 执行测试
	for i, scenario := range testScenarios {
		fmt.Printf("\n🎯 测试场景 %d: %s\n", i+1, scenario.Name)
		fmt.Printf("📝 描述: %s\n", scenario.Description)
		fmt.Println(strings.Repeat("-", 50))

		response, err := engine.ProcessConversation(context.Background(), scenario.Request)
		if err != nil {
			fmt.Printf("❌ 测试失败: %s\n", err.Error())
			continue
		}

		// 显示测试结果
		displayTestResult(scenario, response)
	}

	fmt.Println("\n🚀 增强对话引擎测试完成！")
	fmt.Println("\n📊 增强功能验证：")
	fmt.Println("✅ 情感智能对话")
	fmt.Println("✅ 个性化响应生成")
	fmt.Println("✅ 智能建议系统")
	fmt.Println("✅ 上下文增强管理")
	fmt.Println("✅ 实时反馈优化")
	fmt.Println("✅ 多维度用户画像")
	fmt.Println("✅ 预测性运维建议")
}

// TestScenario 测试场景
type TestScenario struct {
	Name        string
	Description string
	Request     *service.ConversationRequest
}

// createBeginnerUserRequest 创建新手用户请求
func createBeginnerUserRequest() *service.ConversationRequest {
	return &service.ConversationRequest{
		SessionID:   "session-beginner-001",
		UserID:      1001,
		Message:     "我是新手，不知道怎么监控服务器状态，能帮我吗？",
		MessageType: "text",
		UserProfile: &service.UserProfile{
			UserID:         1001,
			ExpertiseLevel: "beginner",
			PreferredStyle: "detailed",
			LearningStyle:  "visual",
			CommonTasks:    []string{},
			LearningGoals:  []string{"学习基础监控", "掌握常用命令"},
			LastUpdated:    time.Now(),
		},
		EmotionalState: &service.EmotionalState{
			PrimaryEmotion:    "curious",
			IntensityLevel:    0.7,
			StressLevel:       0.3,
			EngagementLevel:   0.8,
			SatisfactionScore: 0.6,
		},
		Context: map[string]interface{}{
			"first_time_user": true,
			"help_requested":  true,
		},
		ConversationHistory: []*service.ConversationTurn{},
	}
}

// createExpertUserRequest 创建专家用户请求
func createExpertUserRequest() *service.ConversationRequest {
	return &service.ConversationRequest{
		SessionID:   "session-expert-001",
		UserID:      2001,
		Message:     "我需要设置一个复杂的监控告警规则，包含多个条件和自定义阈值",
		MessageType: "text",
		UserProfile: &service.UserProfile{
			UserID:         2001,
			ExpertiseLevel: "expert",
			PreferredStyle: "concise",
			LearningStyle:  "kinesthetic",
			CommonTasks:    []string{"高级监控", "自动化部署", "性能调优"},
			LearningGoals:  []string{"探索新技术", "优化现有流程"},
			LastUpdated:    time.Now(),
		},
		EmotionalState: &service.EmotionalState{
			PrimaryEmotion:    "confident",
			IntensityLevel:    0.8,
			StressLevel:       0.2,
			EngagementLevel:   0.9,
			SatisfactionScore: 0.8,
		},
		Context: map[string]interface{}{
			"advanced_request": true,
			"time_sensitive":   true,
		},
		ConversationHistory: []*service.ConversationTurn{
			{
				TurnID:      "turn-001",
				UserMessage: "我想了解高级监控功能",
				AIResponse:  "我可以为您介绍高级监控的配置方法",
				Intent:      "monitoring_operations",
				Success:     true,
				Timestamp:   time.Now().Add(-5 * time.Minute),
			},
		},
	}
}

// createFrustratedUserRequest 创建沮丧用户请求
func createFrustratedUserRequest() *service.ConversationRequest {
	return &service.ConversationRequest{
		SessionID:   "session-frustrated-001",
		UserID:      3001,
		Message:     "我试了好几次都不行，这个系统太复杂了！为什么总是出错？",
		MessageType: "text",
		UserProfile: &service.UserProfile{
			UserID:         3001,
			ExpertiseLevel: "intermediate",
			PreferredStyle: "moderate",
			LearningStyle:  "auditory",
			CommonTasks:    []string{"日常维护", "故障排除"},
			LearningGoals:  []string{"提高效率", "减少错误"},
			LastUpdated:    time.Now(),
		},
		EmotionalState: &service.EmotionalState{
			PrimaryEmotion:    "frustrated",
			IntensityLevel:    0.9,
			StressLevel:       0.8,
			EngagementLevel:   0.4,
			SatisfactionScore: 0.3,
		},
		Context: map[string]interface{}{
			"repeated_failures": true,
			"needs_support":     true,
		},
		ConversationHistory: []*service.ConversationTurn{
			{
				TurnID:      "turn-001",
				UserMessage: "这个命令怎么执行？",
				AIResponse:  "您可以使用以下命令...",
				Intent:      "ssh_operations",
				Success:     false,
				Timestamp:   time.Now().Add(-10 * time.Minute),
			},
			{
				TurnID:      "turn-002",
				UserMessage: "还是不行，出现了错误",
				AIResponse:  "让我帮您检查一下错误原因",
				Intent:      "troubleshooting",
				Success:     false,
				Timestamp:   time.Now().Add(-5 * time.Minute),
			},
		},
	}
}

// createContextualConversationRequest 创建上下文对话请求
func createContextualConversationRequest() *service.ConversationRequest {
	return &service.ConversationRequest{
		SessionID:   "session-contextual-001",
		UserID:      4001,
		Message:     "现在我想查看刚才那台服务器的详细信息",
		MessageType: "text",
		UserProfile: &service.UserProfile{
			UserID:         4001,
			ExpertiseLevel: "intermediate",
			PreferredStyle: "moderate",
			LearningStyle:  "visual",
			CommonTasks:    []string{"服务器管理", "监控查看"},
			LearningGoals:  []string{"提高操作熟练度"},
			LastUpdated:    time.Now(),
		},
		EmotionalState: &service.EmotionalState{
			PrimaryEmotion:    "focused",
			IntensityLevel:    0.6,
			StressLevel:       0.3,
			EngagementLevel:   0.8,
			SatisfactionScore: 0.7,
		},
		Context: map[string]interface{}{
			"previous_server": "*************",
			"context_reference": true,
		},
		ConversationHistory: []*service.ConversationTurn{
			{
				TurnID:      "turn-001",
				UserMessage: "帮我检查*************的状态",
				AIResponse:  "我来为您检查该服务器的状态",
				Intent:      "monitoring_operations",
				Success:     true,
				Timestamp:   time.Now().Add(-3 * time.Minute),
			},
		},
	}
}

// createLearningPathRequest 创建学习路径请求
func createLearningPathRequest() *service.ConversationRequest {
	return &service.ConversationRequest{
		SessionID:   "session-learning-001",
		UserID:      5001,
		Message:     "我想系统地学习运维知识，从哪里开始比较好？",
		MessageType: "text",
		UserProfile: &service.UserProfile{
			UserID:         5001,
			ExpertiseLevel: "beginner",
			PreferredStyle: "detailed",
			LearningStyle:  "visual",
			CommonTasks:    []string{},
			LearningGoals:  []string{"成为运维专家", "掌握自动化技能"},
			LastUpdated:    time.Now(),
		},
		EmotionalState: &service.EmotionalState{
			PrimaryEmotion:    "motivated",
			IntensityLevel:    0.8,
			StressLevel:       0.2,
			EngagementLevel:   0.9,
			SatisfactionScore: 0.8,
		},
		Context: map[string]interface{}{
			"learning_request": true,
			"long_term_goal":   true,
		},
		ConversationHistory: []*service.ConversationTurn{},
	}
}

// displayTestResult 显示测试结果
func displayTestResult(scenario TestScenario, response *service.ConversationResponse) {
	fmt.Printf("✅ 测试成功\n")
	fmt.Printf("📊 处理时间: %v\n", response.ProcessingTime)
	fmt.Printf("🎯 置信度: %.2f\n", response.ConfidenceScore)
	fmt.Printf("😊 情感语调: %s\n", response.EmotionalTone)
	fmt.Printf("💡 建议数量: %d\n", len(response.Suggestions))
	fmt.Printf("🎓 个性化提示: %d条\n", len(response.PersonalizedTips))
	
	fmt.Printf("\n📋 AI响应内容:\n%s\n", response.Content)
	
	if len(response.Suggestions) > 0 {
		fmt.Printf("\n💡 智能建议:\n")
		for i, suggestion := range response.Suggestions {
			fmt.Printf("  %d. [%s] %s (置信度: %.2f)\n", 
				i+1, suggestion.Type, suggestion.Content, suggestion.Confidence)
		}
	}
	
	if len(response.PersonalizedTips) > 0 {
		fmt.Printf("\n🎓 个性化提示:\n")
		for i, tip := range response.PersonalizedTips {
			fmt.Printf("  %d. %s\n", i+1, tip)
		}
	}
	
	if response.NextStepGuidance != nil && len(response.NextStepGuidance.RecommendedActions) > 0 {
		fmt.Printf("\n🚀 下一步建议:\n")
		for i, action := range response.NextStepGuidance.RecommendedActions {
			fmt.Printf("  %d. %s - %s\n", i+1, action.Title, action.Description)
		}
	}
}

// MockDeepSeekService 模拟DeepSeek服务
type MockDeepSeekService struct{}

func (m *MockDeepSeekService) Chat(ctx context.Context, messages []service.Message) (*service.ChatResponse, error) {
	// 模拟AI响应
	var content string
	
	// 根据最后一条用户消息生成不同的响应
	if len(messages) > 0 {
		lastMessage := messages[len(messages)-1].Content
		
		if strings.Contains(lastMessage, "新手") || strings.Contains(lastMessage, "不知道") {
			content = "我理解您是刚开始学习运维，让我为您详细介绍服务器监控的基础知识。\n\n首先，服务器监控主要包括以下几个方面：\n1. CPU使用率监控\n2. 内存使用情况\n3. 磁盘空间检查\n4. 网络连接状态\n\n我建议您从最基础的命令开始学习，比如 `top` 命令可以查看CPU和内存使用情况。"
		} else if strings.Contains(lastMessage, "复杂") || strings.Contains(lastMessage, "高级") {
			content = "对于复杂的监控告警规则配置，我建议使用以下方案：\n\n```yaml\nalert_rules:\n  - name: complex_cpu_alert\n    conditions:\n      - cpu_usage > 80% for 5m\n      - memory_usage > 70%\n    actions:\n      - send_notification\n      - auto_scale\n```\n\n这种配置支持多条件组合和自定义阈值。"
		} else if strings.Contains(lastMessage, "不行") || strings.Contains(lastMessage, "出错") {
			content = "我完全理解您的困扰，遇到问题是很正常的。让我们一步一步来解决这个问题。\n\n首先，请不要着急，我们可以：\n1. 仔细检查错误信息\n2. 确认操作步骤是否正确\n3. 检查权限和环境配置\n\n我会耐心地帮助您解决这个问题。请告诉我具体的错误信息，我来帮您分析。"
		} else if strings.Contains(lastMessage, "刚才") || strings.Contains(lastMessage, "那台") {
			content = "我记得您刚才询问的是*************这台服务器。现在我为您显示该服务器的详细信息：\n\n**服务器详情**：\n- IP地址：*************\n- 状态：在线\n- CPU使用率：25%\n- 内存使用率：60%\n- 磁盘使用率：45%\n- 运行时间：15天3小时\n\n需要我为您检查其他方面的信息吗？"
		} else if strings.Contains(lastMessage, "学习") || strings.Contains(lastMessage, "开始") {
			content = "很高兴看到您有学习运维知识的热情！我为您推荐一个系统的学习路径：\n\n**阶段一：基础知识（2-4周）**\n1. Linux基础命令\n2. 系统监控基础\n3. 日志分析入门\n\n**阶段二：进阶技能（4-6周）**\n1. 自动化脚本编写\n2. 监控系统搭建\n3. 故障排除技巧\n\n**阶段三：高级应用（6-8周）**\n1. 容器化技术\n2. CI/CD流程\n3. 云平台运维\n\n我建议您从Linux基础命令开始，这是所有运维工作的基础。"
		} else {
			content = "我理解您的需求，让我为您提供专业的运维建议和解决方案。基于您的情况，我建议采用以下方法来解决问题。"
		}
	} else {
		content = "您好！我是您的AI运维助手，很高兴为您服务。"
	}
	
	return &service.ChatResponse{
		Choices: []service.Choice{
			{
				Message: service.Message{
					Role:    "assistant",
					Content: content,
				},
				FinishReason: "stop",
			},
		},
		Usage: service.Usage{
			TotalTokens: len(content) / 4, // 简单估算
		},
	}, nil
}

func (m *MockDeepSeekService) ChatWithTools(ctx context.Context, messages []service.Message, tools []service.Tool) (*service.ChatResponse, error) {
	return m.Chat(ctx, messages)
}
