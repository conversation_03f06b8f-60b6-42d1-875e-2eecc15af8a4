package server

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"sync"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// GracefulServer 优雅启停服务器
type GracefulServer struct {
	httpServer     *http.Server
	logger         *logrus.Logger
	db             *gorm.DB
	shutdownFuncs  []ShutdownFunc
	startupFuncs   []StartupFunc
	mu             sync.RWMutex
	isShuttingDown bool
	startTime      time.Time
}

// ShutdownFunc 关闭函数类型
type ShutdownFunc func(ctx context.Context) error

// StartupFunc 启动函数类型
type StartupFunc func(ctx context.Context) error

// NewGracefulServer 创建优雅启停服务器
func NewGracefulServer(httpServer *http.Server, logger *logrus.Logger, db *gorm.DB) *GracefulServer {
	return &GracefulServer{
		httpServer:    httpServer,
		logger:        logger,
		db:            db,
		shutdownFuncs: make([]ShutdownFunc, 0),
		startupFuncs:  make([]StartupFunc, 0),
		startTime:     time.Now(),
	}
}

// AddStartupFunc 添加启动函数
func (s *GracefulServer) AddStartupFunc(fn StartupFunc) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.startupFuncs = append(s.startupFuncs, fn)
}

// AddShutdownFunc 添加关闭函数
func (s *GracefulServer) AddShutdownFunc(fn ShutdownFunc) {
	s.mu.Lock()
	defer s.mu.Unlock()
	s.shutdownFuncs = append(s.shutdownFuncs, fn)
}

// Start 启动服务器
func (s *GracefulServer) Start() error {
	s.logger.Info("Starting AI Ops Platform server...")

	// 执行启动前的初始化函数
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	for i, fn := range s.startupFuncs {
		s.logger.Infof("Executing startup function %d/%d", i+1, len(s.startupFuncs))
		if err := fn(ctx); err != nil {
			s.logger.WithError(err).Errorf("Startup function %d failed", i+1)
			return fmt.Errorf("startup function %d failed: %w", i+1, err)
		}
	}

	// 启动HTTP服务器
	go func() {
		s.logger.Infof("HTTP server listening on %s", s.httpServer.Addr)
		if err := s.httpServer.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			s.logger.WithError(err).Fatal("Failed to start HTTP server")
		}
	}()

	s.logger.WithFields(logrus.Fields{
		"addr":       s.httpServer.Addr,
		"start_time": s.startTime.Format(time.RFC3339),
	}).Info("AI Ops Platform server started successfully")

	return nil
}

// WaitForShutdown 等待关闭信号
func (s *GracefulServer) WaitForShutdown() {
	// 创建信号通道
	quit := make(chan os.Signal, 1)
	signal.Notify(quit,
		syscall.SIGINT,  // Ctrl+C
		syscall.SIGTERM, // 终止信号
		syscall.SIGQUIT, // 退出信号
	)

	// 等待信号
	sig := <-quit
	s.logger.WithField("signal", sig.String()).Info("Received shutdown signal")

	// 执行优雅关闭
	if err := s.Shutdown(); err != nil {
		s.logger.WithError(err).Error("Graceful shutdown failed")
		os.Exit(1)
	}

	s.logger.Info("Server shutdown completed")
}

// Shutdown 优雅关闭服务器
func (s *GracefulServer) Shutdown() error {
	s.mu.Lock()
	if s.isShuttingDown {
		s.mu.Unlock()
		return nil // 已经在关闭中
	}
	s.isShuttingDown = true
	s.mu.Unlock()

	s.logger.Info("Starting graceful shutdown...")

	// 设置关闭超时
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 关闭HTTP服务器
	s.logger.Info("Shutting down HTTP server...")
	if err := s.httpServer.Shutdown(ctx); err != nil {
		s.logger.WithError(err).Error("HTTP server shutdown failed")
		return fmt.Errorf("HTTP server shutdown failed: %w", err)
	}

	// 执行自定义关闭函数
	for i, fn := range s.shutdownFuncs {
		s.logger.Infof("Executing shutdown function %d/%d", i+1, len(s.shutdownFuncs))
		if err := fn(ctx); err != nil {
			s.logger.WithError(err).Errorf("Shutdown function %d failed", i+1)
			// 继续执行其他关闭函数，不中断流程
		}
	}

	// 关闭数据库连接
	if s.db != nil {
		s.logger.Info("Closing database connections...")
		if sqlDB, err := s.db.DB(); err == nil {
			if err := sqlDB.Close(); err != nil {
				s.logger.WithError(err).Error("Failed to close database connections")
			} else {
				s.logger.Info("Database connections closed successfully")
			}
		}
	}

	uptime := time.Since(s.startTime)
	s.logger.WithField("uptime", uptime.String()).Info("Graceful shutdown completed")

	return nil
}

// IsShuttingDown 检查是否正在关闭
func (s *GracefulServer) IsShuttingDown() bool {
	s.mu.RLock()
	defer s.mu.RUnlock()
	return s.isShuttingDown
}

// GetUptime 获取运行时间
func (s *GracefulServer) GetUptime() time.Duration {
	return time.Since(s.startTime)
}

// HealthCheck 健康检查中间件
func (s *GracefulServer) HealthCheckMiddleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			if s.IsShuttingDown() {
				w.WriteHeader(http.StatusServiceUnavailable)
				w.Write([]byte("Server is shutting down"))
				return
			}
			next.ServeHTTP(w, r)
		})
	}
}

// DefaultStartupFuncs 默认启动函数
func DefaultStartupFuncs() []StartupFunc {
	return []StartupFunc{
		// 数据库连接检查
		func(ctx context.Context) error {
			// 这里可以添加数据库连接检查逻辑
			return nil
		},
		// 缓存初始化
		func(ctx context.Context) error {
			// 这里可以添加缓存初始化逻辑
			return nil
		},
		// 外部服务连接检查
		func(ctx context.Context) error {
			// 这里可以添加外部服务连接检查逻辑
			return nil
		},
	}
}

// DefaultShutdownFuncs 默认关闭函数
func DefaultShutdownFuncs() []ShutdownFunc {
	return []ShutdownFunc{
		// 完成正在处理的请求
		func(ctx context.Context) error {
			// 等待一段时间让正在处理的请求完成
			select {
			case <-time.After(5 * time.Second):
				return nil
			case <-ctx.Done():
				return ctx.Err()
			}
		},
		// 清理临时文件
		func(ctx context.Context) error {
			// 这里可以添加临时文件清理逻辑
			return nil
		},
		// 关闭外部连接
		func(ctx context.Context) error {
			// 这里可以添加外部连接关闭逻辑
			return nil
		},
	}
}

// ServerManager 服务器管理器
type ServerManager struct {
	servers []*GracefulServer
	logger  *logrus.Logger
}

// NewServerManager 创建服务器管理器
func NewServerManager(logger *logrus.Logger) *ServerManager {
	return &ServerManager{
		servers: make([]*GracefulServer, 0),
		logger:  logger,
	}
}

// AddServer 添加服务器
func (m *ServerManager) AddServer(server *GracefulServer) {
	m.servers = append(m.servers, server)
}

// StartAll 启动所有服务器
func (m *ServerManager) StartAll() error {
	for i, server := range m.servers {
		m.logger.Infof("Starting server %d/%d", i+1, len(m.servers))
		if err := server.Start(); err != nil {
			m.logger.WithError(err).Errorf("Failed to start server %d", i+1)
			// 关闭已启动的服务器
			m.shutdownStartedServers(i)
			return fmt.Errorf("failed to start server %d: %w", i+1, err)
		}
	}
	return nil
}

// WaitForShutdown 等待关闭信号
func (m *ServerManager) WaitForShutdown() {
	if len(m.servers) == 0 {
		return
	}

	// 使用第一个服务器的信号处理
	m.servers[0].WaitForShutdown()

	// 关闭所有服务器
	m.ShutdownAll()
}

// ShutdownAll 关闭所有服务器
func (m *ServerManager) ShutdownAll() {
	for i, server := range m.servers {
		m.logger.Infof("Shutting down server %d/%d", i+1, len(m.servers))
		if err := server.Shutdown(); err != nil {
			m.logger.WithError(err).Errorf("Failed to shutdown server %d", i+1)
		}
	}
}

// shutdownStartedServers 关闭已启动的服务器
func (m *ServerManager) shutdownStartedServers(count int) {
	for i := 0; i < count; i++ {
		if err := m.servers[i].Shutdown(); err != nil {
			m.logger.WithError(err).Errorf("Failed to shutdown server %d during cleanup", i+1)
		}
	}
}
