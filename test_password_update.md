# 密码更新功能测试指南

## 🎯 功能概述

本文档描述了AI运维管理平台的密码更新功能，包括意图识别、安全验证、加密存储等完整流程。

## 🔧 核心组件

### 1. 意图识别增强
- **DeepSeek系统提示词**：已更新支持密码更新操作识别
- **识别模式**：
  ```
  用户输入："修改192.168.119.82这台主机密码为1qaz#EDC"
  AI识别：database_operations + update + password字段
  ```

### 2. 安全防护系统
- **多层安全检查**：
  - SQL注入防护
  - 权限验证
  - 敏感操作识别
  - 二次确认机制
- **风险评估**：自动评估操作风险等级
- **操作审计**：完整记录操作日志

### 3. 密码加密管理
- **加密算法**：AES-256-GCM
- **密钥管理**：支持密钥轮换
- **数据脱敏**：敏感信息自动脱敏显示

## 🚀 测试用例

### 测试用例1：基本密码更新
```
用户输入：修改192.168.119.82这台主机密码为newpass123
预期结果：
1. AI识别为database_operations意图
2. 生成UPDATE SQL语句
3. 触发二次确认流程
4. 用户确认后执行密码加密存储
```

### 测试用例2：通过主机名更新密码
```
用户输入：把web-01的登录密码改成secure@2024
预期结果：
1. 通过主机名查找对应主机
2. 生成确认提示
3. 显示脱敏后的新密码
4. 执行加密更新
```

### 测试用例3：安全验证测试
```
用户输入：修改所有主机密码为123456
预期结果：
1. 检测到批量操作风险
2. 提升风险等级为"critical"
3. 要求强制确认
4. 记录高风险操作日志
```

## 📋 实现状态

### ✅ 已完成
- [x] DeepSeek意图识别扩展
- [x] 统一数据库操作引擎
- [x] 多层安全防护系统
- [x] 密码加密管理器
- [x] 操作确认管理器
- [x] 数据库审计日志系统
- [x] 权限检查器
- [x] 敏感数据脱敏

### 🔄 进行中
- [ ] 完整的密码更新流程集成
- [ ] 前端确认界面优化
- [ ] 错误处理完善

### 📝 待优化
- [ ] 密码强度验证
- [ ] 批量密码更新支持
- [ ] 密码历史记录
- [ ] 自动密码生成

## 🔍 关键文件

### 核心服务文件
- `internal/service/unified_database_engine.go` - 统一数据库操作引擎
- `internal/service/database_security_manager.go` - 安全管理器
- `internal/service/encryption_manager.go` - 加密管理器
- `internal/service/operation_confirmation_manager.go` - 确认管理器
- `internal/service/database_audit_logger.go` - 审计日志器

### 意图处理文件
- `internal/service/deepseek.go` - DeepSeek意图识别
- `internal/service/simplified_intent_handlers.go` - 意图处理器

### 配置文件
- `internal/service/database_operation_tool.go` - 数据库操作工具

## 🛡️ 安全特性

### 1. 密码加密
- **算法**：AES-256-GCM
- **密钥管理**：支持密钥轮换
- **存储格式**：Base64编码的加密数据

### 2. 操作审计
- **记录内容**：操作前后数据、用户信息、时间戳
- **风险评估**：自动评估操作风险等级
- **日志保留**：支持配置保留期限

### 3. 权限控制
- **表级权限**：控制对不同表的访问
- **字段级权限**：控制敏感字段的操作
- **用户级权限**：基于用户角色的权限控制

## 🔧 配置示例

### 数据库引擎配置
```go
config := &DatabaseEngineConfig{
    RequireConfirmForUpdate: true,
    RequireConfirmForDelete: true,
    MaxBatchSize:            100,
    EncryptionKey:           "your-encryption-key",
    EnablePermissionCheck:   true,
    EnableAuditLog:         true,
    RetentionDays:          90,
}
```

### 安全规则配置
```go
tableRules := map[string]*TableSecurityRule{
    "hosts": {
        AllowUpdate:          true,
        RequireConfirmUpdate: true,
        MaxBatchSize:         100,
    },
}
```

## 📊 性能指标

### 预期性能
- **意图识别延迟**：< 500ms
- **密码加密时间**：< 10ms
- **数据库更新时间**：< 100ms
- **审计日志写入**：< 50ms

### 安全指标
- **SQL注入防护**：100%
- **敏感数据脱敏**：100%
- **操作审计覆盖**：100%
- **权限验证覆盖**：100%

## 🚨 注意事项

1. **密钥安全**：生产环境必须使用强密钥
2. **权限配置**：根据实际需求配置权限规则
3. **日志管理**：定期清理过期审计日志
4. **监控告警**：监控异常操作和失败率
5. **备份恢复**：确保密码数据的备份安全

## 📞 支持联系

如有问题或建议，请联系开发团队。
