package agent

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/interfaces"

	"github.com/sirupsen/logrus"
)

// DecisionEngine DeepSeek决策引擎
type DecisionEngine struct {
	deepseekService interfaces.DeepSeekServiceInterface
	registry        *AgentRegistry
	logger          *logrus.Logger
	config          *DecisionConfig
}

// DecisionConfig 决策引擎配置
type DecisionConfig struct {
	MaxAgentsPerDecision    int           `json:"max_agents_per_decision"`
	DecisionTimeout         time.Duration `json:"decision_timeout"`
	EnableParallelExecution bool          `json:"enable_parallel_execution"`
	ConfidenceThreshold     float64       `json:"confidence_threshold"`
}

// DecisionRequest 决策请求
type DecisionRequest struct {
	UserMessage string                 `json:"user_message"`
	Context     *DecisionContext       `json:"context"`
	Options     map[string]interface{} `json:"options"`
}

// DecisionContext 决策上下文
type DecisionContext struct {
	SessionID string                 `json:"session_id"`
	UserID    int64                  `json:"user_id"`
	TraceID   string                 `json:"trace_id"`
	Variables map[string]interface{} `json:"variables"`
	History   []string               `json:"history"`
}

// DecisionResult 决策结果
type DecisionResult struct {
	Success       bool                   `json:"success"`
	Confidence    float64                `json:"confidence"`
	Explanation   string                 `json:"explanation"`
	Agents        []AgentDecision        `json:"agents"`
	ExecutionPlan *ExecutionPlan         `json:"execution_plan"`
	Metadata      map[string]interface{} `json:"metadata"`
	CreatedAt     time.Time              `json:"created_at"`
}

// AgentDecision Agent决策
type AgentDecision struct {
	AgentID     string                 `json:"agent_id"`
	Capability  string                 `json:"capability"`
	Parameters  map[string]interface{} `json:"parameters"`
	Priority    int                    `json:"priority"`
	Condition   string                 `json:"condition,omitempty"`
	Description string                 `json:"description"`
}

// ExecutionPlan 执行计划
type ExecutionPlan struct {
	Strategy     ExecutionStrategy `json:"strategy"`
	Steps        []ExecutionStep   `json:"steps"`
	Dependencies []Dependency      `json:"dependencies"`
	Timeout      time.Duration     `json:"timeout"`
	RetryPolicy  *RetryPolicy      `json:"retry_policy,omitempty"`
}

// ExecutionStrategy 执行策略
type ExecutionStrategy string

const (
	StrategySequential  ExecutionStrategy = "sequential"
	StrategyParallel    ExecutionStrategy = "parallel"
	StrategyConditional ExecutionStrategy = "conditional"
	StrategyPipeline    ExecutionStrategy = "pipeline"
)

// ExecutionStep 执行步骤
type ExecutionStep struct {
	ID          string                 `json:"id"`
	AgentID     string                 `json:"agent_id"`
	Capability  string                 `json:"capability"`
	Parameters  map[string]interface{} `json:"parameters"`
	Order       int                    `json:"order"`
	Condition   string                 `json:"condition,omitempty"`
	Description string                 `json:"description"`
}

// Dependency 依赖关系
type Dependency struct {
	StepID    string `json:"step_id"`
	DependsOn string `json:"depends_on"`
	Type      string `json:"type"` // success, completion, data
	Condition string `json:"condition,omitempty"`
}

// RetryPolicy 重试策略
type RetryPolicy struct {
	MaxRetries    int           `json:"max_retries"`
	Delay         time.Duration `json:"delay"`
	BackoffFactor float64       `json:"backoff_factor"`
}

// NewDecisionEngine 创建决策引擎
func NewDecisionEngine(deepseekService interfaces.DeepSeekServiceInterface, registry *AgentRegistry, logger *logrus.Logger) *DecisionEngine {
	config := &DecisionConfig{
		MaxAgentsPerDecision:    5,
		DecisionTimeout:         30 * time.Second,
		EnableParallelExecution: true,
		ConfidenceThreshold:     0.7,
	}

	return &DecisionEngine{
		deepseekService: deepseekService,
		registry:        registry,
		logger:          logger,
		config:          config,
	}
}

// MakeDecision 进行决策
func (de *DecisionEngine) MakeDecision(ctx context.Context, request *DecisionRequest) (*DecisionResult, error) {
	start := time.Now()

	de.logger.WithFields(logrus.Fields{
		"user_message": request.UserMessage,
		"session_id":   request.Context.SessionID,
	}).Info("Starting decision making process")

	// 创建带超时的上下文
	ctx, cancel := context.WithTimeout(ctx, de.config.DecisionTimeout)
	defer cancel()

	// 获取可用Agent信息
	agentCapabilities := de.registry.GetAgentCapabilities()
	if len(agentCapabilities) == 0 {
		return nil, fmt.Errorf("no agents available for decision making")
	}

	// 构建决策提示
	systemPrompt := de.buildDecisionPrompt(agentCapabilities)
	userPrompt := de.buildUserPrompt(request)

	// 调用DeepSeek API
	messages := []interfaces.Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userPrompt},
	}

	response, err := de.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("failed to get decision from DeepSeek: %w", err)
	}

	if response == "" {
		return nil, fmt.Errorf("no response from DeepSeek")
	}

	// 解析决策结果
	result, err := de.parseDecisionResponse(response)
	if err != nil {
		return nil, fmt.Errorf("failed to parse decision response: %w", err)
	}

	// 验证决策结果
	if err := de.validateDecision(result); err != nil {
		return nil, fmt.Errorf("decision validation failed: %w", err)
	}

	result.CreatedAt = time.Now()
	result.Metadata = map[string]interface{}{
		"processing_time": time.Since(start),
		"agent_count":     len(agentCapabilities),
		"decision_count":  len(result.Agents),
	}

	de.logger.WithFields(logrus.Fields{
		"session_id":      request.Context.SessionID,
		"agents_selected": len(result.Agents),
		"confidence":      result.Confidence,
		"processing_time": time.Since(start),
	}).Info("Decision making completed")

	return result, nil
}

// buildDecisionPrompt 构建决策系统提示
func (de *DecisionEngine) buildDecisionPrompt(agentCapabilities map[string][]Capability) string {
	var prompt strings.Builder

	prompt.WriteString(`你是一个专业的智能运维决策引擎。根据用户的运维需求，分析并决策应该调用哪些Agent来完成任务。

可用的Agent及其能力：

`)

	// 添加Agent能力信息
	for agentID, capabilities := range agentCapabilities {
		prompt.WriteString(fmt.Sprintf("## Agent: %s\n", agentID))
		for _, cap := range capabilities {
			prompt.WriteString(fmt.Sprintf("- **%s** (%s): %s\n", cap.Name, cap.Type, cap.Description))
			if len(cap.Parameters) > 0 {
				prompt.WriteString("  参数: ")
				for i, param := range cap.Parameters {
					if i > 0 {
						prompt.WriteString(", ")
					}
					required := ""
					if param.Required {
						required = "*"
					}
					prompt.WriteString(fmt.Sprintf("%s%s(%s)", param.Name, required, param.Type))
				}
				prompt.WriteString("\n")
			}
		}
		prompt.WriteString("\n")
	}

	prompt.WriteString(`
决策规则：
1. 分析用户意图，选择最合适的Agent和能力
2. 为每个Agent生成具体的执行参数
3. 确定执行顺序和依赖关系
4. 支持多Agent协作场景

请返回JSON格式的决策结果：
{
  "success": true,
  "confidence": 0.95,
  "explanation": "决策说明",
  "agents": [
    {
      "agent_id": "agent_id",
      "capability": "capability_name",
      "parameters": {
        "param1": "value1",
        "param2": "value2"
      },
      "priority": 1,
      "description": "执行描述"
    }
  ],
  "execution_plan": {
    "strategy": "sequential",
    "steps": [
      {
        "id": "step_1",
        "agent_id": "agent_id",
        "capability": "capability_name",
        "parameters": {},
        "order": 1,
        "description": "步骤描述"
      }
    ],
    "dependencies": [],
    "timeout": "60s"
  }
}

特殊场景处理：
- "检查**************的系统状态" → 选择系统监控Agent，生成监控参数
- "备份数据库并检查网络连通性" → 协调备份恢复Agent和网络诊断Agent
- "添加主机************* root password123" → 选择主机管理Agent，生成添加主机参数
`)

	return prompt.String()
}

// buildUserPrompt 构建用户提示
func (de *DecisionEngine) buildUserPrompt(request *DecisionRequest) string {
	prompt := fmt.Sprintf("用户需求：%s", request.UserMessage)

	if request.Context != nil {
		if len(request.Context.History) > 0 {
			prompt += fmt.Sprintf("\n对话历史：%v", request.Context.History)
		}
		if len(request.Context.Variables) > 0 {
			prompt += fmt.Sprintf("\n上下文变量：%v", request.Context.Variables)
		}
	}

	return prompt
}

// parseDecisionResponse 解析决策响应
func (de *DecisionEngine) parseDecisionResponse(content string) (*DecisionResult, error) {
	var result DecisionResult

	// 尝试解析JSON
	if err := json.Unmarshal([]byte(content), &result); err != nil {
		return nil, fmt.Errorf("failed to parse JSON response: %w", err)
	}

	return &result, nil
}

// validateDecision 验证决策结果
func (de *DecisionEngine) validateDecision(result *DecisionResult) error {
	if !result.Success {
		return fmt.Errorf("decision marked as unsuccessful")
	}

	if result.Confidence < de.config.ConfidenceThreshold {
		return fmt.Errorf("decision confidence too low: %.2f < %.2f", result.Confidence, de.config.ConfidenceThreshold)
	}

	if len(result.Agents) == 0 {
		return fmt.Errorf("no agents selected in decision")
	}

	if len(result.Agents) > de.config.MaxAgentsPerDecision {
		return fmt.Errorf("too many agents selected: %d > %d", len(result.Agents), de.config.MaxAgentsPerDecision)
	}

	// 验证Agent是否存在
	for _, agentDecision := range result.Agents {
		if _, err := de.registry.GetAgent(agentDecision.AgentID); err != nil {
			return fmt.Errorf("agent not found: %s", agentDecision.AgentID)
		}
	}

	return nil
}

// GetDecisionCapabilities 获取决策能力信息（用于前端展示）
func (de *DecisionEngine) GetDecisionCapabilities() map[string]interface{} {
	agentCapabilities := de.registry.GetAgentCapabilities()

	capabilities := map[string]interface{}{
		"available_agents": len(agentCapabilities),
		"agent_details":    make(map[string]interface{}),
		"categories":       make(map[AgentCategory][]string),
	}

	agentDetails := capabilities["agent_details"].(map[string]interface{})
	categories := capabilities["categories"].(map[AgentCategory][]string)

	for agentID, caps := range agentCapabilities {
		if registration, err := de.registry.GetAgentRegistration(agentID); err == nil {
			agentDetails[agentID] = map[string]interface{}{
				"name":         registration.Metadata.Name,
				"category":     registration.Metadata.Category,
				"capabilities": caps,
				"status":       registration.Status,
			}

			categories[registration.Metadata.Category] = append(categories[registration.Metadata.Category], agentID)
		}
	}

	return capabilities
}
