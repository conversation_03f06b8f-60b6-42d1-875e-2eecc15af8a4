/* AI运维管理平台 - 可访问性和国际化系统 */
/* 无障碍设计、多语言支持、高对比度模式 */

/* ========================================
   无障碍设计基础
   ======================================== */

/* 焦点可见性增强 */
*:focus {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
  border-radius: var(--radius-sm);
}

*:focus:not(:focus-visible) {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

/* 跳转链接 */
.skip-link {
  position: absolute;
  top: -40px;
  left: 6px;
  background: var(--color-primary-600);
  color: white;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: var(--font-weight-semibold);
  z-index: var(--z-modal);
  transition: top var(--transition-fast);
}

.skip-link:focus {
  top: 6px;
}

/* 屏幕阅读器专用内容 */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  padding: inherit;
  margin: inherit;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

/* ========================================
   高对比度模式
   ======================================== */

@media (prefers-contrast: high) {
  :root {
    --color-primary-500: #0000ff;
    --color-primary-600: #0000cc;
    --color-success-500: #008000;
    --color-warning-500: #ff8c00;
    --color-error-500: #ff0000;
    --color-gray-900: #000000;
    --color-gray-100: #ffffff;
  }

  .btn {
    border: 2px solid currentColor;
    font-weight: var(--font-weight-bold);
  }

  .card,
  .input-wrapper,
  .message-content {
    border: 2px solid var(--color-gray-900);
  }

  .feature-card:hover,
  .quick-action-btn:hover {
    background: var(--color-primary-100);
    border-color: var(--color-primary-600);
  }
}

/* 强制高对比度模式 */
.high-contrast {
  --color-primary-500: #0000ff;
  --color-primary-600: #0000cc;
  --color-success-500: #008000;
  --color-warning-500: #ff8c00;
  --color-error-500: #ff0000;
  --color-gray-900: #000000;
  --color-gray-100: #ffffff;
  --color-gray-800: #1a1a1a;
  --color-gray-200: #e6e6e6;
}

.high-contrast .btn {
  border: 2px solid currentColor;
  font-weight: var(--font-weight-bold);
}

.high-contrast .card,
.high-contrast .input-wrapper,
.high-contrast .message-content {
  border: 2px solid var(--color-gray-900);
}

/* ========================================
   减少动画偏好
   ======================================== */

@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .particles-container {
    display: none !important;
  }

  .animated-gradient {
    background: var(--color-primary-100) !important;
  }

  .welcome-avatar,
  .typing-dot,
  .loading-spinner {
    animation: none !important;
  }
}

/* 强制减少动画模式 */
.reduced-motion *,
.reduced-motion *::before,
.reduced-motion *::after {
  animation-duration: 0.01ms !important;
  animation-iteration-count: 1 !important;
  transition-duration: 0.01ms !important;
  scroll-behavior: auto !important;
}

/* ========================================
   字体大小偏好
   ======================================== */

/* 大字体模式 */
.large-text {
  --font-size-xs: 0.875rem;    /* 14px */
  --font-size-sm: 1rem;        /* 16px */
  --font-size-base: 1.125rem;  /* 18px */
  --font-size-lg: 1.25rem;     /* 20px */
  --font-size-xl: 1.5rem;      /* 24px */
  --font-size-2xl: 1.875rem;   /* 30px */
  --font-size-3xl: 2.25rem;    /* 36px */
  --font-size-4xl: 3rem;       /* 48px */
}

/* 超大字体模式 */
.extra-large-text {
  --font-size-xs: 1rem;        /* 16px */
  --font-size-sm: 1.125rem;    /* 18px */
  --font-size-base: 1.25rem;   /* 20px */
  --font-size-lg: 1.5rem;      /* 24px */
  --font-size-xl: 1.875rem;    /* 30px */
  --font-size-2xl: 2.25rem;    /* 36px */
  --font-size-3xl: 3rem;       /* 48px */
  --font-size-4xl: 4rem;       /* 64px */
}

/* ========================================
   键盘导航增强
   ======================================== */

/* 键盘焦点指示器 */
.keyboard-user *:focus {
  outline: 3px solid var(--color-primary-500);
  outline-offset: 2px;
  box-shadow: 0 0 0 5px rgba(99, 102, 241, 0.2);
}

/* 跳转导航 */
.skip-nav {
  display: flex;
  gap: var(--spacing-2);
  padding: var(--spacing-2);
  background: var(--color-primary-600);
  position: absolute;
  top: -100px;
  left: 0;
  right: 0;
  z-index: var(--z-modal);
  transition: top var(--transition-fast);
}

.skip-nav:focus-within {
  top: 0;
}

.skip-nav a {
  color: white;
  text-decoration: none;
  padding: var(--spacing-2) var(--spacing-4);
  border-radius: var(--radius-md);
  font-weight: var(--font-weight-semibold);
}

.skip-nav a:focus {
  background: rgba(255, 255, 255, 0.2);
  outline: 2px solid white;
}

/* 焦点陷阱 */
.focus-trap {
  position: relative;
}

.focus-trap::before,
.focus-trap::after {
  content: '';
  position: absolute;
  width: 1px;
  height: 1px;
  opacity: 0;
  pointer-events: none;
}

/* ========================================
   多语言支持
   ======================================== */

/* 语言切换器 */
.language-switcher {
  position: relative;
  display: inline-block;
}

.language-toggle {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  background: white;
  cursor: pointer;
  transition: all var(--transition-base);
}

.language-toggle:hover {
  border-color: var(--color-primary-400);
  box-shadow: var(--shadow-sm);
}

.language-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-xl);
  min-width: 150px;
  z-index: var(--z-dropdown);
  opacity: 0;
  transform: translateY(-10px);
  transition: all var(--transition-base);
  pointer-events: none;
}

.language-dropdown.show {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
}

.language-option {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  padding: var(--spacing-3) var(--spacing-4);
  cursor: pointer;
  transition: background var(--transition-base);
  border: none;
  background: none;
  width: 100%;
  text-align: left;
}

.language-option:hover {
  background: var(--color-primary-50);
}

.language-option.active {
  background: var(--color-primary-100);
  color: var(--color-primary-700);
  font-weight: var(--font-weight-semibold);
}

.language-flag {
  width: var(--spacing-5);
  height: var(--spacing-4);
  border-radius: var(--radius-sm);
  background-size: cover;
  background-position: center;
  flex-shrink: 0;
}

/* RTL语言支持 */
[dir="rtl"] {
  text-align: right;
}

[dir="rtl"] .chat-brand {
  flex-direction: row-reverse;
}

[dir="rtl"] .message.user {
  justify-content: flex-start;
  padding-right: 15%;
  padding-left: 0;
}

[dir="rtl"] .message.assistant {
  justify-content: flex-end;
  padding-left: 15%;
  padding-right: 0;
}

[dir="rtl"] .message.user .message-avatar {
  order: -1;
  margin-right: var(--spacing-3);
  margin-left: 0;
}

[dir="rtl"] .message.assistant .message-avatar {
  margin-left: var(--spacing-3);
  margin-right: 0;
}

/* ========================================
   语音和屏幕阅读器支持
   ======================================== */

/* 语音播报区域 */
.live-region {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.live-region[aria-live="polite"] {
  /* 礼貌模式：等待当前语音结束 */
}

.live-region[aria-live="assertive"] {
  /* 断言模式：立即播报 */
}

/* 语音控制按钮 */
.voice-control {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-md);
  background: white;
  cursor: pointer;
  transition: all var(--transition-base);
}

.voice-control:hover {
  border-color: var(--color-primary-400);
  background: var(--color-primary-50);
}

.voice-control.active {
  background: var(--color-primary-500);
  color: white;
  border-color: var(--color-primary-600);
}

/* ========================================
   错误和状态公告
   ======================================== */

.status-announcer {
  position: absolute;
  left: -10000px;
  width: 1px;
  height: 1px;
  overflow: hidden;
}

.error-announcement {
  background: var(--color-error-100);
  color: var(--color-error-800);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--color-error-500);
  margin: var(--spacing-4) 0;
}

.success-announcement {
  background: var(--color-success-100);
  color: var(--color-success-800);
  padding: var(--spacing-4);
  border-radius: var(--radius-lg);
  border-left: 4px solid var(--color-success-500);
  margin: var(--spacing-4) 0;
}

/* ========================================
   打印样式
   ======================================== */

@media print {
  .no-print {
    display: none !important;
  }

  .chat-navbar,
  .chat-input-area,
  .particles-container,
  .settings-panel,
  .shortcuts-panel {
    display: none !important;
  }

  .chat-area {
    box-shadow: none !important;
    border: 1px solid #000 !important;
  }

  .message-content {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  body {
    background: white !important;
    color: black !important;
  }
}
