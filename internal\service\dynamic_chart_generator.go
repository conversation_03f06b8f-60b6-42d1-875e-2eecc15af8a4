package service

import (
	"context"
	"fmt"
	"math"
	"time"

	"github.com/sirupsen/logrus"
)

// DynamicChartGenerator 动态图表生成器
type DynamicChartGenerator struct {
	logger     *logrus.Logger
	renderers  map[string]ChartRenderer
	config     *ChartGeneratorConfig
}

// ChartGeneratorConfig 图表生成器配置
type ChartGeneratorConfig struct {
	DefaultWidth       int                    `json:"default_width"`
	DefaultHeight      int                    `json:"default_height"`
	ColorPalette       []string               `json:"color_palette"`
	EnableAnimations   bool                   `json:"enable_animations"`
	EnableInteractivity bool                  `json:"enable_interactivity"`
	DefaultTheme       string                 `json:"default_theme"`
	ChartOptions       map[string]interface{} `json:"chart_options"`
}

// ChartRenderer 图表渲染器接口
type ChartRenderer interface {
	Render(data interface{}, options map[string]interface{}) (*ChartData, error)
	GetType() string
	GetSupportedDataTypes() []string
}

// NewDynamicChartGenerator 创建动态图表生成器
func NewDynamicChartGenerator(logger *logrus.Logger) *DynamicChartGenerator {
	config := &ChartGeneratorConfig{
		DefaultWidth:        800,
		DefaultHeight:       400,
		ColorPalette:        []string{"#3498db", "#e74c3c", "#2ecc71", "#f39c12", "#9b59b6", "#1abc9c", "#34495e", "#e67e22"},
		EnableAnimations:    true,
		EnableInteractivity: true,
		DefaultTheme:        "light",
		ChartOptions: map[string]interface{}{
			"responsive": true,
			"maintainAspectRatio": false,
		},
	}

	generator := &DynamicChartGenerator{
		logger:    logger,
		renderers: make(map[string]ChartRenderer),
		config:    config,
	}

	// 注册默认渲染器
	generator.registerDefaultRenderers()

	return generator
}

// registerDefaultRenderers 注册默认渲染器
func (dcg *DynamicChartGenerator) registerDefaultRenderers() {
	renderers := []ChartRenderer{
		&LineChartRenderer{config: dcg.config},
		&BarChartRenderer{config: dcg.config},
		&PieChartRenderer{config: dcg.config},
		&AreaChartRenderer{config: dcg.config},
		&ScatterChartRenderer{config: dcg.config},
		&HeatmapChartRenderer{config: dcg.config},
		&GaugeChartRenderer{config: dcg.config},
		&RadarChartRenderer{config: dcg.config},
	}

	for _, renderer := range renderers {
		dcg.renderers[renderer.GetType()] = renderer
	}

	dcg.logger.WithField("renderers", len(dcg.renderers)).Info("Chart renderers registered")
}

// GenerateCharts 生成图表
func (dcg *DynamicChartGenerator) GenerateCharts(ctx context.Context, req *ReportRequest, data *ReportData) ([]*ChartData, error) {
	var charts []*ChartData

	// 根据报表类型和数据自动选择图表类型
	chartConfigs := dcg.determineChartTypes(req, data)

	for _, config := range chartConfigs {
		chart, err := dcg.generateSingleChart(config, data)
		if err != nil {
			dcg.logger.WithError(err).WithField("chart_type", config.Type).Warn("Failed to generate chart")
			continue
		}
		charts = append(charts, chart)
	}

	dcg.logger.WithField("charts_generated", len(charts)).Info("Charts generation completed")
	return charts, nil
}

// ChartConfig 图表配置
type ChartConfig struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"`
	Title    string                 `json:"title"`
	DataKey  string                 `json:"data_key"`
	Options  map[string]interface{} `json:"options"`
}

// determineChartTypes 确定图表类型
func (dcg *DynamicChartGenerator) determineChartTypes(req *ReportRequest, data *ReportData) []*ChartConfig {
	var configs []*ChartConfig

	// 根据报表类型生成不同的图表
	switch req.Type {
	case "operation":
		configs = append(configs, dcg.getOperationChartConfigs()...)
	case "system_health":
		configs = append(configs, dcg.getSystemHealthChartConfigs()...)
	case "ai_usage":
		configs = append(configs, dcg.getAIUsageChartConfigs()...)
	case "performance":
		configs = append(configs, dcg.getPerformanceChartConfigs()...)
	default:
		configs = append(configs, dcg.getDefaultChartConfigs()...)
	}

	// 如果用户指定了图表类型，优先使用
	if len(req.ChartTypes) > 0 {
		var userConfigs []*ChartConfig
		for i, chartType := range req.ChartTypes {
			userConfigs = append(userConfigs, &ChartConfig{
				ID:      fmt.Sprintf("user_chart_%d", i),
				Type:    chartType,
				Title:   fmt.Sprintf("自定义%s图表", chartType),
				DataKey: "metrics",
				Options: make(map[string]interface{}),
			})
		}
		configs = userConfigs
	}

	return configs
}

// getOperationChartConfigs 获取运维操作图表配置
func (dcg *DynamicChartGenerator) getOperationChartConfigs() []*ChartConfig {
	return []*ChartConfig{
		{
			ID:      "operation_trend",
			Type:    "line",
			Title:   "运维操作趋势",
			DataKey: "operation_trend",
			Options: map[string]interface{}{
				"xAxis": map[string]interface{}{"type": "time"},
				"yAxis": map[string]interface{}{"type": "value"},
			},
		},
		{
			ID:      "operation_types",
			Type:    "pie",
			Title:   "操作类型分布",
			DataKey: "operation_types",
			Options: map[string]interface{}{
				"showLegend": true,
			},
		},
		{
			ID:      "success_rate",
			Type:    "gauge",
			Title:   "操作成功率",
			DataKey: "success_rate",
			Options: map[string]interface{}{
				"min": 0,
				"max": 100,
				"unit": "%",
			},
		},
	}
}

// getSystemHealthChartConfigs 获取系统健康图表配置
func (dcg *DynamicChartGenerator) getSystemHealthChartConfigs() []*ChartConfig {
	return []*ChartConfig{
		{
			ID:      "resource_usage",
			Type:    "area",
			Title:   "资源使用率趋势",
			DataKey: "resource_usage",
			Options: map[string]interface{}{
				"stacked": true,
			},
		},
		{
			ID:      "host_status",
			Type:    "bar",
			Title:   "主机状态分布",
			DataKey: "host_status",
			Options: map[string]interface{}{
				"horizontal": false,
			},
		},
		{
			ID:      "alert_heatmap",
			Type:    "heatmap",
			Title:   "告警热力图",
			DataKey: "alert_distribution",
			Options: map[string]interface{}{
				"colorScale": "viridis",
			},
		},
	}
}

// getAIUsageChartConfigs 获取AI使用图表配置
func (dcg *DynamicChartGenerator) getAIUsageChartConfigs() []*ChartConfig {
	return []*ChartConfig{
		{
			ID:      "ai_requests",
			Type:    "line",
			Title:   "AI请求量趋势",
			DataKey: "ai_requests",
			Options: map[string]interface{}{
				"smooth": true,
			},
		},
		{
			ID:      "response_time",
			Type:    "scatter",
			Title:   "AI响应时间分布",
			DataKey: "response_times",
			Options: map[string]interface{}{
				"showTrendLine": true,
			},
		},
		{
			ID:      "model_usage",
			Type:    "radar",
			Title:   "模型使用情况",
			DataKey: "model_usage",
			Options: map[string]interface{}{
				"scale": "linear",
			},
		},
	}
}

// getPerformanceChartConfigs 获取性能图表配置
func (dcg *DynamicChartGenerator) getPerformanceChartConfigs() []*ChartConfig {
	return []*ChartConfig{
		{
			ID:      "performance_metrics",
			Type:    "line",
			Title:   "性能指标趋势",
			DataKey: "performance_metrics",
			Options: map[string]interface{}{
				"multiAxis": true,
			},
		},
		{
			ID:      "throughput",
			Type:    "bar",
			Title:   "吞吐量统计",
			DataKey: "throughput",
			Options: map[string]interface{}{
				"showValues": true,
			},
		},
	}
}

// getDefaultChartConfigs 获取默认图表配置
func (dcg *DynamicChartGenerator) getDefaultChartConfigs() []*ChartConfig {
	return []*ChartConfig{
		{
			ID:      "default_metrics",
			Type:    "line",
			Title:   "指标趋势",
			DataKey: "metrics",
			Options: make(map[string]interface{}),
		},
	}
}

// generateSingleChart 生成单个图表
func (dcg *DynamicChartGenerator) generateSingleChart(config *ChartConfig, data *ReportData) (*ChartData, error) {
	renderer, exists := dcg.renderers[config.Type]
	if !exists {
		return nil, fmt.Errorf("unsupported chart type: %s", config.Type)
	}

	// 从报表数据中提取图表数据
	chartData := dcg.extractChartData(config.DataKey, data)
	if chartData == nil {
		// 如果没有真实数据，生成模拟数据
		chartData = dcg.generateMockData(config.Type)
	}

	// 合并配置选项
	options := dcg.mergeOptions(config.Options)

	// 渲染图表
	chart, err := renderer.Render(chartData, options)
	if err != nil {
		return nil, fmt.Errorf("failed to render chart: %w", err)
	}

	// 设置图表基本信息
	chart.ID = config.ID
	chart.Title = config.Title

	return chart, nil
}

// extractChartData 提取图表数据
func (dcg *DynamicChartGenerator) extractChartData(dataKey string, data *ReportData) interface{} {
	if data == nil || data.Metrics == nil {
		return nil
	}

	if value, exists := data.Metrics[dataKey]; exists {
		return value
	}

	return nil
}

// generateMockData 生成模拟数据
func (dcg *DynamicChartGenerator) generateMockData(chartType string) interface{} {
	switch chartType {
	case "line", "area":
		return dcg.generateTimeSeriesData()
	case "bar":
		return dcg.generateCategoryData()
	case "pie":
		return dcg.generatePieData()
	case "scatter":
		return dcg.generateScatterData()
	case "gauge":
		return dcg.generateGaugeData()
	case "heatmap":
		return dcg.generateHeatmapData()
	case "radar":
		return dcg.generateRadarData()
	default:
		return dcg.generateTimeSeriesData()
	}
}

// generateTimeSeriesData 生成时间序列数据
func (dcg *DynamicChartGenerator) generateTimeSeriesData() interface{} {
	now := time.Now()
	var data []map[string]interface{}

	for i := 0; i < 24; i++ {
		timestamp := now.Add(-time.Duration(23-i) * time.Hour)
		value := 50 + 30*math.Sin(float64(i)*math.Pi/12) + float64(i%5)*5
		
		data = append(data, map[string]interface{}{
			"time":  timestamp.Format("2006-01-02 15:04:05"),
			"value": math.Round(value*100)/100,
		})
	}

	return map[string]interface{}{
		"series": []map[string]interface{}{
			{
				"name": "指标值",
				"data": data,
			},
		},
	}
}

// generateCategoryData 生成分类数据
func (dcg *DynamicChartGenerator) generateCategoryData() interface{} {
	categories := []string{"SSH操作", "数据库查询", "文件操作", "系统监控", "告警处理"}
	var data []map[string]interface{}

	for i, category := range categories {
		value := 100 + float64(i*20) + float64((i*7)%15)
		data = append(data, map[string]interface{}{
			"category": category,
			"value":    value,
		})
	}

	return map[string]interface{}{
		"categories": categories,
		"data":       data,
	}
}

// generatePieData 生成饼图数据
func (dcg *DynamicChartGenerator) generatePieData() interface{} {
	data := []map[string]interface{}{
		{"name": "成功", "value": 85, "color": "#2ecc71"},
		{"name": "失败", "value": 10, "color": "#e74c3c"},
		{"name": "警告", "value": 5, "color": "#f39c12"},
	}

	return map[string]interface{}{
		"data": data,
	}
}

// generateScatterData 生成散点图数据
func (dcg *DynamicChartGenerator) generateScatterData() interface{} {
	var data []map[string]interface{}

	for i := 0; i < 50; i++ {
		x := float64(i) + (float64(i%10)-5)*2
		y := 2*x + 10 + (float64(i%7)-3)*5
		
		data = append(data, map[string]interface{}{
			"x": math.Round(x*100)/100,
			"y": math.Round(y*100)/100,
		})
	}

	return map[string]interface{}{
		"data": data,
	}
}

// generateGaugeData 生成仪表盘数据
func (dcg *DynamicChartGenerator) generateGaugeData() interface{} {
	return map[string]interface{}{
		"value": 78.5,
		"min":   0,
		"max":   100,
		"unit":  "%",
		"ranges": []map[string]interface{}{
			{"min": 0, "max": 30, "color": "#e74c3c", "label": "差"},
			{"min": 30, "max": 70, "color": "#f39c12", "label": "中"},
			{"min": 70, "max": 100, "color": "#2ecc71", "label": "优"},
		},
	}
}

// generateHeatmapData 生成热力图数据
func (dcg *DynamicChartGenerator) generateHeatmapData() interface{} {
	hours := []string{"00", "04", "08", "12", "16", "20"}
	days := []string{"周一", "周二", "周三", "周四", "周五", "周六", "周日"}
	var data []map[string]interface{}

	for i, day := range days {
		for j, hour := range hours {
			value := 10 + float64((i+j*2)%20) + float64(i*j%10)
			data = append(data, map[string]interface{}{
				"day":   day,
				"hour":  hour,
				"value": value,
			})
		}
	}

	return map[string]interface{}{
		"xAxis": hours,
		"yAxis": days,
		"data":  data,
	}
}

// generateRadarData 生成雷达图数据
func (dcg *DynamicChartGenerator) generateRadarData() interface{} {
	indicators := []string{"CPU", "内存", "磁盘", "网络", "响应时间", "可用性"}
	data := []float64{85, 70, 60, 90, 75, 95}

	return map[string]interface{}{
		"indicators": indicators,
		"data":       data,
		"max":        100,
	}
}

// mergeOptions 合并选项
func (dcg *DynamicChartGenerator) mergeOptions(customOptions map[string]interface{}) map[string]interface{} {
	options := make(map[string]interface{})

	// 复制默认选项
	for k, v := range dcg.config.ChartOptions {
		options[k] = v
	}

	// 覆盖自定义选项
	for k, v := range customOptions {
		options[k] = v
	}

	// 添加主题和颜色
	options["theme"] = dcg.config.DefaultTheme
	options["colors"] = dcg.config.ColorPalette
	options["width"] = dcg.config.DefaultWidth
	options["height"] = dcg.config.DefaultHeight

	return options
}

// 具体图表渲染器实现

// LineChartRenderer 折线图渲染器
type LineChartRenderer struct {
	config *ChartGeneratorConfig
}

func (lcr *LineChartRenderer) GetType() string {
	return "line"
}

func (lcr *LineChartRenderer) GetSupportedDataTypes() []string {
	return []string{"time_series", "numeric"}
}

func (lcr *LineChartRenderer) Render(data interface{}, options map[string]interface{}) (*ChartData, error) {
	return &ChartData{
		Type:    "line",
		Data:    data,
		Options: options,
		Config: map[string]interface{}{
			"renderer": "chart.js",
			"version":  "3.9.1",
		},
	}, nil
}

// BarChartRenderer 柱状图渲染器
type BarChartRenderer struct {
	config *ChartGeneratorConfig
}

func (bcr *BarChartRenderer) GetType() string {
	return "bar"
}

func (bcr *BarChartRenderer) GetSupportedDataTypes() []string {
	return []string{"category", "numeric"}
}

func (bcr *BarChartRenderer) Render(data interface{}, options map[string]interface{}) (*ChartData, error) {
	return &ChartData{
		Type:    "bar",
		Data:    data,
		Options: options,
		Config: map[string]interface{}{
			"renderer": "chart.js",
			"version":  "3.9.1",
		},
	}, nil
}

// PieChartRenderer 饼图渲染器
type PieChartRenderer struct {
	config *ChartGeneratorConfig
}

func (pcr *PieChartRenderer) GetType() string {
	return "pie"
}

func (pcr *PieChartRenderer) GetSupportedDataTypes() []string {
	return []string{"category", "percentage"}
}

func (pcr *PieChartRenderer) Render(data interface{}, options map[string]interface{}) (*ChartData, error) {
	return &ChartData{
		Type:    "pie",
		Data:    data,
		Options: options,
		Config: map[string]interface{}{
			"renderer": "chart.js",
			"version":  "3.9.1",
		},
	}, nil
}

// AreaChartRenderer 面积图渲染器
type AreaChartRenderer struct {
	config *ChartGeneratorConfig
}

func (acr *AreaChartRenderer) GetType() string {
	return "area"
}

func (acr *AreaChartRenderer) GetSupportedDataTypes() []string {
	return []string{"time_series", "stacked"}
}

func (acr *AreaChartRenderer) Render(data interface{}, options map[string]interface{}) (*ChartData, error) {
	return &ChartData{
		Type:    "area",
		Data:    data,
		Options: options,
		Config: map[string]interface{}{
			"renderer": "chart.js",
			"version":  "3.9.1",
		},
	}, nil
}

// ScatterChartRenderer 散点图渲染器
type ScatterChartRenderer struct {
	config *ChartGeneratorConfig
}

func (scr *ScatterChartRenderer) GetType() string {
	return "scatter"
}

func (scr *ScatterChartRenderer) GetSupportedDataTypes() []string {
	return []string{"xy_data", "correlation"}
}

func (scr *ScatterChartRenderer) Render(data interface{}, options map[string]interface{}) (*ChartData, error) {
	return &ChartData{
		Type:    "scatter",
		Data:    data,
		Options: options,
		Config: map[string]interface{}{
			"renderer": "chart.js",
			"version":  "3.9.1",
		},
	}, nil
}

// HeatmapChartRenderer 热力图渲染器
type HeatmapChartRenderer struct {
	config *ChartGeneratorConfig
}

func (hcr *HeatmapChartRenderer) GetType() string {
	return "heatmap"
}

func (hcr *HeatmapChartRenderer) GetSupportedDataTypes() []string {
	return []string{"matrix", "grid"}
}

func (hcr *HeatmapChartRenderer) Render(data interface{}, options map[string]interface{}) (*ChartData, error) {
	return &ChartData{
		Type:    "heatmap",
		Data:    data,
		Options: options,
		Config: map[string]interface{}{
			"renderer": "echarts",
			"version":  "5.4.0",
		},
	}, nil
}

// GaugeChartRenderer 仪表盘渲染器
type GaugeChartRenderer struct {
	config *ChartGeneratorConfig
}

func (gcr *GaugeChartRenderer) GetType() string {
	return "gauge"
}

func (gcr *GaugeChartRenderer) GetSupportedDataTypes() []string {
	return []string{"single_value", "percentage"}
}

func (gcr *GaugeChartRenderer) Render(data interface{}, options map[string]interface{}) (*ChartData, error) {
	return &ChartData{
		Type:    "gauge",
		Data:    data,
		Options: options,
		Config: map[string]interface{}{
			"renderer": "echarts",
			"version":  "5.4.0",
		},
	}, nil
}

// RadarChartRenderer 雷达图渲染器
type RadarChartRenderer struct {
	config *ChartGeneratorConfig
}

func (rcr *RadarChartRenderer) GetType() string {
	return "radar"
}

func (rcr *RadarChartRenderer) GetSupportedDataTypes() []string {
	return []string{"multi_dimension", "comparison"}
}

func (rcr *RadarChartRenderer) Render(data interface{}, options map[string]interface{}) (*ChartData, error) {
	return &ChartData{
		Type:    "radar",
		Data:    data,
		Options: options,
		Config: map[string]interface{}{
			"renderer": "echarts",
			"version":  "5.4.0",
		},
	}, nil
}
