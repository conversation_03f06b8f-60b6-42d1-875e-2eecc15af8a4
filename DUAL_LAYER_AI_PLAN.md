# 双层AI意图识别系统实现计划

## 🎯 系统架构设计

### 第一层：意图分类器 (Intent Classifier)
**职责**：精确识别用户输入的运维意图类型
**输入**：用户原始消息
**输出**：意图类型 + 置信度 + 基础实体

**支持的意图类型**：
1. `connection_diagnosis` - 连接诊断（检查主机连接、登录问题）
2. `command_execution` - 命令执行（执行具体命令）
3. `system_monitoring` - 系统监控（CPU、内存、磁盘）
4. `service_management` - 服务管理（启动、停止、重启）
5. `log_analysis` - 日志分析（查看、搜索日志）
6. `file_operations` - 文件操作（查看、编辑、传输）
7. `network_diagnostics` - 网络诊断（ping、端口检查）
8. `security_check` - 安全检查（权限、进程扫描）
9. `host_management` - 主机管理（添加、删除、列表）
10. `performance_analysis` - 性能分析（性能瓶颈诊断）

### 第二层：参数推断器 (Parameter Inference Engine)
**职责**：基于意图类型和上下文，生成具体的执行参数和命令序列
**输入**：意图类型 + 用户消息 + 系统上下文
**输出**：结构化参数 + 命令序列 + 执行计划

### 统一调度器 (Unified Dispatcher)
**职责**：协调两层处理，管理执行流程
**功能**：
- 调度第一层和第二层处理
- 管理上下文和会话状态
- 处理错误和降级策略
- 生成用户友好的响应

## 🔧 核心组件实现

### 1. 第一层意图分类器
```go
type IntentClassifier struct {
    deepseekService *DeepSeekService
    contextManager  *ContextManager
    logger         *logrus.Logger
    config         *ClassifierConfig
}

type ClassificationResult struct {
    Intent     string                 `json:"intent"`
    Confidence float64                `json:"confidence"`
    Entities   map[string]interface{} `json:"entities"`
    Context    map[string]interface{} `json:"context"`
    Reasoning  string                 `json:"reasoning"`
}
```

### 2. 第二层参数推断器
```go
type ParameterInferenceEngine struct {
    deepseekService *DeepSeekService
    hostService     HostServiceInterface
    logger         *logrus.Logger
    scenarioHandlers map[string]ScenarioHandler
}

type InferenceResult struct {
    Parameters    map[string]interface{} `json:"parameters"`
    Commands      []CommandSequence      `json:"commands"`
    ExecutionPlan *ExecutionPlan         `json:"execution_plan"`
    Explanation   string                 `json:"explanation"`
}
```

### 3. 统一调度器
```go
type UnifiedDispatcher struct {
    classifier      *IntentClassifier
    inferenceEngine *ParameterInferenceEngine
    executionEngine *ExecutionEngine
    responseBuilder *ResponseBuilder
    logger         *logrus.Logger
}
```

## 📋 实现步骤

### 阶段1：核心架构搭建
1. 创建双层AI意图识别的基础接口和数据结构
2. 实现第一层意图分类器
3. 实现第二层参数推断器
4. 创建统一调度器框架

### 阶段2：场景化处理器
1. 实现连接诊断场景处理器
2. 实现命令执行场景处理器
3. 实现系统监控场景处理器
4. 实现网络诊断场景处理器

### 阶段3：智能推断优化
1. 添加上下文感知能力
2. 实现命令序列生成
3. 优化错误处理和降级策略
4. 添加执行计划生成

### 阶段4：集成和测试
1. 集成到现有WebSocket处理流程
2. 更新AI服务接口
3. 添加全面的测试用例
4. 性能优化和监控

## 🎯 关键场景实现

### 场景1："检查**************执行命令报什么错误"
**第一层输出**：
```json
{
  "intent": "connection_diagnosis",
  "confidence": 0.95,
  "entities": {
    "ip_address": "**************",
    "action": "check_command_execution"
  }
}
```

**第二层输出**：
```json
{
  "parameters": {
    "target_host": "**************",
    "diagnosis_type": "command_execution_error"
  },
  "commands": [
    {
      "step": 1,
      "command": "ssh -o ConnectTimeout=5 -o BatchMode=yes {host} 'echo connection_test'",
      "description": "测试SSH连接"
    },
    {
      "step": 2,
      "command": "ssh {host} 'whoami && pwd && date'",
      "description": "测试基础命令执行"
    }
  ],
  "execution_plan": {
    "strategy": "sequential",
    "timeout": 30,
    "error_handling": "continue_on_error"
  }
}
```

### 场景2："检查**************登录报什么错误"
**第一层输出**：
```json
{
  "intent": "connection_diagnosis",
  "confidence": 0.92,
  "entities": {
    "ip_address": "**************",
    "action": "check_login_error"
  }
}
```

**第二层输出**：
```json
{
  "parameters": {
    "target_host": "**************",
    "diagnosis_type": "ssh_authentication"
  },
  "commands": [
    {
      "step": 1,
      "command": "nc -zv {host} 22",
      "description": "检查SSH端口连通性"
    },
    {
      "step": 2,
      "command": "ssh -o ConnectTimeout=5 -o PasswordAuthentication=no {host} 'echo test'",
      "description": "测试SSH认证"
    }
  ]
}
```
