# 🚀 AI运维管理平台生产环境部署指南

## 📋 概述

本指南将帮助您将AI运维管理平台从开发/测试环境迁移到生产环境，确保系统使用真实数据并具备企业级的安全性和可靠性。

## 🎯 部署前准备

### 1. 环境要求

- **操作系统**: Linux (Ubuntu 20.04+ / CentOS 8+ / RHEL 8+)
- **Go版本**: 1.19或更高版本
- **内存**: 最少2GB，推荐4GB+
- **磁盘**: 最少10GB可用空间
- **网络**: 需要访问DeepSeek API (api.deepseek.com)

### 2. 必要的环境变量

在部署前，请准备以下环境变量：

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量文件
nano .env
```

**必须设置的关键变量**：

```bash
# DeepSeek API密钥 (必填)
AIOPS_DEEPSEEK_API_KEY=sk-your-actual-deepseek-api-key-here

# JWT安全密钥 (必填，至少32字符)
AIOPS_JWT_SECRET=your-strong-jwt-secret-key-at-least-32-characters-long

# 数据加密密钥 (必填，必须32字节)
AIOPS_ENCRYPTION_KEY=your-32-byte-encryption-key-here

# 环境设置
AIOPS_ENV=production
AIOPS_DEBUG=false
```

### 3. 获取DeepSeek API密钥

1. 访问 [DeepSeek官网](https://platform.deepseek.com/)
2. 注册账号并完成实名认证
3. 在控制台创建API密钥
4. 确保账户有足够的余额

## 🧹 清理测试数据

### 自动清理

运行我们提供的清理脚本：

```bash
# 运行数据库清理工具
cd scripts
go run cleanup_test_data.go
```

### 手动清理

如果需要手动清理，可以删除以下测试数据：

```bash
# 删除测试数据库（如果需要全新开始）
rm -f ./data/aiops.db

# 清理缓存文件
rm -rf ./cache/*

# 清理旧日志
rm -f ./logs/*.log
```

## 🔧 配置生产环境

### 1. 检查配置

运行配置检查工具确保所有设置正确：

```bash
cd scripts
go run check_production_config.go
```

### 2. 数据库配置

生产环境建议使用以下数据库设置：

```yaml
# configs/config.yaml
database:
  path: "./data/aiops.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "1h"
  conn_max_idle_time: "10m"
```

### 3. 安全配置

确保以下安全设置已启用：

```yaml
# configs/config.yaml
security:
  encryption_key: "${AIOPS_ENCRYPTION_KEY}"
  password_hash_cost: 12
  session_timeout: "24h"
  rate_limit:
    enabled: true
    global: "1000/min"
    per_user: "100/min"
    per_ip: "200/min"

advanced_security:
  enable_threat_detection: true
  enable_rate_limit: true
  enable_audit_log: true
  enable_encryption: true
```

### 4. 智能Agent配置

启用所有智能功能：

```yaml
# configs/config.yaml
ai:
  enable_intelligent_mode: true
  intelligent_agent:
    enable_auto_execution: true
    confidence_threshold: 0.7
    max_concurrent_tasks: 10
    default_timeout: "5m"
    enable_fallback: true
```

## 🚀 自动化部署

### 使用部署脚本

我们提供了完整的自动化部署脚本：

```bash
# 设置环境变量
export AIOPS_DEEPSEEK_API_KEY="your-actual-api-key"
export AIOPS_JWT_SECRET="your-strong-jwt-secret"
export AIOPS_ENCRYPTION_KEY="your-32-byte-encryption-key"

# 运行部署脚本
chmod +x scripts/deploy_production.sh
./scripts/deploy_production.sh
```

部署脚本将自动执行：
- ✅ 环境检查
- ✅ 依赖验证
- ✅ 测试数据清理
- ✅ 应用程序构建
- ✅ 数据库迁移
- ✅ 服务配置
- ✅ 防火墙设置
- ✅ 备份脚本创建
- ✅ 定时任务设置
- ✅ 服务启动

## 📊 添加真实数据

### 1. 添加真实主机

通过Web界面或API添加您的实际服务器：

```bash
# 示例：通过API添加主机
curl -X POST http://localhost:8080/api/v1/hosts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "name": "production-web-01",
    "ip_address": "**********",
    "port": 22,
    "username": "admin",
    "description": "生产环境Web服务器"
  }'
```

### 2. 配置真实用户

创建实际的运维用户账号：

```bash
# 通过Web界面注册用户，或使用API
curl -X POST http://localhost:8080/api/v1/users \
  -H "Content-Type: application/json" \
  -d '{
    "username": "ops_admin",
    "email": "<EMAIL>",
    "full_name": "运维管理员",
    "role": "admin"
  }'
```

### 3. 导入现有监控数据

如果您有现有的监控系统，可以通过API导入数据：

```bash
# 导入告警规则
curl -X POST http://localhost:8080/api/v1/alerts \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{
    "title": "CPU使用率过高",
    "description": "服务器CPU使用率超过80%",
    "severity": "warning",
    "host_id": 1
  }'
```

## 🔍 验证部署

### 1. 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/health

# 检查监控指标
curl http://localhost:9090/metrics
```

### 2. 功能测试

1. **Web界面访问**: http://localhost:8080
2. **用户登录**: 使用创建的管理员账号
3. **AI对话测试**: 尝试发送运维指令
4. **主机管理**: 添加和管理真实主机
5. **监控告警**: 验证告警功能

### 3. 性能验证

```bash
# 查看系统资源使用
sudo systemctl status aiops-platform

# 查看日志
sudo journalctl -u aiops-platform -f

# 查看性能指标
curl http://localhost:9090/metrics | grep aiops
```

## 🛡️ 安全最佳实践

### 1. 网络安全

```bash
# 配置防火墙
sudo ufw allow 8080/tcp
sudo ufw allow 9090/tcp
sudo ufw enable

# 配置反向代理 (推荐使用Nginx)
sudo apt install nginx
```

### 2. SSL/TLS配置

```nginx
# /etc/nginx/sites-available/aiops-platform
server {
    listen 443 ssl;
    server_name your-domain.com;
    
    ssl_certificate /path/to/your/certificate.crt;
    ssl_certificate_key /path/to/your/private.key;
    
    location / {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 3. 定期备份

```bash
# 手动备份
./scripts/backup.sh

# 查看定时备份任务
crontab -l
```

## 📈 监控和维护

### 1. 日志监控

```bash
# 实时查看日志
tail -f ./logs/aiops.log

# 查看系统服务日志
sudo journalctl -u aiops-platform -f
```

### 2. 性能监控

- **CPU/内存使用**: 通过 `/metrics` 端点
- **响应时间**: 内置性能监控
- **错误率**: 日志分析
- **缓存命中率**: 缓存统计

### 3. 定期维护

```bash
# 每周执行
./scripts/backup.sh
./scripts/cleanup_old_logs.sh

# 每月执行
./scripts/database_optimize.sh
./scripts/security_audit.sh
```

## 🆘 故障排除

### 常见问题

1. **服务启动失败**
   ```bash
   sudo journalctl -u aiops-platform -n 50
   ```

2. **API密钥错误**
   ```bash
   # 检查环境变量
   echo $AIOPS_DEEPSEEK_API_KEY
   ```

3. **数据库连接问题**
   ```bash
   # 检查数据库文件权限
   ls -la ./data/aiops.db
   ```

4. **内存不足**
   ```bash
   # 查看内存使用
   free -h
   top -p $(pgrep aiops-platform)
   ```

### 获取支持

- **日志文件**: `./logs/aiops.log`
- **配置文件**: `./configs/config.yaml`
- **系统状态**: `sudo systemctl status aiops-platform`
- **进程信息**: `ps aux | grep aiops-platform`

## 🎉 部署完成

恭喜！您已成功将AI运维管理平台部署到生产环境。系统现在：

- ✅ 使用真实的生产数据
- ✅ 启用了所有智能功能
- ✅ 配置了企业级安全
- ✅ 具备高可用性和可扩展性
- ✅ 支持自动备份和监控

您的AI运维管理平台已准备好处理真实的运维工作负载！🚀
