/* ========================================
   消息交互功能JavaScript
   提供类似ChatGPT的消息交互体验
   ======================================== */

class MessageInteractions {
    constructor() {
        this.messageVersions = new Map(); // 存储消息的多个版本
        this.messageReactions = new Map(); // 存储消息反应
        this.init();
    }
    
    init() {
        this.bindGlobalEvents();
    }
    
    bindGlobalEvents() {
        // 监听消息容器的点击事件（事件委托）
        document.addEventListener('click', (e) => {
            if (e.target.closest('.action-btn')) {
                this.handleActionClick(e);
            }
        });
        
        // 监听键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                if (e.key === 'c' && e.target.closest('.message')) {
                    this.copyMessage(e.target.closest('.message'));
                }
            }
        });
    }
    
    // 创建增强的消息元素
    createEnhancedMessage(sender, content, messageId = null) {
        const messageId_ = messageId || this.generateMessageId();
        const timestamp = new Date();
        
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}`;
        messageDiv.dataset.messageId = messageId_;
        
        messageDiv.innerHTML = `
            <div class="message-avatar">
                ${sender === 'user' ? 'U' : 'AI'}
            </div>
            <div class="message-content-area">
                <div class="message-content">
                    ${this.formatMessageContent(content)}
                </div>
                <div class="message-meta">
                    <span class="message-time">${this.formatTime(timestamp)}</span>
                    <div class="message-status">
                        <div class="status-icon sent"></div>
                        <span>已发送</span>
                    </div>
                </div>
                <div class="message-actions">
                    ${this.createActionButtons(sender, messageId_)}
                </div>
                <div class="message-reactions" id="reactions-${messageId_}"></div>
            </div>
        `;
        
        return messageDiv;
    }
    
    createActionButtons(sender, messageId) {
        const commonActions = `
            <button class="action-btn copy-btn" data-action="copy" data-message-id="${messageId}" title="复制消息">
                <i class="bi bi-clipboard"></i>
            </button>
            <button class="action-btn quote-btn" data-action="quote" data-message-id="${messageId}" title="引用回复">
                <i class="bi bi-quote"></i>
            </button>
        `;
        
        if (sender === 'assistant') {
            return `
                ${commonActions}
                <button class="action-btn regenerate-btn" data-action="regenerate" data-message-id="${messageId}" title="重新生成">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
                <button class="action-btn like-btn" data-action="like" data-message-id="${messageId}" title="点赞">
                    <i class="bi bi-hand-thumbs-up"></i>
                </button>
                <button class="action-btn dislike-btn" data-action="dislike" data-message-id="${messageId}" title="点踩">
                    <i class="bi bi-hand-thumbs-down"></i>
                </button>
                <button class="action-btn share-btn" data-action="share" data-message-id="${messageId}" title="分享">
                    <i class="bi bi-share"></i>
                </button>
            `;
        } else {
            return `
                ${commonActions}
                <button class="action-btn edit-btn" data-action="edit" data-message-id="${messageId}" title="编辑消息">
                    <i class="bi bi-pencil"></i>
                </button>
            `;
        }
    }
    
    handleActionClick(e) {
        const button = e.target.closest('.action-btn');
        const action = button.dataset.action;
        const messageId = button.dataset.messageId;
        const messageElement = button.closest('.message');
        
        switch (action) {
            case 'copy':
                this.copyMessage(messageElement);
                break;
            case 'quote':
                this.quoteMessage(messageElement);
                break;
            case 'regenerate':
                this.regenerateMessage(messageId, messageElement);
                break;
            case 'like':
                this.toggleLike(messageId, button);
                break;
            case 'dislike':
                this.toggleDislike(messageId, button);
                break;
            case 'share':
                this.shareMessage(messageElement);
                break;
            case 'edit':
                this.editMessage(messageId, messageElement);
                break;
        }
    }
    
    // 复制消息内容
    copyMessage(messageElement) {
        const content = messageElement.querySelector('.message-content').textContent;
        
        navigator.clipboard.writeText(content).then(() => {
            this.showCopyFeedback(messageElement);
        }).catch(() => {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = content;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            this.showCopyFeedback(messageElement);
        });
    }
    
    showCopyFeedback(messageElement) {
        const feedback = document.createElement('div');
        feedback.className = 'copy-success';
        feedback.textContent = '已复制到剪贴板';
        
        const actionsDiv = messageElement.querySelector('.message-actions');
        actionsDiv.style.position = 'relative';
        actionsDiv.appendChild(feedback);
        
        setTimeout(() => {
            if (feedback.parentNode) {
                feedback.parentNode.removeChild(feedback);
            }
        }, 2000);
    }
    
    // 引用回复
    quoteMessage(messageElement) {
        const content = messageElement.querySelector('.message-content').textContent;
        const sender = messageElement.classList.contains('user') ? '用户' : 'AI助手';
        const time = messageElement.querySelector('.message-time').textContent;
        
        const quoteText = `> ${sender} ${time}\n> ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}\n\n`;
        
        // 如果有智能输入组件，插入引用文本
        if (window.smartInput) {
            const currentValue = window.smartInput.getValue();
            window.smartInput.setValue(currentValue + quoteText);
            window.smartInput.focus();
        }
    }
    
    // 重新生成消息
    regenerateMessage(messageId, messageElement) {
        const regenerateBtn = messageElement.querySelector('.regenerate-btn');
        const originalIcon = regenerateBtn.innerHTML;
        
        // 显示加载状态
        regenerateBtn.innerHTML = '<div class="regenerating-spinner"></div>';
        regenerateBtn.disabled = true;
        
        // 添加重新生成指示器
        const indicator = document.createElement('div');
        indicator.className = 'regenerating-indicator';
        indicator.innerHTML = `
            <div class="regenerating-spinner"></div>
            <span>正在重新生成回答...</span>
        `;
        
        const contentArea = messageElement.querySelector('.message-content-area');
        contentArea.appendChild(indicator);
        
        // 模拟重新生成（实际应该调用API）
        setTimeout(() => {
            this.handleRegenerateResponse(messageId, messageElement, '这是重新生成的回答内容。');
            regenerateBtn.innerHTML = originalIcon;
            regenerateBtn.disabled = false;
            if (indicator.parentNode) {
                indicator.parentNode.removeChild(indicator);
            }
        }, 2000);
    }
    
    handleRegenerateResponse(messageId, messageElement, newContent) {
        // 保存当前版本
        const currentContent = messageElement.querySelector('.message-content').innerHTML;
        if (!this.messageVersions.has(messageId)) {
            this.messageVersions.set(messageId, [currentContent]);
        }
        this.messageVersions.get(messageId).push(this.formatMessageContent(newContent));
        
        // 更新消息内容
        messageElement.querySelector('.message-content').innerHTML = this.formatMessageContent(newContent);
        
        // 添加版本切换控件
        this.addVersionControls(messageId, messageElement);
    }
    
    addVersionControls(messageId, messageElement) {
        const versions = this.messageVersions.get(messageId);
        if (versions.length <= 1) return;
        
        let versionControls = messageElement.querySelector('.message-versions');
        if (!versionControls) {
            versionControls = document.createElement('div');
            versionControls.className = 'message-versions';
            messageElement.querySelector('.message-content-area').appendChild(versionControls);
        }
        
        const currentIndex = versions.length - 1;
        versionControls.innerHTML = `
            <span class="version-info">${currentIndex + 1} / ${versions.length}</span>
            <div class="version-nav">
                <button class="version-btn" data-direction="prev" ${currentIndex === 0 ? 'disabled' : ''}>
                    <i class="bi bi-chevron-left"></i>
                </button>
                <button class="version-btn" data-direction="next" ${currentIndex === versions.length - 1 ? 'disabled' : ''}>
                    <i class="bi bi-chevron-right"></i>
                </button>
            </div>
        `;
        
        // 绑定版本切换事件
        versionControls.addEventListener('click', (e) => {
            if (e.target.closest('.version-btn')) {
                this.switchVersion(messageId, messageElement, e.target.closest('.version-btn').dataset.direction);
            }
        });
    }
    
    switchVersion(messageId, messageElement, direction) {
        const versions = this.messageVersions.get(messageId);
        const contentElement = messageElement.querySelector('.message-content');
        const currentContent = contentElement.innerHTML;
        const currentIndex = versions.indexOf(currentContent);
        
        let newIndex;
        if (direction === 'prev') {
            newIndex = Math.max(0, currentIndex - 1);
        } else {
            newIndex = Math.min(versions.length - 1, currentIndex + 1);
        }
        
        contentElement.innerHTML = versions[newIndex];
        this.updateVersionControls(messageElement, newIndex, versions.length);
    }
    
    updateVersionControls(messageElement, currentIndex, totalVersions) {
        const versionControls = messageElement.querySelector('.message-versions');
        const versionInfo = versionControls.querySelector('.version-info');
        const prevBtn = versionControls.querySelector('[data-direction="prev"]');
        const nextBtn = versionControls.querySelector('[data-direction="next"]');
        
        versionInfo.textContent = `${currentIndex + 1} / ${totalVersions}`;
        prevBtn.disabled = currentIndex === 0;
        nextBtn.disabled = currentIndex === totalVersions - 1;
    }
    
    // 点赞/点踩功能
    toggleLike(messageId, button) {
        const dislikeBtn = button.parentNode.querySelector('.dislike-btn');
        
        if (button.classList.contains('active')) {
            button.classList.remove('active');
        } else {
            button.classList.add('active');
            dislikeBtn.classList.remove('active');
        }
        
        this.updateReactionCount(messageId, 'like', button.classList.contains('active'));
    }
    
    toggleDislike(messageId, button) {
        const likeBtn = button.parentNode.querySelector('.like-btn');
        
        if (button.classList.contains('active')) {
            button.classList.remove('active');
        } else {
            button.classList.add('active');
            likeBtn.classList.remove('active');
        }
        
        this.updateReactionCount(messageId, 'dislike', button.classList.contains('active'));
    }
    
    updateReactionCount(messageId, type, isActive) {
        // 这里可以发送到服务器记录用户反馈
        console.log(`Message ${messageId} ${type}: ${isActive}`);
    }
    
    // 分享消息
    shareMessage(messageElement) {
        const content = messageElement.querySelector('.message-content').textContent;
        const shareData = {
            title: 'AI运维助手对话',
            text: content,
            url: window.location.href
        };
        
        if (navigator.share) {
            navigator.share(shareData);
        } else {
            // 降级方案：复制链接
            this.copyMessage(messageElement);
        }
    }
    
    // 编辑消息
    editMessage(messageId, messageElement) {
        const contentElement = messageElement.querySelector('.message-content');
        const currentContent = contentElement.textContent;
        
        // 创建编辑输入框
        const editInput = document.createElement('textarea');
        editInput.className = 'edit-input';
        editInput.value = currentContent;
        editInput.style.cssText = `
            width: 100%;
            min-height: 60px;
            padding: 8px;
            border: 1px solid var(--border-primary);
            border-radius: 4px;
            font-family: inherit;
            font-size: inherit;
            resize: vertical;
        `;
        
        // 替换内容
        contentElement.style.display = 'none';
        contentElement.parentNode.insertBefore(editInput, contentElement);
        
        // 创建编辑控制按钮
        const editControls = document.createElement('div');
        editControls.className = 'edit-controls';
        editControls.style.cssText = 'margin-top: 8px; display: flex; gap: 8px;';
        editControls.innerHTML = `
            <button class="btn btn-sm btn-primary save-edit">保存</button>
            <button class="btn btn-sm btn-secondary cancel-edit">取消</button>
        `;
        
        editInput.parentNode.insertBefore(editControls, editInput.nextSibling);
        editInput.focus();
        
        // 绑定编辑控制事件
        editControls.querySelector('.save-edit').addEventListener('click', () => {
            this.saveEdit(messageId, messageElement, editInput.value);
        });
        
        editControls.querySelector('.cancel-edit').addEventListener('click', () => {
            this.cancelEdit(messageElement, editInput, editControls);
        });
    }
    
    saveEdit(messageId, messageElement, newContent) {
        const contentElement = messageElement.querySelector('.message-content');
        const editInput = messageElement.querySelector('.edit-input');
        const editControls = messageElement.querySelector('.edit-controls');
        
        // 更新内容
        contentElement.innerHTML = this.formatMessageContent(newContent);
        contentElement.style.display = 'block';
        
        // 移除编辑元素
        editInput.remove();
        editControls.remove();
        
        // 重新发送消息（如果需要）
        if (window.sendSmartMessage) {
            window.sendSmartMessage(newContent);
        }
    }
    
    cancelEdit(messageElement, editInput, editControls) {
        const contentElement = messageElement.querySelector('.message-content');
        contentElement.style.display = 'block';
        editInput.remove();
        editControls.remove();
    }
    
    // 工具方法
    generateMessageId() {
        return 'msg_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }
    
    formatTime(date) {
        return date.toLocaleTimeString('zh-CN', { 
            hour: '2-digit', 
            minute: '2-digit' 
        });
    }
    
    formatMessageContent(content) {
        // 简单的Markdown格式化
        return content
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/`(.*?)`/g, '<code>$1</code>')
            .replace(/\n/g, '<br>');
    }
}

// 初始化消息交互功能
window.messageInteractions = new MessageInteractions();
