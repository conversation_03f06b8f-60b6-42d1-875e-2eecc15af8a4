package intent_engine

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// MultiLayerIntentClassifier 多层意图分类器
type MultiLayerIntentClassifier struct {
	deepseekService interface{}
	logger          *logrus.Logger

	// 分类器配置
	layer1Threshold float64
	layer2Threshold float64
	layer3Threshold float64

	// 缓存
	classificationCache map[string]*ClassificationResult
}

// ClassificationResult 分类结果
type ClassificationResult struct {
	IntentID    string                 `json:"intent_id"`
	IntentName  string                 `json:"intent_name"`
	Category    string                 `json:"category"`
	SubCategory string                 `json:"sub_category"`
	Confidence  float64                `json:"confidence"`
	Layer       int                    `json:"layer"`
	Description string                 `json:"description"`
	Keywords    []string               `json:"keywords"`
	Parameters  map[string]interface{} `json:"parameters"`
	Timestamp   time.Time              `json:"timestamp"`
}

// Layer1Result 第一层分类结果
type Layer1Result struct {
	Category    string   `json:"category"`
	Confidence  float64  `json:"confidence"`
	Keywords    []string `json:"keywords"`
	Description string   `json:"description"`
}

// NewMultiLayerIntentClassifier 创建多层意图分类器
func NewMultiLayerIntentClassifier(deepseekService interface{}, logger *logrus.Logger) *MultiLayerIntentClassifier {
	return &MultiLayerIntentClassifier{
		deepseekService:     deepseekService,
		logger:              logger,
		layer1Threshold:     0.9,
		layer2Threshold:     0.85,
		layer3Threshold:     0.8,
		classificationCache: make(map[string]*ClassificationResult),
	}
}

// ClassifyLayer1 第一层分类：粗粒度意图分类
func (mlc *MultiLayerIntentClassifier) ClassifyLayer1(ctx context.Context, req *RevolutionaryIntentRequest) (*Layer1Result, error) {
	mlc.logger.WithFields(logrus.Fields{
		"request_id": req.ID,
		"message":    req.Message,
	}).Info("Starting Layer 1 intent classification")

	// 构建DeepSeek提示词
	systemPrompt := `你是一个专业的AI运维意图分类器。请将用户的运维请求分类到以下6个主要类别之一：

1. **ops_operations** - 运维操作类
   - 主机管理、服务器操作、系统配置
   - 关键词：主机、服务器、添加、删除、配置、部署

2. **system_monitoring** - 系统监控类  
   - 性能监控、状态检查、资源使用情况
   - 关键词：监控、状态、性能、CPU、内存、磁盘、网络

3. **network_diagnostics** - 网络诊断类
   - 网络连接、端口检查、网络故障排查
   - 关键词：网络、连接、ping、端口、防火墙、路由

4. **security_audit** - 安全审计类
   - 安全检查、权限管理、漏洞扫描
   - 关键词：安全、权限、用户、密码、审计、漏洞

5. **data_analysis** - 数据分析类
   - 日志分析、数据查询、统计报表
   - 关键词：日志、查询、统计、分析、报表、数据

6. **conversational** - 对话交互类
   - 帮助、问候、一般性对话
   - 关键词：帮助、你好、谢谢、怎么用

请返回JSON格式：
{
  "category": "分类名称",
  "confidence": 0.95,
  "keywords": ["关键词1", "关键词2"],
  "description": "分类说明"
}`

	userPrompt := fmt.Sprintf("用户输入：%s", req.Message)

	// 调用DeepSeek API进行分类
	result, err := mlc.callDeepSeekAPI(ctx, systemPrompt, userPrompt)
	if err != nil {
		return nil, fmt.Errorf("DeepSeek API call failed: %w", err)
	}

	// 解析结果
	var layer1Result Layer1Result
	if err := json.Unmarshal([]byte(result), &layer1Result); err != nil {
		// 如果解析失败，使用规则分类作为降级
		return mlc.fallbackLayer1Classification(req.Message), nil
	}

	mlc.logger.WithFields(logrus.Fields{
		"request_id": req.ID,
		"category":   layer1Result.Category,
		"confidence": layer1Result.Confidence,
	}).Info("Layer 1 classification completed")

	return &layer1Result, nil
}

// ClassifyLayer2 第二层分类：细粒度意图识别
func (mlc *MultiLayerIntentClassifier) ClassifyLayer2(ctx context.Context, req *RevolutionaryIntentRequest, layer1 *Layer1Result) (*ClassificationResult, error) {
	mlc.logger.WithFields(logrus.Fields{
		"request_id": req.ID,
		"category":   layer1.Category,
	}).Info("Starting Layer 2 intent classification")

	// 根据第一层分类结果，进行细粒度分类
	var systemPrompt string
	switch layer1.Category {
	case "ops_operations":
		systemPrompt = mlc.getOpsOperationsPrompt()
	case "system_monitoring":
		systemPrompt = mlc.getSystemMonitoringPrompt()
	case "network_diagnostics":
		systemPrompt = mlc.getNetworkDiagnosticsPrompt()
	case "security_audit":
		systemPrompt = mlc.getSecurityAuditPrompt()
	case "data_analysis":
		systemPrompt = mlc.getDataAnalysisPrompt()
	case "conversational":
		systemPrompt = mlc.getConversationalPrompt()
	default:
		return mlc.fallbackLayer2Classification(req.Message, layer1.Category), nil
	}

	userPrompt := fmt.Sprintf("用户输入：%s\n第一层分类：%s", req.Message, layer1.Category)

	// 调用DeepSeek API进行细粒度分类
	result, err := mlc.callDeepSeekAPI(ctx, systemPrompt, userPrompt)
	if err != nil {
		return mlc.fallbackLayer2Classification(req.Message, layer1.Category), nil
	}

	// 解析结果
	var classificationResult ClassificationResult
	if err := json.Unmarshal([]byte(result), &classificationResult); err != nil {
		return mlc.fallbackLayer2Classification(req.Message, layer1.Category), nil
	}

	classificationResult.Layer = 2
	classificationResult.Timestamp = time.Now()

	mlc.logger.WithFields(logrus.Fields{
		"request_id":  req.ID,
		"intent_name": classificationResult.IntentName,
		"confidence":  classificationResult.Confidence,
	}).Info("Layer 2 classification completed")

	return &classificationResult, nil
}

// callDeepSeekAPI 调用DeepSeek API
func (mlc *MultiLayerIntentClassifier) callDeepSeekAPI(ctx context.Context, systemPrompt, userPrompt string) (string, error) {
	mlc.logger.WithFields(logrus.Fields{
		"system_prompt_length": len(systemPrompt),
		"user_prompt":          userPrompt,
	}).Debug("Calling DeepSeek API for intent classification")

	// 尝试调用真实的DeepSeek API
	if deepseekService, ok := mlc.deepseekService.(interface {
		ProcessMessage(ctx context.Context, message string, intent interface{}) (interface{}, error)
	}); ok {
		// 调用DeepSeek服务
		response, err := deepseekService.ProcessMessage(ctx, fmt.Sprintf("%s\n\n%s", systemPrompt, userPrompt), nil)
		if err != nil {
			mlc.logger.WithError(err).Warn("DeepSeek API call failed, using enhanced fallback")
			return mlc.enhancedFallbackClassification(userPrompt), nil
		}

		// 尝试从响应中提取结果
		if respMap, ok := response.(map[string]interface{}); ok {
			if content, exists := respMap["content"]; exists {
				return fmt.Sprintf("%v", content), nil
			}
		}

		mlc.logger.Warn("DeepSeek API response format unexpected, using enhanced fallback")
		return mlc.enhancedFallbackClassification(userPrompt), nil
	}

	// 如果DeepSeek服务不可用，使用增强的降级分类
	mlc.logger.Debug("DeepSeek service not available, using enhanced fallback classification")
	return mlc.enhancedFallbackClassification(userPrompt), nil
}

// enhancedFallbackClassification 增强的降级分类
func (mlc *MultiLayerIntentClassifier) enhancedFallbackClassification(userPrompt string) string {
	message := strings.ToLower(userPrompt)

	// 主机管理操作 - 细分不同类型
	if strings.Contains(message, "主机") || strings.Contains(message, "服务器") {
		// 密码修改操作
		if strings.Contains(message, "密码") || strings.Contains(message, "修改") {
			return `{
				"category": "ops_operations",
				"confidence": 0.95,
				"keywords": ["主机", "密码", "修改"],
				"description": "主机密码修改操作"
			}`
		}

		// 主机列表查询
		if strings.Contains(message, "列出") || strings.Contains(message, "查看") || strings.Contains(message, "显示") {
			return `{
				"category": "ops_operations",
				"confidence": 0.93,
				"keywords": ["主机", "列出", "查看"],
				"description": "主机列表查询操作"
			}`
		}

		// 添加主机
		if strings.Contains(message, "添加") || strings.Contains(message, "新增") {
			return `{
				"category": "ops_operations",
				"confidence": 0.94,
				"keywords": ["主机", "添加"],
				"description": "添加主机操作"
			}`
		}

		// 通用主机操作
		return `{
			"category": "ops_operations",
			"confidence": 0.88,
			"keywords": ["主机", "服务器"],
			"description": "通用主机管理操作"
		}`
	}

	// 监控相关操作
	if strings.Contains(message, "监控") || strings.Contains(message, "状态") || strings.Contains(message, "性能") {
		return `{
			"category": "system_monitoring",
			"confidence": 0.90,
			"keywords": ["监控", "状态", "性能"],
			"description": "系统监控类意图"
		}`
	}

	// 网络相关操作
	if strings.Contains(message, "网络") || strings.Contains(message, "连接") || strings.Contains(message, "ping") {
		return `{
			"category": "network_diagnostics",
			"confidence": 0.89,
			"keywords": ["网络", "连接"],
			"description": "网络诊断类意图"
		}`
	}

	// 默认返回对话类
	return `{
		"category": "conversational",
		"confidence": 0.75,
		"keywords": ["对话"],
		"description": "一般对话交互"
	}`
}

// fallbackLayer1Classification 第一层分类降级方案
func (mlc *MultiLayerIntentClassifier) fallbackLayer1Classification(message string) *Layer1Result {
	lowerMessage := strings.ToLower(message)

	if strings.Contains(lowerMessage, "主机") || strings.Contains(lowerMessage, "服务器") {
		return &Layer1Result{
			Category:    "ops_operations",
			Confidence:  0.8,
			Keywords:    []string{"主机", "服务器"},
			Description: "运维操作类意图（降级分类）",
		}
	}

	if strings.Contains(lowerMessage, "监控") || strings.Contains(lowerMessage, "状态") {
		return &Layer1Result{
			Category:    "system_monitoring",
			Confidence:  0.8,
			Keywords:    []string{"监控", "状态"},
			Description: "系统监控类意图（降级分类）",
		}
	}

	// 默认返回对话类
	return &Layer1Result{
		Category:    "conversational",
		Confidence:  0.6,
		Keywords:    []string{"对话"},
		Description: "一般对话交互（降级分类）",
	}
}

// fallbackLayer2Classification 第二层分类降级方案
func (mlc *MultiLayerIntentClassifier) fallbackLayer2Classification(message, category string) *ClassificationResult {
	lowerMessage := strings.ToLower(message)

	// 针对ops_operations进行智能细分
	if category == "ops_operations" {
		// 主机密码修改
		if (strings.Contains(lowerMessage, "密码") && (strings.Contains(lowerMessage, "修改") || strings.Contains(lowerMessage, "更改") || strings.Contains(lowerMessage, "改"))) ||
			(strings.Contains(lowerMessage, "主机") && strings.Contains(lowerMessage, "密码")) {
			return &ClassificationResult{
				IntentID:    "host_password_change",
				IntentName:  "host_password_change",
				Category:    category,
				SubCategory: "host_management",
				Confidence:  0.92,
				Layer:       2,
				Description: "主机密码修改操作",
				Keywords:    []string{"主机", "密码", "修改"},
				Parameters:  make(map[string]interface{}),
				Timestamp:   time.Now(),
			}
		}

		// 主机列表查询
		if (strings.Contains(lowerMessage, "列出") || strings.Contains(lowerMessage, "查看") || strings.Contains(lowerMessage, "显示")) &&
			(strings.Contains(lowerMessage, "主机") || strings.Contains(lowerMessage, "服务器")) {
			return &ClassificationResult{
				IntentID:    "host_list",
				IntentName:  "host_list",
				Category:    category,
				SubCategory: "host_management",
				Confidence:  0.90,
				Layer:       2,
				Description: "主机列表查询操作",
				Keywords:    []string{"主机", "列出", "查看"},
				Parameters:  make(map[string]interface{}),
				Timestamp:   time.Now(),
			}
		}

		// 添加主机
		if (strings.Contains(lowerMessage, "添加") || strings.Contains(lowerMessage, "新增")) &&
			(strings.Contains(lowerMessage, "主机") || strings.Contains(lowerMessage, "服务器")) {
			return &ClassificationResult{
				IntentID:    "host_add",
				IntentName:  "host_add",
				Category:    category,
				SubCategory: "host_management",
				Confidence:  0.88,
				Layer:       2,
				Description: "添加主机操作",
				Keywords:    []string{"主机", "添加"},
				Parameters:  make(map[string]interface{}),
				Timestamp:   time.Now(),
			}
		}
	}

	// 默认通用分类
	return &ClassificationResult{
		IntentID:    fmt.Sprintf("%s_general", category),
		IntentName:  fmt.Sprintf("%s_general_operation", category),
		Category:    category,
		SubCategory: "general",
		Confidence:  0.6,
		Layer:       2,
		Description: fmt.Sprintf("通用%s操作（降级分类）", category),
		Keywords:    []string{"通用"},
		Parameters:  make(map[string]interface{}),
		Timestamp:   time.Now(),
	}
}

// getOpsOperationsPrompt 获取运维操作类提示词
func (mlc *MultiLayerIntentClassifier) getOpsOperationsPrompt() string {
	return `你是专业的运维操作专家。请将用户的运维操作请求精确分类为以下具体意图之一：

**主机管理类操作：**
1. **host_add** - 添加主机
   - 关键词：添加主机、新增服务器、注册主机
   - 示例：添加主机************* 用户名root 密码secure123

2. **host_list** - 查看主机列表
   - 关键词：列出主机、查看主机、显示服务器、主机列表
   - 示例：列出主机、查看所有服务器

3. **host_password_change** - 修改主机密码
   - 关键词：修改密码、更改密码、密码修改、改密码
   - 示例：修改**************这台主机密码为1qaz#EDC

4. **host_update** - 更新主机信息
   - 关键词：更新主机、修改主机信息、编辑主机
   - 示例：更新主机配置、修改主机备注

5. **host_delete** - 删除主机
   - 关键词：删除主机、移除服务器、注销主机
   - 示例：删除主机*************

**服务管理类操作：**
6. **service_start** - 启动服务
7. **service_stop** - 停止服务
8. **service_restart** - 重启服务

**系统配置类操作：**
9. **config_update** - 更新配置
10. **deployment** - 应用部署
11. **maintenance** - 系统维护

**重要识别规则：**
- 包含"密码"+"修改/更改/改"的请求 → host_password_change
- 包含"列出/查看/显示"+"主机/服务器"的请求 → host_list
- 包含"添加/新增"+"主机/服务器"的请求 → host_add
- 包含IP地址+密码修改的请求 → host_password_change

返回JSON格式：
{
  "intent_id": "具体意图ID",
  "intent_name": "意图名称",
  "category": "ops_operations",
  "sub_category": "host_management",
  "confidence": 0.95,
  "description": "意图描述",
  "keywords": ["关键词"],
  "parameters": {}
}`
}

// getSystemMonitoringPrompt 获取系统监控类提示词
func (mlc *MultiLayerIntentClassifier) getSystemMonitoringPrompt() string {
	return `你是系统监控专家。请将用户的监控请求细分为以下具体意图之一：

1. **performance_check** - 性能检查
2. **resource_usage** - 资源使用情况
3. **health_status** - 健康状态检查
4. **alert_management** - 告警管理
5. **metric_analysis** - 指标分析
6. **trend_analysis** - 趋势分析
7. **capacity_planning** - 容量规划
8. **availability_check** - 可用性检查

返回JSON格式：
{
  "intent_id": "具体意图ID",
  "intent_name": "意图名称",
  "category": "system_monitoring", 
  "sub_category": "具体子类别",
  "confidence": 0.95,
  "description": "意图描述",
  "keywords": ["关键词"],
  "parameters": {}
}`
}

// getNetworkDiagnosticsPrompt 获取网络诊断类提示词
func (mlc *MultiLayerIntentClassifier) getNetworkDiagnosticsPrompt() string {
	return `你是网络诊断专家。请将用户的网络诊断请求细分为以下具体意图之一：

1. **connectivity_test** - 连接测试
2. **port_scan** - 端口扫描
3. **network_trace** - 网络跟踪
4. **bandwidth_test** - 带宽测试
5. **dns_lookup** - DNS查询
6. **firewall_check** - 防火墙检查
7. **route_analysis** - 路由分析
8. **latency_test** - 延迟测试

返回JSON格式：
{
  "intent_id": "具体意图ID", 
  "intent_name": "意图名称",
  "category": "network_diagnostics",
  "sub_category": "具体子类别", 
  "confidence": 0.95,
  "description": "意图描述",
  "keywords": ["关键词"],
  "parameters": {}
}`
}

// getSecurityAuditPrompt 获取安全审计类提示词
func (mlc *MultiLayerIntentClassifier) getSecurityAuditPrompt() string {
	return `你是安全审计专家。请将用户的安全审计请求细分为以下具体意图之一：

1. **vulnerability_scan** - 漏洞扫描
2. **permission_check** - 权限检查
3. **user_audit** - 用户审计
4. **access_log_review** - 访问日志审查
5. **security_policy_check** - 安全策略检查
6. **compliance_audit** - 合规性审计

返回JSON格式：
{
  "intent_id": "具体意图ID",
  "intent_name": "意图名称", 
  "category": "security_audit",
  "sub_category": "具体子类别",
  "confidence": 0.95,
  "description": "意图描述", 
  "keywords": ["关键词"],
  "parameters": {}
}`
}

// getDataAnalysisPrompt 获取数据分析类提示词
func (mlc *MultiLayerIntentClassifier) getDataAnalysisPrompt() string {
	return `你是数据分析专家。请将用户的数据分析请求细分为以下具体意图之一：

1. **log_analysis** - 日志分析
2. **data_query** - 数据查询
3. **report_generation** - 报表生成
4. **statistical_analysis** - 统计分析
5. **data_visualization** - 数据可视化
6. **trend_prediction** - 趋势预测
7. **anomaly_detection** - 异常检测
8. **data_export** - 数据导出

返回JSON格式：
{
  "intent_id": "具体意图ID",
  "intent_name": "意图名称",
  "category": "data_analysis", 
  "sub_category": "具体子类别",
  "confidence": 0.95,
  "description": "意图描述",
  "keywords": ["关键词"], 
  "parameters": {}
}`
}

// getConversationalPrompt 获取对话交互类提示词
func (mlc *MultiLayerIntentClassifier) getConversationalPrompt() string {
	return `你是对话交互专家。请将用户的对话请求细分为以下具体意图之一：

1. **greeting** - 问候
2. **help_request** - 帮助请求
3. **general_question** - 一般问题
4. **system_info** - 系统信息查询
5. **feature_inquiry** - 功能咨询
6. **feedback** - 反馈意见

返回JSON格式：
{
  "intent_id": "具体意图ID",
  "intent_name": "意图名称",
  "category": "conversational",
  "sub_category": "具体子类别", 
  "confidence": 0.95,
  "description": "意图描述",
  "keywords": ["关键词"],
  "parameters": {}
}`
}
