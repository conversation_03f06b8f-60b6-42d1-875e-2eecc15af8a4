package service

import (
	"context"
	"database/sql"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// SQLGenerator SQL生成器
type SQLGenerator struct {
	db     *gorm.DB
	logger *logrus.Logger
	schema *DatabaseSchema
}

// NewSQLGenerator 创建SQL生成器
func NewSQLGenerator(db *gorm.DB, logger *logrus.Logger) *SQLGenerator {
	generator := &SQLGenerator{
		db:     db,
		logger: logger,
		schema: NewDatabaseSchema(db),
	}

	// 初始化数据库模式
	if err := generator.schema.LoadSchema(); err != nil {
		logger.WithError(err).Error("Failed to load database schema")
	} else {
		logger.WithField("tables_loaded", len(generator.schema.tables)).Info("Database schema loaded successfully")
		for tableName, table := range generator.schema.tables {
			logger.WithFields(logrus.Fields{
				"table":  tableName,
				"fields": len(table.Fields),
			}).Debug("Table schema loaded")
		}
	}

	return generator
}

// GenerateSQL 生成SQL语句
func (g *SQLGenerator) GenerateSQL(ctx context.Context, request *DatabaseOperationRequest) (*SQLGenerationResult, error) {
	// 验证表名
	if !g.schema.IsValidTable(request.TableName) {
		return nil, fmt.Errorf("invalid table name: %s", request.TableName)
	}

	var sql string
	var estimatedRows int64
	var err error

	switch request.OperationType {
	case "select":
		sql, estimatedRows, err = g.generateSelectSQL(request)
	case "insert":
		sql, err = g.generateInsertSQL(request)
		estimatedRows = 1
	case "update":
		sql, estimatedRows, err = g.generateUpdateSQL(request)
	case "delete":
		sql, estimatedRows, err = g.generateDeleteSQL(request)
	case "describe":
		sql, err = g.generateDescribeSQL(request)
		estimatedRows = 1
	default:
		return nil, fmt.Errorf("unsupported operation type: %s", request.OperationType)
	}

	if err != nil {
		return nil, err
	}

	// 计算使用的字段
	usedFields := g.extractUsedFields(sql, request.TableName)

	return &SQLGenerationResult{
		SQL:           sql,
		IsValid:       true,
		UsedFields:    usedFields,
		Confidence:    1.0,
		Attempts:      1,
		EstimatedRows: estimatedRows,
		TableName:     request.TableName,
		OperationType: request.OperationType,
		Metadata:      map[string]interface{}{"method": "traditional"},
	}, nil
}

// generateSelectSQL 生成SELECT语句
func (g *SQLGenerator) generateSelectSQL(request *DatabaseOperationRequest) (string, int64, error) {
	table := g.schema.GetTable(request.TableName)
	if table == nil {
		return "", 0, fmt.Errorf("table not found: %s", request.TableName)
	}

	// 构建SELECT子句
	selectClause := "SELECT *"

	// 构建FROM子句
	fromClause := fmt.Sprintf("FROM %s", request.TableName)

	// 构建WHERE子句
	whereClause, args := g.buildWhereClause(request.Conditions, table)

	// 构建LIMIT子句
	limit := request.Limit
	if limit <= 0 {
		limit = 10 // 默认限制
	}
	if limit > 100 {
		limit = 100 // 最大限制
	}
	limitClause := fmt.Sprintf("LIMIT %d", limit)

	// 组合SQL
	sql := fmt.Sprintf("%s %s", selectClause, fromClause)
	if whereClause != "" {
		sql += " " + whereClause
	}
	sql += " " + limitClause

	// 估算行数
	estimatedRows, err := g.estimateRowCount(request.TableName, request.Conditions)
	if err != nil {
		g.logger.WithError(err).Warn("Failed to estimate row count")
		estimatedRows = int64(limit)
	}

	g.logger.WithFields(logrus.Fields{
		"sql":            sql,
		"args":           args,
		"estimated_rows": estimatedRows,
	}).Debug("Generated SELECT SQL")

	return sql, estimatedRows, nil
}

// generateInsertSQL 生成INSERT语句
func (g *SQLGenerator) generateInsertSQL(request *DatabaseOperationRequest) (string, error) {
	table := g.schema.GetTable(request.TableName)
	if table == nil {
		return "", fmt.Errorf("table not found: %s", request.TableName)
	}

	if len(request.Data) == 0 {
		return "", fmt.Errorf("no data provided for insert")
	}

	// 验证字段
	for field := range request.Data {
		if !table.HasField(field) {
			g.logger.WithFields(logrus.Fields{
				"field":            field,
				"table":            request.TableName,
				"available_fields": table.Fields,
			}).Error("Field validation failed")
			return "", fmt.Errorf("invalid field: %s", field)
		}
	}

	// 构建字段列表和值列表
	var fields []string
	var values []string

	for field, value := range request.Data {
		fields = append(fields, field)
		values = append(values, g.formatValue(value))
	}

	sql := fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		request.TableName,
		strings.Join(fields, ", "),
		strings.Join(values, ", "))

	g.logger.WithField("sql", sql).Debug("Generated INSERT SQL")
	return sql, nil
}

// generateUpdateSQL 生成UPDATE语句
func (g *SQLGenerator) generateUpdateSQL(request *DatabaseOperationRequest) (string, int64, error) {
	table := g.schema.GetTable(request.TableName)
	if table == nil {
		return "", 0, fmt.Errorf("table not found: %s", request.TableName)
	}

	if len(request.Data) == 0 {
		return "", 0, fmt.Errorf("no data provided for update")
	}

	if len(request.Conditions) == 0 {
		return "", 0, fmt.Errorf("no conditions provided for update (safety check)")
	}

	// 验证字段
	for field := range request.Data {
		if !table.HasField(field) {
			return "", 0, fmt.Errorf("invalid field: %s", field)
		}
	}

	// 构建SET子句
	var setParts []string
	for field, value := range request.Data {
		setParts = append(setParts, fmt.Sprintf("%s = %s", field, g.formatValue(value)))
	}
	setClause := "SET " + strings.Join(setParts, ", ")

	// 构建WHERE子句
	whereClause, _ := g.buildWhereClause(request.Conditions, table)
	if whereClause == "" {
		return "", 0, fmt.Errorf("invalid conditions for update")
	}

	sql := fmt.Sprintf("UPDATE %s %s %s", request.TableName, setClause, whereClause)

	// 估算影响行数
	estimatedRows, err := g.estimateRowCount(request.TableName, request.Conditions)
	if err != nil {
		g.logger.WithError(err).Warn("Failed to estimate affected rows")
		estimatedRows = 1
	}

	g.logger.WithFields(logrus.Fields{
		"sql":            sql,
		"estimated_rows": estimatedRows,
	}).Debug("Generated UPDATE SQL")

	return sql, estimatedRows, nil
}

// generateDeleteSQL 生成DELETE语句
func (g *SQLGenerator) generateDeleteSQL(request *DatabaseOperationRequest) (string, int64, error) {
	table := g.schema.GetTable(request.TableName)
	if table == nil {
		return "", 0, fmt.Errorf("table not found: %s", request.TableName)
	}

	if len(request.Conditions) == 0 {
		return "", 0, fmt.Errorf("no conditions provided for delete (safety check)")
	}

	// 构建WHERE子句
	whereClause, _ := g.buildWhereClause(request.Conditions, table)
	if whereClause == "" {
		return "", 0, fmt.Errorf("invalid conditions for delete")
	}

	sql := fmt.Sprintf("DELETE FROM %s %s", request.TableName, whereClause)

	// 估算影响行数
	estimatedRows, err := g.estimateRowCount(request.TableName, request.Conditions)
	if err != nil {
		g.logger.WithError(err).Warn("Failed to estimate affected rows")
		estimatedRows = 1
	}

	g.logger.WithFields(logrus.Fields{
		"sql":            sql,
		"estimated_rows": estimatedRows,
	}).Debug("Generated DELETE SQL")

	return sql, estimatedRows, nil
}

// generateDescribeSQL 生成DESCRIBE语句
func (g *SQLGenerator) generateDescribeSQL(request *DatabaseOperationRequest) (string, error) {
	sql := fmt.Sprintf("PRAGMA table_info(%s)", request.TableName)
	g.logger.WithField("sql", sql).Debug("Generated DESCRIBE SQL")
	return sql, nil
}

// buildWhereClause 构建WHERE子句
func (g *SQLGenerator) buildWhereClause(conditions map[string]interface{}, table *TableSchema) (string, []interface{}) {
	if len(conditions) == 0 {
		return "", nil
	}

	var parts []string
	var args []interface{}

	for field, value := range conditions {
		if !table.HasField(field) {
			continue // 跳过无效字段
		}

		parts = append(parts, fmt.Sprintf("%s = %s", field, g.formatValue(value)))
		args = append(args, value)
	}

	if len(parts) == 0 {
		return "", nil
	}

	return "WHERE " + strings.Join(parts, " AND "), args
}

// formatValue 格式化值
func (g *SQLGenerator) formatValue(value interface{}) string {
	if value == nil {
		return "NULL"
	}

	switch v := value.(type) {
	case string:
		return fmt.Sprintf("'%s'", strings.ReplaceAll(v, "'", "''"))
	case int, int64, float64:
		return fmt.Sprintf("%v", v)
	case bool:
		if v {
			return "1"
		}
		return "0"
	default:
		return fmt.Sprintf("'%v'", v)
	}
}

// estimateRowCount 估算行数
func (g *SQLGenerator) estimateRowCount(tableName string, conditions map[string]interface{}) (int64, error) {
	var count int64

	query := g.db.Table(tableName)
	for field, value := range conditions {
		query = query.Where(fmt.Sprintf("%s = ?", field), value)
	}

	err := query.Count(&count).Error
	return count, err
}

// SQLGenerationResult SQL生成结果
type SQLGenerationResult struct {
	SQL             string                 `json:"sql"`
	IsValid         bool                   `json:"is_valid"`
	ValidationError string                 `json:"validation_error,omitempty"`
	UsedFields      []string               `json:"used_fields"`
	Confidence      float64                `json:"confidence"`
	Attempts        int                    `json:"attempts"`
	EstimatedRows   int64                  `json:"estimated_rows"`
	TableName       string                 `json:"table_name"`
	OperationType   string                 `json:"operation_type"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// DatabaseSchema 数据库模式
type DatabaseSchema struct {
	db     *gorm.DB
	tables map[string]*TableSchema
}

// TableSchema 表模式
type TableSchema struct {
	Name   string            `json:"name"`
	Fields map[string]string `json:"fields"` // field_name -> field_type
}

// NewDatabaseSchema 创建数据库模式
func NewDatabaseSchema(db *gorm.DB) *DatabaseSchema {
	return &DatabaseSchema{
		db:     db,
		tables: make(map[string]*TableSchema),
	}
}

// LoadSchema 加载数据库模式
func (ds *DatabaseSchema) LoadSchema() error {
	// 定义支持的表和字段
	supportedTables := map[string]interface{}{
		"hosts":          &struct{}{}, // 简化的表结构定义
		"users":          &struct{}{},
		"alerts":         &struct{}{},
		"operation_logs": &struct{}{},
		"chat_sessions":  &struct{}{},
		"chat_messages":  &struct{}{},
	}

	for tableName := range supportedTables {
		schema := &TableSchema{
			Name:   tableName,
			Fields: make(map[string]string),
		}

		// 获取表字段信息
		rows, err := ds.db.Raw(fmt.Sprintf("PRAGMA table_info(%s)", tableName)).Rows()
		if err != nil {
			continue // 表不存在，跳过
		}

		for rows.Next() {
			var cid int
			var name, dataType string
			var defaultValue sql.NullString
			var notNull, pk int

			if err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk); err != nil {
				continue
			}

			schema.Fields[name] = dataType
		}
		rows.Close()

		ds.tables[tableName] = schema
	}

	return nil
}

// IsValidTable 检查表是否有效
func (ds *DatabaseSchema) IsValidTable(tableName string) bool {
	_, exists := ds.tables[tableName]
	return exists
}

// GetTable 获取表模式
func (ds *DatabaseSchema) GetTable(tableName string) *TableSchema {
	return ds.tables[tableName]
}

// HasField 检查字段是否存在
func (ts *TableSchema) HasField(fieldName string) bool {
	_, exists := ts.Fields[fieldName]
	return exists
}

// extractUsedFields 提取SQL中使用的字段
func (g *SQLGenerator) extractUsedFields(sql, tableName string) []string {
	var fields []string
	sqlLower := strings.ToLower(sql)

	table := g.schema.GetTable(tableName)
	if table == nil {
		return fields
	}

	// 简单的字段提取逻辑
	for fieldName := range table.Fields {
		fieldLower := strings.ToLower(fieldName)
		if strings.Contains(sqlLower, fieldLower) {
			fields = append(fields, fieldName)
		}
	}

	return fields
}

// IntelligentSQLGenerationRequest 智能SQL生成请求
type IntelligentSQLGenerationRequest struct {
	UserInput    string                 `json:"user_input"`
	Intent       map[string]interface{} `json:"intent"`
	TableName    string                 `json:"table_name"`
	Operation    string                 `json:"operation"`
	MaxRetries   int                    `json:"max_retries"`
}

// GenerateIntelligentSQL 智能生成SQL（完美方案）
func (g *SQLGenerator) GenerateIntelligentSQL(ctx context.Context, req *IntelligentSQLGenerationRequest, deepseek *EnhancedDeepSeekService) (*SQLGenerationResult, error) {
	g.logger.WithFields(logrus.Fields{
		"user_input": req.UserInput,
		"table_name": req.TableName,
		"operation":  req.Operation,
	}).Info("🧠 智能SQL生成器：开始生成SQL")

	// 设置默认重试次数
	if req.MaxRetries == 0 {
		req.MaxRetries = 3
	}

	var lastError error
	for attempt := 1; attempt <= req.MaxRetries; attempt++ {
		g.logger.WithField("attempt", attempt).Info("🔄 尝试生成SQL")

		// 生成SQL
		result, err := g.generateSQLAttempt(ctx, req, attempt, lastError, deepseek)
		if err != nil {
			lastError = err
			continue
		}

		// 验证SQL
		if g.validateSQL(result.SQL, req.TableName) {
			result.IsValid = true
			result.Attempts = attempt
			result.Confidence = g.calculateConfidence(result, attempt)

			g.logger.WithFields(logrus.Fields{
				"sql":        result.SQL,
				"attempts":   attempt,
				"confidence": result.Confidence,
			}).Info("✅ 智能SQL生成成功")

			return result, nil
		} else {
			lastError = fmt.Errorf("SQL validation failed: %s", result.ValidationError)
			g.logger.WithFields(logrus.Fields{
				"sql":   result.SQL,
				"error": result.ValidationError,
			}).Warn("❌ SQL验证失败，准备重试")
		}
	}

	return &SQLGenerationResult{
		IsValid:         false,
		ValidationError: fmt.Sprintf("Failed after %d attempts: %v", req.MaxRetries, lastError),
		Attempts:        req.MaxRetries,
		Confidence:      0.0,
		TableName:       req.TableName,
		OperationType:   req.Operation,
	}, lastError
}

// generateSQLAttempt 单次SQL生成尝试
func (g *SQLGenerator) generateSQLAttempt(ctx context.Context, req *IntelligentSQLGenerationRequest, attempt int, previousError error, deepseek *EnhancedDeepSeekService) (*SQLGenerationResult, error) {
	// 获取表结构
	table := g.schema.GetTable(req.TableName)
	if table == nil {
		return nil, fmt.Errorf("table schema not found: %s", req.TableName)
	}

	// 构建增强的系统提示词
	systemPrompt := g.buildEnhancedSystemPrompt(table, req, attempt, previousError)

	// 调用DeepSeek生成SQL
	deepseekReq := &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: g.buildUserPrompt(req),
			},
		},
		MaxTokens:   1500,
		Temperature: 0.1, // 低温度确保一致性
	}

	response, err := deepseek.CallWithComplexity(ctx, deepseekReq, ComplexityMedium)
	if err != nil {
		return nil, fmt.Errorf("DeepSeek API call failed: %w", err)
	}

	// 解析响应
	sqlContent := strings.TrimSpace(response.Choices[0].Message.Content)

	// 提取SQL语句（去除可能的解释文本）
	sql := g.extractSQL(sqlContent)

	return &SQLGenerationResult{
		SQL:        sql,
		UsedFields: g.extractUsedFields(sql, req.TableName),
		TableName:  req.TableName,
		OperationType: req.Operation,
		Metadata: map[string]interface{}{
			"raw_response": sqlContent,
			"attempt":      attempt,
			"method":       "intelligent",
		},
	}, nil
}

// buildEnhancedSystemPrompt 构建增强的系统提示词
func (g *SQLGenerator) buildEnhancedSystemPrompt(table *TableSchema, req *IntelligentSQLGenerationRequest, attempt int, previousError error) string {
	prompt := fmt.Sprintf(`你是一个专业的SQL生成专家。请根据用户需求和数据库表结构生成精确的SQL语句。

🎯 **任务目标**: 为 %s 表生成 %s 操作的SQL语句

📋 **表结构信息** (%s表):
`, req.TableName, req.Operation, table.Name)

	// 添加详细的字段信息
	prompt += "| 字段名 | 类型 | 说明 |\n"
	prompt += "|--------|------|------|\n"

	for fieldName, fieldType := range table.Fields {
		prompt += fmt.Sprintf("| %s | %s | %s字段 |\n",
			fieldName, fieldType, fieldName)
	}

	// 如果是重试，添加错误信息
	if attempt > 1 && previousError != nil {
		prompt += fmt.Sprintf("\n⚠️ **上次尝试失败**: %s\n请仔细检查字段名，确保使用表结构中存在的字段。\n", previousError.Error())
	}

	prompt += `
🚨 **关键要求**:
1. 只返回SQL语句，不要任何解释
2. 必须使用上述表结构中的确切字段名
3. 绝对不要猜测或假设字段名
4. 对于软删除表，添加 WHERE deleted_at IS NULL 条件
5. 确保SQL语法正确

📝 **SQL格式要求**:
- SELECT: SELECT * FROM table_name WHERE conditions
- UPDATE: UPDATE table_name SET field=value WHERE conditions
- INSERT: INSERT INTO table_name (fields) VALUES (values)
- DELETE: UPDATE table_name SET deleted_at=CURRENT_TIMESTAMP WHERE conditions

请直接返回SQL语句:`

	return prompt
}

// buildUserPrompt 构建用户提示词
func (g *SQLGenerator) buildUserPrompt(req *IntelligentSQLGenerationRequest) string {
	return fmt.Sprintf("用户需求: %s\n操作类型: %s\n目标表: %s",
		req.UserInput, req.Operation, req.TableName)
}

// extractSQL 提取SQL语句
func (g *SQLGenerator) extractSQL(content string) string {
	// 移除可能的markdown代码块
	content = strings.TrimSpace(content)
	content = strings.TrimPrefix(content, "```sql")
	content = strings.TrimPrefix(content, "```")
	content = strings.TrimSuffix(content, "```")

	// 提取第一个完整的SQL语句
	lines := strings.Split(content, "\n")
	var sqlLines []string

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" || strings.HasPrefix(line, "--") || strings.HasPrefix(line, "#") {
			continue
		}
		sqlLines = append(sqlLines, line)
		// 如果遇到分号，停止收集
		if strings.HasSuffix(line, ";") {
			break
		}
	}

	sql := strings.Join(sqlLines, " ")
	sql = strings.TrimSuffix(sql, ";") // 移除末尾分号

	return strings.TrimSpace(sql)
}

// validateSQL 验证SQL语句
func (g *SQLGenerator) validateSQL(sql, tableName string) bool {
	if sql == "" {
		return false
	}

	// 检查表名是否正确
	if !strings.Contains(strings.ToLower(sql), strings.ToLower(tableName)) {
		return false
	}

	// 检查字段名是否存在
	table := g.schema.GetTable(tableName)
	if table == nil {
		return false
	}

	// 提取SQL中使用的字段名
	usedFields := g.extractUsedFields(sql, tableName)

	// 验证所有字段都存在于表结构中
	for _, field := range usedFields {
		if !table.HasField(field) {
			g.logger.WithFields(logrus.Fields{
				"sql":   sql,
				"field": field,
				"table": tableName,
			}).Warn("SQL中使用了不存在的字段")
			return false
		}
	}

	return true
}

// calculateConfidence 计算置信度
func (g *SQLGenerator) calculateConfidence(result *SQLGenerationResult, attempts int) float64 {
	baseConfidence := 1.0

	// 根据尝试次数降低置信度
	baseConfidence -= float64(attempts-1) * 0.2

	// 根据使用的字段数量调整置信度
	if len(result.UsedFields) > 0 {
		baseConfidence += 0.1
	}

	// 确保置信度在合理范围内
	if baseConfidence > 1.0 {
		baseConfidence = 1.0
	}
	if baseConfidence < 0.1 {
		baseConfidence = 0.1
	}

	return baseConfidence
}
