package agent

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// RiskAssessmentEngine 风险评估引擎
type RiskAssessmentEngine struct {
	config          *RiskAssessmentConfig
	logger          *logrus.Logger
	riskAnalyzers   map[string]RiskAnalyzer
	riskRules       *RiskRuleSet
	contextAnalyzer *ContextAnalyzer
	impactAnalyzer  *ImpactAnalyzer
	historyAnalyzer *HistoryAnalyzer
	aiRiskAnalyzer  *AIRiskAnalyzer
	mutex           sync.RWMutex
	assessmentCache map[string]*RiskAssessment
	cacheExpiry     time.Duration
}

// RiskAssessmentConfig 风险评估配置
type RiskAssessmentConfig struct {
	EnableAIAnalysis      bool            `json:"enable_ai_analysis"`
	EnableContextAnalysis bool            `json:"enable_context_analysis"`
	EnableImpactAnalysis  bool            `json:"enable_impact_analysis"`
	EnableHistoryAnalysis bool            `json:"enable_history_analysis"`
	CacheExpiry           time.Duration   `json:"cache_expiry"`
	MaxCacheSize          int             `json:"max_cache_size"`
	RiskThresholds        *RiskThresholds `json:"risk_thresholds"`
	WeightConfig          *WeightConfig   `json:"weight_config"`
}

// RiskThresholds 风险阈值配置
type RiskThresholds struct {
	LowThreshold      float64 `json:"low_threshold"`      // 0.0-0.3
	MediumThreshold   float64 `json:"medium_threshold"`   // 0.3-0.7
	HighThreshold     float64 `json:"high_threshold"`     // 0.7-0.9
	CriticalThreshold float64 `json:"critical_threshold"` // 0.9-1.0
}

// WeightConfig 权重配置
type WeightConfig struct {
	CommandWeight    float64 `json:"command_weight"`     // 命令本身的权重
	ArgumentWeight   float64 `json:"argument_weight"`    // 参数的权重
	ContextWeight    float64 `json:"context_weight"`     // 上下文的权重
	ImpactWeight     float64 `json:"impact_weight"`      // 影响范围的权重
	HistoryWeight    float64 `json:"history_weight"`     // 历史记录的权重
	AIAnalysisWeight float64 `json:"ai_analysis_weight"` // AI分析的权重
}

// RiskAssessment 风险评估结果
type RiskAssessment struct {
	ID              string                 `json:"id"`
	Command         string                 `json:"command"`
	Args            []string               `json:"args"`
	FullCommand     string                 `json:"full_command"`
	RiskLevel       string                 `json:"risk_level"` // low, medium, high, critical
	RiskScore       float64                `json:"risk_score"` // 0.0-1.0
	Confidence      float64                `json:"confidence"` // 0.0-1.0
	Reasons         []string               `json:"reasons"`
	Recommendations []string               `json:"recommendations"`
	ImpactAnalysis  *ImpactAnalysis        `json:"impact_analysis"`
	ContextAnalysis *ContextAnalysis       `json:"context_analysis"`
	HistoryAnalysis *HistoryAnalysis       `json:"history_analysis"`
	AIAnalysis      *AIAnalysis            `json:"ai_analysis"`
	Metadata        map[string]interface{} `json:"metadata"`
	AssessedAt      time.Time              `json:"assessed_at"`
	AssessedBy      string                 `json:"assessed_by"`
	UserID          int64                  `json:"user_id"`
	SessionID       string                 `json:"session_id"`
}

// RiskAnalyzer 风险分析器接口
type RiskAnalyzer interface {
	AnalyzeRisk(ctx context.Context, command string, args []string, context *AssessmentContext) (*AnalysisResult, error)
	GetAnalyzerType() string
	GetWeight() float64
}

// AssessmentContext 评估上下文
type AssessmentContext struct {
	UserID           int64                  `json:"user_id"`
	SessionID        string                 `json:"session_id"`
	WorkingDir       string                 `json:"working_dir"`
	Environment      map[string]string      `json:"environment"`
	PreviousCommands []string               `json:"previous_commands"`
	SystemState      *SystemState           `json:"system_state"`
	UserPermissions  *UserPermissions       `json:"user_permissions"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	AnalyzerType    string                 `json:"analyzer_type"`
	RiskScore       float64                `json:"risk_score"`
	Confidence      float64                `json:"confidence"`
	Reasons         []string               `json:"reasons"`
	Recommendations []string               `json:"recommendations"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// ImpactAnalysis 影响分析
type ImpactAnalysis struct {
	Scope           string   `json:"scope"` // local, system, network, global
	AffectedSystems []string `json:"affected_systems"`
	DataRisk        string   `json:"data_risk"`      // none, read, modify, delete
	ServiceImpact   string   `json:"service_impact"` // none, degraded, outage
	RecoveryTime    string   `json:"recovery_time"`  // immediate, minutes, hours, days
	Reversible      bool     `json:"reversible"`
	BackupRequired  bool     `json:"backup_required"`
}

// ContextAnalysis 上下文分析
type ContextAnalysis struct {
	WorkingDirectory string                 `json:"working_directory"`
	UserContext      string                 `json:"user_context"` // normal, privileged, root
	TimeContext      string                 `json:"time_context"` // business_hours, off_hours, maintenance
	SystemLoad       string                 `json:"system_load"`  // low, medium, high
	RecentActivity   []string               `json:"recent_activity"`
	EnvironmentRisk  string                 `json:"environment_risk"` // development, staging, production
	Dependencies     []string               `json:"dependencies"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// HistoryAnalysis 历史分析
type HistoryAnalysis struct {
	CommandFrequency    int        `json:"command_frequency"`
	LastExecuted        *time.Time `json:"last_executed"`
	SuccessRate         float64    `json:"success_rate"`
	FailurePatterns     []string   `json:"failure_patterns"`
	UserExperience      string     `json:"user_experience"` // novice, intermediate, expert
	SimilarCommands     []string   `json:"similar_commands"`
	HistoricalIncidents []string   `json:"historical_incidents"`
}

// AIAnalysis AI分析结果
type AIAnalysis struct {
	IntentAnalysis         string                 `json:"intent_analysis"`
	RiskPrediction         float64                `json:"risk_prediction"`
	AlternativeSuggestions []string               `json:"alternative_suggestions"`
	SafetyRecommendations  []string               `json:"safety_recommendations"`
	ConfidenceLevel        float64                `json:"confidence_level"`
	ReasoningChain         []string               `json:"reasoning_chain"`
	Metadata               map[string]interface{} `json:"metadata"`
}

// RiskRuleSet 风险规则集
type RiskRuleSet struct {
	CommandRules     []*CommandRiskRule     `json:"command_rules"`
	PatternRules     []*PatternRiskRule     `json:"pattern_rules"`
	ContextRules     []*ContextRiskRule     `json:"context_rules"`
	CombinationRules []*CombinationRiskRule `json:"combination_rules"`
	LastUpdated      time.Time              `json:"last_updated"`
}

// CommandRiskRule 命令风险规则
type CommandRiskRule struct {
	Command     string           `json:"command"`
	BaseRisk    float64          `json:"base_risk"`
	Arguments   []*ArgumentRisk  `json:"arguments"`
	Conditions  []*RiskCondition `json:"conditions"`
	Description string           `json:"description"`
	Category    string           `json:"category"`
	Severity    string           `json:"severity"`
	Enabled     bool             `json:"enabled"`
}

// PatternRiskRule 模式风险规则
type PatternRiskRule struct {
	Pattern     string           `json:"pattern"`
	Regex       *regexp.Regexp   `json:"-"`
	RiskScore   float64          `json:"risk_score"`
	Conditions  []*RiskCondition `json:"conditions"`
	Description string           `json:"description"`
	Category    string           `json:"category"`
	Enabled     bool             `json:"enabled"`
}

// ContextRiskRule 上下文风险规则
type ContextRiskRule struct {
	Name         string           `json:"name"`
	Conditions   []*RiskCondition `json:"conditions"`
	RiskModifier float64          `json:"risk_modifier"` // 风险修正系数
	Description  string           `json:"description"`
	Enabled      bool             `json:"enabled"`
}

// CombinationRiskRule 组合风险规则
type CombinationRiskRule struct {
	Name        string        `json:"name"`
	Commands    []string      `json:"commands"`
	Sequence    bool          `json:"sequence"`    // 是否需要按顺序执行
	TimeWindow  time.Duration `json:"time_window"` // 时间窗口
	RiskScore   float64       `json:"risk_score"`
	Description string        `json:"description"`
	Enabled     bool          `json:"enabled"`
}

// ArgumentRisk 参数风险
type ArgumentRisk struct {
	Pattern     string  `json:"pattern"`
	RiskScore   float64 `json:"risk_score"`
	Description string  `json:"description"`
}

// RiskCondition 风险条件
type RiskCondition struct {
	Type     string      `json:"type"` // user, time, system, environment
	Field    string      `json:"field"`
	Operator string      `json:"operator"` // eq, ne, gt, lt, contains, regex
	Value    interface{} `json:"value"`
	Weight   float64     `json:"weight"`
}

// SystemState 系统状态
type SystemState struct {
	CPUUsage           float64           `json:"cpu_usage"`
	MemoryUsage        float64           `json:"memory_usage"`
	DiskUsage          float64           `json:"disk_usage"`
	LoadAverage        float64           `json:"load_average"`
	Uptime             time.Duration     `json:"uptime"`
	Services           map[string]string `json:"services"`
	Processes          []string          `json:"processes"`
	NetworkConnections int               `json:"network_connections"`
}

// UserPermissions 用户权限
type UserPermissions struct {
	UserID      int64    `json:"user_id"`
	Username    string   `json:"username"`
	Groups      []string `json:"groups"`
	Roles       []string `json:"roles"`
	Permissions []string `json:"permissions"`
	IsAdmin     bool     `json:"is_admin"`
	IsSudo      bool     `json:"is_sudo"`
}

// NewRiskAssessmentEngine 创建风险评估引擎
func NewRiskAssessmentEngine(config *RiskAssessmentConfig, logger *logrus.Logger) *RiskAssessmentEngine {
	if config == nil {
		config = DefaultRiskAssessmentConfig()
	}

	engine := &RiskAssessmentEngine{
		config:          config,
		logger:          logger,
		riskAnalyzers:   make(map[string]RiskAnalyzer),
		riskRules:       NewDefaultRiskRuleSet(),
		contextAnalyzer: NewContextAnalyzer(logger),
		impactAnalyzer:  NewImpactAnalyzer(logger),
		historyAnalyzer: NewHistoryAnalyzer(logger),
		assessmentCache: make(map[string]*RiskAssessment),
		cacheExpiry:     config.CacheExpiry,
	}

	// 初始化AI风险分析器
	if config.EnableAIAnalysis {
		engine.aiRiskAnalyzer = NewAIRiskAnalyzer(logger)
	}

	// 注册默认分析器
	engine.registerDefaultAnalyzers()

	return engine
}

// DefaultRiskAssessmentConfig 默认风险评估配置
func DefaultRiskAssessmentConfig() *RiskAssessmentConfig {
	return &RiskAssessmentConfig{
		EnableAIAnalysis:      true,
		EnableContextAnalysis: true,
		EnableImpactAnalysis:  true,
		EnableHistoryAnalysis: true,
		CacheExpiry:           5 * time.Minute,
		MaxCacheSize:          1000,
		RiskThresholds: &RiskThresholds{
			LowThreshold:      0.3,
			MediumThreshold:   0.7,
			HighThreshold:     0.9,
			CriticalThreshold: 1.0,
		},
		WeightConfig: &WeightConfig{
			CommandWeight:    0.3,
			ArgumentWeight:   0.2,
			ContextWeight:    0.15,
			ImpactWeight:     0.15,
			HistoryWeight:    0.1,
			AIAnalysisWeight: 0.1,
		},
	}
}

// NewDefaultRiskRuleSet 创建默认风险规则集
func NewDefaultRiskRuleSet() *RiskRuleSet {
	return &RiskRuleSet{
		CommandRules:     getDefaultCommandRules(),
		PatternRules:     getDefaultPatternRules(),
		ContextRules:     getDefaultContextRules(),
		CombinationRules: getDefaultCombinationRules(),
		LastUpdated:      time.Now(),
	}
}

// AssessRisk 评估命令风险
func (rae *RiskAssessmentEngine) AssessRisk(ctx context.Context, command string, args []string, assessmentContext *AssessmentContext) (*RiskAssessment, error) {
	// 生成缓存键
	cacheKey := rae.generateCacheKey(command, args, assessmentContext)

	// 检查缓存
	if cached := rae.getCachedAssessment(cacheKey); cached != nil {
		return cached, nil
	}

	// 创建风险评估结果
	assessment := &RiskAssessment{
		ID:              fmt.Sprintf("risk_%d", time.Now().UnixNano()),
		Command:         command,
		Args:            args,
		FullCommand:     command + " " + strings.Join(args, " "),
		Reasons:         make([]string, 0),
		Recommendations: make([]string, 0),
		Metadata:        make(map[string]interface{}),
		AssessedAt:      time.Now(),
		AssessedBy:      "risk_assessment_engine",
	}

	if assessmentContext != nil {
		assessment.UserID = assessmentContext.UserID
		assessment.SessionID = assessmentContext.SessionID
	}

	// 执行多维度风险分析
	totalScore := 0.0
	totalWeight := 0.0
	confidence := 0.0

	// 1. 命令基础风险分析
	commandScore, commandConfidence := rae.analyzeCommandRisk(command, args)
	totalScore += commandScore * rae.config.WeightConfig.CommandWeight
	totalWeight += rae.config.WeightConfig.CommandWeight
	confidence += commandConfidence * rae.config.WeightConfig.CommandWeight

	// 2. 参数风险分析
	argScore, argConfidence := rae.analyzeArgumentRisk(command, args)
	totalScore += argScore * rae.config.WeightConfig.ArgumentWeight
	totalWeight += rae.config.WeightConfig.ArgumentWeight
	confidence += argConfidence * rae.config.WeightConfig.ArgumentWeight

	// 3. 上下文分析
	if rae.config.EnableContextAnalysis && assessmentContext != nil {
		contextAnalysis, contextScore, contextConfidence := rae.analyzeContext(ctx, assessmentContext)
		assessment.ContextAnalysis = contextAnalysis
		totalScore += contextScore * rae.config.WeightConfig.ContextWeight
		totalWeight += rae.config.WeightConfig.ContextWeight
		confidence += contextConfidence * rae.config.WeightConfig.ContextWeight
	}

	// 4. 影响分析
	if rae.config.EnableImpactAnalysis {
		impactAnalysis, impactScore, impactConfidence := rae.analyzeImpact(ctx, command, args, assessmentContext)
		assessment.ImpactAnalysis = impactAnalysis
		totalScore += impactScore * rae.config.WeightConfig.ImpactWeight
		totalWeight += rae.config.WeightConfig.ImpactWeight
		confidence += impactConfidence * rae.config.WeightConfig.ImpactWeight
	}

	// 5. 历史分析
	if rae.config.EnableHistoryAnalysis && assessmentContext != nil {
		historyAnalysis, historyScore, historyConfidence := rae.analyzeHistory(ctx, command, assessmentContext)
		assessment.HistoryAnalysis = historyAnalysis
		totalScore += historyScore * rae.config.WeightConfig.HistoryWeight
		totalWeight += rae.config.WeightConfig.HistoryWeight
		confidence += historyConfidence * rae.config.WeightConfig.HistoryWeight
	}

	// 6. AI分析
	if rae.config.EnableAIAnalysis && rae.aiRiskAnalyzer != nil {
		aiAnalysis, aiScore, aiConfidence := rae.analyzeWithAI(ctx, command, args, assessmentContext)
		assessment.AIAnalysis = aiAnalysis
		totalScore += aiScore * rae.config.WeightConfig.AIAnalysisWeight
		totalWeight += rae.config.WeightConfig.AIAnalysisWeight
		confidence += aiConfidence * rae.config.WeightConfig.AIAnalysisWeight
	}

	// 计算最终风险分数
	if totalWeight > 0 {
		assessment.RiskScore = totalScore / totalWeight
		assessment.Confidence = confidence / totalWeight
	}

	// 确定风险等级
	assessment.RiskLevel = rae.determineRiskLevel(assessment.RiskScore)

	// 生成建议
	rae.generateRecommendations(assessment)

	// 缓存结果
	rae.cacheAssessment(cacheKey, assessment)

	rae.logger.WithFields(logrus.Fields{
		"command":    command,
		"risk_level": assessment.RiskLevel,
		"risk_score": assessment.RiskScore,
		"confidence": assessment.Confidence,
	}).Info("Risk assessment completed")

	return assessment, nil
}

// analyzeCommandRisk 分析命令基础风险
func (rae *RiskAssessmentEngine) analyzeCommandRisk(command string, args []string) (float64, float64) {
	// 检查命令规则
	for _, rule := range rae.riskRules.CommandRules {
		if rule.Enabled && rule.Command == command {
			score := rule.BaseRisk
			confidence := 0.9

			// 检查参数风险
			for _, arg := range args {
				for _, argRisk := range rule.Arguments {
					if matched, _ := regexp.MatchString(argRisk.Pattern, arg); matched {
						score = rae.combineRiskScores(score, argRisk.RiskScore)
					}
				}
			}

			return score, confidence
		}
	}

	// 检查模式规则
	fullCommand := command + " " + strings.Join(args, " ")
	for _, rule := range rae.riskRules.PatternRules {
		if rule.Enabled && rule.Regex != nil && rule.Regex.MatchString(fullCommand) {
			return rule.RiskScore, 0.8
		}
	}

	// 默认风险评估
	return rae.getDefaultCommandRisk(command), 0.5
}

// analyzeArgumentRisk 分析参数风险
func (rae *RiskAssessmentEngine) analyzeArgumentRisk(command string, args []string) (float64, float64) {
	maxRisk := 0.0
	confidence := 0.7

	for _, arg := range args {
		risk := rae.evaluateArgumentRisk(arg)
		if risk > maxRisk {
			maxRisk = risk
		}
	}

	return maxRisk, confidence
}

// analyzeContext 分析上下文
func (rae *RiskAssessmentEngine) analyzeContext(ctx context.Context, assessmentContext *AssessmentContext) (*ContextAnalysis, float64, float64) {
	if rae.contextAnalyzer == nil {
		return nil, 0.0, 0.0
	}

	analysis := rae.contextAnalyzer.Analyze(ctx, assessmentContext)
	score := rae.calculateContextRiskScore(analysis)
	confidence := 0.8

	return analysis, score, confidence
}

// analyzeImpact 分析影响
func (rae *RiskAssessmentEngine) analyzeImpact(ctx context.Context, command string, args []string, assessmentContext *AssessmentContext) (*ImpactAnalysis, float64, float64) {
	if rae.impactAnalyzer == nil {
		return nil, 0.0, 0.0
	}

	analysis := rae.impactAnalyzer.Analyze(ctx, command, args, assessmentContext)
	score := rae.calculateImpactRiskScore(analysis)
	confidence := 0.7

	return analysis, score, confidence
}

// analyzeHistory 分析历史
func (rae *RiskAssessmentEngine) analyzeHistory(ctx context.Context, command string, assessmentContext *AssessmentContext) (*HistoryAnalysis, float64, float64) {
	if rae.historyAnalyzer == nil {
		return nil, 0.0, 0.0
	}

	analysis := rae.historyAnalyzer.Analyze(ctx, command, assessmentContext)
	score := rae.calculateHistoryRiskScore(analysis)
	confidence := 0.6

	return analysis, score, confidence
}

// analyzeWithAI 使用AI分析
func (rae *RiskAssessmentEngine) analyzeWithAI(ctx context.Context, command string, args []string, assessmentContext *AssessmentContext) (*AIAnalysis, float64, float64) {
	if rae.aiRiskAnalyzer == nil {
		return nil, 0.0, 0.0
	}

	analysis := rae.aiRiskAnalyzer.Analyze(ctx, command, args, assessmentContext)
	score := analysis.RiskPrediction
	confidence := analysis.ConfidenceLevel

	return analysis, score, confidence
}

// determineRiskLevel 确定风险等级
func (rae *RiskAssessmentEngine) determineRiskLevel(riskScore float64) string {
	thresholds := rae.config.RiskThresholds

	if riskScore >= thresholds.CriticalThreshold {
		return "critical"
	} else if riskScore >= thresholds.HighThreshold {
		return "high"
	} else if riskScore >= thresholds.MediumThreshold {
		return "medium"
	} else {
		return "low"
	}
}

// generateRecommendations 生成建议
func (rae *RiskAssessmentEngine) generateRecommendations(assessment *RiskAssessment) {
	recommendations := make([]string, 0)

	switch assessment.RiskLevel {
	case "critical":
		recommendations = append(recommendations, "强烈建议不要执行此命令")
		recommendations = append(recommendations, "如必须执行，请先备份重要数据")
		recommendations = append(recommendations, "建议在测试环境中先行验证")
		recommendations = append(recommendations, "需要管理员审批")
	case "high":
		recommendations = append(recommendations, "建议谨慎执行此命令")
		recommendations = append(recommendations, "执行前请确认操作意图")
		recommendations = append(recommendations, "建议创建系统快照")
	case "medium":
		recommendations = append(recommendations, "请确认命令参数正确")
		recommendations = append(recommendations, "建议在非生产环境测试")
	case "low":
		recommendations = append(recommendations, "命令风险较低，可以执行")
	}

	// 添加AI分析建议
	if assessment.AIAnalysis != nil {
		recommendations = append(recommendations, assessment.AIAnalysis.SafetyRecommendations...)
	}

	assessment.Recommendations = recommendations
}

// 辅助方法

// generateCacheKey 生成缓存键
func (rae *RiskAssessmentEngine) generateCacheKey(command string, args []string, context *AssessmentContext) string {
	key := fmt.Sprintf("%s_%s", command, strings.Join(args, "_"))
	if context != nil {
		key += fmt.Sprintf("_%d_%s", context.UserID, context.SessionID)
	}
	return key
}

// getCachedAssessment 获取缓存的评估结果
func (rae *RiskAssessmentEngine) getCachedAssessment(key string) *RiskAssessment {
	rae.mutex.RLock()
	defer rae.mutex.RUnlock()

	if assessment, exists := rae.assessmentCache[key]; exists {
		// 检查是否过期
		if time.Since(assessment.AssessedAt) < rae.cacheExpiry {
			return assessment
		}
		// 删除过期缓存
		delete(rae.assessmentCache, key)
	}

	return nil
}

// cacheAssessment 缓存评估结果
func (rae *RiskAssessmentEngine) cacheAssessment(key string, assessment *RiskAssessment) {
	rae.mutex.Lock()
	defer rae.mutex.Unlock()

	// 检查缓存大小限制
	if len(rae.assessmentCache) >= rae.config.MaxCacheSize {
		// 清理最旧的缓存项
		rae.cleanupOldCache()
	}

	rae.assessmentCache[key] = assessment
}

// cleanupOldCache 清理旧缓存
func (rae *RiskAssessmentEngine) cleanupOldCache() {
	// 简化实现：清理一半缓存
	count := 0
	target := len(rae.assessmentCache) / 2

	for key := range rae.assessmentCache {
		if count >= target {
			break
		}
		delete(rae.assessmentCache, key)
		count++
	}
}

// combineRiskScores 组合风险分数
func (rae *RiskAssessmentEngine) combineRiskScores(score1, score2 float64) float64 {
	// 使用最大值组合策略
	if score2 > score1 {
		return score2
	}
	return score1
}

// getDefaultCommandRisk 获取默认命令风险
func (rae *RiskAssessmentEngine) getDefaultCommandRisk(command string) float64 {
	// 基于命令名称的启发式风险评估
	dangerousCommands := map[string]float64{
		"rm":       0.8,
		"rmdir":    0.7,
		"dd":       0.9,
		"mkfs":     0.95,
		"fdisk":    0.9,
		"format":   0.9,
		"del":      0.6,
		"deltree":  0.8,
		"kill":     0.5,
		"killall":  0.6,
		"shutdown": 0.7,
		"reboot":   0.6,
		"halt":     0.7,
		"chmod":    0.4,
		"chown":    0.4,
		"su":       0.5,
		"sudo":     0.6,
	}

	if risk, exists := dangerousCommands[command]; exists {
		return risk
	}

	return 0.2 // 默认低风险
}

// evaluateArgumentRisk 评估参数风险
func (rae *RiskAssessmentEngine) evaluateArgumentRisk(arg string) float64 {
	// 危险参数模式
	dangerousPatterns := []struct {
		pattern string
		risk    float64
	}{
		{`^-rf$|^-fr$`, 0.9},           // rm -rf
		{`^--force$|^-f$`, 0.6},        // 强制选项
		{`^--recursive$|^-r$`, 0.5},    // 递归选项
		{`^/\*$`, 0.95},                // 根目录通配符
		{`^\*$`, 0.7},                  // 通配符
		{`^--no-preserve-root$`, 0.95}, // 不保护根目录
		{`^--delete$`, 0.7},            // 删除选项
		{`^--remove$`, 0.7},            // 移除选项
	}

	for _, pattern := range dangerousPatterns {
		if matched, _ := regexp.MatchString(pattern.pattern, arg); matched {
			return pattern.risk
		}
	}

	return 0.1 // 默认低风险
}

// calculateContextRiskScore 计算上下文风险分数
func (rae *RiskAssessmentEngine) calculateContextRiskScore(analysis *ContextAnalysis) float64 {
	if analysis == nil {
		return 0.0
	}

	score := 0.0

	// 用户上下文风险
	switch analysis.UserContext {
	case "root":
		score += 0.8
	case "privileged":
		score += 0.5
	case "normal":
		score += 0.1
	}

	// 环境风险
	switch analysis.EnvironmentRisk {
	case "production":
		score += 0.7
	case "staging":
		score += 0.3
	case "development":
		score += 0.1
	}

	// 系统负载风险
	switch analysis.SystemLoad {
	case "high":
		score += 0.3
	case "medium":
		score += 0.1
	}

	// 时间上下文风险
	switch analysis.TimeContext {
	case "off_hours":
		score += 0.2
	case "maintenance":
		score += 0.1
	}

	return score
}

// calculateImpactRiskScore 计算影响风险分数
func (rae *RiskAssessmentEngine) calculateImpactRiskScore(analysis *ImpactAnalysis) float64 {
	if analysis == nil {
		return 0.0
	}

	score := 0.0

	// 影响范围
	switch analysis.Scope {
	case "global":
		score += 0.9
	case "network":
		score += 0.7
	case "system":
		score += 0.5
	case "local":
		score += 0.2
	}

	// 数据风险
	switch analysis.DataRisk {
	case "delete":
		score += 0.8
	case "modify":
		score += 0.5
	case "read":
		score += 0.2
	}

	// 服务影响
	switch analysis.ServiceImpact {
	case "outage":
		score += 0.9
	case "degraded":
		score += 0.5
	}

	// 恢复时间
	switch analysis.RecoveryTime {
	case "days":
		score += 0.8
	case "hours":
		score += 0.5
	case "minutes":
		score += 0.2
	}

	// 可逆性
	if !analysis.Reversible {
		score += 0.3
	}

	return score
}

// calculateHistoryRiskScore 计算历史风险分数
func (rae *RiskAssessmentEngine) calculateHistoryRiskScore(analysis *HistoryAnalysis) float64 {
	if analysis == nil {
		return 0.0
	}

	score := 0.0

	// 成功率风险
	if analysis.SuccessRate < 0.5 {
		score += 0.6
	} else if analysis.SuccessRate < 0.8 {
		score += 0.3
	}

	// 用户经验
	switch analysis.UserExperience {
	case "novice":
		score += 0.4
	case "intermediate":
		score += 0.2
	case "expert":
		score += 0.0
	}

	// 历史事件
	if len(analysis.HistoricalIncidents) > 0 {
		score += 0.5
	}

	return score
}

// registerDefaultAnalyzers 注册默认分析器
func (rae *RiskAssessmentEngine) registerDefaultAnalyzers() {
	// 这里可以注册自定义的风险分析器
	rae.logger.Info("Default risk analyzers registered")
}

// getDefaultCommandRules 获取默认命令规则
func getDefaultCommandRules() []*CommandRiskRule {
	return []*CommandRiskRule{
		{
			Command:  "rm",
			BaseRisk: 0.7,
			Arguments: []*ArgumentRisk{
				{Pattern: "^-rf$", RiskScore: 0.9, Description: "强制递归删除"},
				{Pattern: "^-f$", RiskScore: 0.6, Description: "强制删除"},
				{Pattern: "^-r$", RiskScore: 0.5, Description: "递归删除"},
			},
			Description: "文件删除命令",
			Category:    "file_operation",
			Severity:    "high",
			Enabled:     true,
		},
		{
			Command:  "dd",
			BaseRisk: 0.8,
			Arguments: []*ArgumentRisk{
				{Pattern: "of=/dev/", RiskScore: 0.95, Description: "写入设备"},
				{Pattern: "of=/", RiskScore: 0.9, Description: "写入根目录"},
			},
			Description: "磁盘数据复制",
			Category:    "disk_operation",
			Severity:    "critical",
			Enabled:     true,
		},
		{
			Command:     "mkfs",
			BaseRisk:    0.9,
			Description: "创建文件系统",
			Category:    "disk_operation",
			Severity:    "critical",
			Enabled:     true,
		},
		{
			Command:  "fdisk",
			BaseRisk: 0.8,
			Arguments: []*ArgumentRisk{
				{Pattern: "d", RiskScore: 0.9, Description: "删除分区"},
			},
			Description: "磁盘分区操作",
			Category:    "disk_operation",
			Severity:    "high",
			Enabled:     true,
		},
		{
			Command:  "chmod",
			BaseRisk: 0.4,
			Arguments: []*ArgumentRisk{
				{Pattern: "777", RiskScore: 0.8, Description: "最大权限"},
				{Pattern: "666", RiskScore: 0.6, Description: "读写权限"},
			},
			Description: "修改文件权限",
			Category:    "permission",
			Severity:    "medium",
			Enabled:     true,
		},
		{
			Command:  "chown",
			BaseRisk: 0.4,
			Arguments: []*ArgumentRisk{
				{Pattern: "root", RiskScore: 0.6, Description: "设置为root所有者"},
			},
			Description: "修改文件所有者",
			Category:    "permission",
			Severity:    "medium",
			Enabled:     true,
		},
		{
			Command:  "systemctl",
			BaseRisk: 0.5,
			Arguments: []*ArgumentRisk{
				{Pattern: "stop", RiskScore: 0.7, Description: "停止服务"},
				{Pattern: "disable", RiskScore: 0.6, Description: "禁用服务"},
			},
			Description: "系统服务控制",
			Category:    "service",
			Severity:    "medium",
			Enabled:     true,
		},
		{
			Command:  "kill",
			BaseRisk: 0.5,
			Arguments: []*ArgumentRisk{
				{Pattern: "-9", RiskScore: 0.8, Description: "强制杀死进程"},
				{Pattern: "1$", RiskScore: 0.9, Description: "杀死init进程"},
			},
			Description: "终止进程",
			Category:    "process",
			Severity:    "medium",
			Enabled:     true,
		},
		{
			Command:  "shutdown",
			BaseRisk: 0.7,
			Arguments: []*ArgumentRisk{
				{Pattern: "now", RiskScore: 0.8, Description: "立即关机"},
			},
			Description: "系统关机",
			Category:    "system",
			Severity:    "high",
			Enabled:     true,
		},
		{
			Command:  "reboot",
			BaseRisk: 0.6,
			Arguments: []*ArgumentRisk{
				{Pattern: "now", RiskScore: 0.7, Description: "立即重启"},
			},
			Description: "系统重启",
			Category:    "system",
			Severity:    "medium",
			Enabled:     true,
		},
	}
}

// getDefaultPatternRules 获取默认模式规则
func getDefaultPatternRules() []*PatternRiskRule {
	rules := []*PatternRiskRule{
		{
			Pattern:     `rm\s+.*-rf.*/$`,
			RiskScore:   0.95,
			Description: "删除根目录",
			Category:    "critical_operation",
			Enabled:     true,
		},
		{
			Pattern:     `rm\s+.*-rf.*\*`,
			RiskScore:   0.9,
			Description: "通配符删除",
			Category:    "dangerous_operation",
			Enabled:     true,
		},
		{
			Pattern:     `dd\s+.*of=/dev/`,
			RiskScore:   0.95,
			Description: "写入设备文件",
			Category:    "critical_operation",
			Enabled:     true,
		},
		{
			Pattern:     `chmod\s+777.*/$`,
			RiskScore:   0.8,
			Description: "根目录最大权限",
			Category:    "security_risk",
			Enabled:     true,
		},
		{
			Pattern:     `>\s*/dev/`,
			RiskScore:   0.8,
			Description: "重定向到设备文件",
			Category:    "dangerous_operation",
			Enabled:     true,
		},
	}

	// 编译正则表达式
	for _, rule := range rules {
		if regex, err := regexp.Compile(rule.Pattern); err == nil {
			rule.Regex = regex
		}
	}

	return rules
}

// getDefaultContextRules 获取默认上下文规则
func getDefaultContextRules() []*ContextRiskRule {
	return []*ContextRiskRule{
		{
			Name: "production_environment",
			Conditions: []*RiskCondition{
				{
					Type:     "environment",
					Field:    "ENVIRONMENT",
					Operator: "eq",
					Value:    "production",
					Weight:   0.3,
				},
			},
			RiskModifier: 0.3,
			Description:  "生产环境风险加权",
			Enabled:      true,
		},
		{
			Name: "root_user",
			Conditions: []*RiskCondition{
				{
					Type:     "user",
					Field:    "is_admin",
					Operator: "eq",
					Value:    true,
					Weight:   0.2,
				},
			},
			RiskModifier: 0.2,
			Description:  "管理员用户风险加权",
			Enabled:      true,
		},
		{
			Name: "high_system_load",
			Conditions: []*RiskCondition{
				{
					Type:     "system",
					Field:    "cpu_usage",
					Operator: "gt",
					Value:    80.0,
					Weight:   0.1,
				},
			},
			RiskModifier: 0.1,
			Description:  "高系统负载风险加权",
			Enabled:      true,
		},
		{
			Name: "off_hours",
			Conditions: []*RiskCondition{
				{
					Type:     "time",
					Field:    "hour",
					Operator: "lt",
					Value:    9,
					Weight:   0.1,
				},
			},
			RiskModifier: 0.1,
			Description:  "非工作时间风险加权",
			Enabled:      true,
		},
	}
}

// getDefaultCombinationRules 获取默认组合规则
func getDefaultCombinationRules() []*CombinationRiskRule {
	return []*CombinationRiskRule{
		{
			Name:        "dangerous_sequence",
			Commands:    []string{"systemctl stop", "rm -rf"},
			Sequence:    true,
			TimeWindow:  5 * time.Minute,
			RiskScore:   0.9,
			Description: "危险命令序列：停止服务后删除文件",
			Enabled:     true,
		},
		{
			Name:        "privilege_escalation",
			Commands:    []string{"su", "sudo", "chmod 777"},
			Sequence:    false,
			TimeWindow:  10 * time.Minute,
			RiskScore:   0.8,
			Description: "权限提升后的危险操作",
			Enabled:     true,
		},
		{
			Name:        "system_modification",
			Commands:    []string{"mount", "umount", "mkfs"},
			Sequence:    false,
			TimeWindow:  15 * time.Minute,
			RiskScore:   0.85,
			Description: "系统级文件系统修改",
			Enabled:     true,
		},
	}
}
