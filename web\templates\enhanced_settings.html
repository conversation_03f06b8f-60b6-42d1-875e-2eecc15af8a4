<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强设置管理 - AI运维管理平台</title>
    <link rel="stylesheet" href="/static/css/design-system.css">
    <style>
        .settings-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-primary);
            min-height: 100vh;
        }

        .settings-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 25px;
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }

        .settings-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .settings-actions {
            display: flex;
            gap: 12px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: var(--bg-hover);
        }

        .settings-layout {
            display: grid;
            grid-template-columns: 280px 1fr;
            gap: 30px;
        }

        .settings-sidebar {
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 20px;
            height: fit-content;
            position: sticky;
            top: 20px;
        }

        .settings-main {
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 30px;
            min-height: 600px;
        }

        .sidebar-nav {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .nav-item {
            margin-bottom: 8px;
        }

        .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 16px;
            color: var(--text-secondary);
            text-decoration: none;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-weight: 500;
        }

        .nav-link:hover {
            background: var(--bg-hover);
            color: var(--text-primary);
            transform: translateX(4px);
        }

        .nav-link.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .nav-icon {
            margin-right: 12px;
            font-size: 18px;
        }

        .settings-section {
            display: none;
        }

        .settings-section.active {
            display: block;
        }

        .section-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 20px;
            padding-bottom: 12px;
            border-bottom: 2px solid var(--border-color);
        }

        .settings-group {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .group-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
        }

        .group-description {
            color: var(--text-secondary);
            margin-bottom: 20px;
            font-size: 14px;
        }

        .setting-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 0;
            border-bottom: 1px solid var(--border-color);
        }

        .setting-item:last-child {
            border-bottom: none;
        }

        .setting-info {
            flex: 1;
        }

        .setting-label {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .setting-description {
            font-size: 13px;
            color: var(--text-secondary);
        }

        .setting-control {
            margin-left: 20px;
        }

        .toggle-switch {
            position: relative;
            width: 50px;
            height: 24px;
            background: var(--border-color);
            border-radius: 12px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .toggle-switch.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .toggle-slider {
            position: absolute;
            top: 2px;
            left: 2px;
            width: 20px;
            height: 20px;
            background: white;
            border-radius: 50%;
            transition: transform 0.3s ease;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .toggle-switch.active .toggle-slider {
            transform: translateX(26px);
        }

        .select-input {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 14px;
            min-width: 120px;
        }

        .number-input {
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 14px;
            width: 80px;
            text-align: center;
        }

        .theme-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .theme-card {
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 2px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .theme-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .theme-card.active {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .theme-preview {
            width: 100%;
            height: 80px;
            border-radius: 6px;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }

        .theme-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
        }

        .theme-description {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }

        .stat-card {
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .stat-value {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .stat-label {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .notification.error {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }

        .notification.info {
            background: linear-gradient(135deg, #17a2b8, #007bff);
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 30px;
            width: 500px;
            max-width: 90vw;
            border: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .form-textarea {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
            min-height: 120px;
            resize: vertical;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .import-export-section {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }

        .import-export-card {
            flex: 1;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            text-align: center;
        }

        .card-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: var(--text-secondary);
        }

        .card-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 10px;
        }

        .card-description {
            font-size: 14px;
            color: var(--text-secondary);
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="settings-container">
        <!-- 设置管理头部 -->
        <div class="settings-header">
            <h1 class="settings-title">⚙️ 增强设置管理</h1>
            <div class="settings-actions">
                <button class="btn-secondary" onclick="exportSettings()">📤 导出设置</button>
                <button class="btn-secondary" onclick="showImportModal()">📥 导入设置</button>
                <button class="btn-primary" onclick="saveAllSettings()">💾 保存设置</button>
            </div>
        </div>

        <!-- 设置管理主体 -->
        <div class="settings-layout">
            <!-- 侧边栏导航 -->
            <div class="settings-sidebar">
                <ul class="sidebar-nav">
                    <li class="nav-item">
                        <a href="#general" class="nav-link active" onclick="switchSection('general', this)">
                            <span class="nav-icon">🏠</span>
                            常规设置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#appearance" class="nav-link" onclick="switchSection('appearance', this)">
                            <span class="nav-icon">🎨</span>
                            外观主题
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#ai" class="nav-link" onclick="switchSection('ai', this)">
                            <span class="nav-icon">🤖</span>
                            AI设置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#security" class="nav-link" onclick="switchSection('security', this)">
                            <span class="nav-icon">🔒</span>
                            安全设置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#monitoring" class="nav-link" onclick="switchSection('monitoring', this)">
                            <span class="nav-icon">📊</span>
                            监控设置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#notifications" class="nav-link" onclick="switchSection('notifications', this)">
                            <span class="nav-icon">🔔</span>
                            通知设置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#advanced" class="nav-link" onclick="switchSection('advanced', this)">
                            <span class="nav-icon">⚡</span>
                            高级设置
                        </a>
                    </li>
                    <li class="nav-item">
                        <a href="#statistics" class="nav-link" onclick="switchSection('statistics', this)">
                            <span class="nav-icon">📈</span>
                            统计信息
                        </a>
                    </li>
                </ul>
            </div>

            <!-- 主内容区 -->
            <div class="settings-main">
                <!-- 常规设置 -->
                <div id="general" class="settings-section active">
                    <h2 class="section-title">常规设置</h2>
                    
                    <div class="settings-group">
                        <h3 class="group-title">基本信息</h3>
                        <p class="group-description">配置系统的基本信息和显示设置</p>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">网站标题</div>
                                <div class="setting-description">显示在浏览器标题栏的网站名称</div>
                            </div>
                            <div class="setting-control">
                                <input type="text" class="form-input" id="site_title" value="AI运维管理平台" style="width: 200px;">
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">网站描述</div>
                                <div class="setting-description">网站的简短描述信息</div>
                            </div>
                            <div class="setting-control">
                                <input type="text" class="form-input" id="site_description" value="智能化运维管理平台" style="width: 200px;">
                            </div>
                        </div>
                    </div>

                    <div class="settings-group">
                        <h3 class="group-title">界面设置</h3>
                        <p class="group-description">自定义用户界面的显示效果</p>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">启用动画效果</div>
                                <div class="setting-description">开启界面过渡动画和交互效果</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch active" onclick="toggleSetting(this, 'enable_animations')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">紧凑模式</div>
                                <div class="setting-description">减少界面元素间距，显示更多内容</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch" onclick="toggleSetting(this, 'compact_mode')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">语言设置</div>
                                <div class="setting-description">选择界面显示语言</div>
                            </div>
                            <div class="setting-control">
                                <select class="select-input" id="language">
                                    <option value="zh-CN">简体中文</option>
                                    <option value="en-US">English</option>
                                    <option value="ja-JP">日本語</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 外观主题 -->
                <div id="appearance" class="settings-section">
                    <h2 class="section-title">外观主题</h2>
                    
                    <div class="settings-group">
                        <h3 class="group-title">主题选择</h3>
                        <p class="group-description">选择您喜欢的界面主题风格</p>
                        
                        <div class="theme-grid" id="themeGrid">
                            <!-- 动态加载主题列表 -->
                        </div>
                    </div>

                    <div class="settings-group">
                        <h3 class="group-title">自定义设置</h3>
                        <p class="group-description">个性化定制界面显示效果</p>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">字体大小</div>
                                <div class="setting-description">调整界面文字的显示大小</div>
                            </div>
                            <div class="setting-control">
                                <input type="range" min="12" max="20" value="16" id="fontSize" onchange="updateFontSize(this.value)">
                                <span id="fontSizeValue">16px</span>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">高对比度</div>
                                <div class="setting-description">提高界面对比度，便于阅读</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch" onclick="toggleSetting(this, 'high_contrast')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">减少动画</div>
                                <div class="setting-description">减少界面动画效果，提升性能</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch" onclick="toggleSetting(this, 'reduced_motion')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- AI设置 -->
                <div id="ai" class="settings-section">
                    <h2 class="section-title">AI设置</h2>
                    
                    <div class="settings-group">
                        <h3 class="group-title">AI助手配置</h3>
                        <p class="group-description">配置AI助手的行为和响应方式</p>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">启用AI建议</div>
                                <div class="setting-description">开启智能建议和自动完成功能</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch active" onclick="toggleSetting(this, 'enable_ai_suggestions')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">AI响应风格</div>
                                <div class="setting-description">选择AI助手的交流风格</div>
                            </div>
                            <div class="setting-control">
                                <select class="select-input" id="ai_response_style">
                                    <option value="formal">正式</option>
                                    <option value="friendly" selected>友好</option>
                                    <option value="casual">随意</option>
                                    <option value="technical">技术</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">响应详细程度</div>
                                <div class="setting-description">控制AI回答的详细程度</div>
                            </div>
                            <div class="setting-control">
                                <select class="select-input" id="ai_verbosity">
                                    <option value="brief">简洁</option>
                                    <option value="normal" selected>正常</option>
                                    <option value="detailed">详细</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全设置 -->
                <div id="security" class="settings-section">
                    <h2 class="section-title">安全设置</h2>
                    
                    <div class="settings-group">
                        <h3 class="group-title">会话管理</h3>
                        <p class="group-description">配置用户会话和登录安全策略</p>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">会话超时时间</div>
                                <div class="setting-description">用户无操作后自动退出的时间（分钟）</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="number-input" id="session_timeout" value="60" min="5" max="1440">
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">最大登录尝试次数</div>
                                <div class="setting-description">账户锁定前允许的最大登录失败次数</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="number-input" id="max_login_attempts" value="5" min="1" max="20">
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">强制HTTPS</div>
                                <div class="setting-description">强制使用HTTPS加密连接</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch active" onclick="toggleSetting(this, 'force_https')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 监控设置 -->
                <div id="monitoring" class="settings-section">
                    <h2 class="section-title">监控设置</h2>
                    
                    <div class="settings-group">
                        <h3 class="group-title">数据刷新</h3>
                        <p class="group-description">配置监控数据的更新频率</p>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">刷新间隔</div>
                                <div class="setting-description">监控数据自动刷新的时间间隔（秒）</div>
                            </div>
                            <div class="setting-control">
                                <input type="number" class="number-input" id="refresh_interval" value="30" min="5" max="300">
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">实时更新</div>
                                <div class="setting-description">启用WebSocket实时数据推送</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch active" onclick="toggleSetting(this, 'realtime_updates')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 通知设置 -->
                <div id="notifications" class="settings-section">
                    <h2 class="section-title">通知设置</h2>
                    
                    <div class="settings-group">
                        <h3 class="group-title">通知偏好</h3>
                        <p class="group-description">配置系统通知的显示方式</p>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">桌面通知</div>
                                <div class="setting-description">允许显示浏览器桌面通知</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch" onclick="toggleSetting(this, 'desktop_notifications')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">声音提醒</div>
                                <div class="setting-description">重要通知时播放提示音</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch" onclick="toggleSetting(this, 'sound_notifications')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 高级设置 -->
                <div id="advanced" class="settings-section">
                    <h2 class="section-title">高级设置</h2>
                    
                    <div class="settings-group">
                        <h3 class="group-title">开发者选项</h3>
                        <p class="group-description">面向开发者的高级配置选项</p>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">调试模式</div>
                                <div class="setting-description">启用详细的调试信息输出</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch" onclick="toggleSetting(this, 'debug_mode')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <div class="setting-label">性能监控</div>
                                <div class="setting-description">启用前端性能监控和分析</div>
                            </div>
                            <div class="setting-control">
                                <div class="toggle-switch" onclick="toggleSetting(this, 'performance_monitoring')">
                                    <div class="toggle-slider"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="settings-group">
                        <h3 class="group-title">数据管理</h3>
                        <p class="group-description">管理应用数据和缓存</p>
                        
                        <div class="import-export-section">
                            <div class="import-export-card">
                                <div class="card-icon">🗑️</div>
                                <div class="card-title">清除缓存</div>
                                <div class="card-description">清除浏览器缓存和临时数据</div>
                                <button class="btn-secondary" onclick="clearCache()">清除缓存</button>
                            </div>
                            
                            <div class="import-export-card">
                                <div class="card-icon">🔄</div>
                                <div class="card-title">重置设置</div>
                                <div class="card-description">将所有设置恢复为默认值</div>
                                <button class="btn-secondary" onclick="resetSettings()">重置设置</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 统计信息 -->
                <div id="statistics" class="settings-section">
                    <h2 class="section-title">统计信息</h2>
                    
                    <div class="settings-group">
                        <h3 class="group-title">设置统计</h3>
                        <p class="group-description">查看设置管理器的使用统计</p>
                        
                        <div class="stats-grid" id="statsGrid">
                            <!-- 动态加载统计数据 -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导入设置模态框 -->
    <div class="modal" id="importModal">
        <div class="modal-content">
            <h2 class="modal-title">导入设置</h2>
            <div class="form-group">
                <label class="form-label">设置数据</label>
                <textarea class="form-textarea" id="importData" placeholder="请粘贴导出的设置JSON数据..."></textarea>
            </div>
            <div class="modal-actions">
                <button class="btn-secondary" onclick="hideImportModal()">取消</button>
                <button class="btn-primary" onclick="importSettings()">导入</button>
            </div>
        </div>
    </div>

    <!-- 通知组件 -->
    <div class="notification" id="notification"></div>

    <script>
        // 全局变量
        let currentSection = 'general';
        let settings = {};
        let themes = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadSettings();
            loadThemes();
            loadStatistics();
        });

        // 切换设置分区
        function switchSection(sectionId, linkElement) {
            // 隐藏所有分区
            document.querySelectorAll('.settings-section').forEach(section => {
                section.classList.remove('active');
            });
            
            // 移除所有导航链接的激活状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });
            
            // 显示目标分区
            document.getElementById(sectionId).classList.add('active');
            linkElement.classList.add('active');
            
            currentSection = sectionId;
        }

        // 切换设置开关
        function toggleSetting(element, settingKey) {
            element.classList.toggle('active');
            const isActive = element.classList.contains('active');
            
            // 保存设置
            saveSetting('ui', settingKey, isActive.toString(), 'boolean');
            
            showNotification(`${settingKey} ${isActive ? '已启用' : '已禁用'}`, 'success');
        }

        // 更新字体大小
        function updateFontSize(value) {
            document.getElementById('fontSizeValue').textContent = value + 'px';
            document.documentElement.style.setProperty('--font-size-base', value + 'px');
            
            // 保存设置
            saveSetting('ui', 'font_size', value, 'number');
        }

        // 加载设置
        async function loadSettings() {
            try {
                // 加载系统设置
                const systemResponse = await fetch('/api/v1/enhanced-settings/system?public_only=true');
                const systemResult = await systemResponse.json();
                
                if (systemResult.success) {
                    systemResult.data.forEach(setting => {
                        const element = document.getElementById(setting.key);
                        if (element) {
                            if (setting.type === 'boolean') {
                                const toggle = element.closest('.setting-item')?.querySelector('.toggle-switch');
                                if (toggle) {
                                    if (setting.value === 'true') {
                                        toggle.classList.add('active');
                                    } else {
                                        toggle.classList.remove('active');
                                    }
                                }
                            } else {
                                element.value = setting.value;
                            }
                        }
                    });
                }
                
                // 加载用户设置
                const userResponse = await fetch('/api/v1/enhanced-settings/user');
                const userResult = await userResponse.json();
                
                if (userResult.success) {
                    userResult.data.forEach(setting => {
                        settings[`${setting.category}.${setting.key}`] = setting.value;
                    });
                }
            } catch (error) {
                console.error('Failed to load settings:', error);
                showNotification('加载设置失败', 'error');
            }
        }

        // 保存设置
        async function saveSetting(category, key, value, type) {
            try {
                const response = await fetch(`/api/v1/enhanced-settings/user/${category}/${key}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        value: value,
                        type: type
                    })
                });

                const result = await response.json();
                
                if (!result.success) {
                    throw new Error(result.error);
                }
                
                settings[`${category}.${key}`] = value;
            } catch (error) {
                console.error('Failed to save setting:', error);
                showNotification('保存设置失败: ' + error.message, 'error');
            }
        }

        // 保存所有设置
        async function saveAllSettings() {
            // 收集所有表单数据
            const formInputs = document.querySelectorAll('.form-input, .select-input, .number-input');
            const promises = [];
            
            formInputs.forEach(input => {
                if (input.id) {
                    const category = input.closest('.settings-section').id;
                    const key = input.id;
                    const value = input.value;
                    const type = input.type === 'number' ? 'number' : 'string';
                    
                    promises.push(saveSetting(category, key, value, type));
                }
            });
            
            try {
                await Promise.all(promises);
                showNotification('所有设置已保存', 'success');
            } catch (error) {
                showNotification('保存设置时出现错误', 'error');
            }
        }

        // 加载主题
        async function loadThemes() {
            try {
                const response = await fetch('/api/v1/enhanced-settings/themes');
                const result = await response.json();
                
                if (result.success) {
                    themes = result.data;
                    renderThemes();
                }
            } catch (error) {
                console.error('Failed to load themes:', error);
                showNotification('加载主题失败', 'error');
            }
        }

        // 渲染主题
        function renderThemes() {
            const themeGrid = document.getElementById('themeGrid');
            themeGrid.innerHTML = '';

            const defaultThemes = [
                { name: 'light', display_name: '浅色主题', description: '清新明亮的浅色主题', preview: '☀️' },
                { name: 'dark', display_name: '深色主题', description: '护眼舒适的深色主题', preview: '🌙' },
                { name: 'blue', display_name: '海洋蓝', description: '清爽的蓝色主题', preview: '🌊' },
                { name: 'green', display_name: '森林绿', description: '自然的绿色主题', preview: '🌲' },
                { name: 'purple', display_name: '优雅紫', description: '神秘的紫色主题', preview: '🔮' },
                { name: 'orange', display_name: '活力橙', description: '温暖的橙色主题', preview: '🔥' }
            ];

            defaultThemes.forEach(theme => {
                const themeCard = document.createElement('div');
                themeCard.className = 'theme-card';
                themeCard.onclick = () => selectTheme(theme.name, themeCard);
                
                themeCard.innerHTML = `
                    <div class="theme-preview" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
                        ${theme.preview}
                    </div>
                    <div class="theme-name">${theme.display_name}</div>
                    <div class="theme-description">${theme.description}</div>
                `;
                
                themeGrid.appendChild(themeCard);
            });
        }

        // 选择主题
        function selectTheme(themeName, cardElement) {
            // 移除其他主题的选中状态
            document.querySelectorAll('.theme-card').forEach(card => {
                card.classList.remove('active');
            });
            
            // 添加选中状态
            cardElement.classList.add('active');
            
            // 应用主题
            document.documentElement.setAttribute('data-theme', themeName);
            
            // 保存设置
            saveSetting('ui', 'theme', themeName, 'string');
            
            showNotification(`已切换到${cardElement.querySelector('.theme-name').textContent}`, 'success');
        }

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('/api/v1/enhanced-settings/metrics');
                const result = await response.json();
                
                if (result.success) {
                    renderStatistics(result.data);
                }
            } catch (error) {
                console.error('Failed to load statistics:', error);
            }
        }

        // 渲染统计信息
        function renderStatistics(metrics) {
            const statsGrid = document.getElementById('statsGrid');
            statsGrid.innerHTML = '';

            const stats = [
                { label: '系统设置', value: metrics.system_settings || 0 },
                { label: '用户设置', value: metrics.user_settings || 0 },
                { label: '主题配置', value: metrics.theme_configs || 0 },
                { label: '总操作数', value: metrics.total_operations || 0 },
                { label: '成功操作', value: metrics.success_operations || 0 },
                { label: '缓存命中率', value: (metrics.cache_hit_rate * 100 || 0).toFixed(1) + '%' }
            ];

            stats.forEach(stat => {
                const statCard = document.createElement('div');
                statCard.className = 'stat-card';
                statCard.innerHTML = `
                    <div class="stat-value">${stat.value}</div>
                    <div class="stat-label">${stat.label}</div>
                `;
                statsGrid.appendChild(statCard);
            });
        }

        // 导出设置
        async function exportSettings() {
            try {
                const response = await fetch('/api/v1/enhanced-settings/export');
                const result = await response.json();
                
                if (result.success) {
                    const dataStr = JSON.stringify(result.data, null, 2);
                    const dataBlob = new Blob([dataStr], { type: 'application/json' });
                    
                    const link = document.createElement('a');
                    link.href = URL.createObjectURL(dataBlob);
                    link.download = `aiops-settings-${new Date().toISOString().split('T')[0]}.json`;
                    link.click();
                    
                    showNotification('设置导出成功', 'success');
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Failed to export settings:', error);
                showNotification('导出设置失败: ' + error.message, 'error');
            }
        }

        // 显示导入模态框
        function showImportModal() {
            document.getElementById('importModal').classList.add('show');
        }

        // 隐藏导入模态框
        function hideImportModal() {
            document.getElementById('importModal').classList.remove('show');
            document.getElementById('importData').value = '';
        }

        // 导入设置
        async function importSettings() {
            const importData = document.getElementById('importData').value.trim();
            
            if (!importData) {
                showNotification('请输入设置数据', 'error');
                return;
            }
            
            try {
                const data = JSON.parse(importData);
                
                const response = await fetch('/api/v1/enhanced-settings/import', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(data)
                });

                const result = await response.json();
                
                if (result.success) {
                    hideImportModal();
                    showNotification('设置导入成功', 'success');
                    
                    // 重新加载设置
                    setTimeout(() => {
                        location.reload();
                    }, 1000);
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Failed to import settings:', error);
                showNotification('导入设置失败: ' + error.message, 'error');
            }
        }

        // 清除缓存
        function clearCache() {
            if (confirm('确定要清除所有缓存数据吗？')) {
                localStorage.clear();
                sessionStorage.clear();
                showNotification('缓存已清除', 'success');
            }
        }

        // 重置设置
        function resetSettings() {
            if (confirm('确定要重置所有设置为默认值吗？此操作不可撤销。')) {
                // 这里应该调用重置API
                showNotification('设置重置功能开发中', 'info');
            }
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>
