package agent

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// Agent 本地执行代理
type Agent struct {
	config            *AgentConfig
	logger            *logrus.Logger
	commandFilter     *CommandFilter
	resourceMonitor   *ResourceMonitor
	executionQueue    *ExecutionQueue
	sandboxManager    *SandboxManager
	permissionManager *PermissionManager
	executionMonitor  *ExecutionMonitor
	taskManager       *TaskManager
	executorManager   *ExecutorManager
	riskEngine        *RiskAssessmentEngine
	riskController    *DynamicRiskController
	reportSystem      *RiskReportSystem
	mutex             sync.RWMutex
	isRunning         bool
}

// AgentConfig Agent配置
type AgentConfig struct {
	// 执行限制
	MaxConcurrentJobs int           `json:"max_concurrent_jobs"`
	DefaultTimeout    time.Duration `json:"default_timeout"`
	MaxTimeout        time.Duration `json:"max_timeout"`

	// 资源限制
	MaxMemoryMB    int64 `json:"max_memory_mb"`
	MaxCPUPercent  int   `json:"max_cpu_percent"`
	MaxDiskUsageMB int64 `json:"max_disk_usage_mb"`

	// 安全设置
	EnableSandbox   bool     `json:"enable_sandbox"`
	AllowedUsers    []string `json:"allowed_users"`
	RestrictedPaths []string `json:"restricted_paths"`
	RequireApproval bool     `json:"require_approval"`

	// 日志和审计
	EnableAuditLog bool   `json:"enable_audit_log"`
	AuditLogPath   string `json:"audit_log_path"`
	LogLevel       string `json:"log_level"`
}

// ExecutionRequest 执行请求
type ExecutionRequest struct {
	ID          string                 `json:"id"`
	Command     string                 `json:"command"`
	Args        []string               `json:"args,omitempty"`
	WorkingDir  string                 `json:"working_dir,omitempty"`
	Environment map[string]string      `json:"environment,omitempty"`
	Timeout     time.Duration          `json:"timeout,omitempty"`
	UserID      int64                  `json:"user_id"`
	SessionID   string                 `json:"session_id"`
	Priority    int                    `json:"priority"` // 1-10, 10最高
	Metadata    map[string]interface{} `json:"metadata,omitempty"`
	CreatedAt   time.Time              `json:"created_at"`
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	ID            string                 `json:"id"`
	RequestID     string                 `json:"request_id"`
	Success       bool                   `json:"success"`
	ExitCode      int                    `json:"exit_code"`
	Stdout        string                 `json:"stdout"`
	Stderr        string                 `json:"stderr"`
	Duration      time.Duration          `json:"duration"`
	ResourceUsage *ResourceUsage         `json:"resource_usage,omitempty"`
	Error         string                 `json:"error,omitempty"`
	StartTime     time.Time              `json:"start_time"`
	EndTime       time.Time              `json:"end_time"`
	Metadata      map[string]interface{} `json:"metadata,omitempty"`
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	MaxMemoryMB   float64 `json:"max_memory_mb"`
	AvgCPUPercent float64 `json:"avg_cpu_percent"`
	DiskReadMB    float64 `json:"disk_read_mb"`
	DiskWriteMB   float64 `json:"disk_write_mb"`
	NetworkInMB   float64 `json:"network_in_mb"`
	NetworkOutMB  float64 `json:"network_out_mb"`
}

// NewAgent 创建新的Agent
func NewAgent(config *AgentConfig, logger *logrus.Logger) *Agent {
	if config == nil {
		config = DefaultAgentConfig()
	}

	// 创建安全执行框架组件
	sandboxConfig := DefaultSandboxConfig()
	permissionConfig := DefaultPermissionConfig()
	monitorConfig := DefaultMonitorConfig()
	taskManagerConfig := DefaultTaskManagerConfig()
	executorManagerConfig := DefaultExecutorManagerConfig()
	riskAssessmentConfig := DefaultRiskAssessmentConfig()
	dynamicRiskConfig := DefaultDynamicRiskConfig()
	reportSystemConfig := DefaultRiskReportConfig()

	// 创建风险评估引擎
	riskEngine := NewRiskAssessmentEngine(riskAssessmentConfig, logger)

	// 创建动态风险控制器
	riskController := NewDynamicRiskController(dynamicRiskConfig, riskEngine, logger)

	agent := &Agent{
		config:            config,
		logger:            logger,
		commandFilter:     NewCommandFilter(config, logger),
		resourceMonitor:   NewResourceMonitor(config, logger),
		executionQueue:    NewExecutionQueue(config.MaxConcurrentJobs, logger),
		sandboxManager:    NewSandboxManager(sandboxConfig, logger),
		permissionManager: NewPermissionManager(permissionConfig, logger),
		executionMonitor:  NewExecutionMonitor(monitorConfig, logger),
		taskManager:       NewTaskManager(taskManagerConfig, logger),
		executorManager:   NewExecutorManager(executorManagerConfig, logger),
		riskEngine:        riskEngine,
		riskController:    riskController,
		reportSystem:      NewRiskReportSystem(reportSystemConfig, riskEngine, riskController, logger),
		isRunning:         false,
	}

	return agent
}

// DefaultAgentConfig 默认Agent配置
func DefaultAgentConfig() *AgentConfig {
	return &AgentConfig{
		MaxConcurrentJobs: 5,
		DefaultTimeout:    30 * time.Second,
		MaxTimeout:        5 * time.Minute,
		MaxMemoryMB:       512,
		MaxCPUPercent:     80,
		MaxDiskUsageMB:    100,
		EnableSandbox:     true,
		AllowedUsers:      []string{"root", "admin"},
		RestrictedPaths:   []string{"/etc/passwd", "/etc/shadow", "/boot"},
		RequireApproval:   true,
		EnableAuditLog:    true,
		AuditLogPath:      "/var/log/aiops-agent.log",
		LogLevel:          "info",
	}
}

// Start 启动Agent
func (a *Agent) Start(ctx context.Context) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if a.isRunning {
		return fmt.Errorf("agent is already running")
	}

	a.logger.Info("Starting AI Ops Agent with comprehensive risk management framework")

	// 启动风险报告系统
	if err := a.reportSystem.Start(ctx); err != nil {
		return fmt.Errorf("failed to start report system: %w", err)
	}

	// 启动动态风险控制器
	if err := a.riskController.Start(ctx); err != nil {
		return fmt.Errorf("failed to start risk controller: %w", err)
	}

	// 启动执行器管理器
	if err := a.executorManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start executor manager: %w", err)
	}

	// 启动任务管理器
	if err := a.taskManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start task manager: %w", err)
	}

	// 启动沙箱管理器
	if err := a.sandboxManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start sandbox manager: %w", err)
	}

	// 启动执行监控器
	if err := a.executionMonitor.Start(ctx); err != nil {
		return fmt.Errorf("failed to start execution monitor: %w", err)
	}

	// 启动资源监控
	if err := a.resourceMonitor.Start(ctx); err != nil {
		return fmt.Errorf("failed to start resource monitor: %w", err)
	}

	// 启动执行队列
	if err := a.executionQueue.Start(ctx); err != nil {
		return fmt.Errorf("failed to start execution queue: %w", err)
	}

	a.isRunning = true
	a.logger.Info("AI Ops Agent with comprehensive risk management framework started successfully")

	return nil
}

// Stop 停止Agent
func (a *Agent) Stop(ctx context.Context) error {
	a.mutex.Lock()
	defer a.mutex.Unlock()

	if !a.isRunning {
		return nil
	}

	a.logger.Info("Stopping AI Ops Agent with comprehensive risk management framework")

	// 停止执行队列
	if err := a.executionQueue.Stop(ctx); err != nil {
		a.logger.WithError(err).Error("Failed to stop execution queue")
	}

	// 停止资源监控
	if err := a.resourceMonitor.Stop(ctx); err != nil {
		a.logger.WithError(err).Error("Failed to stop resource monitor")
	}

	// 停止执行监控器
	if err := a.executionMonitor.Stop(ctx); err != nil {
		a.logger.WithError(err).Error("Failed to stop execution monitor")
	}

	// 停止沙箱管理器
	if err := a.sandboxManager.Stop(ctx); err != nil {
		a.logger.WithError(err).Error("Failed to stop sandbox manager")
	}

	// 停止任务管理器
	if err := a.taskManager.Stop(ctx); err != nil {
		a.logger.WithError(err).Error("Failed to stop task manager")
	}

	// 停止执行器管理器
	if err := a.executorManager.Stop(ctx); err != nil {
		a.logger.WithError(err).Error("Failed to stop executor manager")
	}

	// 停止动态风险控制器
	if err := a.riskController.Stop(ctx); err != nil {
		a.logger.WithError(err).Error("Failed to stop risk controller")
	}

	// 停止风险报告系统
	if err := a.reportSystem.Stop(ctx); err != nil {
		a.logger.WithError(err).Error("Failed to stop report system")
	}

	a.isRunning = false
	a.logger.Info("AI Ops Agent with comprehensive risk management framework stopped")

	return nil
}

// AssessCommandRisk 评估命令风险（增强版）
func (a *Agent) AssessCommandRisk(ctx context.Context, command string, args []string, userID int64, sessionID string) (*RiskAssessment, error) {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	if !a.isRunning {
		return nil, fmt.Errorf("agent is not running")
	}

	// 构建评估上下文
	assessmentContext := &AssessmentContext{
		UserID:           userID,
		SessionID:        sessionID,
		WorkingDir:       "/tmp", // 简化实现
		Environment:      make(map[string]string),
		PreviousCommands: make([]string, 0),
		SystemState:      a.getSystemState(),
		UserPermissions:  a.getUserPermissions(userID),
		Metadata:         make(map[string]interface{}),
	}

	// 使用风险评估引擎进行评估
	assessment, err := a.riskEngine.AssessRisk(ctx, command, args, assessmentContext)
	if err != nil {
		a.logger.WithError(err).Error("Risk assessment failed")
		return nil, fmt.Errorf("risk assessment failed: %w", err)
	}

	a.logger.WithFields(logrus.Fields{
		"command":    command,
		"risk_level": assessment.RiskLevel,
		"risk_score": assessment.RiskScore,
		"confidence": assessment.Confidence,
		"user_id":    userID,
		"session_id": sessionID,
	}).Info("Enhanced risk assessment completed")

	return assessment, nil
}

// getSystemState 获取系统状态
func (a *Agent) getSystemState() *SystemState {
	// 简化实现，返回模拟的系统状态
	return &SystemState{
		CPUUsage:           50.0,
		MemoryUsage:        60.0,
		DiskUsage:          70.0,
		LoadAverage:        1.5,
		Uptime:             24 * time.Hour,
		Services:           map[string]string{"nginx": "running", "mysql": "running"},
		Processes:          []string{"systemd", "nginx", "mysql"},
		NetworkConnections: 100,
	}
}

// getUserPermissions 获取用户权限
func (a *Agent) getUserPermissions(userID int64) *UserPermissions {
	// 简化实现，返回模拟的用户权限
	return &UserPermissions{
		UserID:      userID,
		Username:    fmt.Sprintf("user_%d", userID),
		Groups:      []string{"users"},
		Roles:       []string{"operator"},
		Permissions: []string{"read", "execute"},
		IsAdmin:     userID == 1, // 假设用户ID为1的是管理员
		IsSudo:      userID == 1,
	}
}

// GetRiskAssessmentEngine 获取风险评估引擎
func (a *Agent) GetRiskAssessmentEngine() *RiskAssessmentEngine {
	return a.riskEngine
}

// EvaluateCommandRiskControl 评估命令风险控制（增强版）
func (a *Agent) EvaluateCommandRiskControl(ctx context.Context, command string, args []string, userID int64, sessionID string) (*RiskControlDecision, error) {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	if !a.isRunning {
		return nil, fmt.Errorf("agent is not running")
	}

	// 首先进行风险评估
	assessment, err := a.AssessCommandRisk(ctx, command, args, userID, sessionID)
	if err != nil {
		return nil, fmt.Errorf("risk assessment failed: %w", err)
	}

	// 然后进行动态风险控制决策
	decision, err := a.riskController.EvaluateRiskControl(ctx, assessment, userID, sessionID)
	if err != nil {
		return nil, fmt.Errorf("risk control evaluation failed: %w", err)
	}

	a.logger.WithFields(logrus.Fields{
		"command":        command,
		"risk_score":     assessment.RiskScore,
		"risk_level":     assessment.RiskLevel,
		"control_action": decision.Action,
		"policy_id":      decision.PolicyID,
		"user_id":        userID,
		"session_id":     sessionID,
	}).Info("Command risk control evaluation completed")

	return decision, nil
}

// GetDynamicRiskController 获取动态风险控制器
func (a *Agent) GetDynamicRiskController() *DynamicRiskController {
	return a.riskController
}

// GetRiskControlStatus 获取风险控制状态
func (a *Agent) GetRiskControlStatus() *RiskControlStatus {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	status := &RiskControlStatus{
		AgentRunning:     a.isRunning,
		RiskEngineStatus: a.riskEngine != nil,
		ControllerStatus: a.riskController != nil,
	}

	if a.riskController != nil {
		controllerStatus := a.riskController.GetStatus()
		status.ControllerRunning = controllerStatus.IsRunning
		status.EmergencyMode = controllerStatus.EmergencyMode
		status.ActivePolicies = controllerStatus.ActivePolicies
		status.ActiveRisks = controllerStatus.ActiveRisks
		status.CurrentThresholds = controllerStatus.CurrentThresholds
		status.RiskTrends = controllerStatus.RiskTrends
		status.PerformanceMetrics = controllerStatus.PerformanceMetrics
	}

	return status
}

// RiskControlStatus 风险控制状态
type RiskControlStatus struct {
	AgentRunning       bool                `json:"agent_running"`
	RiskEngineStatus   bool                `json:"risk_engine_status"`
	ControllerStatus   bool                `json:"controller_status"`
	ControllerRunning  bool                `json:"controller_running"`
	EmergencyMode      bool                `json:"emergency_mode"`
	ActivePolicies     int                 `json:"active_policies"`
	ActiveRisks        int                 `json:"active_risks"`
	CurrentThresholds  *DynamicThresholds  `json:"current_thresholds"`
	RiskTrends         *RiskTrends         `json:"risk_trends"`
	PerformanceMetrics *PerformanceMetrics `json:"performance_metrics"`
}

// GetRiskReportSystem 获取风险报告系统
func (a *Agent) GetRiskReportSystem() *RiskReportSystem {
	return a.reportSystem
}

// GenerateRiskReport 生成风险报告
func (a *Agent) GenerateRiskReport(ctx context.Context, request *ReportRequest) (*RiskReport, error) {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	if !a.isRunning {
		return nil, fmt.Errorf("agent is not running")
	}

	if a.reportSystem == nil {
		return nil, fmt.Errorf("report system is not available")
	}

	a.logger.WithFields(logrus.Fields{
		"report_type": request.Type,
		"template":    request.Template,
		"time_range":  request.TimeRange,
	}).Info("Generating risk report")

	report, err := a.reportSystem.GenerateReport(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("failed to generate risk report: %w", err)
	}

	a.logger.WithField("report_id", report.ID).Info("Risk report generated successfully")

	return report, nil
}

// GetRiskReport 获取风险报告
func (a *Agent) GetRiskReport(reportID string) (*RiskReport, error) {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	if !a.isRunning {
		return nil, fmt.Errorf("agent is not running")
	}

	if a.reportSystem == nil {
		return nil, fmt.Errorf("report system is not available")
	}

	return a.reportSystem.GetReport(reportID)
}

// ListRiskReports 列出风险报告
func (a *Agent) ListRiskReports(filter *ReportFilter) ([]*RiskReport, error) {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	if !a.isRunning {
		return nil, fmt.Errorf("agent is not running")
	}

	if a.reportSystem == nil {
		return nil, fmt.Errorf("report system is not available")
	}

	return a.reportSystem.ListReports(filter)
}

// DeleteRiskReport 删除风险报告
func (a *Agent) DeleteRiskReport(reportID string) error {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	if !a.isRunning {
		return fmt.Errorf("agent is not running")
	}

	if a.reportSystem == nil {
		return fmt.Errorf("report system is not available")
	}

	return a.reportSystem.DeleteReport(reportID)
}

// GetReportSystemStatus 获取报告系统状态
func (a *Agent) GetReportSystemStatus() *ReportSystemStatus {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	status := &ReportSystemStatus{
		AgentRunning:          a.isRunning,
		ReportSystemAvailable: a.reportSystem != nil,
	}

	if a.reportSystem != nil {
		systemStatus := a.reportSystem.GetStatus()
		status.SystemRunning = systemStatus.IsRunning
		status.TotalReports = systemStatus.TotalReports
		status.ReportsByType = systemStatus.ReportsByType
		status.LastGeneration = systemStatus.LastGeneration
		status.Config = systemStatus.Config
	}

	return status
}

// ReportSystemStatus 报告系统状态
type ReportSystemStatus struct {
	AgentRunning          bool              `json:"agent_running"`
	ReportSystemAvailable bool              `json:"report_system_available"`
	SystemRunning         bool              `json:"system_running"`
	TotalReports          int               `json:"total_reports"`
	ReportsByType         map[string]int    `json:"reports_by_type"`
	LastGeneration        time.Time         `json:"last_generation"`
	Config                *RiskReportConfig `json:"config"`
}

// GetComprehensiveStatus 获取综合状态
func (a *Agent) GetComprehensiveStatus() *ComprehensiveStatus {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	status := &ComprehensiveStatus{
		AgentStatus:        a.GetStatus(),
		RiskControlStatus:  a.GetRiskControlStatus(),
		ReportSystemStatus: a.GetReportSystemStatus(),
		Timestamp:          time.Now(),
	}

	return status
}

// ComprehensiveStatus 综合状态
type ComprehensiveStatus struct {
	AgentStatus        *AgentStatus        `json:"agent_status"`
	RiskControlStatus  *RiskControlStatus  `json:"risk_control_status"`
	ReportSystemStatus *ReportSystemStatus `json:"report_system_status"`
	Timestamp          time.Time           `json:"timestamp"`
}

// ExecuteCommand 执行命令
func (a *Agent) ExecuteCommand(ctx context.Context, req *ExecutionRequest) (*ExecutionResult, error) {
	if !a.isRunning {
		return nil, fmt.Errorf("agent is not running")
	}

	// 验证请求
	if err := a.validateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// 权限检查
	permCheck, err := a.permissionManager.CheckPermission(req.UserID, req.Command, req.Args, req.WorkingDir)
	if err != nil {
		return nil, fmt.Errorf("permission check failed: %w", err)
	}

	if !permCheck.Allowed {
		return nil, fmt.Errorf("permission denied: %s", permCheck.Reason)
	}

	// 命令过滤检查
	if err := a.commandFilter.ValidateCommand(req.Command, req.Args); err != nil {
		return nil, fmt.Errorf("command validation failed: %w", err)
	}

	// 如果需要审批且风险等级高，先进行审批流程
	if permCheck.RequiresApproval && (permCheck.RiskLevel == "high" || permCheck.RiskLevel == "critical") {
		a.logger.WithFields(logrus.Fields{
			"user_id":    req.UserID,
			"command":    req.Command,
			"risk_level": permCheck.RiskLevel,
		}).Warn("High-risk command requires approval")

		// 在实际实现中，这里应该触发审批流程
		// 简化实现，直接拒绝高风险命令
		return nil, fmt.Errorf("high-risk command requires approval: %s", permCheck.RiskLevel)
	}

	// 添加到执行队列
	result, err := a.executionQueue.Submit(ctx, req)
	if err != nil {
		return nil, fmt.Errorf("failed to submit execution request: %w", err)
	}

	return result, nil
}

// ExecuteCommandSync 同步执行命令
func (a *Agent) ExecuteCommandSync(ctx context.Context, req *ExecutionRequest) (*ExecutionResult, error) {
	if !a.isRunning {
		return nil, fmt.Errorf("agent is not running")
	}

	// 验证请求
	if err := a.validateRequest(req); err != nil {
		return nil, fmt.Errorf("invalid request: %w", err)
	}

	// 权限检查
	permCheck, err := a.permissionManager.CheckPermission(req.UserID, req.Command, req.Args, req.WorkingDir)
	if err != nil {
		return nil, fmt.Errorf("permission check failed: %w", err)
	}

	if !permCheck.Allowed {
		return nil, fmt.Errorf("permission denied: %s", permCheck.Reason)
	}

	// 命令过滤检查
	if err := a.commandFilter.ValidateCommand(req.Command, req.Args); err != nil {
		return nil, fmt.Errorf("command validation failed: %w", err)
	}

	// 直接执行（带安全框架）
	return a.executeCommandWithSecurity(ctx, req, permCheck)
}

// validateRequest 验证请求
func (a *Agent) validateRequest(req *ExecutionRequest) error {
	if req == nil {
		return fmt.Errorf("request cannot be nil")
	}

	if req.Command == "" {
		return fmt.Errorf("command cannot be empty")
	}

	if req.Timeout == 0 {
		req.Timeout = a.config.DefaultTimeout
	}

	if req.Timeout > a.config.MaxTimeout {
		return fmt.Errorf("timeout %v exceeds maximum allowed %v", req.Timeout, a.config.MaxTimeout)
	}

	if req.ID == "" {
		req.ID = generateExecutionID()
	}

	if req.CreatedAt.IsZero() {
		req.CreatedAt = time.Now()
	}

	return nil
}

// executeCommandInternal 内部执行命令
func (a *Agent) executeCommandInternal(ctx context.Context, req *ExecutionRequest) (*ExecutionResult, error) {
	startTime := time.Now()

	a.logger.WithFields(logrus.Fields{
		"request_id": req.ID,
		"command":    req.Command,
		"args":       req.Args,
		"user_id":    req.UserID,
		"session_id": req.SessionID,
	}).Info("Executing command")

	// 创建执行上下文
	execCtx, cancel := context.WithTimeout(ctx, req.Timeout)
	defer cancel()

	// 构建命令
	cmd := exec.CommandContext(execCtx, req.Command, req.Args...)

	// 设置工作目录
	if req.WorkingDir != "" {
		cmd.Dir = req.WorkingDir
	}

	// 设置环境变量
	if len(req.Environment) > 0 {
		env := make([]string, 0, len(req.Environment))
		for k, v := range req.Environment {
			env = append(env, fmt.Sprintf("%s=%s", k, v))
		}
		cmd.Env = env
	}

	// 启动资源监控
	resourceCtx, resourceCancel := context.WithCancel(ctx)
	defer resourceCancel()

	var resourceUsage *ResourceUsage
	if a.config.EnableSandbox {
		resourceUsage = a.resourceMonitor.StartMonitoring(resourceCtx, req.ID)
	}

	// 执行命令
	stdout, stderr, err := a.runCommand(cmd)

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// 构建结果
	result := &ExecutionResult{
		ID:            generateResultID(),
		RequestID:     req.ID,
		Success:       err == nil,
		Stdout:        stdout,
		Stderr:        stderr,
		Duration:      duration,
		ResourceUsage: resourceUsage,
		StartTime:     startTime,
		EndTime:       endTime,
		Metadata:      make(map[string]interface{}),
	}

	if err != nil {
		result.Error = err.Error()
		if exitError, ok := err.(*exec.ExitError); ok {
			result.ExitCode = exitError.ExitCode()
		} else {
			result.ExitCode = -1
		}
	}

	// 记录审计日志
	a.logExecution(req, result)

	return result, nil
}

// runCommand 运行命令
func (a *Agent) runCommand(cmd *exec.Cmd) (string, string, error) {
	var stdout, stderr strings.Builder
	cmd.Stdout = &stdout
	cmd.Stderr = &stderr

	err := cmd.Run()
	return stdout.String(), stderr.String(), err
}

// logExecution 记录执行日志
func (a *Agent) logExecution(req *ExecutionRequest, result *ExecutionResult) {
	if !a.config.EnableAuditLog {
		return
	}

	a.logger.WithFields(logrus.Fields{
		"request_id":  req.ID,
		"result_id":   result.ID,
		"command":     req.Command,
		"args":        req.Args,
		"user_id":     req.UserID,
		"session_id":  req.SessionID,
		"success":     result.Success,
		"exit_code":   result.ExitCode,
		"duration_ms": result.Duration.Milliseconds(),
	}).Info("Command execution completed")
}

// GetStatus 获取Agent状态
func (a *Agent) GetStatus() *AgentStatus {
	a.mutex.RLock()
	defer a.mutex.RUnlock()

	return &AgentStatus{
		IsRunning:       a.isRunning,
		QueueSize:       a.executionQueue.GetQueueSize(),
		ActiveJobs:      a.executionQueue.GetActiveJobs(),
		TotalExecutions: a.executionQueue.GetTotalExecutions(),
		ResourceUsage:   a.resourceMonitor.GetCurrentUsage(),
		Config:          a.config,
	}
}

// executeCommandWithSecurity 带安全框架的命令执行
func (a *Agent) executeCommandWithSecurity(ctx context.Context, req *ExecutionRequest, permCheck *PermissionCheck) (*ExecutionResult, error) {
	// 开始执行监控
	session := a.executionMonitor.StartExecution(req)

	var result *ExecutionResult
	var err error

	// 根据配置决定是否使用沙箱
	if a.sandboxManager.GetSandboxStatus().Enabled && permCheck.RiskLevel != "low" {
		// 高风险命令使用沙箱执行
		result, err = a.executeInSandbox(ctx, req, session)
	} else {
		// 低风险命令直接执行
		result, err = a.executeCommandInternal(ctx, req)
	}

	// 结束执行监控
	if result != nil {
		a.executionMonitor.EndExecution(session.ID, result)
	}

	return result, err
}

// executeInSandbox 在沙箱中执行命令
func (a *Agent) executeInSandbox(ctx context.Context, req *ExecutionRequest, session *ExecutionSession) (*ExecutionResult, error) {
	// 创建沙箱
	sandbox, err := a.sandboxManager.CreateSandbox(ctx, req.UserID, req.SessionID)
	if err != nil {
		return nil, fmt.Errorf("failed to create sandbox: %w", err)
	}

	// 确保清理沙箱
	defer func() {
		if cleanupErr := a.sandboxManager.DestroySandbox(sandbox.ID); cleanupErr != nil {
			a.logger.WithError(cleanupErr).WithField("sandbox_id", sandbox.ID).Error("Failed to cleanup sandbox")
		}
	}()

	// 在沙箱中执行命令
	result, err := a.sandboxManager.ExecuteInSandbox(ctx, sandbox.ID, req.Command, req.Args)
	if err != nil {
		return nil, fmt.Errorf("sandbox execution failed: %w", err)
	}

	// 更新会话信息
	session.mutex.Lock()
	session.SandboxID = sandbox.ID
	session.mutex.Unlock()

	a.logger.WithFields(logrus.Fields{
		"request_id": req.ID,
		"sandbox_id": sandbox.ID,
		"command":    req.Command,
		"success":    result.Success,
	}).Info("Command executed in sandbox")

	return result, nil
}

// AgentStatus Agent状态
type AgentStatus struct {
	IsRunning       bool           `json:"is_running"`
	QueueSize       int            `json:"queue_size"`
	ActiveJobs      int            `json:"active_jobs"`
	TotalExecutions int64          `json:"total_executions"`
	ResourceUsage   *ResourceUsage `json:"resource_usage"`
	Config          *AgentConfig   `json:"config"`
}

// 辅助函数
func generateExecutionID() string {
	return fmt.Sprintf("exec_%d", time.Now().UnixNano())
}

func generateResultID() string {
	return fmt.Sprintf("result_%d", time.Now().UnixNano())
}
