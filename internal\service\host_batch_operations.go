package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// HostBatchOperationsService 主机批量操作服务
type HostBatchOperationsService struct {
	db          *gorm.DB
	logger      *logrus.Logger
	hostService HostService
	sshPool     *SSHConnectionPool
	wsManager   *WebSocketManager
	config      *BatchOperationsConfig
}

// BatchOperationsConfig 批量操作配置
type BatchOperationsConfig struct {
	MaxConcurrentOperations int           `json:"max_concurrent_operations"`
	OperationTimeout        time.Duration `json:"operation_timeout"`
	RetryAttempts           int           `json:"retry_attempts"`
	RetryDelay              time.Duration `json:"retry_delay"`
	EnableProgressUpdates   bool          `json:"enable_progress_updates"`
}

// BatchOperationRequest 批量操作请求
type BatchOperationRequest struct {
	HostIDs     []int64                `json:"host_ids"`
	Operation   string                 `json:"operation"`
	Parameters  map[string]interface{} `json:"parameters"`
	UserID      int64                  `json:"user_id"`
	Description string                 `json:"description"`
}

// BatchOperationResult 批量操作结果
type BatchOperationResult struct {
	RequestID    string                    `json:"request_id"`
	TotalHosts   int                       `json:"total_hosts"`
	SuccessCount int                       `json:"success_count"`
	FailureCount int                       `json:"failure_count"`
	Results      []HostOperationResult     `json:"results"`
	StartTime    time.Time                 `json:"start_time"`
	EndTime      time.Time                 `json:"end_time"`
	Duration     time.Duration             `json:"duration"`
	Status       string                    `json:"status"` // running, completed, failed
}

// HostOperationResult 单个主机操作结果
type HostOperationResult struct {
	HostID      int64         `json:"host_id"`
	HostName    string        `json:"host_name"`
	IPAddress   string        `json:"ip_address"`
	Success     bool          `json:"success"`
	Message     string        `json:"message"`
	Output      string        `json:"output,omitempty"`
	Error       string        `json:"error,omitempty"`
	Duration    time.Duration `json:"duration"`
	RetryCount  int           `json:"retry_count"`
}

// NewHostBatchOperationsService 创建主机批量操作服务
func NewHostBatchOperationsService(
	db *gorm.DB,
	logger *logrus.Logger,
	hostService HostService,
	sshPool *SSHConnectionPool,
	wsManager *WebSocketManager,
) *HostBatchOperationsService {
	config := &BatchOperationsConfig{
		MaxConcurrentOperations: 10,
		OperationTimeout:        5 * time.Minute,
		RetryAttempts:           3,
		RetryDelay:              2 * time.Second,
		EnableProgressUpdates:   true,
	}

	return &HostBatchOperationsService{
		db:          db,
		logger:      logger,
		hostService: hostService,
		sshPool:     sshPool,
		wsManager:   wsManager,
		config:      config,
	}
}

// ExecuteBatchOperation 执行批量操作
func (hbos *HostBatchOperationsService) ExecuteBatchOperation(ctx context.Context, req *BatchOperationRequest) (*BatchOperationResult, error) {
	requestID := fmt.Sprintf("batch_%d_%d", time.Now().Unix(), req.UserID)
	
	hbos.logger.WithFields(logrus.Fields{
		"request_id":  requestID,
		"operation":   req.Operation,
		"host_count":  len(req.HostIDs),
		"user_id":     req.UserID,
	}).Info("🚀 Starting batch operation")

	// 获取主机信息
	hosts, err := hbos.getHostsByIDs(req.HostIDs)
	if err != nil {
		return nil, fmt.Errorf("failed to get hosts: %w", err)
	}

	result := &BatchOperationResult{
		RequestID:  requestID,
		TotalHosts: len(hosts),
		Results:    make([]HostOperationResult, 0, len(hosts)),
		StartTime:  time.Now(),
		Status:     "running",
	}

	// 发送开始通知
	if hbos.config.EnableProgressUpdates {
		hbos.sendProgressUpdate(requestID, "started", 0, len(hosts), result)
	}

	// 使用协程池执行批量操作
	semaphore := make(chan struct{}, hbos.config.MaxConcurrentOperations)
	var wg sync.WaitGroup
	var mutex sync.Mutex

	for _, host := range hosts {
		wg.Add(1)
		go func(h *model.Host) {
			defer wg.Done()
			semaphore <- struct{}{}
			defer func() { <-semaphore }()

			hostResult := hbos.executeOperationOnHost(ctx, h, req)
			
			mutex.Lock()
			result.Results = append(result.Results, hostResult)
			if hostResult.Success {
				result.SuccessCount++
			} else {
				result.FailureCount++
			}
			
			// 发送进度更新
			if hbos.config.EnableProgressUpdates {
				completed := len(result.Results)
				hbos.sendProgressUpdate(requestID, "progress", completed, len(hosts), result)
			}
			mutex.Unlock()
		}(host)
	}

	wg.Wait()

	// 完成操作
	result.EndTime = time.Now()
	result.Duration = result.EndTime.Sub(result.StartTime)
	
	if result.FailureCount == 0 {
		result.Status = "completed"
	} else if result.SuccessCount == 0 {
		result.Status = "failed"
	} else {
		result.Status = "partial_success"
	}

	hbos.logger.WithFields(logrus.Fields{
		"request_id":     requestID,
		"total_hosts":    result.TotalHosts,
		"success_count":  result.SuccessCount,
		"failure_count":  result.FailureCount,
		"duration":       result.Duration,
		"status":         result.Status,
	}).Info("✅ Batch operation completed")

	// 发送完成通知
	if hbos.config.EnableProgressUpdates {
		hbos.sendProgressUpdate(requestID, "completed", len(hosts), len(hosts), result)
	}

	return result, nil
}

// executeOperationOnHost 在单个主机上执行操作
func (hbos *HostBatchOperationsService) executeOperationOnHost(ctx context.Context, host *model.Host, req *BatchOperationRequest) HostOperationResult {
	startTime := time.Now()
	
	result := HostOperationResult{
		HostID:    host.ID,
		HostName:  host.Name,
		IPAddress: host.IPAddress,
		Success:   false,
	}

	// 根据操作类型执行不同的操作
	switch req.Operation {
	case "test_connection":
		result = hbos.executeTestConnection(host)
	case "execute_command":
		if command, ok := req.Parameters["command"].(string); ok {
			result = hbos.executeCommand(host, command)
		} else {
			result.Error = "command parameter is required"
		}
	case "restart_service":
		if service, ok := req.Parameters["service"].(string); ok {
			result = hbos.executeServiceOperation(host, service, "restart")
		} else {
			result.Error = "service parameter is required"
		}
	case "update_status":
		if status, ok := req.Parameters["status"].(string); ok {
			result = hbos.updateHostStatus(host, status)
		} else {
			result.Error = "status parameter is required"
		}
	default:
		result.Error = fmt.Sprintf("unsupported operation: %s", req.Operation)
	}

	result.Duration = time.Since(startTime)
	return result
}

// getHostsByIDs 根据ID列表获取主机
func (hbos *HostBatchOperationsService) getHostsByIDs(hostIDs []int64) ([]*model.Host, error) {
	var hosts []*model.Host
	err := hbos.db.Where("id IN ? AND deleted_at IS NULL", hostIDs).Find(&hosts).Error
	if err != nil {
		return nil, err
	}
	return hosts, nil
}

// sendProgressUpdate 发送进度更新
func (hbos *HostBatchOperationsService) sendProgressUpdate(requestID, status string, completed, total int, result *BatchOperationResult) {
	if hbos.wsManager == nil {
		return
	}

	progressData := map[string]interface{}{
		"request_id":     requestID,
		"status":         status,
		"completed":      completed,
		"total":          total,
		"success_count":  result.SuccessCount,
		"failure_count":  result.FailureCount,
		"progress_percent": float64(completed) / float64(total) * 100,
	}

	message := &WSMessage{
		Type:      "batch_operation_progress",
		Data:      progressData,
		Timestamp: time.Now(),
	}

	hbos.wsManager.BroadcastToAll(message)
}

// executeTestConnection 执行连接测试
func (hbos *HostBatchOperationsService) executeTestConnection(host *model.Host) HostOperationResult {
	result := HostOperationResult{
		HostID:    host.ID,
		HostName:  host.Name,
		IPAddress: host.IPAddress,
	}

	testResult, err := hbos.hostService.TestConnection(host.ID)
	if err != nil {
		result.Error = err.Error()
		result.Message = "Connection test failed"
		return result
	}

	result.Success = testResult.Success
	result.Message = testResult.Message
	if testResult.Success {
		result.Output = fmt.Sprintf("Connection successful (%.2fms)", float64(testResult.Duration)/1e6)
	}

	return result
}

// executeCommand 执行命令
func (hbos *HostBatchOperationsService) executeCommand(host *model.Host, command string) HostOperationResult {
	result := HostOperationResult{
		HostID:    host.ID,
		HostName:  host.Name,
		IPAddress: host.IPAddress,
	}

	cmdResult, err := hbos.hostService.ExecuteCommand(host.ID, &model.CommandExecuteRequest{
		Command: command,
		Timeout: int(hbos.config.OperationTimeout.Seconds()),
	})

	if err != nil {
		result.Error = err.Error()
		result.Message = "Command execution failed"
		return result
	}

	result.Success = (cmdResult.ExitCode == 0)
	result.Output = cmdResult.Stdout
	if cmdResult.Stderr != "" {
		result.Error = cmdResult.Stderr
	}
	result.Message = fmt.Sprintf("Command executed with exit code %d", cmdResult.ExitCode)

	return result
}

// executeServiceOperation 执行服务操作
func (hbos *HostBatchOperationsService) executeServiceOperation(host *model.Host, service, action string) HostOperationResult {
	command := fmt.Sprintf("systemctl %s %s", action, service)
	return hbos.executeCommand(host, command)
}

// updateHostStatus 更新主机状态
func (hbos *HostBatchOperationsService) updateHostStatus(host *model.Host, status string) HostOperationResult {
	result := HostOperationResult{
		HostID:    host.ID,
		HostName:  host.Name,
		IPAddress: host.IPAddress,
	}

	err := hbos.hostService.UpdateHostStatus(host.ID, status)
	if err != nil {
		result.Error = err.Error()
		result.Message = "Status update failed"
		return result
	}

	result.Success = true
	result.Message = fmt.Sprintf("Status updated to %s", status)
	return result
}

// BatchTestConnections 批量测试连接
func (hbos *HostBatchOperationsService) BatchTestConnections(ctx context.Context, hostIDs []int64, userID int64) (*BatchOperationResult, error) {
	req := &BatchOperationRequest{
		HostIDs:     hostIDs,
		Operation:   "test_connection",
		UserID:      userID,
		Description: "Batch connection test",
	}
	return hbos.ExecuteBatchOperation(ctx, req)
}

// BatchExecuteCommand 批量执行命令
func (hbos *HostBatchOperationsService) BatchExecuteCommand(ctx context.Context, hostIDs []int64, command string, userID int64) (*BatchOperationResult, error) {
	req := &BatchOperationRequest{
		HostIDs:   hostIDs,
		Operation: "execute_command",
		Parameters: map[string]interface{}{
			"command": command,
		},
		UserID:      userID,
		Description: fmt.Sprintf("Batch command execution: %s", command),
	}
	return hbos.ExecuteBatchOperation(ctx, req)
}

// BatchRestartService 批量重启服务
func (hbos *HostBatchOperationsService) BatchRestartService(ctx context.Context, hostIDs []int64, service string, userID int64) (*BatchOperationResult, error) {
	req := &BatchOperationRequest{
		HostIDs:   hostIDs,
		Operation: "restart_service",
		Parameters: map[string]interface{}{
			"service": service,
		},
		UserID:      userID,
		Description: fmt.Sprintf("Batch service restart: %s", service),
	}
	return hbos.ExecuteBatchOperation(ctx, req)
}
