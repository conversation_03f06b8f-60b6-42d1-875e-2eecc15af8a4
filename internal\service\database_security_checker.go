package service

import (
	"context"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
)

// DatabaseSecurityChecker 数据库安全检查器
type DatabaseSecurityChecker struct {
	logger *logrus.Logger
	rules  *SecurityRules
}

// NewDatabaseSecurityChecker 创建数据库安全检查器
func NewDatabaseSecurityChecker(logger *logrus.Logger) *DatabaseSecurityChecker {
	return &DatabaseSecurityChecker{
		logger: logger,
		rules:  NewSecurityRules(),
	}
}

// CheckOperation 检查操作安全性
func (c *DatabaseSecurityChecker) CheckOperation(ctx context.Context, request *DatabaseOperationRequest) (*SecurityCheckResult, error) {
	result := &SecurityCheckResult{
		Allowed:        true,
		RiskLevel:      "low",
		RequireConfirm: false,
		Warnings:       make([]string, 0),
	}

	c.logger.WithFields(logrus.Fields{
		"operation_type": request.OperationType,
		"table_name":     request.TableName,
		"user_id":        request.UserID,
	}).Debug("Checking database operation security")

	// 1. 检查操作类型风险
	if err := c.checkOperationTypeRisk(request, result); err != nil {
		return nil, err
	}

	// 2. 检查表访问权限
	if err := c.checkTableAccess(request, result); err != nil {
		return nil, err
	}

	// 3. 检查条件安全性
	if err := c.checkConditionSafety(request, result); err != nil {
		return nil, err
	}

	// 4. 检查数据完整性
	if err := c.checkDataIntegrity(request, result); err != nil {
		return nil, err
	}

	// 5. 检查批量操作风险
	if err := c.checkBatchOperationRisk(request, result); err != nil {
		return nil, err
	}

	// 6. 应用安全规则
	c.applySecurityRules(request, result)

	c.logger.WithFields(logrus.Fields{
		"allowed":         result.Allowed,
		"risk_level":      result.RiskLevel,
		"require_confirm": result.RequireConfirm,
		"warnings_count":  len(result.Warnings),
	}).Debug("Security check completed")

	return result, nil
}

// checkOperationTypeRisk 检查操作类型风险
func (c *DatabaseSecurityChecker) checkOperationTypeRisk(request *DatabaseOperationRequest, result *SecurityCheckResult) error {
	switch request.OperationType {
	case "select", "describe":
		// 查询操作风险较低
		result.RiskLevel = "low"

	case "insert":
		// 插入操作中等风险
		result.RiskLevel = "medium"
		result.RequireConfirm = true
		result.Warnings = append(result.Warnings, "插入操作将创建新记录")

	case "update":
		// 更新操作高风险
		result.RiskLevel = "high"
		result.RequireConfirm = true
		result.Warnings = append(result.Warnings, "更新操作将修改现有记录")

	case "delete":
		// 删除操作极高风险
		result.RiskLevel = "critical"
		result.RequireConfirm = true
		result.Warnings = append(result.Warnings, "删除操作不可逆，请谨慎确认")

	default:
		result.Allowed = false
		result.Reason = fmt.Sprintf("不支持的操作类型: %s", request.OperationType)
		return nil
	}

	return nil
}

// checkTableAccess 检查表访问权限
func (c *DatabaseSecurityChecker) checkTableAccess(request *DatabaseOperationRequest, result *SecurityCheckResult) error {
	// 检查表是否在允许列表中
	allowedTables := []string{"hosts", "users", "alerts", "operation_logs", "chat_sessions", "chat_messages"}

	tableAllowed := false
	for _, table := range allowedTables {
		if request.TableName == table {
			tableAllowed = true
			break
		}
	}

	if !tableAllowed {
		result.Allowed = false
		result.Reason = fmt.Sprintf("不允许访问表: %s", request.TableName)
		return nil
	}

	// 检查敏感表的特殊权限
	sensitiveTables := []string{"users", "operation_logs"}
	for _, table := range sensitiveTables {
		if request.TableName == table {
			if request.OperationType != "select" {
				result.RiskLevel = "critical"
				result.RequireConfirm = true
				result.Warnings = append(result.Warnings, fmt.Sprintf("正在操作敏感表: %s", table))
			}
			break
		}
	}

	return nil
}

// checkConditionSafety 检查条件安全性
func (c *DatabaseSecurityChecker) checkConditionSafety(request *DatabaseOperationRequest, result *SecurityCheckResult) error {
	// 对于修改操作，必须有条件
	if (request.OperationType == "update" || request.OperationType == "delete") && len(request.Conditions) == 0 {
		result.Allowed = false
		result.Reason = "修改或删除操作必须指定条件"
		return nil
	}

	// 检查危险的条件
	for field, value := range request.Conditions {
		// 检查SQL注入风险
		if c.containsSQLInjection(fmt.Sprintf("%v", value)) {
			result.Allowed = false
			result.Reason = "检测到潜在的SQL注入风险"
			return nil
		}

		// 检查敏感字段
		if c.isSensitiveField(field) {
			result.Warnings = append(result.Warnings, fmt.Sprintf("正在使用敏感字段作为条件: %s", field))
		}
	}

	return nil
}

// checkDataIntegrity 检查数据完整性
func (c *DatabaseSecurityChecker) checkDataIntegrity(request *DatabaseOperationRequest, result *SecurityCheckResult) error {
	if request.OperationType != "insert" && request.OperationType != "update" {
		return nil
	}

	// 检查必填字段
	requiredFields := c.getRequiredFields(request.TableName)
	for _, field := range requiredFields {
		if _, exists := request.Data[field]; !exists && request.OperationType == "insert" {
			result.Warnings = append(result.Warnings, fmt.Sprintf("缺少必填字段: %s", field))
		}
	}

	// 检查数据类型和格式
	for field, value := range request.Data {
		if err := c.validateFieldValue(request.TableName, field, value); err != nil {
			result.Warnings = append(result.Warnings, fmt.Sprintf("字段 %s 的值可能无效: %v", field, err))
		}
	}

	return nil
}

// checkBatchOperationRisk 检查批量操作风险
func (c *DatabaseSecurityChecker) checkBatchOperationRisk(request *DatabaseOperationRequest, result *SecurityCheckResult) error {
	// 如果条件过于宽泛，可能影响大量记录
	if request.OperationType == "update" || request.OperationType == "delete" {
		if len(request.Conditions) == 1 {
			// 检查是否使用了可能匹配大量记录的条件
			for field, value := range request.Conditions {
				if c.isBroadCondition(field, value) {
					result.RiskLevel = "critical"
					result.RequireConfirm = true
					result.Warnings = append(result.Warnings, "条件可能匹配大量记录，请确认操作范围")
				}
			}
		}
	}

	return nil
}

// applySecurityRules 应用安全规则
func (c *DatabaseSecurityChecker) applySecurityRules(request *DatabaseOperationRequest, result *SecurityCheckResult) {
	// 应用表级规则
	if rule, exists := c.rules.TableRules[request.TableName]; exists {
		if !rule.AllowDelete && request.OperationType == "delete" {
			result.Allowed = false
			result.Reason = fmt.Sprintf("表 %s 不允许删除操作", request.TableName)
			return
		}

		if !rule.AllowUpdate && request.OperationType == "update" {
			result.Allowed = false
			result.Reason = fmt.Sprintf("表 %s 不允许更新操作", request.TableName)
			return
		}
	}

	// 应用全局规则
	if c.rules.Global.RequireConfirmForAll {
		result.RequireConfirm = true
	}

	if c.rules.Global.MaxRecordsPerOperation > 0 {
		// 这里可以添加记录数限制检查
	}
}

// 辅助方法
func (c *DatabaseSecurityChecker) containsSQLInjection(value string) bool {
	// 更智能的SQL注入检测，避免误报
	lowerValue := strings.ToLower(value)

	// 检查明显的SQL注入模式
	dangerousPatterns := []string{
		"'; drop", "'; delete", "'; update", "'; insert",
		"union select", "union all select",
		"-- ", "/*", "*/",
		"xp_cmdshell", "sp_executesql",
	}

	for _, pattern := range dangerousPatterns {
		if strings.Contains(lowerValue, pattern) {
			return true
		}
	}

	// 检查多个单引号（可能的注入尝试）
	if strings.Count(value, "'") > 1 {
		return true
	}

	// 检查分号后跟SQL关键字
	if strings.Contains(value, ";") {
		parts := strings.Split(lowerValue, ";")
		for i, part := range parts {
			if i > 0 { // 跳过第一部分
				trimmed := strings.TrimSpace(part)
				if strings.HasPrefix(trimmed, "drop") ||
					strings.HasPrefix(trimmed, "delete") ||
					strings.HasPrefix(trimmed, "update") ||
					strings.HasPrefix(trimmed, "insert") ||
					strings.HasPrefix(trimmed, "select") {
					return true
				}
			}
		}
	}

	return false
}

func (c *DatabaseSecurityChecker) isSensitiveField(field string) bool {
	sensitiveFields := []string{"password", "password_hash", "password_encrypted", "ssh_key", "api_key", "token"}
	lowerField := strings.ToLower(field)

	for _, sensitive := range sensitiveFields {
		if strings.Contains(lowerField, sensitive) {
			return true
		}
	}

	return false
}

func (c *DatabaseSecurityChecker) getRequiredFields(tableName string) []string {
	requiredFieldsMap := map[string][]string{
		"hosts":  {"name", "ip_address", "username"},
		"users":  {"username", "password_hash", "email", "full_name"},
		"alerts": {"title", "level", "status"},
	}

	if fields, exists := requiredFieldsMap[tableName]; exists {
		return fields
	}

	return []string{}
}

func (c *DatabaseSecurityChecker) validateFieldValue(tableName, field string, value interface{}) error {
	// 基本的字段值验证
	if value == nil {
		return nil
	}

	valueStr := fmt.Sprintf("%v", value)

	// IP地址验证
	if field == "ip_address" {
		// 简单的IP格式检查
		parts := strings.Split(valueStr, ".")
		if len(parts) != 4 {
			return fmt.Errorf("invalid IP address format")
		}
	}

	// 邮箱验证
	if field == "email" {
		if !strings.Contains(valueStr, "@") {
			return fmt.Errorf("invalid email format")
		}
	}

	return nil
}

func (c *DatabaseSecurityChecker) isBroadCondition(field string, value interface{}) bool {
	// 检查是否是可能匹配大量记录的宽泛条件
	broadFields := []string{"environment", "status", "role", "is_active"}

	for _, broadField := range broadFields {
		if field == broadField {
			return true
		}
	}

	return false
}

// SecurityCheckResult 安全检查结果
type SecurityCheckResult struct {
	Allowed        bool     `json:"allowed"`
	Reason         string   `json:"reason,omitempty"`
	RiskLevel      string   `json:"risk_level"`
	RequireConfirm bool     `json:"require_confirm"`
	Warnings       []string `json:"warnings"`
	Warning        string   `json:"warning,omitempty"`
}

// SecurityRules 安全规则
type SecurityRules struct {
	Global     GlobalSecurityRule           `json:"global"`
	TableRules map[string]TableSecurityRule `json:"table_rules"`
}

// GlobalSecurityRule 全局安全规则
type GlobalSecurityRule struct {
	RequireConfirmForAll     bool `json:"require_confirm_for_all"`
	MaxRecordsPerOperation   int  `json:"max_records_per_operation"`
	AllowDangerousOperations bool `json:"allow_dangerous_operations"`
}

// TableSecurityRule 表级安全规则
type TableSecurityRule struct {
	AllowSelect bool `json:"allow_select"`
	AllowInsert bool `json:"allow_insert"`
	AllowUpdate bool `json:"allow_update"`
	AllowDelete bool `json:"allow_delete"`
}

// NewSecurityRules 创建安全规则
func NewSecurityRules() *SecurityRules {
	return &SecurityRules{
		Global: GlobalSecurityRule{
			RequireConfirmForAll:     false,
			MaxRecordsPerOperation:   100,
			AllowDangerousOperations: true,
		},
		TableRules: map[string]TableSecurityRule{
			"hosts": {
				AllowSelect: true,
				AllowInsert: true,
				AllowUpdate: true,
				AllowDelete: true,
			},
			"users": {
				AllowSelect: true,
				AllowInsert: true,
				AllowUpdate: true,
				AllowDelete: false, // 用户表不允许删除
			},
			"alerts": {
				AllowSelect: true,
				AllowInsert: true,
				AllowUpdate: true,
				AllowDelete: true,
			},
			"operation_logs": {
				AllowSelect: true,
				AllowInsert: true,
				AllowUpdate: false, // 操作日志不允许修改
				AllowDelete: false, // 操作日志不允许删除
			},
		},
	}
}
