package config

import (
	"fmt"
	"os"
	"strconv"
	"strings"
	"time"

	"github.com/spf13/viper"
)

// Config 革命性AI运维平台配置结构
type Config struct {
	App      AppConfig      `mapstructure:"app"`
	Database DatabaseConfig `mapstructure:"database"`
	JWT      JWTConfig      `mapstructure:"jwt"`
	DeepSeek DeepSeekConfig `mapstructure:"deepseek"`
	Security SecurityConfig `mapstructure:"security"`
	Redis    RedisConfig    `mapstructure:"redis"`
	SSH      SSHConfig      `mapstructure:"ssh"`
	Log      LogConfig      `mapstructure:"log"`
	Metrics  MetricsConfig  `mapstructure:"metrics"`
	Cache    CacheConfig    `mapstructure:"cache"`
	Agent    AgentConfig    `mapstructure:"agent"`
	// 革命性AI平台新增配置
	AIModels         AIModelsConfig         `mapstructure:"ai_models"`
	AIBrain          AIBrainConfig          `mapstructure:"ai_brain"`
	IntentEngine     IntentEngineConfig     `mapstructure:"intent_engine"`
	Multimodal       MultimodalConfig       `mapstructure:"multimodal"`
	Execution        ExecutionConfig        `mapstructure:"execution"`
	Knowledge        KnowledgeConfig        `mapstructure:"knowledge"`
	AI               AIConfig               `mapstructure:"ai"`
	Alerting         AlertingConfig         `mapstructure:"alerting"`
	Performance      PerformanceConfig      `mapstructure:"performance"`
	Worker           WorkerConfig           `mapstructure:"worker"`
	Backup           BackupConfig           `mapstructure:"backup"`
	Cors             CorsConfig             `mapstructure:"cors"`
	Health           HealthConfig           `mapstructure:"health"`
	Static           StaticConfig           `mapstructure:"static"`
	AdvancedSecurity AdvancedSecurityConfig `mapstructure:"advanced_security"`
	LoadBalancer     LoadBalancerConfig     `mapstructure:"load_balancer"`
	Distributed      DistributedConfig      `mapstructure:"distributed"`
	Development      DevelopmentConfig      `mapstructure:"development"`
}

// AppConfig 应用基础配置
type AppConfig struct {
	Name    string `mapstructure:"name"`
	Env     string `mapstructure:"env"`
	Debug   bool   `mapstructure:"debug"`
	Port    int    `mapstructure:"port"`
	Version string `mapstructure:"version"`
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Path            string        `mapstructure:"path"`
	MaxOpenConns    int           `mapstructure:"max_open_conns"`
	MaxIdleConns    int           `mapstructure:"max_idle_conns"`
	ConnMaxLifetime time.Duration `mapstructure:"conn_max_lifetime"`
	ConnMaxIdleTime time.Duration `mapstructure:"conn_max_idle_time"`
}

// JWTConfig JWT配置
type JWTConfig struct {
	Secret                string        `mapstructure:"secret"`
	AccessTokenTTL        time.Duration `mapstructure:"access_token_ttl"`
	RefreshTokenTTL       time.Duration `mapstructure:"refresh_token_ttl"`
	Issuer                string        `mapstructure:"issuer"`
	MaxConcurrentSessions int           `mapstructure:"max_concurrent_sessions"`
}

// AIModelConfig AI模型配置
type AIModelConfig struct {
	Name             string        `mapstructure:"name"`
	DisplayName      string        `mapstructure:"display_name"`
	Provider         string        `mapstructure:"provider"`
	APIKey           string        `mapstructure:"api_key"`
	APIURL           string        `mapstructure:"api_url"`
	Timeout          time.Duration `mapstructure:"timeout"`
	MaxRetries       int           `mapstructure:"max_retries"`
	MaxContextTokens int           `mapstructure:"max_context_tokens"`
	Temperature      float64       `mapstructure:"temperature"`
	TopP             float64       `mapstructure:"top_p"`
	Icon             string        `mapstructure:"icon"`
	Description      string        `mapstructure:"description"`
	Enabled          bool          `mapstructure:"enabled"`
}

// AIModelsConfig AI模型配置集合
type AIModelsConfig struct {
	DefaultModel string           `mapstructure:"default_model"`
	Models       []AIModelConfig  `mapstructure:"models"`
}

// DeepSeekConfig DeepSeek API配置 (保持向后兼容)
type DeepSeekConfig struct {
	APIKey           string        `mapstructure:"api_key"`
	APIURL           string        `mapstructure:"api_url"`
	Model            string        `mapstructure:"model"`
	Timeout          time.Duration `mapstructure:"timeout"`
	MaxRetries       int           `mapstructure:"max_retries"`
	MaxContextTokens int           `mapstructure:"max_context_tokens"`
	Temperature      float64       `mapstructure:"temperature"`
	TopP             float64       `mapstructure:"top_p"`
}

// SecurityConfig 安全配置
type SecurityConfig struct {
	EncryptionKey    string          `mapstructure:"encryption_key"`
	PasswordHashCost int             `mapstructure:"password_hash_cost"`
	SessionTimeout   time.Duration   `mapstructure:"session_timeout"`
	RateLimit        RateLimitConfig `mapstructure:"rate_limit"`
}

// RateLimitConfig 限流配置
type RateLimitConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Global  string `mapstructure:"global"`
	PerUser string `mapstructure:"per_user"`
	PerIP   string `mapstructure:"per_ip"`
}

// RedisConfig Redis配置
type RedisConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	Host     string `mapstructure:"host"`
	Port     int    `mapstructure:"port"`
	Password string `mapstructure:"password"`
	DB       int    `mapstructure:"db"`
	PoolSize int    `mapstructure:"pool_size"`
}

// SSHConfig SSH配置
type SSHConfig struct {
	Timeout             time.Duration `mapstructure:"timeout"`
	MaxConnections      int           `mapstructure:"max_connections"`
	IdleTimeout         time.Duration `mapstructure:"idle_timeout"`
	HealthCheckInterval time.Duration `mapstructure:"health_check_interval"`
}

// LogConfig 日志配置
type LogConfig struct {
	Level         string `mapstructure:"level"`
	File          string `mapstructure:"file"`
	MaxSize       int    `mapstructure:"max_size"`
	RetentionDays int    `mapstructure:"retention_days"`
	Format        string `mapstructure:"format"`
}

// MetricsConfig 监控配置
type MetricsConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Port    int    `mapstructure:"port"`
	Path    string `mapstructure:"path"`
}

// CacheConfig 缓存配置
type CacheConfig struct {
	Enabled bool          `mapstructure:"enabled"`
	L1Size  string        `mapstructure:"l1_size"`
	L1TTL   time.Duration `mapstructure:"l1_ttl"`
	L2TTL   time.Duration `mapstructure:"l2_ttl"`
}

// AgentConfig Agent平台配置
type AgentConfig struct {
	Enabled                bool          `mapstructure:"enabled"`                  // 是否启用Agent平台
	MaxAgents              int           `mapstructure:"max_agents"`               // 最大Agent数量
	MaxConcurrentRequests  int           `mapstructure:"max_concurrent_requests"`  // 最大并发请求数
	HealthCheckInterval    time.Duration `mapstructure:"health_check_interval"`    // 健康检查间隔
	RegistrationTTL        time.Duration `mapstructure:"registration_ttl"`         // 注册TTL
	CleanupInterval        time.Duration `mapstructure:"cleanup_interval"`         // 清理间隔
	EnableAutoRegistration bool          `mapstructure:"enable_auto_registration"` // 是否启用自动注册
}

// AIBrainConfig AI大脑配置
type AIBrainConfig struct {
	Enabled           bool                 `mapstructure:"enabled"`
	Name              string               `mapstructure:"name"`
	Version           string               `mapstructure:"version"`
	MaxConcurrentOps  int                  `mapstructure:"max_concurrent_ops"`
	ResponseTimeout   time.Duration        `mapstructure:"response_timeout"`
	LearningEnabled   bool                 `mapstructure:"learning_enabled"`
	PredictionEnabled bool                 `mapstructure:"prediction_enabled"`
	KnowledgeGraph    KnowledgeGraphConfig `mapstructure:"knowledge_graph"`
	ExpertSystem      ExpertSystemConfig   `mapstructure:"expert_system"`
	DecisionEngine    DecisionEngineConfig `mapstructure:"decision_engine"`
	LearningEngine    LearningEngineConfig `mapstructure:"learning_engine"`
}

// KnowledgeGraphConfig 知识图谱配置
type KnowledgeGraphConfig struct {
	Enabled             bool `mapstructure:"enabled"`
	MaxNodes            int  `mapstructure:"max_nodes"`
	MaxEdges            int  `mapstructure:"max_edges"`
	CacheSize           int  `mapstructure:"cache_size"`
	PersistenceEnabled  bool `mapstructure:"persistence_enabled"`
	AutoLearningEnabled bool `mapstructure:"auto_learning_enabled"`
}

// ExpertSystemConfig 专家系统配置
type ExpertSystemConfig struct {
	Enabled   bool `mapstructure:"enabled"`
	RuleCount int  `mapstructure:"rule_count"`
}

// DecisionEngineConfig 决策引擎配置
type DecisionEngineConfig struct {
	Enabled             bool    `mapstructure:"enabled"`
	ConfidenceThreshold float64 `mapstructure:"confidence_threshold"`
}

// LearningEngineConfig 学习引擎配置
type LearningEngineConfig struct {
	Enabled    bool          `mapstructure:"enabled"`
	ModelType  string        `mapstructure:"model_type"`
	UpdateFreq time.Duration `mapstructure:"update_freq"`
}

// IntentEngineConfig 意图引擎配置
type IntentEngineConfig struct {
	Enabled               bool          `mapstructure:"enabled"`
	Name                  string        `mapstructure:"name"`
	Version               string        `mapstructure:"version"`
	MaxConcurrentRequests int           `mapstructure:"max_concurrent_requests"`
	ProcessingTimeout     time.Duration `mapstructure:"processing_timeout"`
	ConfidenceThreshold   float64       `mapstructure:"confidence_threshold"`
	MultimodalEnabled     bool          `mapstructure:"multimodal_enabled"`
	ContextWindowSize     int           `mapstructure:"context_window_size"`
	IntentChainMaxDepth   int           `mapstructure:"intent_chain_max_depth"`
	SelfLearningEnabled   bool          `mapstructure:"self_learning_enabled"`
	CacheEnabled          bool          `mapstructure:"cache_enabled"`
}

// MultimodalConfig 多模态配置
type MultimodalConfig struct {
	Enabled          bool                   `mapstructure:"enabled"`
	TextProcessor    TextProcessorConfig    `mapstructure:"text_processor"`
	VoiceProcessor   VoiceProcessorConfig   `mapstructure:"voice_processor"`
	ImageProcessor   ImageProcessorConfig   `mapstructure:"image_processor"`
	GestureProcessor GestureProcessorConfig `mapstructure:"gesture_processor"`
}

// TextProcessorConfig 文本处理器配置
type TextProcessorConfig struct {
	MaxLength                int    `mapstructure:"max_length"`
	Language                 string `mapstructure:"language"`
	NormalizationEnabled     bool   `mapstructure:"normalization_enabled"`
	SentimentAnalysisEnabled bool   `mapstructure:"sentiment_analysis_enabled"`
}

// VoiceProcessorConfig 语音处理器配置
type VoiceProcessorConfig struct {
	SampleRate            int    `mapstructure:"sample_rate"`
	AudioFormat           string `mapstructure:"audio_format"`
	Language              string `mapstructure:"language"`
	NoiseReductionEnabled bool   `mapstructure:"noise_reduction_enabled"`
}

// ImageProcessorConfig 图像处理器配置
type ImageProcessorConfig struct {
	MaxResolution          string   `mapstructure:"max_resolution"`
	SupportedFormats       []string `mapstructure:"supported_formats"`
	OCREnabled             bool     `mapstructure:"ocr_enabled"`
	ObjectDetectionEnabled bool     `mapstructure:"object_detection_enabled"`
}

// GestureProcessorConfig 手势处理器配置
type GestureProcessorConfig struct {
	SupportedGestures []string `mapstructure:"supported_gestures"`
	Sensitivity       float64  `mapstructure:"sensitivity"`
	TrackingPoints    int      `mapstructure:"tracking_points"`
}

// ExecutionConfig 执行引擎配置
type ExecutionConfig struct {
	Enabled               bool          `mapstructure:"enabled"`
	MaxConcurrentTasks    int           `mapstructure:"max_concurrent_tasks"`
	TaskTimeout           time.Duration `mapstructure:"task_timeout"`
	RetryAttempts         int           `mapstructure:"retry_attempts"`
	HealthCheckInterval   time.Duration `mapstructure:"health_check_interval"`
	AutoScalingEnabled    bool          `mapstructure:"auto_scaling_enabled"`
	LoadBalancingEnabled  bool          `mapstructure:"load_balancing_enabled"`
	FaultToleranceEnabled bool          `mapstructure:"fault_tolerance_enabled"`
}

// KnowledgeConfig 知识库配置
type KnowledgeConfig struct {
	Enabled           bool   `mapstructure:"enabled"`
	DatabasePath      string `mapstructure:"database_path"`
	VectorDimension   int    `mapstructure:"vector_dimension"`
	IndexType         string `mapstructure:"index_type"`
	SimilarityMetric  string `mapstructure:"similarity_metric"`
	CacheSize         int    `mapstructure:"cache_size"`
	AutoUpdateEnabled bool   `mapstructure:"auto_update_enabled"`
}

// AIConfig AI智能配置
type AIConfig struct {
	EnableIntelligentMode bool                  `mapstructure:"enable_intelligent_mode"`
	DeepSeek              AIDeepSeekConfig      `mapstructure:"deepseek"`
	IntelligentAgent      IntelligentAgentConfig `mapstructure:"intelligent_agent"`
}

// AIDeepSeekConfig AI DeepSeek配置
type AIDeepSeekConfig struct {
	APIKey      string  `mapstructure:"api_key"`
	BaseURL     string  `mapstructure:"base_url"`
	Model       string  `mapstructure:"model"`
	MaxTokens   int     `mapstructure:"max_tokens"`
	Temperature float64 `mapstructure:"temperature"`
}

// IntelligentAgentConfig 智能Agent配置
type IntelligentAgentConfig struct {
	EnableAutoExecution   bool          `mapstructure:"enable_auto_execution"`
	ConfidenceThreshold   float64       `mapstructure:"confidence_threshold"`
	MaxConcurrentTasks    int           `mapstructure:"max_concurrent_tasks"`
	DefaultTimeout        time.Duration `mapstructure:"default_timeout"`
	EnableFallback        bool          `mapstructure:"enable_fallback"`
}

// AlertingConfig 告警配置
type AlertingConfig struct {
	Enabled       bool               `mapstructure:"enabled"`
	RetentionDays int                `mapstructure:"retention_days"`
	Email         EmailConfig        `mapstructure:"email"`
	DingTalk      DingTalkConfig     `mapstructure:"dingtalk"`
}

// EmailConfig 邮件配置
type EmailConfig struct {
	Enabled  bool   `mapstructure:"enabled"`
	SMTPHost string `mapstructure:"smtp_host"`
	SMTPPort int    `mapstructure:"smtp_port"`
	Username string `mapstructure:"username"`
	Password string `mapstructure:"password"`
	From     string `mapstructure:"from"`
}

// DingTalkConfig 钉钉配置
type DingTalkConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Webhook string `mapstructure:"webhook"`
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	Enabled               bool          `mapstructure:"enabled"`
	CollectionInterval    time.Duration `mapstructure:"collection_interval"`
	EnableSystemMetrics   bool          `mapstructure:"enable_system_metrics"`
	EnableBusinessMetrics bool          `mapstructure:"enable_business_metrics"`
	EnableAlerting        bool          `mapstructure:"enable_alerting"`
	MetricsRetention      time.Duration `mapstructure:"metrics_retention"`
	AlertCooldown         time.Duration `mapstructure:"alert_cooldown"`
}

// WorkerConfig 工作池配置
type WorkerConfig struct {
	PoolSize  int `mapstructure:"pool_size"`
	QueueSize int `mapstructure:"queue_size"`
}

// BackupConfig 备份配置
type BackupConfig struct {
	Enabled       bool          `mapstructure:"enabled"`
	Interval      time.Duration `mapstructure:"interval"`
	RetentionDays int           `mapstructure:"retention_days"`
	StoragePath   string        `mapstructure:"storage_path"`
	Compression   bool          `mapstructure:"compression"`
	Encryption    bool          `mapstructure:"encryption"`
}

// CorsConfig 跨域配置
type CorsConfig struct {
	Enabled bool   `mapstructure:"enabled"`
	Origins string `mapstructure:"origins"`
}

// HealthConfig 健康检查配置
type HealthConfig struct {
	CheckPath               string        `mapstructure:"check_path"`
	GracefulShutdownTimeout time.Duration `mapstructure:"graceful_shutdown_timeout"`
}

// StaticConfig 静态文件配置
type StaticConfig struct {
	Dir         string `mapstructure:"dir"`
	TemplateDir string `mapstructure:"template_dir"`
}

// AdvancedSecurityConfig 高级安全配置
type AdvancedSecurityConfig struct {
	EnableThreatDetection bool `mapstructure:"enable_threat_detection"`
	EnableAuditLog        bool `mapstructure:"enable_audit_log"`
	MaxRequestsPerMinute  int  `mapstructure:"max_requests_per_minute"`
	PasswordMinLength     int  `mapstructure:"password_min_length"`
	RequireMFA            bool `mapstructure:"require_mfa"`
}

// LoadBalancerConfig 负载均衡配置
type LoadBalancerConfig struct {
	Enabled             bool          `mapstructure:"enabled"`
	DefaultStrategy     string        `mapstructure:"default_strategy"`
	HealthCheckInterval time.Duration `mapstructure:"health_check_interval"`
	HealthCheckTimeout  time.Duration `mapstructure:"health_check_timeout"`
	MaxRetries          int           `mapstructure:"max_retries"`
	RetryInterval       time.Duration `mapstructure:"retry_interval"`
	EnableMetrics       bool          `mapstructure:"enable_metrics"`
	EnableAutoScaling   bool          `mapstructure:"enable_auto_scaling"`
	LoadThreshold       float64       `mapstructure:"load_threshold"`
}

// DistributedConfig 分布式配置
type DistributedConfig struct {
	LockEnabled             bool   `mapstructure:"lock_enabled"`
	LockRedisHost           string `mapstructure:"lock_redis_host"`
	LockRedisPort           int    `mapstructure:"lock_redis_port"`
	TracingEnabled          bool   `mapstructure:"tracing_enabled"`
	JaegerEndpoint          string `mapstructure:"jaeger_endpoint"`
	ServiceDiscoveryEnabled bool   `mapstructure:"service_discovery_enabled"`
	ConsulHost              string `mapstructure:"consul_host"`
	ConsulPort              int    `mapstructure:"consul_port"`
}

// DevelopmentConfig 开发配置
type DevelopmentConfig struct {
	HotReload        bool `mapstructure:"hot_reload"`
	ProfilingEnabled bool `mapstructure:"profiling_enabled"`
	DebugMode        bool `mapstructure:"debug_mode"`
}

// Load 加载配置 - 统一配置管理系统
// 优先级：环境变量 > 配置文件 > 默认值
func Load() (*Config, error) {
	fmt.Println("🔧 AI运维管理平台 - 统一配置加载系统")
	fmt.Println("📋 配置优先级：环境变量 > 配置文件 > 默认值")

	// 映射旧的环境变量名到新的名称（向后兼容）
	mapLegacyEnvVars()

	// 先设置默认值
	setDefaults()

	// 配置文件设置
	viper.SetConfigName("config")
	viper.SetConfigType("yaml")
	viper.AddConfigPath("./configs")
	viper.AddConfigPath(".")

	// 环境变量设置 - 确保环境变量优先级最高
	viper.SetEnvPrefix("AIOPS")
	viper.AutomaticEnv()
	viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 读取配置文件
	if err := viper.ReadInConfig(); err != nil {
		if _, ok := err.(viper.ConfigFileNotFoundError); ok {
			fmt.Printf("⚠️  配置文件未找到，使用默认值和环境变量: %v\n", err)
		} else {
			return nil, fmt.Errorf("failed to read config file: %w", err)
		}
	} else {
		fmt.Printf("✅ 配置文件加载成功: %s\n", viper.ConfigFileUsed())
	}

	// 显示配置加载信息
	fmt.Printf("🔍 应用端口: %v (来源: %s)\n", viper.Get("app.port"), getConfigSource("app.port"))
	fmt.Printf("🔍 应用环境: %v (来源: %s)\n", viper.Get("app.env"), getConfigSource("app.env"))
	fmt.Printf("🔍 应用名称: %v (来源: %s)\n", viper.Get("app.name"), getConfigSource("app.name"))

	var config Config
	if err := viper.Unmarshal(&config); err != nil {
		return nil, fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 手动处理环境变量覆盖
	applyEnvironmentOverrides(&config)

	// 验证配置
	if err := validateConfig(&config); err != nil {
		return nil, fmt.Errorf("config validation failed: %w", err)
	}

	fmt.Println("🎉 配置加载完成！")
	return &config, nil
}

// applyEnvironmentOverrides 应用环境变量覆盖
func applyEnvironmentOverrides(config *Config) {
	fmt.Println("🔧 应用环境变量覆盖...")

	// DeepSeek API密钥环境变量覆盖
	if apiKey := os.Getenv("AIOPS_DEEPSEEK_API_KEY"); apiKey != "" {
		config.DeepSeek.APIKey = apiKey
		fmt.Printf("✅ DeepSeek API密钥已通过环境变量覆盖\n")
	} else {
		fmt.Printf("📋 DeepSeek API密钥使用配置文件值\n")
	}

	// JWT密钥环境变量覆盖
	if jwtSecret := os.Getenv("AIOPS_JWT_SECRET"); jwtSecret != "" {
		config.JWT.Secret = jwtSecret
		fmt.Printf("✅ JWT密钥已通过环境变量覆盖\n")
	} else {
		fmt.Printf("📋 JWT密钥使用配置文件值\n")
	}

	// 加密密钥环境变量覆盖
	if encKey := os.Getenv("AIOPS_ENCRYPTION_KEY"); encKey != "" {
		config.Security.EncryptionKey = encKey
		fmt.Printf("✅ 加密密钥已通过环境变量覆盖\n")
	} else {
		fmt.Printf("📋 加密密钥使用配置文件值\n")
	}

	// 应用端口环境变量覆盖
	if port := os.Getenv("AIOPS_APP_PORT"); port != "" {
		if portInt, err := strconv.Atoi(port); err == nil {
			config.App.Port = portInt
			fmt.Printf("✅ 应用端口已通过环境变量覆盖: %d\n", portInt)
		}
	}

	// 应用环境环境变量覆盖
	if env := os.Getenv("AIOPS_APP_ENV"); env != "" {
		config.App.Env = env
		fmt.Printf("✅ 应用环境已通过环境变量覆盖: %s\n", env)
	}

	// 调试模式环境变量覆盖
	if debug := os.Getenv("AIOPS_APP_DEBUG"); debug != "" {
		if debugBool, err := strconv.ParseBool(debug); err == nil {
			config.App.Debug = debugBool
			fmt.Printf("✅ 调试模式已通过环境变量覆盖: %v\n", debugBool)
		}
	}
}

// getConfigSource 获取配置项的来源
func getConfigSource(key string) string {
	envKey := strings.ToUpper(strings.ReplaceAll("AIOPS_"+key, ".", "_"))
	if os.Getenv(envKey) != "" {
		return "环境变量"
	}
	if viper.InConfig(key) {
		return "配置文件"
	}
	return "默认值"
}

// setDefaults 设置默认配置值 - 统一配置系统
func setDefaults() {
	fmt.Println("🔧 设置默认配置值...")

	// App defaults
	viper.SetDefault("app.name", "aiops-platform")
	viper.SetDefault("app.env", "development")
	viper.SetDefault("app.debug", true)
	viper.SetDefault("app.port", 8080)  // 统一使用8080端口
	viper.SetDefault("app.version", "3.0.0")

	// Database defaults
	viper.SetDefault("database.path", "./data/aiops.db")
	viper.SetDefault("database.max_open_conns", 25)
	viper.SetDefault("database.max_idle_conns", 5)
	viper.SetDefault("database.conn_max_lifetime", "1h")
	viper.SetDefault("database.conn_max_idle_time", "10m")

	// JWT defaults
	viper.SetDefault("jwt.secret", "your-secret-key")
	viper.SetDefault("jwt.access_token_ttl", "15m")
	viper.SetDefault("jwt.refresh_token_ttl", "7d")
	viper.SetDefault("jwt.issuer", "aiops-platform")
	viper.SetDefault("jwt.max_concurrent_sessions", 5)

	// DeepSeek defaults
	viper.SetDefault("deepseek.api_url", "https://api.deepseek.com")
	viper.SetDefault("deepseek.model", "deepseek-chat")
	viper.SetDefault("deepseek.timeout", "30s")
	viper.SetDefault("deepseek.max_retries", 3)
	viper.SetDefault("deepseek.max_context_tokens", 4000)
	viper.SetDefault("deepseek.temperature", 0.7)
	viper.SetDefault("deepseek.top_p", 0.9)

	// Security defaults
	viper.SetDefault("security.encryption_key", "dev-encryption-key-32bytes-long!")
	viper.SetDefault("security.password_hash_cost", 12)
	viper.SetDefault("security.session_timeout", "24h")
	viper.SetDefault("security.rate_limit.enabled", true)
	viper.SetDefault("security.rate_limit.global", "1000/min")
	viper.SetDefault("security.rate_limit.per_user", "100/min")
	viper.SetDefault("security.rate_limit.per_ip", "200/min")

	// Redis defaults
	viper.SetDefault("redis.enabled", false)
	viper.SetDefault("redis.host", "localhost")
	viper.SetDefault("redis.port", 6379)
	viper.SetDefault("redis.db", 0)
	viper.SetDefault("redis.pool_size", 10)

	// SSH defaults
	viper.SetDefault("ssh.timeout", "30s")
	viper.SetDefault("ssh.max_connections", 10)
	viper.SetDefault("ssh.idle_timeout", "5m")
	viper.SetDefault("ssh.health_check_interval", "1m")

	// Log defaults
	viper.SetDefault("log.level", "info")
	viper.SetDefault("log.file", "./logs/aiops.log")
	viper.SetDefault("log.max_size", 100)
	viper.SetDefault("log.retention_days", 30)
	viper.SetDefault("log.format", "json")

	// Metrics defaults
	viper.SetDefault("metrics.enabled", true)
	viper.SetDefault("metrics.port", 9090)
	viper.SetDefault("metrics.path", "/metrics")

	// Cache defaults
	viper.SetDefault("cache.enabled", true)
	viper.SetDefault("cache.l1_size", "100MB")
	viper.SetDefault("cache.l1_ttl", "5m")
	viper.SetDefault("cache.l2_ttl", "1h")

	// Agent defaults
	viper.SetDefault("agent.enabled", false)
	viper.SetDefault("agent.max_agents", 50)
	viper.SetDefault("agent.max_concurrent_requests", 10)
	viper.SetDefault("agent.health_check_interval", "30s")
	viper.SetDefault("agent.registration_ttl", "1h")
	viper.SetDefault("agent.cleanup_interval", "10m")
	viper.SetDefault("agent.enable_auto_registration", true)

	// AI Brain defaults
	viper.SetDefault("ai_brain.enabled", true)
	viper.SetDefault("ai_brain.name", "Revolutionary-AI-Brain")
	viper.SetDefault("ai_brain.version", "2.0.0")
	viper.SetDefault("ai_brain.max_concurrent_ops", 1000)
	viper.SetDefault("ai_brain.response_timeout", "30s")
	viper.SetDefault("ai_brain.learning_enabled", true)
	viper.SetDefault("ai_brain.prediction_enabled", true)

	// Knowledge Graph defaults
	viper.SetDefault("ai_brain.knowledge_graph.enabled", true)
	viper.SetDefault("ai_brain.knowledge_graph.max_nodes", 100000)
	viper.SetDefault("ai_brain.knowledge_graph.max_edges", 1000000)
	viper.SetDefault("ai_brain.knowledge_graph.cache_size", 10000)
	viper.SetDefault("ai_brain.knowledge_graph.persistence_enabled", true)
	viper.SetDefault("ai_brain.knowledge_graph.auto_learning_enabled", true)

	// Expert System defaults
	viper.SetDefault("ai_brain.expert_system.enabled", true)
	viper.SetDefault("ai_brain.expert_system.rule_count", 1000)

	// Decision Engine defaults
	viper.SetDefault("ai_brain.decision_engine.enabled", true)
	viper.SetDefault("ai_brain.decision_engine.confidence_threshold", 0.8)

	// Learning Engine defaults
	viper.SetDefault("ai_brain.learning_engine.enabled", true)
	viper.SetDefault("ai_brain.learning_engine.model_type", "ensemble")
	viper.SetDefault("ai_brain.learning_engine.update_freq", "1h")

	// Intent Engine defaults
	viper.SetDefault("intent_engine.enabled", true)
	viper.SetDefault("intent_engine.name", "Revolutionary-Intent-Engine")
	viper.SetDefault("intent_engine.version", "2.0.0")
	viper.SetDefault("intent_engine.max_concurrent_requests", 1000)
	viper.SetDefault("intent_engine.processing_timeout", "10s")
	viper.SetDefault("intent_engine.confidence_threshold", 0.7)
	viper.SetDefault("intent_engine.multimodal_enabled", true)
	viper.SetDefault("intent_engine.context_window_size", 20)
	viper.SetDefault("intent_engine.intent_chain_max_depth", 10)
	viper.SetDefault("intent_engine.self_learning_enabled", true)
	viper.SetDefault("intent_engine.cache_enabled", true)

	// Multimodal defaults
	viper.SetDefault("multimodal.enabled", true)

	// Text Processor defaults
	viper.SetDefault("multimodal.text_processor.max_length", 10000)
	viper.SetDefault("multimodal.text_processor.language", "zh-CN")
	viper.SetDefault("multimodal.text_processor.normalization_enabled", true)
	viper.SetDefault("multimodal.text_processor.sentiment_analysis_enabled", true)

	// Voice Processor defaults
	viper.SetDefault("multimodal.voice_processor.sample_rate", 16000)
	viper.SetDefault("multimodal.voice_processor.audio_format", "wav")
	viper.SetDefault("multimodal.voice_processor.language", "zh-CN")
	viper.SetDefault("multimodal.voice_processor.noise_reduction_enabled", true)

	// Image Processor defaults
	viper.SetDefault("multimodal.image_processor.max_resolution", "1920x1080")
	viper.SetDefault("multimodal.image_processor.supported_formats", []string{"jpg", "png", "bmp", "webp"})
	viper.SetDefault("multimodal.image_processor.ocr_enabled", true)
	viper.SetDefault("multimodal.image_processor.object_detection_enabled", true)

	// Gesture Processor defaults
	viper.SetDefault("multimodal.gesture_processor.supported_gestures", []string{"swipe", "tap", "pinch", "rotate"})
	viper.SetDefault("multimodal.gesture_processor.sensitivity", 0.8)
	viper.SetDefault("multimodal.gesture_processor.tracking_points", 20)

	// Execution Engine defaults
	viper.SetDefault("execution.enabled", true)
	viper.SetDefault("execution.max_concurrent_tasks", 100)
	viper.SetDefault("execution.task_timeout", "5m")
	viper.SetDefault("execution.retry_attempts", 3)
	viper.SetDefault("execution.health_check_interval", "30s")
	viper.SetDefault("execution.auto_scaling_enabled", true)
	viper.SetDefault("execution.load_balancing_enabled", true)
	viper.SetDefault("execution.fault_tolerance_enabled", true)

	// Knowledge defaults
	viper.SetDefault("knowledge.enabled", true)
	viper.SetDefault("knowledge.database_path", "./data/knowledge.db")
	viper.SetDefault("knowledge.vector_dimension", 768)
	viper.SetDefault("knowledge.index_type", "hnsw")
	viper.SetDefault("knowledge.similarity_metric", "cosine")
	viper.SetDefault("knowledge.cache_size", 10000)
	viper.SetDefault("knowledge.auto_update_enabled", true)
}

// validateConfig 验证配置 - 统一配置验证系统
func validateConfig(config *Config) error {
	fmt.Println("🔍 验证配置...")

	// 验证DeepSeek API密钥 - 支持环境变量优先，配置文件后备
	if config.DeepSeek.APIKey == "" || config.DeepSeek.APIKey == "${AIOPS_DEEPSEEK_API_KEY}" {
		fmt.Println("⚠️  DeepSeek API密钥未通过环境变量设置，检查配置文件...")
		return fmt.Errorf("deepseek.api_key is required, please set AIOPS_DEEPSEEK_API_KEY environment variable or configure in config.yaml")
	}
	fmt.Printf("✅ DeepSeek API密钥已配置 (长度: %d)\n", len(config.DeepSeek.APIKey))

	// 验证JWT密钥 - 支持环境变量优先，配置文件后备
	if config.JWT.Secret == "" || config.JWT.Secret == "your-secret-key" || config.JWT.Secret == "${AIOPS_JWT_SECRET}" {
		fmt.Println("⚠️  JWT密钥未通过环境变量设置，检查配置文件...")
		return fmt.Errorf("jwt.secret must be set to a secure value, please set AIOPS_JWT_SECRET environment variable or configure in config.yaml")
	}
	fmt.Printf("✅ JWT密钥已配置 (长度: %d)\n", len(config.JWT.Secret))

	// 验证加密密钥 - 支持环境变量优先，配置文件后备
	if config.Security.EncryptionKey == "" || config.Security.EncryptionKey == "${AIOPS_ENCRYPTION_KEY}" {
		fmt.Println("⚠️  加密密钥未通过环境变量设置，使用配置文件默认值")
		// 这种情况下配置文件应该已经有默认值了
		if config.Security.EncryptionKey == "" {
			config.Security.EncryptionKey = "aiops-dev-encryption-key-32bytes"
		}
	}

	if len(config.Security.EncryptionKey) != 32 {
		return fmt.Errorf("security.encryption_key must be exactly 32 bytes, current length: %d", len(config.Security.EncryptionKey))
	}
	fmt.Printf("✅ 加密密钥已配置 (长度: %d字节)\n", len(config.Security.EncryptionKey))

	fmt.Println("✅ 配置验证通过")
	return nil
}

// mapLegacyEnvVars 映射旧的环境变量名到新的名称
func mapLegacyEnvVars() {
	// 映射旧的环境变量名到新的AIOPS_前缀名称
	envMappings := map[string]string{
		"DEEPSEEK_API_KEY": "AIOPS_DEEPSEEK_API_KEY",
		"JWT_SECRET":       "AIOPS_JWT_SECRET",
		"ENCRYPTION_KEY":   "AIOPS_ENCRYPTION_KEY",
	}

	for oldName, newName := range envMappings {
		if oldValue := os.Getenv(oldName); oldValue != "" && os.Getenv(newName) == "" {
			os.Setenv(newName, oldValue)
		}
	}
}
