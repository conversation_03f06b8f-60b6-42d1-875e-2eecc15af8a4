package logger

import (
	"context"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"runtime"
	"strings"
	"sync"
	"time"

	"aiops-platform/internal/config"

	"github.com/sirupsen/logrus"
	"gopkg.in/natefinch/lumberjack.v2"
)

// New 创建新的日志实例
func New(cfg config.LogConfig) *logrus.Logger {
	logger := logrus.New()

	// 设置日志级别
	level, err := logrus.ParseLevel(cfg.Level)
	if err != nil {
		level = logrus.InfoLevel
	}
	logger.SetLevel(level)

	// 设置日志格式
	if cfg.Format == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: "2006-01-02 15:04:05",
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: "2006-01-02 15:04:05",
		})
	}

	// 设置输出
	var writers []io.Writer

	// 控制台输出
	writers = append(writers, os.Stdout)

	// 文件输出
	if cfg.File != "" {
		// 确保日志目录存在
		logDir := filepath.Dir(cfg.File)
		if err := os.MkdirAll(logDir, 0755); err != nil {
			logger.Errorf("Failed to create log directory: %v", err)
		} else {
			// 使用lumberjack进行日志轮转
			fileWriter := &lumberjack.Logger{
				Filename:   cfg.File,
				MaxSize:    cfg.MaxSize, // MB
				MaxBackups: 3,
				MaxAge:     cfg.RetentionDays, // days
				Compress:   true,
			}
			writers = append(writers, fileWriter)
		}
	}

	// 设置多输出
	if len(writers) > 1 {
		logger.SetOutput(io.MultiWriter(writers...))
	} else if len(writers) == 1 {
		logger.SetOutput(writers[0])
	}

	return logger
}

// WithContext 创建带上下文的日志条目
func WithContext(logger *logrus.Logger, fields map[string]interface{}) *logrus.Entry {
	return logger.WithFields(logrus.Fields(fields))
}

// WithRequest 创建带请求信息的日志条目
func WithRequest(logger *logrus.Logger, method, path, userAgent, clientIP string) *logrus.Entry {
	return logger.WithFields(logrus.Fields{
		"method":     method,
		"path":       path,
		"user_agent": userAgent,
		"client_ip":  clientIP,
	})
}

// WithUser 创建带用户信息的日志条目
func WithUser(logger *logrus.Logger, userID int64, username string) *logrus.Entry {
	return logger.WithFields(logrus.Fields{
		"user_id":  userID,
		"username": username,
	})
}

// WithError 创建带错误信息的日志条目
func WithError(logger *logrus.Logger, err error) *logrus.Entry {
	return logger.WithError(err)
}

// LogLevel 日志级别枚举
type LogLevel string

const (
	DebugLevel LogLevel = "debug"
	InfoLevel  LogLevel = "info"
	WarnLevel  LogLevel = "warn"
	ErrorLevel LogLevel = "error"
	FatalLevel LogLevel = "fatal"
)

// ParseLevel 解析日志级别
func ParseLevel(level string) LogLevel {
	switch strings.ToLower(level) {
	case "debug":
		return DebugLevel
	case "info":
		return InfoLevel
	case "warn", "warning":
		return WarnLevel
	case "error":
		return ErrorLevel
	case "fatal":
		return FatalLevel
	default:
		return InfoLevel
	}
}

// StructuredLogger 结构化日志接口
type StructuredLogger interface {
	Debug(args ...interface{})
	Debugf(format string, args ...interface{})
	Info(args ...interface{})
	Infof(format string, args ...interface{})
	Warn(args ...interface{})
	Warnf(format string, args ...interface{})
	Error(args ...interface{})
	Errorf(format string, args ...interface{})
	Fatal(args ...interface{})
	Fatalf(format string, args ...interface{})
	WithFields(fields map[string]interface{}) StructuredLogger
	WithError(err error) StructuredLogger
}

// LogrusAdapter logrus适配器
type LogrusAdapter struct {
	entry *logrus.Entry
}

// NewLogrusAdapter 创建logrus适配器
func NewLogrusAdapter(logger *logrus.Logger) StructuredLogger {
	return &LogrusAdapter{
		entry: logrus.NewEntry(logger),
	}
}

func (l *LogrusAdapter) Debug(args ...interface{}) {
	l.entry.Debug(args...)
}

func (l *LogrusAdapter) Debugf(format string, args ...interface{}) {
	l.entry.Debugf(format, args...)
}

func (l *LogrusAdapter) Info(args ...interface{}) {
	l.entry.Info(args...)
}

func (l *LogrusAdapter) Infof(format string, args ...interface{}) {
	l.entry.Infof(format, args...)
}

func (l *LogrusAdapter) Warn(args ...interface{}) {
	l.entry.Warn(args...)
}

func (l *LogrusAdapter) Warnf(format string, args ...interface{}) {
	l.entry.Warnf(format, args...)
}

func (l *LogrusAdapter) Error(args ...interface{}) {
	l.entry.Error(args...)
}

func (l *LogrusAdapter) Errorf(format string, args ...interface{}) {
	l.entry.Errorf(format, args...)
}

func (l *LogrusAdapter) Fatal(args ...interface{}) {
	l.entry.Fatal(args...)
}

func (l *LogrusAdapter) Fatalf(format string, args ...interface{}) {
	l.entry.Fatalf(format, args...)
}

func (l *LogrusAdapter) WithFields(fields map[string]interface{}) StructuredLogger {
	return &LogrusAdapter{
		entry: l.entry.WithFields(logrus.Fields(fields)),
	}
}

func (l *LogrusAdapter) WithError(err error) StructuredLogger {
	return &LogrusAdapter{
		entry: l.entry.WithError(err),
	}
}

// EnhancedLogger 增强的日志器
type EnhancedLogger struct {
	logger    *logrus.Logger
	mu        sync.RWMutex
	startTime time.Time
	hooks     []logrus.Hook
}

// NewEnhancedLogger 创建增强的日志器
func NewEnhancedLogger(cfg config.LogConfig) *EnhancedLogger {
	logger := New(cfg)

	enhanced := &EnhancedLogger{
		logger:    logger,
		startTime: time.Now(),
		hooks:     make([]logrus.Hook, 0),
	}

	// 添加调用者信息钩子
	enhanced.AddHook(&CallerHook{})

	// 添加性能监控钩子
	enhanced.AddHook(&PerformanceHook{})

	return enhanced
}

// AddHook 添加日志钩子
func (e *EnhancedLogger) AddHook(hook logrus.Hook) {
	e.mu.Lock()
	defer e.mu.Unlock()

	e.logger.AddHook(hook)
	e.hooks = append(e.hooks, hook)
}

// WithContext 创建带上下文的日志条目
func (e *EnhancedLogger) WithContext(ctx context.Context) *logrus.Entry {
	entry := e.logger.WithContext(ctx)

	// 从上下文中提取常用字段
	if requestID := ctx.Value("request_id"); requestID != nil {
		entry = entry.WithField("request_id", requestID)
	}

	if traceID := ctx.Value("trace_id"); traceID != nil {
		entry = entry.WithField("trace_id", traceID)
	}

	if userID := ctx.Value("user_id"); userID != nil {
		entry = entry.WithField("user_id", userID)
	}

	return entry
}

// WithRequestID 创建带请求ID的日志条目
func (e *EnhancedLogger) WithRequestID(requestID string) *logrus.Entry {
	return e.logger.WithField("request_id", requestID)
}

// WithTraceID 创建带跟踪ID的日志条目
func (e *EnhancedLogger) WithTraceID(traceID string) *logrus.Entry {
	return e.logger.WithField("trace_id", traceID)
}

// WithOperation 创建带操作信息的日志条目
func (e *EnhancedLogger) WithOperation(operation string) *logrus.Entry {
	return e.logger.WithFields(logrus.Fields{
		"operation": operation,
		"timestamp": time.Now().Unix(),
	})
}

// GetLogger 获取底层logger
func (e *EnhancedLogger) GetLogger() *logrus.Logger {
	return e.logger
}

// CallerHook 调用者信息钩子
type CallerHook struct{}

func (hook *CallerHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

func (hook *CallerHook) Fire(entry *logrus.Entry) error {
	// 获取调用者信息
	if pc, file, line, ok := runtime.Caller(8); ok {
		funcName := runtime.FuncForPC(pc).Name()
		entry.Data["caller"] = fmt.Sprintf("%s:%d", filepath.Base(file), line)
		entry.Data["function"] = filepath.Base(funcName)
	}
	return nil
}

// PerformanceHook 性能监控钩子
type PerformanceHook struct {
	mu        sync.RWMutex
	startTime map[string]time.Time
}

func (hook *PerformanceHook) Levels() []logrus.Level {
	return []logrus.Level{logrus.InfoLevel, logrus.WarnLevel, logrus.ErrorLevel}
}

func (hook *PerformanceHook) Fire(entry *logrus.Entry) error {
	if hook.startTime == nil {
		hook.startTime = make(map[string]time.Time)
	}

	// 如果有操作标识，记录执行时间
	if operation, ok := entry.Data["operation"].(string); ok {
		hook.mu.Lock()
		if startTime, exists := hook.startTime[operation]; exists {
			duration := time.Since(startTime)
			entry.Data["duration"] = duration.String()
			entry.Data["duration_ms"] = duration.Milliseconds()
			delete(hook.startTime, operation)
		} else {
			hook.startTime[operation] = time.Now()
		}
		hook.mu.Unlock()
	}

	return nil
}

// LogMetrics 日志指标
type LogMetrics struct {
	mu          sync.RWMutex
	TotalLogs   int64
	ErrorLogs   int64
	WarnLogs    int64
	InfoLogs    int64
	DebugLogs   int64
	LastLogTime time.Time
}

// MetricsHook 指标收集钩子
type MetricsHook struct {
	metrics *LogMetrics
}

func NewMetricsHook() *MetricsHook {
	return &MetricsHook{
		metrics: &LogMetrics{},
	}
}

func (hook *MetricsHook) Levels() []logrus.Level {
	return logrus.AllLevels
}

func (hook *MetricsHook) Fire(entry *logrus.Entry) error {
	hook.metrics.mu.Lock()
	defer hook.metrics.mu.Unlock()

	hook.metrics.TotalLogs++
	hook.metrics.LastLogTime = time.Now()

	switch entry.Level {
	case logrus.ErrorLevel:
		hook.metrics.ErrorLogs++
	case logrus.WarnLevel:
		hook.metrics.WarnLogs++
	case logrus.InfoLevel:
		hook.metrics.InfoLogs++
	case logrus.DebugLevel:
		hook.metrics.DebugLogs++
	}

	return nil
}

func (hook *MetricsHook) GetMetrics() LogMetrics {
	hook.metrics.mu.RLock()
	defer hook.metrics.mu.RUnlock()
	return *hook.metrics
}
