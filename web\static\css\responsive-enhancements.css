/* ========================================
   响应式布局增强系统
   针对不同设备尺寸的专门优化
   ======================================== */

/* 超大屏幕 (1920px+) */
@media (min-width: 1920px) {
  :root {
    --sidebar-width: 320px;
    --assistant-panel-width: 360px;
  }
  
  .chat-container {
    max-width: 1200px;
    padding: var(--space-8);
  }
  
  .feature-cards {
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-6);
  }
  
  .welcome-title {
    font-size: 3rem;
  }
  
  .welcome-description {
    font-size: 1.25rem;
    max-width: 800px;
  }
}

/* 大屏幕 (1440px - 1920px) */
@media (min-width: 1440px) and (max-width: 1919px) {
  .chat-container {
    max-width: 1000px;
    padding: var(--space-6);
  }
  
  .feature-cards {
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-5);
  }
}

/* 标准桌面 (1024px - 1439px) */
@media (min-width: 1024px) and (max-width: 1439px) {
  :root {
    --sidebar-width: 260px;
    --assistant-panel-width: 280px;
  }
  
  .chat-container {
    max-width: 900px;
    padding: var(--space-5);
  }
  
  .feature-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-4);
  }
  
  .navbar-center {
    max-width: 200px;
  }
  
  .current-chat-title {
    max-width: 200px;
  }
}

/* 平板横屏 (768px - 1023px) */
@media (min-width: 768px) and (max-width: 1023px) {
  :root {
    --sidebar-width: 240px;
    --navbar-height: 56px;
  }
  
  .assistant-panel {
    display: none;
  }
  
  .chat-container {
    padding: var(--space-4);
  }
  
  .feature-cards {
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-3);
  }
  
  .navbar-center {
    display: flex;
    max-width: 150px;
  }
  
  .current-chat-title {
    max-width: 150px;
    font-size: var(--font-size-sm);
  }
  
  .navbar-right {
    gap: var(--space-1);
  }
  
  /* 隐藏部分导航元素 */
  .language-switcher {
    display: none;
  }
}

/* 平板竖屏 (480px - 767px) */
@media (min-width: 480px) and (max-width: 767px) {
  :root {
    --sidebar-width: 100vw;
    --navbar-height: 56px;
  }
  
  .app-navbar {
    padding: 0 var(--space-3);
  }
  
  .navbar-center {
    display: none;
  }
  
  .navbar-right {
    gap: var(--space-1);
  }
  
  /* 隐藏更多导航元素 */
  .language-switcher,
  .settings-toggle,
  .notification-toggle {
    display: none;
  }
  
  .sidebar {
    position: fixed;
    top: var(--navbar-height);
    left: 0;
    height: calc(100vh - var(--navbar-height));
    z-index: 60;
    transform: translateX(-100%);
    transition: transform var(--duration-normal) var(--easing-ease);
    box-shadow: var(--shadow-xl);
  }
  
  .sidebar.open {
    transform: translateX(0);
  }
  
  .chat-container {
    padding: var(--space-3);
  }
  
  .feature-cards {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }
  
  .feature-card {
    padding: var(--space-4);
  }
  
  .quick-actions {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-2);
  }
  
  .welcome-title {
    font-size: var(--font-size-2xl);
  }
  
  .welcome-description {
    font-size: var(--font-size-base);
  }
  
  .welcome-avatar {
    width: 64px;
    height: 64px;
    font-size: 1.75rem;
  }
}

/* 手机 (320px - 479px) */
@media (max-width: 479px) {
  :root {
    --navbar-height: 52px;
    --space-4: 0.75rem;
    --space-6: 1rem;
  }
  
  .app-navbar {
    padding: 0 var(--space-2);
  }
  
  .brand-text {
    display: none;
  }
  
  .navbar-right {
    gap: var(--space-1);
  }
  
  /* 只保留最重要的导航元素 */
  .language-switcher,
  .settings-toggle,
  .notification-toggle,
  .user-menu span {
    display: none;
  }
  
  .chat-container {
    padding: var(--space-2);
  }
  
  .welcome-avatar {
    width: 56px;
    height: 56px;
    font-size: 1.5rem;
    margin-bottom: var(--space-4);
  }
  
  .welcome-title {
    font-size: var(--font-size-xl);
    margin-bottom: var(--space-3);
  }
  
  .welcome-description {
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-6);
  }
  
  .feature-card {
    padding: var(--space-3);
  }
  
  .feature-icon {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
  }
  
  .feature-title {
    font-size: var(--font-size-base);
  }
  
  .feature-desc {
    font-size: var(--font-size-xs);
  }
  
  .quick-actions {
    display: grid;
    grid-template-columns: 1fr;
    gap: var(--space-2);
  }
  
  .quick-action-btn {
    padding: var(--space-3);
    min-width: auto;
  }
  
  .quick-action-icon {
    width: 28px;
    height: 28px;
    font-size: 0.875rem;
  }
  
  .input-wrapper {
    padding: var(--space-2);
  }
  
  .send-button {
    width: 32px;
    height: 32px;
  }
  
  .sidebar-header {
    padding: var(--space-3);
  }
  
  .sidebar-content {
    padding: var(--space-3);
  }
  
  .new-chat-btn {
    padding: var(--space-2) var(--space-3);
    font-size: var(--font-size-xs);
  }
}

/* 超小屏幕 (< 320px) */
@media (max-width: 319px) {
  .app-navbar {
    padding: 0 var(--space-1);
  }
  
  .chat-container {
    padding: var(--space-1);
  }
  
  .welcome-title {
    font-size: var(--font-size-lg);
  }
  
  .feature-card {
    padding: var(--space-2);
  }
}

/* 横屏手机特殊处理 */
@media (max-height: 500px) and (orientation: landscape) {
  .welcome-screen {
    padding: var(--space-4);
  }
  
  .welcome-avatar {
    width: 48px;
    height: 48px;
    font-size: 1.25rem;
    margin-bottom: var(--space-3);
  }
  
  .welcome-title {
    font-size: var(--font-size-lg);
    margin-bottom: var(--space-2);
  }
  
  .welcome-description {
    font-size: var(--font-size-sm);
    margin-bottom: var(--space-4);
  }
  
  .feature-cards {
    gap: var(--space-2);
  }
  
  .feature-card {
    padding: var(--space-3);
  }
  
  .quick-actions {
    gap: var(--space-1);
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .quick-function-btn,
  .ops-btn,
  .suggestion-btn,
  .quick-action-btn {
    min-height: 44px;
  }
  
  .sidebar-toggle,
  .search-toggle,
  .panel-toggle {
    min-width: 44px;
    min-height: 44px;
  }
  
  .send-button {
    min-width: 44px;
    min-height: 44px;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .brand-icon,
  .feature-icon,
  .quick-action-icon {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* 打印样式 */
@media print {
  .sidebar,
  .assistant-panel,
  .app-navbar,
  .chat-input-area {
    display: none !important;
  }
  
  .chat-main {
    width: 100% !important;
    margin: 0 !important;
    padding: 0 !important;
  }
  
  .chat-container {
    max-width: none !important;
    padding: 0 !important;
  }
}
