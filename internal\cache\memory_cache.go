package cache

import (
	"context"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// MemoryCache L1内存缓存实现
type MemoryCache struct {
	data      map[string]*CacheItem
	maxSize   int
	defaultTTL time.Duration
	logger    *logrus.Logger
	mutex     sync.RWMutex
}

// NewMemoryCache 创建内存缓存
func NewMemoryCache(maxSize int, defaultTTL time.Duration, logger *logrus.Logger) *MemoryCache {
	return &MemoryCache{
		data:       make(map[string]*CacheItem),
		maxSize:    maxSize,
		defaultTTL: defaultTTL,
		logger:     logger,
	}
}

// Get 获取值
func (mc *MemoryCache) Get(ctx context.Context, key string) (interface{}, bool) {
	mc.mutex.RLock()
	defer mc.mutex.RUnlock()

	item, exists := mc.data[key]
	if !exists {
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(item.ExpiresAt) {
		// 异步删除过期项
		go func() {
			mc.mutex.Lock()
			delete(mc.data, key)
			mc.mutex.Unlock()
		}()
		return nil, false
	}

	// 更新访问信息
	item.AccessedAt = time.Now()
	item.AccessCount++

	return item.Value, true
}

// Set 设置值
func (mc *MemoryCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	// 检查容量限制
	if len(mc.data) >= mc.maxSize {
		// 删除最旧的项
		mc.evictOldest()
	}

	if ttl <= 0 {
		ttl = mc.defaultTTL
	}

	mc.data[key] = &CacheItem{
		Key:         key,
		Value:       value,
		ExpiresAt:   time.Now().Add(ttl),
		CreatedAt:   time.Now(),
		AccessedAt:  time.Now(),
		AccessCount: 0,
	}

	return nil
}

// Delete 删除值
func (mc *MemoryCache) Delete(ctx context.Context, key string) error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	delete(mc.data, key)
	return nil
}

// Clear 清空缓存
func (mc *MemoryCache) Clear(ctx context.Context) error {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	mc.data = make(map[string]*CacheItem)
	return nil
}

// Cleanup 清理过期项
func (mc *MemoryCache) Cleanup(ctx context.Context) {
	mc.mutex.Lock()
	defer mc.mutex.Unlock()

	now := time.Now()
	for key, item := range mc.data {
		if now.After(item.ExpiresAt) {
			delete(mc.data, key)
		}
	}
}

// evictOldest 删除最旧的项
func (mc *MemoryCache) evictOldest() {
	var oldestKey string
	var oldestTime time.Time

	for key, item := range mc.data {
		if oldestKey == "" || item.CreatedAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = item.CreatedAt
		}
	}

	if oldestKey != "" {
		delete(mc.data, oldestKey)
	}
}

// LRUCache L2 LRU缓存实现
type LRUCache struct {
	data       map[string]*LRUNode
	head       *LRUNode
	tail       *LRUNode
	maxSize    int
	defaultTTL time.Duration
	logger     *logrus.Logger
	mutex      sync.RWMutex
}

// LRUNode LRU节点
type LRUNode struct {
	Key       string
	Item      *CacheItem
	Prev      *LRUNode
	Next      *LRUNode
}

// NewLRUCache 创建LRU缓存
func NewLRUCache(maxSize int, defaultTTL time.Duration, logger *logrus.Logger) *LRUCache {
	lru := &LRUCache{
		data:       make(map[string]*LRUNode),
		maxSize:    maxSize,
		defaultTTL: defaultTTL,
		logger:     logger,
	}

	// 创建哨兵节点
	lru.head = &LRUNode{}
	lru.tail = &LRUNode{}
	lru.head.Next = lru.tail
	lru.tail.Prev = lru.head

	return lru
}

// Get 获取值
func (lru *LRUCache) Get(ctx context.Context, key string) (interface{}, bool) {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()

	node, exists := lru.data[key]
	if !exists {
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(node.Item.ExpiresAt) {
		lru.removeNode(node)
		delete(lru.data, key)
		return nil, false
	}

	// 移动到头部（最近使用）
	lru.moveToHead(node)

	// 更新访问信息
	node.Item.AccessedAt = time.Now()
	node.Item.AccessCount++

	return node.Item.Value, true
}

// Set 设置值
func (lru *LRUCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()

	if ttl <= 0 {
		ttl = lru.defaultTTL
	}

	if node, exists := lru.data[key]; exists {
		// 更新现有节点
		node.Item.Value = value
		node.Item.ExpiresAt = time.Now().Add(ttl)
		node.Item.AccessedAt = time.Now()
		lru.moveToHead(node)
	} else {
		// 创建新节点
		newItem := &CacheItem{
			Key:         key,
			Value:       value,
			ExpiresAt:   time.Now().Add(ttl),
			CreatedAt:   time.Now(),
			AccessedAt:  time.Now(),
			AccessCount: 0,
		}

		newNode := &LRUNode{
			Key:  key,
			Item: newItem,
		}

		lru.data[key] = newNode
		lru.addToHead(newNode)

		// 检查容量限制
		if len(lru.data) > lru.maxSize {
			tail := lru.removeTail()
			delete(lru.data, tail.Key)
		}
	}

	return nil
}

// Delete 删除值
func (lru *LRUCache) Delete(ctx context.Context, key string) error {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()

	if node, exists := lru.data[key]; exists {
		lru.removeNode(node)
		delete(lru.data, key)
	}

	return nil
}

// Clear 清空缓存
func (lru *LRUCache) Clear(ctx context.Context) error {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()

	lru.data = make(map[string]*LRUNode)
	lru.head.Next = lru.tail
	lru.tail.Prev = lru.head

	return nil
}

// Cleanup 清理过期项
func (lru *LRUCache) Cleanup(ctx context.Context) {
	lru.mutex.Lock()
	defer lru.mutex.Unlock()

	now := time.Now()
	toDelete := make([]string, 0)

	for key, node := range lru.data {
		if now.After(node.Item.ExpiresAt) {
			toDelete = append(toDelete, key)
		}
	}

	for _, key := range toDelete {
		if node, exists := lru.data[key]; exists {
			lru.removeNode(node)
			delete(lru.data, key)
		}
	}
}

// addToHead 添加到头部
func (lru *LRUCache) addToHead(node *LRUNode) {
	node.Prev = lru.head
	node.Next = lru.head.Next
	lru.head.Next.Prev = node
	lru.head.Next = node
}

// removeNode 移除节点
func (lru *LRUCache) removeNode(node *LRUNode) {
	node.Prev.Next = node.Next
	node.Next.Prev = node.Prev
}

// moveToHead 移动到头部
func (lru *LRUCache) moveToHead(node *LRUNode) {
	lru.removeNode(node)
	lru.addToHead(node)
}

// removeTail 移除尾部节点
func (lru *LRUCache) removeTail() *LRUNode {
	lastNode := lru.tail.Prev
	lru.removeNode(lastNode)
	return lastNode
}

// PersistentCache L3持久化缓存实现
type PersistentCache struct {
	data       map[string]*CacheItem
	maxSize    int
	defaultTTL time.Duration
	filePath   string
	logger     *logrus.Logger
	mutex      sync.RWMutex
}

// NewPersistentCache 创建持久化缓存
func NewPersistentCache(maxSize int, defaultTTL time.Duration, filePath string, logger *logrus.Logger) *PersistentCache {
	pc := &PersistentCache{
		data:       make(map[string]*CacheItem),
		maxSize:    maxSize,
		defaultTTL: defaultTTL,
		filePath:   filePath,
		logger:     logger,
	}

	// 加载持久化数据
	pc.loadFromDisk()

	return pc
}

// Get 获取值
func (pc *PersistentCache) Get(ctx context.Context, key string) (interface{}, bool) {
	pc.mutex.RLock()
	defer pc.mutex.RUnlock()

	item, exists := pc.data[key]
	if !exists {
		return nil, false
	}

	// 检查是否过期
	if time.Now().After(item.ExpiresAt) {
		// 异步删除过期项
		go func() {
			pc.mutex.Lock()
			delete(pc.data, key)
			pc.mutex.Unlock()
			pc.saveToDisk()
		}()
		return nil, false
	}

	// 更新访问信息
	item.AccessedAt = time.Now()
	item.AccessCount++

	return item.Value, true
}

// Set 设置值
func (pc *PersistentCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	// 检查容量限制
	if len(pc.data) >= pc.maxSize {
		pc.evictOldest()
	}

	if ttl <= 0 {
		ttl = pc.defaultTTL
	}

	pc.data[key] = &CacheItem{
		Key:         key,
		Value:       value,
		ExpiresAt:   time.Now().Add(ttl),
		CreatedAt:   time.Now(),
		AccessedAt:  time.Now(),
		AccessCount: 0,
	}

	// 异步保存到磁盘
	go pc.saveToDisk()

	return nil
}

// Delete 删除值
func (pc *PersistentCache) Delete(ctx context.Context, key string) error {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	delete(pc.data, key)

	// 异步保存到磁盘
	go pc.saveToDisk()

	return nil
}

// Clear 清空缓存
func (pc *PersistentCache) Clear(ctx context.Context) error {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	pc.data = make(map[string]*CacheItem)

	// 异步保存到磁盘
	go pc.saveToDisk()

	return nil
}

// Cleanup 清理过期项
func (pc *PersistentCache) Cleanup(ctx context.Context) {
	pc.mutex.Lock()
	defer pc.mutex.Unlock()

	now := time.Now()
	changed := false

	for key, item := range pc.data {
		if now.After(item.ExpiresAt) {
			delete(pc.data, key)
			changed = true
		}
	}

	if changed {
		// 异步保存到磁盘
		go pc.saveToDisk()
	}
}

// evictOldest 删除最旧的项
func (pc *PersistentCache) evictOldest() {
	var oldestKey string
	var oldestTime time.Time

	for key, item := range pc.data {
		if oldestKey == "" || item.CreatedAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = item.CreatedAt
		}
	}

	if oldestKey != "" {
		delete(pc.data, oldestKey)
	}
}

// loadFromDisk 从磁盘加载数据
func (pc *PersistentCache) loadFromDisk() {
	// 简化实现，实际应该使用文件系统
	pc.logger.Debug("PersistentCache: Loading from disk (placeholder)")
}

// saveToDisk 保存到磁盘
func (pc *PersistentCache) saveToDisk() {
	// 简化实现，实际应该使用文件系统
	pc.logger.Debug("PersistentCache: Saving to disk (placeholder)")
}
