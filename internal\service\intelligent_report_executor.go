package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"

	"aiops-platform/internal/ai"
	"aiops-platform/internal/statistics"
)

// 🎯 智能报表执行器 - 专门处理报表生成操作
type IntelligentReportExecutor struct {
	db                    *gorm.DB
	logger                *logrus.Logger
	reportIntentRecognizer *ai.IntelligentReportIntentRecognizer
	reportGenerator       *ai.IntelligentReportGenerator
	reportSystem          *statistics.ReportSystem
	deepseekClient        *ai.DeepSeekClient
}

// NewIntelligentReportExecutor 创建智能报表执行器
func NewIntelligentReportExecutor(
	db *gorm.DB,
	logger *logrus.Logger,
	deepseekClient *ai.DeepSeekClient,
) *IntelligentReportExecutor {
	executor := &IntelligentReportExecutor{
		db:             db,
		logger:         logger,
		deepseekClient: deepseekClient,
	}
	
	// 初始化组件
	executor.reportIntentRecognizer = ai.NewIntelligentReportIntentRecognizer(deepseekClient, logger)
	executor.reportGenerator = ai.NewIntelligentReportGenerator(deepseekClient, db, logger)
	
	// 初始化报表系统
	reportConfig := statistics.DefaultReportSystemConfig()
	executor.reportSystem = statistics.NewReportSystem(reportConfig, logger)
	
	logger.Info("🎯 智能报表执行器初始化完成")
	return executor
}

// Execute 执行报表生成操作
func (ire *IntelligentReportExecutor) Execute(
	ctx context.Context,
	req *ExecutionRequest,
) (*UnifiedExecutionResult, error) {
	start := time.Now()
	
	ire.logger.WithFields(logrus.Fields{
		"user_id":      req.UserID,
		"session_id":   req.SessionID,
		"original_msg": req.OriginalMsg,
	}).Info("🎯 开始执行智能报表生成")
	
	// 第一步：识别报表意图
	ire.logger.Info("📋 第一步：开始识别报表意图...")
	reportIntent, err := ire.reportIntentRecognizer.RecognizeReportIntent(ctx, req.OriginalMsg)
	if err != nil {
		ire.logger.WithError(err).Error("❌ 第一步失败：报表意图识别失败")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 报表意图识别失败：%s", err.Error()),
			Action:  "intent_recognition_failed",
		}, nil
	}
	
	ire.logger.WithFields(logrus.Fields{
		"report_type":    reportIntent.ReportType,
		"time_range":     reportIntent.TimeRange,
		"export_format":  reportIntent.ExportFormat,
		"confidence":     reportIntent.Confidence,
	}).Info("✅ 第一步完成：报表意图识别成功")

	// 第二步：生成智能报表
	ire.logger.Info("🤖 第二步：开始生成智能报表...")
	reportReq := &ai.IntelligentReportRequest{
		ReportType:      reportIntent.ReportType,
		TimeRange:       reportIntent.TimeRange,
		ExportFormat:    reportIntent.ExportFormat,
		AnalysisLevel:   reportIntent.AnalysisLevel,
		IncludeInsights: reportIntent.IncludeInsights,
		CustomFilters:   reportIntent.CustomFilters,
		UserID:          req.UserID,
		SessionID:       req.SessionID,
	}
	
	reportResult, err := ire.reportGenerator.GenerateReport(ctx, reportReq)
	if err != nil {
		ire.logger.WithError(err).Error("❌ 第二步失败：智能报表生成失败")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 智能报表生成失败：%s", err.Error()),
			Action:  "report_generation_failed",
		}, nil
	}

	ire.logger.WithFields(logrus.Fields{
		"report_id":      reportResult.ReportID,
		"execution_time": reportResult.ExecutionTime,
		"insights_count": len(reportResult.Insights),
		"charts_count":   len(reportResult.Charts),
	}).Info("✅ 第二步完成：智能报表生成成功")
	
	// 第三步：格式化输出内容
	ire.logger.Info("📝 第三步：开始格式化报表内容...")
	content := ire.formatReportContent(reportResult)
	ire.logger.WithField("content_length", len(content)).Info("✅ 第三步完成：报表内容格式化成功")
	
	result := &UnifiedExecutionResult{
		Success:       true,
		Content:       content,
		Action:        "report_generated",
		ExecutionType: "report_operations",
		ExecutionTime: time.Since(start),
		RawData:       reportResult,
		Metadata: map[string]interface{}{
			"report_id":      reportResult.ReportID,
			"report_type":    reportResult.ReportType,
			"export_format":  reportResult.ExportFormat,
			"insights_count": len(reportResult.Insights),
			"charts_count":   len(reportResult.Charts),
			"ai_enhanced":    true,
		},
	}
	
	ire.logger.WithFields(logrus.Fields{
		"report_id":      reportResult.ReportID,
		"execution_time": result.ExecutionTime,
		"success":        result.Success,
		"total_steps":    3,
	}).Info("🎉 智能报表执行完成 - 所有步骤成功！")
	
	return result, nil
}

// formatReportContent 格式化报表内容
func (ire *IntelligentReportExecutor) formatReportContent(result *ai.IntelligentReportResult) string {
	content := fmt.Sprintf(`## 📊 %s

**报表ID**: %s
**生成时间**: %s
**执行时间**: %s

### 📈 关键指标
%s

### 🔍 数据洞察
%s

### 📋 详细内容
%s

---
*本报表由AI智能生成，包含深度数据分析和预测性洞察*`,
		result.Title,
		result.ReportID,
		result.GeneratedAt.Format("2006-01-02 15:04:05"),
		result.ExecutionTime.String(),
		ire.formatKeyMetrics(result.Summary),
		ire.formatInsights(result.Insights),
		result.Content,
	)
	
	return content
}

// formatKeyMetrics 格式化关键指标
func (ire *IntelligentReportExecutor) formatKeyMetrics(summary *ai.ReportSummary) string {
	if summary == nil || len(summary.KeyMetrics) == 0 {
		return "暂无关键指标数据"
	}
	
	var metrics string
	for key, value := range summary.KeyMetrics {
		metrics += fmt.Sprintf("- **%s**: %v\n", key, value)
	}
	
	return metrics
}

// formatInsights 格式化洞察信息
func (ire *IntelligentReportExecutor) formatInsights(insights []string) string {
	if len(insights) == 0 {
		return "暂无AI洞察信息"
	}
	
	var content string
	for i, insight := range insights {
		content += fmt.Sprintf("%d. %s\n", i+1, insight)
	}
	
	return content
}

// IsReportRequest 检查是否为报表请求
func (ire *IntelligentReportExecutor) IsReportRequest(message string) bool {
	// 快速检查是否包含报表关键词
	reportKeywords := []string{
		"报表", "报告", "统计", "分析", "生成",
		"运维报表", "系统报表", "健康报表", "AI报表",
		"数据分析", "趋势分析", "性能报表",
	}
	
	messageLower := strings.ToLower(message)
	for _, keyword := range reportKeywords {
		if strings.Contains(messageLower, keyword) {
			return true
		}
	}
	
	return false
}

// GetSupportedReportTypes 获取支持的报表类型
func (ire *IntelligentReportExecutor) GetSupportedReportTypes() []string {
	return []string{
		"operation",  // 运维操作报表
		"health",     // 系统健康报表
		"ai_usage",   // AI使用报表
		"custom",     // 自定义报表
	}
}

// GetSupportedTimeRanges 获取支持的时间范围
func (ire *IntelligentReportExecutor) GetSupportedTimeRanges() []string {
	return []string{
		"24h",  // 最近24小时
		"7d",   // 最近7天
		"30d",  // 最近30天
	}
}

// GetSupportedExportFormats 获取支持的导出格式
func (ire *IntelligentReportExecutor) GetSupportedExportFormats() []string {
	return []string{
		"json",  // JSON格式
		"csv",   // CSV表格格式
		"pdf",   // PDF文档格式
		"html",  // HTML网页格式
	}
}

// StartReportSystem 启动报表系统
func (ire *IntelligentReportExecutor) StartReportSystem(ctx context.Context) error {
	if ire.reportSystem != nil {
		return ire.reportSystem.Start(ctx)
	}
	return nil
}

// StopReportSystem 停止报表系统
func (ire *IntelligentReportExecutor) StopReportSystem(ctx context.Context) error {
	if ire.reportSystem != nil {
		return ire.reportSystem.Stop(ctx)
	}
	return nil
}

// GetExecutorInfo 获取执行器信息
func (ire *IntelligentReportExecutor) GetExecutorInfo() *ExecutorInfo {
	return &ExecutorInfo{
		Name:         "IntelligentReportExecutor",
		Version:      "1.0.0",
		Description:  "智能报表执行器，支持AI驱动的报表生成和数据分析",
		Capabilities: []string{
			"运维操作报表", "系统健康报表", "AI使用报表", "自定义报表",
			"智能数据分析", "趋势预测", "多格式导出", "AI洞察生成",
		},
		SafetyLevel: "safe",
	}
}
