package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// WorkflowEngine 工作流引擎
type WorkflowEngine struct {
	workflows  map[string]*Workflow
	executions map[string]*WorkflowExecution
	templates  map[string]*WorkflowTemplate
	mutex      sync.RWMutex
	logger     *logrus.Logger
	contextMgr *ContextManager
}

// Workflow 工作流定义
type Workflow struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Version     string                 `json:"version"`
	Steps       []*WorkflowStep        `json:"steps"`
	Variables   map[string]interface{} `json:"variables"`
	Triggers    []*WorkflowTrigger     `json:"triggers"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Type       string                 `json:"type"` // command, condition, loop, parallel, human_approval
	Action     *StepAction            `json:"action"`
	Condition  *StepCondition         `json:"condition"`
	OnSuccess  string                 `json:"on_success"` // next step id
	OnFailure  string                 `json:"on_failure"` // next step id or "retry", "abort"
	Timeout    time.Duration          `json:"timeout"`
	RetryCount int                    `json:"retry_count"`
	Variables  map[string]interface{} `json:"variables"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// StepAction 步骤动作
type StepAction struct {
	Type       string                 `json:"type"`   // command, api_call, notification, script
	Target     string                 `json:"target"` // host_id, api_endpoint, etc.
	Command    string                 `json:"command"`
	Script     string                 `json:"script"`
	Parameters map[string]interface{} `json:"parameters"`
	Headers    map[string]string      `json:"headers"`
}

// StepCondition 步骤条件
type StepCondition struct {
	Type       string      `json:"type"` // expression, script, api_check
	Expression string      `json:"expression"`
	Script     string      `json:"script"`
	Expected   interface{} `json:"expected"`
}

// WorkflowTrigger 工作流触发器
type WorkflowTrigger struct {
	Type       string                 `json:"type"`     // schedule, event, manual, api
	Schedule   string                 `json:"schedule"` // cron expression
	Event      string                 `json:"event"`
	Condition  *StepCondition         `json:"condition"`
	Parameters map[string]interface{} `json:"parameters"`
}

// WorkflowExecution 工作流执行
type WorkflowExecution struct {
	ID          string                    `json:"id"`
	WorkflowID  string                    `json:"workflow_id"`
	SessionID   string                    `json:"session_id"`
	UserID      int64                     `json:"user_id"`
	Status      string                    `json:"status"` // running, completed, failed, paused, cancelled
	CurrentStep string                    `json:"current_step"`
	Steps       map[string]*StepExecution `json:"steps"`
	Variables   map[string]interface{}    `json:"variables"`
	StartedAt   time.Time                 `json:"started_at"`
	CompletedAt *time.Time                `json:"completed_at"`
	Error       string                    `json:"error"`
	Metadata    map[string]interface{}    `json:"metadata"`
	mutex       sync.RWMutex              `json:"-"`
}

// StepExecution 步骤执行
type StepExecution struct {
	ID          string                 `json:"id"`
	StepID      string                 `json:"step_id"`
	Status      string                 `json:"status"` // pending, running, completed, failed, skipped
	StartedAt   *time.Time             `json:"started_at"`
	CompletedAt *time.Time             `json:"completed_at"`
	Duration    time.Duration          `json:"duration"`
	Result      interface{}            `json:"result"`
	Error       string                 `json:"error"`
	RetryCount  int                    `json:"retry_count"`
	Variables   map[string]interface{} `json:"variables"`
	Logs        []string               `json:"logs"`
}

// WorkflowTemplate 工作流模板
type WorkflowTemplate struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Category    string                 `json:"category"`
	Description string                 `json:"description"`
	Template    *Workflow              `json:"template"`
	Parameters  map[string]interface{} `json:"parameters"`
	Examples    []string               `json:"examples"`
	Tags        []string               `json:"tags"`
}

// NewWorkflowEngine 创建工作流引擎
func NewWorkflowEngine(contextMgr *ContextManager, logger *logrus.Logger) *WorkflowEngine {
	engine := &WorkflowEngine{
		workflows:  make(map[string]*Workflow),
		executions: make(map[string]*WorkflowExecution),
		templates:  make(map[string]*WorkflowTemplate),
		logger:     logger,
		contextMgr: contextMgr,
	}

	// 初始化内置模板
	engine.initializeBuiltinTemplates()

	return engine
}

// initializeBuiltinTemplates 初始化内置模板
func (we *WorkflowEngine) initializeBuiltinTemplates() {
	templates := []*WorkflowTemplate{
		{
			ID:          "health_check",
			Name:        "系统健康检查",
			Category:    "monitoring",
			Description: "对指定主机进行全面的系统健康检查",
			Template: &Workflow{
				Name:        "系统健康检查",
				Description: "检查CPU、内存、磁盘、网络等系统指标",
				Steps: []*WorkflowStep{
					{
						ID:   "check_cpu",
						Name: "检查CPU使用率",
						Type: "command",
						Action: &StepAction{
							Type:    "command",
							Command: "top -bn1 | grep 'Cpu(s)' | awk '{print $2}' | cut -d'%' -f1",
						},
						OnSuccess: "check_memory",
						OnFailure: "abort",
						Timeout:   30 * time.Second,
					},
					{
						ID:   "check_memory",
						Name: "检查内存使用率",
						Type: "command",
						Action: &StepAction{
							Type:    "command",
							Command: "free | grep Mem | awk '{printf \"%.2f\", $3/$2 * 100.0}'",
						},
						OnSuccess: "check_disk",
						OnFailure: "abort",
						Timeout:   30 * time.Second,
					},
					{
						ID:   "check_disk",
						Name: "检查磁盘使用率",
						Type: "command",
						Action: &StepAction{
							Type:    "command",
							Command: "df -h | grep -vE '^Filesystem|tmpfs|cdrom' | awk '{print $5}' | cut -d'%' -f1 | sort -n | tail -1",
						},
						OnSuccess: "generate_report",
						OnFailure: "abort",
						Timeout:   30 * time.Second,
					},
					{
						ID:   "generate_report",
						Name: "生成健康报告",
						Type: "script",
						Action: &StepAction{
							Type:   "script",
							Script: "generate_health_report",
						},
						Timeout: 10 * time.Second,
					},
				},
			},
			Parameters: map[string]interface{}{
				"target_host": "required",
			},
			Examples: []string{
				"对*************进行健康检查",
				"检查服务器健康状态",
			},
			Tags: []string{"monitoring", "health", "system"},
		},
		{
			ID:          "service_restart",
			Name:        "服务重启流程",
			Category:    "service_management",
			Description: "安全地重启指定服务",
			Template: &Workflow{
				Name:        "服务重启流程",
				Description: "检查服务状态、停止服务、启动服务、验证状态",
				Steps: []*WorkflowStep{
					{
						ID:   "check_service_status",
						Name: "检查服务状态",
						Type: "command",
						Action: &StepAction{
							Type:    "command",
							Command: "systemctl is-active {{.service_name}}",
						},
						OnSuccess: "stop_service",
						OnFailure: "start_service",
						Timeout:   15 * time.Second,
					},
					{
						ID:   "stop_service",
						Name: "停止服务",
						Type: "command",
						Action: &StepAction{
							Type:    "command",
							Command: "systemctl stop {{.service_name}}",
						},
						OnSuccess: "wait_stop",
						OnFailure: "force_stop",
						Timeout:   60 * time.Second,
					},
					{
						ID:   "wait_stop",
						Name: "等待服务停止",
						Type: "command",
						Action: &StepAction{
							Type:    "command",
							Command: "sleep 3 && systemctl is-active {{.service_name}}",
						},
						OnSuccess: "start_service",
						OnFailure: "start_service",
						Timeout:   10 * time.Second,
					},
					{
						ID:   "start_service",
						Name: "启动服务",
						Type: "command",
						Action: &StepAction{
							Type:    "command",
							Command: "systemctl start {{.service_name}}",
						},
						OnSuccess:  "verify_service",
						OnFailure:  "abort",
						Timeout:    60 * time.Second,
						RetryCount: 2,
					},
					{
						ID:   "verify_service",
						Name: "验证服务状态",
						Type: "command",
						Action: &StepAction{
							Type:    "command",
							Command: "systemctl is-active {{.service_name}} && systemctl status {{.service_name}}",
						},
						Timeout: 30 * time.Second,
					},
				},
			},
			Parameters: map[string]interface{}{
				"service_name": "required",
				"target_host":  "required",
			},
			Examples: []string{
				"重启nginx服务",
				"重启*************上的mysql服务",
			},
			Tags: []string{"service", "restart", "management"},
		},
		{
			ID:          "log_analysis",
			Name:        "日志分析流程",
			Category:    "troubleshooting",
			Description: "分析系统日志并提取关键信息",
			Template: &Workflow{
				Name:        "日志分析流程",
				Description: "收集、分析和汇总系统日志信息",
				Steps: []*WorkflowStep{
					{
						ID:   "collect_system_logs",
						Name: "收集系统日志",
						Type: "command",
						Action: &StepAction{
							Type:    "command",
							Command: "journalctl --since '{{.time_range}}' --no-pager | tail -1000",
						},
						OnSuccess: "collect_error_logs",
						OnFailure: "abort",
						Timeout:   60 * time.Second,
					},
					{
						ID:   "collect_error_logs",
						Name: "收集错误日志",
						Type: "command",
						Action: &StepAction{
							Type:    "command",
							Command: "grep -i error /var/log/messages | tail -100",
						},
						OnSuccess: "analyze_patterns",
						OnFailure: "analyze_patterns",
						Timeout:   30 * time.Second,
					},
					{
						ID:   "analyze_patterns",
						Name: "分析日志模式",
						Type: "script",
						Action: &StepAction{
							Type:   "script",
							Script: "analyze_log_patterns",
						},
						Timeout: 30 * time.Second,
					},
				},
			},
			Parameters: map[string]interface{}{
				"time_range":  "1 hour ago",
				"target_host": "required",
			},
			Examples: []string{
				"分析最近1小时的日志",
				"检查*************的错误日志",
			},
			Tags: []string{"logs", "analysis", "troubleshooting"},
		},
	}

	for _, template := range templates {
		we.templates[template.ID] = template
	}
}

// CreateWorkflowFromTemplate 从模板创建工作流
func (we *WorkflowEngine) CreateWorkflowFromTemplate(templateID string, parameters map[string]interface{}) (*Workflow, error) {
	we.mutex.RLock()
	template, exists := we.templates[templateID]
	we.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("template not found: %s", templateID)
	}

	// 深拷贝模板
	workflowData, err := json.Marshal(template.Template)
	if err != nil {
		return nil, err
	}

	var workflow Workflow
	if err := json.Unmarshal(workflowData, &workflow); err != nil {
		return nil, err
	}

	// 生成新的ID
	workflow.ID = fmt.Sprintf("wf_%d", time.Now().UnixNano())
	workflow.CreatedAt = time.Now()
	workflow.UpdatedAt = time.Now()

	// 应用参数
	workflow.Variables = parameters

	// 存储工作流
	we.mutex.Lock()
	we.workflows[workflow.ID] = &workflow
	we.mutex.Unlock()

	return &workflow, nil
}

// ExecuteWorkflow 执行工作流
func (we *WorkflowEngine) ExecuteWorkflow(ctx context.Context, workflowID, sessionID string, userID int64, variables map[string]interface{}) (*WorkflowExecution, error) {
	we.mutex.RLock()
	workflow, exists := we.workflows[workflowID]
	we.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("workflow not found: %s", workflowID)
	}

	// 创建执行实例
	execution := &WorkflowExecution{
		ID:          fmt.Sprintf("exec_%d", time.Now().UnixNano()),
		WorkflowID:  workflowID,
		SessionID:   sessionID,
		UserID:      userID,
		Status:      "running",
		CurrentStep: "",
		Steps:       make(map[string]*StepExecution),
		Variables:   make(map[string]interface{}),
		StartedAt:   time.Now(),
		Metadata:    make(map[string]interface{}),
	}

	// 合并变量
	for k, v := range workflow.Variables {
		execution.Variables[k] = v
	}
	for k, v := range variables {
		execution.Variables[k] = v
	}

	// 存储执行实例
	we.mutex.Lock()
	we.executions[execution.ID] = execution
	we.mutex.Unlock()

	// 异步执行工作流
	go we.runWorkflow(ctx, execution, workflow)

	return execution, nil
}

// runWorkflow 运行工作流
func (we *WorkflowEngine) runWorkflow(ctx context.Context, execution *WorkflowExecution, workflow *Workflow) {
	defer func() {
		if r := recover(); r != nil {
			execution.mutex.Lock()
			execution.Status = "failed"
			execution.Error = fmt.Sprintf("panic: %v", r)
			now := time.Now()
			execution.CompletedAt = &now
			execution.mutex.Unlock()

			we.logger.WithFields(logrus.Fields{
				"execution_id": execution.ID,
				"workflow_id":  workflow.ID,
				"error":        execution.Error,
			}).Error("Workflow execution panicked")
		}
	}()

	we.logger.WithFields(logrus.Fields{
		"execution_id": execution.ID,
		"workflow_id":  workflow.ID,
		"session_id":   execution.SessionID,
	}).Info("Starting workflow execution")

	// 找到第一个步骤
	if len(workflow.Steps) == 0 {
		execution.mutex.Lock()
		execution.Status = "completed"
		now := time.Now()
		execution.CompletedAt = &now
		execution.mutex.Unlock()
		return
	}

	currentStepID := workflow.Steps[0].ID
	execution.mutex.Lock()
	execution.CurrentStep = currentStepID
	execution.mutex.Unlock()

	// 执行步骤
	for currentStepID != "" {
		select {
		case <-ctx.Done():
			execution.mutex.Lock()
			execution.Status = "cancelled"
			execution.Error = "context cancelled"
			now := time.Now()
			execution.CompletedAt = &now
			execution.mutex.Unlock()
			return
		default:
		}

		// 找到当前步骤
		var currentStep *WorkflowStep
		for _, step := range workflow.Steps {
			if step.ID == currentStepID {
				currentStep = step
				break
			}
		}

		if currentStep == nil {
			execution.mutex.Lock()
			execution.Status = "failed"
			execution.Error = fmt.Sprintf("step not found: %s", currentStepID)
			now := time.Now()
			execution.CompletedAt = &now
			execution.mutex.Unlock()
			return
		}

		// 执行步骤
		nextStepID, err := we.executeStep(ctx, execution, currentStep)
		if err != nil {
			execution.mutex.Lock()
			execution.Status = "failed"
			execution.Error = err.Error()
			now := time.Now()
			execution.CompletedAt = &now
			execution.mutex.Unlock()
			return
		}

		currentStepID = nextStepID
		execution.mutex.Lock()
		execution.CurrentStep = currentStepID
		execution.mutex.Unlock()
	}

	// 工作流完成
	execution.mutex.Lock()
	execution.Status = "completed"
	now := time.Now()
	execution.CompletedAt = &now
	execution.mutex.Unlock()

	we.logger.WithFields(logrus.Fields{
		"execution_id": execution.ID,
		"workflow_id":  workflow.ID,
		"duration":     time.Since(execution.StartedAt),
	}).Info("Workflow execution completed")
}

// executeStep 执行步骤
func (we *WorkflowEngine) executeStep(ctx context.Context, execution *WorkflowExecution, step *WorkflowStep) (string, error) {
	stepExecution := &StepExecution{
		ID:        fmt.Sprintf("step_%d", time.Now().UnixNano()),
		StepID:    step.ID,
		Status:    "running",
		Variables: make(map[string]interface{}),
		Logs:      make([]string, 0),
	}

	now := time.Now()
	stepExecution.StartedAt = &now

	execution.mutex.Lock()
	execution.Steps[step.ID] = stepExecution
	execution.mutex.Unlock()

	we.logger.WithFields(logrus.Fields{
		"execution_id": execution.ID,
		"step_id":      step.ID,
		"step_name":    step.Name,
	}).Info("Executing workflow step")

	// 执行步骤逻辑
	var result interface{}
	var err error

	switch step.Type {
	case "command":
		result, err = we.executeCommandStep(ctx, execution, step)
	case "condition":
		result, err = we.executeConditionStep(ctx, execution, step)
	case "script":
		result, err = we.executeScriptStep(ctx, execution, step)
	default:
		err = fmt.Errorf("unsupported step type: %s", step.Type)
	}

	// 更新步骤执行结果
	stepExecution.Result = result
	stepExecution.Error = ""
	if err != nil {
		stepExecution.Error = err.Error()
		stepExecution.Status = "failed"
	} else {
		stepExecution.Status = "completed"
	}

	completedAt := time.Now()
	stepExecution.CompletedAt = &completedAt
	stepExecution.Duration = completedAt.Sub(*stepExecution.StartedAt)

	// 决定下一步
	var nextStepID string
	if err != nil {
		nextStepID = step.OnFailure
		if nextStepID == "retry" && stepExecution.RetryCount < step.RetryCount {
			stepExecution.RetryCount++
			return step.ID, nil // 重试当前步骤
		} else if nextStepID == "abort" {
			return "", err
		}
	} else {
		nextStepID = step.OnSuccess
	}

	return nextStepID, nil
}

// executeCommandStep 执行命令步骤
func (we *WorkflowEngine) executeCommandStep(ctx context.Context, execution *WorkflowExecution, step *WorkflowStep) (interface{}, error) {
	if step.Action == nil || step.Action.Command == "" {
		return nil, fmt.Errorf("command not specified")
	}

	// 这里应该调用实际的命令执行服务
	// 暂时返回模拟结果
	result := map[string]interface{}{
		"command":   step.Action.Command,
		"output":    "Command executed successfully",
		"exit_code": 0,
	}

	return result, nil
}

// executeConditionStep 执行条件步骤
func (we *WorkflowEngine) executeConditionStep(ctx context.Context, execution *WorkflowExecution, step *WorkflowStep) (interface{}, error) {
	// 简化的条件评估
	return true, nil
}

// executeScriptStep 执行脚本步骤
func (we *WorkflowEngine) executeScriptStep(ctx context.Context, execution *WorkflowExecution, step *WorkflowStep) (interface{}, error) {
	// 简化的脚本执行
	return "Script executed", nil
}

// GetExecution 获取执行状态
func (we *WorkflowEngine) GetExecution(executionID string) (*WorkflowExecution, error) {
	we.mutex.RLock()
	defer we.mutex.RUnlock()

	execution, exists := we.executions[executionID]
	if !exists {
		return nil, fmt.Errorf("execution not found: %s", executionID)
	}

	return execution, nil
}

// ListTemplates 列出模板
func (we *WorkflowEngine) ListTemplates() []*WorkflowTemplate {
	we.mutex.RLock()
	defer we.mutex.RUnlock()

	templates := make([]*WorkflowTemplate, 0, len(we.templates))
	for _, template := range we.templates {
		templates = append(templates, template)
	}

	return templates
}
