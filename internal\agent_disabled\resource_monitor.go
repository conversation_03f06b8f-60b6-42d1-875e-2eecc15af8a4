package agent

import (
	"context"
	"fmt"
	"runtime"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ResourceMonitor 资源监控器
type ResourceMonitor struct {
	config        *AgentConfig
	logger        *logrus.Logger
	isRunning     bool
	mutex         sync.RWMutex
	currentUsage  *ResourceUsage
	monitoringMap map[string]*ProcessMonitor
	stopChan      chan struct{}
}

// ProcessMonitor 进程监控器
type ProcessMonitor struct {
	ExecutionID string
	StartTime   time.Time
	MaxMemory   float64
	TotalCPU    float64
	SampleCount int
	DiskRead    float64
	DiskWrite   float64
	NetworkIn   float64
	NetworkOut  float64
	mutex       sync.RWMutex
}

// SystemMetrics 系统指标
type SystemMetrics struct {
	CPUPercent    float64   `json:"cpu_percent"`
	MemoryUsedMB  float64   `json:"memory_used_mb"`
	MemoryTotalMB float64   `json:"memory_total_mb"`
	DiskUsedMB    float64   `json:"disk_used_mb"`
	DiskTotalMB   float64   `json:"disk_total_mb"`
	LoadAverage   []float64 `json:"load_average"`
	Timestamp     time.Time `json:"timestamp"`
}

// NewResourceMonitor 创建资源监控器
func NewResourceMonitor(config *AgentConfig, logger *logrus.Logger) *ResourceMonitor {
	return &ResourceMonitor{
		config:        config,
		logger:        logger,
		isRunning:     false,
		currentUsage:  &ResourceUsage{},
		monitoringMap: make(map[string]*ProcessMonitor),
		stopChan:      make(chan struct{}),
	}
}

// Start 启动资源监控
func (rm *ResourceMonitor) Start(ctx context.Context) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if rm.isRunning {
		return nil
	}

	rm.logger.Info("Starting resource monitor")
	rm.isRunning = true

	// 启动系统资源监控协程
	go rm.monitorSystemResources(ctx)

	return nil
}

// Stop 停止资源监控
func (rm *ResourceMonitor) Stop(ctx context.Context) error {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	if !rm.isRunning {
		return nil
	}

	rm.logger.Info("Stopping resource monitor")
	rm.isRunning = false

	close(rm.stopChan)

	return nil
}

// StartMonitoring 开始监控特定执行
func (rm *ResourceMonitor) StartMonitoring(ctx context.Context, executionID string) *ResourceUsage {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	monitor := &ProcessMonitor{
		ExecutionID: executionID,
		StartTime:   time.Now(),
		MaxMemory:   0,
		TotalCPU:    0,
		SampleCount: 0,
	}

	rm.monitoringMap[executionID] = monitor

	// 启动进程监控协程
	go rm.monitorProcess(ctx, monitor)

	// 返回一个ResourceUsage指针，会在监控结束时填充数据
	return &ResourceUsage{}
}

// StopMonitoring 停止监控特定执行
func (rm *ResourceMonitor) StopMonitoring(executionID string) *ResourceUsage {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	monitor, exists := rm.monitoringMap[executionID]
	if !exists {
		return &ResourceUsage{}
	}

	delete(rm.monitoringMap, executionID)

	// 计算平均值并返回结果
	monitor.mutex.RLock()
	defer monitor.mutex.RUnlock()

	avgCPU := 0.0
	if monitor.SampleCount > 0 {
		avgCPU = monitor.TotalCPU / float64(monitor.SampleCount)
	}

	return &ResourceUsage{
		MaxMemoryMB:   monitor.MaxMemory,
		AvgCPUPercent: avgCPU,
		DiskReadMB:    monitor.DiskRead,
		DiskWriteMB:   monitor.DiskWrite,
		NetworkInMB:   monitor.NetworkIn,
		NetworkOutMB:  monitor.NetworkOut,
	}
}

// GetCurrentUsage 获取当前系统资源使用情况
func (rm *ResourceMonitor) GetCurrentUsage() *ResourceUsage {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	return &ResourceUsage{
		MaxMemoryMB:   rm.currentUsage.MaxMemoryMB,
		AvgCPUPercent: rm.currentUsage.AvgCPUPercent,
		DiskReadMB:    rm.currentUsage.DiskReadMB,
		DiskWriteMB:   rm.currentUsage.DiskWriteMB,
		NetworkInMB:   rm.currentUsage.NetworkInMB,
		NetworkOutMB:  rm.currentUsage.NetworkOutMB,
	}
}

// GetSystemMetrics 获取系统指标
func (rm *ResourceMonitor) GetSystemMetrics() *SystemMetrics {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// 转换为MB
	memoryUsedMB := float64(m.Alloc) / 1024 / 1024
	memoryTotalMB := float64(m.Sys) / 1024 / 1024

	return &SystemMetrics{
		CPUPercent:    rm.getCurrentCPUPercent(),
		MemoryUsedMB:  memoryUsedMB,
		MemoryTotalMB: memoryTotalMB,
		DiskUsedMB:    rm.getCurrentDiskUsage(),
		DiskTotalMB:   rm.getTotalDiskSpace(),
		LoadAverage:   rm.getLoadAverage(),
		Timestamp:     time.Now(),
	}
}

// CheckResourceLimits 检查资源限制
func (rm *ResourceMonitor) CheckResourceLimits() error {
	metrics := rm.GetSystemMetrics()

	// 检查内存限制
	if rm.config.MaxMemoryMB > 0 && metrics.MemoryUsedMB > float64(rm.config.MaxMemoryMB) {
		return fmt.Errorf("memory usage %.2f MB exceeds limit %d MB",
			metrics.MemoryUsedMB, rm.config.MaxMemoryMB)
	}

	// 检查CPU限制
	if rm.config.MaxCPUPercent > 0 && metrics.CPUPercent > float64(rm.config.MaxCPUPercent) {
		return fmt.Errorf("CPU usage %.2f%% exceeds limit %d%%",
			metrics.CPUPercent, rm.config.MaxCPUPercent)
	}

	// 检查磁盘限制
	if rm.config.MaxDiskUsageMB > 0 && metrics.DiskUsedMB > float64(rm.config.MaxDiskUsageMB) {
		return fmt.Errorf("disk usage %.2f MB exceeds limit %d MB",
			metrics.DiskUsedMB, rm.config.MaxDiskUsageMB)
	}

	return nil
}

// monitorSystemResources 监控系统资源
func (rm *ResourceMonitor) monitorSystemResources(ctx context.Context) {
	ticker := time.NewTicker(5 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rm.stopChan:
			return
		case <-ticker.C:
			rm.updateSystemUsage()
		}
	}
}

// monitorProcess 监控特定进程
func (rm *ResourceMonitor) monitorProcess(ctx context.Context, monitor *ProcessMonitor) {
	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			rm.updateProcessUsage(monitor)
		}
	}
}

// updateSystemUsage 更新系统使用情况
func (rm *ResourceMonitor) updateSystemUsage() {
	rm.mutex.Lock()
	defer rm.mutex.Unlock()

	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	rm.currentUsage.MaxMemoryMB = float64(m.Alloc) / 1024 / 1024
	rm.currentUsage.AvgCPUPercent = rm.getCurrentCPUPercent()
	rm.currentUsage.DiskReadMB = rm.getCurrentDiskUsage()
	rm.currentUsage.DiskWriteMB = rm.getCurrentDiskUsage()
}

// updateProcessUsage 更新进程使用情况
func (rm *ResourceMonitor) updateProcessUsage(monitor *ProcessMonitor) {
	monitor.mutex.Lock()
	defer monitor.mutex.Unlock()

	// 模拟获取进程资源使用情况
	// 在实际实现中，这里应该使用系统调用获取真实的进程资源使用情况
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	currentMemory := float64(m.Alloc) / 1024 / 1024
	if currentMemory > monitor.MaxMemory {
		monitor.MaxMemory = currentMemory
	}

	currentCPU := rm.getCurrentCPUPercent()
	monitor.TotalCPU += currentCPU
	monitor.SampleCount++

	// 模拟磁盘和网络IO
	monitor.DiskRead += 0.1
	monitor.DiskWrite += 0.1
	monitor.NetworkIn += 0.05
	monitor.NetworkOut += 0.05
}

// getCurrentCPUPercent 获取当前CPU使用率
func (rm *ResourceMonitor) getCurrentCPUPercent() float64 {
	// 简化实现，返回模拟值
	// 在实际实现中，应该使用系统调用获取真实的CPU使用率
	return float64(runtime.NumGoroutine()) * 0.1
}

// getCurrentDiskUsage 获取当前磁盘使用量
func (rm *ResourceMonitor) getCurrentDiskUsage() float64 {
	// 简化实现，返回模拟值
	// 在实际实现中，应该使用系统调用获取真实的磁盘使用量
	return 100.0
}

// getTotalDiskSpace 获取总磁盘空间
func (rm *ResourceMonitor) getTotalDiskSpace() float64 {
	// 简化实现，返回模拟值
	// 在实际实现中，应该使用系统调用获取真实的磁盘空间
	return 1000.0
}

// getLoadAverage 获取系统负载
func (rm *ResourceMonitor) getLoadAverage() []float64 {
	// 简化实现，返回模拟值
	// 在实际实现中，应该读取 /proc/loadavg 文件
	return []float64{0.5, 0.3, 0.2}
}

// IsResourceLimitExceeded 检查是否超过资源限制
func (rm *ResourceMonitor) IsResourceLimitExceeded() bool {
	return rm.CheckResourceLimits() != nil
}

// GetActiveMonitors 获取活跃的监控器数量
func (rm *ResourceMonitor) GetActiveMonitors() int {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	return len(rm.monitoringMap)
}

// GetMonitoringInfo 获取监控信息
func (rm *ResourceMonitor) GetMonitoringInfo() map[string]interface{} {
	rm.mutex.RLock()
	defer rm.mutex.RUnlock()

	info := make(map[string]interface{})
	info["is_running"] = rm.isRunning
	info["active_monitors"] = len(rm.monitoringMap)
	info["current_usage"] = rm.currentUsage
	info["system_metrics"] = rm.GetSystemMetrics()

	return info
}
