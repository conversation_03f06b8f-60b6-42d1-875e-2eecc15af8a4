-- AI运维管理平台数据库优化脚本
-- Database Optimization Script for AI Ops Platform

-- 1. 性能优化索引
-- Performance Optimization Indexes

-- 聊天消息表优化索引
CREATE INDEX IF NOT EXISTS idx_chat_messages_session_created ON chat_messages(session_id, created_at);
CREATE INDEX IF NOT EXISTS idx_chat_messages_type ON chat_messages(message_type);
CREATE INDEX IF NOT EXISTS idx_chat_messages_intent ON chat_messages(intent);

-- 操作日志表优化索引
CREATE INDEX IF NOT EXISTS idx_operation_logs_created_at ON operation_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_operation_logs_user_created ON operation_logs(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_operation_logs_operation ON operation_logs(operation_type);
CREATE INDEX IF NOT EXISTS idx_operation_logs_status ON operation_logs(status);

-- 告警表优化索引
CREATE INDEX IF NOT EXISTS idx_alerts_severity ON alerts(severity);
CREATE INDEX IF NOT EXISTS idx_alerts_status ON alerts(status);
CREATE INDEX IF NOT EXISTS idx_alerts_created_at ON alerts(created_at);
CREATE INDEX IF NOT EXISTS idx_alerts_host_severity ON alerts(host_id, severity);

-- 主机表复合索引
CREATE INDEX IF NOT EXISTS idx_hosts_env_status ON hosts(environment, status);
CREATE INDEX IF NOT EXISTS idx_hosts_group_status ON hosts(group_name, status);
CREATE INDEX IF NOT EXISTS idx_hosts_created_by ON hosts(created_by);

-- 聊天会话表索引
CREATE INDEX IF NOT EXISTS idx_chat_sessions_user_created ON chat_sessions(user_id, created_at);
CREATE INDEX IF NOT EXISTS idx_chat_sessions_updated_at ON chat_sessions(updated_at);

-- 2. 性能优化配置
-- Performance Configuration

-- 启用WAL模式以提高并发性能
PRAGMA journal_mode = WAL;

-- 设置同步模式为NORMAL以平衡性能和安全性
PRAGMA synchronous = NORMAL;

-- 增加缓存大小到128MB
PRAGMA cache_size = -131072;

-- 临时表存储在内存中
PRAGMA temp_store = MEMORY;

-- 设置内存映射大小为512MB
PRAGMA mmap_size = 536870912;

-- 启用外键约束
PRAGMA foreign_keys = ON;

-- 设置忙等待超时为30秒
PRAGMA busy_timeout = 30000;

-- 3. 统计信息更新
-- Update Statistics

-- 更新所有表的统计信息以优化查询计划
ANALYZE;

-- 4. 数据库维护
-- Database Maintenance

-- 重新组织数据库以减少碎片
VACUUM;

-- 5. 查看优化结果
-- View Optimization Results

-- 显示所有索引
SELECT 
    name as index_name,
    tbl_name as table_name,
    sql as index_definition
FROM sqlite_master 
WHERE type = 'index' 
    AND name NOT LIKE 'sqlite_%'
ORDER BY tbl_name, name;

-- 显示表统计信息
SELECT 
    name as table_name,
    (SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND tbl_name=m.name) as index_count
FROM sqlite_master m
WHERE type = 'table' 
    AND name NOT LIKE 'sqlite_%'
ORDER BY name;
