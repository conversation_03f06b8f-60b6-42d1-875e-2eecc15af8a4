package workflow

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
)

// processStepInput 处理步骤输入
func (we *WorkflowEngine) processStepInput(ctx context.Context, instance *WorkflowInstance, step *WorkflowStep, userInput string) error {
	// 验证输入
	if err := we.validateStepInput(step, userInput); err != nil {
		return fmt.Errorf("input validation failed: %w", err)
	}

	// 解析输入数据
	inputData, err := we.parseStepInput(step, userInput)
	if err != nil {
		return fmt.Errorf("failed to parse input: %w", err)
	}

	// 存储输入数据
	for key, value := range inputData {
		instance.CollectedData[key] = value
	}

	// 更新实例状态
	instance.Status = StatusRunning
	instance.LastActivity = time.Now()

	return nil
}

// validateStepInput 验证步骤输入
func (we *WorkflowEngine) validateStepInput(step *WorkflowStep, userInput string) error {
	// 获取输入要求
	requirements, exists := step.Parameters["input_requirements"]
	if !exists {
		return nil // 没有输入要求
	}

	// 转换为输入要求结构
	reqData, err := json.Marshal(requirements)
	if err != nil {
		return fmt.Errorf("failed to marshal requirements: %w", err)
	}

	var inputReqs []InputRequirement
	if err := json.Unmarshal(reqData, &inputReqs); err != nil {
		return fmt.Errorf("failed to unmarshal requirements: %w", err)
	}

	// 验证每个要求
	for _, req := range inputReqs {
		if req.Required && strings.TrimSpace(userInput) == "" {
			return fmt.Errorf("required field %s is missing", req.Name)
		}

		// 执行验证规则
		if req.Validation != nil {
			if err := we.validateField(userInput, req.Validation); err != nil {
				return fmt.Errorf("validation failed for %s: %w", req.Name, err)
			}
		}
	}

	return nil
}

// parseStepInput 解析步骤输入
func (we *WorkflowEngine) parseStepInput(step *WorkflowStep, userInput string) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 根据步骤类型解析输入
	switch step.Type {
	case StepTypeInput:
		// 简单文本输入
		if paramName, exists := step.Parameters["parameter_name"].(string); exists {
			result[paramName] = userInput
		} else {
			result["user_input"] = userInput
		}

	default:
		result["user_input"] = userInput
	}

	return result, nil
}

// validateField 验证字段
func (we *WorkflowEngine) validateField(value string, validation *Validation) error {
	// 长度验证
	if validation.MinLen > 0 && len(value) < validation.MinLen {
		return fmt.Errorf("value too short, minimum length is %d", validation.MinLen)
	}

	if validation.MaxLen > 0 && len(value) > validation.MaxLen {
		return fmt.Errorf("value too long, maximum length is %d", validation.MaxLen)
	}

	// 模式验证
	if validation.Pattern != "" {
		// 这里可以添加正则表达式验证
		// 为了简化，暂时跳过
	}

	return nil
}

// saveInstance 保存工作流实例
func (we *WorkflowEngine) saveInstance(instance *WorkflowInstance) error {
	// 序列化实例数据
	variablesJSON, _ := json.Marshal(instance.Variables)
	collectedDataJSON, _ := json.Marshal(instance.CollectedData)
	executionPathJSON, _ := json.Marshal(instance.ExecutionPath)
	metadataJSON, _ := json.Marshal(instance.Metadata)

	// 创建数据库记录
	dbInstance := &model.WorkflowInstance{
		ID:            instance.ID,
		DefinitionID:  instance.DefinitionID,
		SessionID:     instance.SessionID,
		UserID:        instance.UserID,
		Status:        string(instance.Status),
		CurrentStep:   instance.CurrentStep,
		Variables:     string(variablesJSON),
		CollectedData: string(collectedDataJSON),
		ExecutionPath: string(executionPathJSON),
		StartTime:     instance.StartTime,
		EndTime:       instance.EndTime,
		LastActivity:  instance.LastActivity,
		ErrorMessage:  instance.ErrorMessage,
		RetryCount:    instance.RetryCount,
		Priority:      instance.Priority,
		Metadata:      string(metadataJSON),
	}

	// 保存到数据库
	return we.db.Save(dbInstance).Error
}

// loadWorkflowDefinitions 加载工作流定义
func (we *WorkflowEngine) loadWorkflowDefinitions() error {
	// 注册内置工作流定义
	we.registerBuiltinWorkflows()

	// 从数据库加载自定义工作流定义
	var dbDefinitions []model.WorkflowDefinition
	if err := we.db.Find(&dbDefinitions).Error; err != nil {
		return fmt.Errorf("failed to load workflow definitions: %w", err)
	}

	for _, dbDef := range dbDefinitions {
		definition := &WorkflowDefinition{}
		if err := json.Unmarshal([]byte(dbDef.Definition), definition); err != nil {
			we.logger.WithError(err).Warnf("Failed to unmarshal workflow definition %s", dbDef.ID)
			continue
		}

		we.definitions[definition.ID] = definition
	}

	we.logger.Infof("Loaded %d workflow definitions", len(we.definitions))
	return nil
}

// registerBuiltinWorkflows 注册内置工作流
func (we *WorkflowEngine) registerBuiltinWorkflows() {
	// 主机管理工作流
	hostManagementWorkflow := &WorkflowDefinition{
		ID:          "host_management",
		Name:        "主机管理工作流",
		Description: "智能引导用户进行主机管理操作",
		Version:     "1.0.0",
		Category:    "host_management",
		Enabled:     true,
		Timeout:     30 * time.Minute,
		Triggers: []WorkflowTrigger{
			{
				Type: "intent",
				Conditions: map[string]interface{}{
					"intent": "host_management",
				},
				Priority: 10,
			},
		},
		Steps: []WorkflowStep{
			{
				ID:          "check_hosts",
				Name:        "检查现有主机",
				Type:        StepTypeExecution,
				Description: "检查数据库中是否有现有主机",
				Action:      "check_existing_hosts",
				NextSteps:   []string{"analyze_request"},
				Timeout:     5 * time.Second,
			},
			{
				ID:          "analyze_request",
				Name:        "分析用户请求",
				Type:        StepTypeAIAnalysis,
				Description: "使用AI分析用户的具体需求",
				Action:      "analyze_host_request",
				NextSteps:   []string{"guide_user"},
				Timeout:     10 * time.Second,
				AIGuidance: &AIGuidance{
					Enabled: true,
					Prompt:  "分析用户的主机管理需求，确定下一步操作",
					Context: "主机管理场景",
				},
			},
			{
				ID:          "guide_user",
				Name:        "引导用户操作",
				Type:        StepTypeMessage,
				Description: "根据分析结果引导用户",
				Action:      "send_guidance_message",
				NextSteps:   []string{"collect_host_info"},
				Timeout:     1 * time.Second,
			},
			{
				ID:          "collect_host_info",
				Name:        "收集主机信息",
				Type:        StepTypeInput,
				Description: "收集用户提供的主机信息",
				Action:      "collect_host_data",
				Parameters: map[string]interface{}{
					"input_requirements": []InputRequirement{
						{
							Name:        "host_name",
							Type:        "string",
							Description: "主机名称",
							Required:    true,
							Validation: &Validation{
								MinLen: 1,
								MaxLen: 100,
							},
						},
						{
							Name:        "ip_address",
							Type:        "string",
							Description: "IP地址",
							Required:    true,
							Validation: &Validation{
								Pattern: `^(\d{1,3}\.){3}\d{1,3}$`,
							},
						},
						{
							Name:        "ssh_port",
							Type:        "number",
							Description: "SSH端口",
							Required:    false,
							Default:     22,
						},
						{
							Name:        "username",
							Type:        "string",
							Description: "用户名",
							Required:    true,
						},
					},
				},
				NextSteps: []string{"validate_host_info"},
				Timeout:   5 * time.Minute,
			},
			{
				ID:          "validate_host_info",
				Name:        "验证主机信息",
				Type:        StepTypeValidation,
				Description: "验证用户提供的主机信息",
				Action:      "validate_host_data",
				NextSteps:   []string{"save_host", "collect_host_info"},
				Timeout:     10 * time.Second,
			},
			{
				ID:          "save_host",
				Name:        "保存主机信息",
				Type:        StepTypeExecution,
				Description: "将主机信息保存到数据库",
				Action:      "save_host_to_database",
				NextSteps:   []string{"test_connection"},
				Timeout:     5 * time.Second,
			},
			{
				ID:          "test_connection",
				Name:        "测试连接",
				Type:        StepTypeExecution,
				Description: "测试到主机的SSH连接",
				Action:      "test_ssh_connection",
				NextSteps:   []string{"completion_message"},
				Timeout:     30 * time.Second,
				RetryPolicy: &RetryPolicy{
					MaxAttempts: 3,
					Delay:       5 * time.Second,
					Backoff:     "linear",
				},
			},
			{
				ID:          "completion_message",
				Name:        "完成消息",
				Type:        StepTypeMessage,
				Description: "发送完成消息给用户",
				Action:      "send_completion_message",
				NextSteps:   []string{}, // 空表示工作流结束
				Timeout:     1 * time.Second,
			},
		},
		Variables: map[string]interface{}{
			"max_retry_attempts": 3,
			"connection_timeout": 30,
		},
	}

	we.definitions[hostManagementWorkflow.ID] = hostManagementWorkflow
}

// recoverInstances 恢复工作流实例
func (we *WorkflowEngine) recoverInstances(ctx context.Context) error {
	var dbInstances []model.WorkflowInstance
	err := we.db.Where("status IN ?", []string{
		string(StatusRunning),
		string(StatusWaiting),
		string(StatusPending),
	}).Find(&dbInstances).Error

	if err != nil {
		return fmt.Errorf("failed to query workflow instances: %w", err)
	}

	for _, dbInstance := range dbInstances {
		instance := &WorkflowInstance{
			ID:           dbInstance.ID,
			DefinitionID: dbInstance.DefinitionID,
			SessionID:    dbInstance.SessionID,
			UserID:       dbInstance.UserID,
			Status:       WorkflowStatus(dbInstance.Status),
			CurrentStep:  dbInstance.CurrentStep,
			StartTime:    dbInstance.StartTime,
			LastActivity: dbInstance.LastActivity,
			ErrorMessage: dbInstance.ErrorMessage,
			RetryCount:   dbInstance.RetryCount,
			Priority:     dbInstance.Priority,
		}

		// 反序列化JSON字段
		if dbInstance.Variables != "" {
			json.Unmarshal([]byte(dbInstance.Variables), &instance.Variables)
		}
		if dbInstance.CollectedData != "" {
			json.Unmarshal([]byte(dbInstance.CollectedData), &instance.CollectedData)
		}
		if dbInstance.ExecutionPath != "" {
			json.Unmarshal([]byte(dbInstance.ExecutionPath), &instance.ExecutionPath)
		}
		if dbInstance.Metadata != "" {
			json.Unmarshal([]byte(dbInstance.Metadata), &instance.Metadata)
		}

		// 初始化空映射
		if instance.Variables == nil {
			instance.Variables = make(map[string]interface{})
		}
		if instance.CollectedData == nil {
			instance.CollectedData = make(map[string]interface{})
		}
		if instance.Metadata == nil {
			instance.Metadata = make(map[string]interface{})
		}

		we.instances[instance.ID] = instance

		// 如果实例处于运行状态，恢复执行
		if instance.Status == StatusRunning {
			go we.executeWorkflow(ctx, instance)
		}
	}

	we.logger.Infof("Recovered %d workflow instances", len(dbInstances))
	return nil
}

// saveActiveInstances 保存活跃实例
func (we *WorkflowEngine) saveActiveInstances() error {
	we.mutex.RLock()
	defer we.mutex.RUnlock()

	for _, instance := range we.instances {
		if instance.Status == StatusRunning || instance.Status == StatusWaiting {
			if err := we.saveInstance(instance); err != nil {
				we.logger.WithError(err).Errorf("Failed to save instance %s", instance.ID)
			}
		}
	}

	return nil
}

// handleStepError 处理步骤错误
func (we *WorkflowEngine) handleStepError(instance *WorkflowInstance, step *WorkflowStep, err error) {
	we.logger.WithFields(logrus.Fields{
		"instance_id": instance.ID,
		"step_id":     step.ID,
		"error":       err.Error(),
	}).Error("Step execution failed")

	// 检查错误处理策略
	if step.ErrorHandling != nil {
		switch step.ErrorHandling.Strategy {
		case "retry":
			if instance.RetryCount < step.ErrorHandling.MaxRetries {
				instance.RetryCount++
				we.logger.Infof("Retrying step %s (attempt %d)", step.ID, instance.RetryCount)
				return // 不改变状态，继续重试
			}
		case "skip":
			we.logger.Infof("Skipping failed step %s", step.ID)
			// 跳到下一步
			if len(step.NextSteps) > 0 {
				instance.CurrentStep = step.NextSteps[0]
				return
			}
		case "custom":
			if step.ErrorHandling.FallbackStep != "" {
				instance.CurrentStep = step.ErrorHandling.FallbackStep
				return
			}
		}
	}

	// 默认失败处理
	instance.Status = StatusFailed
	instance.ErrorMessage = err.Error()
}

// processStepResult 处理步骤结果
func (we *WorkflowEngine) processStepResult(instance *WorkflowInstance, step *WorkflowStep, result *StepResult) error {
	if !result.Success {
		return fmt.Errorf("step failed: %s", result.Error)
	}

	// 合并步骤数据
	for key, value := range result.Data {
		instance.Variables[key] = value
	}

	// 检查是否需要等待用户输入
	if result.WaitForUser {
		instance.Status = StatusWaiting
		return nil
	}

	// 确定下一步
	if result.NextStep != "" {
		instance.CurrentStep = result.NextStep
	} else if len(step.NextSteps) > 0 {
		// 使用步骤定义的下一步
		instance.CurrentStep = step.NextSteps[0]
	} else {
		// 没有下一步，工作流完成
		instance.CurrentStep = ""
	}

	return nil
}
