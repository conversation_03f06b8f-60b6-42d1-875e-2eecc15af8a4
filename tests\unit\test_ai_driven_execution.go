package main

import (
	"context"
	"fmt"
	"log"
	"strings"
	"time"

	"aiops-platform/internal/service"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// 🚀 AI驱动执行引擎测试程序

func main() {
	fmt.Println("🚀 AI驱动执行引擎架构测试")
	fmt.Println(strings.Repeat("=", 50))

	// 初始化日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 初始化数据库
	db, err := gorm.Open(sqlite.Open("test_ai_driven.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}

	// 创建模拟的HostService
	hostService := &MockHostService{}
	confirmationManager := &service.ConfirmationManager{}

	// 🚀 创建革命性执行引擎
	engine := service.NewUnifiedExecutionEngine(db, logger, hostService, confirmationManager)

	// 测试场景
	testScenarios := []TestScenario{
		{
			Name:        "AI生成的SQL查询指令",
			Description: "测试DeepSeek生成的完整SQL指令执行",
			Request:     createSQLInstructionRequest(),
		},
		{
			Name:        "AI生成的SSH命令指令", 
			Description: "测试DeepSeek生成的Shell命令执行",
			Request:     createSSHInstructionRequest(),
		},
		{
			Name:        "传统Intent参数兼容",
			Description: "测试传统Intent参数的向后兼容性",
			Request:     createLegacyIntentRequest(),
		},
		{
			Name:        "智能指令生成",
			Description: "测试从Intent动态生成AI指令",
			Request:     createDynamicGenerationRequest(),
		},
	}

	// 执行测试
	for i, scenario := range testScenarios {
		fmt.Printf("\n🎯 测试场景 %d: %s\n", i+1, scenario.Name)
		fmt.Printf("📝 描述: %s\n", scenario.Description)
		fmt.Println(strings.Repeat("-", 40))

		result, err := engine.Execute(context.Background(), scenario.Request)
		if err != nil {
			fmt.Printf("❌ 执行失败: %s\n", err.Error())
			continue
		}

		fmt.Printf("✅ 执行成功: %t\n", result.Success)
		fmt.Printf("📊 执行时间: %v\n", result.ExecutionTime)
		fmt.Printf("🎯 操作类型: %s\n", result.Action)
		fmt.Printf("📋 结果内容:\n%s\n", result.Content)

		if result.Data != nil {
			fmt.Printf("📈 数据详情: %+v\n", result.Data)
		}
	}

	fmt.Println("\n🚀 AI驱动架构测试完成！")
}

// TestScenario 测试场景
type TestScenario struct {
	Name        string
	Description string
	Request     *service.ExecutionRequest
}

// createSQLInstructionRequest 创建SQL指令请求
func createSQLInstructionRequest() *service.ExecutionRequest {
	// 🚀 模拟DeepSeek生成的完整SQL指令
	aiInstructions := []*service.AIGeneratedInstruction{
		{
			InstructionType: "sql",
			ExecutorType:    "database",
			Content:         "SELECT id, ip_address, name, status, environment FROM hosts WHERE deleted_at IS NULL LIMIT 10",
			Description:     "查询主机列表",
			SecurityLevel:   "safe",
			RequireConfirm:  false,
			Timeout:         30,
			Parameters: map[string]interface{}{
				"operation": "select",
				"table":     "hosts",
			},
			ExpectedResult: &service.ExpectedResult{
				DataType: "table",
				Columns:  []string{"id", "ip_address", "name", "status", "environment"},
				Format:   "table",
			},
		},
	}

	return &service.ExecutionRequest{
		SessionID:   "test-session-1",
		UserID:      1,
		OriginalMsg: "查看所有主机状态",
		Intent: &service.IntentResult{
			Type:       "database_operations",
			Confidence: 0.95,
			Parameters: map[string]interface{}{
				"operation": "select",
				"table":     "hosts",
			},
		},
		AIInstructions: aiInstructions,
	}
}

// createSSHInstructionRequest 创建SSH指令请求
func createSSHInstructionRequest() *service.ExecutionRequest {
	// 🚀 模拟DeepSeek生成的Shell命令指令
	aiInstructions := []*service.AIGeneratedInstruction{
		{
			InstructionType: "shell",
			ExecutorType:    "ssh",
			Content:         "uptime && free -h && df -h",
			Description:     "系统状态检查",
			SecurityLevel:   "safe",
			RequireConfirm:  false,
			Timeout:         30,
			TargetResource: &service.TargetResource{
				Type:       "host",
				Identifier: "*************",
			},
			Parameters: map[string]interface{}{
				"operation": "system_check",
			},
		},
	}

	return &service.ExecutionRequest{
		SessionID:   "test-session-2",
		UserID:      1,
		OriginalMsg: "检查*************的系统状态",
		Intent: &service.IntentResult{
			Type:       "ssh_operations",
			Confidence: 0.92,
			Parameters: map[string]interface{}{
				"operation": "system_check",
				"target_host": map[string]interface{}{
					"ip": "*************",
				},
			},
		},
		AIInstructions: aiInstructions,
	}
}

// createLegacyIntentRequest 创建传统Intent请求
func createLegacyIntentRequest() *service.ExecutionRequest {
	return &service.ExecutionRequest{
		SessionID:   "test-session-3",
		UserID:      1,
		OriginalMsg: "查看主机列表",
		Intent: &service.IntentResult{
			Type:       "database_operations",
			Confidence: 0.88,
			Parameters: map[string]interface{}{
				"operation": "select",
				"table":     "hosts",
				"sql":       "SELECT * FROM hosts WHERE deleted_at IS NULL",
				"description": "查询所有主机",
			},
		},
		// 没有AIInstructions，测试向后兼容
	}
}

// createDynamicGenerationRequest 创建动态生成请求
func createDynamicGenerationRequest() *service.ExecutionRequest {
	return &service.ExecutionRequest{
		SessionID:   "test-session-4",
		UserID:      1,
		OriginalMsg: "在*************上检查CPU使用率",
		Intent: &service.IntentResult{
			Type:       "monitoring_operations",
			Confidence: 0.90,
			Parameters: map[string]interface{}{
				"operation": "cpu_usage",
				"target_host": map[string]interface{}{
					"ip": "*************",
				},
			},
		},
		// 没有AIInstructions，测试动态生成
	}
}

// MockHostService 模拟主机服务
type MockHostService struct{}

func (m *MockHostService) ExecuteCommand(hostID int64, req interface{}) (*MockCommandResponse, error) {
	// 模拟命令执行结果
	return &MockCommandResponse{
		ExitCode:     0,
		Stdout:       "系统运行正常\nCPU使用率: 25%\n内存使用率: 60%\n磁盘使用率: 45%",
		Stderr:       "",
		Duration:     150,
		ExecutedAt:   time.Now(),
		ErrorMessage: "",
	}, nil
}

func (m *MockHostService) UpdateHostStatus(hostID int64, status string) error {
	fmt.Printf("🔄 更新主机 %d 状态为: %s\n", hostID, status)
	return nil
}

func (m *MockHostService) ListHosts(req interface{}) (interface{}, error) {
	return nil, fmt.Errorf("模拟服务未实现")
}

func (m *MockHostService) CreateHost(req interface{}) (interface{}, error) {
	return nil, fmt.Errorf("模拟服务未实现")
}

// MockCommandResponse 模拟命令响应
type MockCommandResponse struct {
	ExitCode     int       `json:"exit_code"`
	Stdout       string    `json:"stdout"`
	Stderr       string    `json:"stderr"`
	Duration     int       `json:"duration"`
	ExecutedAt   time.Time `json:"executed_at"`
	ErrorMessage string    `json:"error_message"`
}
