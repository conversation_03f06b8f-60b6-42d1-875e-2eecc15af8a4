package ai

import (
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// RecommendationEngine 建议引擎
type RecommendationEngine struct {
	logger    *logrus.Logger
	config    *PredictiveConfig
	templates map[InsightType][]RecommendationTemplate
}

// RecommendationTemplate 建议模板
type RecommendationTemplate struct {
	Title       string                                         `json:"title"`
	Description string                                         `json:"description"`
	Action      RecommendationAction                           `json:"action"`
	Priority    PriorityLevel                                  `json:"priority"`
	Impact      ImpactLevel                                    `json:"impact"`
	Effort      EffortLevel                                    `json:"effort"`
	Commands    []string                                       `json:"commands"`
	Benefits    []string                                       `json:"benefits"`
	Risks       []string                                       `json:"risks"`
	Timeline    string                                         `json:"timeline"`
	Condition   func(*PredictiveInsight, *OperationsData) bool `json:"-"`
}

// NewRecommendationEngine 创建建议引擎
func NewRecommendationEngine(logger *logrus.Logger, config *PredictiveConfig) *RecommendationEngine {
	engine := &RecommendationEngine{
		logger:    logger,
		config:    config,
		templates: make(map[InsightType][]RecommendationTemplate),
	}

	// 初始化建议模板
	engine.initializeRecommendationTemplates()

	logger.Info("🎯 建议引擎初始化完成")
	return engine
}

// GenerateRecommendations 生成建议
func (re *RecommendationEngine) GenerateRecommendations(
	insights []PredictiveInsight,
	data *OperationsData,
) []OperationRecommendation {
	start := time.Now()

	re.logger.Info("🎯 开始生成运维建议")

	recommendations := make([]OperationRecommendation, 0)

	// 为每个洞察生成建议
	for _, insight := range insights {
		insightRecs := re.generateRecommendationsForInsight(&insight, data)
		recommendations = append(recommendations, insightRecs...)
	}

	// 去重和优化
	recommendations = re.deduplicateRecommendations(recommendations)

	// 按优先级排序
	re.sortRecommendationsByPriority(recommendations)

	// 限制数量
	if len(recommendations) > re.config.MaxRecommendations {
		recommendations = recommendations[:re.config.MaxRecommendations]
	}

	processingTime := time.Since(start)
	re.logger.WithFields(logrus.Fields{
		"recommendations_count": len(recommendations),
		"processing_time":       processingTime,
	}).Info("🎯 运维建议生成完成")

	return recommendations
}

// generateRecommendationsForInsight 为单个洞察生成建议
func (re *RecommendationEngine) generateRecommendationsForInsight(
	insight *PredictiveInsight,
	data *OperationsData,
) []OperationRecommendation {
	recommendations := make([]OperationRecommendation, 0)

	// 获取对应类型的模板
	templates, exists := re.templates[insight.Type]
	if !exists {
		return recommendations
	}

	// 遍历模板，生成适用的建议
	for _, template := range templates {
		if template.Condition == nil || template.Condition(insight, data) {
			rec := re.createRecommendationFromTemplate(template, insight, data)
			recommendations = append(recommendations, rec)
		}
	}

	return recommendations
}

// createRecommendationFromTemplate 从模板创建建议
func (re *RecommendationEngine) createRecommendationFromTemplate(
	template RecommendationTemplate,
	insight *PredictiveInsight,
	data *OperationsData,
) OperationRecommendation {
	return OperationRecommendation{
		ID:          re.generateRecommendationID(template.Action, insight.Type),
		Title:       template.Title,
		Description: re.personalizeDescription(template.Description, insight, data),
		Action:      template.Action,
		Priority:    re.adjustPriority(template.Priority, insight),
		Impact:      template.Impact,
		Effort:      template.Effort,
		Commands:    re.personalizeCommands(template.Commands, insight, data),
		Parameters:  re.extractParameters(insight, data),
		Benefits:    template.Benefits,
		Risks:       template.Risks,
		Timeline:    template.Timeline,
	}
}

// initializeRecommendationTemplates 初始化建议模板
func (re *RecommendationEngine) initializeRecommendationTemplates() {
	// 性能下降相关建议
	re.templates[InsightTypePerformanceDegradation] = []RecommendationTemplate{
		{
			Title:       "重启高负载服务",
			Description: "重启资源使用率过高的服务以释放内存和重置连接",
			Action:      ActionMaintain,
			Priority:    PriorityHigh,
			Impact:      ImpactMedium,
			Effort:      EffortLow,
			Commands:    []string{"systemctl restart {service}", "docker restart {container}"},
			Benefits:    []string{"快速释放资源", "重置服务状态", "恢复正常性能"},
			Risks:       []string{"短暂服务中断", "可能丢失未保存数据"},
			Timeline:    "立即执行",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return insight.Confidence > 0.7
			},
		},
		{
			Title:       "清理系统缓存",
			Description: "清理系统缓存和临时文件以释放磁盘空间",
			Action:      ActionCleanup,
			Priority:    PriorityMedium,
			Impact:      ImpactLow,
			Effort:      EffortLow,
			Commands:    []string{"echo 3 > /proc/sys/vm/drop_caches", "find /tmp -type f -atime +7 -delete"},
			Benefits:    []string{"释放磁盘空间", "提升I/O性能", "清理无用文件"},
			Risks:       []string{"可能影响缓存性能"},
			Timeline:    "30分钟内",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return data.SystemMetrics.DiskUtilization > 0.8
			},
		},
		{
			Title:       "优化数据库连接池",
			Description: "调整数据库连接池大小以优化资源使用",
			Action:      ActionOptimize,
			Priority:    PriorityMedium,
			Impact:      ImpactHigh,
			Effort:      EffortMedium,
			Commands:    []string{"修改数据库配置文件", "重启数据库服务"},
			Benefits:    []string{"提升数据库性能", "减少连接等待", "优化资源使用"},
			Risks:       []string{"需要重启数据库", "配置错误可能影响性能"},
			Timeline:    "1-2小时",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return data.PerformanceMetrics.DatabaseConnections > 40
			},
		},
	}

	// 资源耗尽相关建议
	re.templates[InsightTypeResourceExhaustion] = []RecommendationTemplate{
		{
			Title:       "扩容服务器资源",
			Description: "增加CPU、内存或存储资源以应对资源不足",
			Action:      ActionScale,
			Priority:    PriorityCritical,
			Impact:      ImpactHigh,
			Effort:      EffortHigh,
			Commands:    []string{"云平台扩容操作", "添加新服务器节点"},
			Benefits:    []string{"彻底解决资源不足", "提升系统容量", "改善用户体验"},
			Risks:       []string{"增加成本", "可能需要停机维护"},
			Timeline:    "2-4小时",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return insight.Severity == SeverityCritical
			},
		},
		{
			Title:       "启用负载均衡",
			Description: "配置负载均衡器分散请求压力",
			Action:      ActionOptimize,
			Priority:    PriorityHigh,
			Impact:      ImpactHigh,
			Effort:      EffortMedium,
			Commands:    []string{"配置nginx负载均衡", "启动负载均衡服务"},
			Benefits:    []string{"分散服务器压力", "提高可用性", "改善响应时间"},
			Risks:       []string{"配置复杂", "需要额外维护"},
			Timeline:    "1-3小时",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return len(data.HostMetrics) > 1
			},
		},
		{
			Title:       "清理日志文件",
			Description: "清理过期日志文件释放磁盘空间",
			Action:      ActionCleanup,
			Priority:    PriorityHigh,
			Impact:      ImpactMedium,
			Effort:      EffortLow,
			Commands:    []string{"find /var/log -name '*.log' -mtime +30 -delete", "logrotate -f /etc/logrotate.conf"},
			Benefits:    []string{"快速释放磁盘空间", "改善I/O性能", "清理无用文件"},
			Risks:       []string{"可能删除有用的历史日志"},
			Timeline:    "15分钟",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return data.SystemMetrics.DiskUtilization > 0.85
			},
		},
	}

	// 系统故障相关建议
	re.templates[InsightTypeSystemFailure] = []RecommendationTemplate{
		{
			Title:       "执行系统健康检查",
			Description: "全面检查系统组件状态和配置",
			Action:      ActionMonitor,
			Priority:    PriorityHigh,
			Impact:      ImpactMedium,
			Effort:      EffortMedium,
			Commands:    []string{"systemctl status", "df -h", "free -m", "top"},
			Benefits:    []string{"发现潜在问题", "确认系统状态", "预防故障发生"},
			Risks:       []string{"检查过程可能影响性能"},
			Timeline:    "30分钟",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return true // 总是适用
			},
		},
		{
			Title:       "创建系统备份",
			Description: "创建关键数据和配置的备份",
			Action:      ActionBackup,
			Priority:    PriorityCritical,
			Impact:      ImpactHigh,
			Effort:      EffortMedium,
			Commands:    []string{"rsync -av /etc/ /backup/etc/", "mysqldump --all-databases > backup.sql"},
			Benefits:    []string{"保护重要数据", "快速恢复能力", "降低故障风险"},
			Risks:       []string{"备份过程占用资源", "需要存储空间"},
			Timeline:    "1-2小时",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return insight.Severity >= SeverityHigh
			},
		},
		{
			Title:       "更新系统补丁",
			Description: "安装最新的安全补丁和系统更新",
			Action:      ActionUpgrade,
			Priority:    PriorityMedium,
			Impact:      ImpactMedium,
			Effort:      EffortMedium,
			Commands:    []string{"yum update -y", "apt update && apt upgrade -y"},
			Benefits:    []string{"修复已知漏洞", "提升系统稳定性", "获得新功能"},
			Risks:       []string{"可能引入新问题", "需要重启系统"},
			Timeline:    "2-4小时",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return data.SecurityMetrics.VulnerabilityCount > 0
			},
		},
	}

	// 安全威胁相关建议
	re.templates[InsightTypeSecurityThreat] = []RecommendationTemplate{
		{
			Title:       "加强访问控制",
			Description: "检查和加强系统访问控制策略",
			Action:      ActionSecure,
			Priority:    PriorityHigh,
			Impact:      ImpactHigh,
			Effort:      EffortMedium,
			Commands:    []string{"检查用户权限", "更新防火墙规则", "审查SSH配置"},
			Benefits:    []string{"提高系统安全性", "防止未授权访问", "降低安全风险"},
			Risks:       []string{"可能影响正常访问", "配置错误可能锁定用户"},
			Timeline:    "1-2小时",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return data.SecurityMetrics.FailedLogins > 5
			},
		},
		{
			Title:       "启用安全监控",
			Description: "启用或加强安全监控和日志记录",
			Action:      ActionMonitor,
			Priority:    PriorityHigh,
			Impact:      ImpactMedium,
			Effort:      EffortLow,
			Commands:    []string{"启用审计日志", "配置入侵检测", "设置安全告警"},
			Benefits:    []string{"及时发现威胁", "记录安全事件", "提供取证信息"},
			Risks:       []string{"增加日志存储需求", "可能影响性能"},
			Timeline:    "30分钟",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return true // 总是适用
			},
		},
	}

	// 优化机会相关建议
	re.templates[InsightTypeOptimizationOpportunity] = []RecommendationTemplate{
		{
			Title:       "优化缓存配置",
			Description: "调整缓存策略和配置以提升性能",
			Action:      ActionOptimize,
			Priority:    PriorityMedium,
			Impact:      ImpactHigh,
			Effort:      EffortMedium,
			Commands:    []string{"调整Redis配置", "优化应用缓存策略"},
			Benefits:    []string{"提升响应速度", "减少数据库压力", "改善用户体验"},
			Risks:       []string{"配置错误可能影响功能", "需要重启服务"},
			Timeline:    "2-3小时",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return data.PerformanceMetrics.CacheHitRate < 0.8
			},
		},
		{
			Title:       "数据库索引优化",
			Description: "分析和优化数据库索引以提升查询性能",
			Action:      ActionOptimize,
			Priority:    PriorityMedium,
			Impact:      ImpactHigh,
			Effort:      EffortHigh,
			Commands:    []string{"分析慢查询日志", "创建缺失索引", "删除无用索引"},
			Benefits:    []string{"大幅提升查询速度", "减少CPU使用", "改善并发性能"},
			Risks:       []string{"索引维护开销", "可能影响写入性能"},
			Timeline:    "4-6小时",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return data.ApplicationMetrics.AverageResponseTime > 200
			},
		},
	}

	// 容量规划相关建议
	re.templates[InsightTypeCapacityPlanning] = []RecommendationTemplate{
		{
			Title:       "制定扩容计划",
			Description: "基于增长趋势制定详细的扩容计划",
			Action:      ActionScale,
			Priority:    PriorityMedium,
			Impact:      ImpactHigh,
			Effort:      EffortHigh,
			Commands:    []string{"评估资源需求", "准备扩容方案", "测试扩容流程"},
			Benefits:    []string{"提前应对增长", "避免性能瓶颈", "保证服务质量"},
			Risks:       []string{"预测可能不准确", "提前投入成本"},
			Timeline:    "1-2周",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return true // 总是适用
			},
		},
		{
			Title:       "优化资源分配",
			Description: "重新分配现有资源以提高利用率",
			Action:      ActionOptimize,
			Priority:    PriorityMedium,
			Impact:      ImpactMedium,
			Effort:      EffortMedium,
			Commands:    []string{"调整服务配置", "重新分配工作负载"},
			Benefits:    []string{"提高资源利用率", "延缓扩容需求", "降低成本"},
			Risks:       []string{"可能影响服务性能", "需要仔细测试"},
			Timeline:    "1-2天",
			Condition: func(insight *PredictiveInsight, data *OperationsData) bool {
				return data.CurrentResourceUsage < 0.9
			},
		},
	}
}

// 辅助方法

func (re *RecommendationEngine) generateRecommendationID(action RecommendationAction, insightType InsightType) string {
	return fmt.Sprintf("%s_%s_%d", action, insightType, time.Now().Unix())
}

func (re *RecommendationEngine) personalizeDescription(
	template string,
	insight *PredictiveInsight,
	data *OperationsData,
) string {
	// 简单的模板替换
	description := template

	// 可以添加更多的个性化逻辑
	if len(insight.AffectedSystems) > 0 {
		description += fmt.Sprintf(" 主要影响系统：%v", insight.AffectedSystems)
	}

	return description
}

func (re *RecommendationEngine) personalizeCommands(
	commands []string,
	insight *PredictiveInsight,
	data *OperationsData,
) []string {
	// 简单的命令个性化
	personalizedCommands := make([]string, len(commands))
	copy(personalizedCommands, commands)

	// 可以根据具体情况替换命令中的占位符
	for i, cmd := range personalizedCommands {
		if len(insight.AffectedSystems) > 0 {
			// 替换服务名称等占位符
			personalizedCommands[i] = strings.ReplaceAll(cmd, "{service}", insight.AffectedSystems[0])
		}
	}

	return personalizedCommands
}

func (re *RecommendationEngine) extractParameters(
	insight *PredictiveInsight,
	data *OperationsData,
) map[string]interface{} {
	parameters := make(map[string]interface{})

	parameters["insight_id"] = insight.ID
	parameters["severity"] = insight.Severity
	parameters["confidence"] = insight.Confidence
	parameters["affected_systems"] = insight.AffectedSystems
	parameters["predicted_time"] = insight.PredictedTime

	return parameters
}

func (re *RecommendationEngine) adjustPriority(
	basePriority PriorityLevel,
	insight *PredictiveInsight,
) PriorityLevel {
	// 根据洞察的严重程度调整建议优先级
	if insight.Severity == SeverityCritical {
		return PriorityCritical
	}
	if insight.Severity == SeverityHigh && basePriority == PriorityMedium {
		return PriorityHigh
	}

	return basePriority
}

func (re *RecommendationEngine) deduplicateRecommendations(
	recommendations []OperationRecommendation,
) []OperationRecommendation {
	// 简单的去重逻辑：基于动作和标题
	seen := make(map[string]bool)
	deduplicated := make([]OperationRecommendation, 0)

	for _, rec := range recommendations {
		key := fmt.Sprintf("%s_%s", rec.Action, rec.Title)
		if !seen[key] {
			seen[key] = true
			deduplicated = append(deduplicated, rec)
		}
	}

	return deduplicated
}

func (re *RecommendationEngine) sortRecommendationsByPriority(recommendations []OperationRecommendation) {
	// 简单的排序：按优先级和影响程度排序
	for i := 0; i < len(recommendations)-1; i++ {
		for j := i + 1; j < len(recommendations); j++ {
			if re.getRecommendationScore(recommendations[i]) < re.getRecommendationScore(recommendations[j]) {
				recommendations[i], recommendations[j] = recommendations[j], recommendations[i]
			}
		}
	}
}

func (re *RecommendationEngine) getRecommendationScore(rec OperationRecommendation) float64 {
	priorityWeight := map[PriorityLevel]float64{
		PriorityCritical: 4.0,
		PriorityHigh:     3.0,
		PriorityMedium:   2.0,
		PriorityLow:      1.0,
	}

	impactWeight := map[ImpactLevel]float64{
		ImpactHigh:   3.0,
		ImpactMedium: 2.0,
		ImpactLow:    1.0,
	}

	effortPenalty := map[EffortLevel]float64{
		EffortLow:    0.0,
		EffortMedium: 0.5,
		EffortHigh:   1.0,
	}

	return priorityWeight[rec.Priority]*2 + impactWeight[rec.Impact] - effortPenalty[rec.Effort]
}
