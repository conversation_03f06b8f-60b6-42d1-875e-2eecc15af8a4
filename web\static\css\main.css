/* AI对话运维管理平台 - 主样式文件 */
/* 注意：基础设计系统已移至 design-system.css，此文件仅包含特定业务样式 */

/* ========================================
   业务特定样式
   ======================================== */

/* Bootstrap导航栏增强 */
.navbar-brand {
    font-weight: var(--font-weight-semibold);
    font-size: var(--font-size-xl);
}

.navbar-nav .nav-link {
    font-weight: var(--font-weight-medium);
    transition: var(--transition-base);
    padding: var(--spacing-2) var(--spacing-3);
    border-radius: var(--radius-md);
}

.navbar-nav .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* AI运维平台功能卡片 */
.feature-cards {
    max-width: 900px;
    margin: 0 auto;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-6);
}

.feature-card {
    background: linear-gradient(135deg, #ffffff 0%, var(--color-gray-50) 100%);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    text-align: center;
    cursor: pointer;
    transition: all var(--transition-base);
    height: 100%;
    box-shadow: var(--shadow-sm);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-lg);
    border-color: var(--color-primary-300);
}

.feature-icon {
    font-size: 2.5rem;
    color: var(--color-primary-500);
    margin-bottom: var(--spacing-3);
}

.feature-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
    margin-bottom: var(--spacing-2);
}

.feature-desc {
    font-size: var(--font-size-sm);
    color: var(--color-gray-600);
    margin-bottom: var(--spacing-3);
    line-height: var(--line-height-relaxed);
}

.feature-example {
    font-size: var(--font-size-xs);
    color: var(--color-primary-600);
    font-style: italic;
    background: var(--color-primary-50);
    padding: var(--spacing-1) var(--spacing-2);
    border-radius: var(--radius-md);
    display: inline-block;
}

/* 快速开始提示组件 */
.quick-start-tips {
    max-width: 700px;
    margin: 0 auto;
    background: linear-gradient(135deg, var(--color-gray-50) 0%, var(--color-gray-100) 100%);
    border-radius: var(--radius-xl);
    padding: var(--spacing-6);
    border: 1px solid var(--color-gray-200);
    box-shadow: var(--shadow-sm);
}

.tips-title {
    font-size: var(--font-size-lg);
    font-weight: var(--font-weight-semibold);
    color: var(--color-gray-900);
    margin-bottom: var(--spacing-4);
    text-align: center;
}

.tips-title i {
    color: var(--color-warning-500);
    margin-right: var(--spacing-2);
}

.tips-content {
    text-align: left;
}

.tip-item {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--spacing-3);
    font-size: var(--font-size-sm);
    color: var(--color-gray-800);
    line-height: var(--line-height-relaxed);
}

.tip-item:last-child {
    margin-bottom: 0;
}

.tip-badge {
    margin-right: var(--spacing-3);
    font-size: var(--font-size-base);
    flex-shrink: 0;
}

/* AI运维操作模板 */
.operation-templates {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: var(--spacing-3);
}

.template-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-3) var(--spacing-4);
    background: linear-gradient(135deg, #ffffff 0%, var(--color-gray-50) 100%);
    border: 1px solid var(--color-gray-200);
    border-radius: var(--radius-lg);
    cursor: pointer;
    transition: all var(--transition-base);
    font-size: var(--font-size-sm);
    color: var(--color-gray-800);
    font-weight: var(--font-weight-medium);
}

.template-item:hover {
    background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-primary-100) 100%);
    border-color: var(--color-primary-300);
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.template-item i {
    margin-right: 10px;
    color: var(--primary-color);
    font-size: 1.1rem;
    flex-shrink: 0;
}

.template-item span {
    font-weight: 500;
}

/* 聊天页面增强样式 */
.chat-container .alert {
    border: none;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.chat-container .badge {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
}

.chat-container .badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Dashboard功能指南样式 */
.guide-item {
    display: flex;
    align-items: flex-start;
    padding: 20px;
    background: #ffffff;
    border: 1px solid #e9ecef;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 100%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.guide-item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: var(--primary-color);
}

.guide-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    flex-shrink: 0;
}

.guide-icon i {
    font-size: 1.5rem;
}

.guide-content {
    flex: 1;
}

.guide-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 8px;
}

.guide-desc {
    font-size: 0.9rem;
    color: var(--secondary-color);
    margin-bottom: 5px;
    line-height: 1.4;
}

/* Dashboard快速操作样式 */
.quick-actions .badge {
    transition: all 0.3s ease;
    cursor: pointer;
    border: 1px solid transparent;
}

.quick-actions .badge:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    border-color: currentColor;
}

/* 对话头部增强样式 */
.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 20px;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    margin-bottom: 20px;
    border: 1px solid #dee2e6;
}

.chat-header-left {
    flex: 1;
}

.chat-header-right {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 10px;
}

.chat-controls {
    display: flex;
    gap: 8px;
}

.current-session-info {
    text-align: right;
}

/* 对话历史侧边栏样式 */
.chat-history-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: #ffffff;
    box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
    z-index: 1050;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
}

.chat-history-sidebar.show {
    right: 0;
}

.sidebar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #e9ecef;
    background: #f8f9fa;
}

.sidebar-content {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
}

.search-box input {
    border-radius: 8px;
    border: 1px solid #dee2e6;
}

.chat-sessions {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
}

.session-item {
    padding: 15px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    margin-bottom: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    background: #ffffff;
}

.session-item:hover {
    background: #f8f9fa;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.session-item.active {
    background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
    border-color: var(--primary-color);
}

.session-title {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 5px;
    font-size: 0.95rem;
}

.session-meta {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.session-actions {
    display: flex;
    justify-content: flex-end;
}

.no-sessions {
    color: var(--secondary-color);
}

/* 动画和过渡效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-20px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

/* 消息动画 */
.message.user {
    animation: fadeInLeft 0.3s ease-out;
}

.message.assistant {
    animation: fadeInUp 0.3s ease-out;
}

/* 按钮悬停效果增强 */
.btn {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.3s ease, height 0.3s ease;
}

.btn:hover::before {
    width: 300px;
    height: 300px;
}

/* 输入框焦点效果 */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
    animation: pulse 2s infinite;
}

/* 加载状态 */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 平滑滚动 */
html {
    scroll-behavior: smooth;
}

/* 选择文本样式 */
::selection {
    background: rgba(13, 110, 253, 0.2);
    color: inherit;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .chat-history-sidebar {
        width: 100vw;
        right: -100vw;
    }

    .chat-header {
        flex-direction: column;
        gap: 15px;
    }

    .chat-header-right {
        align-items: flex-start;
        width: 100%;
    }

    .chat-controls {
        width: 100%;
        justify-content: space-between;
    }

    .feature-cards .col-md-6 {
        margin-bottom: 15px;
    }

    .operation-templates {
        grid-template-columns: 1fr;
    }
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    border-radius: var(--border-radius);
}

.card:hover {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    transform: translateY(-2px);
}

.card-header {
    background-color: var(--light-color);
    border-bottom: 1px solid #dee2e6;
    font-weight: 600;
}

/* 按钮样式 */
.btn {
    font-weight: 500;
    border-radius: var(--border-radius);
    transition: var(--transition);
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
}

/* 表格样式 */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table thead th {
    background-color: var(--light-color);
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
}

.table tbody tr:hover {
    background-color: rgba(0, 0, 0, 0.025);
}

/* 状态标签 */
.status-badge {
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.status-online {
    background-color: var(--success-color);
    color: white;
}

.status-offline {
    background-color: var(--danger-color);
    color: white;
}

.status-unknown {
    background-color: var(--secondary-color);
    color: white;
}

.status-warning {
    background-color: var(--warning-color);
    color: var(--dark-color);
}

/* 告警级别样式 */
.alert-critical {
    border-left: 4px solid var(--danger-color);
    background-color: rgba(220, 53, 69, 0.1);
}

.alert-warning {
    border-left: 4px solid var(--warning-color);
    background-color: rgba(255, 193, 7, 0.1);
}

.alert-info {
    border-left: 4px solid var(--info-color);
    background-color: rgba(13, 202, 240, 0.1);
}

/* 聊天界面样式 */
.chat-container {
    height: 600px;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 1rem;
    background-color: white;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
}

.message {
    margin-bottom: 1rem;
    display: flex;
    align-items: flex-start;
}

.message.user {
    justify-content: flex-end;
}

.message.assistant {
    justify-content: flex-start;
}

.message-content {
    max-width: 70%;
    padding: 0.75rem 1rem;
    border-radius: 1rem;
    word-wrap: break-word;
}

.message.user .message-content {
    background-color: var(--primary-color);
    color: white;
    border-bottom-right-radius: 0.25rem;
}

.message.assistant .message-content {
    background-color: var(--light-color);
    color: var(--dark-color);
    border-bottom-left-radius: 0.25rem;
}

.message-time {
    font-size: 0.75rem;
    color: var(--secondary-color);
    margin-top: 0.25rem;
}

.chat-input {
    margin-top: 1rem;
}

.chat-input .input-group {
    border-radius: var(--border-radius);
    overflow: hidden;
}

/* 统计图表样式 */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

.stat-card {
    text-align: center;
    padding: 1.5rem;
    border-radius: var(--border-radius);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    margin-bottom: 1rem;
}

.stat-card .stat-number {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0.5rem 0;
}

.stat-card .stat-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

/* 首页统计卡片动画 */
.card.text-center {
    transition: all 0.3s ease;
}

.card.text-center:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.75rem 1.5rem rgba(0, 0, 0, 0.15);
}

.card.text-center .display-4 {
    transition: all 0.3s ease;
}

.card.text-center:hover .display-4 {
    transform: scale(1.1);
}

/* 加载状态样式 */
.loading-pulse {
    animation: pulse 1.5s ease-in-out infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

/* 状态指示器 */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-online {
    background-color: #28a745;
    animation: pulse-green 2s infinite;
}

.status-offline {
    background-color: #dc3545;
}

.status-warning {
    background-color: #ffc107;
}

@keyframes pulse-green {
    0% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7); }
    70% { box-shadow: 0 0 0 10px rgba(40, 167, 69, 0); }
    100% { box-shadow: 0 0 0 0 rgba(40, 167, 69, 0); }
}

/* 改进的通知样式 */
.notification-container {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 9999;
    max-width: 400px;
}

.notification {
    margin-bottom: 10px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* 改进的按钮样式 */
.btn-gradient {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    transition: all 0.3s ease;
}

.btn-gradient:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
    color: white;
}

/* 改进的表格样式 */
.table-modern {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.table-modern thead th {
    background: linear-gradient(45deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.875rem;
    letter-spacing: 0.5px;
}

.table-modern tbody tr {
    transition: all 0.2s ease;
}

.table-modern tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

/* 加载骨架屏 */
.skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

.skeleton-text {
    height: 1rem;
    border-radius: 4px;
    margin-bottom: 0.5rem;
}

.skeleton-title {
    height: 1.5rem;
    border-radius: 4px;
    margin-bottom: 1rem;
    width: 60%;
}

.skeleton-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

/* 改进的加载动画 */
.spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #fff;
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 悬浮效果 */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

/* 渐入动画 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 数字计数动画 */
.counter {
    transition: all 0.3s ease;
}

.counter.updating {
    color: #007bff;
    transform: scale(1.1);
}

/* 加载动画 */
.loading {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 3px solid rgba(0, 0, 0, 0.1);
    border-radius: 50%;
    border-top-color: var(--primary-color);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .navbar-nav {
        text-align: center;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    .message-content {
        max-width: 85%;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

/* 模态框样式 */
.modal-content {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: var(--light-color);
}

.modal-footer {
    border-top: 1px solid #dee2e6;
    background-color: var(--light-color);
}

/* 表单样式 */
.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* 分页样式 */
.pagination .page-link {
    color: var(--primary-color);
    border-color: #dee2e6;
}

.pagination .page-link:hover {
    color: var(--primary-color);
    background-color: var(--light-color);
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 进度条样式 */
.progress {
    height: 0.5rem;
    border-radius: var(--border-radius);
}

.progress-bar {
    border-radius: var(--border-radius);
}

/* 徽章样式 */
.badge {
    font-weight: 500;
}

/* 列表组样式 */
.list-group-item {
    border: none;
    border-bottom: 1px solid #dee2e6;
}

.list-group-item:last-child {
    border-bottom: none;
}

.list-group-item-action:hover {
    background-color: var(--light-color);
}

/* 代码块样式 */
pre {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: var(--border-radius);
    padding: 1rem;
    font-size: 0.875rem;
    overflow-x: auto;
}

code {
    background-color: #f8f9fa;
    color: #e83e8c;
    padding: 0.125rem 0.25rem;
    border-radius: 0.25rem;
    font-size: 0.875rem;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 打印样式 */
@media print {
    .navbar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .card {
        box-shadow: none !important;
        border: 1px solid #dee2e6 !important;
    }
}
