package handler

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"aiops-platform/internal/service"
	"github.com/sirupsen/logrus"
)

// ModelHandler 模型处理器
type ModelHandler struct {
	modelManager *service.ModelManager
	logger       *logrus.Logger
}

// NewModelHandler 创建模型处理器
func NewModelHandler(modelManager *service.ModelManager, logger *logrus.Logger) *ModelHandler {
	return &ModelHandler{
		modelManager: modelManager,
		logger:       logger,
	}
}

// GetAvailableModels 获取可用模型列表
func (h *ModelHandler) GetAvailableModels(c *gin.Context) {
	models := h.modelManager.GetAvailableModels()
	
	h.logger.WithField("model_count", len(models)).Debug("Retrieved available models")
	
	c.JSO<PERSON>(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"models": models,
			"current_model": h.modelManager.GetCurrentModelName(),
		},
	})
}

// GetCurrentModel 获取当前模型
func (h *ModelHandler) GetCurrentModel(c *gin.Context) {
	currentModel := h.modelManager.GetCurrentModel()
	if currentModel == nil {
		h.logger.Error("No current model available")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "No model available",
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"name":         currentModel.Name,
			"display_name": currentModel.DisplayName,
			"provider":     currentModel.Provider,
			"icon":         currentModel.Icon,
			"description":  currentModel.Description,
		},
	})
}

// SwitchModel 切换模型
func (h *ModelHandler) SwitchModel(c *gin.Context) {
	var req struct {
		ModelName string `json:"model_name" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Invalid request body")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Invalid request body",
		})
		return
	}
	
	if err := h.modelManager.SetCurrentModel(req.ModelName); err != nil {
		h.logger.WithError(err).WithField("model", req.ModelName).Error("Failed to switch model")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	
	newModel := h.modelManager.GetCurrentModel()
	h.logger.WithFields(logrus.Fields{
		"model": req.ModelName,
		"display_name": newModel.DisplayName,
	}).Info("Model switched successfully")
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "Model switched successfully",
		"data": gin.H{
			"name":         newModel.Name,
			"display_name": newModel.DisplayName,
			"provider":     newModel.Provider,
			"icon":         newModel.Icon,
			"description":  newModel.Description,
		},
	})
}

// GetModelsByProvider 根据提供商获取模型
func (h *ModelHandler) GetModelsByProvider(c *gin.Context) {
	provider := c.Param("provider")
	if provider == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Provider parameter is required",
		})
		return
	}
	
	models := h.modelManager.GetModelsByProvider(provider)
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"provider": provider,
			"models":   models,
		},
	})
}

// GetModelInfo 获取特定模型信息
func (h *ModelHandler) GetModelInfo(c *gin.Context) {
	modelName := c.Param("name")
	if modelName == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "Model name parameter is required",
		})
		return
	}
	
	model, err := h.modelManager.GetModelByName(modelName)
	if err != nil {
		h.logger.WithError(err).WithField("model", modelName).Error("Model not found")
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data": gin.H{
			"name":         model.Name,
			"display_name": model.DisplayName,
			"provider":     model.Provider,
			"icon":         model.Icon,
			"description":  model.Description,
			"is_current":   model.Name == h.modelManager.GetCurrentModelName(),
		},
	})
}
