package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// DatabaseOperationTool 数据库操作工具
type DatabaseOperationTool struct {
	db              *gorm.DB
	logger          *logrus.Logger
	sqlGenerator    *SQLGenerator
	securityChecker *DatabaseSecurityChecker
	resultProcessor *DatabaseResultProcessor
}

// NewDatabaseOperationTool 创建数据库操作工具
func NewDatabaseOperationTool(db *gorm.DB, logger *logrus.Logger) *DatabaseOperationTool {
	return &DatabaseOperationTool{
		db:              db,
		logger:          logger,
		sqlGenerator:    NewSQLGenerator(db, logger),
		securityChecker: NewDatabaseSecurityChecker(logger),
		resultProcessor: NewDatabaseResultProcessor(logger),
	}
}

// Execute 执行数据库操作
func (t *DatabaseOperationTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// 解析操作类型
	operationType, ok := args["operation_type"].(string)
	if !ok {
		return nil, fmt.Errorf("operation_type is required")
	}

	// 解析目标表
	tableName, ok := args["table_name"].(string)
	if !ok {
		return nil, fmt.Errorf("table_name is required")
	}

	// 创建操作请求
	request := &DatabaseOperationRequest{
		OperationType:  operationType,
		TableName:      tableName,
		Conditions:     extractConditions(args),
		Data:           extractData(args),
		UserID:         extractUserID(args),
		SessionID:      extractSessionID(args),
		RequireConfirm: extractRequireConfirm(args),
	}

	t.logger.WithFields(logrus.Fields{
		"operation_type": operationType,
		"table_name":     tableName,
		"user_id":        request.UserID,
	}).Info("Executing database operation")

	// 1. 安全检查
	securityResult, err := t.securityChecker.CheckOperation(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("security check failed: %w", err)
	}

	if !securityResult.Allowed {
		return &DatabaseOperationResult{
			Success:         false,
			Message:         securityResult.Reason,
			RequireConfirm:  true,
			SecurityWarning: securityResult.Warning,
		}, nil
	}

	// 2. 生成SQL
	sqlResult, err := t.sqlGenerator.GenerateSQL(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("SQL generation failed: %w", err)
	}

	// 3. 检查是否有确认标志
	if confirmed, ok := args["confirmed"].(bool); ok && confirmed {
		// 用户已确认，跳过确认步骤
	} else if request.RequireConfirm || securityResult.RequireConfirm {
		// 需要确认，返回预览
		return &DatabaseOperationResult{
			Success:        false,
			Message:        t.generateConfirmationMessage(request, securityResult, sqlResult),
			RequireConfirm: true,
			PreviewSQL:     sqlResult.SQL,
			EstimatedRows:  sqlResult.EstimatedRows,
			RiskLevel:      securityResult.RiskLevel,
			Warnings:       securityResult.Warnings,
			ConfirmationID: t.generateConfirmationID(request),
		}, nil
	}

	// 4. 执行SQL
	execResult, err := t.executeSQL(ctx, sqlResult, request)
	if err != nil {
		return nil, fmt.Errorf("SQL execution failed: %w", err)
	}

	// 5. 处理结果
	result, err := t.resultProcessor.ProcessResult(ctx, execResult, request)
	if err != nil {
		return nil, fmt.Errorf("result processing failed: %w", err)
	}

	// 6. 记录操作日志
	t.logOperation(ctx, request, result)

	return result, nil
}

// GetDefinition 获取工具定义
func (t *DatabaseOperationTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "database_operation",
			Description: "执行数据库操作，支持查询、插入、更新、删除等操作。当用户想要查看、修改、添加或删除数据时使用此工具。",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"operation_type": map[string]interface{}{
						"type":        "string",
						"description": "操作类型：select(查询), insert(插入), update(更新), delete(删除), describe(查看表结构)",
						"enum":        []string{"select", "insert", "update", "delete", "describe"},
					},
					"table_name": map[string]interface{}{
						"type":        "string",
						"description": "目标表名：hosts(主机), users(用户), alerts(告警), operation_logs(操作日志), chat_sessions(对话会话), chat_messages(对话消息)",
						"enum":        []string{"hosts", "users", "alerts", "operation_logs", "chat_sessions", "chat_messages"},
					},
					"conditions": map[string]interface{}{
						"type":        "object",
						"description": "查询或更新条件，如 {\"name\": \"web-01\", \"environment\": \"production\"}",
					},
					"data": map[string]interface{}{
						"type":        "object",
						"description": "要插入或更新的数据，如 {\"ip_address\": \"*************\", \"description\": \"Web服务器\"}",
					},
					"limit": map[string]interface{}{
						"type":        "number",
						"description": "查询结果限制数量，默认10，最大100",
					},
					"require_confirm": map[string]interface{}{
						"type":        "boolean",
						"description": "是否需要用户确认，危险操作会自动要求确认",
					},
				},
				"required": []string{"operation_type", "table_name"},
			},
		},
	}
}

// DatabaseOperationRequest 数据库操作请求
type DatabaseOperationRequest struct {
	OperationType  string                 `json:"operation_type"`
	TableName      string                 `json:"table_name"`
	Conditions     map[string]interface{} `json:"conditions"`
	Data           map[string]interface{} `json:"data"`
	Limit          int                    `json:"limit"`
	UserID         int64                  `json:"user_id"`
	SessionID      string                 `json:"session_id"`
	RequireConfirm bool                   `json:"require_confirm"`
}

// DatabaseOperationResult 数据库操作结果
type DatabaseOperationResult struct {
	Success         bool          `json:"success"`
	Message         string        `json:"message"`
	Data            interface{}   `json:"data,omitempty"`
	RowsAffected    int64         `json:"rows_affected,omitempty"`
	RequireConfirm  bool          `json:"require_confirm,omitempty"`
	PreviewSQL      string        `json:"preview_sql,omitempty"`
	EstimatedRows   int64         `json:"estimated_rows,omitempty"`
	RiskLevel       string        `json:"risk_level,omitempty"`
	Warnings        []string      `json:"warnings,omitempty"`
	SecurityWarning string        `json:"security_warning,omitempty"`
	UndoSuggestion  string        `json:"undo_suggestion,omitempty"`
	ExecutionTime   time.Duration `json:"execution_time,omitempty"`
	ConfirmationID  string        `json:"confirmation_id,omitempty"`
}

// 辅助函数
func extractConditions(args map[string]interface{}) map[string]interface{} {
	if conditions, ok := args["conditions"].(map[string]interface{}); ok {
		return conditions
	}
	return make(map[string]interface{})
}

func extractData(args map[string]interface{}) map[string]interface{} {
	if data, ok := args["data"].(map[string]interface{}); ok {
		return data
	}
	return make(map[string]interface{})
}

func extractUserID(args map[string]interface{}) int64 {
	if userID, ok := args["user_id"].(float64); ok {
		return int64(userID)
	}
	return 1 // 默认用户ID
}

func extractSessionID(args map[string]interface{}) string {
	if sessionID, ok := args["session_id"].(string); ok {
		return sessionID
	}
	return ""
}

func extractRequireConfirm(args map[string]interface{}) bool {
	if requireConfirm, ok := args["require_confirm"].(bool); ok {
		return requireConfirm
	}
	return false
}

// executeSQL 执行SQL
func (t *DatabaseOperationTool) executeSQL(ctx context.Context, sqlResult *SQLGenerationResult, request *DatabaseOperationRequest) (*SQLExecutionResult, error) {
	start := time.Now()

	var result *SQLExecutionResult
	var err error

	switch request.OperationType {
	case "select", "describe":
		result, err = t.executeQuery(ctx, sqlResult.SQL)
	case "insert", "update", "delete":
		result, err = t.executeModification(ctx, sqlResult.SQL)
	default:
		return nil, fmt.Errorf("unsupported operation type: %s", request.OperationType)
	}

	if result != nil {
		result.ExecutionTime = time.Since(start)
	}

	return result, err
}

// executeQuery 执行查询
func (t *DatabaseOperationTool) executeQuery(ctx context.Context, sql string) (*SQLExecutionResult, error) {
	var results []map[string]interface{}

	rows, err := t.db.Raw(sql).Rows()
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	columns, err := rows.Columns()
	if err != nil {
		return nil, err
	}

	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			return nil, err
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			row[col] = values[i]
		}
		results = append(results, row)
	}

	return &SQLExecutionResult{
		Success:      true,
		Data:         results,
		RowsAffected: int64(len(results)),
	}, nil
}

// executeModification 执行修改操作
func (t *DatabaseOperationTool) executeModification(ctx context.Context, sql string) (*SQLExecutionResult, error) {
	result := t.db.Exec(sql)
	if result.Error != nil {
		return nil, result.Error
	}

	return &SQLExecutionResult{
		Success:      true,
		RowsAffected: result.RowsAffected,
	}, nil
}

// logOperation 记录操作日志
func (t *DatabaseOperationTool) logOperation(ctx context.Context, request *DatabaseOperationRequest, result *DatabaseOperationResult) {
	logEntry := &model.OperationLog{
		OperationType: "database_operation",
		Command:       fmt.Sprintf("%s on %s", request.OperationType, request.TableName),
		Status:        "success",
		UserID:        request.UserID,
		SessionID:     request.SessionID,
		ExecutedAt:    time.Now(),
	}

	if !result.Success {
		logEntry.Status = "failed"
		logEntry.ErrorMessage = result.Message
	}

	if err := t.db.Create(logEntry).Error; err != nil {
		t.logger.WithError(err).Error("Failed to log database operation")
	}
}

// SQLExecutionResult SQL执行结果
type SQLExecutionResult struct {
	Success       bool          `json:"success"`
	Data          interface{}   `json:"data,omitempty"`
	RowsAffected  int64         `json:"rows_affected"`
	ExecutionTime time.Duration `json:"execution_time"`
	Error         string        `json:"error,omitempty"`
}

// generateConfirmationMessage 生成确认消息
func (t *DatabaseOperationTool) generateConfirmationMessage(request *DatabaseOperationRequest, securityResult *SecurityCheckResult, sqlResult *SQLGenerationResult) string {
	var message strings.Builder

	// 基本操作描述
	switch request.OperationType {
	case "select":
		message.WriteString(fmt.Sprintf("即将查询表 %s", request.TableName))
		if sqlResult.EstimatedRows > 0 {
			message.WriteString(fmt.Sprintf("，预计返回 %d 条记录", sqlResult.EstimatedRows))
		}
	case "insert":
		message.WriteString(fmt.Sprintf("即将向表 %s 插入新记录", request.TableName))
	case "update":
		message.WriteString(fmt.Sprintf("即将更新表 %s 中的记录", request.TableName))
		if sqlResult.EstimatedRows > 0 {
			message.WriteString(fmt.Sprintf("，预计影响 %d 条记录", sqlResult.EstimatedRows))
		}
	case "delete":
		message.WriteString(fmt.Sprintf("⚠️ 即将从表 %s 删除记录", request.TableName))
		if sqlResult.EstimatedRows > 0 {
			message.WriteString(fmt.Sprintf("，预计删除 %d 条记录", sqlResult.EstimatedRows))
		}
		message.WriteString("（此操作不可逆）")
	case "describe":
		message.WriteString(fmt.Sprintf("即将查看表 %s 的结构信息", request.TableName))
	}

	// 风险级别提示
	switch securityResult.RiskLevel {
	case "medium":
		message.WriteString("\n🔶 风险级别：中等")
	case "high":
		message.WriteString("\n🔸 风险级别：高")
	case "critical":
		message.WriteString("\n🔴 风险级别：极高")
	}

	// 添加警告信息
	if len(securityResult.Warnings) > 0 {
		message.WriteString("\n\n⚠️ 注意事项：")
		for _, warning := range securityResult.Warnings {
			message.WriteString(fmt.Sprintf("\n• %s", warning))
		}
	}

	message.WriteString("\n\n请确认是否继续执行此操作？")
	return message.String()
}

// generateConfirmationID 生成确认ID
func (t *DatabaseOperationTool) generateConfirmationID(request *DatabaseOperationRequest) string {
	// 使用时间戳和操作信息生成唯一ID
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("confirm_%s_%s_%d", request.OperationType, request.TableName, timestamp)
}
