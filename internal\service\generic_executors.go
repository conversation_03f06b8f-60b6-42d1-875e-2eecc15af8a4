package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 🚀 革命性架构：通用执行器实现

// GenericDatabaseExecutor 通用数据库执行器
type GenericDatabaseExecutor struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewGenericDatabaseExecutor 创建通用数据库执行器
func NewGenericDatabaseExecutor(db *gorm.DB, logger *logrus.Logger) *GenericDatabaseExecutor {
	return &GenericDatabaseExecutor{
		db:     db,
		logger: logger,
	}
}

// Execute 执行数据库指令
func (gde *GenericDatabaseExecutor) Execute(ctx context.Context, instruction *AIGeneratedInstruction) (*GenericExecutionResult, error) {
	start := time.Now()
	
	gde.logger.WithFields(logrus.Fields{
		"instruction_type": instruction.InstructionType,
		"content":          instruction.Content,
		"security_level":   instruction.SecurityLevel,
	}).Info("🎯 通用数据库执行器：开始执行")

	switch instruction.InstructionType {
	case "sql":
		return gde.executeSQL(ctx, instruction, start)
	case "query":
		return gde.executeSQL(ctx, instruction, start) // 查询也使用SQL执行
	case "transaction":
		return gde.executeSQL(ctx, instruction, start) // 事务也使用SQL执行
	default:
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ 不支持的数据库指令类型：%s", instruction.InstructionType),
			Action:          "unsupported_instruction",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "database",
		}, nil
	}
}

// executeSQL 执行SQL语句
func (gde *GenericDatabaseExecutor) executeSQL(ctx context.Context, instruction *AIGeneratedInstruction, start time.Time) (*GenericExecutionResult, error) {
	sqlQuery := instruction.Content
	
	// 🔒 SQL安全验证
	if err := gde.validateSQL(sqlQuery); err != nil {
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ SQL安全验证失败：%s", err.Error()),
			Action:          "sql_validation_failed",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "database",
		}, nil
	}

	// 🎯 根据SQL类型执行
	sqlType := gde.detectSQLType(sqlQuery)
	switch sqlType {
	case "SELECT":
		return gde.executeSelectSQL(ctx, sqlQuery, instruction, start)
	case "INSERT":
		return gde.executeModifySQL(ctx, sqlQuery, instruction, start, "插入")
	case "UPDATE":
		return gde.executeModifySQL(ctx, sqlQuery, instruction, start, "更新")
	case "DELETE":
		return gde.executeModifySQL(ctx, sqlQuery, instruction, start, "删除")
	default:
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ 不支持的SQL类型：%s", sqlType),
			Action:          "unsupported_sql_type",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "database",
		}, nil
	}
}

// executeSelectSQL 执行SELECT查询
func (gde *GenericDatabaseExecutor) executeSelectSQL(ctx context.Context, sqlQuery string, instruction *AIGeneratedInstruction, start time.Time) (*GenericExecutionResult, error) {
	// 🔑 使用通用的map切片来接收任意表的查询结果
	var results []map[string]interface{}

	rows, err := gde.db.Raw(sqlQuery).Rows()
	if err != nil {
		gde.logger.WithError(err).Error("SQL查询执行失败")
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ SQL查询执行失败：%s", err.Error()),
			Action:          "sql_execution_failed",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "database",
		}, nil
	}
	defer rows.Close()

	// 获取列名
	columns, err := rows.Columns()
	if err != nil {
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ 获取列信息失败：%s", err.Error()),
			Action:          "column_info_failed",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "database",
		}, nil
	}

	// 扫描所有行
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			gde.logger.WithError(err).Error("行数据扫描失败")
			continue
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			val := values[i]
			if b, ok := val.([]byte); ok {
				row[col] = string(b)
			} else {
				row[col] = val
			}
		}
		results = append(results, row)
	}

	// 🚀 智能格式化结果
	content := gde.formatQueryResults(results, columns, instruction.Description, sqlQuery)

	return &GenericExecutionResult{
		Success:         true,
		Content:         content,
		Action:          "sql_query_success",
		ExecutionTime:   time.Since(start),
		InstructionType: instruction.InstructionType,
		ExecutorType:    "database",
		Data: map[string]interface{}{
			"results":   results,
			"count":     len(results),
			"columns":   columns,
			"sql_query": sqlQuery,
		},
	}, nil
}

// executeModifySQL 执行修改类SQL
func (gde *GenericDatabaseExecutor) executeModifySQL(ctx context.Context, sqlQuery string, instruction *AIGeneratedInstruction, start time.Time, operation string) (*GenericExecutionResult, error) {
	// 🔒 危险操作确认检查
	if instruction.RequireConfirm {
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("⚠️ 危险操作需要确认\n\n**操作描述**：%s\n**SQL语句**：`%s`\n\n请输入\"确认%s\"来执行此操作", instruction.Description, sqlQuery, operation),
			Action:          "confirmation_required",
			RequireConfirm:  true,
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "database",
		}, nil
	}

	// 执行SQL
	result := gde.db.WithContext(ctx).Exec(sqlQuery)
	if result.Error != nil {
		gde.logger.WithError(result.Error).Error("SQL修改执行失败")
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ %s操作失败：%s", operation, result.Error.Error()),
			Action:          "sql_modify_failed",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "database",
		}, nil
	}

	rowsAffected := result.RowsAffected
	return &GenericExecutionResult{
		Success:         true,
		Content:         fmt.Sprintf("✅ %s操作成功！\n\n📊 影响行数：%d\n\n🔍 执行的SQL：\n```sql\n%s\n```", operation, rowsAffected, sqlQuery),
		Action:          "sql_modify_success",
		ExecutionTime:   time.Since(start),
		InstructionType: instruction.InstructionType,
		ExecutorType:    "database",
		Data: map[string]interface{}{
			"rows_affected": rowsAffected,
			"sql":           sqlQuery,
			"operation":     operation,
		},
	}, nil
}

// GetSupportedTypes 获取支持的指令类型
func (gde *GenericDatabaseExecutor) GetSupportedTypes() []string {
	return []string{"sql", "query", "transaction"}
}

// ValidateInstruction 验证指令
func (gde *GenericDatabaseExecutor) ValidateInstruction(instruction *AIGeneratedInstruction) error {
	if instruction.Content == "" {
		return fmt.Errorf("SQL内容不能为空")
	}
	return gde.validateSQL(instruction.Content)
}

// GetExecutorInfo 获取执行器信息
func (gde *GenericDatabaseExecutor) GetExecutorInfo() *ExecutorInfo {
	return &ExecutorInfo{
		Name:         "GenericDatabaseExecutor",
		Version:      "1.0.0",
		Description:  "通用数据库执行器，支持任意SQL执行",
		Capabilities: []string{"SELECT", "INSERT", "UPDATE", "DELETE", "事务处理"},
		SafetyLevel:  "medium",
	}
}

// validateSQL SQL安全验证
func (gde *GenericDatabaseExecutor) validateSQL(sqlQuery string) error {
	// 🔒 基础安全检查
	dangerousPatterns := []string{
		`(?i)drop\s+table`,
		`(?i)drop\s+database`,
		`(?i)truncate\s+table`,
		`(?i)alter\s+table.*drop`,
		`(?i)delete\s+from.*where\s+1\s*=\s*1`,
		`(?i)update.*set.*where\s+1\s*=\s*1`,
	}

	for _, pattern := range dangerousPatterns {
		if matched, _ := regexp.MatchString(pattern, sqlQuery); matched {
			return fmt.Errorf("检测到危险SQL模式：%s", pattern)
		}
	}

	return nil
}

// detectSQLType 检测SQL类型
func (gde *GenericDatabaseExecutor) detectSQLType(sqlQuery string) string {
	sqlQuery = strings.TrimSpace(strings.ToUpper(sqlQuery))
	
	if strings.HasPrefix(sqlQuery, "SELECT") {
		return "SELECT"
	} else if strings.HasPrefix(sqlQuery, "INSERT") {
		return "INSERT"
	} else if strings.HasPrefix(sqlQuery, "UPDATE") {
		return "UPDATE"
	} else if strings.HasPrefix(sqlQuery, "DELETE") {
		return "DELETE"
	}
	
	return "UNKNOWN"
}

// formatQueryResults 格式化查询结果
func (gde *GenericDatabaseExecutor) formatQueryResults(results []map[string]interface{}, columns []string, description, sqlQuery string) string {
	if len(results) == 0 {
		return fmt.Sprintf("📊 **%s** \n\n✅ 查询执行成功，但没有找到匹配的记录\n\n🔍 **执行的SQL**：`%s`", description, sqlQuery)
	}

	content := fmt.Sprintf("📊 **%s** (共 %d 条记录)\n\n", description, len(results))
	
	// 🎯 智能表格格式化
	content += "```\n"
	
	// 表头
	for i, col := range columns {
		if i > 0 {
			content += " | "
		}
		content += fmt.Sprintf("%-15s", col)
	}
	content += "\n"
	
	// 分隔线
	for i := range columns {
		if i > 0 {
			content += " | "
		}
		content += strings.Repeat("-", 15)
	}
	content += "\n"
	
	// 数据行（最多显示10行）
	maxRows := 10
	for i, row := range results {
		if i >= maxRows {
			content += fmt.Sprintf("... 还有 %d 行数据\n", len(results)-maxRows)
			break
		}
		
		for j, col := range columns {
			if j > 0 {
				content += " | "
			}
			value := fmt.Sprintf("%v", row[col])
			if len(value) > 15 {
				value = value[:12] + "..."
			}
			content += fmt.Sprintf("%-15s", value)
		}
		content += "\n"
	}
	
	content += "```\n\n"
	content += fmt.Sprintf("🔍 **执行的SQL**：`%s`", sqlQuery)
	
	return content
}

// 🚀 GenericSSHExecutor 通用SSH执行器
type GenericSSHExecutor struct {
	hostService HostService
	logger      *logrus.Logger
	db          *gorm.DB
}

// NewGenericSSHExecutor 创建通用SSH执行器
func NewGenericSSHExecutor(db *gorm.DB, logger *logrus.Logger, hostService HostService) *GenericSSHExecutor {
	return &GenericSSHExecutor{
		hostService: hostService,
		logger:      logger,
		db:          db,
	}
}

// Execute 执行SSH指令
func (gse *GenericSSHExecutor) Execute(ctx context.Context, instruction *AIGeneratedInstruction) (*GenericExecutionResult, error) {
	start := time.Now()

	gse.logger.WithFields(logrus.Fields{
		"instruction_type": instruction.InstructionType,
		"content":          instruction.Content,
		"security_level":   instruction.SecurityLevel,
	}).Info("🎯 通用SSH执行器：开始执行")

	switch instruction.InstructionType {
	case "shell":
		return gse.executeShellCommand(ctx, instruction, start)
	case "script":
		return gse.executeShellCommand(ctx, instruction, start) // 脚本也使用Shell命令执行
	case "batch":
		return gse.executeShellCommand(ctx, instruction, start) // 批量命令也使用Shell命令执行
	default:
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ 不支持的SSH指令类型：%s", instruction.InstructionType),
			Action:          "unsupported_instruction",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "ssh",
		}, nil
	}
}

// executeShellCommand 执行Shell命令
func (gse *GenericSSHExecutor) executeShellCommand(ctx context.Context, instruction *AIGeneratedInstruction, start time.Time) (*GenericExecutionResult, error) {
	// 🔧 检查是否为复杂命令链（包含多个echo和ssh命令）
	if gse.isComplexCommandChain(instruction.Content) {
		gse.logger.Info("🔧 检测到复杂命令链，使用本地Shell执行")
		return gse.executeComplexCommandChain(ctx, instruction, start)
	}

	// 🎯 获取目标主机
	targetHost, err := gse.getTargetHost(instruction.TargetResource)
	if err != nil {
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ 获取目标主机失败：%s", err.Error()),
			Action:          "target_host_error",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "ssh",
		}, nil
	}

	// 🔒 命令安全验证
	if err := gse.validateCommand(instruction.Content); err != nil {
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ 命令安全验证失败：%s", err.Error()),
			Action:          "command_validation_failed",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "ssh",
		}, nil
	}

	// 🚀 执行命令
	cmdReq := &model.CommandExecuteRequest{
		Command: instruction.Content,
		Timeout: instruction.Timeout,
	}

	response, err := gse.hostService.ExecuteCommand(targetHost.ID, cmdReq)
	if err != nil {
		gse.logger.WithError(err).Error("SSH命令执行失败")
		// SSH失败时更新主机状态为离线
		gse.hostService.UpdateHostStatus(targetHost.ID, "offline")
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ SSH命令执行失败：%s", err.Error()),
			Action:          "ssh_execution_failed",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "ssh",
		}, nil
	}

	// 🔧 关键修复：检查命令执行的退出码
	commandSuccess := response.ExitCode == 0

	if commandSuccess {
		// 🎯 SSH命令执行成功后立即更新主机状态为在线
		gse.hostService.UpdateHostStatus(targetHost.ID, "online")
		gse.logger.WithFields(logrus.Fields{
			"host_id":   targetHost.ID,
			"host_ip":   targetHost.IPAddress,
			"command":   instruction.Content,
			"exit_code": response.ExitCode,
		}).Info("✅ SSH命令执行成功")
	} else {
		// 命令执行失败，但SSH连接正常，主机状态仍为在线
		gse.hostService.UpdateHostStatus(targetHost.ID, "online")
		gse.logger.WithFields(logrus.Fields{
			"host_id":     targetHost.ID,
			"host_ip":     targetHost.IPAddress,
			"command":     instruction.Content,
			"exit_code":   response.ExitCode,
			"error_msg":   response.ErrorMessage,
		}).Warn("⚠️ SSH命令执行失败，但连接正常")
	}

	// 🚀 格式化输出
	content := gse.formatCommandResult(targetHost, instruction.Content, response, instruction.Description)

	// 🔧 关键修复：根据实际命令执行结果设置Success状态
	action := "ssh_command_success"
	if !commandSuccess {
		action = "ssh_command_failed"
	}

	return &GenericExecutionResult{
		Success:         commandSuccess, // 使用实际的命令执行结果
		Content:         content,
		Action:          action,
		ExecutionTime:   time.Since(start),
		InstructionType: instruction.InstructionType,
		ExecutorType:    "ssh",
		Data: map[string]interface{}{
			"host_id":    targetHost.ID,
			"host_ip":    targetHost.IPAddress,
			"command":    instruction.Content,
			"exit_code":  response.ExitCode,
			"stdout":     response.Stdout,
			"stderr":     response.Stderr,
			"duration":   response.Duration,
		},
	}, nil
}

// getTargetHost 获取目标主机
func (gse *GenericSSHExecutor) getTargetHost(targetResource *TargetResource) (*model.Host, error) {
	if targetResource == nil || targetResource.Identifier == "" {
		return nil, fmt.Errorf("目标主机标识符不能为空")
	}

	var host model.Host
	err := gse.db.Where("ip_address = ? AND deleted_at IS NULL", targetResource.Identifier).First(&host).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("未找到IP为 %s 的主机", targetResource.Identifier)
		}
		return nil, fmt.Errorf("查询主机失败: %w", err)
	}

	return &host, nil
}

// validateCommand 验证命令安全性
func (gse *GenericSSHExecutor) validateCommand(command string) error {
	// 🔒 危险命令检查
	dangerousPatterns := []string{
		`(?i)rm\s+-rf\s+/`,
		`(?i)dd\s+if=.*of=/dev/`,
		`(?i)mkfs\s+`,
		`(?i)fdisk\s+`,
		`(?i)^shutdown\s+`,     // 只匹配以shutdown开头的命令
		`(?i)^reboot\s+`,       // 只匹配以reboot开头的命令，避免误判"last reboot"
		`(?i)^halt\s+`,         // 只匹配以halt开头的命令
		`(?i)init\s+0`,
		`(?i)init\s+6`,
		`(?i)passwd\s+`,
		`(?i)userdel\s+`,
		`(?i)groupdel\s+`,
		`(?i)chmod\s+777`,
		`(?i)chown\s+-R.*root`,
	}

	for _, pattern := range dangerousPatterns {
		if matched, _ := regexp.MatchString(pattern, command); matched {
			return fmt.Errorf("检测到危险命令模式：%s", pattern)
		}
	}

	return nil
}

// formatCommandResult 格式化命令执行结果
func (gse *GenericSSHExecutor) formatCommandResult(host *model.Host, command string, response *model.CommandExecuteResponse, description string) string {
	result := fmt.Sprintf("🖥️ **SSH命令执行结果** - %s (%s)\n\n", host.Name, host.IPAddress)

	if description != "" {
		result += fmt.Sprintf("📝 **操作描述**：%s\n", description)
	}

	result += fmt.Sprintf("📝 **执行命令**：`%s`\n\n", command)
	result += "📋 **执行结果**：\n"

	if response.ExitCode == 0 {
		result += "✅ **状态**：成功\n"
		result += "```\n"
		result += response.Stdout
		result += "\n```\n"
	} else {
		result += "❌ **状态**：失败\n"
		result += fmt.Sprintf("🔢 **退出码**：%d\n", response.ExitCode)
		if response.Stdout != "" {
			result += "📤 **标准输出**：\n```\n" + response.Stdout + "\n```\n"
		}
		if response.Stderr != "" {
			result += "📥 **错误输出**：\n```\n" + response.Stderr + "\n```\n"
		}
		if response.ErrorMessage != "" {
			result += "⚠️ **错误信息**：" + response.ErrorMessage + "\n"
		}
	}

	result += fmt.Sprintf("\n⏰ **执行时间**：%s", response.ExecutedAt.Format("2006-01-02 15:04:05"))
	result += fmt.Sprintf("\n⌛ **耗时**：%d毫秒", response.Duration)

	return result
}

// GetSupportedTypes 获取支持的指令类型
func (gse *GenericSSHExecutor) GetSupportedTypes() []string {
	return []string{"shell", "script", "batch"}
}

// ValidateInstruction 验证指令
func (gse *GenericSSHExecutor) ValidateInstruction(instruction *AIGeneratedInstruction) error {
	if instruction.Content == "" {
		return fmt.Errorf("命令内容不能为空")
	}
	if instruction.TargetResource == nil || instruction.TargetResource.Identifier == "" {
		return fmt.Errorf("目标主机不能为空")
	}
	return gse.validateCommand(instruction.Content)
}

// GetExecutorInfo 获取执行器信息
func (gse *GenericSSHExecutor) GetExecutorInfo() *ExecutorInfo {
	return &ExecutorInfo{
		Name:         "GenericSSHExecutor",
		Version:      "1.0.0",
		Description:  "通用SSH执行器，支持任意Shell命令执行",
		Capabilities: []string{"Shell命令", "脚本执行", "批量命令", "安全验证"},
		SafetyLevel:  "high",
	}
}

// isComplexCommandChain 检查是否为复杂命令链
func (gse *GenericSSHExecutor) isComplexCommandChain(command string) bool {
	// 检查是否包含多个分号分隔的命令
	if strings.Count(command, ";") >= 3 {
		return true
	}

	// 检查是否包含多个echo语句（通常是格式化输出的标志）
	if strings.Count(command, "echo") >= 3 {
		return true
	}

	// 检查是否包含多个SSH命令
	if strings.Count(command, "ssh") >= 3 {
		return true
	}

	// 检查命令长度（超过500字符通常是复杂命令链）
	if len(command) > 500 {
		return true
	}

	return false
}

// executeComplexCommandChain 执行复杂命令链
func (gse *GenericSSHExecutor) executeComplexCommandChain(ctx context.Context, instruction *AIGeneratedInstruction, start time.Time) (*GenericExecutionResult, error) {
	command := instruction.Content

	gse.logger.WithField("command_length", len(command)).Info("🔧 开始执行复杂命令链")

	// 🔒 基本安全验证
	if err := gse.validateCommand(command); err != nil {
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ 命令安全验证失败：%s", err.Error()),
			Action:          "command_validation_failed",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "ssh",
		}, nil
	}

	// 🎯 获取目标主机并通过SSH执行复杂命令链
	targetHost, err := gse.getTargetHost(instruction.TargetResource)
	if err != nil {
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ 获取目标主机失败：%s", err.Error()),
			Action:          "target_host_error",
			ExecutionTime:   time.Since(start),
			InstructionType: instruction.InstructionType,
			ExecutorType:    "ssh",
		}, nil
	}

	// 🚀 通过SSH执行复杂命令链
	cmdReq := &model.CommandExecuteRequest{
		Command: command,
		Timeout: instruction.Timeout,
	}

	response, err := gse.hostService.ExecuteCommand(targetHost.ID, cmdReq)
	executionTime := time.Since(start)

	if err != nil {
		gse.logger.WithError(err).Error("SSH复杂命令链执行失败")
		// SSH失败时更新主机状态为离线
		gse.hostService.UpdateHostStatus(targetHost.ID, "offline")
		return &GenericExecutionResult{
			Success:         false,
			Content:         fmt.Sprintf("❌ SSH命令执行失败：%s", err.Error()),
			Action:          "ssh_execution_failed",
			ExecutionTime:   executionTime,
			InstructionType: instruction.InstructionType,
			ExecutorType:    "ssh",
		}, nil
	}

	// 🔑 SSH命令执行成功后立即更新主机状态为在线
	gse.hostService.UpdateHostStatus(targetHost.ID, "online")
	gse.logger.WithFields(logrus.Fields{
		"host_id":        targetHost.ID,
		"host_ip":        targetHost.IPAddress,
		"execution_time": executionTime,
		"exit_code":      response.ExitCode,
	}).Info("✅ SSH复杂命令链执行成功")

	// 构建执行结果
	content := fmt.Sprintf("🖥️ **SSH命令执行结果** - %s (%s)\n\n", targetHost.Name, targetHost.IPAddress)
	content += fmt.Sprintf("📝 **操作描述**：DeepSeek生成的复杂命令链\n")
	content += fmt.Sprintf("📝 **执行命令**：%s\n\n", command)
	content += fmt.Sprintf("📋 **执行结果**：\n")

	if response.ExitCode == 0 {
		content += fmt.Sprintf("✅ **状态**：成功\n")
	} else {
		content += fmt.Sprintf("❌ **状态**：失败\n")
		content += fmt.Sprintf("🔢 **退出码**：%d\n", response.ExitCode)
	}

	if response.Stdout != "" {
		content += fmt.Sprintf("📤 **标准输出**：\n```\n%s\n```\n\n", response.Stdout)
	}

	if response.Stderr != "" {
		content += fmt.Sprintf("📥 **错误输出**：\n```\n%s\n```\n\n", response.Stderr)
	}

	content += fmt.Sprintf("⏰ **执行时间**：%s\n", response.ExecutedAt.Format("2006-01-02 15:04:05"))
	content += fmt.Sprintf("⌛ **耗时**：%d毫秒", response.Duration)

	return &GenericExecutionResult{
		Success:         response.ExitCode == 0,
		Content:         content,
		Action:          "ssh_command_executed",
		ExecutionTime:   executionTime,
		InstructionType: instruction.InstructionType,
		ExecutorType:    "ssh",
	}, nil
}
