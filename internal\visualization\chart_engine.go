package visualization

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ChartEngine 图表引擎
type ChartEngine struct {
	config         *ChartEngineConfig
	logger         *logrus.Logger
	chartRenderers map[string]ChartRenderer
	dataProcessors map[string]DataProcessor
	themeManager   *ThemeManager
	cacheManager   *ChartCacheManager
	exportManager  *ChartExportManager
	mutex          sync.RWMutex
	isRunning      bool
}

// ChartEngineConfig 图表引擎配置
type ChartEngineConfig struct {
	EnableCache       bool          `json:"enable_cache"`
	CacheExpiration   time.Duration `json:"cache_expiration"`
	MaxCacheSize      int           `json:"max_cache_size"`
	EnableExport      bool          `json:"enable_export"`
	SupportedFormats  []string      `json:"supported_formats"`
	DefaultTheme      string        `json:"default_theme"`
	EnableAnimation   bool          `json:"enable_animation"`
	EnableInteraction bool          `json:"enable_interaction"`
	MaxDataPoints     int           `json:"max_data_points"`
	RenderTimeout     time.Duration `json:"render_timeout"`
}

// ChartRenderer 图表渲染器接口
type ChartRenderer interface {
	Render(data *ChartData, options *ChartOptions) (*RenderedChart, error)
	GetChartType() string
	GetSupportedOptions() []string
	ValidateData(data *ChartData) error
}

// DataProcessor 数据处理器接口
type DataProcessor interface {
	Process(rawData interface{}) (*ChartData, error)
	GetDataType() string
	ValidateInput(rawData interface{}) error
}

// ChartData 图表数据
type ChartData struct {
	Type      string                 `json:"type"`
	Labels    []string               `json:"labels"`
	Datasets  []*Dataset             `json:"datasets"`
	Metadata  map[string]interface{} `json:"metadata"`
	Timestamp time.Time              `json:"timestamp"`
	Source    string                 `json:"source"`
	Version   string                 `json:"version"`
}

// Dataset 数据集
type Dataset struct {
	Label           string                 `json:"label"`
	Data            []interface{}          `json:"data"`
	BackgroundColor []string               `json:"background_color,omitempty"`
	BorderColor     []string               `json:"border_color,omitempty"`
	BorderWidth     int                    `json:"border_width,omitempty"`
	Fill            bool                   `json:"fill,omitempty"`
	Tension         float64                `json:"tension,omitempty"`
	PointRadius     int                    `json:"point_radius,omitempty"`
	PointStyle      string                 `json:"point_style,omitempty"`
	ShowLine        bool                   `json:"show_line,omitempty"`
	Stack           string                 `json:"stack,omitempty"`
	Type            string                 `json:"type,omitempty"`
	YAxisID         string                 `json:"y_axis_id,omitempty"`
	Hidden          bool                   `json:"hidden,omitempty"`
	Order           int                    `json:"order,omitempty"`
	Metadata        map[string]interface{} `json:"metadata,omitempty"`
}

// ChartOptions 图表选项
type ChartOptions struct {
	Type             string                 `json:"type"`
	Title            *TitleOptions          `json:"title,omitempty"`
	Legend           *LegendOptions         `json:"legend,omitempty"`
	Scales           *ScalesOptions         `json:"scales,omitempty"`
	Plugins          map[string]interface{} `json:"plugins,omitempty"`
	Responsive       bool                   `json:"responsive"`
	Animation        *AnimationOptions      `json:"animation,omitempty"`
	Interaction      *InteractionOptions    `json:"interaction,omitempty"`
	Layout           *LayoutOptions         `json:"layout,omitempty"`
	Elements         *ElementsOptions       `json:"elements,omitempty"`
	Tooltip          *TooltipOptions        `json:"tooltip,omitempty"`
	Theme            string                 `json:"theme,omitempty"`
	Width            int                    `json:"width,omitempty"`
	Height           int                    `json:"height,omitempty"`
	DevicePixelRatio float64                `json:"device_pixel_ratio,omitempty"`
}

// TitleOptions 标题选项
type TitleOptions struct {
	Display  bool   `json:"display"`
	Text     string `json:"text"`
	Position string `json:"position"`
	Font     *Font  `json:"font,omitempty"`
	Color    string `json:"color,omitempty"`
	Padding  int    `json:"padding,omitempty"`
}

// LegendOptions 图例选项
type LegendOptions struct {
	Display   bool    `json:"display"`
	Position  string  `json:"position"`
	Align     string  `json:"align"`
	Labels    *Labels `json:"labels,omitempty"`
	Reverse   bool    `json:"reverse,omitempty"`
	MaxHeight int     `json:"max_height,omitempty"`
	MaxWidth  int     `json:"max_width,omitempty"`
}

// ScalesOptions 坐标轴选项
type ScalesOptions struct {
	X map[string]*AxisOptions `json:"x,omitempty"`
	Y map[string]*AxisOptions `json:"y,omitempty"`
}

// AxisOptions 坐标轴选项
type AxisOptions struct {
	Type         string         `json:"type"`
	Display      bool           `json:"display"`
	Position     string         `json:"position"`
	Title        *TitleOptions  `json:"title,omitempty"`
	Min          *float64       `json:"min,omitempty"`
	Max          *float64       `json:"max,omitempty"`
	SuggestedMin *float64       `json:"suggested_min,omitempty"`
	SuggestedMax *float64       `json:"suggested_max,omitempty"`
	Ticks        *TicksOptions  `json:"ticks,omitempty"`
	Grid         *GridOptions   `json:"grid,omitempty"`
	Border       *BorderOptions `json:"border,omitempty"`
	Stack        string         `json:"stack,omitempty"`
	StackWeight  float64        `json:"stack_weight,omitempty"`
	Offset       bool           `json:"offset,omitempty"`
	Reverse      bool           `json:"reverse,omitempty"`
}

// AnimationOptions 动画选项
type AnimationOptions struct {
	Duration int    `json:"duration"`
	Easing   string `json:"easing"`
	Delay    int    `json:"delay"`
	Loop     bool   `json:"loop"`
}

// InteractionOptions 交互选项
type InteractionOptions struct {
	Mode      string `json:"mode"`
	Intersect bool   `json:"intersect"`
	Axis      string `json:"axis"`
}

// LayoutOptions 布局选项
type LayoutOptions struct {
	Padding *Padding `json:"padding,omitempty"`
}

// ElementsOptions 元素选项
type ElementsOptions struct {
	Point *PointOptions `json:"point,omitempty"`
	Line  *LineOptions  `json:"line,omitempty"`
	Bar   *BarOptions   `json:"bar,omitempty"`
	Arc   *ArcOptions   `json:"arc,omitempty"`
}

// TooltipOptions 提示框选项
type TooltipOptions struct {
	Enabled         bool       `json:"enabled"`
	Mode            string     `json:"mode"`
	Intersect       bool       `json:"intersect"`
	Position        string     `json:"position"`
	BackgroundColor string     `json:"background_color,omitempty"`
	TitleColor      string     `json:"title_color,omitempty"`
	BodyColor       string     `json:"body_color,omitempty"`
	BorderColor     string     `json:"border_color,omitempty"`
	BorderWidth     int        `json:"border_width,omitempty"`
	DisplayColors   bool       `json:"display_colors"`
	Callbacks       *Callbacks `json:"callbacks,omitempty"`
}

// RenderedChart 渲染后的图表
type RenderedChart struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"`
	Data       *ChartData             `json:"data"`
	Options    *ChartOptions          `json:"options"`
	Config     *RenderConfig          `json:"config"`
	Output     *ChartOutput           `json:"output"`
	Metadata   map[string]interface{} `json:"metadata"`
	CreatedAt  time.Time              `json:"created_at"`
	RenderTime time.Duration          `json:"render_time"`
	CacheKey   string                 `json:"cache_key,omitempty"`
}

// RenderConfig 渲染配置
type RenderConfig struct {
	Format      string  `json:"format"`
	Quality     float64 `json:"quality"`
	Compression bool    `json:"compression"`
	Transparent bool    `json:"transparent"`
	Background  string  `json:"background,omitempty"`
}

// ChartOutput 图表输出
type ChartOutput struct {
	Format   string `json:"format"`
	Data     []byte `json:"data"`
	MimeType string `json:"mime_type"`
	Size     int64  `json:"size"`
	Width    int    `json:"width"`
	Height   int    `json:"height"`
}

// 辅助类型

// Font 字体
type Font struct {
	Family string `json:"family"`
	Size   int    `json:"size"`
	Style  string `json:"style"`
	Weight string `json:"weight"`
}

// Labels 标签
type Labels struct {
	Color         string `json:"color,omitempty"`
	Font          *Font  `json:"font,omitempty"`
	Padding       int    `json:"padding,omitempty"`
	UsePointStyle bool   `json:"use_point_style,omitempty"`
}

// TicksOptions 刻度选项
type TicksOptions struct {
	Display     bool     `json:"display"`
	Color       string   `json:"color,omitempty"`
	Font        *Font    `json:"font,omitempty"`
	MaxRotation int      `json:"max_rotation,omitempty"`
	MinRotation int      `json:"min_rotation,omitempty"`
	Mirror      bool     `json:"mirror,omitempty"`
	Padding     int      `json:"padding,omitempty"`
	StepSize    *float64 `json:"step_size,omitempty"`
	Count       *int     `json:"count,omitempty"`
	Precision   *int     `json:"precision,omitempty"`
}

// GridOptions 网格选项
type GridOptions struct {
	Display         bool   `json:"display"`
	Color           string `json:"color,omitempty"`
	LineWidth       int    `json:"line_width,omitempty"`
	DrawBorder      bool   `json:"draw_border"`
	DrawOnChartArea bool   `json:"draw_on_chart_area"`
	DrawTicks       bool   `json:"draw_ticks"`
	TickLength      int    `json:"tick_length,omitempty"`
	Offset          bool   `json:"offset,omitempty"`
	Circular        bool   `json:"circular,omitempty"`
}

// BorderOptions 边框选项
type BorderOptions struct {
	Display    bool   `json:"display"`
	Color      string `json:"color,omitempty"`
	Width      int    `json:"width,omitempty"`
	Dash       []int  `json:"dash,omitempty"`
	DashOffset int    `json:"dash_offset,omitempty"`
}

// Padding 内边距
type Padding struct {
	Top    int `json:"top"`
	Right  int `json:"right"`
	Bottom int `json:"bottom"`
	Left   int `json:"left"`
}

// PointOptions 点选项
type PointOptions struct {
	Radius           int    `json:"radius"`
	PointStyle       string `json:"point_style"`
	BackgroundColor  string `json:"background_color,omitempty"`
	BorderColor      string `json:"border_color,omitempty"`
	BorderWidth      int    `json:"border_width,omitempty"`
	HitRadius        int    `json:"hit_radius,omitempty"`
	HoverRadius      int    `json:"hover_radius,omitempty"`
	HoverBorderWidth int    `json:"hover_border_width,omitempty"`
}

// LineOptions 线选项
type LineOptions struct {
	BackgroundColor        string      `json:"background_color,omitempty"`
	BorderColor            string      `json:"border_color,omitempty"`
	BorderWidth            int         `json:"border_width,omitempty"`
	BorderCapStyle         string      `json:"border_cap_style,omitempty"`
	BorderDash             []int       `json:"border_dash,omitempty"`
	BorderDashOffset       int         `json:"border_dash_offset,omitempty"`
	BorderJoinStyle        string      `json:"border_join_style,omitempty"`
	CapBezierPoints        bool        `json:"cap_bezier_points,omitempty"`
	CubicInterpolationMode string      `json:"cubic_interpolation_mode,omitempty"`
	Fill                   interface{} `json:"fill,omitempty"`
	Stepped                interface{} `json:"stepped,omitempty"`
	Tension                float64     `json:"tension,omitempty"`
}

// BarOptions 柱状图选项
type BarOptions struct {
	BackgroundColor string `json:"background_color,omitempty"`
	BorderColor     string `json:"border_color,omitempty"`
	BorderWidth     int    `json:"border_width,omitempty"`
	BorderRadius    int    `json:"border_radius,omitempty"`
	BorderSkipped   string `json:"border_skipped,omitempty"`
	InflateAmount   int    `json:"inflate_amount,omitempty"`
}

// ArcOptions 弧形选项
type ArcOptions struct {
	BackgroundColor string `json:"background_color,omitempty"`
	BorderColor     string `json:"border_color,omitempty"`
	BorderWidth     int    `json:"border_width,omitempty"`
	BorderAlign     string `json:"border_align,omitempty"`
	Circular        bool   `json:"circular,omitempty"`
}

// Callbacks 回调函数
type Callbacks struct {
	Label          string `json:"label,omitempty"`
	LabelColor     string `json:"label_color,omitempty"`
	LabelTextColor string `json:"label_text_color,omitempty"`
	Title          string `json:"title,omitempty"`
	BeforeTitle    string `json:"before_title,omitempty"`
	AfterTitle     string `json:"after_title,omitempty"`
	BeforeBody     string `json:"before_body,omitempty"`
	AfterBody      string `json:"after_body,omitempty"`
	BeforeFooter   string `json:"before_footer,omitempty"`
	Footer         string `json:"footer,omitempty"`
	AfterFooter    string `json:"after_footer,omitempty"`
}

// NewChartEngine 创建图表引擎
func NewChartEngine(config *ChartEngineConfig, logger *logrus.Logger) *ChartEngine {
	if config == nil {
		config = DefaultChartEngineConfig()
	}

	engine := &ChartEngine{
		config:         config,
		logger:         logger,
		chartRenderers: make(map[string]ChartRenderer),
		dataProcessors: make(map[string]DataProcessor),
		isRunning:      false,
	}

	// 初始化子组件
	engine.themeManager = NewThemeManager(config.DefaultTheme, logger)

	if config.EnableCache {
		engine.cacheManager = NewChartCacheManager(config.CacheExpiration, config.MaxCacheSize, logger)
	}

	if config.EnableExport {
		engine.exportManager = NewChartExportManager(config.SupportedFormats, logger)
	}

	// 注册默认渲染器
	engine.registerDefaultRenderers()

	// 注册默认数据处理器
	engine.registerDefaultProcessors()

	return engine
}

// DefaultChartEngineConfig 默认图表引擎配置
func DefaultChartEngineConfig() *ChartEngineConfig {
	return &ChartEngineConfig{
		EnableCache:       true,
		CacheExpiration:   1 * time.Hour,
		MaxCacheSize:      100,
		EnableExport:      true,
		SupportedFormats:  []string{"png", "jpg", "svg", "pdf"},
		DefaultTheme:      "default",
		EnableAnimation:   true,
		EnableInteraction: true,
		MaxDataPoints:     10000,
		RenderTimeout:     30 * time.Second,
	}
}

// Start 启动图表引擎
func (ce *ChartEngine) Start(ctx context.Context) error {
	ce.mutex.Lock()
	defer ce.mutex.Unlock()

	if ce.isRunning {
		return fmt.Errorf("chart engine is already running")
	}

	ce.logger.Info("Starting chart engine")

	// 启动缓存管理器
	if ce.cacheManager != nil {
		if err := ce.cacheManager.Start(ctx); err != nil {
			return fmt.Errorf("failed to start cache manager: %w", err)
		}
	}

	// 启动导出管理器
	if ce.exportManager != nil {
		if err := ce.exportManager.Start(ctx); err != nil {
			return fmt.Errorf("failed to start export manager: %w", err)
		}
	}

	ce.isRunning = true
	ce.logger.Info("Chart engine started successfully")

	return nil
}

// Stop 停止图表引擎
func (ce *ChartEngine) Stop(ctx context.Context) error {
	ce.mutex.Lock()
	defer ce.mutex.Unlock()

	if !ce.isRunning {
		return nil
	}

	ce.logger.Info("Stopping chart engine")

	// 停止缓存管理器
	if ce.cacheManager != nil {
		if err := ce.cacheManager.Stop(ctx); err != nil {
			ce.logger.WithError(err).Error("Failed to stop cache manager")
		}
	}

	// 停止导出管理器
	if ce.exportManager != nil {
		if err := ce.exportManager.Stop(ctx); err != nil {
			ce.logger.WithError(err).Error("Failed to stop export manager")
		}
	}

	ce.isRunning = false
	ce.logger.Info("Chart engine stopped")

	return nil
}

// RenderChart 渲染图表
func (ce *ChartEngine) RenderChart(ctx context.Context, data *ChartData, options *ChartOptions) (*RenderedChart, error) {
	ce.mutex.RLock()
	defer ce.mutex.RUnlock()

	if !ce.isRunning {
		return nil, fmt.Errorf("chart engine is not running")
	}

	startTime := time.Now()

	// 验证输入
	if err := ce.validateInput(data, options); err != nil {
		return nil, fmt.Errorf("input validation failed: %w", err)
	}

	// 检查缓存
	cacheKey := ce.generateCacheKey(data, options)
	if ce.cacheManager != nil {
		if cached := ce.cacheManager.Get(cacheKey); cached != nil {
			ce.logger.WithField("cache_key", cacheKey).Debug("Chart found in cache")
			return cached, nil
		}
	}

	// 应用主题
	if options.Theme == "" {
		options.Theme = ce.config.DefaultTheme
	}
	ce.themeManager.ApplyTheme(options, options.Theme)

	// 获取渲染器
	renderer, exists := ce.chartRenderers[data.Type]
	if !exists {
		return nil, fmt.Errorf("unsupported chart type: %s", data.Type)
	}

	// 渲染图表
	rendered, err := renderer.Render(data, options)
	if err != nil {
		return nil, fmt.Errorf("chart rendering failed: %w", err)
	}

	// 设置渲染信息
	rendered.ID = ce.generateChartID()
	rendered.CreatedAt = time.Now()
	rendered.RenderTime = time.Since(startTime)
	rendered.CacheKey = cacheKey

	// 缓存结果
	if ce.cacheManager != nil {
		ce.cacheManager.Set(cacheKey, rendered)
	}

	ce.logger.WithFields(logrus.Fields{
		"chart_id":    rendered.ID,
		"chart_type":  data.Type,
		"render_time": rendered.RenderTime,
	}).Info("Chart rendered successfully")

	return rendered, nil
}

// ProcessData 处理数据
func (ce *ChartEngine) ProcessData(ctx context.Context, rawData interface{}, dataType string) (*ChartData, error) {
	ce.mutex.RLock()
	defer ce.mutex.RUnlock()

	processor, exists := ce.dataProcessors[dataType]
	if !exists {
		return nil, fmt.Errorf("unsupported data type: %s", dataType)
	}

	// 验证输入数据
	if err := processor.ValidateInput(rawData); err != nil {
		return nil, fmt.Errorf("data validation failed: %w", err)
	}

	// 处理数据
	chartData, err := processor.Process(rawData)
	if err != nil {
		return nil, fmt.Errorf("data processing failed: %w", err)
	}

	// 检查数据点数量限制
	if ce.exceedsDataPointLimit(chartData) {
		return nil, fmt.Errorf("data exceeds maximum points limit: %d", ce.config.MaxDataPoints)
	}

	return chartData, nil
}

// ExportChart 导出图表
func (ce *ChartEngine) ExportChart(ctx context.Context, chartID string, format string) ([]byte, error) {
	ce.mutex.RLock()
	defer ce.mutex.RUnlock()

	if ce.exportManager == nil {
		return nil, fmt.Errorf("export manager is not enabled")
	}

	return ce.exportManager.Export(chartID, format)
}

// GetSupportedChartTypes 获取支持的图表类型
func (ce *ChartEngine) GetSupportedChartTypes() []string {
	ce.mutex.RLock()
	defer ce.mutex.RUnlock()

	var types []string
	for chartType := range ce.chartRenderers {
		types = append(types, chartType)
	}

	sort.Strings(types)
	return types
}

// GetSupportedDataTypes 获取支持的数据类型
func (ce *ChartEngine) GetSupportedDataTypes() []string {
	ce.mutex.RLock()
	defer ce.mutex.RUnlock()

	var types []string
	for dataType := range ce.dataProcessors {
		types = append(types, dataType)
	}

	sort.Strings(types)
	return types
}

// RegisterRenderer 注册图表渲染器
func (ce *ChartEngine) RegisterRenderer(renderer ChartRenderer) error {
	ce.mutex.Lock()
	defer ce.mutex.Unlock()

	chartType := renderer.GetChartType()
	if chartType == "" {
		return fmt.Errorf("chart type cannot be empty")
	}

	ce.chartRenderers[chartType] = renderer
	ce.logger.WithField("chart_type", chartType).Info("Chart renderer registered")

	return nil
}

// RegisterDataProcessor 注册数据处理器
func (ce *ChartEngine) RegisterDataProcessor(processor DataProcessor) error {
	ce.mutex.Lock()
	defer ce.mutex.Unlock()

	dataType := processor.GetDataType()
	if dataType == "" {
		return fmt.Errorf("data type cannot be empty")
	}

	ce.dataProcessors[dataType] = processor
	ce.logger.WithField("data_type", dataType).Info("Data processor registered")

	return nil
}

// GetChartEngineStatus 获取图表引擎状态
func (ce *ChartEngine) GetChartEngineStatus() *ChartEngineStatus {
	ce.mutex.RLock()
	defer ce.mutex.RUnlock()

	status := &ChartEngineStatus{
		IsRunning:            ce.isRunning,
		Config:               ce.config,
		SupportedChartTypes:  ce.GetSupportedChartTypes(),
		SupportedDataTypes:   ce.GetSupportedDataTypes(),
		RegisteredRenderers:  len(ce.chartRenderers),
		RegisteredProcessors: len(ce.dataProcessors),
	}

	if ce.cacheManager != nil {
		status.CacheStats = ce.cacheManager.GetStats()
	}

	if ce.exportManager != nil {
		status.ExportStats = ce.exportManager.GetStats()
	}

	return status
}

// 私有方法

// validateInput 验证输入
func (ce *ChartEngine) validateInput(data *ChartData, options *ChartOptions) error {
	if data == nil {
		return fmt.Errorf("chart data cannot be nil")
	}

	if options == nil {
		return fmt.Errorf("chart options cannot be nil")
	}

	if data.Type == "" {
		return fmt.Errorf("chart type cannot be empty")
	}

	if len(data.Datasets) == 0 {
		return fmt.Errorf("chart data must contain at least one dataset")
	}

	// 验证数据集
	for i, dataset := range data.Datasets {
		if dataset == nil {
			return fmt.Errorf("dataset %d cannot be nil", i)
		}
		if len(dataset.Data) == 0 {
			return fmt.Errorf("dataset %d must contain data", i)
		}
	}

	return nil
}

// generateCacheKey 生成缓存键
func (ce *ChartEngine) generateCacheKey(data *ChartData, options *ChartOptions) string {
	// 简化实现：基于数据和选项的哈希
	dataBytes, _ := json.Marshal(data)
	optionsBytes, _ := json.Marshal(options)

	return fmt.Sprintf("chart_%x_%x",
		ce.simpleHash(dataBytes),
		ce.simpleHash(optionsBytes))
}

// generateChartID 生成图表ID
func (ce *ChartEngine) generateChartID() string {
	return fmt.Sprintf("chart_%d", time.Now().UnixNano())
}

// exceedsDataPointLimit 检查是否超过数据点限制
func (ce *ChartEngine) exceedsDataPointLimit(data *ChartData) bool {
	totalPoints := 0

	for _, dataset := range data.Datasets {
		totalPoints += len(dataset.Data)
	}

	return totalPoints > ce.config.MaxDataPoints
}

// simpleHash 简单哈希函数
func (ce *ChartEngine) simpleHash(data []byte) uint32 {
	var hash uint32 = 2166136261
	for _, b := range data {
		hash ^= uint32(b)
		hash *= 16777619
	}
	return hash
}

// registerDefaultRenderers 注册默认渲染器
func (ce *ChartEngine) registerDefaultRenderers() {
	// 注册线图渲染器
	ce.chartRenderers["line"] = &LineChartRenderer{logger: ce.logger}

	// 注册柱状图渲染器
	ce.chartRenderers["bar"] = &BarChartRenderer{logger: ce.logger}

	// 注册饼图渲染器
	ce.chartRenderers["pie"] = &PieChartRenderer{logger: ce.logger}

	// 注册面积图渲染器
	ce.chartRenderers["area"] = &AreaChartRenderer{logger: ce.logger}

	// 注册散点图渲染器
	ce.chartRenderers["scatter"] = &ScatterChartRenderer{logger: ce.logger}

	// 注册雷达图渲染器
	ce.chartRenderers["radar"] = &RadarChartRenderer{logger: ce.logger}

	// 注册甜甜圈图渲染器
	ce.chartRenderers["doughnut"] = &DoughnutChartRenderer{logger: ce.logger}

	// 注册极坐标图渲染器
	ce.chartRenderers["polarArea"] = &PolarAreaChartRenderer{logger: ce.logger}
}

// registerDefaultProcessors 注册默认数据处理器
func (ce *ChartEngine) registerDefaultProcessors() {
	// 注册时间序列数据处理器
	ce.dataProcessors["timeseries"] = &TimeSeriesDataProcessor{logger: ce.logger}

	// 注册统计数据处理器
	ce.dataProcessors["statistics"] = &StatisticsDataProcessor{logger: ce.logger}

	// 注册性能数据处理器
	ce.dataProcessors["performance"] = &PerformanceDataProcessor{logger: ce.logger}

	// 注册监控数据处理器
	ce.dataProcessors["monitoring"] = &MonitoringDataProcessor{logger: ce.logger}

	// 注册日志数据处理器
	ce.dataProcessors["logs"] = &LogDataProcessor{logger: ce.logger}

	// 注册通用数据处理器
	ce.dataProcessors["generic"] = &GenericDataProcessor{logger: ce.logger}
}

// ChartEngineStatus 图表引擎状态
type ChartEngineStatus struct {
	IsRunning            bool               `json:"is_running"`
	Config               *ChartEngineConfig `json:"config"`
	SupportedChartTypes  []string           `json:"supported_chart_types"`
	SupportedDataTypes   []string           `json:"supported_data_types"`
	RegisteredRenderers  int                `json:"registered_renderers"`
	RegisteredProcessors int                `json:"registered_processors"`
	CacheStats           *ChartCacheStats   `json:"cache_stats,omitempty"`
	ExportStats          *ChartExportStats  `json:"export_stats,omitempty"`
}

// ChartCacheStats 图表缓存统计
type ChartCacheStats struct {
	HitCount    int64   `json:"hit_count"`
	MissCount   int64   `json:"miss_count"`
	HitRate     float64 `json:"hit_rate"`
	CacheSize   int     `json:"cache_size"`
	MaxSize     int     `json:"max_size"`
	MemoryUsage int64   `json:"memory_usage"`
}

// ChartExportStats 图表导出统计
type ChartExportStats struct {
	TotalExports      int64            `json:"total_exports"`
	ExportsByFormat   map[string]int64 `json:"exports_by_format"`
	AverageExportTime time.Duration    `json:"average_export_time"`
	LastExportTime    time.Time        `json:"last_export_time"`
	FailedExports     int64            `json:"failed_exports"`
}
