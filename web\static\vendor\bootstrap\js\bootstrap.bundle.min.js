/*!
 * Bootstrap v5.3.0 (https://getbootstrap.com/)
 * Copyright 2011-2023 The Bootstrap Authors (https://github.com/twbs/bootstrap/graphs/contributors)
 * Licensed under MIT (https://github.com/twbs/bootstrap/blob/main/LICENSE)
 */

// 简化的Bootstrap JavaScript功能
(function() {
    'use strict';

    // 基础工具函数
    const Util = {
        getElement: function(obj) {
            if (typeof obj === 'string') {
                return document.querySelector(obj);
            }
            return obj;
        },
        
        trigger: function(element, eventName, detail = {}) {
            const event = new CustomEvent(eventName, {
                bubbles: true,
                cancelable: true,
                detail: detail
            });
            element.dispatchEvent(event);
        },
        
        addClass: function(element, className) {
            if (element && className) {
                element.classList.add(className);
            }
        },
        
        removeClass: function(element, className) {
            if (element && className) {
                element.classList.remove(className);
            }
        },
        
        hasClass: function(element, className) {
            return element && element.classList.contains(className);
        },
        
        toggleClass: function(element, className) {
            if (element && className) {
                element.classList.toggle(className);
            }
        }
    };

    // Alert组件
    class Alert {
        constructor(element) {
            this._element = Util.getElement(element);
            this._bindEvents();
        }
        
        _bindEvents() {
            const closeBtn = this._element.querySelector('[data-bs-dismiss="alert"]');
            if (closeBtn) {
                closeBtn.addEventListener('click', () => this.close());
            }
        }
        
        close() {
            Util.trigger(this._element, 'close.bs.alert');
            this._element.style.opacity = '0';
            setTimeout(() => {
                this._element.remove();
                Util.trigger(this._element, 'closed.bs.alert');
            }, 150);
        }
        
        static getInstance(element) {
            return element._bsAlert || new Alert(element);
        }
    }

    // Modal组件
    class Modal {
        constructor(element, options = {}) {
            this._element = Util.getElement(element);
            this._options = options;
            this._isShown = false;
            this._backdrop = null;
            this._bindEvents();
        }
        
        _bindEvents() {
            const closeButtons = this._element.querySelectorAll('[data-bs-dismiss="modal"]');
            closeButtons.forEach(btn => {
                btn.addEventListener('click', () => this.hide());
            });
        }
        
        show() {
            if (this._isShown) return;
            
            Util.trigger(this._element, 'show.bs.modal');
            this._showBackdrop();
            this._showElement();
        }
        
        hide() {
            if (!this._isShown) return;
            
            Util.trigger(this._element, 'hide.bs.modal');
            this._hideElement();
        }
        
        _showBackdrop() {
            this._backdrop = document.createElement('div');
            this._backdrop.className = 'modal-backdrop fade';
            document.body.appendChild(this._backdrop);
            
            setTimeout(() => {
                Util.addClass(this._backdrop, 'show');
            }, 10);
            
            this._backdrop.addEventListener('click', () => {
                if (this._options.backdrop !== 'static') {
                    this.hide();
                }
            });
        }
        
        _showElement() {
            Util.addClass(document.body, 'modal-open');
            Util.addClass(this._element, 'show');
            this._element.style.display = 'block';
            this._isShown = true;
            
            Util.trigger(this._element, 'shown.bs.modal');
        }
        
        _hideElement() {
            Util.removeClass(this._element, 'show');
            setTimeout(() => {
                this._element.style.display = 'none';
                this._hideBackdrop();
                Util.removeClass(document.body, 'modal-open');
                this._isShown = false;
                Util.trigger(this._element, 'hidden.bs.modal');
            }, 150);
        }
        
        _hideBackdrop() {
            if (this._backdrop) {
                Util.removeClass(this._backdrop, 'show');
                setTimeout(() => {
                    this._backdrop.remove();
                    this._backdrop = null;
                }, 150);
            }
        }
        
        static getInstance(element) {
            return element._bsModal || new Modal(element);
        }
    }

    // Dropdown组件
    class Dropdown {
        constructor(element) {
            this._element = Util.getElement(element);
            this._menu = this._element.nextElementSibling;
            this._isShown = false;
            this._bindEvents();
        }
        
        _bindEvents() {
            this._element.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggle();
            });
            
            document.addEventListener('click', (e) => {
                if (!this._element.contains(e.target) && !this._menu.contains(e.target)) {
                    this.hide();
                }
            });
        }
        
        toggle() {
            this._isShown ? this.hide() : this.show();
        }
        
        show() {
            if (this._isShown) return;
            
            Util.trigger(this._element, 'show.bs.dropdown');
            Util.addClass(this._menu, 'show');
            this._element.setAttribute('aria-expanded', 'true');
            this._isShown = true;
            Util.trigger(this._element, 'shown.bs.dropdown');
        }
        
        hide() {
            if (!this._isShown) return;
            
            Util.trigger(this._element, 'hide.bs.dropdown');
            Util.removeClass(this._menu, 'show');
            this._element.setAttribute('aria-expanded', 'false');
            this._isShown = false;
            Util.trigger(this._element, 'hidden.bs.dropdown');
        }
        
        static getInstance(element) {
            return element._bsDropdown || new Dropdown(element);
        }
    }

    // Collapse组件
    class Collapse {
        constructor(element, options = {}) {
            this._element = Util.getElement(element);
            this._options = options;
            this._isShown = false;
            this._bindEvents();
        }
        
        _bindEvents() {
            const triggers = document.querySelectorAll(`[data-bs-toggle="collapse"][data-bs-target="#${this._element.id}"]`);
            triggers.forEach(trigger => {
                trigger.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.toggle();
                });
            });
        }
        
        toggle() {
            this._isShown ? this.hide() : this.show();
        }
        
        show() {
            if (this._isShown) return;
            
            Util.trigger(this._element, 'show.bs.collapse');
            this._element.style.height = '0px';
            Util.addClass(this._element, 'collapsing');
            Util.removeClass(this._element, 'collapse');
            
            const height = this._element.scrollHeight;
            this._element.style.height = height + 'px';
            
            setTimeout(() => {
                Util.removeClass(this._element, 'collapsing');
                Util.addClass(this._element, 'collapse show');
                this._element.style.height = '';
                this._isShown = true;
                Util.trigger(this._element, 'shown.bs.collapse');
            }, 350);
        }
        
        hide() {
            if (!this._isShown) return;
            
            Util.trigger(this._element, 'hide.bs.collapse');
            this._element.style.height = this._element.scrollHeight + 'px';
            
            setTimeout(() => {
                Util.addClass(this._element, 'collapsing');
                Util.removeClass(this._element, 'collapse show');
                this._element.style.height = '0px';
                
                setTimeout(() => {
                    Util.removeClass(this._element, 'collapsing');
                    Util.addClass(this._element, 'collapse');
                    this._element.style.height = '';
                    this._isShown = false;
                    Util.trigger(this._element, 'hidden.bs.collapse');
                }, 350);
            }, 10);
        }
        
        static getInstance(element) {
            return element._bsCollapse || new Collapse(element);
        }
    }

    // 自动初始化
    document.addEventListener('DOMContentLoaded', function() {
        // 初始化Alert
        document.querySelectorAll('.alert').forEach(element => {
            element._bsAlert = new Alert(element);
        });
        
        // 初始化Modal触发器
        document.querySelectorAll('[data-bs-toggle="modal"]').forEach(trigger => {
            trigger.addEventListener('click', function(e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('data-bs-target'));
                if (target) {
                    const modal = Modal.getInstance(target);
                    modal.show();
                }
            });
        });
        
        // 初始化Dropdown
        document.querySelectorAll('[data-bs-toggle="dropdown"]').forEach(element => {
            element._bsDropdown = new Dropdown(element);
        });
        
        // 初始化Collapse
        document.querySelectorAll('.collapse').forEach(element => {
            element._bsCollapse = new Collapse(element);
        });
    });

    // 导出到全局
    window.bootstrap = {
        Alert: Alert,
        Modal: Modal,
        Dropdown: Dropdown,
        Collapse: Collapse,
        Util: Util
    };

})();
