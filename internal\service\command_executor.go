package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
)

// CommandExecutor 安全命令执行器
type CommandExecutor struct {
	logger      *logrus.Logger
	sshPool     *SSHConnectionPool
	validator   *CommandValidator
	sandbox     *CommandSandbox
	auditLogger *AuditLogger
}

// CommandValidator 命令验证器
type CommandValidator struct {
	logger            *logrus.Logger
	allowedCommands   map[string]bool
	blockedCommands   map[string]bool
	dangerousPatterns []*regexp.Regexp
	safePatterns      []*regexp.Regexp
}

// CommandSandbox 命令沙箱
type CommandSandbox struct {
	logger           *logrus.Logger
	maxExecutionTime time.Duration
	maxOutputSize    int64
	allowedPaths     []string
	blockedPaths     []string
	resourceLimits   *ResourceLimits
}

// ResourceLimits 资源限制
type ResourceLimits struct {
	MaxCPUPercent   int `json:"max_cpu_percent"`
	MaxMemoryMB     int `json:"max_memory_mb"`
	MaxDiskReadMB   int `json:"max_disk_read_mb"`
	MaxDiskWriteMB  int `json:"max_disk_write_mb"`
	MaxNetworkKBps  int `json:"max_network_kbps"`
	MaxProcessCount int `json:"max_process_count"`
}

// AuditLogger 审计日志记录器
type AuditLogger struct {
	logger *logrus.Logger
}

// CommandExecutionContext 命令执行上下文
type CommandExecutionContext struct {
	UserID      int64                  `json:"user_id"`
	SessionID   string                 `json:"session_id"`
	HostID      int64                  `json:"host_id"`
	Command     string                 `json:"command"`
	WorkingDir  string                 `json:"working_dir"`
	Environment map[string]string      `json:"environment"`
	Timeout     time.Duration          `json:"timeout"`
	RiskLevel   RiskLevel              `json:"risk_level"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// RiskLevel 风险等级
type RiskLevel string

const (
	RiskLevelSafe     RiskLevel = "safe"
	RiskLevelLow      RiskLevel = "low"
	RiskLevelMedium   RiskLevel = "medium"
	RiskLevelHigh     RiskLevel = "high"
	RiskLevelCritical RiskLevel = "critical"
)

// CommandExecutionResult 命令执行结果
type CommandExecutionResult struct {
	Success       bool                   `json:"success"`
	Output        string                 `json:"output"`
	Error         string                 `json:"error"`
	ExitCode      int                    `json:"exit_code"`
	Duration      time.Duration          `json:"duration"`
	ResourceUsage *ResourceUsage         `json:"resource_usage"`
	SecurityInfo  *SecurityInfo          `json:"security_info"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// ResourceUsage 资源使用情况
type ResourceUsage struct {
	CPUPercent   float64 `json:"cpu_percent"`
	MemoryMB     int64   `json:"memory_mb"`
	DiskReadMB   int64   `json:"disk_read_mb"`
	DiskWriteMB  int64   `json:"disk_write_mb"`
	NetworkInKB  int64   `json:"network_in_kb"`
	NetworkOutKB int64   `json:"network_out_kb"`
	ProcessCount int     `json:"process_count"`
}

// SecurityInfo 安全信息
type SecurityInfo struct {
	RiskLevel         RiskLevel `json:"risk_level"`
	SecurityWarnings  []string  `json:"security_warnings"`
	BlockedAttempts   []string  `json:"blocked_attempts"`
	SandboxViolations []string  `json:"sandbox_violations"`
}

// NewCommandExecutor 创建命令执行器
func NewCommandExecutor(logger *logrus.Logger, sshPool *SSHConnectionPool) *CommandExecutor {
	return &CommandExecutor{
		logger:      logger,
		sshPool:     sshPool,
		validator:   NewCommandValidator(logger),
		sandbox:     NewCommandSandbox(logger),
		auditLogger: NewAuditLogger(logger),
	}
}

// NewCommandValidator 创建命令验证器
func NewCommandValidator(logger *logrus.Logger) *CommandValidator {
	validator := &CommandValidator{
		logger:          logger,
		allowedCommands: make(map[string]bool),
		blockedCommands: make(map[string]bool),
	}

	// 初始化安全命令列表
	validator.initializeSafeCommands()
	validator.initializeDangerousPatterns()

	return validator
}

// initializeSafeCommands 初始化安全命令列表
func (cv *CommandValidator) initializeSafeCommands() {
	// 安全的只读命令
	safeCommands := []string{
		"ls", "cat", "head", "tail", "grep", "find", "locate",
		"ps", "top", "htop", "free", "df", "du", "uptime",
		"who", "w", "id", "whoami", "groups", "date",
		"uname", "hostname", "pwd", "which", "whereis",
		"netstat", "ss", "lsof", "iftop", "iostat", "vmstat",
		"journalctl", "dmesg", "last", "lastlog", "history",
		"env", "printenv", "echo", "wc", "sort", "uniq",
		"awk", "sed", "cut", "tr", "tee", "less", "more",
		"file", "stat", "lsattr", "getfacl", "mount",
	}

	for _, cmd := range safeCommands {
		cv.allowedCommands[cmd] = true
	}

	// 危险命令列表
	dangerousCommands := []string{
		"rm", "rmdir", "dd", "mkfs", "fdisk", "parted",
		"shutdown", "reboot", "halt", "poweroff", "init",
		"passwd", "userdel", "useradd", "usermod", "groupdel",
		"chmod", "chown", "chgrp", "setfacl", "setcap",
		"iptables", "ufw", "firewall-cmd", "systemctl",
		"service", "kill", "killall", "pkill", "nohup",
		"crontab", "at", "batch", "su", "sudo", "visudo",
		"mount", "umount", "fsck", "e2fsck", "xfs_repair",
		"format", "del", "copy", "move", "xcopy", "robocopy",
	}

	for _, cmd := range dangerousCommands {
		cv.blockedCommands[cmd] = true
	}
}

// initializeDangerousPatterns 初始化危险模式
func (cv *CommandValidator) initializeDangerousPatterns() {
	dangerousPatterns := []string{
		`rm\s+-rf\s+/`,                 // 删除根目录
		`dd\s+if=.*of=/dev/`,           // 磁盘写入
		`>\s*/dev/`,                    // 重定向到设备
		`chmod\s+777`,                  // 危险权限
		`chown\s+-R`,                   // 递归更改所有者
		`find\s+.*-exec\s+rm`,          // 查找并删除
		`curl\s+.*\|\s*sh`,             // 下载并执行
		`wget\s+.*\|\s*sh`,             // 下载并执行
		`eval\s+\$\(`,                  // 动态执行
		`\$\(.*\)`,                     // 命令替换
		"`.*`",                         // 反引号命令替换
		`;\s*(rm|dd|shutdown|reboot)`,  // 命令链中的危险操作
		`&&\s*(rm|dd|shutdown|reboot)`, // 条件执行危险操作
		`\|\s*(rm|dd|shutdown|reboot)`, // 管道到危险操作
	}

	cv.dangerousPatterns = make([]*regexp.Regexp, len(dangerousPatterns))
	for i, pattern := range dangerousPatterns {
		cv.dangerousPatterns[i] = regexp.MustCompile(pattern)
	}

	// 安全模式
	safePatterns := []string{
		`^(ls|cat|head|tail|grep|find)\s+`,
		`^(ps|top|free|df|du|uptime)\s*`,
		`^(who|w|id|whoami|date|uname)\s*`,
		`^(netstat|ss|lsof|iostat)\s+`,
		`^echo\s+`,
		`^pwd\s*$`,
		`^which\s+`,
		`^whereis\s+`,
	}

	cv.safePatterns = make([]*regexp.Regexp, len(safePatterns))
	for i, pattern := range safePatterns {
		cv.safePatterns[i] = regexp.MustCompile(pattern)
	}
}

// ValidateCommand 验证命令安全性
func (cv *CommandValidator) ValidateCommand(command string) (*ValidationResult, error) {
	result := &ValidationResult{
		IsValid:   true,
		RiskLevel: RiskLevelSafe,
		Warnings:  make([]string, 0),
		Blocks:    make([]string, 0),
	}

	// 清理命令
	cleanCmd := strings.TrimSpace(command)
	if cleanCmd == "" {
		result.IsValid = false
		result.Blocks = append(result.Blocks, "Empty command")
		return result, nil
	}

	// 检查危险模式
	for _, pattern := range cv.dangerousPatterns {
		if pattern.MatchString(cleanCmd) {
			result.IsValid = false
			result.RiskLevel = RiskLevelCritical
			result.Blocks = append(result.Blocks, fmt.Sprintf("Dangerous pattern detected: %s", pattern.String()))
		}
	}

	// 提取主命令
	parts := strings.Fields(cleanCmd)
	if len(parts) == 0 {
		result.IsValid = false
		result.Blocks = append(result.Blocks, "No command found")
		return result, nil
	}

	mainCmd := parts[0]

	// 检查是否在阻止列表中
	if cv.blockedCommands[mainCmd] {
		result.IsValid = false
		result.RiskLevel = RiskLevelHigh
		result.Blocks = append(result.Blocks, fmt.Sprintf("Command '%s' is blocked", mainCmd))
	}

	// 检查是否在允许列表中
	if cv.allowedCommands[mainCmd] {
		// 检查安全模式
		isSafe := false
		for _, pattern := range cv.safePatterns {
			if pattern.MatchString(cleanCmd) {
				isSafe = true
				break
			}
		}
		if !isSafe {
			result.RiskLevel = RiskLevelLow
			result.Warnings = append(result.Warnings, "Command parameters may need review")
		}
	} else {
		// 未知命令，需要谨慎处理
		result.RiskLevel = RiskLevelMedium
		result.Warnings = append(result.Warnings, fmt.Sprintf("Unknown command '%s', proceed with caution", mainCmd))
	}

	// 检查特殊字符和操作符
	if strings.Contains(cleanCmd, "|") || strings.Contains(cleanCmd, ">") || strings.Contains(cleanCmd, "<") {
		if result.RiskLevel < RiskLevelMedium {
			result.RiskLevel = RiskLevelMedium
		}
		result.Warnings = append(result.Warnings, "Command contains redirection or pipes")
	}

	if strings.Contains(cleanCmd, ";") || strings.Contains(cleanCmd, "&&") || strings.Contains(cleanCmd, "||") {
		if result.RiskLevel < RiskLevelMedium {
			result.RiskLevel = RiskLevelMedium
		}
		result.Warnings = append(result.Warnings, "Command contains multiple operations")
	}

	return result, nil
}

// ValidationResult 验证结果
type ValidationResult struct {
	IsValid   bool      `json:"is_valid"`
	RiskLevel RiskLevel `json:"risk_level"`
	Warnings  []string  `json:"warnings"`
	Blocks    []string  `json:"blocks"`
}

// NewCommandSandbox 创建命令沙箱
func NewCommandSandbox(logger *logrus.Logger) *CommandSandbox {
	return &CommandSandbox{
		logger:           logger,
		maxExecutionTime: 5 * time.Minute,
		maxOutputSize:    10 * 1024 * 1024, // 10MB
		allowedPaths: []string{
			"/home", "/tmp", "/var/log", "/etc", "/usr", "/bin", "/sbin",
		},
		blockedPaths: []string{
			"/dev", "/proc/sys", "/sys", "/boot",
		},
		resourceLimits: &ResourceLimits{
			MaxCPUPercent:   80,
			MaxMemoryMB:     512,
			MaxDiskReadMB:   100,
			MaxDiskWriteMB:  10,
			MaxNetworkKBps:  1024,
			MaxProcessCount: 10,
		},
	}
}

// NewAuditLogger 创建审计日志记录器
func NewAuditLogger(logger *logrus.Logger) *AuditLogger {
	return &AuditLogger{
		logger: logger,
	}
}

// ExecuteCommand 安全执行命令
func (ce *CommandExecutor) ExecuteCommand(ctx context.Context, execCtx *CommandExecutionContext, host *model.Host) (*CommandExecutionResult, error) {
	// 1. 审计日志记录
	ce.auditLogger.LogCommandAttempt(execCtx)

	// 2. 命令验证
	validation, err := ce.validator.ValidateCommand(execCtx.Command)
	if err != nil {
		return nil, fmt.Errorf("command validation failed: %w", err)
	}

	if !validation.IsValid {
		ce.auditLogger.LogCommandBlocked(execCtx, validation.Blocks)
		return &CommandExecutionResult{
			Success: false,
			Error:   fmt.Sprintf("Command blocked: %s", strings.Join(validation.Blocks, "; ")),
			SecurityInfo: &SecurityInfo{
				RiskLevel:       validation.RiskLevel,
				BlockedAttempts: validation.Blocks,
			},
		}, nil
	}

	// 3. 设置执行上下文
	execCtx.RiskLevel = validation.RiskLevel

	// 4. 沙箱执行
	result, err := ce.executeInSandbox(ctx, execCtx, host)
	if err != nil {
		ce.auditLogger.LogCommandError(execCtx, err)
		return nil, err
	}

	// 5. 记录执行结果
	ce.auditLogger.LogCommandResult(execCtx, result)

	return result, nil
}

// executeInSandbox 在沙箱中执行命令
func (ce *CommandExecutor) executeInSandbox(ctx context.Context, execCtx *CommandExecutionContext, host *model.Host) (*CommandExecutionResult, error) {
	// 构建安全命令
	safeCommand := ce.sandbox.WrapCommand(execCtx.Command, execCtx.WorkingDir)

	// 执行命令
	cmdResult, err := ce.sshPool.ExecuteCommand(host, safeCommand, execCtx.Timeout)
	if err != nil {
		return &CommandExecutionResult{
			Success: false,
			Error:   err.Error(),
			SecurityInfo: &SecurityInfo{
				RiskLevel: execCtx.RiskLevel,
			},
		}, nil
	}

	// 构建结果
	result := &CommandExecutionResult{
		Success:  cmdResult.ExitCode == 0,
		Output:   cmdResult.Output,
		Error:    cmdResult.Error,
		ExitCode: cmdResult.ExitCode,
		Duration: cmdResult.Duration,
		SecurityInfo: &SecurityInfo{
			RiskLevel: execCtx.RiskLevel,
		},
		Metadata: map[string]interface{}{
			"original_command": execCtx.Command,
			"safe_command":     safeCommand,
			"host_id":          execCtx.HostID,
			"user_id":          execCtx.UserID,
			"session_id":       execCtx.SessionID,
		},
	}

	return result, nil
}

// WrapCommand 包装命令以增加安全性
func (cs *CommandSandbox) WrapCommand(command, workingDir string) string {
	// 基本的命令包装，添加超时和资源限制
	wrappedCmd := command

	// 设置工作目录
	if workingDir != "" && cs.isPathAllowed(workingDir) {
		wrappedCmd = fmt.Sprintf("cd %s && %s", workingDir, wrappedCmd)
	}

	// 添加超时保护
	wrappedCmd = fmt.Sprintf("timeout %d %s", int(cs.maxExecutionTime.Seconds()), wrappedCmd)

	// 限制输出大小
	wrappedCmd = fmt.Sprintf("%s | head -c %d", wrappedCmd, cs.maxOutputSize)

	return wrappedCmd
}

// isPathAllowed 检查路径是否被允许
func (cs *CommandSandbox) isPathAllowed(path string) bool {
	// 检查是否在阻止列表中
	for _, blocked := range cs.blockedPaths {
		if strings.HasPrefix(path, blocked) {
			return false
		}
	}

	// 检查是否在允许列表中
	for _, allowed := range cs.allowedPaths {
		if strings.HasPrefix(path, allowed) {
			return true
		}
	}

	return false
}

// LogCommandAttempt 记录命令尝试
func (al *AuditLogger) LogCommandAttempt(execCtx *CommandExecutionContext) {
	al.logger.WithFields(logrus.Fields{
		"event":      "command_attempt",
		"user_id":    execCtx.UserID,
		"session_id": execCtx.SessionID,
		"host_id":    execCtx.HostID,
		"command":    execCtx.Command,
		"risk_level": execCtx.RiskLevel,
	}).Info("Command execution attempted")
}

// LogCommandBlocked 记录命令被阻止
func (al *AuditLogger) LogCommandBlocked(execCtx *CommandExecutionContext, reasons []string) {
	al.logger.WithFields(logrus.Fields{
		"event":      "command_blocked",
		"user_id":    execCtx.UserID,
		"session_id": execCtx.SessionID,
		"host_id":    execCtx.HostID,
		"command":    execCtx.Command,
		"reasons":    reasons,
	}).Warn("Command execution blocked")
}

// LogCommandError 记录命令错误
func (al *AuditLogger) LogCommandError(execCtx *CommandExecutionContext, err error) {
	al.logger.WithFields(logrus.Fields{
		"event":      "command_error",
		"user_id":    execCtx.UserID,
		"session_id": execCtx.SessionID,
		"host_id":    execCtx.HostID,
		"command":    execCtx.Command,
		"error":      err.Error(),
	}).Error("Command execution error")
}

// LogCommandResult 记录命令结果
func (al *AuditLogger) LogCommandResult(execCtx *CommandExecutionContext, result *CommandExecutionResult) {
	al.logger.WithFields(logrus.Fields{
		"event":      "command_completed",
		"user_id":    execCtx.UserID,
		"session_id": execCtx.SessionID,
		"host_id":    execCtx.HostID,
		"command":    execCtx.Command,
		"success":    result.Success,
		"exit_code":  result.ExitCode,
		"duration":   result.Duration,
		"risk_level": result.SecurityInfo.RiskLevel,
	}).Info("Command execution completed")
}
