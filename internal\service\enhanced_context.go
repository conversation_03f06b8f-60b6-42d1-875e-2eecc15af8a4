package service

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedContextManager 增强的上下文管理器
type EnhancedContextManager struct {
	*ContextManager
	db                *gorm.DB
	logger            *logrus.Logger
	intentRecognizer  *IntentRecognizer
	conversationCache map[string]*EnhancedConversationContext
	mutex             sync.RWMutex
}

// EnhancedConversationContext 增强的对话上下文
type EnhancedConversationContext struct {
	SessionID        string                   `json:"session_id"`
	UserID           int64                    `json:"user_id"`
	Messages         []EnhancedContextMessage `json:"messages"`
	Variables        map[string]interface{}   `json:"variables"`
	IntentHistory    []IntentRecord           `json:"intent_history"`
	TopicFlow        []TopicTransition        `json:"topic_flow"`
	UserPreferences  map[string]interface{}   `json:"user_preferences"`
	ContextSummary   string                   `json:"context_summary"`
	LastActivity     time.Time                `json:"last_activity"`
	MaxMessages      int                      `json:"max_messages"`
	CompressionLevel int                      `json:"compression_level"`
}

// 使用conversation_context_enhancer.go中定义的EnhancedContextMessage，避免重复定义

// IntentRecord 意图记录
type IntentRecord struct {
	Intent     string                 `json:"intent"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Timestamp  time.Time              `json:"timestamp"`
	Resolved   bool                   `json:"resolved"`
	FollowUp   string                 `json:"follow_up,omitempty"`
}

// TopicTransition 话题转换
type TopicTransition struct {
	FromTopic  string    `json:"from_topic"`
	ToTopic    string    `json:"to_topic"`
	Trigger    string    `json:"trigger"`
	Timestamp  time.Time `json:"timestamp"`
	Confidence float64   `json:"confidence"`
}

// NewEnhancedContextManager 创建增强的上下文管理器
func NewEnhancedContextManager(db *gorm.DB, logger *logrus.Logger, intentRecognizer *IntentRecognizer) *EnhancedContextManager {
	baseManager := NewContextManager(db, logger)

	return &EnhancedContextManager{
		ContextManager:    baseManager,
		db:                db,
		logger:            logger,
		intentRecognizer:  intentRecognizer,
		conversationCache: make(map[string]*EnhancedConversationContext),
		mutex:             sync.RWMutex{},
	}
}

// GetEnhancedContext 获取增强的对话上下文
func (ecm *EnhancedContextManager) GetEnhancedContext(sessionID string) (*EnhancedConversationContext, error) {
	ecm.mutex.RLock()
	if ctx, exists := ecm.conversationCache[sessionID]; exists {
		ecm.mutex.RUnlock()
		return ctx, nil
	}
	ecm.mutex.RUnlock()

	// 从基础上下文管理器加载
	baseCtx, err := ecm.ContextManager.GetContext(sessionID)
	if err != nil {
		return nil, err
	}

	// 转换为增强上下文
	enhancedCtx := &EnhancedConversationContext{
		SessionID:        sessionID,
		UserID:           baseCtx.UserID,
		Messages:         ecm.convertToEnhancedMessages(baseCtx.Messages),
		Variables:        baseCtx.Variables,
		IntentHistory:    []IntentRecord{},
		TopicFlow:        []TopicTransition{},
		UserPreferences:  make(map[string]interface{}),
		LastActivity:     baseCtx.LastActivity,
		MaxMessages:      baseCtx.MaxMessages,
		CompressionLevel: 0,
	}

	// 分析现有消息的意图历史
	ecm.analyzeIntentHistory(enhancedCtx)

	// 分析话题流转
	ecm.analyzeTopicFlow(enhancedCtx)

	// 缓存增强上下文
	ecm.mutex.Lock()
	ecm.conversationCache[sessionID] = enhancedCtx
	ecm.mutex.Unlock()

	return enhancedCtx, nil
}

// AddEnhancedMessage 添加增强消息
func (ecm *EnhancedContextManager) AddEnhancedMessage(sessionID string, message *EnhancedContextMessage) error {
	ecm.mutex.Lock()
	defer ecm.mutex.Unlock()

	ctx, exists := ecm.conversationCache[sessionID]
	if !exists {
		return fmt.Errorf("enhanced context not found: %s", sessionID)
	}

	// 分析消息意图（如果是用户消息）
	if message.ContextMessage.Role == "user" && ecm.intentRecognizer != nil {
		intent, err := ecm.intentRecognizer.RecognizeIntent(context.Background(), message.ContextMessage.Content)
		if err == nil {
			message.Intent = intent.Type
			// 将置信度存储在Metadata中，因为EnhancedContextMessage没有Confidence字段
			if message.ContextMessage.Metadata == nil {
				message.ContextMessage.Metadata = make(map[string]interface{})
			}
			message.ContextMessage.Metadata["confidence"] = intent.Confidence

			// 添加到意图历史
			intentRecord := IntentRecord{
				Intent:     intent.Type,
				Confidence: intent.Confidence,
				Parameters: intent.Parameters,
				Timestamp:  message.ContextMessage.Timestamp,
				Resolved:   false,
			}
			ctx.IntentHistory = append(ctx.IntentHistory, intentRecord)
		}
	}

	// 设置消息重要性
	message.Importance = float64(ecm.calculateMessageImportance(message, ctx))

	// 添加消息
	ctx.Messages = append(ctx.Messages, *message)
	ctx.LastActivity = time.Now()

	// 检查是否需要压缩上下文
	if len(ctx.Messages) > ctx.MaxMessages {
		ecm.compressContext(ctx)
	}

	// 更新话题流转
	ecm.updateTopicFlow(ctx, message)

	return nil
}

// calculateMessageImportance 计算消息重要性
func (ecm *EnhancedContextManager) calculateMessageImportance(message *EnhancedContextMessage, ctx *EnhancedConversationContext) int {
	importance := 5 // 默认重要性

	// 基于角色调整
	if message.ContextMessage.Role == "user" {
		importance += 1
	} else if message.ContextMessage.Role == "assistant" {
		// 检查是否有工具调用（从Metadata中获取）
		if toolCalls, exists := message.ContextMessage.Metadata["tool_calls"]; exists {
			if count, ok := toolCalls.(int); ok && count > 0 {
				importance += 2 // 包含工具调用的回复更重要
			}
		}
	}

	// 基于意图调整
	if message.Intent != "" {
		switch message.Intent {
		case "emergency", "critical_issue":
			importance += 3
		case "system_check", "monitoring":
			importance += 2
		case "general_chat":
			importance -= 1
		}
	}

	// 基于置信度调整（从Metadata中获取）
	if confidence, exists := message.ContextMessage.Metadata["confidence"]; exists {
		if conf, ok := confidence.(float64); ok {
			if conf > 0.8 {
				importance += 1
			} else if conf < 0.5 {
				importance -= 1
			}
		}
	}

	// 确保在1-10范围内
	if importance < 1 {
		importance = 1
	} else if importance > 10 {
		importance = 10
	}

	return importance
}

// compressContext 压缩上下文
func (ecm *EnhancedContextManager) compressContext(ctx *EnhancedConversationContext) {
	if len(ctx.Messages) <= ctx.MaxMessages {
		return
	}

	// 按重要性排序，保留重要消息
	importantMessages := make([]EnhancedContextMessage, 0)
	recentMessages := make([]EnhancedContextMessage, 0)

	// 保留最近的消息
	recentCount := ctx.MaxMessages / 2
	if recentCount < 10 {
		recentCount = 10
	}

	startIndex := len(ctx.Messages) - recentCount
	if startIndex < 0 {
		startIndex = 0
	}

	recentMessages = ctx.Messages[startIndex:]

	// 从较早的消息中选择重要的
	for i := 0; i < startIndex; i++ {
		msg := ctx.Messages[i]
		if msg.Importance >= 8 { // 只保留高重要性消息
			importantMessages = append(importantMessages, msg)
		}
	}

	// 合并消息
	ctx.Messages = append(importantMessages, recentMessages...)
	ctx.CompressionLevel++

	ecm.logger.WithFields(logrus.Fields{
		"session_id":        ctx.SessionID,
		"compressed_to":     len(ctx.Messages),
		"compression_level": ctx.CompressionLevel,
	}).Info("Context compressed")
}

// analyzeIntentHistory 分析意图历史
func (ecm *EnhancedContextManager) analyzeIntentHistory(ctx *EnhancedConversationContext) {
	// 从消息中提取意图历史
	for _, msg := range ctx.Messages {
		if msg.ContextMessage.Role == "user" && msg.Intent != "" {
			confidence := 0.5 // 默认置信度
			if conf, exists := msg.ContextMessage.Metadata["confidence"]; exists {
				if c, ok := conf.(float64); ok {
					confidence = c
				}
			}

			intentRecord := IntentRecord{
				Intent:     msg.Intent,
				Confidence: confidence,
				Timestamp:  msg.ContextMessage.Timestamp,
				Resolved:   ecm.isIntentResolved(msg.Intent, ctx.Messages, msg.ContextMessage.Timestamp),
			}
			ctx.IntentHistory = append(ctx.IntentHistory, intentRecord)
		}
	}
}

// analyzeTopicFlow 分析话题流转
func (ecm *EnhancedContextManager) analyzeTopicFlow(ctx *EnhancedConversationContext) {
	if len(ctx.Messages) < 2 {
		return
	}

	var lastTopic string
	for _, msg := range ctx.Messages {
		if msg.ContextMessage.Role == "user" && msg.Intent != "" {
			currentTopic := ecm.extractTopicFromIntent(msg.Intent)
			if lastTopic != "" && lastTopic != currentTopic {
				confidence := 0.5 // 默认置信度
				if conf, exists := msg.ContextMessage.Metadata["confidence"]; exists {
					if c, ok := conf.(float64); ok {
						confidence = c
					}
				}

				transition := TopicTransition{
					FromTopic:  lastTopic,
					ToTopic:    currentTopic,
					Trigger:    msg.ContextMessage.Content,
					Timestamp:  msg.ContextMessage.Timestamp,
					Confidence: confidence,
				}
				ctx.TopicFlow = append(ctx.TopicFlow, transition)
			}
			lastTopic = currentTopic
		}
	}
}

// updateTopicFlow 更新话题流转
func (ecm *EnhancedContextManager) updateTopicFlow(ctx *EnhancedConversationContext, message *EnhancedContextMessage) {
	if message.ContextMessage.Role != "user" || message.Intent == "" {
		return
	}

	currentTopic := ecm.extractTopicFromIntent(message.Intent)

	// 获取最后一个话题
	var lastTopic string
	if len(ctx.TopicFlow) > 0 {
		lastTopic = ctx.TopicFlow[len(ctx.TopicFlow)-1].ToTopic
	} else if len(ctx.IntentHistory) > 0 {
		lastTopic = ecm.extractTopicFromIntent(ctx.IntentHistory[len(ctx.IntentHistory)-1].Intent)
	}

	// 如果话题发生变化，记录转换
	if lastTopic != "" && lastTopic != currentTopic {
		confidence := 0.5 // 默认置信度
		if conf, exists := message.ContextMessage.Metadata["confidence"]; exists {
			if c, ok := conf.(float64); ok {
				confidence = c
			}
		}

		transition := TopicTransition{
			FromTopic:  lastTopic,
			ToTopic:    currentTopic,
			Trigger:    message.ContextMessage.Content,
			Timestamp:  message.ContextMessage.Timestamp,
			Confidence: confidence,
		}
		ctx.TopicFlow = append(ctx.TopicFlow, transition)
	}
}

// convertToEnhancedMessages 转换为增强消息
func (ecm *EnhancedContextManager) convertToEnhancedMessages(messages []ContextMessage) []EnhancedContextMessage {
	enhanced := make([]EnhancedContextMessage, len(messages))
	for i, msg := range messages {
		enhanced[i] = EnhancedContextMessage{
			ContextMessage: msg,
			MessageID:      fmt.Sprintf("msg_%d", i),
			Importance:     5.0, // 默认重要性
		}
	}
	return enhanced
}

// isIntentResolved 检查意图是否已解决
func (ecm *EnhancedContextManager) isIntentResolved(intent string, messages []EnhancedContextMessage, intentTime time.Time) bool {
	// 简单的启发式：如果在意图之后有助手回复，认为已解决
	for _, msg := range messages {
		if msg.Timestamp.After(intentTime) && msg.Role == "assistant" {
			return true
		}
	}
	return false
}

// extractTopicFromIntent 从意图中提取话题
func (ecm *EnhancedContextManager) extractTopicFromIntent(intent string) string {
	// 简单的话题映射
	topicMap := map[string]string{
		"system_check":    "system_monitoring",
		"host_management": "infrastructure",
		"log_analysis":    "troubleshooting",
		"performance":     "optimization",
		"security":        "security",
		"general_chat":    "general",
	}

	if topic, exists := topicMap[intent]; exists {
		return topic
	}

	// 默认基于意图名称
	parts := strings.Split(intent, "_")
	if len(parts) > 0 {
		return parts[0]
	}

	return "general"
}
