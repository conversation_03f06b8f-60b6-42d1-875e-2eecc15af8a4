package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
)

// 🤖 AI组件 - 负责智能分析、生成和渲染

// AIIntentAnalyzer AI意图分析器
type AIIntentAnalyzer struct {
	deepseekClient *DeepSeekClient
	logger         *logrus.Logger
}

// AIContentGenerator AI内容生成器
type AIContentGenerator struct {
	deepseekClient *DeepSeekClient
	logger         *logrus.Logger
}

// AIResponseRenderer AI响应渲染器
type AIResponseRenderer struct {
	deepseekClient *DeepSeekClient
	logger         *logrus.Logger
}

// AISafetyValidator AI安全验证器
type AISafetyValidator struct {
	deepseekClient *DeepSeekClient
	logger         *logrus.Logger
}

// RenderRequest 渲染请求
type RenderRequest struct {
	Analysis      *AIAnalysisResult  `json:"analysis"`
	ExecutionData interface{}        `json:"execution_data"`
	UserInput     string             `json:"user_input"`
	Preferences   *UserPreferences   `json:"preferences"`
}

// NewAIIntentAnalyzer 创建AI意图分析器
func NewAIIntentAnalyzer(deepseekClient *DeepSeekClient, logger *logrus.Logger) *AIIntentAnalyzer {
	return &AIIntentAnalyzer{
		deepseekClient: deepseekClient,
		logger:         logger,
	}
}

// NewAIContentGenerator 创建AI内容生成器
func NewAIContentGenerator(deepseekClient *DeepSeekClient, logger *logrus.Logger) *AIContentGenerator {
	return &AIContentGenerator{
		deepseekClient: deepseekClient,
		logger:         logger,
	}
}

// NewAIResponseRenderer 创建AI响应渲染器
func NewAIResponseRenderer(deepseekClient *DeepSeekClient, logger *logrus.Logger) *AIResponseRenderer {
	return &AIResponseRenderer{
		deepseekClient: deepseekClient,
		logger:         logger,
	}
}

// NewAISafetyValidator 创建AI安全验证器
func NewAISafetyValidator(deepseekClient *DeepSeekClient, logger *logrus.Logger) *AISafetyValidator {
	return &AISafetyValidator{
		deepseekClient: deepseekClient,
		logger:         logger,
	}
}

// Validate 安全验证
func (asv *AISafetyValidator) Validate(
	ctx context.Context,
	analysis *AIAnalysisResult,
) (*SafetyInfo, error) {
	asv.logger.WithFields(logrus.Fields{
		"execution_type": analysis.ExecutionType,
		"estimated_risk": analysis.EstimatedRisk,
	}).Info("AISafetyValidator: 开始安全验证")

	// 构建安全验证提示词
	prompt := fmt.Sprintf(`
请对以下操作进行安全评估:

操作类型: %s
生成的代码: %s
预估风险: %s
操作描述: %s

请返回JSON格式的安全评估结果:
{
  "risk_level": "low|medium|high",
  "warnings": ["警告1", "警告2"],
  "suggestions": ["建议1", "建议2"]
}

评估标准:
1. low: 只读操作，无风险
2. medium: 修改操作，有一定风险但可控
3. high: 删除或破坏性操作，高风险

请仔细分析代码内容，识别潜在的安全风险。
`, analysis.ExecutionType, analysis.GeneratedCode, analysis.EstimatedRisk, analysis.Description)

	response, err := asv.deepseekClient.GenerateContent(ctx, &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: "你是一个专业的安全分析师，负责评估运维操作的安全风险。",
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   1000,
		Temperature: 0.1, // 低温度确保一致性
	})

	if err != nil {
		return nil, fmt.Errorf("安全验证失败: %w", err)
	}

	// 解析安全评估结果
	var safetyInfo SafetyInfo
	if err := json.Unmarshal([]byte(response.Content), &safetyInfo); err != nil {
		// 如果JSON解析失败，使用默认安全策略
		safetyInfo = SafetyInfo{
			RiskLevel:   "medium",
			Warnings:    []string{"无法解析安全评估结果，采用中等风险级别"},
			Suggestions: []string{"建议人工审核操作内容"},
		}
	}

	asv.logger.WithFields(logrus.Fields{
		"risk_level":     safetyInfo.RiskLevel,
		"warning_count":  len(safetyInfo.Warnings),
		"suggestion_count": len(safetyInfo.Suggestions),
	}).Info("AISafetyValidator: 安全验证完成")

	return &safetyInfo, nil
}

// Render 渲染响应
func (arr *AIResponseRenderer) Render(
	ctx context.Context,
	req *RenderRequest,
) (string, error) {
	arr.logger.WithFields(logrus.Fields{
		"execution_type": req.Analysis.ExecutionType,
		"user_input":     req.UserInput,
	}).Info("AIResponseRenderer: 开始渲染响应")

	// 构建渲染提示词
	prompt := arr.buildRenderPrompt(req)

	response, err := arr.deepseekClient.GenerateContent(ctx, &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: arr.buildRenderSystemPrompt(),
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   3000,
		Temperature: 0.4,
	})

	if err != nil {
		return "", fmt.Errorf("响应渲染失败: %w", err)
	}

	arr.logger.WithField("response_length", len(response.Content)).Info("AIResponseRenderer: 响应渲染完成")

	return response.Content, nil
}

// buildRenderPrompt 构建渲染提示词
func (arr *AIResponseRenderer) buildRenderPrompt(req *RenderRequest) string {
	var prompt strings.Builder

	prompt.WriteString(fmt.Sprintf("用户输入: %s\n", req.UserInput))
	prompt.WriteString(fmt.Sprintf("操作类型: %s\n", req.Analysis.ExecutionType))
	prompt.WriteString(fmt.Sprintf("操作描述: %s\n", req.Analysis.Description))
	prompt.WriteString(fmt.Sprintf("生成的代码: %s\n", req.Analysis.GeneratedCode))

	// 添加执行结果
	if req.ExecutionData != nil {
		executionDataJSON, _ := json.MarshalIndent(req.ExecutionData, "", "  ")
		prompt.WriteString(fmt.Sprintf("执行结果: %s\n", string(executionDataJSON)))
	}

	// 添加用户偏好
	if req.Preferences != nil {
		prompt.WriteString(fmt.Sprintf("输出格式偏好: %s\n", req.Preferences.OutputFormat))
	}

	prompt.WriteString(`
请根据以上信息生成一个友好、专业的响应内容。要求:

1. 使用Markdown格式
2. 包含操作摘要
3. 如果有数据结果，用表格展示
4. 包含执行的代码
5. 添加适当的emoji图标
6. 语言风格专业但友好
7. 如果是查询结果，要有统计信息

示例格式:
## 📊 操作结果

**操作摘要**: [操作描述]

### 🔍 执行的代码
` + "```sql" + `
[生成的代码]
` + "```" + `

### 📈 查询结果
[表格或其他格式的结果]

### 📋 统计信息
- 总记录数: X条
- 执行时间: X毫秒
`)

	return prompt.String()
}

// buildRenderSystemPrompt 构建渲染系统提示词
func (arr *AIResponseRenderer) buildRenderSystemPrompt() string {
	return `你是一个专业的AI运维助手，负责将执行结果渲染成用户友好的格式。

核心要求:
1. 使用清晰的Markdown格式
2. 添加适当的emoji图标增强可读性
3. 对数据进行合理的格式化和统计
4. 语言风格专业但易懂
5. 突出重要信息
6. 提供有价值的洞察

格式规范:
- 使用二级标题(##)作为主要分区
- 使用三级标题(###)作为子分区
- 代码块使用正确的语言标识
- 表格要对齐美观
- 统计信息要准确

安全提醒:
- 如果操作有风险，要明确提醒
- 对于敏感信息要适当脱敏
- 提供操作建议和最佳实践`
}

// GenerateContent 生成内容
func (acg *AIContentGenerator) GenerateContent(
	ctx context.Context,
	userInput string,
	contentType string,
	context map[string]interface{},
) (string, error) {
	acg.logger.WithFields(logrus.Fields{
		"user_input":   userInput,
		"content_type": contentType,
	}).Info("AIContentGenerator: 开始生成内容")

	prompt := fmt.Sprintf(`
用户需求: %s
内容类型: %s
上下文信息: %v

请生成相应的%s内容，确保:
1. 语法正确
2. 逻辑合理
3. 安全可执行
4. 符合最佳实践

直接返回生成的内容，不需要额外说明。
`, userInput, contentType, context, contentType)

	response, err := acg.deepseekClient.GenerateContent(ctx, &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: fmt.Sprintf("你是一个专业的%s生成器，只返回高质量的%s内容。", contentType, contentType),
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   2000,
		Temperature: 0.3,
	})

	if err != nil {
		return "", fmt.Errorf("内容生成失败: %w", err)
	}

	acg.logger.WithField("generated_length", len(response.Content)).Info("AIContentGenerator: 内容生成完成")

	return response.Content, nil
}
