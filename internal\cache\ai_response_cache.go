package cache

import (
	"context"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// AIResponseCache AI响应缓存
type AIResponseCache struct {
	cache  *MultiLayerCache
	config *AIResponseCacheConfig
	logger *logrus.Logger
	stats  *AIResponseCacheStats
	mutex  sync.RWMutex
}

// AIResponseCacheConfig AI响应缓存配置
type AIResponseCacheConfig struct {
	Enabled           bool          `json:"enabled"`
	TTL               time.Duration `json:"ttl"`
	MaxSize           int64         `json:"max_size"`
	EnableCompression bool          `json:"enable_compression"`
	EnableSimilarity  bool          `json:"enable_similarity"`
	SimilarityThreshold float64     `json:"similarity_threshold"`
	IgnoreUserContext bool          `json:"ignore_user_context"`
	TimeWindow        time.Duration `json:"time_window"`
}

// AIResponseCacheStats AI响应缓存统计
type AIResponseCacheStats struct {
	TotalRequests   int64     `json:"total_requests"`
	CacheHits       int64     `json:"cache_hits"`
	CacheMisses     int64     `json:"cache_misses"`
	HitRate         float64   `json:"hit_rate"`
	AvgResponseTime time.Duration `json:"avg_response_time"`
	LastUpdated     time.Time `json:"last_updated"`
}

// CachedAIResponse 缓存的AI响应
type CachedAIResponse struct {
	Content     string                 `json:"content"`
	Intent      string                 `json:"intent"`
	Parameters  map[string]interface{} `json:"parameters"`
	Confidence  float64                `json:"confidence"`
	TokenCount  int                    `json:"token_count"`
	CachedAt    time.Time              `json:"cached_at"`
	AccessCount int                    `json:"access_count"`
	LastAccess  time.Time              `json:"last_access"`
}

// AIRequestKey AI请求缓存键
type AIRequestKey struct {
	Content     string `json:"content"`
	Intent      string `json:"intent"`
	UserID      int64  `json:"user_id,omitempty"`
	SessionID   string `json:"session_id,omitempty"`
	TimeWindow  int64  `json:"time_window,omitempty"`
}

// NewAIResponseCache 创建AI响应缓存
func NewAIResponseCache(cache *MultiLayerCache, config *AIResponseCacheConfig, logger *logrus.Logger) *AIResponseCache {
	if config == nil {
		config = &AIResponseCacheConfig{
			Enabled:             true,
			TTL:                 30 * time.Minute,
			MaxSize:             10000,
			EnableCompression:   true,
			EnableSimilarity:    true,
			SimilarityThreshold: 0.85,
			IgnoreUserContext:   false,
			TimeWindow:          5 * time.Minute,
		}
	}

	return &AIResponseCache{
		cache:  cache,
		config: config,
		logger: logger,
		stats: &AIResponseCacheStats{
			LastUpdated: time.Now(),
		},
	}
}

// Get 获取缓存的AI响应
func (arc *AIResponseCache) Get(ctx context.Context, content, intent string, userID int64, sessionID string) (*CachedAIResponse, bool) {
	if !arc.config.Enabled {
		return nil, false
	}

	start := time.Now()
	defer func() {
		arc.updateStats(time.Since(start))
	}()

	// 生成缓存键
	key := arc.generateCacheKey(content, intent, userID, sessionID)

	// 从缓存获取
	cached, found := arc.cache.Get(ctx, key)
	if !found {
		arc.incrementMisses()
		return nil, false
	}

	// 反序列化
	response, ok := cached.(*CachedAIResponse)
	if !ok {
		arc.logger.Warn("Invalid cached AI response type")
		arc.incrementMisses()
		return nil, false
	}

	// 更新访问统计
	response.AccessCount++
	response.LastAccess = time.Now()

	// 重新缓存更新后的数据
	arc.cache.Set(ctx, key, response, arc.config.TTL)

	arc.incrementHits()
	arc.logger.WithFields(logrus.Fields{
		"cache_key":    key,
		"intent":       intent,
		"access_count": response.AccessCount,
	}).Debug("AI response cache hit")

	return response, true
}

// Set 设置AI响应缓存
func (arc *AIResponseCache) Set(ctx context.Context, content, intent string, userID int64, sessionID string, response *CachedAIResponse) error {
	if !arc.config.Enabled {
		return nil
	}

	// 生成缓存键
	key := arc.generateCacheKey(content, intent, userID, sessionID)

	// 设置缓存时间戳
	response.CachedAt = time.Now()
	response.LastAccess = time.Now()
	response.AccessCount = 1

	// 存储到缓存
	err := arc.cache.Set(ctx, key, response, arc.config.TTL)
	if err != nil {
		arc.logger.WithError(err).Error("Failed to set AI response cache")
		return err
	}

	arc.logger.WithFields(logrus.Fields{
		"cache_key": key,
		"intent":    intent,
		"ttl":       arc.config.TTL,
	}).Debug("AI response cached")

	return nil
}

// generateCacheKey 生成缓存键
func (arc *AIResponseCache) generateCacheKey(content, intent string, userID int64, sessionID string) string {
	key := AIRequestKey{
		Content: strings.TrimSpace(strings.ToLower(content)),
		Intent:  intent,
	}

	// 根据配置决定是否包含用户上下文
	if !arc.config.IgnoreUserContext {
		key.UserID = userID
		key.SessionID = sessionID
	}

	// 时间窗口
	if arc.config.TimeWindow > 0 {
		key.TimeWindow = time.Now().Truncate(arc.config.TimeWindow).Unix()
	}

	// 生成哈希
	data, _ := json.Marshal(key)
	hash := sha256.Sum256(data)
	return fmt.Sprintf("ai_response:%x", hash)
}

// GetSimilar 获取相似的缓存响应
func (arc *AIResponseCache) GetSimilar(ctx context.Context, content, intent string, userID int64, sessionID string) (*CachedAIResponse, bool) {
	if !arc.config.Enabled || !arc.config.EnableSimilarity {
		return nil, false
	}

	// 这里可以实现相似度匹配算法
	// 简化版本：先尝试精确匹配
	return arc.Get(ctx, content, intent, userID, sessionID)
}

// Clear 清空缓存
func (arc *AIResponseCache) Clear(ctx context.Context) error {
	return arc.cache.Clear(ctx)
}

// GetStats 获取缓存统计
func (arc *AIResponseCache) GetStats() *AIResponseCacheStats {
	arc.mutex.RLock()
	defer arc.mutex.RUnlock()

	stats := *arc.stats
	if stats.TotalRequests > 0 {
		stats.HitRate = float64(stats.CacheHits) / float64(stats.TotalRequests)
	}
	stats.LastUpdated = time.Now()

	return &stats
}

// incrementHits 增加命中计数
func (arc *AIResponseCache) incrementHits() {
	arc.mutex.Lock()
	defer arc.mutex.Unlock()
	arc.stats.CacheHits++
	arc.stats.TotalRequests++
}

// incrementMisses 增加未命中计数
func (arc *AIResponseCache) incrementMisses() {
	arc.mutex.Lock()
	defer arc.mutex.Unlock()
	arc.stats.CacheMisses++
	arc.stats.TotalRequests++
}

// updateStats 更新统计信息
func (arc *AIResponseCache) updateStats(responseTime time.Duration) {
	arc.mutex.Lock()
	defer arc.mutex.Unlock()
	
	// 计算平均响应时间
	if arc.stats.TotalRequests > 0 {
		arc.stats.AvgResponseTime = (arc.stats.AvgResponseTime*time.Duration(arc.stats.TotalRequests-1) + responseTime) / time.Duration(arc.stats.TotalRequests)
	} else {
		arc.stats.AvgResponseTime = responseTime
	}
}

// Cleanup 清理过期缓存
func (arc *AIResponseCache) Cleanup(ctx context.Context) error {
	if arc.cache != nil {
		arc.cache.cleanup()
	}
	return nil
}
