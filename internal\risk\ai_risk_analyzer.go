package risk

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// AIRiskAnalyzer AI风险分析器
type AIRiskAnalyzer struct {
	config         *AIRiskConfig
	logger         *logrus.Logger
	aiService      AIService
	knowledgeBase  *RiskKnowledgeBase
	patternMatcher *RiskPatternMatcher
	contextManager *RiskContextManager
	cache          *RiskAnalysisCache
	mutex          sync.RWMutex
	isRunning      bool
}

// AIRiskConfig AI风险分析配置
type AIRiskConfig struct {
	EnableAIAnalysis      bool          `json:"enable_ai_analysis"`
	AIModel               string        `json:"ai_model"`
	MaxTokens             int           `json:"max_tokens"`
	Temperature           float64       `json:"temperature"`
	AnalysisTimeout       time.Duration `json:"analysis_timeout"`
	CacheExpiration       time.Duration `json:"cache_expiration"`
	MaxCacheSize          int           `json:"max_cache_size"`
	EnableContextLearning bool          `json:"enable_context_learning"`
	EnablePatternMatching bool          `json:"enable_pattern_matching"`
	ConfidenceThreshold   float64       `json:"confidence_threshold"`
}

// AIService AI服务接口
type AIService interface {
	AnalyzeRisk(ctx context.Context, request *RiskAnalysisRequest) (*RiskAnalysisResponse, error)
	GenerateRecommendations(ctx context.Context, risk *RiskAssessment) ([]*RiskRecommendation, error)
	LearnFromFeedback(ctx context.Context, feedback *RiskFeedback) error
}

// RiskAnalysisRequest 风险分析请求
type RiskAnalysisRequest struct {
	Command     string                 `json:"command"`
	Context     *CommandContext        `json:"context"`
	Environment *EnvironmentInfo       `json:"environment"`
	UserProfile *UserProfile           `json:"user_profile"`
	History     []*CommandHistory      `json:"history"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// RiskAnalysisResponse 风险分析响应
type RiskAnalysisResponse struct {
	RiskLevel    string                 `json:"risk_level"`
	Confidence   float64                `json:"confidence"`
	Reasoning    string                 `json:"reasoning"`
	Factors      []*RiskFactor          `json:"factors"`
	Mitigations  []*RiskMitigation      `json:"mitigations"`
	Alternatives []*CommandAlternative  `json:"alternatives"`
	Metadata     map[string]interface{} `json:"metadata"`
	AnalysisTime time.Duration          `json:"analysis_time"`
}

// CommandContext 命令上下文
type CommandContext struct {
	WorkingDirectory string            `json:"working_directory"`
	Environment      map[string]string `json:"environment"`
	User             string            `json:"user"`
	Session          string            `json:"session"`
	Timestamp        time.Time         `json:"timestamp"`
	PreviousCommands []string          `json:"previous_commands"`
}

// EnvironmentInfo 环境信息
type EnvironmentInfo struct {
	OS           string            `json:"os"`
	Architecture string            `json:"architecture"`
	Hostname     string            `json:"hostname"`
	IPAddress    string            `json:"ip_address"`
	Services     []string          `json:"services"`
	Processes    []string          `json:"processes"`
	NetworkPorts []int             `json:"network_ports"`
	FileSystem   *FileSystemInfo   `json:"file_system"`
	Resources    *ResourceInfo     `json:"resources"`
	Security     *SecurityInfo     `json:"security"`
	Metadata     map[string]string `json:"metadata"`
}

// FileSystemInfo 文件系统信息
type FileSystemInfo struct {
	Mounts        []string          `json:"mounts"`
	Permissions   map[string]string `json:"permissions"`
	CriticalPaths []string          `json:"critical_paths"`
	TempDirs      []string          `json:"temp_dirs"`
}

// ResourceInfo 资源信息
type ResourceInfo struct {
	CPUUsage    float64 `json:"cpu_usage"`
	MemoryUsage float64 `json:"memory_usage"`
	DiskUsage   float64 `json:"disk_usage"`
	LoadAverage float64 `json:"load_average"`
	Uptime      int64   `json:"uptime"`
}

// SecurityInfo 安全信息
type SecurityInfo struct {
	SELinuxEnabled  bool     `json:"selinux_enabled"`
	FirewallActive  bool     `json:"firewall_active"`
	SSHKeys         []string `json:"ssh_keys"`
	Certificates    []string `json:"certificates"`
	Vulnerabilities []string `json:"vulnerabilities"`
}

// UserProfile 用户档案
type UserProfile struct {
	Username      string            `json:"username"`
	Role          string            `json:"role"`
	Permissions   []string          `json:"permissions"`
	Experience    string            `json:"experience"`     // beginner, intermediate, expert
	RiskTolerance string            `json:"risk_tolerance"` // low, medium, high
	Preferences   map[string]string `json:"preferences"`
	History       *UserHistory      `json:"history"`
}

// UserHistory 用户历史
type UserHistory struct {
	TotalCommands    int64             `json:"total_commands"`
	RiskyCommands    int64             `json:"risky_commands"`
	SuccessRate      float64           `json:"success_rate"`
	CommonCommands   []string          `json:"common_commands"`
	RecentActivity   []*ActivityRecord `json:"recent_activity"`
	LearningProgress *LearningProgress `json:"learning_progress"`
}

// ActivityRecord 活动记录
type ActivityRecord struct {
	Command   string        `json:"command"`
	RiskLevel string        `json:"risk_level"`
	Outcome   string        `json:"outcome"` // success, failure, cancelled
	Timestamp time.Time     `json:"timestamp"`
	Duration  time.Duration `json:"duration"`
}

// LearningProgress 学习进度
type LearningProgress struct {
	SkillLevel       string   `json:"skill_level"`
	CompletedLessons []string `json:"completed_lessons"`
	Achievements     []string `json:"achievements"`
	WeakAreas        []string `json:"weak_areas"`
	Recommendations  []string `json:"recommendations"`
}

// CommandHistory 命令历史
type CommandHistory struct {
	Command    string        `json:"command"`
	Timestamp  time.Time     `json:"timestamp"`
	ExitCode   int           `json:"exit_code"`
	Output     string        `json:"output"`
	Error      string        `json:"error"`
	Duration   time.Duration `json:"duration"`
	RiskLevel  string        `json:"risk_level"`
	UserAction string        `json:"user_action"` // executed, cancelled, modified
}

// RiskFactor 风险因素
type RiskFactor struct {
	Type        string   `json:"type"`
	Description string   `json:"description"`
	Severity    string   `json:"severity"`
	Impact      string   `json:"impact"`
	Likelihood  float64  `json:"likelihood"`
	Weight      float64  `json:"weight"`
	Evidence    []string `json:"evidence"`
}

// RiskMitigation 风险缓解
type RiskMitigation struct {
	Type          string   `json:"type"`
	Description   string   `json:"description"`
	Actions       []string `json:"actions"`
	Effectiveness float64  `json:"effectiveness"`
	Cost          string   `json:"cost"`
	TimeRequired  string   `json:"time_required"`
}

// CommandAlternative 命令替代方案
type CommandAlternative struct {
	Command     string   `json:"command"`
	Description string   `json:"description"`
	RiskLevel   string   `json:"risk_level"`
	Similarity  float64  `json:"similarity"`
	Benefits    []string `json:"benefits"`
	Limitations []string `json:"limitations"`
}

// RiskRecommendation 风险建议
type RiskRecommendation struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"` // prevention, mitigation, alternative
	Priority    string    `json:"priority"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Actions     []string  `json:"actions"`
	Benefits    []string  `json:"benefits"`
	Risks       []string  `json:"risks"`
	Confidence  float64   `json:"confidence"`
	CreatedAt   time.Time `json:"created_at"`
}

// RiskFeedback 风险反馈
type RiskFeedback struct {
	AnalysisID    string                 `json:"analysis_id"`
	Command       string                 `json:"command"`
	PredictedRisk string                 `json:"predicted_risk"`
	ActualOutcome string                 `json:"actual_outcome"`
	UserRating    int                    `json:"user_rating"` // 1-5
	Comments      string                 `json:"comments"`
	Corrections   map[string]interface{} `json:"corrections"`
	Timestamp     time.Time              `json:"timestamp"`
}

// RiskKnowledgeBase 风险知识库
type RiskKnowledgeBase struct {
	logger       *logrus.Logger
	riskPatterns []*RiskPattern
	commandRules []*CommandRule
	contextRules []*ContextRule
	mitigationDB *MitigationDatabase
	mutex        sync.RWMutex
}

// RiskPattern 风险模式
type RiskPattern struct {
	ID          string   `json:"id"`
	Name        string   `json:"name"`
	Pattern     string   `json:"pattern"`
	RiskLevel   string   `json:"risk_level"`
	Category    string   `json:"category"`
	Description string   `json:"description"`
	Examples    []string `json:"examples"`
	Indicators  []string `json:"indicators"`
	Weight      float64  `json:"weight"`
	Confidence  float64  `json:"confidence"`
}

// CommandRule 命令规则
type CommandRule struct {
	ID          string            `json:"id"`
	Command     string            `json:"command"`
	RiskLevel   string            `json:"risk_level"`
	Conditions  map[string]string `json:"conditions"`
	Exceptions  []string          `json:"exceptions"`
	Description string            `json:"description"`
	Reasoning   string            `json:"reasoning"`
	Weight      float64           `json:"weight"`
}

// ContextRule 上下文规则
type ContextRule struct {
	ID           string                 `json:"id"`
	Name         string                 `json:"name"`
	Conditions   map[string]interface{} `json:"conditions"`
	RiskModifier float64                `json:"risk_modifier"`
	Description  string                 `json:"description"`
	Priority     int                    `json:"priority"`
}

// MitigationDatabase 缓解措施数据库
type MitigationDatabase struct {
	mitigations map[string][]*RiskMitigation
	mutex       sync.RWMutex
}

// RiskPatternMatcher 风险模式匹配器
type RiskPatternMatcher struct {
	logger   *logrus.Logger
	patterns []*CompiledPattern
	mutex    sync.RWMutex
}

// CompiledPattern 编译后的模式
type CompiledPattern struct {
	Pattern    *RiskPattern
	Regex      interface{} // 简化实现
	Matcher    func(string) bool
	Weight     float64
	Confidence float64
}

// RiskContextManager 风险上下文管理器
type RiskContextManager struct {
	logger   *logrus.Logger
	contexts map[string]*RiskContext
	mutex    sync.RWMutex
}

// RiskContext 风险上下文
type RiskContext struct {
	SessionID   string                 `json:"session_id"`
	UserID      string                 `json:"user_id"`
	Commands    []*CommandHistory      `json:"commands"`
	RiskHistory []*RiskAssessment      `json:"risk_history"`
	Patterns    []string               `json:"patterns"`
	Anomalies   []string               `json:"anomalies"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// RiskAnalysisCache 风险分析缓存
type RiskAnalysisCache struct {
	logger     *logrus.Logger
	cache      map[string]*CachedAnalysis
	expiration time.Duration
	maxSize    int
	mutex      sync.RWMutex
}

// CachedAnalysis 缓存的分析结果
type CachedAnalysis struct {
	Key       string                `json:"key"`
	Response  *RiskAnalysisResponse `json:"response"`
	CreatedAt time.Time             `json:"created_at"`
	ExpiresAt time.Time             `json:"expires_at"`
	HitCount  int64                 `json:"hit_count"`
}

// NewAIRiskAnalyzer 创建AI风险分析器
func NewAIRiskAnalyzer(config *AIRiskConfig, aiService AIService, logger *logrus.Logger) *AIRiskAnalyzer {
	if config == nil {
		config = DefaultAIRiskConfig()
	}

	analyzer := &AIRiskAnalyzer{
		config:    config,
		logger:    logger,
		aiService: aiService,
		isRunning: false,
	}

	// 初始化子组件
	analyzer.knowledgeBase = NewRiskKnowledgeBase(logger)
	analyzer.patternMatcher = NewRiskPatternMatcher(logger)
	analyzer.contextManager = NewRiskContextManager(logger)

	if config.MaxCacheSize > 0 {
		analyzer.cache = NewRiskAnalysisCache(config.CacheExpiration, config.MaxCacheSize, logger)
	}

	return analyzer
}

// DefaultAIRiskConfig 默认AI风险配置
func DefaultAIRiskConfig() *AIRiskConfig {
	return &AIRiskConfig{
		EnableAIAnalysis:      true,
		AIModel:               "deepseek-chat",
		MaxTokens:             2048,
		Temperature:           0.1,
		AnalysisTimeout:       30 * time.Second,
		CacheExpiration:       1 * time.Hour,
		MaxCacheSize:          1000,
		EnableContextLearning: true,
		EnablePatternMatching: true,
		ConfidenceThreshold:   0.7,
	}
}

// Start 启动AI风险分析器
func (ara *AIRiskAnalyzer) Start(ctx context.Context) error {
	ara.mutex.Lock()
	defer ara.mutex.Unlock()

	if ara.isRunning {
		return fmt.Errorf("AI risk analyzer is already running")
	}

	ara.logger.Info("Starting AI risk analyzer")

	// 初始化知识库
	if err := ara.knowledgeBase.Initialize(); err != nil {
		return fmt.Errorf("failed to initialize knowledge base: %w", err)
	}

	// 初始化模式匹配器
	if ara.config.EnablePatternMatching {
		if err := ara.patternMatcher.Initialize(ara.knowledgeBase.riskPatterns); err != nil {
			return fmt.Errorf("failed to initialize pattern matcher: %w", err)
		}
	}

	ara.isRunning = true
	ara.logger.Info("AI risk analyzer started successfully")

	return nil
}

// Stop 停止AI风险分析器
func (ara *AIRiskAnalyzer) Stop(ctx context.Context) error {
	ara.mutex.Lock()
	defer ara.mutex.Unlock()

	if !ara.isRunning {
		return nil
	}

	ara.logger.Info("Stopping AI risk analyzer")
	ara.isRunning = false

	return nil
}

// AnalyzeCommand 分析命令风险
func (ara *AIRiskAnalyzer) AnalyzeCommand(ctx context.Context, request *RiskAnalysisRequest) (*RiskAnalysisResponse, error) {
	ara.mutex.RLock()
	defer ara.mutex.RUnlock()

	if !ara.isRunning {
		return nil, fmt.Errorf("AI risk analyzer is not running")
	}

	startTime := time.Now()

	// 检查缓存
	cacheKey := ara.generateCacheKey(request)
	if ara.cache != nil {
		if cached := ara.cache.Get(cacheKey); cached != nil {
			ara.logger.WithField("cache_key", cacheKey).Debug("Risk analysis found in cache")
			return cached.Response, nil
		}
	}

	// 创建分析上下文
	analysisCtx, cancel := context.WithTimeout(ctx, ara.config.AnalysisTimeout)
	defer cancel()

	// 执行多层分析
	response := &RiskAnalysisResponse{
		Factors:      make([]*RiskFactor, 0),
		Mitigations:  make([]*RiskMitigation, 0),
		Alternatives: make([]*CommandAlternative, 0),
		Metadata:     make(map[string]interface{}),
	}

	// 1. 基础模式匹配分析
	if ara.config.EnablePatternMatching {
		patternRisk := ara.analyzeWithPatterns(request)
		ara.mergeRiskAnalysis(response, patternRisk)
	}

	// 2. 知识库规则分析
	ruleRisk := ara.analyzeWithRules(request)
	ara.mergeRiskAnalysis(response, ruleRisk)

	// 3. AI智能分析
	if ara.config.EnableAIAnalysis {
		aiRisk, err := ara.analyzeWithAI(analysisCtx, request)
		if err != nil {
			ara.logger.WithError(err).Warn("AI analysis failed, using rule-based result")
		} else {
			ara.mergeRiskAnalysis(response, aiRisk)
		}
	}

	// 4. 上下文分析
	if ara.config.EnableContextLearning {
		contextRisk := ara.analyzeWithContext(request)
		ara.mergeRiskAnalysis(response, contextRisk)
	}

	// 计算最终风险等级
	ara.calculateFinalRisk(response)

	// 生成建议
	recommendations, err := ara.generateRecommendations(ctx, response)
	if err != nil {
		ara.logger.WithError(err).Warn("Failed to generate recommendations")
	} else {
		response.Metadata["recommendations"] = recommendations
	}

	response.AnalysisTime = time.Since(startTime)

	// 缓存结果
	if ara.cache != nil && response.Confidence >= ara.config.ConfidenceThreshold {
		ara.cache.Set(cacheKey, response)
	}

	// 更新上下文
	if ara.config.EnableContextLearning {
		ara.updateContext(request, response)
	}

	ara.logger.WithFields(logrus.Fields{
		"command":       request.Command,
		"risk_level":    response.RiskLevel,
		"confidence":    response.Confidence,
		"analysis_time": response.AnalysisTime,
	}).Info("Risk analysis completed")

	return response, nil
}

// analyzeWithPatterns 使用模式匹配分析
func (ara *AIRiskAnalyzer) analyzeWithPatterns(request *RiskAnalysisRequest) *RiskAnalysisResponse {
	matches := ara.patternMatcher.MatchCommand(request.Command)

	response := &RiskAnalysisResponse{
		RiskLevel:    "low",
		Confidence:   0.5,
		Reasoning:    "Pattern-based analysis",
		Factors:      make([]*RiskFactor, 0),
		Mitigations:  make([]*RiskMitigation, 0),
		Alternatives: make([]*CommandAlternative, 0),
		Metadata:     map[string]interface{}{"analysis_type": "pattern"},
	}

	if len(matches) > 0 {
		// 找到最高风险的匹配
		highestRisk := matches[0]
		for _, match := range matches {
			if ara.getRiskScore(match.RiskLevel) > ara.getRiskScore(highestRisk.RiskLevel) {
				highestRisk = match
			}
		}

		response.RiskLevel = highestRisk.RiskLevel
		response.Confidence = highestRisk.Confidence
		response.Reasoning = fmt.Sprintf("Matched pattern: %s", highestRisk.Name)

		// 添加风险因素
		response.Factors = append(response.Factors, &RiskFactor{
			Type:        "pattern_match",
			Description: highestRisk.Description,
			Severity:    highestRisk.RiskLevel,
			Impact:      "system",
			Likelihood:  highestRisk.Confidence,
			Weight:      highestRisk.Weight,
			Evidence:    []string{fmt.Sprintf("Pattern: %s", highestRisk.Pattern)},
		})
	}

	return response
}

// analyzeWithRules 使用规则分析
func (ara *AIRiskAnalyzer) analyzeWithRules(request *RiskAnalysisRequest) *RiskAnalysisResponse {
	rules := ara.knowledgeBase.MatchCommandRules(request.Command, request.Context)

	response := &RiskAnalysisResponse{
		RiskLevel:    "low",
		Confidence:   0.6,
		Reasoning:    "Rule-based analysis",
		Factors:      make([]*RiskFactor, 0),
		Mitigations:  make([]*RiskMitigation, 0),
		Alternatives: make([]*CommandAlternative, 0),
		Metadata:     map[string]interface{}{"analysis_type": "rule"},
	}

	if len(rules) > 0 {
		// 应用最严格的规则
		strictestRule := rules[0]
		for _, rule := range rules {
			if ara.getRiskScore(rule.RiskLevel) > ara.getRiskScore(strictestRule.RiskLevel) {
				strictestRule = rule
			}
		}

		response.RiskLevel = strictestRule.RiskLevel
		response.Confidence = 0.8
		response.Reasoning = strictestRule.Reasoning

		// 添加风险因素
		response.Factors = append(response.Factors, &RiskFactor{
			Type:        "rule_match",
			Description: strictestRule.Description,
			Severity:    strictestRule.RiskLevel,
			Impact:      "system",
			Likelihood:  0.8,
			Weight:      strictestRule.Weight,
			Evidence:    []string{fmt.Sprintf("Rule: %s", strictestRule.Command)},
		})
	}

	return response
}

// analyzeWithAI 使用AI分析
func (ara *AIRiskAnalyzer) analyzeWithAI(ctx context.Context, request *RiskAnalysisRequest) (*RiskAnalysisResponse, error) {
	// 调用AI服务进行分析
	aiResponse, err := ara.aiService.AnalyzeRisk(ctx, request)
	if err != nil {
		return nil, fmt.Errorf("AI analysis failed: %w", err)
	}

	// 验证AI响应
	if aiResponse.Confidence < ara.config.ConfidenceThreshold {
		ara.logger.WithField("confidence", aiResponse.Confidence).Warn("AI analysis confidence below threshold")
	}

	aiResponse.Metadata["analysis_type"] = "ai"
	return aiResponse, nil
}

// analyzeWithContext 使用上下文分析
func (ara *AIRiskAnalyzer) analyzeWithContext(request *RiskAnalysisRequest) *RiskAnalysisResponse {
	context := ara.contextManager.GetContext(request.Context.Session)

	response := &RiskAnalysisResponse{
		RiskLevel:    "low",
		Confidence:   0.4,
		Reasoning:    "Context-based analysis",
		Factors:      make([]*RiskFactor, 0),
		Mitigations:  make([]*RiskMitigation, 0),
		Alternatives: make([]*CommandAlternative, 0),
		Metadata:     map[string]interface{}{"analysis_type": "context"},
	}

	if context != nil {
		// 分析命令序列模式
		if ara.detectSuspiciousPattern(context, request.Command) {
			response.RiskLevel = "medium"
			response.Confidence = 0.7
			response.Reasoning = "Suspicious command sequence detected"

			response.Factors = append(response.Factors, &RiskFactor{
				Type:        "sequence_pattern",
				Description: "Command sequence indicates potential risk",
				Severity:    "medium",
				Impact:      "system",
				Likelihood:  0.7,
				Weight:      0.6,
				Evidence:    []string{"Suspicious command sequence"},
			})
		}

		// 分析用户行为异常
		if ara.detectAnomalousUser(context, request.UserProfile) {
			response.RiskLevel = "high"
			response.Confidence = 0.8
			response.Reasoning = "Anomalous user behavior detected"

			response.Factors = append(response.Factors, &RiskFactor{
				Type:        "user_anomaly",
				Description: "User behavior deviates from normal pattern",
				Severity:    "high",
				Impact:      "security",
				Likelihood:  0.8,
				Weight:      0.8,
				Evidence:    []string{"Anomalous user behavior"},
			})
		}
	}

	return response
}

// 辅助方法

// generateCacheKey 生成缓存键
func (ara *AIRiskAnalyzer) generateCacheKey(request *RiskAnalysisRequest) string {
	// 简化实现：基于命令和用户的哈希
	return fmt.Sprintf("risk_%x", ara.simpleHash([]byte(request.Command+request.Context.User)))
}

// simpleHash 简单哈希函数
func (ara *AIRiskAnalyzer) simpleHash(data []byte) uint32 {
	var hash uint32 = 2166136261
	for _, b := range data {
		hash ^= uint32(b)
		hash *= 16777619
	}
	return hash
}

// mergeRiskAnalysis 合并风险分析结果
func (ara *AIRiskAnalyzer) mergeRiskAnalysis(target, source *RiskAnalysisResponse) {
	// 合并风险等级（取最高）
	if ara.getRiskScore(source.RiskLevel) > ara.getRiskScore(target.RiskLevel) {
		target.RiskLevel = source.RiskLevel
	}

	// 合并置信度（加权平均）
	if source.Confidence > target.Confidence {
		target.Confidence = (target.Confidence + source.Confidence) / 2
	}

	// 合并推理
	if target.Reasoning == "" {
		target.Reasoning = source.Reasoning
	} else if source.Reasoning != "" {
		target.Reasoning = target.Reasoning + "; " + source.Reasoning
	}

	// 合并风险因素
	target.Factors = append(target.Factors, source.Factors...)

	// 合并缓解措施
	target.Mitigations = append(target.Mitigations, source.Mitigations...)

	// 合并替代方案
	target.Alternatives = append(target.Alternatives, source.Alternatives...)

	// 合并元数据
	for key, value := range source.Metadata {
		target.Metadata[key] = value
	}
}

// getRiskScore 获取风险分数
func (ara *AIRiskAnalyzer) getRiskScore(riskLevel string) int {
	switch strings.ToLower(riskLevel) {
	case "critical":
		return 4
	case "high":
		return 3
	case "medium":
		return 2
	case "low":
		return 1
	default:
		return 0
	}
}

// calculateFinalRisk 计算最终风险等级
func (ara *AIRiskAnalyzer) calculateFinalRisk(response *RiskAnalysisResponse) {
	if len(response.Factors) == 0 {
		return
	}

	// 计算加权风险分数
	totalWeight := 0.0
	weightedScore := 0.0

	for _, factor := range response.Factors {
		score := float64(ara.getRiskScore(factor.Severity))
		weight := factor.Weight
		if weight == 0 {
			weight = 1.0
		}

		weightedScore += score * weight * factor.Likelihood
		totalWeight += weight
	}

	if totalWeight > 0 {
		finalScore := weightedScore / totalWeight

		// 转换为风险等级
		if finalScore >= 3.5 {
			response.RiskLevel = "critical"
		} else if finalScore >= 2.5 {
			response.RiskLevel = "high"
		} else if finalScore >= 1.5 {
			response.RiskLevel = "medium"
		} else {
			response.RiskLevel = "low"
		}

		// 调整置信度
		response.Confidence = (response.Confidence + (finalScore / 4.0)) / 2
	}
}

// generateRecommendations 生成建议
func (ara *AIRiskAnalyzer) generateRecommendations(ctx context.Context, response *RiskAnalysisResponse) ([]*RiskRecommendation, error) {
	var recommendations []*RiskRecommendation

	// 基于风险等级生成建议
	switch response.RiskLevel {
	case "critical":
		recommendations = append(recommendations, &RiskRecommendation{
			ID:          fmt.Sprintf("rec_%d", time.Now().UnixNano()),
			Type:        "prevention",
			Priority:    "critical",
			Title:       "阻止执行",
			Description: "命令风险极高，建议阻止执行",
			Actions:     []string{"停止命令执行", "联系管理员", "审查权限"},
			Benefits:    []string{"避免系统损坏", "保护数据安全"},
			Risks:       []string{"可能影响正常操作"},
			Confidence:  0.9,
			CreatedAt:   time.Now(),
		})
	case "high":
		recommendations = append(recommendations, &RiskRecommendation{
			ID:          fmt.Sprintf("rec_%d", time.Now().UnixNano()),
			Type:        "mitigation",
			Priority:    "high",
			Title:       "需要确认",
			Description: "命令风险较高，需要额外确认",
			Actions:     []string{"二次确认", "备份数据", "准备回滚"},
			Benefits:    []string{"降低风险", "可控执行"},
			Risks:       []string{"延迟操作时间"},
			Confidence:  0.8,
			CreatedAt:   time.Now(),
		})
	case "medium":
		recommendations = append(recommendations, &RiskRecommendation{
			ID:          fmt.Sprintf("rec_%d", time.Now().UnixNano()),
			Type:        "alternative",
			Priority:    "medium",
			Title:       "建议替代方案",
			Description: "存在更安全的替代方案",
			Actions:     []string{"使用替代命令", "添加安全参数", "监控执行"},
			Benefits:    []string{"降低风险", "达到相同目标"},
			Risks:       []string{"可能需要学习新命令"},
			Confidence:  0.7,
			CreatedAt:   time.Now(),
		})
	}

	return recommendations, nil
}

// updateContext 更新上下文
func (ara *AIRiskAnalyzer) updateContext(request *RiskAnalysisRequest, response *RiskAnalysisResponse) {
	ara.contextManager.UpdateContext(request.Context.Session, &CommandHistory{
		Command:    request.Command,
		Timestamp:  time.Now(),
		RiskLevel:  response.RiskLevel,
		UserAction: "analyzed",
	})
}

// detectSuspiciousPattern 检测可疑模式
func (ara *AIRiskAnalyzer) detectSuspiciousPattern(context *RiskContext, command string) bool {
	// 简化实现：检查是否有连续的高风险命令
	highRiskCount := 0
	for _, cmd := range context.Commands {
		if cmd.RiskLevel == "high" || cmd.RiskLevel == "critical" {
			highRiskCount++
		}
	}

	return highRiskCount >= 3
}

// detectAnomalousUser 检测异常用户
func (ara *AIRiskAnalyzer) detectAnomalousUser(context *RiskContext, profile *UserProfile) bool {
	// 简化实现：检查用户是否在异常时间执行高风险命令
	if profile.Experience == "beginner" && len(context.Commands) > 0 {
		lastCmd := context.Commands[len(context.Commands)-1]
		if lastCmd.RiskLevel == "high" || lastCmd.RiskLevel == "critical" {
			return true
		}
	}

	return false
}
