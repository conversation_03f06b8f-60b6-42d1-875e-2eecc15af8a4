package agent

import (
	"context"
	"fmt"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
)

// AIRiskAnalyzer AI风险分析器
type AIRiskAnalyzer struct {
	logger *logrus.Logger
}

// NewAIRiskAnalyzer 创建AI风险分析器
func NewAIRiskAnalyzer(logger *logrus.Logger) *AIRiskAnalyzer {
	return &AIRiskAnalyzer{
		logger: logger,
	}
}

// Analyze 使用AI分析命令风险
func (ara *AIRiskAnalyzer) Analyze(ctx context.Context, command string, args []string, assessmentContext *AssessmentContext) *AIAnalysis {
	analysis := &AIAnalysis{
		AlternativeSuggestions: make([]string, 0),
		SafetyRecommendations:  make([]string, 0),
		ReasoningChain:         make([]string, 0),
		Metadata:               make(map[string]interface{}),
	}

	fullCommand := command + " " + strings.Join(args, " ")

	// 分析命令意图
	analysis.IntentAnalysis = ara.analyzeIntent(fullCommand)

	// 预测风险
	analysis.RiskPrediction = ara.predictRisk(fullCommand, assessmentContext)

	// 生成替代建议
	analysis.AlternativeSuggestions = ara.generateAlternatives(fullCommand)

	// 生成安全建议
	analysis.SafetyRecommendations = ara.generateSafetyRecommendations(fullCommand, analysis.RiskPrediction)

	// 设置置信度
	analysis.ConfidenceLevel = ara.calculateConfidence(fullCommand, assessmentContext)

	// 生成推理链
	analysis.ReasoningChain = ara.generateReasoningChain(fullCommand, analysis.RiskPrediction)

	return analysis
}

// analyzeIntent 分析命令意图
func (ara *AIRiskAnalyzer) analyzeIntent(fullCommand string) string {
	// 基于命令模式分析意图
	intentPatterns := map[string]string{
		"rm.*-rf":            "批量删除文件或目录",
		"chmod.*777":         "设置最大权限",
		"chown.*root":        "更改文件所有者为root",
		"systemctl.*stop":    "停止系统服务",
		"systemctl.*start":   "启动系统服务",
		"systemctl.*restart": "重启系统服务",
		"mount":              "挂载文件系统",
		"umount":             "卸载文件系统",
		"dd.*of=":            "磁盘数据复制或写入",
		"mkfs":               "创建文件系统",
		"fdisk":              "磁盘分区操作",
		"iptables":           "配置防火墙规则",
		"passwd":             "修改用户密码",
		"useradd":            "添加用户账户",
		"userdel":            "删除用户账户",
		"crontab":            "配置定时任务",
		"ssh":                "远程连接",
		"scp":                "远程文件传输",
		"wget":               "下载文件",
		"curl":               "网络请求",
		"ps":                 "查看进程信息",
		"kill":               "终止进程",
		"top":                "查看系统资源使用",
		"df":                 "查看磁盘使用情况",
		"ls":                 "列出文件和目录",
		"cat":                "查看文件内容",
		"grep":               "搜索文本内容",
		"find":               "查找文件",
		"tar":                "文件压缩或解压",
		"cp":                 "复制文件",
		"mv":                 "移动或重命名文件",
	}

	for pattern, intent := range intentPatterns {
		if matched, _ := regexp.MatchString(pattern, fullCommand); matched {
			return intent
		}
	}

	return "执行系统命令"
}

// predictRisk 预测风险等级
func (ara *AIRiskAnalyzer) predictRisk(fullCommand string, assessmentContext *AssessmentContext) float64 {
	risk := 0.0

	// 基于命令危险性评分
	dangerousPatterns := map[string]float64{
		`rm\s+.*-rf.*/$`:            0.95, // 删除根目录
		`rm\s+.*-rf.*\*`:            0.90, // 通配符删除
		`dd\s+.*of=/dev/`:           0.95, // 写入设备
		`mkfs`:                      0.90, // 格式化
		`fdisk.*d`:                  0.85, // 删除分区
		`chmod.*777.*/$`:            0.80, // 根目录权限
		`chown.*root.*/$`:           0.75, // 根目录所有者
		`systemctl.*stop.*database`: 0.80, // 停止数据库
		`systemctl.*stop.*web`:      0.75, // 停止Web服务
		`iptables.*-F`:              0.70, // 清空防火墙规则
		`passwd.*root`:              0.65, // 修改root密码
		`userdel.*root`:             0.90, // 删除root用户
		`kill.*-9.*1$`:              0.85, // 杀死init进程
		`shutdown.*now`:             0.70, // 立即关机
		`reboot.*now`:               0.65, // 立即重启
	}

	for pattern, score := range dangerousPatterns {
		if matched, _ := regexp.MatchString(pattern, fullCommand); matched {
			if score > risk {
				risk = score
			}
		}
	}

	// 基于上下文调整风险
	if assessmentContext != nil {
		// 生产环境风险加权
		if assessmentContext.Environment != nil {
			if env, exists := assessmentContext.Environment["ENVIRONMENT"]; exists {
				if strings.ToLower(env) == "production" {
					risk += 0.2
				}
			}
		}

		// root用户风险加权
		if assessmentContext.UserPermissions != nil && assessmentContext.UserPermissions.IsAdmin {
			risk += 0.1
		}

		// 系统负载高时风险加权
		if assessmentContext.SystemState != nil {
			if assessmentContext.SystemState.CPUUsage > 80 || assessmentContext.SystemState.MemoryUsage > 90 {
				risk += 0.1
			}
		}
	}

	// 确保风险值在0-1范围内
	if risk > 1.0 {
		risk = 1.0
	}

	return risk
}

// generateAlternatives 生成替代建议
func (ara *AIRiskAnalyzer) generateAlternatives(fullCommand string) []string {
	alternatives := make([]string, 0)

	// 基于命令类型提供替代方案
	alternativeMap := map[string][]string{
		"rm.*-rf": {
			"使用 mv 命令先移动到临时目录",
			"使用 find 命令精确定位要删除的文件",
			"分批删除而不是一次性删除",
			"使用 trash 命令代替 rm（如果可用）",
		},
		"chmod.*777": {
			"使用更具体的权限设置，如 755 或 644",
			"只对特定用户或组设置权限",
			"使用 ACL 进行更细粒度的权限控制",
		},
		"systemctl.*stop": {
			"使用 systemctl reload 重新加载配置",
			"使用 systemctl restart 重启服务",
			"先检查服务依赖关系",
		},
		"dd.*of=": {
			"使用 rsync 进行文件同步",
			"使用 cp 命令复制文件",
			"先在测试环境验证",
		},
		"mkfs": {
			"先备份重要数据",
			"使用 fsck 检查文件系统",
			"考虑使用 resize2fs 调整大小",
		},
	}

	for pattern, alts := range alternativeMap {
		if matched, _ := regexp.MatchString(pattern, fullCommand); matched {
			alternatives = append(alternatives, alts...)
			break
		}
	}

	return alternatives
}

// generateSafetyRecommendations 生成安全建议
func (ara *AIRiskAnalyzer) generateSafetyRecommendations(fullCommand string, riskScore float64) []string {
	recommendations := make([]string, 0)

	// 基于风险等级提供建议
	if riskScore >= 0.9 {
		recommendations = append(recommendations, []string{
			"强烈建议不要执行此命令",
			"如必须执行，请先完整备份系统",
			"在隔离的测试环境中先行验证",
			"需要多人确认和审批",
			"准备详细的回滚计划",
		}...)
	} else if riskScore >= 0.7 {
		recommendations = append(recommendations, []string{
			"建议谨慎执行此命令",
			"执行前请备份相关数据",
			"确认当前系统状态正常",
			"准备应急恢复方案",
		}...)
	} else if riskScore >= 0.5 {
		recommendations = append(recommendations, []string{
			"建议在非生产环境测试",
			"确认命令参数正确",
			"检查系统资源使用情况",
		}...)
	} else {
		recommendations = append(recommendations, []string{
			"命令风险较低，可以执行",
			"建议检查命令语法",
		}...)
	}

	// 基于命令类型提供特定建议
	if strings.Contains(fullCommand, "rm") {
		recommendations = append(recommendations, "使用 ls 命令先确认要删除的文件")
	}

	if strings.Contains(fullCommand, "chmod") || strings.Contains(fullCommand, "chown") {
		recommendations = append(recommendations, "确认权限设置符合安全策略")
	}

	if strings.Contains(fullCommand, "systemctl") || strings.Contains(fullCommand, "service") {
		recommendations = append(recommendations, "确认服务停止不会影响其他服务")
	}

	return recommendations
}

// calculateConfidence 计算置信度
func (ara *AIRiskAnalyzer) calculateConfidence(fullCommand string, assessmentContext *AssessmentContext) float64 {
	confidence := 0.7 // 基础置信度

	// 基于命令复杂度调整置信度
	if len(strings.Fields(fullCommand)) > 5 {
		confidence -= 0.1 // 复杂命令降低置信度
	}

	// 基于上下文信息调整置信度
	if assessmentContext != nil {
		if assessmentContext.SystemState != nil {
			confidence += 0.1 // 有系统状态信息提高置信度
		}

		if assessmentContext.UserPermissions != nil {
			confidence += 0.1 // 有用户权限信息提高置信度
		}

		if len(assessmentContext.PreviousCommands) > 0 {
			confidence += 0.1 // 有历史命令信息提高置信度
		}
	}

	// 确保置信度在0-1范围内
	if confidence > 1.0 {
		confidence = 1.0
	} else if confidence < 0.0 {
		confidence = 0.0
	}

	return confidence
}

// generateReasoningChain 生成推理链
func (ara *AIRiskAnalyzer) generateReasoningChain(fullCommand string, riskScore float64) []string {
	chain := make([]string, 0)

	chain = append(chain, fmt.Sprintf("分析命令: %s", fullCommand))

	// 命令类型分析
	if strings.Contains(fullCommand, "rm") {
		chain = append(chain, "检测到文件删除操作")
		if strings.Contains(fullCommand, "-rf") {
			chain = append(chain, "包含强制递归删除参数，风险较高")
		}
	}

	if strings.Contains(fullCommand, "chmod") {
		chain = append(chain, "检测到权限修改操作")
		if strings.Contains(fullCommand, "777") {
			chain = append(chain, "设置为最大权限，存在安全风险")
		}
	}

	if strings.Contains(fullCommand, "systemctl") {
		chain = append(chain, "检测到系统服务操作")
		if strings.Contains(fullCommand, "stop") {
			chain = append(chain, "停止服务可能影响系统功能")
		}
	}

	// 风险评分推理
	if riskScore >= 0.9 {
		chain = append(chain, "综合评估：极高风险，强烈不建议执行")
	} else if riskScore >= 0.7 {
		chain = append(chain, "综合评估：高风险，需要谨慎考虑")
	} else if riskScore >= 0.5 {
		chain = append(chain, "综合评估：中等风险，建议测试后执行")
	} else {
		chain = append(chain, "综合评估：低风险，可以执行")
	}

	return chain
}
