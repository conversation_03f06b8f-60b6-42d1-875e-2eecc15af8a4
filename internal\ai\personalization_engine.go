package ai

import (
	"context"
	"fmt"
	"math"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// PersonalizationEngine 个性化学习引擎
type PersonalizationEngine struct {
	db                   *gorm.DB
	logger               *logrus.Logger
	config               *PersonalizationConfig
	userProfileManager   *UserProfileManager
	behaviorAnalyzer     *BehaviorAnalyzer
	preferencePredictor  *PreferencePredictor
	adaptationController *AdaptationController
	learningOptimizer    *LearningOptimizer
	privacyManager       *PrivacyManager
	mutex                sync.RWMutex
	isRunning            bool
	userProfiles         map[int64]*UserProfile
	learningMetrics      *LearningMetrics
}

// PersonalizationConfig 个性化配置
type PersonalizationConfig struct {
	EnablePersonalization      bool          `json:"enable_personalization"`
	LearningRate               float64       `json:"learning_rate"`
	AdaptationThreshold        float64       `json:"adaptation_threshold"`
	MinInteractionsForLearning int           `json:"min_interactions_for_learning"`
	MaxProfileAge              time.Duration `json:"max_profile_age"`
	PrivacyLevel               string        `json:"privacy_level"`
	EnableBehaviorTracking     bool          `json:"enable_behavior_tracking"`
	EnablePreferenceSync       bool          `json:"enable_preference_sync"`
	LearningUpdateInterval     time.Duration `json:"learning_update_interval"`
	ProfileBackupInterval      time.Duration `json:"profile_backup_interval"`
}

// UserProfile 用户画像
type UserProfile struct {
	UserID           int64     `json:"user_id"`
	CreatedAt        time.Time `json:"created_at"`
	LastUpdated      time.Time `json:"last_updated"`
	InteractionCount int64     `json:"interaction_count"`
	LearningProgress float64   `json:"learning_progress"`

	// 交互偏好
	PreferredModes     map[InteractionMode]float64  `json:"preferred_modes"`
	ResponseStyle      ResponseStylePreference      `json:"response_style"`
	CommunicationStyle CommunicationStylePreference `json:"communication_style"`

	// 行为模式
	BehaviorPatterns map[string]*BehaviorPattern `json:"behavior_patterns"`
	UsagePatterns    *UsagePattern               `json:"usage_patterns"`
	TaskPreferences  map[string]*TaskPreference  `json:"task_preferences"`

	// 技能水平
	TechnicalLevel  float64            `json:"technical_level"`
	DomainExpertise map[string]float64 `json:"domain_expertise"`
	LearningSpeed   float64            `json:"learning_speed"`

	// 个性化设置
	CustomSettings     map[string]interface{} `json:"custom_settings"`
	AccessibilityNeeds []string               `json:"accessibility_needs"`
	LanguagePreference string                 `json:"language_preference"`

	// 学习状态
	LearningState     *LearningState     `json:"learning_state"`
	AdaptationHistory []AdaptationRecord `json:"adaptation_history"`

	// 隐私设置
	PrivacySettings *PrivacySettings `json:"privacy_settings"`
}

// ResponseStylePreference 响应风格偏好
type ResponseStylePreference struct {
	Formality    float64 `json:"formality"`    // 正式程度 0-1
	Verbosity    float64 `json:"verbosity"`    // 详细程度 0-1
	Friendliness float64 `json:"friendliness"` // 友好程度 0-1
	Directness   float64 `json:"directness"`   // 直接程度 0-1
	Humor        float64 `json:"humor"`        // 幽默程度 0-1
	Empathy      float64 `json:"empathy"`      // 共情程度 0-1
	Patience     float64 `json:"patience"`     // 耐心程度 0-1
}

// CommunicationStylePreference 沟通风格偏好
type CommunicationStylePreference struct {
	PreferredTone       string `json:"preferred_tone"`       // 偏好语调
	PreferredPace       string `json:"preferred_pace"`       // 偏好节奏
	PreferredComplexity string `json:"preferred_complexity"` // 偏好复杂度
	PreferredExamples   bool   `json:"preferred_examples"`   // 是否喜欢举例
	PreferredSummary    bool   `json:"preferred_summary"`    // 是否喜欢总结
	PreferredSteps      bool   `json:"preferred_steps"`      // 是否喜欢分步骤
}

// BehaviorPattern 行为模式
type BehaviorPattern struct {
	PatternName  string                 `json:"pattern_name"`
	Frequency    float64                `json:"frequency"`
	Confidence   float64                `json:"confidence"`
	LastObserved time.Time              `json:"last_observed"`
	Triggers     []string               `json:"triggers"`
	Outcomes     []string               `json:"outcomes"`
	Context      map[string]interface{} `json:"context"`
}

// UsagePattern 使用模式
type UsagePattern struct {
	ActiveHours          []int              `json:"active_hours"`          // 活跃时间段
	SessionDuration      time.Duration      `json:"session_duration"`      // 平均会话时长
	InteractionFrequency float64            `json:"interaction_frequency"` // 交互频率
	PreferredDevices     []string           `json:"preferred_devices"`     // 偏好设备
	CommonTasks          map[string]float64 `json:"common_tasks"`          // 常用任务
	WorkflowPatterns     []WorkflowPattern  `json:"workflow_patterns"`     // 工作流模式
}

// TaskPreference 任务偏好
type TaskPreference struct {
	TaskType        string                 `json:"task_type"`
	PreferredMethod string                 `json:"preferred_method"`
	SkillLevel      float64                `json:"skill_level"`
	Frequency       float64                `json:"frequency"`
	SuccessRate     float64                `json:"success_rate"`
	AverageTime     time.Duration          `json:"average_time"`
	Preferences     map[string]interface{} `json:"preferences"`
}

// WorkflowPattern 工作流模式
type WorkflowPattern struct {
	Name        string    `json:"name"`
	Steps       []string  `json:"steps"`
	Frequency   float64   `json:"frequency"`
	SuccessRate float64   `json:"success_rate"`
	LastUsed    time.Time `json:"last_used"`
}

// LearningState 学习状态
type LearningState struct {
	Phase              LearningPhase       `json:"phase"`
	Progress           float64             `json:"progress"`
	LastLearningUpdate time.Time           `json:"last_learning_update"`
	LearningGoals      []LearningGoal      `json:"learning_goals"`
	Achievements       []Achievement       `json:"achievements"`
	LearningChallenges []LearningChallenge `json:"learning_challenges"`
}

// LearningPhase 学习阶段
type LearningPhase string

const (
	PhaseInitial     LearningPhase = "initial"     // 初始阶段
	PhaseObservation LearningPhase = "observation" // 观察阶段
	PhaseLearning    LearningPhase = "learning"    // 学习阶段
	PhaseAdaptation  LearningPhase = "adaptation"  // 适应阶段
	PhaseOptimized   LearningPhase = "optimized"   // 优化阶段
)

// LearningGoal 学习目标
type LearningGoal struct {
	ID          string    `json:"id"`
	Description string    `json:"description"`
	Progress    float64   `json:"progress"`
	Deadline    time.Time `json:"deadline"`
	Priority    int       `json:"priority"`
}

// Achievement 成就
type Achievement struct {
	ID          string    `json:"id"`
	Name        string    `json:"name"`
	Description string    `json:"description"`
	UnlockedAt  time.Time `json:"unlocked_at"`
	Category    string    `json:"category"`
}

// LearningChallenge 学习挑战
type LearningChallenge struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Difficulty  float64                `json:"difficulty"`
	Context     map[string]interface{} `json:"context"`
	CreatedAt   time.Time              `json:"created_at"`
}

// AdaptationRecord 适应记录
type AdaptationRecord struct {
	Timestamp  time.Time              `json:"timestamp"`
	Type       string                 `json:"type"`
	Before     map[string]interface{} `json:"before"`
	After      map[string]interface{} `json:"after"`
	Reason     string                 `json:"reason"`
	Confidence float64                `json:"confidence"`
	Success    bool                   `json:"success"`
}

// PrivacySettings 隐私设置
type PrivacySettings struct {
	DataCollection     bool     `json:"data_collection"`
	BehaviorTracking   bool     `json:"behavior_tracking"`
	ProfileSharing     bool     `json:"profile_sharing"`
	DataRetention      int      `json:"data_retention"` // 天数
	AnonymousMode      bool     `json:"anonymous_mode"`
	AllowedDataTypes   []string `json:"allowed_data_types"`
	RestrictedFeatures []string `json:"restricted_features"`
}

// LearningMetrics 学习指标
type LearningMetrics struct {
	TotalUsers          int64                   `json:"total_users"`
	ActiveLearners      int64                   `json:"active_learners"`
	AverageLearningRate float64                 `json:"average_learning_rate"`
	AdaptationSuccess   float64                 `json:"adaptation_success"`
	UserSatisfaction    float64                 `json:"user_satisfaction"`
	LearningEfficiency  float64                 `json:"learning_efficiency"`
	PhaseDistribution   map[LearningPhase]int64 `json:"phase_distribution"`
	LastUpdated         time.Time               `json:"last_updated"`
}

// NewPersonalizationEngine 创建个性化学习引擎
func NewPersonalizationEngine(
	db *gorm.DB,
	logger *logrus.Logger,
	config *PersonalizationConfig,
) *PersonalizationEngine {
	if config == nil {
		config = &PersonalizationConfig{
			EnablePersonalization:      true,
			LearningRate:               0.1,
			AdaptationThreshold:        0.7,
			MinInteractionsForLearning: 10,
			MaxProfileAge:              30 * 24 * time.Hour,
			PrivacyLevel:               "balanced",
			EnableBehaviorTracking:     true,
			EnablePreferenceSync:       true,
			LearningUpdateInterval:     1 * time.Hour,
			ProfileBackupInterval:      24 * time.Hour,
		}
	}

	engine := &PersonalizationEngine{
		db:           db,
		logger:       logger,
		config:       config,
		isRunning:    false,
		userProfiles: make(map[int64]*UserProfile),
		learningMetrics: &LearningMetrics{
			PhaseDistribution: make(map[LearningPhase]int64),
			LastUpdated:       time.Now(),
		},
	}

	// 初始化子组件
	engine.userProfileManager = NewUserProfileManager(db, logger, config)
	engine.behaviorAnalyzer = NewBehaviorAnalyzer(logger, config)
	engine.preferencePredictor = NewPreferencePredictor(logger, config)
	engine.adaptationController = NewAdaptationController(logger, config)
	engine.learningOptimizer = NewLearningOptimizer(logger, config)
	engine.privacyManager = NewPrivacyManager(logger, config)

	// 确保数据库表存在
	if err := db.AutoMigrate(
		&UserProfileRecord{},
		&BehaviorRecord{},
		&LearningRecord{},
		&AdaptationRecord{},
		&PersonalizationMetricsRecord{},
	); err != nil {
		logger.WithError(err).Error("Failed to migrate personalization tables")
	}

	logger.Info("🧠 个性化学习引擎初始化完成")
	return engine
}

// Start 启动个性化学习引擎
func (pe *PersonalizationEngine) Start() error {
	pe.mutex.Lock()
	defer pe.mutex.Unlock()

	if pe.isRunning {
		return fmt.Errorf("personalization engine is already running")
	}

	pe.isRunning = true

	// 加载现有用户画像
	if err := pe.loadUserProfiles(); err != nil {
		pe.logger.WithError(err).Error("Failed to load user profiles")
	}

	// 启动后台任务
	go pe.runLearningUpdates()
	go pe.runProfileBackup()
	go pe.runMetricsCollection()

	pe.logger.Info("🧠 个性化学习引擎启动成功")
	return nil
}

// Stop 停止个性化学习引擎
func (pe *PersonalizationEngine) Stop() error {
	pe.mutex.Lock()
	defer pe.mutex.Unlock()

	if !pe.isRunning {
		return fmt.Errorf("personalization engine is not running")
	}

	pe.isRunning = false

	// 保存用户画像
	if err := pe.saveUserProfiles(); err != nil {
		pe.logger.WithError(err).Error("Failed to save user profiles")
	}

	pe.logger.Info("个性化学习引擎已停止")
	return nil
}

// LearnFromInteraction 从交互中学习
func (pe *PersonalizationEngine) LearnFromInteraction(
	ctx context.Context,
	userID int64,
	interaction *InteractionData,
) error {
	if !pe.config.EnablePersonalization {
		return nil
	}

	pe.mutex.Lock()
	defer pe.mutex.Unlock()

	pe.logger.WithFields(logrus.Fields{
		"user_id":        userID,
		"interaction_id": interaction.ID,
		"type":           interaction.Type,
	}).Debug("🧠 开始从交互中学习")

	// 获取或创建用户画像
	profile := pe.getOrCreateUserProfile(userID)

	// 更新交互计数
	profile.InteractionCount++
	profile.LastUpdated = time.Now()

	// 分析行为
	behaviorInsights, err := pe.behaviorAnalyzer.AnalyzeBehavior(interaction, profile)
	if err != nil {
		pe.logger.WithError(err).Warn("Failed to analyze behavior")
	} else {
		pe.updateBehaviorPatterns(profile, behaviorInsights)
	}

	// 预测偏好
	preferenceUpdates, err := pe.preferencePredictor.PredictPreferences(interaction, profile)
	if err != nil {
		pe.logger.WithError(err).Warn("Failed to predict preferences")
	} else {
		pe.updatePreferences(profile, preferenceUpdates)
	}

	// 检查是否需要适应
	if pe.shouldAdapt(profile, interaction) {
		adaptations, err := pe.adaptationController.GenerateAdaptations(profile, interaction)
		if err != nil {
			pe.logger.WithError(err).Warn("Failed to generate adaptations")
		} else {
			pe.applyAdaptations(profile, adaptations)
		}
	}

	// 优化学习过程
	pe.learningOptimizer.OptimizeLearning(profile, interaction)

	// 更新学习进度
	pe.updateLearningProgress(profile)

	pe.logger.WithFields(logrus.Fields{
		"user_id":           userID,
		"learning_progress": profile.LearningProgress,
		"learning_phase":    profile.LearningState.Phase,
	}).Debug("🧠 交互学习完成")

	return nil
}

// GetPersonalizedResponse 获取个性化响应
func (pe *PersonalizationEngine) GetPersonalizedResponse(
	ctx context.Context,
	userID int64,
	baseResponse interface{},
) (interface{}, error) {
	if !pe.config.EnablePersonalization {
		return baseResponse, nil
	}

	pe.mutex.RLock()
	defer pe.mutex.RUnlock()

	profile, exists := pe.userProfiles[userID]
	if !exists {
		// 用户画像不存在，返回基础响应
		return baseResponse, nil
	}

	pe.logger.WithFields(logrus.Fields{
		"user_id": userID,
		"phase":   profile.LearningState.Phase,
	}).Debug("🧠 生成个性化响应")

	// 根据用户画像个性化响应
	personalizedResponse := pe.personalizeResponse(baseResponse, profile)

	return personalizedResponse, nil
}

// GetUserProfile 获取用户画像
func (pe *PersonalizationEngine) GetUserProfile(userID int64) (*UserProfile, error) {
	pe.mutex.RLock()
	defer pe.mutex.RUnlock()

	profile, exists := pe.userProfiles[userID]
	if !exists {
		return nil, fmt.Errorf("user profile not found: %d", userID)
	}

	// 返回副本以避免并发修改
	profileCopy := *profile
	return &profileCopy, nil
}

// UpdateUserPreferences 更新用户偏好
func (pe *PersonalizationEngine) UpdateUserPreferences(
	userID int64,
	preferences map[string]interface{},
) error {
	pe.mutex.Lock()
	defer pe.mutex.Unlock()

	profile := pe.getOrCreateUserProfile(userID)

	// 更新偏好设置
	for key, value := range preferences {
		profile.CustomSettings[key] = value
	}

	profile.LastUpdated = time.Now()

	pe.logger.WithFields(logrus.Fields{
		"user_id":     userID,
		"preferences": preferences,
	}).Info("更新用户偏好")

	return nil
}

// GetLearningMetrics 获取学习指标
func (pe *PersonalizationEngine) GetLearningMetrics() *LearningMetrics {
	pe.mutex.RLock()
	defer pe.mutex.RUnlock()

	// 创建副本
	metrics := &LearningMetrics{
		TotalUsers:          pe.learningMetrics.TotalUsers,
		ActiveLearners:      pe.learningMetrics.ActiveLearners,
		AverageLearningRate: pe.learningMetrics.AverageLearningRate,
		AdaptationSuccess:   pe.learningMetrics.AdaptationSuccess,
		UserSatisfaction:    pe.learningMetrics.UserSatisfaction,
		LearningEfficiency:  pe.learningMetrics.LearningEfficiency,
		PhaseDistribution:   make(map[LearningPhase]int64),
		LastUpdated:         pe.learningMetrics.LastUpdated,
	}

	for phase, count := range pe.learningMetrics.PhaseDistribution {
		metrics.PhaseDistribution[phase] = count
	}

	return metrics
}

// 私有方法

func (pe *PersonalizationEngine) getOrCreateUserProfile(userID int64) *UserProfile {
	profile, exists := pe.userProfiles[userID]
	if !exists {
		profile = &UserProfile{
			UserID:           userID,
			CreatedAt:        time.Now(),
			LastUpdated:      time.Now(),
			InteractionCount: 0,
			LearningProgress: 0.0,
			PreferredModes:   make(map[InteractionMode]float64),
			ResponseStyle: ResponseStylePreference{
				Formality:    0.5,
				Verbosity:    0.5,
				Friendliness: 0.7,
				Directness:   0.6,
				Humor:        0.3,
				Empathy:      0.6,
				Patience:     0.7,
			},
			CommunicationStyle: CommunicationStylePreference{
				PreferredTone:       "friendly",
				PreferredPace:       "normal",
				PreferredComplexity: "medium",
				PreferredExamples:   true,
				PreferredSummary:    true,
				PreferredSteps:      true,
			},
			BehaviorPatterns:   make(map[string]*BehaviorPattern),
			TaskPreferences:    make(map[string]*TaskPreference),
			TechnicalLevel:     0.5,
			DomainExpertise:    make(map[string]float64),
			LearningSpeed:      0.5,
			CustomSettings:     make(map[string]interface{}),
			AccessibilityNeeds: []string{},
			LanguagePreference: "zh-CN",
			LearningState: &LearningState{
				Phase:              PhaseInitial,
				Progress:           0.0,
				LastLearningUpdate: time.Now(),
				LearningGoals:      []LearningGoal{},
				Achievements:       []Achievement{},
				LearningChallenges: []LearningChallenge{},
			},
			AdaptationHistory: []AdaptationRecord{},
			PrivacySettings: &PrivacySettings{
				DataCollection:   true,
				BehaviorTracking: true,
				ProfileSharing:   false,
				DataRetention:    90,
				AnonymousMode:    false,
				AllowedDataTypes: []string{"interaction", "preference", "behavior"},
			},
		}

		// 初始化默认偏好模态
		profile.PreferredModes[ModeText] = 1.0
		profile.PreferredModes[ModeSpeech] = 0.5
		profile.PreferredModes[ModeImage] = 0.3

		pe.userProfiles[userID] = profile
	}

	return profile
}

func (pe *PersonalizationEngine) loadUserProfiles() error {
	// 从数据库加载用户画像
	var records []UserProfileRecord
	if err := pe.db.Find(&records).Error; err != nil {
		return fmt.Errorf("failed to load user profiles: %w", err)
	}

	for _, record := range records {
		profile, err := record.ToUserProfile()
		if err != nil {
			pe.logger.WithError(err).WithField("user_id", record.UserID).Warn("Failed to convert user profile")
			continue
		}

		pe.userProfiles[record.UserID] = profile
	}

	pe.logger.WithField("profiles_loaded", len(records)).Info("用户画像加载完成")
	return nil
}

func (pe *PersonalizationEngine) saveUserProfiles() error {
	for userID, profile := range pe.userProfiles {
		record := &UserProfileRecord{}
		if err := record.FromUserProfile(profile); err != nil {
			pe.logger.WithError(err).WithField("user_id", userID).Warn("Failed to convert user profile")
			continue
		}

		if err := pe.db.Save(record).Error; err != nil {
			pe.logger.WithError(err).WithField("user_id", userID).Warn("Failed to save user profile")
		}
	}

	return nil
}

func (pe *PersonalizationEngine) updateBehaviorPatterns(profile *UserProfile, insights *BehaviorInsights) {
	for _, pattern := range insights.Patterns {
		existing, exists := profile.BehaviorPatterns[pattern.PatternName]
		if exists {
			// 更新现有模式
			existing.Frequency = existing.Frequency*0.9 + pattern.Frequency*0.1
			existing.Confidence = math.Max(existing.Confidence, pattern.Confidence)
			existing.LastObserved = time.Now()
		} else {
			// 添加新模式
			profile.BehaviorPatterns[pattern.PatternName] = &pattern
		}
	}
}

func (pe *PersonalizationEngine) updatePreferences(profile *UserProfile, updates *PreferenceUpdates) {
	// 更新响应风格偏好
	if updates.ResponseStyle != nil {
		pe.mergeResponseStyle(&profile.ResponseStyle, updates.ResponseStyle)
	}

	// 更新沟通风格偏好
	if updates.CommunicationStyle != nil {
		pe.mergeCommunicationStyle(&profile.CommunicationStyle, updates.CommunicationStyle)
	}

	// 更新模态偏好
	for mode, preference := range updates.ModePreferences {
		current := profile.PreferredModes[mode]
		profile.PreferredModes[mode] = current*0.9 + preference*0.1
	}
}

func (pe *PersonalizationEngine) shouldAdapt(profile *UserProfile, interaction *InteractionData) bool {
	// 检查是否达到适应阈值
	if profile.LearningProgress < pe.config.AdaptationThreshold {
		return false
	}

	// 检查最小交互次数
	if profile.InteractionCount < int64(pe.config.MinInteractionsForLearning) {
		return false
	}

	// 检查是否有足够的学习数据
	return len(profile.BehaviorPatterns) > 0
}

func (pe *PersonalizationEngine) applyAdaptations(profile *UserProfile, adaptations []Adaptation) {
	for _, adaptation := range adaptations {
		record := AdaptationRecord{
			Timestamp:  time.Now(),
			Type:       adaptation.Type,
			Before:     adaptation.Before,
			After:      adaptation.After,
			Reason:     adaptation.Reason,
			Confidence: adaptation.Confidence,
			Success:    true, // 假设成功，实际应该根据后续反馈确定
		}

		profile.AdaptationHistory = append(profile.AdaptationHistory, record)

		// 应用具体的适应
		pe.applySpecificAdaptation(profile, &adaptation)
	}
}

func (pe *PersonalizationEngine) applySpecificAdaptation(profile *UserProfile, adaptation *Adaptation) {
	switch adaptation.Type {
	case "response_style":
		if styleData, ok := adaptation.After["response_style"].(ResponseStylePreference); ok {
			profile.ResponseStyle = styleData
		}
	case "communication_style":
		if commData, ok := adaptation.After["communication_style"].(CommunicationStylePreference); ok {
			profile.CommunicationStyle = commData
		}
	case "mode_preference":
		if modeData, ok := adaptation.After["mode_preferences"].(map[InteractionMode]float64); ok {
			profile.PreferredModes = modeData
		}
	}
}

func (pe *PersonalizationEngine) updateLearningProgress(profile *UserProfile) {
	// 基于交互次数和行为模式数量计算学习进度
	interactionFactor := math.Min(float64(profile.InteractionCount)/100.0, 1.0)
	patternFactor := math.Min(float64(len(profile.BehaviorPatterns))/10.0, 1.0)

	profile.LearningProgress = (interactionFactor + patternFactor) / 2.0

	// 更新学习阶段
	pe.updateLearningPhase(profile)
}

func (pe *PersonalizationEngine) updateLearningPhase(profile *UserProfile) {
	progress := profile.LearningProgress

	switch {
	case progress < 0.2:
		profile.LearningState.Phase = PhaseInitial
	case progress < 0.4:
		profile.LearningState.Phase = PhaseObservation
	case progress < 0.6:
		profile.LearningState.Phase = PhaseLearning
	case progress < 0.8:
		profile.LearningState.Phase = PhaseAdaptation
	default:
		profile.LearningState.Phase = PhaseOptimized
	}
}

func (pe *PersonalizationEngine) personalizeResponse(baseResponse interface{}, profile *UserProfile) interface{} {
	// 根据用户画像个性化响应
	// 这里应该根据具体的响应类型进行个性化处理

	// 简化实现：返回包含个性化信息的响应
	personalizedResponse := map[string]interface{}{
		"base_response": baseResponse,
		"personalization": map[string]interface{}{
			"user_id":         profile.UserID,
			"learning_phase":  profile.LearningState.Phase,
			"preferred_style": profile.ResponseStyle,
			"technical_level": profile.TechnicalLevel,
		},
	}

	return personalizedResponse
}

func (pe *PersonalizationEngine) mergeResponseStyle(current *ResponseStylePreference, update *ResponseStylePreference) {
	alpha := pe.config.LearningRate

	current.Formality = current.Formality*(1-alpha) + update.Formality*alpha
	current.Verbosity = current.Verbosity*(1-alpha) + update.Verbosity*alpha
	current.Friendliness = current.Friendliness*(1-alpha) + update.Friendliness*alpha
	current.Directness = current.Directness*(1-alpha) + update.Directness*alpha
	current.Humor = current.Humor*(1-alpha) + update.Humor*alpha
	current.Empathy = current.Empathy*(1-alpha) + update.Empathy*alpha
	current.Patience = current.Patience*(1-alpha) + update.Patience*alpha
}

func (pe *PersonalizationEngine) mergeCommunicationStyle(current *CommunicationStylePreference, update *CommunicationStylePreference) {
	// 简化实现：直接更新
	if update.PreferredTone != "" {
		current.PreferredTone = update.PreferredTone
	}
	if update.PreferredPace != "" {
		current.PreferredPace = update.PreferredPace
	}
	if update.PreferredComplexity != "" {
		current.PreferredComplexity = update.PreferredComplexity
	}
}

func (pe *PersonalizationEngine) runLearningUpdates() {
	ticker := time.NewTicker(pe.config.LearningUpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !pe.isRunning {
				return
			}
			pe.performLearningUpdates()
		}
	}
}

func (pe *PersonalizationEngine) runProfileBackup() {
	ticker := time.NewTicker(pe.config.ProfileBackupInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !pe.isRunning {
				return
			}
			if err := pe.saveUserProfiles(); err != nil {
				pe.logger.WithError(err).Error("Failed to backup user profiles")
			}
		}
	}
}

func (pe *PersonalizationEngine) runMetricsCollection() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !pe.isRunning {
				return
			}
			pe.updateLearningMetrics()
		}
	}
}

func (pe *PersonalizationEngine) performLearningUpdates() {
	pe.mutex.Lock()
	defer pe.mutex.Unlock()

	for userID, profile := range pe.userProfiles {
		// 执行定期学习更新
		pe.learningOptimizer.PerformPeriodicUpdate(profile)

		pe.logger.WithFields(logrus.Fields{
			"user_id":  userID,
			"phase":    profile.LearningState.Phase,
			"progress": profile.LearningProgress,
		}).Debug("执行学习更新")
	}
}

func (pe *PersonalizationEngine) updateLearningMetrics() {
	pe.mutex.Lock()
	defer pe.mutex.Unlock()

	totalUsers := int64(len(pe.userProfiles))
	activeUsers := int64(0)
	totalProgress := 0.0
	phaseCount := make(map[LearningPhase]int64)

	for _, profile := range pe.userProfiles {
		// 检查是否为活跃用户（最近7天有交互）
		if time.Since(profile.LastUpdated) < 7*24*time.Hour {
			activeUsers++
		}

		totalProgress += profile.LearningProgress
		phaseCount[profile.LearningState.Phase]++
	}

	pe.learningMetrics.TotalUsers = totalUsers
	pe.learningMetrics.ActiveLearners = activeUsers
	if totalUsers > 0 {
		pe.learningMetrics.AverageLearningRate = totalProgress / float64(totalUsers)
	}
	pe.learningMetrics.PhaseDistribution = phaseCount
	pe.learningMetrics.LastUpdated = time.Now()
}

// InteractionData 交互数据
type InteractionData struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Content   string                 `json:"content"`
	Mode      InteractionMode        `json:"mode"`
	Timestamp time.Time              `json:"timestamp"`
	Success   bool                   `json:"success"`
	Duration  time.Duration          `json:"duration"`
	Context   map[string]interface{} `json:"context"`
	Feedback  *UserFeedback          `json:"feedback"`
}

// UserFeedback 用户反馈
type UserFeedback struct {
	Rating   float64 `json:"rating"`   // 1-5评分
	Helpful  bool    `json:"helpful"`  // 是否有帮助
	Accurate bool    `json:"accurate"` // 是否准确
	Relevant bool    `json:"relevant"` // 是否相关
	Comments string  `json:"comments"` // 评论
}

// BehaviorInsights 行为洞察
type BehaviorInsights struct {
	Patterns []BehaviorPattern `json:"patterns"`
}

// PreferenceUpdates 偏好更新
type PreferenceUpdates struct {
	ResponseStyle      *ResponseStylePreference      `json:"response_style"`
	CommunicationStyle *CommunicationStylePreference `json:"communication_style"`
	ModePreferences    map[InteractionMode]float64   `json:"mode_preferences"`
}

// Adaptation 适应
type Adaptation struct {
	Type       string                 `json:"type"`
	Before     map[string]interface{} `json:"before"`
	After      map[string]interface{} `json:"after"`
	Reason     string                 `json:"reason"`
	Confidence float64                `json:"confidence"`
}
