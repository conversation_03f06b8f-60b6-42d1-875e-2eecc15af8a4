package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// MonitoringEngine 监控引擎
type MonitoringEngine struct {
	db           *gorm.DB
	logger       *logrus.Logger
	hostService  HostService
	wsManager    *WebSocketManager
	config       *MonitoringConfig
	collectors   map[string]MetricCollector
	processors   map[string]MetricProcessor
	storage      MetricStorage
	alertService AlertService
	running      bool
	stopChan     chan struct{}
	mutex        sync.RWMutex
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Enabled             bool               `json:"enabled"`
	CollectInterval     time.Duration      `json:"collect_interval"`
	RetentionDays       int                `json:"retention_days"`
	MaxConcurrent       int                `json:"max_concurrent"`
	HealthCheckInterval time.Duration      `json:"health_check_interval"`
	AlertThresholds     map[string]float64 `json:"alert_thresholds"`
}

// MetricCollector 指标收集器接口
type MetricCollector interface {
	Collect(ctx context.Context, host *model.Host) (*MetricData, error)
	GetName() string
	GetInterval() time.Duration
}

// MetricProcessor 指标处理器接口
type MetricProcessor interface {
	Process(ctx context.Context, data *MetricData) error
	GetName() string
}

// MetricStorage 指标存储接口
type MetricStorage interface {
	Store(ctx context.Context, data *MetricData) error
	Query(ctx context.Context, query *MetricQuery) ([]*MetricData, error)
	Cleanup(ctx context.Context, before time.Time) error
}

// MetricData 指标数据
type MetricData struct {
	ID         string    `json:"id" gorm:"primaryKey"`
	HostID     int64     `json:"host_id" gorm:"index"`
	HostName   string    `json:"host_name"`
	MetricType string    `json:"metric_type" gorm:"index"`
	Timestamp  time.Time `json:"timestamp" gorm:"index"`
	Value      float64   `json:"value"`
	Unit       string    `json:"unit"`
	Tags       string    `json:"tags" gorm:"type:text"`     // JSON string
	Metadata   string    `json:"metadata" gorm:"type:text"` // JSON string
	CreatedAt  time.Time `json:"created_at"`
}

// MetricQuery 指标查询
type MetricQuery struct {
	HostID      *int64    `json:"host_id"`
	MetricType  string    `json:"metric_type"`
	StartTime   time.Time `json:"start_time"`
	EndTime     time.Time `json:"end_time"`
	Aggregation string    `json:"aggregation"` // avg, max, min, sum
	Interval    string    `json:"interval"`    // 1m, 5m, 1h, 1d
	Limit       int       `json:"limit"`
}

// HostStatus 主机状态
type HostStatus struct {
	HostID      int64                  `json:"host_id"`
	HostName    string                 `json:"host_name"`
	Status      string                 `json:"status"` // online, offline, warning, critical
	LastSeen    time.Time              `json:"last_seen"`
	Uptime      time.Duration          `json:"uptime"`
	CPUUsage    float64                `json:"cpu_usage"`
	MemoryUsage float64                `json:"memory_usage"`
	DiskUsage   float64                `json:"disk_usage"`
	NetworkIn   float64                `json:"network_in"`
	NetworkOut  float64                `json:"network_out"`
	LoadAverage []float64              `json:"load_average"`
	Processes   int                    `json:"processes"`
	Alerts      []*Alert               `json:"alerts"`
	Metadata    map[string]interface{} `json:"metadata"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// Alert 告警
type Alert struct {
	ID         string     `json:"id" gorm:"primaryKey"`
	HostID     int64      `json:"host_id" gorm:"index"`
	HostName   string     `json:"host_name"`
	MetricType string     `json:"metric_type" gorm:"index"`
	Level      string     `json:"level" gorm:"index"` // info, warning, critical
	Title      string     `json:"title"`
	Message    string     `json:"message"`
	Value      float64    `json:"value"`
	Threshold  float64    `json:"threshold"`
	Status     string     `json:"status" gorm:"index"` // active, resolved, suppressed
	FirstSeen  time.Time  `json:"first_seen"`
	LastSeen   time.Time  `json:"last_seen"`
	ResolvedAt *time.Time `json:"resolved_at"`
	Count      int        `json:"count"`
	Tags       string     `json:"tags" gorm:"type:text"`     // JSON string
	Metadata   string     `json:"metadata" gorm:"type:text"` // JSON string
	CreatedAt  time.Time  `json:"created_at"`
	UpdatedAt  time.Time  `json:"updated_at"`
}

// NewMonitoringEngine 创建监控引擎
func NewMonitoringEngine(db *gorm.DB, logger *logrus.Logger, hostService HostService, wsManager *WebSocketManager, alertService AlertService) *MonitoringEngine {
	config := &MonitoringConfig{
		Enabled:             true,
		CollectInterval:     30 * time.Second,
		RetentionDays:       30,
		MaxConcurrent:       10,
		HealthCheckInterval: 1 * time.Minute,
		AlertThresholds: map[string]float64{
			"cpu_usage":    80.0,
			"memory_usage": 85.0,
			"disk_usage":   90.0,
		},
	}

	engine := &MonitoringEngine{
		db:           db,
		logger:       logger,
		hostService:  hostService,
		wsManager:    wsManager,
		alertService: alertService,
		config:       config,
		collectors:   make(map[string]MetricCollector),
		processors:   make(map[string]MetricProcessor),
		stopChan:     make(chan struct{}),
	}

	// 初始化数据库表
	engine.initializeDatabase()

	// 注册默认收集器和处理器
	engine.registerDefaultComponents()

	return engine
}

// initializeDatabase 初始化数据库
func (me *MonitoringEngine) initializeDatabase() error {
	return me.db.AutoMigrate(&MetricData{}, &Alert{})
}

// registerDefaultComponents 注册默认组件
func (me *MonitoringEngine) registerDefaultComponents() {
	// 注册收集器
	me.collectors["system"] = &SystemMetricCollector{
		hostService: me.hostService,
		logger:      me.logger,
		interval:    me.config.CollectInterval,
	}

	// 注册处理器
	me.processors["database"] = &DatabaseMetricProcessor{
		db:     me.db,
		logger: me.logger,
	}

	me.processors["websocket"] = &WebSocketMetricProcessor{
		wsManager: me.wsManager,
		logger:    me.logger,
	}

	// 设置存储
	me.storage = &DatabaseMetricStorage{
		db:     me.db,
		logger: me.logger,
	}

	me.logger.WithFields(logrus.Fields{
		"collectors": len(me.collectors),
		"processors": len(me.processors),
	}).Info("Monitoring components registered")
}

// Start 启动监控引擎
func (me *MonitoringEngine) Start(ctx context.Context) error {
	me.mutex.Lock()
	defer me.mutex.Unlock()

	if me.running {
		return fmt.Errorf("monitoring engine is already running")
	}

	if !me.config.Enabled {
		me.logger.Info("Monitoring engine is disabled")
		return nil
	}

	me.running = true

	// 启动收集器
	for name, collector := range me.collectors {
		go me.runCollector(ctx, name, collector)
	}

	// 启动健康检查
	go me.runHealthCheck(ctx)

	me.logger.Info("Monitoring engine started")
	return nil
}

// Stop 停止监控引擎
func (me *MonitoringEngine) Stop() error {
	me.mutex.Lock()
	defer me.mutex.Unlock()

	if !me.running {
		return nil
	}

	close(me.stopChan)
	me.running = false

	me.logger.Info("Monitoring engine stopped")
	return nil
}

// runCollector 运行收集器
func (me *MonitoringEngine) runCollector(ctx context.Context, name string, collector MetricCollector) {
	ticker := time.NewTicker(collector.GetInterval())
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-me.stopChan:
			return
		case <-ticker.C:
			me.collectMetrics(ctx, name, collector)
		}
	}
}

// collectMetrics 收集指标
func (me *MonitoringEngine) collectMetrics(ctx context.Context, collectorName string, collector MetricCollector) {
	hosts, err := me.getActiveHosts()
	if err != nil {
		me.logger.WithError(err).Error("Failed to get active hosts")
		return
	}

	for _, host := range hosts {
		go me.collectHostMetrics(ctx, collectorName, collector, host)
	}
}

// collectHostMetrics 收集主机指标
func (me *MonitoringEngine) collectHostMetrics(ctx context.Context, collectorName string, collector MetricCollector, host *model.Host) {
	data, err := collector.Collect(ctx, host)
	if err != nil {
		me.logger.WithError(err).WithField("host_id", host.ID).Error("Failed to collect metrics")
		return
	}

	if data == nil {
		return
	}

	// 处理指标数据
	for _, processor := range me.processors {
		processor.Process(ctx, data)
	}
}

// getActiveHosts 获取活跃主机
func (me *MonitoringEngine) getActiveHosts() ([]*model.Host, error) {
	var hosts []*model.Host
	err := me.db.Where("status IN ?", []string{"online", "unknown"}).Find(&hosts).Error
	return hosts, err
}

// runHealthCheck 运行健康检查
func (me *MonitoringEngine) runHealthCheck(ctx context.Context) {
	ticker := time.NewTicker(me.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-me.stopChan:
			return
		case <-ticker.C:
			me.performHealthCheck(ctx)
		}
	}
}

// performHealthCheck 执行健康检查
func (me *MonitoringEngine) performHealthCheck(ctx context.Context) {
	hosts, err := me.getActiveHosts()
	if err != nil {
		me.logger.WithError(err).Error("Failed to get hosts for health check")
		return
	}

	for _, host := range hosts {
		go me.checkHostHealth(ctx, host)
	}
}

// checkHostHealth 检查主机健康状态
func (me *MonitoringEngine) checkHostHealth(ctx context.Context, host *model.Host) {
	result, err := me.hostService.TestConnection(host.ID)
	if err != nil {
		me.logger.WithError(err).WithField("host_id", host.ID).Error("Health check failed")
		return
	}

	status := "offline"
	if result.Success {
		status = "online"
	}

	me.db.Model(&model.Host{}).Where("id = ?", host.ID).Update("status", status)

	// 发送状态更新
	if me.wsManager != nil {
		statusUpdate := map[string]interface{}{
			"host_id":    host.ID,
			"host_name":  host.Name,
			"status":     status,
			"last_check": time.Now(),
		}

		message := &WSMessage{
			Type:      "host_status_update",
			Data:      statusUpdate,
			Timestamp: time.Now(),
		}

		me.wsManager.BroadcastToAll(message)
	}
}

// GetHostStatus 获取主机状态
func (me *MonitoringEngine) GetHostStatus(hostID int64) (*HostStatus, error) {
	var host model.Host
	if err := me.db.First(&host, hostID).Error; err != nil {
		return nil, err
	}

	status := &HostStatus{
		HostID:    host.ID,
		HostName:  host.Name,
		Status:    host.Status,
		UpdatedAt: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	if host.LastConnected != nil {
		status.LastSeen = *host.LastConnected
	}

	return status, nil
}

// SystemMetricCollector 系统指标收集器
type SystemMetricCollector struct {
	hostService HostService
	logger      *logrus.Logger
	interval    time.Duration
}

func (smc *SystemMetricCollector) Collect(ctx context.Context, host *model.Host) (*MetricData, error) {
	// 执行系统监控命令
	req := &model.CommandExecuteRequest{
		Command: "echo 'cpu:75.5,memory:68.2,disk:45.8'", // 模拟数据
		Timeout: 30,
	}

	result, err := smc.hostService.ExecuteCommand(host.ID, req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute monitoring command: %w", err)
	}

	if result.ExitCode != 0 {
		return nil, fmt.Errorf("monitoring command failed with exit code %d", result.ExitCode)
	}

	// 创建CPU使用率指标
	metric := &MetricData{
		ID:         fmt.Sprintf("cpu_%d_%d", host.ID, time.Now().Unix()),
		HostID:     host.ID,
		HostName:   host.Name,
		MetricType: "cpu_usage",
		Timestamp:  time.Now(),
		Value:      75.5, // 模拟数据
		Unit:       "percent",
		CreatedAt:  time.Now(),
	}

	return metric, nil
}

func (smc *SystemMetricCollector) GetName() string {
	return "system"
}

func (smc *SystemMetricCollector) GetInterval() time.Duration {
	return smc.interval
}

// DatabaseMetricProcessor 数据库指标处理器
type DatabaseMetricProcessor struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func (dmp *DatabaseMetricProcessor) Process(ctx context.Context, data *MetricData) error {
	return dmp.db.Create(data).Error
}

func (dmp *DatabaseMetricProcessor) GetName() string {
	return "database"
}

// WebSocketMetricProcessor WebSocket指标处理器
type WebSocketMetricProcessor struct {
	wsManager *WebSocketManager
	logger    *logrus.Logger
}

func (wmp *WebSocketMetricProcessor) Process(ctx context.Context, data *MetricData) error {
	if wmp.wsManager == nil {
		return nil
	}

	message := &WSMessage{
		Type: "metric_update",
		Data: map[string]interface{}{
			"host_id":     data.HostID,
			"host_name":   data.HostName,
			"metric_type": data.MetricType,
			"value":       data.Value,
			"unit":        data.Unit,
			"timestamp":   data.Timestamp,
		},
		Timestamp: time.Now(),
	}

	return wmp.wsManager.BroadcastToAll(message)
}

func (wmp *WebSocketMetricProcessor) GetName() string {
	return "websocket"
}

// DatabaseMetricStorage 数据库指标存储
type DatabaseMetricStorage struct {
	db     *gorm.DB
	logger *logrus.Logger
}

func (dms *DatabaseMetricStorage) Store(ctx context.Context, data *MetricData) error {
	return dms.db.Create(data).Error
}

func (dms *DatabaseMetricStorage) Query(ctx context.Context, query *MetricQuery) ([]*MetricData, error) {
	db := dms.db.Model(&MetricData{})

	if query.HostID != nil {
		db = db.Where("host_id = ?", *query.HostID)
	}
	if query.MetricType != "" {
		db = db.Where("metric_type = ?", query.MetricType)
	}
	if !query.StartTime.IsZero() {
		db = db.Where("timestamp >= ?", query.StartTime)
	}
	if !query.EndTime.IsZero() {
		db = db.Where("timestamp <= ?", query.EndTime)
	}

	db = db.Order("timestamp DESC")

	if query.Limit > 0 {
		db = db.Limit(query.Limit)
	}

	var metrics []*MetricData
	err := db.Find(&metrics).Error
	return metrics, err
}

func (dms *DatabaseMetricStorage) Cleanup(ctx context.Context, before time.Time) error {
	result := dms.db.Where("created_at < ?", before).Delete(&MetricData{})
	if result.Error != nil {
		return result.Error
	}

	if result.RowsAffected > 0 {
		dms.logger.WithField("deleted_metrics", result.RowsAffected).Info("Cleaned up old metrics")
	}

	return nil
}
