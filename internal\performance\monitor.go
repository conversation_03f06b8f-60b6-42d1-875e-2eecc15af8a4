package performance

import (
	"context"
	"runtime"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	// 系统指标
	CPUUsage        float64   `json:"cpu_usage"`         // CPU使用率
	MemoryUsage     float64   `json:"memory_usage"`      // 内存使用率
	GoroutineCount  int       `json:"goroutine_count"`   // Goroutine数量
	HeapSize        uint64    `json:"heap_size"`         // 堆大小
	HeapInUse       uint64    `json:"heap_in_use"`       // 堆使用量
	StackInUse      uint64    `json:"stack_in_use"`      // 栈使用量
	GCPauseTime     float64   `json:"gc_pause_time"`     // GC暂停时间
	
	// 应用指标
	RequestCount    int64     `json:"request_count"`     // 请求总数
	ErrorCount      int64     `json:"error_count"`       // 错误总数
	AvgResponseTime float64   `json:"avg_response_time"` // 平均响应时间
	P95ResponseTime float64   `json:"p95_response_time"` // P95响应时间
	P99ResponseTime float64   `json:"p99_response_time"` // P99响应时间
	ThroughputRPS   float64   `json:"throughput_rps"`    // 吞吐量(RPS)
	
	// 数据库指标
	DBConnections   int       `json:"db_connections"`    // 数据库连接数
	DBQueryTime     float64   `json:"db_query_time"`     // 数据库查询时间
	DBSlowQueries   int64     `json:"db_slow_queries"`   // 慢查询数量
	
	// WebSocket指标
	WSConnections   int       `json:"ws_connections"`    // WebSocket连接数
	WSMessagesSent  int64     `json:"ws_messages_sent"`  // 发送消息数
	WSMessagesRecv  int64     `json:"ws_messages_recv"`  // 接收消息数
	
	// 缓存指标
	CacheHitRate    float64   `json:"cache_hit_rate"`    // 缓存命中率
	CacheSize       int64     `json:"cache_size"`        // 缓存大小
	
	Timestamp       time.Time `json:"timestamp"`
}

// PerformanceThresholds 性能阈值
type PerformanceThresholds struct {
	CPUUsageWarning      float64 `json:"cpu_usage_warning"`       // CPU使用率警告阈值
	CPUUsageCritical     float64 `json:"cpu_usage_critical"`      // CPU使用率严重阈值
	MemoryUsageWarning   float64 `json:"memory_usage_warning"`    // 内存使用率警告阈值
	MemoryUsageCritical  float64 `json:"memory_usage_critical"`   // 内存使用率严重阈值
	ResponseTimeWarning  float64 `json:"response_time_warning"`   // 响应时间警告阈值
	ResponseTimeCritical float64 `json:"response_time_critical"`  // 响应时间严重阈值
	ErrorRateWarning     float64 `json:"error_rate_warning"`      // 错误率警告阈值
	ErrorRateCritical    float64 `json:"error_rate_critical"`     // 错误率严重阈值
	GoroutineWarning     int     `json:"goroutine_warning"`       // Goroutine数量警告阈值
	GoroutineCritical    int     `json:"goroutine_critical"`      // Goroutine数量严重阈值
}

// PerformanceAlert 性能告警
type PerformanceAlert struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`        // warning, critical
	Metric      string                 `json:"metric"`      // 指标名称
	Value       float64                `json:"value"`       // 当前值
	Threshold   float64                `json:"threshold"`   // 阈值
	Message     string                 `json:"message"`     // 告警消息
	Timestamp   time.Time              `json:"timestamp"`
	Resolved    bool                   `json:"resolved"`
	ResolvedAt  *time.Time             `json:"resolved_at,omitempty"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// PerformanceMonitor 性能监控器
type PerformanceMonitor struct {
	config         *PerformanceConfig
	metrics        *PerformanceMetrics
	thresholds     *PerformanceThresholds
	alerts         []*PerformanceAlert
	collectors     []MetricCollector
	alertHandlers  []AlertHandler
	logger         *logrus.Logger
	mutex          sync.RWMutex
	running        bool
	stopChan       chan struct{}
	metricsChan    chan *PerformanceMetrics
	alertsChan     chan *PerformanceAlert
}

// PerformanceConfig 性能监控配置
type PerformanceConfig struct {
	Enabled           bool          `json:"enabled"`
	CollectInterval   time.Duration `json:"collect_interval"`   // 收集间隔
	RetentionPeriod   time.Duration `json:"retention_period"`   // 数据保留期
	AlertEnabled      bool          `json:"alert_enabled"`      // 告警启用
	AlertCooldown     time.Duration `json:"alert_cooldown"`     // 告警冷却期
	MetricsBufferSize int           `json:"metrics_buffer_size"` // 指标缓冲区大小
	AlertBufferSize   int           `json:"alert_buffer_size"`   // 告警缓冲区大小
}

// MetricCollector 指标收集器接口
type MetricCollector interface {
	Collect(ctx context.Context) (*PerformanceMetrics, error)
	Name() string
}

// AlertHandler 告警处理器接口
type AlertHandler interface {
	Handle(ctx context.Context, alert *PerformanceAlert) error
	Name() string
}

// NewPerformanceMonitor 创建性能监控器
func NewPerformanceMonitor(config *PerformanceConfig, logger *logrus.Logger) *PerformanceMonitor {
	// 设置默认值
	if config.CollectInterval <= 0 {
		config.CollectInterval = 10 * time.Second
	}
	if config.RetentionPeriod <= 0 {
		config.RetentionPeriod = 24 * time.Hour
	}
	if config.AlertCooldown <= 0 {
		config.AlertCooldown = 5 * time.Minute
	}
	if config.MetricsBufferSize <= 0 {
		config.MetricsBufferSize = 1000
	}
	if config.AlertBufferSize <= 0 {
		config.AlertBufferSize = 100
	}

	// 默认阈值
	thresholds := &PerformanceThresholds{
		CPUUsageWarning:      70.0,  // 70%
		CPUUsageCritical:     90.0,  // 90%
		MemoryUsageWarning:   80.0,  // 80%
		MemoryUsageCritical:  95.0,  // 95%
		ResponseTimeWarning:  1000.0, // 1秒
		ResponseTimeCritical: 5000.0, // 5秒
		ErrorRateWarning:     5.0,   // 5%
		ErrorRateCritical:    15.0,  // 15%
		GoroutineWarning:     1000,
		GoroutineCritical:    5000,
	}

	pm := &PerformanceMonitor{
		config:      config,
		thresholds:  thresholds,
		alerts:      make([]*PerformanceAlert, 0),
		collectors:  make([]MetricCollector, 0),
		alertHandlers: make([]AlertHandler, 0),
		logger:      logger,
		stopChan:    make(chan struct{}),
		metricsChan: make(chan *PerformanceMetrics, config.MetricsBufferSize),
		alertsChan:  make(chan *PerformanceAlert, config.AlertBufferSize),
	}

	// 注册默认收集器
	pm.RegisterCollector(&SystemMetricCollector{logger: logger})
	pm.RegisterCollector(&RuntimeMetricCollector{logger: logger})

	// 注册默认告警处理器
	pm.RegisterAlertHandler(&LogAlertHandler{logger: logger})

	return pm
}

// Start 启动性能监控器
func (pm *PerformanceMonitor) Start(ctx context.Context) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if pm.running {
		return fmt.Errorf("performance monitor is already running")
	}

	if !pm.config.Enabled {
		pm.logger.Info("Performance monitor is disabled")
		return nil
	}

	pm.running = true

	// 启动指标收集协程
	go pm.collectLoop(ctx)

	// 启动告警处理协程
	go pm.alertLoop(ctx)

	// 启动数据处理协程
	go pm.processLoop(ctx)

	pm.logger.Info("Performance monitor started")
	return nil
}

// Stop 停止性能监控器
func (pm *PerformanceMonitor) Stop() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if !pm.running {
		return nil
	}

	close(pm.stopChan)
	pm.running = false

	pm.logger.Info("Performance monitor stopped")
	return nil
}

// RegisterCollector 注册指标收集器
func (pm *PerformanceMonitor) RegisterCollector(collector MetricCollector) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.collectors = append(pm.collectors, collector)
	pm.logger.WithField("collector", collector.Name()).Info("Metric collector registered")
}

// RegisterAlertHandler 注册告警处理器
func (pm *PerformanceMonitor) RegisterAlertHandler(handler AlertHandler) {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	pm.alertHandlers = append(pm.alertHandlers, handler)
	pm.logger.WithField("handler", handler.Name()).Info("Alert handler registered")
}

// collectLoop 指标收集循环
func (pm *PerformanceMonitor) collectLoop(ctx context.Context) {
	ticker := time.NewTicker(pm.config.CollectInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-pm.stopChan:
			return
		case <-ticker.C:
			pm.collectMetrics(ctx)
		}
	}
}

// collectMetrics 收集指标
func (pm *PerformanceMonitor) collectMetrics(ctx context.Context) {
	pm.mutex.RLock()
	collectors := make([]MetricCollector, len(pm.collectors))
	copy(collectors, pm.collectors)
	pm.mutex.RUnlock()

	// 合并所有收集器的指标
	combinedMetrics := &PerformanceMetrics{
		Timestamp: time.Now(),
	}

	for _, collector := range collectors {
		metrics, err := collector.Collect(ctx)
		if err != nil {
			pm.logger.WithFields(logrus.Fields{
				"collector": collector.Name(),
				"error":     err.Error(),
			}).Error("Failed to collect metrics")
			continue
		}

		// 合并指标
		pm.mergeMetrics(combinedMetrics, metrics)
	}

	// 发送到处理通道
	select {
	case pm.metricsChan <- combinedMetrics:
	default:
		pm.logger.Warn("Metrics channel is full, dropping metrics")
	}
}

// mergeMetrics 合并指标
func (pm *PerformanceMonitor) mergeMetrics(target, source *PerformanceMetrics) {
	if source.CPUUsage > 0 {
		target.CPUUsage = source.CPUUsage
	}
	if source.MemoryUsage > 0 {
		target.MemoryUsage = source.MemoryUsage
	}
	if source.GoroutineCount > 0 {
		target.GoroutineCount = source.GoroutineCount
	}
	if source.HeapSize > 0 {
		target.HeapSize = source.HeapSize
	}
	if source.HeapInUse > 0 {
		target.HeapInUse = source.HeapInUse
	}
	if source.StackInUse > 0 {
		target.StackInUse = source.StackInUse
	}
	if source.GCPauseTime > 0 {
		target.GCPauseTime = source.GCPauseTime
	}
	if source.RequestCount > 0 {
		target.RequestCount += source.RequestCount
	}
	if source.ErrorCount > 0 {
		target.ErrorCount += source.ErrorCount
	}
	if source.AvgResponseTime > 0 {
		target.AvgResponseTime = source.AvgResponseTime
	}
	if source.P95ResponseTime > 0 {
		target.P95ResponseTime = source.P95ResponseTime
	}
	if source.P99ResponseTime > 0 {
		target.P99ResponseTime = source.P99ResponseTime
	}
	if source.ThroughputRPS > 0 {
		target.ThroughputRPS = source.ThroughputRPS
	}
}

// processLoop 数据处理循环
func (pm *PerformanceMonitor) processLoop(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-pm.stopChan:
			return
		case metrics := <-pm.metricsChan:
			pm.processMetrics(metrics)
		}
	}
}

// processMetrics 处理指标
func (pm *PerformanceMonitor) processMetrics(metrics *PerformanceMetrics) {
	pm.mutex.Lock()
	pm.metrics = metrics
	pm.mutex.Unlock()

	// 检查告警
	if pm.config.AlertEnabled {
		pm.checkAlerts(metrics)
	}

	pm.logger.WithFields(logrus.Fields{
		"cpu_usage":    metrics.CPUUsage,
		"memory_usage": metrics.MemoryUsage,
		"goroutines":   metrics.GoroutineCount,
		"heap_size":    metrics.HeapSize,
	}).Debug("Performance metrics collected")
}

// checkAlerts 检查告警
func (pm *PerformanceMonitor) checkAlerts(metrics *PerformanceMetrics) {
	alerts := make([]*PerformanceAlert, 0)

	// 检查CPU使用率
	if metrics.CPUUsage >= pm.thresholds.CPUUsageCritical {
		alerts = append(alerts, pm.createAlert("critical", "cpu_usage", metrics.CPUUsage, pm.thresholds.CPUUsageCritical, "CPU usage is critically high"))
	} else if metrics.CPUUsage >= pm.thresholds.CPUUsageWarning {
		alerts = append(alerts, pm.createAlert("warning", "cpu_usage", metrics.CPUUsage, pm.thresholds.CPUUsageWarning, "CPU usage is high"))
	}

	// 检查内存使用率
	if metrics.MemoryUsage >= pm.thresholds.MemoryUsageCritical {
		alerts = append(alerts, pm.createAlert("critical", "memory_usage", metrics.MemoryUsage, pm.thresholds.MemoryUsageCritical, "Memory usage is critically high"))
	} else if metrics.MemoryUsage >= pm.thresholds.MemoryUsageWarning {
		alerts = append(alerts, pm.createAlert("warning", "memory_usage", metrics.MemoryUsage, pm.thresholds.MemoryUsageWarning, "Memory usage is high"))
	}

	// 检查响应时间
	if metrics.AvgResponseTime >= pm.thresholds.ResponseTimeCritical {
		alerts = append(alerts, pm.createAlert("critical", "response_time", metrics.AvgResponseTime, pm.thresholds.ResponseTimeCritical, "Response time is critically high"))
	} else if metrics.AvgResponseTime >= pm.thresholds.ResponseTimeWarning {
		alerts = append(alerts, pm.createAlert("warning", "response_time", metrics.AvgResponseTime, pm.thresholds.ResponseTimeWarning, "Response time is high"))
	}

	// 检查Goroutine数量
	if float64(metrics.GoroutineCount) >= float64(pm.thresholds.GoroutineCritical) {
		alerts = append(alerts, pm.createAlert("critical", "goroutine_count", float64(metrics.GoroutineCount), float64(pm.thresholds.GoroutineCritical), "Goroutine count is critically high"))
	} else if float64(metrics.GoroutineCount) >= float64(pm.thresholds.GoroutineWarning) {
		alerts = append(alerts, pm.createAlert("warning", "goroutine_count", float64(metrics.GoroutineCount), float64(pm.thresholds.GoroutineWarning), "Goroutine count is high"))
	}

	// 发送告警
	for _, alert := range alerts {
		select {
		case pm.alertsChan <- alert:
		default:
			pm.logger.Warn("Alert channel is full, dropping alert")
		}
	}
}

// createAlert 创建告警
func (pm *PerformanceMonitor) createAlert(alertType, metric string, value, threshold float64, message string) *PerformanceAlert {
	return &PerformanceAlert{
		ID:        fmt.Sprintf("%s_%s_%d", alertType, metric, time.Now().UnixNano()),
		Type:      alertType,
		Metric:    metric,
		Value:     value,
		Threshold: threshold,
		Message:   message,
		Timestamp: time.Now(),
		Resolved:  false,
		Metadata:  make(map[string]interface{}),
	}
}

// alertLoop 告警处理循环
func (pm *PerformanceMonitor) alertLoop(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-pm.stopChan:
			return
		case alert := <-pm.alertsChan:
			pm.handleAlert(ctx, alert)
		}
	}
}

// handleAlert 处理告警
func (pm *PerformanceMonitor) handleAlert(ctx context.Context, alert *PerformanceAlert) {
	pm.mutex.Lock()
	pm.alerts = append(pm.alerts, alert)
	pm.mutex.Unlock()

	// 调用所有告警处理器
	pm.mutex.RLock()
	handlers := make([]AlertHandler, len(pm.alertHandlers))
	copy(handlers, pm.alertHandlers)
	pm.mutex.RUnlock()

	for _, handler := range handlers {
		if err := handler.Handle(ctx, alert); err != nil {
			pm.logger.WithFields(logrus.Fields{
				"handler": handler.Name(),
				"alert":   alert.ID,
				"error":   err.Error(),
			}).Error("Failed to handle alert")
		}
	}
}

// GetCurrentMetrics 获取当前指标
func (pm *PerformanceMonitor) GetCurrentMetrics() *PerformanceMetrics {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	if pm.metrics == nil {
		return &PerformanceMetrics{Timestamp: time.Now()}
	}

	// 返回副本
	metrics := *pm.metrics
	return &metrics
}

// GetAlerts 获取告警列表
func (pm *PerformanceMonitor) GetAlerts(limit int) []*PerformanceAlert {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	if limit <= 0 || limit > len(pm.alerts) {
		limit = len(pm.alerts)
	}

	// 返回最新的告警
	start := len(pm.alerts) - limit
	if start < 0 {
		start = 0
	}

	alerts := make([]*PerformanceAlert, limit)
	copy(alerts, pm.alerts[start:])
	return alerts
}

// 默认收集器实现

// SystemMetricCollector 系统指标收集器
type SystemMetricCollector struct {
	logger *logrus.Logger
}

func (smc *SystemMetricCollector) Name() string {
	return "system_metric_collector"
}

func (smc *SystemMetricCollector) Collect(ctx context.Context) (*PerformanceMetrics, error) {
	metrics := &PerformanceMetrics{
		Timestamp: time.Now(),
	}

	// 这里应该实现实际的系统指标收集
	// 简化实现，返回模拟数据
	metrics.CPUUsage = 45.5
	metrics.MemoryUsage = 62.3

	return metrics, nil
}

// RuntimeMetricCollector 运行时指标收集器
type RuntimeMetricCollector struct {
	logger *logrus.Logger
}

func (rmc *RuntimeMetricCollector) Name() string {
	return "runtime_metric_collector"
}

func (rmc *RuntimeMetricCollector) Collect(ctx context.Context) (*PerformanceMetrics, error) {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	metrics := &PerformanceMetrics{
		GoroutineCount: runtime.NumGoroutine(),
		HeapSize:       m.HeapSys,
		HeapInUse:      m.HeapInuse,
		StackInUse:     m.StackInuse,
		Timestamp:      time.Now(),
	}

	// 计算GC暂停时间
	if len(m.PauseNs) > 0 {
		var totalPause uint64
		for _, pause := range m.PauseNs {
			totalPause += pause
		}
		metrics.GCPauseTime = float64(totalPause) / float64(len(m.PauseNs)) / 1e6 // 转换为毫秒
	}

	return metrics, nil
}

// LogAlertHandler 日志告警处理器
type LogAlertHandler struct {
	logger *logrus.Logger
}

func (lah *LogAlertHandler) Name() string {
	return "log_alert_handler"
}

func (lah *LogAlertHandler) Handle(ctx context.Context, alert *PerformanceAlert) error {
	level := logrus.WarnLevel
	if alert.Type == "critical" {
		level = logrus.ErrorLevel
	}

	lah.logger.WithFields(logrus.Fields{
		"alert_id":   alert.ID,
		"alert_type": alert.Type,
		"metric":     alert.Metric,
		"value":      alert.Value,
		"threshold":  alert.Threshold,
		"message":    alert.Message,
	}).Log(level, "Performance alert triggered")

	return nil
}
