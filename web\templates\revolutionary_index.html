<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.title}} - 革命性AI运维管理平台</title>
    
    <!-- 引入现代化CSS框架 -->
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- 自定义样式 -->
    <style>
        .gradient-bg {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .glass-effect {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .chat-container {
            height: 600px;
            background: rgba(0, 0, 0, 0.05);
            border-radius: 20px;
        }
        
        .message-bubble {
            max-width: 80%;
            word-wrap: break-word;
        }
        
        .user-message {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .ai-message {
            background: rgba(255, 255, 255, 0.9);
            color: #333;
            border: 1px solid rgba(0, 0, 0, 0.1);
        }
        
        .typing-indicator {
            display: none;
        }
        
        .typing-indicator.active {
            display: flex;
        }
        
        .dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #667eea;
            animation: typing 1.4s infinite ease-in-out;
        }
        
        .dot:nth-child(1) { animation-delay: -0.32s; }
        .dot:nth-child(2) { animation-delay: -0.16s; }
        
        @keyframes typing {
            0%, 80%, 100% { transform: scale(0); }
            40% { transform: scale(1); }
        }
        
        .feature-card {
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        
        .stats-card {
            background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
            backdrop-filter: blur(10px);
        }
    </style>
</head>
<body class="gradient-bg min-h-screen">
    <!-- 导航栏 -->
    <nav class="glass-effect p-4 mb-8">
        <div class="container mx-auto flex justify-between items-center">
            <div class="flex items-center space-x-4">
                <i class="fas fa-brain text-white text-2xl"></i>
                <h1 class="text-white text-xl font-bold">{{.title}}</h1>
                <span class="bg-green-500 text-white px-2 py-1 rounded-full text-xs">v{{.version}}</span>
            </div>
            <div class="flex items-center space-x-4">
                <button id="voiceBtn" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-microphone text-lg"></i>
                </button>
                <button id="settingsBtn" class="text-white hover:text-gray-200 transition-colors">
                    <i class="fas fa-cog text-lg"></i>
                </button>
                <div class="text-white text-sm">
                    <i class="fas fa-circle text-green-400 mr-1"></i>
                    在线
                </div>
            </div>
        </div>
    </nav>

    <div class="container mx-auto px-4">
        <!-- 统计面板 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div class="stats-card p-6 rounded-xl text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">AI处理请求</p>
                        <p class="text-2xl font-bold" id="processedCount">0</p>
                    </div>
                    <i class="fas fa-brain text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="stats-card p-6 rounded-xl text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">意图识别率</p>
                        <p class="text-2xl font-bold" id="accuracyRate">98.5%</p>
                    </div>
                    <i class="fas fa-bullseye text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="stats-card p-6 rounded-xl text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">响应时间</p>
                        <p class="text-2xl font-bold" id="responseTime">0.8s</p>
                    </div>
                    <i class="fas fa-tachometer-alt text-3xl opacity-60"></i>
                </div>
            </div>
            <div class="stats-card p-6 rounded-xl text-white">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm opacity-80">在线主机</p>
                        <p class="text-2xl font-bold" id="onlineHosts">12</p>
                    </div>
                    <i class="fas fa-server text-3xl opacity-60"></i>
                </div>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <!-- AI对话界面 -->
            <div class="lg:col-span-2">
                <div class="glass-effect rounded-xl p-6">
                    <div class="flex items-center justify-between mb-4">
                        <h2 class="text-white text-xl font-bold">
                            <i class="fas fa-comments mr-2"></i>
                            AI智能对话
                        </h2>
                        <div class="flex space-x-2">
                            <button id="clearChat" class="text-white hover:text-gray-200 transition-colors">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button id="exportChat" class="text-white hover:text-gray-200 transition-colors">
                                <i class="fas fa-download"></i>
                            </button>
                        </div>
                    </div>
                    
                    <!-- 聊天容器 -->
                    <div class="chat-container p-4 overflow-y-auto mb-4" id="chatContainer">
                        <!-- 欢迎消息 -->
                        <div class="flex items-start space-x-3 mb-4">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                            <div class="ai-message message-bubble p-3 rounded-lg">
                                <p>🤖 您好！我是革命性AI运维助手，具备以下能力：</p>
                                <ul class="mt-2 space-y-1 text-sm">
                                    <li>• 🧠 智能意图识别（支持50+种运维场景）</li>
                                    <li>• 🎯 多模态交互（语音、文本、图像、手势）</li>
                                    <li>• 🔍 智能故障诊断和预测性运维</li>
                                    <li>• 🛡️ 企业级安全和合规管理</li>
                                    <li>• 🚀 自动化编排和智能优化建议</li>
                                </ul>
                                <p class="mt-2">请告诉我您需要什么帮助？</p>
                            </div>
                        </div>
                        
                        <!-- 打字指示器 -->
                        <div class="typing-indicator flex items-start space-x-3 mb-4">
                            <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-robot text-white text-sm"></i>
                            </div>
                            <div class="ai-message message-bubble p-3 rounded-lg">
                                <div class="flex space-x-1">
                                    <div class="dot"></div>
                                    <div class="dot"></div>
                                    <div class="dot"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 输入区域 -->
                    <div class="flex space-x-2">
                        <div class="flex-1 relative">
                            <input type="text" id="messageInput" 
                                   placeholder="输入您的运维需求..." 
                                   class="w-full p-3 rounded-lg bg-white bg-opacity-90 border border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <button id="attachBtn" class="absolute right-12 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                                <i class="fas fa-paperclip"></i>
                            </button>
                            <button id="emojiBtn" class="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700">
                                <i class="fas fa-smile"></i>
                            </button>
                        </div>
                        <button id="sendBtn" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-lg transition-colors">
                            <i class="fas fa-paper-plane"></i>
                        </button>
                    </div>
                    
                    <!-- 快捷操作 -->
                    <div class="mt-4 flex flex-wrap gap-2">
                        <button class="quick-action bg-white bg-opacity-20 text-white px-3 py-1 rounded-full text-sm hover:bg-opacity-30 transition-colors" data-action="查看主机状态">
                            查看主机状态
                        </button>
                        <button class="quick-action bg-white bg-opacity-20 text-white px-3 py-1 rounded-full text-sm hover:bg-opacity-30 transition-colors" data-action="添加主机">
                            添加主机
                        </button>
                        <button class="quick-action bg-white bg-opacity-20 text-white px-3 py-1 rounded-full text-sm hover:bg-opacity-30 transition-colors" data-action="系统监控">
                            系统监控
                        </button>
                        <button class="quick-action bg-white bg-opacity-20 text-white px-3 py-1 rounded-full text-sm hover:bg-opacity-30 transition-colors" data-action="故障诊断">
                            故障诊断
                        </button>
                    </div>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="space-y-6">
                <!-- 系统状态 -->
                <div class="glass-effect rounded-xl p-6">
                    <h3 class="text-white text-lg font-bold mb-4">
                        <i class="fas fa-heartbeat mr-2"></i>
                        系统状态
                    </h3>
                    <div class="space-y-3">
                        <div class="flex justify-between items-center">
                            <span class="text-white text-sm">AI大脑</span>
                            <span class="text-green-400 text-sm">
                                <i class="fas fa-circle mr-1"></i>运行中
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-white text-sm">意图引擎</span>
                            <span class="text-green-400 text-sm">
                                <i class="fas fa-circle mr-1"></i>运行中
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-white text-sm">多模态处理</span>
                            <span class="text-green-400 text-sm">
                                <i class="fas fa-circle mr-1"></i>运行中
                            </span>
                        </div>
                        <div class="flex justify-between items-center">
                            <span class="text-white text-sm">知识图谱</span>
                            <span class="text-green-400 text-sm">
                                <i class="fas fa-circle mr-1"></i>运行中
                            </span>
                        </div>
                    </div>
                </div>

                <!-- 最近操作 -->
                <div class="glass-effect rounded-xl p-6">
                    <h3 class="text-white text-lg font-bold mb-4">
                        <i class="fas fa-history mr-2"></i>
                        最近操作
                    </h3>
                    <div class="space-y-3" id="recentOperations">
                        <div class="text-white text-sm opacity-60">
                            暂无操作记录
                        </div>
                    </div>
                </div>

                <!-- 智能建议 -->
                <div class="glass-effect rounded-xl p-6">
                    <h3 class="text-white text-lg font-bold mb-4">
                        <i class="fas fa-lightbulb mr-2"></i>
                        智能建议
                    </h3>
                    <div class="space-y-3" id="smartSuggestions">
                        <div class="bg-yellow-500 bg-opacity-20 border border-yellow-500 border-opacity-30 rounded-lg p-3">
                            <p class="text-yellow-200 text-sm">
                                <i class="fas fa-exclamation-triangle mr-1"></i>
                                建议检查主机 192.168.1.100 的磁盘使用率
                            </p>
                        </div>
                        <div class="bg-blue-500 bg-opacity-20 border border-blue-500 border-opacity-30 rounded-lg p-3">
                            <p class="text-blue-200 text-sm">
                                <i class="fas fa-info-circle mr-1"></i>
                                可以优化数据库查询性能
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        // 全局变量
        let sessionId = 'session_' + Date.now();
        let messageCount = 0;
        
        // DOM元素
        const chatContainer = document.getElementById('chatContainer');
        const messageInput = document.getElementById('messageInput');
        const sendBtn = document.getElementById('sendBtn');
        const typingIndicator = document.querySelector('.typing-indicator');
        const processedCountEl = document.getElementById('processedCount');
        const responseTimeEl = document.getElementById('responseTime');
        
        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeEventListeners();
            updateStats();
            setInterval(updateStats, 5000); // 每5秒更新统计
        });
        
        // 初始化事件监听器
        function initializeEventListeners() {
            // 发送消息
            sendBtn.addEventListener('click', sendMessage);
            messageInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    sendMessage();
                }
            });
            
            // 快捷操作
            document.querySelectorAll('.quick-action').forEach(btn => {
                btn.addEventListener('click', function() {
                    messageInput.value = this.dataset.action;
                    sendMessage();
                });
            });
            
            // 清除聊天
            document.getElementById('clearChat').addEventListener('click', clearChat);
            
            // 语音按钮
            document.getElementById('voiceBtn').addEventListener('click', toggleVoiceInput);
        }
        
        // 发送消息
        async function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            // 添加用户消息到界面
            addMessage(message, 'user');
            messageInput.value = '';
            
            // 显示打字指示器
            showTypingIndicator();
            
            try {
                // 发送到后端
                const response = await fetch('/api/v2/chat/message', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        message: message,
                        session_id: sessionId,
                        user_id: 1,
                        input_type: 'text'
                    })
                });
                
                const data = await response.json();
                
                // 隐藏打字指示器
                hideTypingIndicator();
                
                // 添加AI回复
                addMessage(data.response_text || '处理完成', 'ai', data);
                
                // 更新统计
                messageCount++;
                updateProcessedCount();
                updateResponseTime(data.processing_time);
                
                // 添加到最近操作
                addRecentOperation(message, data.intent_result?.primary_intent?.type);
                
            } catch (error) {
                hideTypingIndicator();
                addMessage('抱歉，处理您的请求时出现错误。请稍后重试。', 'ai');
                console.error('Error:', error);
            }
        }
        
        // 添加消息到聊天界面
        function addMessage(text, type, data = null) {
            const messageDiv = document.createElement('div');
            messageDiv.className = 'flex items-start space-x-3 mb-4';
            
            if (type === 'user') {
                messageDiv.innerHTML = `
                    <div class="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center ml-auto">
                        <i class="fas fa-user text-white text-sm"></i>
                    </div>
                    <div class="user-message message-bubble p-3 rounded-lg">
                        <p>${text}</p>
                    </div>
                `;
                messageDiv.classList.add('flex-row-reverse');
            } else {
                let additionalInfo = '';
                if (data) {
                    additionalInfo = `
                        <div class="mt-2 text-xs opacity-70">
                            <div>意图: ${data.intent_result?.primary_intent?.type || '未知'}</div>
                            <div>置信度: ${(data.confidence * 100).toFixed(1)}%</div>
                            <div>处理时间: ${data.processing_time || '未知'}</div>
                        </div>
                    `;
                }
                
                messageDiv.innerHTML = `
                    <div class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                        <i class="fas fa-robot text-white text-sm"></i>
                    </div>
                    <div class="ai-message message-bubble p-3 rounded-lg">
                        <p>${text}</p>
                        ${additionalInfo}
                    </div>
                `;
            }
            
            // 插入到打字指示器之前
            chatContainer.insertBefore(messageDiv, typingIndicator);
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        // 显示/隐藏打字指示器
        function showTypingIndicator() {
            typingIndicator.classList.add('active');
            chatContainer.scrollTop = chatContainer.scrollHeight;
        }
        
        function hideTypingIndicator() {
            typingIndicator.classList.remove('active');
        }
        
        // 清除聊天
        function clearChat() {
            const messages = chatContainer.querySelectorAll('.flex.items-start.space-x-3.mb-4:not(.typing-indicator)');
            messages.forEach(msg => {
                if (!msg.querySelector('.ai-message')?.textContent.includes('您好！我是革命性AI运维助手')) {
                    msg.remove();
                }
            });
        }
        
        // 更新统计信息
        function updateStats() {
            // 模拟实时数据更新
            fetch('/api/v2/brain/status')
                .then(response => response.json())
                .then(data => {
                    // 更新统计数据
                })
                .catch(error => console.error('Stats update error:', error));
        }
        
        function updateProcessedCount() {
            processedCountEl.textContent = messageCount;
        }
        
        function updateResponseTime(time) {
            if (time) {
                responseTimeEl.textContent = time;
            }
        }
        
        // 添加最近操作
        function addRecentOperation(message, intentType) {
            const recentOps = document.getElementById('recentOperations');
            const opDiv = document.createElement('div');
            opDiv.className = 'text-white text-sm';
            opDiv.innerHTML = `
                <div class="flex justify-between items-center">
                    <span class="truncate">${message.substring(0, 20)}...</span>
                    <span class="text-xs opacity-60">${new Date().toLocaleTimeString()}</span>
                </div>
                <div class="text-xs opacity-60">${intentType || '通用对话'}</div>
            `;
            
            recentOps.insertBefore(opDiv, recentOps.firstChild);
            
            // 保持最多5条记录
            const ops = recentOps.children;
            if (ops.length > 5) {
                recentOps.removeChild(ops[ops.length - 1]);
            }
        }
        
        // 语音输入（占位符）
        function toggleVoiceInput() {
            alert('语音输入功能开发中...');
        }
    </script>
</body>
</html>
