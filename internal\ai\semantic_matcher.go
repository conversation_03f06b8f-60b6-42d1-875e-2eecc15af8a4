package ai

import (
	"math"
	"sort"
	"strings"
	"unicode"

	"github.com/sirupsen/logrus"
)

// SemanticMatcher 语义匹配器
type SemanticMatcher struct {
	vocabulary    map[string]int       // 词汇表
	idfScores     map[string]float64   // IDF分数
	intentVectors map[string][]float64 // 意图向量
	logger        *logrus.Logger
}

// SemanticResult 语义匹配结果
type SemanticResult struct {
	Intent     string   `json:"intent"`
	Similarity float64  `json:"similarity"`
	Confidence float64  `json:"confidence"`
	Keywords   []string `json:"keywords"`
}

// NewSemanticMatcher 创建语义匹配器
func NewSemanticMatcher(logger *logrus.Logger) *SemanticMatcher {
	matcher := &SemanticMatcher{
		vocabulary:    make(map[string]int),
		idfScores:     make(map[string]float64),
		intentVectors: make(map[string][]float64),
		logger:        logger,
	}

	// 初始化运维领域词汇库
	matcher.initializeVocabulary()
	matcher.buildIntentVectors()

	return matcher
}

// initializeVocabulary 初始化运维领域词汇库
func (sm *SemanticMatcher) initializeVocabulary() {
	// 运维核心词汇
	coreWords := []string{
		// 动作词汇
		"连接", "登录", "执行", "运行", "启动", "停止", "重启", "检查", "查看", "显示", "获取", "监控",
		"分析", "诊断", "排查", "修复", "配置", "安装", "卸载", "更新", "升级", "备份", "恢复",
		"部署", "发布", "回滚", "测试", "验证", "清理", "优化", "调试", "跟踪", "扫描",

		// 目标对象
		"主机", "服务器", "机器", "节点", "实例", "容器", "服务", "进程", "应用", "系统",
		"网络", "存储", "数据库", "缓存", "队列", "负载均衡", "防火墙", "代理", "网关",

		// 系统组件
		"cpu", "内存", "磁盘", "网卡", "端口", "文件", "目录", "日志", "配置", "脚本",
		"命令", "参数", "选项", "环境", "变量", "路径", "权限", "用户", "组", "密码",

		// 状态描述
		"正常", "异常", "错误", "故障", "宕机", "超时", "阻塞", "繁忙", "空闲", "活跃",
		"在线", "离线", "可用", "不可用", "健康", "不健康", "稳定", "不稳定",

		// 性能指标
		"使用率", "负载", "吞吐量", "延迟", "响应时间", "并发", "连接数", "请求数",
		"错误率", "成功率", "可用性", "性能", "效率", "速度", "带宽", "流量",

		// 运维工具
		"nginx", "apache", "mysql", "redis", "docker", "kubernetes", "jenkins", "git",
		"ssh", "ftp", "http", "https", "tcp", "udp", "dns", "dhcp", "ntp", "snmp",

		// 时间相关
		"现在", "当前", "最近", "刚才", "之前", "今天", "昨天", "小时", "分钟", "秒",
		"实时", "定时", "周期", "间隔", "频率", "持续", "临时", "永久",

		// 数量和范围
		"所有", "全部", "部分", "单个", "多个", "批量", "一台", "几台", "这台", "那台",
		"第一", "最后", "下一个", "上一个", "其他", "剩余", "额外",

		// 逻辑关系
		"和", "或", "但是", "如果", "那么", "因为", "所以", "除了", "包括", "排除",
		"同时", "然后", "接着", "最后", "首先", "其次", "另外", "此外",
	}

	// 构建词汇表
	for i, word := range coreWords {
		sm.vocabulary[word] = i
	}

	// 计算IDF分数（简化版本）
	totalDocs := float64(len(coreWords))
	for word := range sm.vocabulary {
		// 简化的IDF计算，实际应该基于文档频率
		sm.idfScores[word] = math.Log(totalDocs / (1.0 + 1.0))
	}
}

// buildIntentVectors 构建意图向量
func (sm *SemanticMatcher) buildIntentVectors() {
	// 定义意图的特征词汇
	intentFeatures := map[string][]string{
		"host_connect": {
			"连接", "登录", "ssh", "主机", "服务器", "机器", "进入", "访问",
		},
		"host_add": {
			"添加", "新增", "创建", "注册", "主机", "服务器", "机器", "接入", "配置",
		},
		"host_list": {
			"列出", "显示", "查看", "获取", "主机", "服务器", "列表", "清单", "所有",
		},
		"command_execute": {
			"执行", "运行", "启动", "命令", "脚本", "操作", "处理", "调用",
		},
		"monitoring_status": {
			"监控", "状态", "健康", "性能", "指标", "数据", "检查", "查看", "cpu", "内存", "磁盘",
		},
		"troubleshoot_start": {
			"问题", "故障", "异常", "错误", "排查", "诊断", "修复", "解决", "分析",
		},
		"service_management": {
			"服务", "启动", "停止", "重启", "nginx", "apache", "mysql", "redis", "管理",
		},
		"log_analysis": {
			"日志", "记录", "分析", "查看", "错误", "异常", "跟踪", "调试",
		},
		"workflow_create": {
			"工作流", "自动化", "批量", "流程", "任务", "创建", "执行", "调度",
		},
		"help_general": {
			"帮助", "help", "怎么", "如何", "什么", "能做", "功能", "使用",
		},
	}

	// 为每个意图构建TF-IDF向量
	vocabSize := len(sm.vocabulary)
	for intent, features := range intentFeatures {
		vector := make([]float64, vocabSize)

		// 计算词频
		wordCount := make(map[string]int)
		for _, word := range features {
			wordCount[word]++
		}

		// 构建TF-IDF向量
		for word, count := range wordCount {
			if idx, exists := sm.vocabulary[word]; exists {
				tf := float64(count) / float64(len(features))
				idf := sm.idfScores[word]
				vector[idx] = tf * idf
			}
		}

		// 归一化向量
		sm.intentVectors[intent] = sm.normalizeVector(vector)
	}
}

// MatchIntent 匹配意图
func (sm *SemanticMatcher) MatchIntent(text string) []*SemanticResult {
	// 文本预处理和向量化
	textVector := sm.textToVector(text)

	// 计算与所有意图的相似度
	results := make([]*SemanticResult, 0)
	for intent, intentVector := range sm.intentVectors {
		similarity := sm.cosineSimilarity(textVector, intentVector)
		confidence := sm.calculateConfidence(similarity, text, intent)

		if similarity > 0.1 { // 过滤低相似度结果
			result := &SemanticResult{
				Intent:     intent,
				Similarity: similarity,
				Confidence: confidence,
				Keywords:   sm.extractKeywords(text, intent),
			}
			results = append(results, result)
		}
	}

	// 按相似度排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Similarity > results[j].Similarity
	})

	return results
}

// textToVector 将文本转换为向量
func (sm *SemanticMatcher) textToVector(text string) []float64 {
	words := sm.tokenize(text)
	vocabSize := len(sm.vocabulary)
	vector := make([]float64, vocabSize)

	// 计算词频
	wordCount := make(map[string]int)
	for _, word := range words {
		wordCount[word]++
	}

	// 构建TF-IDF向量
	for word, count := range wordCount {
		if idx, exists := sm.vocabulary[word]; exists {
			tf := float64(count) / float64(len(words))
			idf := sm.idfScores[word]
			vector[idx] = tf * idf
		}
	}

	return sm.normalizeVector(vector)
}

// tokenize 分词
func (sm *SemanticMatcher) tokenize(text string) []string {
	// 简化的中文分词
	text = strings.ToLower(text)
	words := make([]string, 0)

	// 移除标点符号并分割
	var currentWord strings.Builder
	for _, r := range text {
		if unicode.IsLetter(r) || unicode.IsDigit(r) {
			currentWord.WriteRune(r)
		} else {
			if currentWord.Len() > 0 {
				word := currentWord.String()
				if len(word) > 1 { // 过滤单字符
					words = append(words, word)
				}
				currentWord.Reset()
			}
		}
	}

	if currentWord.Len() > 0 {
		word := currentWord.String()
		if len(word) > 1 {
			words = append(words, word)
		}
	}

	// 添加中文词汇识别
	words = append(words, sm.extractChineseWords(text)...)

	return words
}

// extractChineseWords 提取中文词汇
func (sm *SemanticMatcher) extractChineseWords(text string) []string {
	words := make([]string, 0)

	// 检查词汇表中的词汇
	for word := range sm.vocabulary {
		if strings.Contains(text, word) {
			words = append(words, word)
		}
	}

	return words
}

// cosineSimilarity 计算余弦相似度
func (sm *SemanticMatcher) cosineSimilarity(vec1, vec2 []float64) float64 {
	if len(vec1) != len(vec2) {
		return 0.0
	}

	var dotProduct, norm1, norm2 float64
	for i := 0; i < len(vec1); i++ {
		dotProduct += vec1[i] * vec2[i]
		norm1 += vec1[i] * vec1[i]
		norm2 += vec2[i] * vec2[i]
	}

	if norm1 == 0 || norm2 == 0 {
		return 0.0
	}

	return dotProduct / (math.Sqrt(norm1) * math.Sqrt(norm2))
}

// normalizeVector 归一化向量
func (sm *SemanticMatcher) normalizeVector(vector []float64) []float64 {
	var norm float64
	for _, val := range vector {
		norm += val * val
	}
	norm = math.Sqrt(norm)

	if norm == 0 {
		return vector
	}

	normalized := make([]float64, len(vector))
	for i, val := range vector {
		normalized[i] = val / norm
	}

	return normalized
}

// calculateConfidence 计算置信度
func (sm *SemanticMatcher) calculateConfidence(similarity float64, text, intent string) float64 {
	// 基础置信度基于相似度
	confidence := similarity

	// 根据文本长度调整
	textLength := float64(len(strings.Fields(text)))
	if textLength > 0 {
		lengthFactor := math.Min(textLength/5.0, 1.0) // 5个词以上认为是完整表达
		confidence *= (0.7 + 0.3*lengthFactor)
	}

	// 根据关键词密度调整
	keywords := sm.extractKeywords(text, intent)
	if len(keywords) > 0 {
		keywordDensity := float64(len(keywords)) / textLength
		confidence *= (0.8 + 0.2*math.Min(keywordDensity*2, 1.0))
	}

	return math.Min(confidence, 1.0)
}

// extractKeywords 提取关键词
func (sm *SemanticMatcher) extractKeywords(text, intent string) []string {
	words := sm.tokenize(text)
	keywords := make([]string, 0)

	// 检查哪些词在词汇表中
	for _, word := range words {
		if _, exists := sm.vocabulary[word]; exists {
			keywords = append(keywords, word)
		}
	}

	return keywords
}

// UpdateIntentVector 更新意图向量（用于在线学习）
func (sm *SemanticMatcher) UpdateIntentVector(intent string, positiveExamples, negativeExamples []string) {
	// 基于正负样本更新意图向量
	vocabSize := len(sm.vocabulary)
	newVector := make([]float64, vocabSize)

	// 处理正样本
	for _, example := range positiveExamples {
		exampleVector := sm.textToVector(example)
		for i, val := range exampleVector {
			newVector[i] += val * 0.8 // 正样本权重
		}
	}

	// 处理负样本
	for _, example := range negativeExamples {
		exampleVector := sm.textToVector(example)
		for i, val := range exampleVector {
			newVector[i] -= val * 0.2 // 负样本权重
		}
	}

	// 与原向量融合
	if originalVector, exists := sm.intentVectors[intent]; exists {
		for i := range newVector {
			newVector[i] = 0.7*originalVector[i] + 0.3*newVector[i]
		}
	}

	sm.intentVectors[intent] = sm.normalizeVector(newVector)

	sm.logger.WithFields(logrus.Fields{
		"intent":           intent,
		"positive_samples": len(positiveExamples),
		"negative_samples": len(negativeExamples),
	}).Info("Intent vector updated")
}
