package ai

import (
	"context"
	"fmt"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// SimpleExecutionEngine 简单执行引擎
type SimpleExecutionEngine struct {
	db          *gorm.DB
	hostService interface{} // 主机服务接口
	logger      *logrus.Logger
}

// SimpleExecutionResult 简单执行结果
type SimpleExecutionResult struct {
	Success   bool                   `json:"success"`
	Message   string                 `json:"message"`
	Data      map[string]interface{} `json:"data"`
	Error     string                 `json:"error,omitempty"`
	Timestamp time.Time              `json:"timestamp"`
}

// NewSimpleExecutionEngine 创建简单执行引擎
func NewSimpleExecutionEngine(db *gorm.DB, hostService interface{}, logger *logrus.Logger) *SimpleExecutionEngine {
	return &SimpleExecutionEngine{
		db:          db,
		hostService: hostService,
		logger:      logger,
	}
}

// NewSimpleExecutionEngineFromNew 别名函数，避免冲突
func NewSimpleExecutionEngineFromNew(db *gorm.DB, hostService interface{}, logger *logrus.Logger) *SimpleExecutionEngine {
	return NewSimpleExecutionEngine(db, hostService, logger)
}

// Execute 执行意图
func (see *SimpleExecutionEngine) Execute(ctx context.Context, intent *SimpleIntentResult, userID int64) (*SimpleExecutionResult, error) {
	see.logger.WithFields(logrus.Fields{
		"intent_type": intent.Type,
		"command":     intent.Command,
		"user_id":     userID,
	}).Info("开始执行意图")

	switch intent.Type {
	case "database_operations":
		return see.executeDatabaseOperation(ctx, intent, userID)
	case "ssh_operations":
		return see.executeSSHOperation(ctx, intent, userID)
	case "monitoring_operations":
		return see.executeMonitoringOperation(ctx, intent, userID)
	case "general_chat":
		return see.executeGeneralChat(ctx, intent, userID)
	default:
		return &SimpleExecutionResult{
			Success:   false,
			Message:   fmt.Sprintf("不支持的意图类型: %s", intent.Type),
			Error:     "unsupported_intent_type",
			Timestamp: time.Now(),
		}, nil
	}
}

// executeDatabaseOperation 执行数据库操作
func (see *SimpleExecutionEngine) executeDatabaseOperation(ctx context.Context, intent *SimpleIntentResult, userID int64) (*SimpleExecutionResult, error) {
	operation, _ := intent.Parameters["operation"].(string)
	table, _ := intent.Parameters["table"].(string)

	see.logger.WithFields(logrus.Fields{
		"operation": operation,
		"table":     table,
	}).Info("执行数据库操作")

	switch operation {
	case "select":
		return see.handleSelect(ctx, intent, userID)
	case "insert":
		return see.handleInsert(ctx, intent, userID)
	case "delete":
		return see.handleDelete(ctx, intent, userID)
	default:
		return &SimpleExecutionResult{
			Success:   false,
			Message:   fmt.Sprintf("不支持的数据库操作: %s", operation),
			Error:     "unsupported_db_operation",
			Timestamp: time.Now(),
		}, nil
	}
}

// handleSelect 处理查询操作
func (see *SimpleExecutionEngine) handleSelect(ctx context.Context, intent *SimpleIntentResult, userID int64) (*SimpleExecutionResult, error) {
	table, _ := intent.Parameters["table"].(string)

	if table == "hosts" {
		return see.selectHosts(ctx)
	}

	return &SimpleExecutionResult{
		Success:   false,
		Message:   fmt.Sprintf("不支持查询表: %s", table),
		Error:     "unsupported_table",
		Timestamp: time.Now(),
	}, nil
}

// selectHosts 查询主机列表
func (see *SimpleExecutionEngine) selectHosts(ctx context.Context) (*SimpleExecutionResult, error) {
	var hosts []model.Host
	result := see.db.WithContext(ctx).Find(&hosts)
	if result.Error != nil {
		see.logger.WithError(result.Error).Error("查询主机列表失败")
		return &SimpleExecutionResult{
			Success:   false,
			Message:   "查询主机列表失败",
			Error:     result.Error.Error(),
			Timestamp: time.Now(),
		}, nil
	}

	// 格式化主机列表
	content := see.formatHostList(hosts)

	return &SimpleExecutionResult{
		Success: true,
		Message: content,
		Data: map[string]interface{}{
			"hosts": hosts,
			"count": len(hosts),
		},
		Timestamp: time.Now(),
	}, nil
}

// formatHostList 格式化主机列表
func (see *SimpleExecutionEngine) formatHostList(hosts []model.Host) string {
	if len(hosts) == 0 {
		return `📊 **主机列表查询结果**

🔍 **查询状态**: 成功
📈 **主机数量**: 0

暂无主机数据。

💡 **建议操作**:
• 添加新主机：输入 "添加主机 [IP] [用户名] [密码]"
• 例如：添加主机 ************* root mypassword`
	}

	content := fmt.Sprintf(`📊 **主机列表查询结果**

🔍 **查询状态**: 成功
📈 **主机数量**: %d

| 序号 | 主机名称 | IP地址 | 端口 | 状态 | 环境 |
|------|----------|--------|------|------|------|
`, len(hosts))

	for i, host := range hosts {
		status := "🔴 离线"
		if host.Status == "online" {
			status = "🟢 在线"
		} else if host.Status == "connecting" {
			status = "🟡 连接中"
		}

		environment := host.Environment
		if environment == "" {
			environment = "未设置"
		}

		content += fmt.Sprintf("| %d | %s | %s | %d | %s | %s |\n",
			i+1,
			host.Name,
			host.IPAddress,
			host.Port,
			status,
			environment,
		)
	}

	content += fmt.Sprintf(`
💡 **可用操作**:
• 查看主机详情：输入 "查看主机 [主机名]"
• 添加新主机：输入 "添加主机 [IP] [用户名] [密码]"
• 删除主机：输入 "删除主机 [IP]"

⏱️ **查询时间**: %s`, time.Now().Format("2006-01-02 15:04:05"))

	return content
}

// executeSSHOperation 执行SSH操作
func (see *SimpleExecutionEngine) executeSSHOperation(ctx context.Context, intent *SimpleIntentResult, userID int64) (*SimpleExecutionResult, error) {
	return &SimpleExecutionResult{
		Success:   false,
		Message:   "SSH操作功能正在开发中",
		Error:     "not_implemented",
		Timestamp: time.Now(),
	}, nil
}

// executeMonitoringOperation 执行监控操作
func (see *SimpleExecutionEngine) executeMonitoringOperation(ctx context.Context, intent *SimpleIntentResult, userID int64) (*SimpleExecutionResult, error) {
	return &SimpleExecutionResult{
		Success:   false,
		Message:   "监控操作功能正在开发中",
		Error:     "not_implemented",
		Timestamp: time.Now(),
	}, nil
}

// executeGeneralChat 执行通用对话
func (see *SimpleExecutionEngine) executeGeneralChat(ctx context.Context, intent *SimpleIntentResult, userID int64) (*SimpleExecutionResult, error) {
	intentType, _ := intent.Parameters["intent"].(string)

	switch intentType {
	case "help":
		return see.handleHelp()
	case "greeting":
		return see.handleGreeting()
	default:
		return see.handleGeneral(intent)
	}
}

// handleHelp 处理帮助
func (see *SimpleExecutionEngine) handleHelp() (*SimpleExecutionResult, error) {
	content := `🤖 **AI运维助手帮助信息**

我可以帮您完成以下操作：

📊 **主机管理**
• 查看主机列表：列出主机 / 查看主机
• 添加主机：添加主机 ************* root password123
• 删除主机：删除主机 *************

🔧 **SSH操作** (开发中)
• 执行命令：在*************上执行ps aux命令

📈 **监控操作** (开发中)
• 检查状态：检查*************主机状态

💬 **通用对话**
• 问候：你好 / hello
• 帮助：帮助 / help

如需具体帮助，请直接描述您想要执行的操作！`

	return &SimpleExecutionResult{
		Success:   true,
		Message:   content,
		Timestamp: time.Now(),
	}, nil
}

// handleGreeting 处理问候
func (see *SimpleExecutionEngine) handleGreeting() (*SimpleExecutionResult, error) {
	return &SimpleExecutionResult{
		Success:   true,
		Message:   "您好！我是AI运维助手，可以帮您管理主机、执行命令、监控系统等。输入'帮助'查看可用功能。",
		Timestamp: time.Now(),
	}, nil
}

// handleGeneral 处理通用对话
func (see *SimpleExecutionEngine) handleGeneral(intent *SimpleIntentResult) (*SimpleExecutionResult, error) {
	message, _ := intent.Parameters["message"].(string)
	return &SimpleExecutionResult{
		Success:   true,
		Message:   fmt.Sprintf("我收到了您的消息：%s\n\n如需帮助，请输入'帮助'查看可用功能。", message),
		Timestamp: time.Now(),
	}, nil
}

// handleInsert 处理插入操作
func (see *SimpleExecutionEngine) handleInsert(ctx context.Context, intent *SimpleIntentResult, userID int64) (*SimpleExecutionResult, error) {
	table, _ := intent.Parameters["table"].(string)

	if table == "hosts" {
		return see.insertHost(ctx, intent, userID)
	}

	return &SimpleExecutionResult{
		Success:   false,
		Message:   fmt.Sprintf("不支持插入表: %s", table),
		Error:     "unsupported_table",
		Timestamp: time.Now(),
	}, nil
}

// insertHost 插入主机
func (see *SimpleExecutionEngine) insertHost(ctx context.Context, intent *SimpleIntentResult, userID int64) (*SimpleExecutionResult, error) {
	ipAddress, hasIP := intent.Parameters["ip_address"].(string)
	username, hasUsername := intent.Parameters["username"].(string)
	password, hasPassword := intent.Parameters["password"].(string)

	if !hasIP {
		return &SimpleExecutionResult{
			Success:   false,
			Message:   "缺少必要参数：IP地址",
			Error:     "missing_ip_address",
			Timestamp: time.Now(),
		}, nil
	}

	// 检查主机是否已存在
	var existingHost model.Host
	result := see.db.WithContext(ctx).Where("ip_address = ?", ipAddress).First(&existingHost)
	if result.Error == nil {
		return &SimpleExecutionResult{
			Success:   false,
			Message:   fmt.Sprintf("主机 %s 已存在", ipAddress),
			Error:     "host_already_exists",
			Timestamp: time.Now(),
		}, nil
	}

	// 创建新主机
	host := model.Host{
		Name:        fmt.Sprintf("host-%s", ipAddress),
		IPAddress:   ipAddress,
		Port:        22,
		Status:      "unknown",
		Environment: "development",
		CreatedBy:   userID,
	}

	if hasUsername {
		host.Username = username
	}
	if hasPassword {
		host.PasswordEncrypted = password // 注意：实际应用中需要加密
	}

	result = see.db.WithContext(ctx).Create(&host)
	if result.Error != nil {
		see.logger.WithError(result.Error).Error("创建主机失败")
		return &SimpleExecutionResult{
			Success:   false,
			Message:   "创建主机失败",
			Error:     result.Error.Error(),
			Timestamp: time.Now(),
		}, nil
	}

	return &SimpleExecutionResult{
		Success: true,
		Message: fmt.Sprintf("✅ 主机 %s 添加成功！\n\n📋 主机信息:\n• IP地址: %s\n• 用户名: %s\n• 端口: %d\n• 状态: %s",
			ipAddress, ipAddress, username, host.Port, host.Status),
		Data: map[string]interface{}{
			"host_id":    host.ID,
			"ip_address": ipAddress,
		},
		Timestamp: time.Now(),
	}, nil
}

// handleDelete 处理删除操作
func (see *SimpleExecutionEngine) handleDelete(ctx context.Context, intent *SimpleIntentResult, userID int64) (*SimpleExecutionResult, error) {
	table, _ := intent.Parameters["table"].(string)

	if table == "hosts" {
		return see.deleteHost(ctx, intent, userID)
	}

	return &SimpleExecutionResult{
		Success:   false,
		Message:   fmt.Sprintf("不支持删除表: %s", table),
		Error:     "unsupported_table",
		Timestamp: time.Now(),
	}, nil
}

// deleteHost 删除主机
func (see *SimpleExecutionEngine) deleteHost(ctx context.Context, intent *SimpleIntentResult, userID int64) (*SimpleExecutionResult, error) {
	ipAddress, hasIP := intent.Parameters["ip_address"].(string)

	if !hasIP {
		return &SimpleExecutionResult{
			Success:   false,
			Message:   "缺少必要参数：IP地址",
			Error:     "missing_ip_address",
			Timestamp: time.Now(),
		}, nil
	}

	// 查找主机
	var host model.Host
	result := see.db.WithContext(ctx).Where("ip_address = ?", ipAddress).First(&host)
	if result.Error != nil {
		return &SimpleExecutionResult{
			Success:   false,
			Message:   fmt.Sprintf("未找到主机 %s", ipAddress),
			Error:     "host_not_found",
			Timestamp: time.Now(),
		}, nil
	}

	// 删除主机
	result = see.db.WithContext(ctx).Delete(&host)
	if result.Error != nil {
		see.logger.WithError(result.Error).Error("删除主机失败")
		return &SimpleExecutionResult{
			Success:   false,
			Message:   "删除主机失败",
			Error:     result.Error.Error(),
			Timestamp: time.Now(),
		}, nil
	}

	return &SimpleExecutionResult{
		Success: true,
		Message: fmt.Sprintf("✅ 主机 %s (%s) 删除成功！", host.Name, ipAddress),
		Data: map[string]interface{}{
			"deleted_host_id": host.ID,
			"ip_address":      ipAddress,
		},
		Timestamp: time.Now(),
	}, nil
}
