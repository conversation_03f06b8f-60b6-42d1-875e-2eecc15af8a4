package main

import (
	"fmt"
	"log"
	"os"
	"strings"
	"time"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

func main() {
	fmt.Println("🚀 AI运维管理平台数据库优化工具")
	fmt.Println("=" + strings.Repeat("=", 50))

	// 连接数据库
	db, err := gorm.Open(sqlite.Open("aiops.db"), &gorm.Config{
		Logger: logger.Default.LogMode(logger.Info),
	})
	if err != nil {
		log.Fatal("❌ 数据库连接失败:", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 读取优化脚本
	sqlFile := "scripts/optimize_database.sql"
	content, err := os.ReadFile(sqlFile)
	if err != nil {
		log.Fatal("❌ 读取SQL文件失败:", err)
	}

	// 分割SQL语句
	statements := strings.Split(string(content), ";")
	
	fmt.Printf("📝 准备执行 %d 条SQL语句\n", len(statements))
	fmt.Println("\n🔧 开始数据库优化...")

	successCount := 0
	errorCount := 0

	for i, stmt := range statements {
		stmt = strings.TrimSpace(stmt)
		if stmt == "" || strings.HasPrefix(stmt, "--") {
			continue
		}

		fmt.Printf("  [%d] 执行: %s...\n", i+1, truncateSQL(stmt, 50))
		
		start := time.Now()
		err := db.Exec(stmt).Error
		duration := time.Since(start)

		if err != nil {
			fmt.Printf("    ❌ 失败 (%v): %v\n", duration, err)
			errorCount++
		} else {
			fmt.Printf("    ✅ 成功 (%v)\n", duration)
			successCount++
		}
	}

	fmt.Println("\n📊 优化结果统计:")
	fmt.Printf("  ✅ 成功: %d 条\n", successCount)
	fmt.Printf("  ❌ 失败: %d 条\n", errorCount)

	// 显示优化后的数据库信息
	fmt.Println("\n📈 优化后数据库信息:")
	showDatabaseInfo(db)

	// 性能测试
	fmt.Println("\n⚡ 性能测试:")
	runPerformanceTest(db)

	fmt.Println("\n🎉 数据库优化完成!")
}

// truncateSQL 截断SQL语句用于显示
func truncateSQL(sql string, maxLen int) string {
	sql = strings.ReplaceAll(sql, "\n", " ")
	sql = strings.ReplaceAll(sql, "\t", " ")
	
	// 移除多余空格
	for strings.Contains(sql, "  ") {
		sql = strings.ReplaceAll(sql, "  ", " ")
	}
	
	if len(sql) > maxLen {
		return sql[:maxLen] + "..."
	}
	return sql
}

// showDatabaseInfo 显示数据库信息
func showDatabaseInfo(db *gorm.DB) {
	// 显示索引信息
	var indexes []struct {
		IndexName string `gorm:"column:index_name"`
		TableName string `gorm:"column:table_name"`
	}

	err := db.Raw(`
		SELECT 
			name as index_name,
			tbl_name as table_name
		FROM sqlite_master 
		WHERE type = 'index' 
			AND name NOT LIKE 'sqlite_%'
		ORDER BY tbl_name, name
	`).Scan(&indexes).Error

	if err != nil {
		fmt.Printf("  ❌ 查询索引失败: %v\n", err)
		return
	}

	fmt.Printf("  📋 索引总数: %d\n", len(indexes))
	
	// 按表分组显示索引
	tableIndexes := make(map[string][]string)
	for _, idx := range indexes {
		tableIndexes[idx.TableName] = append(tableIndexes[idx.TableName], idx.IndexName)
	}

	for table, idxList := range tableIndexes {
		fmt.Printf("    %s: %d 个索引\n", table, len(idxList))
	}

	// 显示表行数
	tables := []string{"users", "hosts", "alerts", "operation_logs", "chat_sessions", "chat_messages"}
	fmt.Println("  📊 表数据统计:")
	
	totalRows := int64(0)
	for _, table := range tables {
		var count int64
		if err := db.Raw(fmt.Sprintf("SELECT COUNT(*) FROM %s", table)).Scan(&count).Error; err != nil {
			fmt.Printf("    %s: 查询失败\n", table)
			continue
		}
		fmt.Printf("    %s: %d 行\n", table, count)
		totalRows += count
	}
	fmt.Printf("  📈 总数据量: %d 行\n", totalRows)
}

// runPerformanceTest 运行性能测试
func runPerformanceTest(db *gorm.DB) {
	testQueries := []struct {
		name  string
		query string
	}{
		{"主机状态查询", "SELECT COUNT(*) FROM hosts WHERE status = 'active'"},
		{"用户角色查询", "SELECT COUNT(*) FROM users WHERE role = 'admin'"},
		{"最近告警查询", "SELECT COUNT(*) FROM alerts WHERE created_at > datetime('now', '-1 day')"},
		{"操作日志查询", "SELECT COUNT(*) FROM operation_logs WHERE created_at > datetime('now', '-1 hour')"},
		{"会话消息查询", "SELECT COUNT(*) FROM chat_messages WHERE session_id = 1"},
	}

	for _, test := range testQueries {
		start := time.Now()
		var count int64
		err := db.Raw(test.query).Scan(&count).Error
		duration := time.Since(start)

		if err != nil {
			fmt.Printf("  ❌ %s: 失败 (%v)\n", test.name, err)
		} else {
			fmt.Printf("  ✅ %s: %v (%d 条记录)\n", test.name, duration, count)
		}
	}
}
