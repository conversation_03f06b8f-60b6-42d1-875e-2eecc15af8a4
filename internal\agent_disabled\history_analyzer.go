package agent

import (
	"context"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// HistoryAnalyzer 历史分析器
type HistoryAnalyzer struct {
	logger *logrus.Logger
}

// NewHistoryAnalyzer 创建历史分析器
func NewHistoryAnalyzer(logger *logrus.Logger) *HistoryAnalyzer {
	return &HistoryAnalyzer{
		logger: logger,
	}
}

// Analyze 分析命令历史
func (ha *HistoryAnalyzer) Analyze(ctx context.Context, command string, assessmentContext *AssessmentContext) *HistoryAnalysis {
	analysis := &HistoryAnalysis{
		FailurePatterns:     make([]string, 0),
		SimilarCommands:     make([]string, 0),
		HistoricalIncidents: make([]string, 0),
	}

	// 分析命令频率
	analysis.CommandFrequency = ha.analyzeCommandFrequency(command, assessmentContext)

	// 分析最后执行时间
	analysis.LastExecuted = ha.analyzeLastExecuted(command, assessmentContext)

	// 分析成功率
	analysis.SuccessRate = ha.analyzeSuccessRate(command, assessmentContext)

	// 分析失败模式
	analysis.FailurePatterns = ha.analyzeFailurePatterns(command, assessmentContext)

	// 分析用户经验
	analysis.UserExperience = ha.analyzeUserExperience(assessmentContext)

	// 分析相似命令
	analysis.SimilarCommands = ha.analyzeSimilarCommands(command, assessmentContext)

	// 分析历史事件
	analysis.HistoricalIncidents = ha.analyzeHistoricalIncidents(command, assessmentContext)

	return analysis
}

// analyzeCommandFrequency 分析命令频率
func (ha *HistoryAnalyzer) analyzeCommandFrequency(command string, assessmentContext *AssessmentContext) int {
	if assessmentContext == nil || len(assessmentContext.PreviousCommands) == 0 {
		return 0
	}

	frequency := 0
	for _, prevCmd := range assessmentContext.PreviousCommands {
		if strings.HasPrefix(prevCmd, command) {
			frequency++
		}
	}

	return frequency
}

// analyzeLastExecuted 分析最后执行时间
func (ha *HistoryAnalyzer) analyzeLastExecuted(command string, assessmentContext *AssessmentContext) *time.Time {
	// 简化实现：假设最近的命令是最后执行的
	if assessmentContext != nil && len(assessmentContext.PreviousCommands) > 0 {
		for i := len(assessmentContext.PreviousCommands) - 1; i >= 0; i-- {
			if strings.HasPrefix(assessmentContext.PreviousCommands[i], command) {
				// 假设是最近执行的
				lastTime := time.Now().Add(-time.Duration(len(assessmentContext.PreviousCommands)-i) * time.Minute)
				return &lastTime
			}
		}
	}

	return nil
}

// analyzeSuccessRate 分析成功率
func (ha *HistoryAnalyzer) analyzeSuccessRate(command string, assessmentContext *AssessmentContext) float64 {
	// 简化实现：基于命令类型估算成功率
	commandSuccessRates := map[string]float64{
		"ls":        0.99,
		"cd":        0.95,
		"pwd":       0.99,
		"cat":       0.95,
		"grep":      0.90,
		"find":      0.85,
		"ps":        0.95,
		"top":       0.95,
		"df":        0.95,
		"du":        0.90,
		"chmod":     0.85,
		"chown":     0.85,
		"cp":        0.90,
		"mv":        0.85,
		"rm":        0.80,
		"mkdir":     0.90,
		"rmdir":     0.85,
		"tar":       0.80,
		"zip":       0.85,
		"unzip":     0.85,
		"wget":      0.75,
		"curl":      0.80,
		"ssh":       0.70,
		"scp":       0.75,
		"rsync":     0.80,
		"systemctl": 0.85,
		"service":   0.85,
		"mount":     0.75,
		"umount":    0.80,
		"fdisk":     0.60,
		"mkfs":      0.65,
		"dd":        0.70,
	}

	if rate, exists := commandSuccessRates[command]; exists {
		return rate
	}

	// 默认成功率
	return 0.80
}

// analyzeFailurePatterns 分析失败模式
func (ha *HistoryAnalyzer) analyzeFailurePatterns(command string, assessmentContext *AssessmentContext) []string {
	patterns := make([]string, 0)

	// 基于命令类型预测可能的失败模式
	failurePatterns := map[string][]string{
		"rm": {
			"文件不存在",
			"权限不足",
			"文件正在使用中",
			"只读文件系统",
		},
		"cp": {
			"目标目录不存在",
			"磁盘空间不足",
			"权限不足",
			"文件已存在",
		},
		"mv": {
			"目标目录不存在",
			"权限不足",
			"跨文件系统移动",
			"文件正在使用中",
		},
		"chmod": {
			"文件不存在",
			"权限不足",
			"无效的权限模式",
		},
		"chown": {
			"文件不存在",
			"权限不足",
			"用户不存在",
			"组不存在",
		},
		"mount": {
			"设备不存在",
			"挂载点不存在",
			"文件系统类型不支持",
			"设备已挂载",
			"权限不足",
		},
		"umount": {
			"设备未挂载",
			"设备正在使用中",
			"权限不足",
		},
		"systemctl": {
			"服务不存在",
			"权限不足",
			"服务依赖问题",
			"配置文件错误",
		},
		"ssh": {
			"连接超时",
			"认证失败",
			"主机不可达",
			"端口被拒绝",
		},
		"wget": {
			"网络连接失败",
			"URL不存在",
			"磁盘空间不足",
			"权限不足",
		},
		"tar": {
			"文件不存在",
			"磁盘空间不足",
			"权限不足",
			"压缩格式不支持",
		},
	}

	if cmdPatterns, exists := failurePatterns[command]; exists {
		patterns = append(patterns, cmdPatterns...)
	}

	return patterns
}

// analyzeUserExperience 分析用户经验
func (ha *HistoryAnalyzer) analyzeUserExperience(assessmentContext *AssessmentContext) string {
	if assessmentContext == nil {
		return "unknown"
	}

	// 基于历史命令数量和复杂度评估用户经验
	commandCount := len(assessmentContext.PreviousCommands)

	if commandCount == 0 {
		return "novice"
	}

	// 统计高级命令使用情况
	advancedCommands := []string{
		"awk", "sed", "grep -E", "find.*-exec", "xargs",
		"systemctl", "service", "mount", "umount", "fdisk",
		"iptables", "ssh", "scp", "rsync", "tar.*z",
		"dd", "mkfs", "fsck", "crontab", "at",
	}

	advancedCount := 0
	for _, prevCmd := range assessmentContext.PreviousCommands {
		for _, advCmd := range advancedCommands {
			if strings.Contains(prevCmd, advCmd) {
				advancedCount++
				break
			}
		}
	}

	advancedRatio := float64(advancedCount) / float64(commandCount)

	if commandCount >= 100 && advancedRatio >= 0.3 {
		return "expert"
	} else if commandCount >= 50 && advancedRatio >= 0.2 {
		return "intermediate"
	} else if commandCount >= 20 && advancedRatio >= 0.1 {
		return "intermediate"
	}

	return "novice"
}

// analyzeSimilarCommands 分析相似命令
func (ha *HistoryAnalyzer) analyzeSimilarCommands(command string, assessmentContext *AssessmentContext) []string {
	similar := make([]string, 0)

	if assessmentContext == nil || len(assessmentContext.PreviousCommands) == 0 {
		return similar
	}

	// 查找相似的命令
	for _, prevCmd := range assessmentContext.PreviousCommands {
		if ha.isSimilarCommand(command, prevCmd) {
			similar = append(similar, prevCmd)
		}
	}

	// 去重并限制数量
	similar = ha.removeDuplicates(similar)
	if len(similar) > 5 {
		similar = similar[:5]
	}

	return similar
}

// analyzeHistoricalIncidents 分析历史事件
func (ha *HistoryAnalyzer) analyzeHistoricalIncidents(command string, assessmentContext *AssessmentContext) []string {
	incidents := make([]string, 0)

	// 基于命令类型预测可能的历史事件
	historicalIncidents := map[string][]string{
		"rm": {
			"意外删除重要文件",
			"删除系统配置文件导致服务异常",
			"删除日志文件影响审计",
		},
		"chmod": {
			"权限设置错误导致服务无法启动",
			"过度开放权限造成安全风险",
		},
		"systemctl": {
			"停止关键服务导致业务中断",
			"重启服务时配置错误",
		},
		"mount": {
			"挂载错误导致数据丢失",
			"挂载点冲突",
		},
		"dd": {
			"错误的输出设备导致数据覆盖",
			"块大小设置不当影响性能",
		},
		"fdisk": {
			"分区表损坏",
			"误删分区",
		},
	}

	if cmdIncidents, exists := historicalIncidents[command]; exists {
		incidents = append(incidents, cmdIncidents...)
	}

	return incidents
}

// isSimilarCommand 判断是否为相似命令
func (ha *HistoryAnalyzer) isSimilarCommand(command, prevCommand string) bool {
	// 提取命令名称
	cmdParts := strings.Fields(command)
	prevParts := strings.Fields(prevCommand)

	if len(cmdParts) == 0 || len(prevParts) == 0 {
		return false
	}

	cmdName := cmdParts[0]
	prevName := prevParts[0]

	// 相同命令名称
	if cmdName == prevName {
		return true
	}

	// 相似命令组
	similarGroups := [][]string{
		{"ls", "ll", "dir"},
		{"cat", "less", "more", "head", "tail"},
		{"cp", "mv", "rsync"},
		{"rm", "rmdir", "del"},
		{"chmod", "chown", "chgrp"},
		{"ps", "top", "htop"},
		{"df", "du"},
		{"tar", "zip", "unzip", "gzip", "gunzip"},
		{"wget", "curl"},
		{"systemctl", "service"},
		{"mount", "umount"},
	}

	for _, group := range similarGroups {
		cmdInGroup := false
		prevInGroup := false

		for _, cmd := range group {
			if cmd == cmdName {
				cmdInGroup = true
			}
			if cmd == prevName {
				prevInGroup = true
			}
		}

		if cmdInGroup && prevInGroup {
			return true
		}
	}

	return false
}

// removeDuplicates 去除重复项
func (ha *HistoryAnalyzer) removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	result := make([]string, 0)

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}
