package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// TestMessage 测试消息结构
type TestMessage struct {
	SessionID string `json:"session_id"`
	UserID    int64  `json:"user_id"`
	Message   string `json:"message"`
}

// TestResponse 测试响应结构
type TestResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

func main() {
	fmt.Println("🚀 开始验证AI运维管理平台修复效果...")

	// 等待服务器完全启动
	time.Sleep(2 * time.Second)

	// 测试用例
	testCases := []struct {
		name        string
		message     string
		expectError bool
		description string
	}{
		{
			name:        "密码修改操作测试",
			message:     "修改**************这台主机密码为1qaz#EDC",
			expectError: false,
			description: "测试原始报错的密码修改操作",
		},
		{
			name:        "低置信度测试",
			message:     "密码",
			expectError: false,
			description: "测试低置信度情况的智能引导",
		},
		{
			name:        "主机查询测试",
			message:     "查看主机列表",
			expectError: false,
			description: "测试数据库查询操作",
		},
		{
			name:        "SSH命令测试",
			message:     "在*************上执行ps aux命令",
			expectError: false,
			description: "测试SSH命令执行",
		},
		{
			name:        "服务管理测试",
			message:     "重启nginx服务",
			expectError: false,
			description: "测试服务管理功能",
		},
		{
			name:        "模糊输入测试",
			message:     "主机",
			expectError: false,
			description: "测试模糊输入的智能引导",
		},
	}

	successCount := 0
	totalCount := len(testCases)

	for i, tc := range testCases {
		fmt.Printf("\n📋 测试 %d/%d: %s\n", i+1, totalCount, tc.name)
		fmt.Printf("📝 描述: %s\n", tc.description)
		fmt.Printf("💬 输入: %s\n", tc.message)

		success := testAIMessage(tc.message, tc.expectError)
		if success {
			successCount++
			fmt.Printf("✅ 测试通过\n")
		} else {
			fmt.Printf("❌ 测试失败\n")
		}

		// 间隔一下避免请求过快
		time.Sleep(1 * time.Second)
	}

	fmt.Printf("\n🎯 测试总结:\n")
	fmt.Printf("✅ 成功: %d/%d\n", successCount, totalCount)
	fmt.Printf("❌ 失败: %d/%d\n", totalCount-successCount, totalCount)

	if successCount == totalCount {
		fmt.Printf("🎉 所有测试通过！修复效果验证成功！\n")
	} else {
		fmt.Printf("⚠️ 部分测试失败，需要进一步检查\n")
	}
}

// testAIMessage 测试AI消息处理
func testAIMessage(message string, expectError bool) bool {
	// 构建请求
	testMsg := TestMessage{
		SessionID: "test_session_123",
		UserID:    1,
		Message:   message,
	}

	jsonData, err := json.Marshal(testMsg)
	if err != nil {
		fmt.Printf("❌ JSON序列化失败: %v\n", err)
		return false
	}

	// 发送请求
	resp, err := http.Post("http://localhost:8080/api/v1/ai/message", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ 请求失败: %v\n", err)
		return false
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return false
	}

	// 解析响应
	var testResp TestResponse
	if err := json.Unmarshal(body, &testResp); err != nil {
		fmt.Printf("❌ 响应解析失败: %v\n", err)
		fmt.Printf("📄 原始响应: %s\n", string(body))
		return false
	}

	// 检查是否有空指针异常
	if resp.StatusCode == 500 {
		fmt.Printf("❌ 服务器内部错误 (可能是空指针异常)\n")
		fmt.Printf("📄 响应内容: %s\n", string(body))
		return false
	}

	// 输出响应信息
	fmt.Printf("📤 状态码: %d\n", resp.StatusCode)
	fmt.Printf("📄 响应: %s\n", testResp.Message)

	// 如果期望错误但没有错误，或者不期望错误但有错误，则测试失败
	if expectError && resp.StatusCode == 200 {
		fmt.Printf("⚠️ 期望错误但请求成功\n")
		return false
	}

	if !expectError && resp.StatusCode != 200 {
		fmt.Printf("⚠️ 不期望错误但请求失败\n")
		return false
	}

	return true
}
