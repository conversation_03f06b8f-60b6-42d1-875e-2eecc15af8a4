package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// MultiLayerCache 多层缓存系统
type MultiLayerCache struct {
	// 缓存层
	l1Cache *MemoryCache    // L1: 内存缓存 (最快)
	l2Cache *LRUCache       // L2: LRU缓存 (中等)
	l3Cache *PersistentCache // L3: 持久化缓存 (最慢但持久)
	
	// 配置
	config *CacheConfig
	logger *logrus.Logger
	
	// 统计
	stats *CacheStats
	mutex sync.RWMutex
}

// CacheConfig 缓存配置
type CacheConfig struct {
	// L1 内存缓存配置
	L1MaxSize    int           `json:"l1_max_size"`
	L1TTL        time.Duration `json:"l1_ttl"`
	L1Enabled    bool          `json:"l1_enabled"`
	
	// L2 LRU缓存配置
	L2MaxSize    int           `json:"l2_max_size"`
	L2TTL        time.Duration `json:"l2_ttl"`
	L2Enabled    bool          `json:"l2_enabled"`
	
	// L3 持久化缓存配置
	L3MaxSize    int           `json:"l3_max_size"`
	L3TTL        time.Duration `json:"l3_ttl"`
	L3Enabled    bool          `json:"l3_enabled"`
	L3Path       string        `json:"l3_path"`
	
	// 通用配置
	EnableStats  bool          `json:"enable_stats"`
	CleanupInterval time.Duration `json:"cleanup_interval"`
}

// CacheStats 缓存统计
type CacheStats struct {
	// 命中统计
	L1Hits       int64 `json:"l1_hits"`
	L2Hits       int64 `json:"l2_hits"`
	L3Hits       int64 `json:"l3_hits"`
	Misses       int64 `json:"misses"`
	
	// 操作统计
	Sets         int64 `json:"sets"`
	Gets         int64 `json:"gets"`
	Deletes      int64 `json:"deletes"`
	Evictions    int64 `json:"evictions"`
	
	// 性能统计
	AvgGetTime   float64 `json:"avg_get_time_ms"`
	AvgSetTime   float64 `json:"avg_set_time_ms"`
	
	// 时间戳
	LastUpdated  time.Time `json:"last_updated"`
}

// CacheItem 缓存项
type CacheItem struct {
	Key        string      `json:"key"`
	Value      interface{} `json:"value"`
	ExpiresAt  time.Time   `json:"expires_at"`
	CreatedAt  time.Time   `json:"created_at"`
	AccessedAt time.Time   `json:"accessed_at"`
	AccessCount int64      `json:"access_count"`
}

// NewMultiLayerCache 创建多层缓存
func NewMultiLayerCache(config *CacheConfig, logger *logrus.Logger) *MultiLayerCache {
	if config == nil {
		config = &CacheConfig{
			L1MaxSize:       1000,
			L1TTL:           5 * time.Minute,
			L1Enabled:       true,
			L2MaxSize:       5000,
			L2TTL:           15 * time.Minute,
			L2Enabled:       true,
			L3MaxSize:       10000,
			L3TTL:           1 * time.Hour,
			L3Enabled:       true,
			L3Path:          "./cache",
			EnableStats:     true,
			CleanupInterval: 1 * time.Minute,
		}
	}

	cache := &MultiLayerCache{
		config: config,
		logger: logger,
		stats: &CacheStats{
			LastUpdated: time.Now(),
		},
	}

	// 初始化缓存层
	if config.L1Enabled {
		cache.l1Cache = NewMemoryCache(config.L1MaxSize, config.L1TTL, logger)
	}
	
	if config.L2Enabled {
		cache.l2Cache = NewLRUCache(config.L2MaxSize, config.L2TTL, logger)
	}
	
	if config.L3Enabled {
		cache.l3Cache = NewPersistentCache(config.L3MaxSize, config.L3TTL, config.L3Path, logger)
	}

	// 启动清理任务
	go cache.startCleanupTask()

	logger.WithFields(logrus.Fields{
		"l1_enabled": config.L1Enabled,
		"l2_enabled": config.L2Enabled,
		"l3_enabled": config.L3Enabled,
	}).Info("MultiLayerCache initialized")

	return cache
}

// Get 获取缓存值
func (mlc *MultiLayerCache) Get(ctx context.Context, key string) (interface{}, bool) {
	start := time.Now()
	defer func() {
		if mlc.config.EnableStats {
			mlc.updateGetStats(time.Since(start))
		}
	}()

	mlc.logger.WithField("key", key).Debug("MultiLayerCache: Getting value")

	// L1 缓存查找
	if mlc.config.L1Enabled && mlc.l1Cache != nil {
		if value, found := mlc.l1Cache.Get(ctx, key); found {
			mlc.updateStats("l1_hit")
			mlc.logger.WithField("key", key).Debug("MultiLayerCache: L1 cache hit")
			return value, true
		}
	}

	// L2 缓存查找
	if mlc.config.L2Enabled && mlc.l2Cache != nil {
		if value, found := mlc.l2Cache.Get(ctx, key); found {
			mlc.updateStats("l2_hit")
			mlc.logger.WithField("key", key).Debug("MultiLayerCache: L2 cache hit")
			
			// 提升到L1缓存
			if mlc.config.L1Enabled && mlc.l1Cache != nil {
				mlc.l1Cache.Set(ctx, key, value, mlc.config.L1TTL)
			}
			
			return value, true
		}
	}

	// L3 缓存查找
	if mlc.config.L3Enabled && mlc.l3Cache != nil {
		if value, found := mlc.l3Cache.Get(ctx, key); found {
			mlc.updateStats("l3_hit")
			mlc.logger.WithField("key", key).Debug("MultiLayerCache: L3 cache hit")
			
			// 提升到L2和L1缓存
			if mlc.config.L2Enabled && mlc.l2Cache != nil {
				mlc.l2Cache.Set(ctx, key, value, mlc.config.L2TTL)
			}
			if mlc.config.L1Enabled && mlc.l1Cache != nil {
				mlc.l1Cache.Set(ctx, key, value, mlc.config.L1TTL)
			}
			
			return value, true
		}
	}

	// 缓存未命中
	mlc.updateStats("miss")
	mlc.logger.WithField("key", key).Debug("MultiLayerCache: Cache miss")
	return nil, false
}

// Set 设置缓存值
func (mlc *MultiLayerCache) Set(ctx context.Context, key string, value interface{}, ttl time.Duration) error {
	start := time.Now()
	defer func() {
		if mlc.config.EnableStats {
			mlc.updateSetStats(time.Since(start))
		}
	}()

	mlc.logger.WithFields(logrus.Fields{
		"key": key,
		"ttl": ttl,
	}).Debug("MultiLayerCache: Setting value")

	// 设置到所有启用的缓存层
	var errors []error

	if mlc.config.L1Enabled && mlc.l1Cache != nil {
		if err := mlc.l1Cache.Set(ctx, key, value, ttl); err != nil {
			errors = append(errors, fmt.Errorf("L1 cache set failed: %w", err))
		}
	}

	if mlc.config.L2Enabled && mlc.l2Cache != nil {
		if err := mlc.l2Cache.Set(ctx, key, value, ttl); err != nil {
			errors = append(errors, fmt.Errorf("L2 cache set failed: %w", err))
		}
	}

	if mlc.config.L3Enabled && mlc.l3Cache != nil {
		if err := mlc.l3Cache.Set(ctx, key, value, ttl); err != nil {
			errors = append(errors, fmt.Errorf("L3 cache set failed: %w", err))
		}
	}

	mlc.updateStats("set")

	if len(errors) > 0 {
		return fmt.Errorf("cache set errors: %v", errors)
	}

	return nil
}

// Delete 删除缓存值
func (mlc *MultiLayerCache) Delete(ctx context.Context, key string) error {
	mlc.logger.WithField("key", key).Debug("MultiLayerCache: Deleting value")

	// 从所有缓存层删除
	var errors []error

	if mlc.config.L1Enabled && mlc.l1Cache != nil {
		if err := mlc.l1Cache.Delete(ctx, key); err != nil {
			errors = append(errors, fmt.Errorf("L1 cache delete failed: %w", err))
		}
	}

	if mlc.config.L2Enabled && mlc.l2Cache != nil {
		if err := mlc.l2Cache.Delete(ctx, key); err != nil {
			errors = append(errors, fmt.Errorf("L2 cache delete failed: %w", err))
		}
	}

	if mlc.config.L3Enabled && mlc.l3Cache != nil {
		if err := mlc.l3Cache.Delete(ctx, key); err != nil {
			errors = append(errors, fmt.Errorf("L3 cache delete failed: %w", err))
		}
	}

	mlc.updateStats("delete")

	if len(errors) > 0 {
		return fmt.Errorf("cache delete errors: %v", errors)
	}

	return nil
}

// Clear 清空所有缓存
func (mlc *MultiLayerCache) Clear(ctx context.Context) error {
	mlc.logger.Info("MultiLayerCache: Clearing all caches")

	var errors []error

	if mlc.config.L1Enabled && mlc.l1Cache != nil {
		if err := mlc.l1Cache.Clear(ctx); err != nil {
			errors = append(errors, fmt.Errorf("L1 cache clear failed: %w", err))
		}
	}

	if mlc.config.L2Enabled && mlc.l2Cache != nil {
		if err := mlc.l2Cache.Clear(ctx); err != nil {
			errors = append(errors, fmt.Errorf("L2 cache clear failed: %w", err))
		}
	}

	if mlc.config.L3Enabled && mlc.l3Cache != nil {
		if err := mlc.l3Cache.Clear(ctx); err != nil {
			errors = append(errors, fmt.Errorf("L3 cache clear failed: %w", err))
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("cache clear errors: %v", errors)
	}

	return nil
}

// GetStats 获取缓存统计
func (mlc *MultiLayerCache) GetStats() *CacheStats {
	mlc.mutex.RLock()
	defer mlc.mutex.RUnlock()

	// 创建副本
	stats := &CacheStats{
		L1Hits:      mlc.stats.L1Hits,
		L2Hits:      mlc.stats.L2Hits,
		L3Hits:      mlc.stats.L3Hits,
		Misses:      mlc.stats.Misses,
		Sets:        mlc.stats.Sets,
		Gets:        mlc.stats.Gets,
		Deletes:     mlc.stats.Deletes,
		Evictions:   mlc.stats.Evictions,
		AvgGetTime:  mlc.stats.AvgGetTime,
		AvgSetTime:  mlc.stats.AvgSetTime,
		LastUpdated: mlc.stats.LastUpdated,
	}

	return stats
}

// GetHitRate 获取命中率
func (mlc *MultiLayerCache) GetHitRate() float64 {
	mlc.mutex.RLock()
	defer mlc.mutex.RUnlock()

	totalHits := mlc.stats.L1Hits + mlc.stats.L2Hits + mlc.stats.L3Hits
	totalRequests := totalHits + mlc.stats.Misses

	if totalRequests == 0 {
		return 0.0
	}

	return float64(totalHits) / float64(totalRequests)
}

// updateStats 更新统计信息
func (mlc *MultiLayerCache) updateStats(operation string) {
	if !mlc.config.EnableStats {
		return
	}

	mlc.mutex.Lock()
	defer mlc.mutex.Unlock()

	switch operation {
	case "l1_hit":
		mlc.stats.L1Hits++
		mlc.stats.Gets++
	case "l2_hit":
		mlc.stats.L2Hits++
		mlc.stats.Gets++
	case "l3_hit":
		mlc.stats.L3Hits++
		mlc.stats.Gets++
	case "miss":
		mlc.stats.Misses++
		mlc.stats.Gets++
	case "set":
		mlc.stats.Sets++
	case "delete":
		mlc.stats.Deletes++
	case "eviction":
		mlc.stats.Evictions++
	}

	mlc.stats.LastUpdated = time.Now()
}

// updateGetStats 更新获取操作统计
func (mlc *MultiLayerCache) updateGetStats(duration time.Duration) {
	if !mlc.config.EnableStats {
		return
	}

	mlc.mutex.Lock()
	defer mlc.mutex.Unlock()

	currentAvg := mlc.stats.AvgGetTime
	newTime := float64(duration.Milliseconds())
	
	if mlc.stats.Gets > 0 {
		mlc.stats.AvgGetTime = (currentAvg*float64(mlc.stats.Gets-1) + newTime) / float64(mlc.stats.Gets)
	} else {
		mlc.stats.AvgGetTime = newTime
	}
}

// updateSetStats 更新设置操作统计
func (mlc *MultiLayerCache) updateSetStats(duration time.Duration) {
	if !mlc.config.EnableStats {
		return
	}

	mlc.mutex.Lock()
	defer mlc.mutex.Unlock()

	currentAvg := mlc.stats.AvgSetTime
	newTime := float64(duration.Milliseconds())
	
	if mlc.stats.Sets > 0 {
		mlc.stats.AvgSetTime = (currentAvg*float64(mlc.stats.Sets-1) + newTime) / float64(mlc.stats.Sets)
	} else {
		mlc.stats.AvgSetTime = newTime
	}
}

// startCleanupTask 启动清理任务
func (mlc *MultiLayerCache) startCleanupTask() {
	ticker := time.NewTicker(mlc.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		mlc.cleanup()
	}
}

// cleanup 清理过期缓存
func (mlc *MultiLayerCache) cleanup() {
	mlc.logger.Debug("MultiLayerCache: Running cleanup task")

	ctx := context.Background()

	if mlc.config.L1Enabled && mlc.l1Cache != nil {
		mlc.l1Cache.Cleanup(ctx)
	}

	if mlc.config.L2Enabled && mlc.l2Cache != nil {
		mlc.l2Cache.Cleanup(ctx)
	}

	if mlc.config.L3Enabled && mlc.l3Cache != nil {
		mlc.l3Cache.Cleanup(ctx)
	}
}
