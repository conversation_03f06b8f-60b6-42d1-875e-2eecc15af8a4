# AI运维管理平台部署指南

## 📋 目录

- [系统要求](#系统要求)
- [快速开始](#快速开始)
- [部署模式](#部署模式)
- [配置说明](#配置说明)
- [安全配置](#安全配置)
- [监控与维护](#监控与维护)
- [故障排除](#故障排除)
- [升级指南](#升级指南)

## 🔧 系统要求

### 最低要求
- **操作系统**: Linux (Ubuntu 20.04+, CentOS 8+, RHEL 8+)
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 20GB 可用空间
- **网络**: 稳定的互联网连接

### 推荐配置
- **操作系统**: Ubuntu 22.04 LTS
- **CPU**: 4核心或更多
- **内存**: 8GB RAM或更多
- **存储**: 100GB SSD
- **网络**: 1Gbps带宽

### 软件依赖
- **Go**: 1.19或更高版本
- **Git**: 2.0或更高版本
- **Docker**: 20.10或更高版本 (可选)
- **Docker Compose**: 2.0或更高版本 (可选)

## 🚀 快速开始

### 1. 获取源码

```bash
git clone https://github.com/your-org/aiops-platform.git
cd aiops-platform
```

### 2. 设置环境变量

```bash
export DEEPSEEK_API_KEY="your-deepseek-api-key"
export NODE_ID="$(hostname)"
```

### 3. 一键部署

```bash
# 独立部署（推荐用于开发和测试）
./scripts/deploy.sh -m standalone -e production -p 8080

# 集群部署（推荐用于生产环境）
./scripts/deploy.sh -m cluster --enable-ha --enable-multi-tenant

# Docker部署（推荐用于容器化环境）
./scripts/deploy.sh -m docker -e production
```

## 🏗️ 部署模式

### 独立部署 (Standalone)

适用于开发、测试和小规模生产环境。

```bash
./scripts/deploy.sh -m standalone -e production -p 8080 --enable-security
```

**特点:**
- 单节点部署
- 快速启动
- 资源占用较少
- 适合中小型团队

### 集群部署 (Cluster)

适用于大规模生产环境，提供高可用性。

```bash
./scripts/deploy.sh -m cluster --enable-ha --enable-multi-tenant -p 8080
```

**特点:**
- 多节点部署
- 自动故障转移
- 负载均衡
- 数据复制

### Docker部署

适用于容器化环境和云原生部署。

```bash
./scripts/deploy.sh -m docker -e production --enable-multi-tenant
```

**特点:**
- 容器化部署
- 易于扩展
- 环境隔离
- 支持编排工具

## ⚙️ 配置说明

### 主配置文件

配置文件位置: `configs/config.yaml`

```yaml
# 服务器配置
server:
  port: 8080
  host: "0.0.0.0"
  environment: "production"
  read_timeout: "30s"
  write_timeout: "30s"
  idle_timeout: "60s"

# 数据库配置
database:
  type: "sqlite"
  dsn: "./aiops.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "5m"

# DeepSeek AI配置
deepseek:
  api_key: "${DEEPSEEK_API_KEY}"
  base_url: "https://api.deepseek.com"
  model: "deepseek-chat"
  timeout: "15s"
  max_retries: 3

# 缓存配置
cache:
  enable: true
  l1_max_size: 1000
  l1_ttl: "5m"
  l2_max_size: 5000
  l2_ttl: "15m"
  l3_max_size: 10000
  l3_ttl: "1h"

# 安全配置
security:
  enable: true
  encryption_algorithm: "AES-256-GCM"
  key_rotation_interval: "24h"
  audit_retention_days: 90
  max_login_attempts: 5
  session_timeout: "30m"

# 高可用配置
high_availability:
  enable: false
  cluster_name: "aiops-cluster"
  node_id: "${NODE_ID}"
  health_check_interval: "30s"
  failover_timeout: "5m"

# 多租户配置
multi_tenant:
  enable: false
  max_tenants_per_node: 100
  default_quotas:
    max_cpu_cores: 4
    max_memory_mb: 8192
    max_storage_gb: 100
    max_hosts: 50

# 监控配置
monitoring:
  enable: true
  collection_interval: "10s"
  retention_period: "24h"
  alert_thresholds:
    cpu_usage_percent: 80.0
    memory_usage_mb: 1024
    goroutine_count: 10000

# 日志配置
logging:
  level: "info"
  format: "json"
  output: "stdout"
  file_path: "./logs/aiops.log"
  max_size: 100
  max_backups: 10
  max_age: 30
```

### 环境变量

| 变量名 | 描述 | 默认值 | 必需 |
|--------|------|--------|------|
| `DEEPSEEK_API_KEY` | DeepSeek API密钥 | - | 是 |
| `NODE_ID` | 节点标识符 | hostname | 否 |
| `CONFIG_PATH` | 配置文件路径 | ./configs/config.yaml | 否 |
| `LOG_LEVEL` | 日志级别 | info | 否 |
| `PORT` | 服务端口 | 8080 | 否 |

## 🛡️ 安全配置

### SSL/TLS配置

```yaml
server:
  tls:
    enable: true
    cert_file: "/path/to/cert.pem"
    key_file: "/path/to/key.pem"
    min_version: "1.2"
```

### 防火墙配置

```bash
# 开放必要端口
sudo ufw allow 8080/tcp
sudo ufw allow 22/tcp
sudo ufw enable
```

### 用户权限

```bash
# 创建专用用户
sudo useradd -r -s /bin/false aiops
sudo mkdir -p /opt/aiops
sudo chown aiops:aiops /opt/aiops
```

### API密钥管理

```bash
# 使用环境变量
export DEEPSEEK_API_KEY="your-secure-api-key"

# 或使用密钥管理服务
# 例如: AWS Secrets Manager, HashiCorp Vault
```

## 📊 监控与维护

### 健康检查

```bash
# 检查服务状态
curl http://localhost:8080/health

# 检查系统指标
curl http://localhost:8080/api/v1/metrics

# 检查追踪信息
curl http://localhost:8080/api/v1/traces
```

### 日志管理

```bash
# 查看实时日志
sudo journalctl -u aiops-platform -f

# 查看错误日志
sudo journalctl -u aiops-platform -p err

# 日志轮转配置
sudo logrotate -d /etc/logrotate.d/aiops-platform
```

### 性能监控

```bash
# CPU和内存使用情况
top -p $(pgrep aiops-platform)

# 网络连接
netstat -tulpn | grep :8080

# 磁盘使用情况
df -h /opt/aiops
```

### 备份策略

```bash
# 数据库备份
cp /opt/aiops/aiops.db /backup/aiops_$(date +%Y%m%d_%H%M%S).db

# 配置备份
tar -czf /backup/configs_$(date +%Y%m%d_%H%M%S).tar.gz /opt/aiops/configs

# 自动备份脚本
cat > /etc/cron.daily/aiops-backup << 'EOF'
#!/bin/bash
BACKUP_DIR="/backup/aiops"
DATE=$(date +%Y%m%d_%H%M%S)
mkdir -p $BACKUP_DIR
cp /opt/aiops/aiops.db $BACKUP_DIR/aiops_$DATE.db
tar -czf $BACKUP_DIR/configs_$DATE.tar.gz /opt/aiops/configs
find $BACKUP_DIR -name "*.db" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete
EOF
chmod +x /etc/cron.daily/aiops-backup
```

## 🔧 故障排除

### 常见问题

#### 1. 服务无法启动

```bash
# 检查端口占用
sudo netstat -tulpn | grep :8080

# 检查配置文件
./aiops-platform -config configs/config.yaml -validate

# 检查权限
ls -la /opt/aiops/
```

#### 2. API连接失败

```bash
# 检查DeepSeek API连接
curl -H "Authorization: Bearer $DEEPSEEK_API_KEY" \
     https://api.deepseek.com/v1/models

# 检查网络连接
ping api.deepseek.com
```

#### 3. 数据库问题

```bash
# 检查数据库文件
ls -la aiops.db

# 数据库完整性检查
sqlite3 aiops.db "PRAGMA integrity_check;"
```

#### 4. 内存不足

```bash
# 检查内存使用
free -h

# 调整配置
# 在config.yaml中减少缓存大小
cache:
  l1_max_size: 500
  l2_max_size: 2000
  l3_max_size: 5000
```

### 日志分析

```bash
# 错误日志分析
grep "ERROR" /opt/aiops/logs/aiops.log | tail -20

# 性能问题分析
grep "slow" /opt/aiops/logs/aiops.log

# 安全事件分析
grep "security" /opt/aiops/logs/aiops.log
```

## 📈 升级指南

### 版本升级

```bash
# 1. 备份当前版本
./scripts/deploy.sh --skip-tests --backup-only

# 2. 下载新版本
git fetch origin
git checkout v2.1.0

# 3. 执行升级
./scripts/upgrade.sh --from-version v2.0.0 --to-version v2.1.0

# 4. 验证升级
curl http://localhost:8080/health
```

### 配置迁移

```bash
# 配置文件迁移
./scripts/migrate-config.sh --from configs/config.yaml --to configs/config.v2.yaml

# 数据库迁移
./scripts/migrate-db.sh --backup --migrate
```

### 回滚策略

```bash
# 快速回滚
./scripts/rollback.sh --to-version v2.0.0

# 数据回滚
./scripts/restore-backup.sh --backup-date 20240130_120000
```

## 🆘 支持与帮助

### 获取帮助

- **文档**: [https://docs.aiops-platform.com](https://docs.aiops-platform.com)
- **GitHub Issues**: [https://github.com/your-org/aiops-platform/issues](https://github.com/your-org/aiops-platform/issues)
- **社区论坛**: [https://community.aiops-platform.com](https://community.aiops-platform.com)

### 联系方式

- **技术支持**: <EMAIL>
- **商务咨询**: <EMAIL>
- **安全问题**: <EMAIL>

---

**注意**: 本指南基于AI运维管理平台v2.0.0版本编写。不同版本可能存在差异，请参考对应版本的文档。
