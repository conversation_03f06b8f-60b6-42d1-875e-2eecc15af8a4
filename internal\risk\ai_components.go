package risk

import (
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// 知识库实现

// NewRiskKnowledgeBase 创建风险知识库
func NewRiskKnowledgeBase(logger *logrus.Logger) *RiskKnowledgeBase {
	kb := &RiskKnowledgeBase{
		logger:       logger,
		riskPatterns: make([]*RiskPattern, 0),
		commandRules: make([]*CommandRule, 0),
		contextRules: make([]*ContextRule, 0),
		mitigationDB: &MitigationDatabase{
			mitigations: make(map[string][]*RiskMitigation),
		},
	}

	return kb
}

// Initialize 初始化知识库
func (kb *RiskKnowledgeBase) Initialize() error {
	kb.mutex.Lock()
	defer kb.mutex.Unlock()

	kb.logger.Info("Initializing risk knowledge base")

	// 加载默认风险模式
	kb.loadDefaultPatterns()

	// 加载默认命令规则
	kb.loadDefaultCommandRules()

	// 加载默认上下文规则
	kb.loadDefaultContextRules()

	// 加载默认缓解措施
	kb.loadDefaultMitigations()

	kb.logger.WithFields(logrus.Fields{
		"patterns": len(kb.riskPatterns),
		"rules":    len(kb.commandRules),
		"contexts": len(kb.contextRules),
	}).Info("Risk knowledge base initialized")

	return nil
}

// loadDefaultPatterns 加载默认模式
func (kb *RiskKnowledgeBase) loadDefaultPatterns() {
	patterns := []*RiskPattern{
		{
			ID:          "pattern_001",
			Name:        "危险删除操作",
			Pattern:     "rm.*-rf.*",
			RiskLevel:   "critical",
			Category:    "file_operation",
			Description: "强制递归删除操作，可能导致数据丢失",
			Examples:    []string{"rm -rf /", "rm -rf *"},
			Indicators:  []string{"rm", "-rf", "递归删除"},
			Weight:      1.0,
			Confidence:  0.9,
		},
		{
			ID:          "pattern_002",
			Name:        "系统关机操作",
			Pattern:     "(shutdown|halt|reboot).*",
			RiskLevel:   "high",
			Category:    "system_control",
			Description: "系统关机或重启操作",
			Examples:    []string{"shutdown -h now", "reboot"},
			Indicators:  []string{"shutdown", "halt", "reboot"},
			Weight:      0.8,
			Confidence:  0.8,
		},
		{
			ID:          "pattern_003",
			Name:        "权限修改操作",
			Pattern:     "(chmod|chown).*777.*",
			RiskLevel:   "medium",
			Category:    "permission",
			Description: "设置过于宽松的文件权限",
			Examples:    []string{"chmod 777 file", "chmod -R 777 /"},
			Indicators:  []string{"chmod", "777", "权限"},
			Weight:      0.6,
			Confidence:  0.7,
		},
	}

	kb.riskPatterns = append(kb.riskPatterns, patterns...)
}

// loadDefaultCommandRules 加载默认命令规则
func (kb *RiskKnowledgeBase) loadDefaultCommandRules() {
	rules := []*CommandRule{
		{
			ID:          "rule_001",
			Command:     "dd",
			RiskLevel:   "critical",
			Conditions:  map[string]string{"target": "/dev/"},
			Exceptions:  []string{},
			Description: "dd命令操作设备文件",
			Reasoning:   "dd命令直接操作设备可能导致数据丢失",
			Weight:      1.0,
		},
		{
			ID:          "rule_002",
			Command:     "mkfs",
			RiskLevel:   "critical",
			Conditions:  map[string]string{},
			Exceptions:  []string{},
			Description: "格式化文件系统",
			Reasoning:   "格式化操作会清除所有数据",
			Weight:      1.0,
		},
		{
			ID:          "rule_003",
			Command:     "iptables",
			RiskLevel:   "high",
			Conditions:  map[string]string{"action": "flush"},
			Exceptions:  []string{},
			Description: "清空防火墙规则",
			Reasoning:   "清空防火墙规则可能导致安全风险",
			Weight:      0.8,
		},
	}

	kb.commandRules = append(kb.commandRules, rules...)
}

// loadDefaultContextRules 加载默认上下文规则
func (kb *RiskKnowledgeBase) loadDefaultContextRules() {
	rules := []*ContextRule{
		{
			ID:           "ctx_001",
			Name:         "非工作时间高风险操作",
			Conditions:   map[string]interface{}{"time_range": "22:00-06:00"},
			RiskModifier: 1.5,
			Description:  "非工作时间执行高风险操作",
			Priority:     1,
		},
		{
			ID:           "ctx_002",
			Name:         "新用户高风险操作",
			Conditions:   map[string]interface{}{"user_experience": "beginner"},
			RiskModifier: 1.3,
			Description:  "新用户执行高风险操作",
			Priority:     2,
		},
		{
			ID:           "ctx_003",
			Name:         "生产环境操作",
			Conditions:   map[string]interface{}{"environment": "production"},
			RiskModifier: 1.4,
			Description:  "生产环境中的操作",
			Priority:     1,
		},
	}

	kb.contextRules = append(kb.contextRules, rules...)
}

// loadDefaultMitigations 加载默认缓解措施
func (kb *RiskKnowledgeBase) loadDefaultMitigations() {
	mitigations := map[string][]*RiskMitigation{
		"critical": {
			{
				Type:          "prevention",
				Description:   "阻止命令执行",
				Actions:       []string{"停止执行", "需要管理员授权"},
				Effectiveness: 0.95,
				Cost:          "low",
				TimeRequired:  "immediate",
			},
		},
		"high": {
			{
				Type:          "confirmation",
				Description:   "需要二次确认",
				Actions:       []string{"显示警告", "要求确认", "记录操作"},
				Effectiveness: 0.8,
				Cost:          "low",
				TimeRequired:  "1-2 minutes",
			},
		},
		"medium": {
			{
				Type:          "monitoring",
				Description:   "增强监控",
				Actions:       []string{"实时监控", "记录详细日志", "设置告警"},
				Effectiveness: 0.6,
				Cost:          "medium",
				TimeRequired:  "ongoing",
			},
		},
	}

	kb.mitigationDB.mitigations = mitigations
}

// MatchCommandRules 匹配命令规则
func (kb *RiskKnowledgeBase) MatchCommandRules(command string, context *CommandContext) []*CommandRule {
	kb.mutex.RLock()
	defer kb.mutex.RUnlock()

	var matches []*CommandRule

	for _, rule := range kb.commandRules {
		if kb.matchesRule(command, rule) {
			matches = append(matches, rule)
		}
	}

	return matches
}

// matchesRule 检查是否匹配规则
func (kb *RiskKnowledgeBase) matchesRule(command string, rule *CommandRule) bool {
	// 简化实现：检查命令是否包含规则中的命令
	return len(command) > 0 && len(rule.Command) > 0 &&
		(command == rule.Command ||
			(len(command) > len(rule.Command) && command[:len(rule.Command)] == rule.Command))
}

// 模式匹配器实现

// NewRiskPatternMatcher 创建风险模式匹配器
func NewRiskPatternMatcher(logger *logrus.Logger) *RiskPatternMatcher {
	return &RiskPatternMatcher{
		logger:   logger,
		patterns: make([]*CompiledPattern, 0),
	}
}

// Initialize 初始化模式匹配器
func (rpm *RiskPatternMatcher) Initialize(patterns []*RiskPattern) error {
	rpm.mutex.Lock()
	defer rpm.mutex.Unlock()

	rpm.logger.Info("Initializing risk pattern matcher")

	for _, pattern := range patterns {
		compiled := &CompiledPattern{
			Pattern:    pattern,
			Regex:      nil, // 简化实现
			Matcher:    rpm.createMatcher(pattern.Pattern),
			Weight:     pattern.Weight,
			Confidence: pattern.Confidence,
		}

		rpm.patterns = append(rpm.patterns, compiled)
	}

	rpm.logger.WithField("patterns", len(rpm.patterns)).Info("Pattern matcher initialized")

	return nil
}

// createMatcher 创建匹配器
func (rpm *RiskPatternMatcher) createMatcher(pattern string) func(string) bool {
	// 简化实现：基于字符串包含的匹配
	return func(command string) bool {
		// 简单的模式匹配逻辑
		if pattern == "rm.*-rf.*" {
			return len(command) >= 5 && command[:2] == "rm" &&
				(command[2] == ' ' || command[2] == '\t') &&
				(command[len(command)-2:] == "rf" ||
					command[len(command)-3:] == "-rf")
		}

		if pattern == "(shutdown|halt|reboot).*" {
			return command == "shutdown" || command == "halt" || command == "reboot" ||
				(len(command) > 8 && (command[:8] == "shutdown" ||
					command[:4] == "halt" || command[:6] == "reboot"))
		}

		if pattern == "(chmod|chown).*777.*" {
			return (len(command) >= 5 && command[:5] == "chmod" ||
				len(command) >= 5 && command[:5] == "chown") &&
				len(command) >= 3 && command[len(command)-3:] == "777"
		}

		return false
	}
}

// MatchCommand 匹配命令
func (rpm *RiskPatternMatcher) MatchCommand(command string) []*RiskPattern {
	rpm.mutex.RLock()
	defer rpm.mutex.RUnlock()

	var matches []*RiskPattern

	for _, compiled := range rpm.patterns {
		if compiled.Matcher(command) {
			matches = append(matches, compiled.Pattern)
		}
	}

	return matches
}

// 上下文管理器实现

// NewRiskContextManager 创建风险上下文管理器
func NewRiskContextManager(logger *logrus.Logger) *RiskContextManager {
	return &RiskContextManager{
		logger:   logger,
		contexts: make(map[string]*RiskContext),
	}
}

// GetContext 获取上下文
func (rcm *RiskContextManager) GetContext(sessionID string) *RiskContext {
	rcm.mutex.RLock()
	defer rcm.mutex.RUnlock()

	return rcm.contexts[sessionID]
}

// UpdateContext 更新上下文
func (rcm *RiskContextManager) UpdateContext(sessionID string, command *CommandHistory) {
	rcm.mutex.Lock()
	defer rcm.mutex.Unlock()

	context, exists := rcm.contexts[sessionID]
	if !exists {
		context = &RiskContext{
			SessionID:   sessionID,
			Commands:    make([]*CommandHistory, 0),
			RiskHistory: make([]*RiskAssessment, 0),
			Patterns:    make([]string, 0),
			Anomalies:   make([]string, 0),
			Metadata:    make(map[string]interface{}),
			CreatedAt:   time.Now(),
		}
		rcm.contexts[sessionID] = context
	}

	context.Commands = append(context.Commands, command)
	context.UpdatedAt = time.Now()

	// 限制历史记录长度
	if len(context.Commands) > 100 {
		context.Commands = context.Commands[1:]
	}
}

// 缓存实现

// NewRiskAnalysisCache 创建风险分析缓存
func NewRiskAnalysisCache(expiration time.Duration, maxSize int, logger *logrus.Logger) *RiskAnalysisCache {
	return &RiskAnalysisCache{
		logger:     logger,
		cache:      make(map[string]*CachedAnalysis),
		expiration: expiration,
		maxSize:    maxSize,
	}
}

// Get 获取缓存
func (rac *RiskAnalysisCache) Get(key string) *CachedAnalysis {
	rac.mutex.RLock()
	defer rac.mutex.RUnlock()

	cached, exists := rac.cache[key]
	if !exists {
		return nil
	}

	// 检查是否过期
	if time.Now().After(cached.ExpiresAt) {
		delete(rac.cache, key)
		return nil
	}

	cached.HitCount++
	return cached
}

// Set 设置缓存
func (rac *RiskAnalysisCache) Set(key string, response *RiskAnalysisResponse) {
	rac.mutex.Lock()
	defer rac.mutex.Unlock()

	// 检查缓存大小限制
	if len(rac.cache) >= rac.maxSize {
		rac.evictOldest()
	}

	cached := &CachedAnalysis{
		Key:       key,
		Response:  response,
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(rac.expiration),
		HitCount:  0,
	}

	rac.cache[key] = cached
}

// evictOldest 驱逐最旧的缓存
func (rac *RiskAnalysisCache) evictOldest() {
	var oldestKey string
	var oldestTime time.Time

	for key, cached := range rac.cache {
		if oldestTime.IsZero() || cached.CreatedAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = cached.CreatedAt
		}
	}

	if oldestKey != "" {
		delete(rac.cache, oldestKey)
	}
}
