package alerting

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// 告警关联器实现

// NewAlertCorrelator 创建告警关联器
func NewAlertCorrelator(logger *logrus.Logger) *AlertCorrelator {
	return &AlertCorrelator{
		logger:       logger,
		rules:        make([]*CorrelationRule, 0),
		correlations: make(map[string]*AlertCorrelation),
	}
}

// 告警去重器实现

// NewAlertDeduplicator 创建告警去重器
func NewAlertDeduplicator(logger *logrus.Logger, window time.Duration, threshold float64) *AlertDeduplicator {
	return &AlertDeduplicator{
		logger:    logger,
		cache:     make(map[string]*DuplicateGroup),
		window:    window,
		threshold: threshold,
	}
}

// IsDuplicate 检查是否重复
func (ad *AlertDeduplicator) IsDuplicate(alert *Alert) bool {
	ad.mutex.Lock()
	defer ad.mutex.Unlock()

	signature := ad.generateSignature(alert)

	group, exists := ad.cache[signature]
	if !exists {
		// 创建新的重复组
		group = &DuplicateGroup{
			Signature:  signature,
			Alerts:     []*Alert{alert},
			Count:      1,
			FirstSeen:  alert.CreatedAt,
			LastSeen:   alert.CreatedAt,
			Suppressed: false,
		}
		ad.cache[signature] = group
		return false
	}

	// 检查时间窗口
	if time.Since(group.LastSeen) > ad.window {
		// 超出时间窗口，重置组
		group.Alerts = []*Alert{alert}
		group.Count = 1
		group.FirstSeen = alert.CreatedAt
		group.LastSeen = alert.CreatedAt
		group.Suppressed = false
		return false
	}

	// 在时间窗口内，增加计数
	group.Alerts = append(group.Alerts, alert)
	group.Count++
	group.LastSeen = alert.CreatedAt

	// 如果超过阈值，标记为抑制
	if group.Count > 3 {
		group.Suppressed = true
		return true
	}

	return false
}

// generateSignature 生成签名
func (ad *AlertDeduplicator) generateSignature(alert *Alert) string {
	return fmt.Sprintf("%s:%s:%s", alert.Type, alert.Source, alert.MetricName)
}

// Cleanup 清理过期缓存
func (ad *AlertDeduplicator) Cleanup() {
	ad.mutex.Lock()
	defer ad.mutex.Unlock()

	now := time.Now()
	for signature, group := range ad.cache {
		if now.Sub(group.LastSeen) > ad.window {
			delete(ad.cache, signature)
		}
	}
}

// 告警限流器实现

// NewAlertRateLimiter 创建告警限流器
func NewAlertRateLimiter(logger *logrus.Logger, maxRate int, window time.Duration) *AlertRateLimiter {
	return &AlertRateLimiter{
		logger:  logger,
		buckets: make(map[string]*RateBucket),
		maxRate: maxRate,
		window:  window,
	}
}

// ShouldLimit 检查是否应该限流
func (arl *AlertRateLimiter) ShouldLimit(alert *Alert) bool {
	arl.mutex.Lock()
	defer arl.mutex.Unlock()

	key := alert.Source
	now := time.Now()

	bucket, exists := arl.buckets[key]
	if !exists {
		bucket = &RateBucket{
			Key:       key,
			Count:     1,
			Window:    now,
			Blocked:   0,
			LastReset: now,
		}
		arl.buckets[key] = bucket
		return false
	}

	// 检查是否需要重置窗口
	if now.Sub(bucket.Window) > arl.window {
		bucket.Count = 1
		bucket.Window = now
		bucket.LastReset = now
		return false
	}

	// 检查是否超过限制
	if bucket.Count >= arl.maxRate {
		bucket.Blocked++
		return true
	}

	bucket.Count++
	return false
}

// Cleanup 清理过期桶
func (arl *AlertRateLimiter) Cleanup() {
	arl.mutex.Lock()
	defer arl.mutex.Unlock()

	now := time.Now()
	for key, bucket := range arl.buckets {
		if now.Sub(bucket.LastReset) > arl.window*2 {
			delete(arl.buckets, key)
		}
	}
}

// 告警聚合器实现

// NewAlertAggregator 创建告警聚合器
func NewAlertAggregator(config *SmartAlertConfig, logger *logrus.Logger) *AlertAggregator {
	aggConfig := &AggregationConfig{
		EnableTimeBasedAggregation: true,
		EnableRuleBasedAggregation: true,
		DefaultAggregationWindow:   config.AggregationWindow,
		MaxAlertsPerGroup:          50,
		AggregationFlushInterval:   1 * time.Minute,
	}

	return &AlertAggregator{
		logger:    logger,
		config:    aggConfig,
		groups:    make(map[string]*AlertGroup),
		rules:     make([]*AggregationRule, 0),
		scheduler: NewAggregationScheduler(logger),
	}
}

// Start 启动聚合器
func (aa *AlertAggregator) Start(ctx context.Context) error {
	aa.logger.Info("Starting alert aggregator")
	return nil
}

// Stop 停止聚合器
func (aa *AlertAggregator) Stop(ctx context.Context) error {
	aa.logger.Info("Stopping alert aggregator")
	return nil
}

// Aggregate 聚合告警
func (aa *AlertAggregator) Aggregate(alert *Alert) bool {
	aa.mutex.Lock()
	defer aa.mutex.Unlock()

	// 生成组键
	groupKey := aa.generateGroupKey(alert)

	group, exists := aa.groups[groupKey]
	if !exists {
		// 创建新组
		group = &AlertGroup{
			ID:        fmt.Sprintf("group_%d", time.Now().UnixNano()),
			Name:      fmt.Sprintf("Group for %s", alert.Source),
			Alerts:    []*Alert{alert},
			Summary:   &GroupSummary{},
			Metadata:  make(map[string]interface{}),
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
			Status:    "active",
			FlushTime: time.Now().Add(aa.config.DefaultAggregationWindow),
		}
		aa.groups[groupKey] = group
	} else {
		// 添加到现有组
		group.Alerts = append(group.Alerts, alert)
		group.UpdatedAt = time.Now()
	}

	// 更新组摘要
	aa.updateGroupSummary(group)

	// 检查是否需要立即刷新
	if len(group.Alerts) >= aa.config.MaxAlertsPerGroup {
		aa.flushGroup(group)
		delete(aa.groups, groupKey)
		return false // 不聚合，立即发送
	}

	return true // 聚合成功
}

// generateGroupKey 生成组键
func (aa *AlertAggregator) generateGroupKey(alert *Alert) string {
	return fmt.Sprintf("%s:%s", alert.Source, alert.Type)
}

// updateGroupSummary 更新组摘要
func (aa *AlertAggregator) updateGroupSummary(group *AlertGroup) {
	summary := &GroupSummary{
		TotalAlerts: len(group.Alerts),
		TopSources:  make([]string, 0),
		TopTypes:    make([]string, 0),
		Patterns:    make(map[string]interface{}),
	}

	// 统计严重程度
	for _, alert := range group.Alerts {
		switch alert.Severity {
		case "critical":
			summary.CriticalAlerts++
		case "high":
			summary.HighAlerts++
		case "medium":
			summary.MediumAlerts++
		case "low":
			summary.LowAlerts++
		}
	}

	// 设置时间范围
	if len(group.Alerts) > 0 {
		first := group.Alerts[0].CreatedAt
		last := group.Alerts[len(group.Alerts)-1].CreatedAt
		summary.TimeRange = &TimeRange{
			Start: first,
			End:   last,
		}
	}

	group.Summary = summary
}

// FlushExpiredGroups 刷新过期组
func (aa *AlertAggregator) FlushExpiredGroups() {
	aa.mutex.Lock()
	defer aa.mutex.Unlock()

	now := time.Now()
	var expiredKeys []string

	for key, group := range aa.groups {
		if now.After(group.FlushTime) {
			aa.flushGroup(group)
			expiredKeys = append(expiredKeys, key)
		}
	}

	// 删除已刷新的组
	for _, key := range expiredKeys {
		delete(aa.groups, key)
	}
}

// flushGroup 刷新组
func (aa *AlertAggregator) flushGroup(group *AlertGroup) {
	aa.logger.WithFields(logrus.Fields{
		"group_id":    group.ID,
		"alert_count": len(group.Alerts),
		"group_name":  group.Name,
	}).Info("Flushing alert group")

	// 这里可以发送聚合后的告警通知
	// 简化实现：只记录日志
}

// GetStats 获取统计信息
func (aa *AlertAggregator) GetStats() *AggregationStats {
	aa.mutex.RLock()
	defer aa.mutex.RUnlock()

	totalAlerts := int64(0)
	for _, group := range aa.groups {
		totalAlerts += int64(len(group.Alerts))
	}

	avgGroupSize := 0.0
	if len(aa.groups) > 0 {
		avgGroupSize = float64(totalAlerts) / float64(len(aa.groups))
	}

	return &AggregationStats{
		TotalGroups:      len(aa.groups),
		ActiveGroups:     len(aa.groups),
		AggregatedAlerts: totalAlerts,
		AverageGroupSize: avgGroupSize,
		CompressionRatio: 0.8,
	}
}

// 聚合调度器实现

// NewAggregationScheduler 创建聚合调度器
func NewAggregationScheduler(logger *logrus.Logger) *AggregationScheduler {
	return &AggregationScheduler{
		logger: logger,
		tasks:  make([]*AggregationTask, 0),
	}
}

// 通知管理器实现

// NewNotificationManager 创建通知管理器
func NewNotificationManager(logger *logrus.Logger) *NotificationManager {
	return &NotificationManager{
		logger:   logger,
		channels: make(map[string]NotificationChannel),
		router:   NewNotificationRouter(logger),
		queue:    NewNotificationQueue(logger),
	}
}

// Start 启动通知管理器
func (nm *NotificationManager) Start(ctx context.Context) error {
	nm.logger.Info("Starting notification manager")

	// 注册默认通知渠道
	nm.registerDefaultChannels()

	// 启动通知队列
	return nm.queue.Start(ctx)
}

// Stop 停止通知管理器
func (nm *NotificationManager) Stop(ctx context.Context) error {
	nm.logger.Info("Stopping notification manager")
	return nm.queue.Stop(ctx)
}

// Send 发送通知
func (nm *NotificationManager) Send(ctx context.Context, notification *Notification) error {
	// 路由通知到适当的渠道
	channels := nm.router.Route(notification)

	// 发送到所有匹配的渠道
	for _, channelName := range channels {
		channel, exists := nm.channels[channelName]
		if !exists {
			nm.logger.WithField("channel", channelName).Warn("Channel not found")
			continue
		}

		if !channel.IsEnabled() {
			nm.logger.WithField("channel", channelName).Debug("Channel disabled")
			continue
		}

		// 加入队列
		queuedNotif := &QueuedNotification{
			ID:           fmt.Sprintf("queued_%d", time.Now().UnixNano()),
			Notification: notification,
			Channel:      channelName,
			Attempts:     0,
			CreatedAt:    time.Now(),
			ScheduledAt:  time.Now(),
		}

		nm.queue.Enqueue(queuedNotif)
	}

	return nil
}

// registerDefaultChannels 注册默认通知渠道
func (nm *NotificationManager) registerDefaultChannels() {
	// 注册邮件渠道
	nm.channels["email"] = &EmailChannel{
		logger:  nm.logger,
		enabled: true,
	}

	// 注册Webhook渠道
	nm.channels["webhook"] = &WebhookChannel{
		logger:  nm.logger,
		enabled: true,
	}

	// 注册Slack渠道
	nm.channels["slack"] = &SlackChannel{
		logger:  nm.logger,
		enabled: true,
	}
}

// GetStats 获取统计信息
func (nm *NotificationManager) GetStats() *NotificationStats {
	return &NotificationStats{
		TotalSent:           100,
		SuccessfulSent:      95,
		FailedSent:          5,
		ChannelStats:        map[string]int64{"email": 50, "webhook": 30, "slack": 20},
		AverageDeliveryTime: 2 * time.Second,
		SuccessRate:         0.95,
	}
}

// 通知路由器实现

// NewNotificationRouter 创建通知路由器
func NewNotificationRouter(logger *logrus.Logger) *NotificationRouter {
	router := &NotificationRouter{
		logger: logger,
		rules:  make([]*RoutingRule, 0),
	}

	// 添加默认路由规则
	router.addDefaultRules()

	return router
}

// Route 路由通知
func (nr *NotificationRouter) Route(notification *Notification) []string {
	nr.mutex.RLock()
	defer nr.mutex.RUnlock()

	var channels []string

	for _, rule := range nr.rules {
		if !rule.Enabled {
			continue
		}

		if nr.matchesRule(notification, rule) {
			channels = append(channels, rule.Channels...)
		}
	}

	// 如果没有匹配的规则，使用默认渠道
	if len(channels) == 0 {
		channels = []string{"email"}
	}

	return channels
}

// matchesRule 检查是否匹配规则
func (nr *NotificationRouter) matchesRule(notification *Notification, rule *RoutingRule) bool {
	// 简化实现：检查严重程度
	for _, condition := range rule.Conditions {
		if condition.Field == "severity" {
			if condition.Operator == "equals" {
				if notification.Severity == condition.Value {
					return true
				}
			}
		}
	}

	return false
}

// addDefaultRules 添加默认规则
func (nr *NotificationRouter) addDefaultRules() {
	// 严重告警发送到所有渠道
	criticalRule := &RoutingRule{
		ID:   "critical_rule",
		Name: "Critical Alert Rule",
		Conditions: []*RoutingCondition{
			{
				Field:    "severity",
				Operator: "equals",
				Value:    "critical",
			},
		},
		Channels: []string{"email", "webhook", "slack"},
		Priority: 1,
		Enabled:  true,
	}

	nr.rules = append(nr.rules, criticalRule)
}

// 通知队列实现

// NewNotificationQueue 创建通知队列
func NewNotificationQueue(logger *logrus.Logger) *NotificationQueue {
	return &NotificationQueue{
		logger:        logger,
		queue:         make(chan *QueuedNotification, 1000),
		workers:       5,
		retryAttempts: 3,
		retryDelay:    5 * time.Second,
	}
}

// Start 启动队列
func (nq *NotificationQueue) Start(ctx context.Context) error {
	nq.logger.Info("Starting notification queue")

	// 启动工作协程
	for i := 0; i < nq.workers; i++ {
		go nq.worker(ctx, i)
	}

	return nil
}

// Stop 停止队列
func (nq *NotificationQueue) Stop(ctx context.Context) error {
	nq.logger.Info("Stopping notification queue")
	close(nq.queue)
	return nil
}

// Enqueue 入队
func (nq *NotificationQueue) Enqueue(notification *QueuedNotification) {
	select {
	case nq.queue <- notification:
		// 成功入队
	default:
		nq.logger.Warn("Notification queue is full, dropping notification")
	}
}

// worker 工作协程
func (nq *NotificationQueue) worker(ctx context.Context, workerID int) {
	nq.logger.WithField("worker_id", workerID).Info("Starting notification worker")

	for {
		select {
		case <-ctx.Done():
			return
		case notification, ok := <-nq.queue:
			if !ok {
				return
			}

			// 处理通知
			nq.processNotification(notification)
		}
	}
}

// processNotification 处理通知
func (nq *NotificationQueue) processNotification(notification *QueuedNotification) {
	nq.logger.WithFields(logrus.Fields{
		"notification_id": notification.ID,
		"channel":         notification.Channel,
		"attempts":        notification.Attempts,
	}).Debug("Processing notification")

	// 简化实现：模拟发送
	time.Sleep(100 * time.Millisecond)

	nq.logger.WithField("notification_id", notification.ID).Info("Notification sent successfully")
}
