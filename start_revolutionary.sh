#!/bin/bash

# 革命性AI运维管理平台启动脚本
# Revolutionary AI Operations Management Platform Startup Script

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 显示横幅
show_banner() {
    echo -e "${PURPLE}"
    echo "╔══════════════════════════════════════════════════════════════════════════════╗"
    echo "║                    🚀 革命性AI运维管理平台 v2.0                              ║"
    echo "║                Revolutionary AI Operations Management Platform               ║"
    echo "║                                                                              ║"
    echo "║  🧠 AI大脑中枢    🎯 意图识别引擎    🌐 多模态交互    🔧 分布式执行          ║"
    echo "║  🛡️ 企业级安全    📊 智能监控      🔮 预测运维      ⚡ 实时响应            ║"
    echo "╚══════════════════════════════════════════════════════════════════════════════╝"
    echo -e "${NC}"
}

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查环境
check_environment() {
    log_info "检查运行环境..."
    
    # 检查Go
    if ! command -v go &> /dev/null; then
        log_error "Go 未安装，请先安装 Go 1.21+"
        exit 1
    fi
    
    # 检查Go版本
    GO_VERSION=$(go version | grep -oE 'go[0-9]+\.[0-9]+' | sed 's/go//')
    if [[ $(echo "$GO_VERSION < 1.21" | bc -l) -eq 1 ]]; then
        log_error "Go版本过低，需要1.21+，当前版本: $GO_VERSION"
        exit 1
    fi
    
    log_success "Go环境检查通过 (版本: $GO_VERSION)"
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    if [ -z "$AIOPS_DEEPSEEK_API_KEY" ]; then
        log_warning "AIOPS_DEEPSEEK_API_KEY 未设置"
        read -p "请输入DeepSeek API Key: " api_key
        export AIOPS_DEEPSEEK_API_KEY="$api_key"
    fi
    
    if [ -z "$AIOPS_JWT_SECRET" ]; then
        log_warning "AIOPS_JWT_SECRET 未设置，使用默认值"
        export AIOPS_JWT_SECRET="revolutionary-aiops-jwt-secret-key-2024"
    fi
    
    if [ -z "$AIOPS_ENCRYPTION_KEY" ]; then
        log_warning "AIOPS_ENCRYPTION_KEY 未设置，使用默认值"
        export AIOPS_ENCRYPTION_KEY="revolutionary-aiops-encryption-32b"
    fi
    
    log_success "环境变量检查完成"
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    mkdir -p data
    mkdir -p logs
    mkdir -p config
    mkdir -p web/static
    mkdir -p web/templates
    
    log_success "目录创建完成"
}

# 检查配置文件
check_config() {
    log_info "检查配置文件..."
    
    if [ ! -f "config/revolutionary_config.yaml" ]; then
        log_warning "配置文件不存在，将使用默认配置"
    else
        log_success "配置文件检查通过"
    fi
}

# 构建应用
build_app() {
    log_info "构建革命性AI运维平台..."
    
    # 清理之前的构建
    rm -f revolutionary-aiops
    
    # 设置构建信息
    BUILD_TIME=$(date -u +%Y-%m-%dT%H:%M:%SZ)
    GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    # 构建应用
    go build -ldflags="-w -s -X main.version=2.0.0 -X main.buildTime=$BUILD_TIME -X main.gitCommit=$GIT_COMMIT" \
        -o revolutionary-aiops \
        ./cmd/revolutionary_aiops
    
    if [ $? -eq 0 ]; then
        log_success "应用构建成功"
    else
        log_error "应用构建失败"
        exit 1
    fi
}

# 启动应用
start_app() {
    log_info "启动革命性AI运维管理平台..."
    
    # 设置配置文件路径
    export CONFIG_PATH="./config/revolutionary_config.yaml"
    
    # 显示启动信息
    echo -e "${CYAN}"
    echo "🚀 启动参数:"
    echo "   - 配置文件: $CONFIG_PATH"
    echo "   - 端口: 8080"
    echo "   - 环境: development"
    echo "   - 版本: 2.0.0"
    echo -e "${NC}"
    
    # 启动应用
    ./revolutionary-aiops
}

# 显示帮助信息
show_help() {
    echo "革命性AI运维管理平台启动脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  --build-only    仅构建应用，不启动"
    echo "  --no-build      跳过构建，直接启动"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "环境变量:"
    echo "  AIOPS_DEEPSEEK_API_KEY    DeepSeek API密钥 (必需)"
    echo "  AIOPS_JWT_SECRET          JWT密钥"
    echo "  AIOPS_ENCRYPTION_KEY      加密密钥"
    echo ""
    echo "示例:"
    echo "  $0                        # 构建并启动"
    echo "  $0 --build-only          # 仅构建"
    echo "  $0 --no-build            # 跳过构建直接启动"
}

# 主函数
main() {
    show_banner
    
    # 解析命令行参数
    BUILD_ONLY=false
    NO_BUILD=false
    
    while [[ $# -gt 0 ]]; do
        case $1 in
            --build-only)
                BUILD_ONLY=true
                shift
                ;;
            --no-build)
                NO_BUILD=true
                shift
                ;;
            --help|-h)
                show_help
                exit 0
                ;;
            *)
                log_error "未知选项: $1"
                show_help
                exit 1
                ;;
        esac
    done
    
    # 执行检查
    check_environment
    check_env_vars
    create_directories
    check_config
    
    # 构建应用
    if [ "$NO_BUILD" = false ]; then
        build_app
    fi
    
    # 如果只是构建，则退出
    if [ "$BUILD_ONLY" = true ]; then
        log_success "构建完成，退出"
        exit 0
    fi
    
    # 启动应用
    start_app
}

# 信号处理
trap 'echo -e "\n${YELLOW}[INFO]${NC} 正在优雅关闭..."; exit 0' INT TERM

# 运行主函数
main "$@"
