# AI对话运维管理平台 - Docker Compose配置
version: '3.8'

services:
  # =============================================================================
  # 应用服务
  # =============================================================================
  aiops-platform:
    build:
      context: .
      dockerfile: Dockerfile
      args:
        - GO_VERSION=1.21
    container_name: aiops-platform
    restart: unless-stopped
    ports:
      - "${PORT:-8080}:8080"
      - "${METRICS_PORT:-9090}:9090"
    environment:
      - ENV=${ENV:-production}
      - DEBUG=${DEBUG:-false}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - DB_PATH=/data/aiops.db
      - DEEPSEEK_API_KEY=${DEEPSEEK_API_KEY}
      - DEEPSEEK_API_URL=${DEEPSEEK_API_URL:-https://api.deepseek.com}
      - DEEPSEEK_MODEL=${DEEPSEEK_MODEL:-deepseek-chat}
      - JWT_SECRET=${JWT_SECRET}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
    volumes:
      - aiops_data:/data
      - aiops_logs:/logs
      - aiops_backups:/backups
      - ./configs:/configs:ro
    depends_on:
      - redis
    networks:
      - aiops_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # =============================================================================
  # 负载均衡器
  # =============================================================================
  nginx:
    image: nginx:alpine
    container_name: aiops-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - aiops-platform
    networks:
      - aiops_network
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # Redis缓存
  # =============================================================================
  redis:
    image: redis:7-alpine
    container_name: aiops-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: >
      redis-server
      --appendonly yes
      --appendfsync everysec
      --save 900 1
      --save 300 10
      --save 60 10000
      --maxmemory 256mb
      --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/etc/redis/redis.conf:ro
    networks:
      - aiops_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # =============================================================================
  # 监控服务
  # =============================================================================
  prometheus:
    image: prom/prometheus:latest
    container_name: aiops-prometheus
    restart: unless-stopped
    ports:
      - "9091:9090"
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=15d'
      - '--web.enable-lifecycle'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - ./monitoring/alert_rules.yml:/etc/prometheus/alert_rules.yml:ro
      - prometheus_data:/prometheus
    depends_on:
      - aiops-platform
    networks:
      - aiops_network

  grafana:
    image: grafana/grafana:latest
    container_name: aiops-grafana
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_PASSWORD:-admin123}
      - GF_USERS_ALLOW_SIGN_UP=false
      - GF_INSTALL_PLUGINS=grafana-clock-panel,grafana-simple-json-datasource
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    depends_on:
      - prometheus
    networks:
      - aiops_network

  # =============================================================================
  # 日志收集
  # =============================================================================
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.11.0
    container_name: aiops-filebeat
    restart: unless-stopped
    user: root
    volumes:
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - aiops_logs:/var/log/aiops:ro
      - nginx_logs:/var/log/nginx:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    depends_on:
      - aiops-platform
    networks:
      - aiops_network

  # =============================================================================
  # 服务发现 (可选)
  # =============================================================================
  consul:
    image: consul:latest
    container_name: aiops-consul
    restart: unless-stopped
    ports:
      - "8500:8500"
      - "8600:8600/udp"
    command: >
      consul agent
      -server
      -bootstrap-expect=1
      -ui
      -bind=0.0.0.0
      -client=0.0.0.0
      -datacenter=dc1
    volumes:
      - consul_data:/consul/data
    networks:
      - aiops_network
    profiles:
      - consul

# =============================================================================
# 网络配置
# =============================================================================
networks:
  aiops_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# =============================================================================
# 数据卷配置
# =============================================================================
volumes:
  # 应用数据
  aiops_data:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./data
  
  # 日志数据
  aiops_logs:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./logs
  
  # 备份数据
  aiops_backups:
    driver: local
    driver_opts:
      type: none
      o: bind
      device: ./backups
  
  # Nginx日志
  nginx_logs:
    driver: local
  
  # Redis数据
  redis_data:
    driver: local
  
  # Prometheus数据
  prometheus_data:
    driver: local
  
  # Grafana数据
  grafana_data:
    driver: local
  
  # Consul数据
  consul_data:
    driver: local
