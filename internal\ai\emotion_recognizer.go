package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// EmotionRecognizer 情感识别器
type EmotionRecognizer struct {
	logger          *logrus.Logger
	deepseekService interface {
		Chat(ctx context.Context, messages []EmotionMessage) (*EmotionChatResponse, error)
	}
	config          *EmotionalIntelligenceConfig
	emotionPatterns map[EmotionType]*EmotionPattern
	contextAnalyzer *ContextAnalyzer
}

// EmotionPattern 情感模式
type EmotionPattern struct {
	Keywords   []string           `json:"keywords"`
	Phrases    []string           `json:"phrases"`
	Patterns   []*regexp.Regexp   `json:"-"`
	Weight     float64            `json:"weight"`
	Context    []string           `json:"context"`
	Indicators []EmotionIndicator `json:"indicators"`
}

// EmotionIndicator 情感指标
type EmotionIndicator struct {
	Type        string  `json:"type"` // punctuation, capitalization, repetition
	Pattern     string  `json:"pattern"`
	Weight      float64 `json:"weight"`
	Description string  `json:"description"`
}

// ContextAnalyzer 上下文分析器
type ContextAnalyzer struct {
	logger *logrus.Logger
}

// NewEmotionRecognizer 创建情感识别器
func NewEmotionRecognizer(
	logger *logrus.Logger,
	deepseekService interface {
		Chat(ctx context.Context, messages []EmotionMessage) (*EmotionChatResponse, error)
	},
	config *EmotionalIntelligenceConfig,
) *EmotionRecognizer {
	recognizer := &EmotionRecognizer{
		logger:          logger,
		deepseekService: deepseekService,
		config:          config,
		contextAnalyzer: &ContextAnalyzer{logger: logger},
	}

	// 初始化情感模式
	recognizer.initializeEmotionPatterns()

	return recognizer
}

// RecognizeEmotion 识别情感
func (er *EmotionRecognizer) RecognizeEmotion(
	ctx context.Context,
	message string,
	context map[string]interface{},
) (*EmotionState, error) {
	start := time.Now()

	er.logger.WithFields(logrus.Fields{
		"message": message,
		"context": context,
	}).Debug("开始情感识别")

	// 1. 基于规则的快速识别
	ruleBasedEmotion := er.recognizeByRules(message)

	// 2. 如果启用了DeepSeek情感分析，使用AI增强识别
	var aiEnhancedEmotion *EmotionState
	if er.config.EnableDeepSeekEmotion && er.deepseekService != nil {
		var err error
		aiEnhancedEmotion, err = er.recognizeByAI(ctx, message, context)
		if err != nil {
			er.logger.WithError(err).Warn("AI情感识别失败，使用规则识别结果")
		}
	}

	// 3. 融合识别结果
	finalEmotion := er.fuseEmotionResults(ruleBasedEmotion, aiEnhancedEmotion)

	// 4. 上下文增强
	finalEmotion = er.enhanceWithContext(finalEmotion, context)

	processingTime := time.Since(start)
	er.logger.WithFields(logrus.Fields{
		"primary_emotion": finalEmotion.PrimaryEmotion,
		"intensity":       finalEmotion.Intensity,
		"confidence":      finalEmotion.Confidence,
		"processing_time": processingTime,
	}).Debug("情感识别完成")

	return finalEmotion, nil
}

// recognizeByRules 基于规则的情感识别
func (er *EmotionRecognizer) recognizeByRules(message string) *EmotionState {
	messageLower := strings.ToLower(message)
	emotionScores := make(map[EmotionType]float64)

	// 遍历所有情感模式
	for emotionType, pattern := range er.emotionPatterns {
		score := er.calculateEmotionScore(messageLower, pattern)
		if score > 0 {
			emotionScores[emotionType] = score
		}
	}

	// 找到最高分的情感
	var primaryEmotion EmotionType = EmotionNeutral
	var maxScore float64 = 0
	var secondaryEmotion EmotionType = EmotionNeutral
	var secondMaxScore float64 = 0

	for emotion, score := range emotionScores {
		if score > maxScore {
			secondaryEmotion = primaryEmotion
			secondMaxScore = maxScore
			primaryEmotion = emotion
			maxScore = score
		} else if score > secondMaxScore {
			secondaryEmotion = emotion
			secondMaxScore = score
		}
	}

	// 计算置信度和强度
	confidence := er.calculateConfidence(maxScore, secondMaxScore)
	intensity := er.calculateIntensity(message, primaryEmotion)

	return &EmotionState{
		PrimaryEmotion:   primaryEmotion,
		SecondaryEmotion: secondaryEmotion,
		Intensity:        intensity,
		Confidence:       confidence,
		Context:          "rule_based",
		Timestamp:        time.Now(),
		Metadata: map[string]interface{}{
			"rule_scores":        emotionScores,
			"max_score":          maxScore,
			"second_score":       secondMaxScore,
			"recognition_method": "rule_based",
		},
	}
}

// recognizeByAI 基于AI的情感识别
func (er *EmotionRecognizer) recognizeByAI(
	ctx context.Context,
	message string,
	context map[string]interface{},
) (*EmotionState, error) {
	// 构建情感分析提示词
	systemPrompt := er.buildEmotionAnalysisPrompt()
	userPrompt := fmt.Sprintf("请分析以下消息的情感：\n\n消息：%s\n\n上下文：%v", message, context)

	// 调用DeepSeek API
	messages := []EmotionMessage{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userPrompt},
	}

	response, err := er.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("DeepSeek API调用失败: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("DeepSeek返回空响应")
	}

	// 解析AI响应
	content := response.Choices[0].Message.Content
	return er.parseAIEmotionResponse(content)
}

// buildEmotionAnalysisPrompt 构建情感分析提示词
func (er *EmotionRecognizer) buildEmotionAnalysisPrompt() string {
	return `你是一个专业的情感分析专家，专门分析中文文本中的情感状态。

请分析用户消息中的情感，并返回JSON格式的结果：

{
  "primary_emotion": "主要情感类型",
  "secondary_emotion": "次要情感类型",
  "intensity": 0.8,
  "confidence": 0.9,
  "context": "分析上下文",
  "reasoning": "分析推理过程",
  "indicators": ["情感指标1", "情感指标2"]
}

支持的情感类型：
- joy: 喜悦、高兴、满意
- sadness: 悲伤、失落、沮丧
- anger: 愤怒、生气、不满
- fear: 恐惧、担心、紧张
- surprise: 惊讶、意外
- disgust: 厌恶、反感
- neutral: 中性、平静
- frustration: 挫折、烦躁
- excitement: 兴奋、激动
- anxiety: 焦虑、不安
- relief: 放松、安心
- confidence: 自信、确定

分析要点：
1. 关注关键词和语气词
2. 考虑标点符号的使用
3. 注意重复字符和大写字母
4. 分析句式结构和语调
5. 考虑运维场景的特殊性

请确保返回有效的JSON格式。`
}

// parseAIEmotionResponse 解析AI情感响应
func (er *EmotionRecognizer) parseAIEmotionResponse(content string) (*EmotionState, error) {
	// 提取JSON内容
	jsonContent := er.extractJSONFromResponse(content)
	if jsonContent == "" {
		return nil, fmt.Errorf("无法从AI响应中提取JSON")
	}

	// 解析JSON
	var aiResult struct {
		PrimaryEmotion   string   `json:"primary_emotion"`
		SecondaryEmotion string   `json:"secondary_emotion"`
		Intensity        float64  `json:"intensity"`
		Confidence       float64  `json:"confidence"`
		Context          string   `json:"context"`
		Reasoning        string   `json:"reasoning"`
		Indicators       []string `json:"indicators"`
	}

	if err := json.Unmarshal([]byte(jsonContent), &aiResult); err != nil {
		return nil, fmt.Errorf("解析AI情感响应失败: %w", err)
	}

	// 验证和转换情感类型
	primaryEmotion := er.validateEmotionType(aiResult.PrimaryEmotion)
	secondaryEmotion := er.validateEmotionType(aiResult.SecondaryEmotion)

	return &EmotionState{
		PrimaryEmotion:   primaryEmotion,
		SecondaryEmotion: secondaryEmotion,
		Intensity:        er.clampFloat(aiResult.Intensity, 0.0, 1.0),
		Confidence:       er.clampFloat(aiResult.Confidence, 0.0, 1.0),
		Context:          "ai_enhanced",
		Timestamp:        time.Now(),
		Metadata: map[string]interface{}{
			"ai_reasoning":       aiResult.Reasoning,
			"ai_indicators":      aiResult.Indicators,
			"recognition_method": "ai_enhanced",
		},
	}, nil
}

// fuseEmotionResults 融合情感识别结果
func (er *EmotionRecognizer) fuseEmotionResults(
	ruleResult *EmotionState,
	aiResult *EmotionState,
) *EmotionState {
	if aiResult == nil {
		return ruleResult
	}

	// 加权融合
	ruleWeight := 0.3
	aiWeight := 0.7

	// 如果两种方法识别出相同的主要情感，增加置信度
	if ruleResult.PrimaryEmotion == aiResult.PrimaryEmotion {
		finalConfidence := ruleResult.Confidence*ruleWeight + aiResult.Confidence*aiWeight + 0.2
		finalIntensity := ruleResult.Intensity*ruleWeight + aiResult.Intensity*aiWeight

		return &EmotionState{
			PrimaryEmotion:   aiResult.PrimaryEmotion,
			SecondaryEmotion: aiResult.SecondaryEmotion,
			Intensity:        er.clampFloat(finalIntensity, 0.0, 1.0),
			Confidence:       er.clampFloat(finalConfidence, 0.0, 1.0),
			Context:          "fused_analysis",
			Timestamp:        time.Now(),
			Metadata: map[string]interface{}{
				"rule_emotion":       ruleResult.PrimaryEmotion,
				"ai_emotion":         aiResult.PrimaryEmotion,
				"agreement":          true,
				"recognition_method": "fused",
			},
		}
	}

	// 如果不一致，使用AI结果但降低置信度
	finalConfidence := aiResult.Confidence * 0.8
	return &EmotionState{
		PrimaryEmotion:   aiResult.PrimaryEmotion,
		SecondaryEmotion: ruleResult.PrimaryEmotion, // 规则结果作为次要情感
		Intensity:        aiResult.Intensity,
		Confidence:       finalConfidence,
		Context:          "fused_analysis",
		Timestamp:        time.Now(),
		Metadata: map[string]interface{}{
			"rule_emotion":       ruleResult.PrimaryEmotion,
			"ai_emotion":         aiResult.PrimaryEmotion,
			"agreement":          false,
			"recognition_method": "fused",
		},
	}
}

// enhanceWithContext 使用上下文增强情感识别
func (er *EmotionRecognizer) enhanceWithContext(
	emotion *EmotionState,
	context map[string]interface{},
) *EmotionState {
	if context == nil {
		return emotion
	}

	// 根据上下文调整情感强度
	if previousEmotion, exists := context["previous_emotion"]; exists {
		if prevEmotion, ok := previousEmotion.(string); ok {
			if prevEmotion == string(emotion.PrimaryEmotion) {
				// 连续相同情感，增强强度
				emotion.Intensity = er.clampFloat(emotion.Intensity*1.2, 0.0, 1.0)
			}
		}
	}

	// 根据时间上下文调整
	if timeContext, exists := context["time_of_day"]; exists {
		if timeStr, ok := timeContext.(string); ok {
			emotion = er.adjustEmotionByTime(emotion, timeStr)
		}
	}

	return emotion
}

// initializeEmotionPatterns 初始化情感模式
func (er *EmotionRecognizer) initializeEmotionPatterns() {
	er.emotionPatterns = map[EmotionType]*EmotionPattern{
		EmotionJoy: {
			Keywords: []string{"好", "棒", "太好了", "成功", "完成", "解决了", "谢谢", "感谢"},
			Phrases:  []string{"太好了", "非常好", "很棒", "成功了", "解决了"},
			Weight:   1.0,
		},
		EmotionFrustration: {
			Keywords: []string{"错误", "失败", "不行", "问题", "故障", "坏了", "崩溃", "卡住"},
			Phrases:  []string{"出错了", "不工作", "有问题", "失败了"},
			Weight:   1.0,
		},
		EmotionAnxiety: {
			Keywords: []string{"急", "快", "紧急", "马上", "立即", "赶紧", "担心", "着急"},
			Phrases:  []string{"很急", "紧急情况", "马上处理", "快点"},
			Weight:   1.0,
		},
		EmotionAnger: {
			Keywords: []string{"气死", "烦死", "讨厌", "垃圾", "破", "什么鬼", "坑"},
			Phrases:  []string{"太烦了", "什么破", "真是的"},
			Weight:   1.0,
		},
		EmotionRelief: {
			Keywords: []string{"终于", "总算", "松口气", "安心", "放心", "好了"},
			Phrases:  []string{"终于好了", "总算解决", "松了口气"},
			Weight:   1.0,
		},
	}

	// 编译正则表达式模式
	for _, pattern := range er.emotionPatterns {
		for _, phrase := range pattern.Phrases {
			if regex, err := regexp.Compile(phrase); err == nil {
				pattern.Patterns = append(pattern.Patterns, regex)
			}
		}
	}
}

// 辅助方法
func (er *EmotionRecognizer) calculateEmotionScore(message string, pattern *EmotionPattern) float64 {
	score := 0.0

	// 关键词匹配
	for _, keyword := range pattern.Keywords {
		if strings.Contains(message, keyword) {
			score += 0.3
		}
	}

	// 短语匹配
	for _, regex := range pattern.Patterns {
		if regex.MatchString(message) {
			score += 0.5
		}
	}

	return score * pattern.Weight
}

func (er *EmotionRecognizer) calculateConfidence(maxScore, secondMaxScore float64) float64 {
	if maxScore == 0 {
		return 0.5
	}
	confidence := maxScore / (maxScore + secondMaxScore + 0.1)
	return er.clampFloat(confidence, 0.0, 1.0)
}

func (er *EmotionRecognizer) calculateIntensity(message string, emotion EmotionType) float64 {
	intensity := 0.5

	// 基于标点符号调整强度
	if strings.Contains(message, "!!") || strings.Contains(message, "！！") {
		intensity += 0.3
	} else if strings.Contains(message, "!") || strings.Contains(message, "！") {
		intensity += 0.2
	}

	// 基于重复字符调整强度
	if matched, _ := regexp.MatchString(`(.)\1{2,}`, message); matched {
		intensity += 0.2
	}

	return er.clampFloat(intensity, 0.0, 1.0)
}

func (er *EmotionRecognizer) validateEmotionType(emotionStr string) EmotionType {
	switch emotionStr {
	case "joy", "happiness", "happy":
		return EmotionJoy
	case "sadness", "sad":
		return EmotionSadness
	case "anger", "angry":
		return EmotionAnger
	case "fear", "afraid":
		return EmotionFear
	case "surprise", "surprised":
		return EmotionSurprise
	case "disgust", "disgusted":
		return EmotionDisgust
	case "frustration", "frustrated":
		return EmotionFrustration
	case "excitement", "excited":
		return EmotionExcitement
	case "anxiety", "anxious":
		return EmotionAnxiety
	case "relief", "relieved":
		return EmotionRelief
	case "confidence", "confident":
		return EmotionConfidence
	default:
		return EmotionNeutral
	}
}

func (er *EmotionRecognizer) clampFloat(value, min, max float64) float64 {
	if value < min {
		return min
	}
	if value > max {
		return max
	}
	return value
}

func (er *EmotionRecognizer) extractJSONFromResponse(content string) string {
	// 查找JSON代码块
	jsonBlockRegex := regexp.MustCompile("```(?:json)?\n?(.*?)\n?```")
	matches := jsonBlockRegex.FindStringSubmatch(content)
	if len(matches) > 1 {
		return strings.TrimSpace(matches[1])
	}

	// 查找JSON对象
	jsonObjectRegex := regexp.MustCompile(`\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}`)
	match := jsonObjectRegex.FindString(content)
	return strings.TrimSpace(match)
}

func (er *EmotionRecognizer) adjustEmotionByTime(emotion *EmotionState, timeOfDay string) *EmotionState {
	// 根据时间调整情感（例如，深夜时焦虑情感可能更强）
	if timeOfDay == "night" && emotion.PrimaryEmotion == EmotionAnxiety {
		emotion.Intensity = er.clampFloat(emotion.Intensity*1.1, 0.0, 1.0)
	}
	return emotion
}
