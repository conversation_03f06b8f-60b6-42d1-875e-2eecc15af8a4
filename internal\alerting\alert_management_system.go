package alerting

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// AlertManagementSystem 告警管理系统
type AlertManagementSystem struct {
	config           *AlertManagementConfig
	logger           *logrus.Logger
	ruleManager      *AlertRuleManager
	statusTracker    *AlertStatusTracker
	workflowEngine   *AlertWorkflowEngine
	escalationEngine *AlertEscalationEngine
	dashboardManager *AlertDashboardManager
	reportGenerator  *AlertReportGenerator
	apiServer        *AlertAPIServer
	mutex            sync.RWMutex
	isRunning        bool
	stopChan         chan struct{}
}

// AlertManagementConfig 告警管理配置
type AlertManagementConfig struct {
	EnableRuleManagement     bool          `json:"enable_rule_management"`
	EnableStatusTracking     bool          `json:"enable_status_tracking"`
	EnableWorkflowEngine     bool          `json:"enable_workflow_engine"`
	EnableEscalation         bool          `json:"enable_escalation"`
	EnableDashboard          bool          `json:"enable_dashboard"`
	EnableReporting          bool          `json:"enable_reporting"`
	EnableAPI                bool          `json:"enable_api"`
	StatusUpdateInterval     time.Duration `json:"status_update_interval"`
	EscalationCheckInterval  time.Duration `json:"escalation_check_interval"`
	ReportGenerationInterval time.Duration `json:"report_generation_interval"`
	MaxConcurrentWorkflows   int           `json:"max_concurrent_workflows"`
	AlertRetentionDays       int           `json:"alert_retention_days"`
	APIPort                  int           `json:"api_port"`
}

// AlertRuleManager 告警规则管理器
type AlertRuleManager struct {
	logger    *logrus.Logger
	rules     map[string]*AlertRule
	templates []*AlertRuleTemplate
	validator *RuleValidator
	scheduler *RuleScheduler
	mutex     sync.RWMutex
}

// AlertRuleTemplate 告警规则模板
type AlertRuleTemplate struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Category    string                 `json:"category"`
	Description string                 `json:"description"`
	Template    *AlertRule             `json:"template"`
	Variables   []*TemplateVariable    `json:"variables"`
	Tags        []string               `json:"tags"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// TemplateVariable 模板变量
type TemplateVariable struct {
	Name         string              `json:"name"`
	Type         string              `json:"type"`
	DefaultValue interface{}         `json:"default_value"`
	Required     bool                `json:"required"`
	Description  string              `json:"description"`
	Validation   *VariableValidation `json:"validation"`
}

// VariableValidation 变量验证
type VariableValidation struct {
	MinValue      *float64      `json:"min_value,omitempty"`
	MaxValue      *float64      `json:"max_value,omitempty"`
	Pattern       string        `json:"pattern,omitempty"`
	AllowedValues []interface{} `json:"allowed_values,omitempty"`
}

// RuleValidator 规则验证器
type RuleValidator struct {
	logger *logrus.Logger
}

// RuleScheduler 规则调度器
type RuleScheduler struct {
	logger    *logrus.Logger
	schedules map[string]*RuleSchedule
	mutex     sync.RWMutex
}

// RuleSchedule 规则调度
type RuleSchedule struct {
	RuleID    string    `json:"rule_id"`
	Enabled   bool      `json:"enabled"`
	Schedule  string    `json:"schedule"` // cron expression
	NextRun   time.Time `json:"next_run"`
	LastRun   time.Time `json:"last_run"`
	RunCount  int64     `json:"run_count"`
	FailCount int64     `json:"fail_count"`
}

// AlertStatusTracker 告警状态跟踪器
type AlertStatusTracker struct {
	logger        *logrus.Logger
	statusHistory map[string][]*AlertStatusChange
	metrics       *StatusMetrics
	analyzer      *StatusAnalyzer
	mutex         sync.RWMutex
}

// AlertStatusChange 告警状态变更
type AlertStatusChange struct {
	AlertID   string                 `json:"alert_id"`
	OldStatus string                 `json:"old_status"`
	NewStatus string                 `json:"new_status"`
	Reason    string                 `json:"reason"`
	User      string                 `json:"user"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata"`
	Duration  time.Duration          `json:"duration"`
}

// StatusMetrics 状态指标
type StatusMetrics struct {
	TotalAlerts           int64               `json:"total_alerts"`
	ActiveAlerts          int64               `json:"active_alerts"`
	ResolvedAlerts        int64               `json:"resolved_alerts"`
	AcknowledgedAlerts    int64               `json:"acknowledged_alerts"`
	SuppressedAlerts      int64               `json:"suppressed_alerts"`
	StatusDistribution    map[string]int64    `json:"status_distribution"`
	AverageResolutionTime time.Duration       `json:"average_resolution_time"`
	SeverityDistribution  map[string]int64    `json:"severity_distribution"`
	SourceDistribution    map[string]int64    `json:"source_distribution"`
	TrendData             []*StatusTrendPoint `json:"trend_data"`
}

// StatusTrendPoint 状态趋势点
type StatusTrendPoint struct {
	Timestamp time.Time `json:"timestamp"`
	Active    int64     `json:"active"`
	Resolved  int64     `json:"resolved"`
	Total     int64     `json:"total"`
}

// StatusAnalyzer 状态分析器
type StatusAnalyzer struct {
	logger *logrus.Logger
}

// AlertWorkflowEngine 告警工作流引擎
type AlertWorkflowEngine struct {
	logger    *logrus.Logger
	workflows map[string]*AlertWorkflow
	instances map[string]*WorkflowInstance
	executor  *WorkflowExecutor
	scheduler *WorkflowScheduler
	mutex     sync.RWMutex
}

// AlertWorkflow 告警工作流
type AlertWorkflow struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Version     string                 `json:"version"`
	Triggers    []*WorkflowTrigger     `json:"triggers"`
	Steps       []*WorkflowStep        `json:"steps"`
	Variables   map[string]interface{} `json:"variables"`
	Timeout     time.Duration          `json:"timeout"`
	Enabled     bool                   `json:"enabled"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// WorkflowTrigger 工作流触发器
type WorkflowTrigger struct {
	Type       string                 `json:"type"`
	Conditions []*TriggerCondition    `json:"conditions"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// TriggerCondition 触发条件
type TriggerCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Type       string                 `json:"type"`
	Action     string                 `json:"action"`
	Parameters map[string]interface{} `json:"parameters"`
	Conditions []*StepCondition       `json:"conditions"`
	OnSuccess  string                 `json:"on_success"`
	OnFailure  string                 `json:"on_failure"`
	Timeout    time.Duration          `json:"timeout"`
	RetryCount int                    `json:"retry_count"`
	RetryDelay time.Duration          `json:"retry_delay"`
}

// StepCondition 步骤条件
type StepCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// WorkflowInstance 工作流实例
type WorkflowInstance struct {
	ID          string                 `json:"id"`
	WorkflowID  string                 `json:"workflow_id"`
	AlertID     string                 `json:"alert_id"`
	Status      string                 `json:"status"`
	CurrentStep string                 `json:"current_step"`
	Variables   map[string]interface{} `json:"variables"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     *time.Time             `json:"end_time,omitempty"`
	Duration    time.Duration          `json:"duration"`
	Steps       []*StepExecution       `json:"steps"`
	Error       string                 `json:"error,omitempty"`
}

// StepExecution 步骤执行
type StepExecution struct {
	StepID    string                 `json:"step_id"`
	Status    string                 `json:"status"`
	StartTime time.Time              `json:"start_time"`
	EndTime   *time.Time             `json:"end_time,omitempty"`
	Duration  time.Duration          `json:"duration"`
	Output    map[string]interface{} `json:"output"`
	Error     string                 `json:"error,omitempty"`
	Retries   int                    `json:"retries"`
}

// WorkflowExecutor 工作流执行器
type WorkflowExecutor struct {
	logger   *logrus.Logger
	handlers map[string]StepHandler
	mutex    sync.RWMutex
}

// StepHandler 步骤处理器接口
type StepHandler interface {
	Execute(ctx context.Context, step *WorkflowStep, variables map[string]interface{}) (map[string]interface{}, error)
	GetType() string
	GetDescription() string
}

// WorkflowScheduler 工作流调度器
type WorkflowScheduler struct {
	logger  *logrus.Logger
	queue   chan *WorkflowInstance
	workers int
	mutex   sync.RWMutex
}

// AlertEscalationEngine 告警升级引擎
type AlertEscalationEngine struct {
	logger      *logrus.Logger
	policies    map[string]*EscalationPolicy
	escalations map[string]*AlertEscalation
	scheduler   *EscalationScheduler
	mutex       sync.RWMutex
}

// EscalationPolicy 升级策略
type EscalationPolicy struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Rules       []*EscalationRule      `json:"rules"`
	Enabled     bool                   `json:"enabled"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// EscalationRule 升级规则
type EscalationRule struct {
	Level       int                    `json:"level"`
	Delay       time.Duration          `json:"delay"`
	Conditions  []*EscalationCondition `json:"conditions"`
	Actions     []*EscalationAction    `json:"actions"`
	StopOnMatch bool                   `json:"stop_on_match"`
}

// EscalationCondition 升级条件
type EscalationCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// EscalationAction 升级动作
type EscalationAction struct {
	Type       string                 `json:"type"`
	Target     string                 `json:"target"`
	Parameters map[string]interface{} `json:"parameters"`
}

// AlertEscalation 告警升级
type AlertEscalation struct {
	ID        string                 `json:"id"`
	AlertID   string                 `json:"alert_id"`
	PolicyID  string                 `json:"policy_id"`
	Level     int                    `json:"level"`
	Status    string                 `json:"status"`
	StartTime time.Time              `json:"start_time"`
	NextTime  time.Time              `json:"next_time"`
	Actions   []*EscalationExecution `json:"actions"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// EscalationExecution 升级执行
type EscalationExecution struct {
	ActionID  string                 `json:"action_id"`
	Status    string                 `json:"status"`
	StartTime time.Time              `json:"start_time"`
	EndTime   *time.Time             `json:"end_time,omitempty"`
	Output    map[string]interface{} `json:"output"`
	Error     string                 `json:"error,omitempty"`
}

// EscalationScheduler 升级调度器
type EscalationScheduler struct {
	logger *logrus.Logger
	queue  chan *AlertEscalation
	mutex  sync.RWMutex
}

// AlertDashboardManager 告警仪表盘管理器
type AlertDashboardManager struct {
	logger     *logrus.Logger
	dashboards map[string]*AlertDashboard
	widgets    map[string]*DashboardWidget
	mutex      sync.RWMutex
}

// AlertDashboard 告警仪表盘
type AlertDashboard struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Layout      *DashboardLayout       `json:"layout"`
	Widgets     []*DashboardWidget     `json:"widgets"`
	Filters     []*DashboardFilter     `json:"filters"`
	RefreshRate time.Duration          `json:"refresh_rate"`
	Permissions *DashboardPermissions  `json:"permissions"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// DashboardLayout 仪表盘布局
type DashboardLayout struct {
	Type    string                 `json:"type"`
	Columns int                    `json:"columns"`
	Rows    int                    `json:"rows"`
	Config  map[string]interface{} `json:"config"`
}

// DashboardWidget 仪表盘小部件
type DashboardWidget struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Position    *WidgetPosition        `json:"position"`
	Size        *WidgetSize            `json:"size"`
	Config      map[string]interface{} `json:"config"`
	DataSource  *WidgetDataSource      `json:"data_source"`
	RefreshRate time.Duration          `json:"refresh_rate"`
}

// WidgetPosition 小部件位置
type WidgetPosition struct {
	X int `json:"x"`
	Y int `json:"y"`
}

// WidgetSize 小部件大小
type WidgetSize struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

// WidgetDataSource 小部件数据源
type WidgetDataSource struct {
	Type   string                 `json:"type"`
	Query  string                 `json:"query"`
	Config map[string]interface{} `json:"config"`
}

// DashboardFilter 仪表盘过滤器
type DashboardFilter struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
	Label    string      `json:"label"`
}

// DashboardPermissions 仪表盘权限
type DashboardPermissions struct {
	Public  bool     `json:"public"`
	Viewers []string `json:"viewers"`
	Editors []string `json:"editors"`
	Admins  []string `json:"admins"`
}

// AlertReportGenerator 告警报告生成器
type AlertReportGenerator struct {
	logger    *logrus.Logger
	templates map[string]*ReportTemplate
	scheduler *ReportScheduler
	mutex     sync.RWMutex
}

// ReportTemplate 报告模板
type ReportTemplate struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Type       string                 `json:"type"`
	Format     string                 `json:"format"`
	Sections   []*ReportSection       `json:"sections"`
	Filters    []*ReportFilter        `json:"filters"`
	Schedule   string                 `json:"schedule"`
	Recipients []string               `json:"recipients"`
	Metadata   map[string]interface{} `json:"metadata"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
}

// ReportSection 报告章节
type ReportSection struct {
	ID     string                 `json:"id"`
	Title  string                 `json:"title"`
	Type   string                 `json:"type"`
	Query  string                 `json:"query"`
	Config map[string]interface{} `json:"config"`
	Order  int                    `json:"order"`
}

// ReportFilter 报告过滤器
type ReportFilter struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
}

// ReportScheduler 报告调度器
type ReportScheduler struct {
	logger    *logrus.Logger
	schedules map[string]*ReportSchedule
	mutex     sync.RWMutex
}

// ReportSchedule 报告调度
type ReportSchedule struct {
	TemplateID string    `json:"template_id"`
	Schedule   string    `json:"schedule"`
	NextRun    time.Time `json:"next_run"`
	LastRun    time.Time `json:"last_run"`
	Enabled    bool      `json:"enabled"`
}

// AlertAPIServer 告警API服务器
type AlertAPIServer struct {
	logger *logrus.Logger
	port   int
	routes map[string]APIRoute
	mutex  sync.RWMutex
}

// APIRoute API路由
type APIRoute struct {
	Method  string                        `json:"method"`
	Path    string                        `json:"path"`
	Handler func(interface{}) interface{} `json:"-"`
	Auth    bool                          `json:"auth"`
}

// NewAlertManagementSystem 创建告警管理系统
func NewAlertManagementSystem(config *AlertManagementConfig, logger *logrus.Logger) *AlertManagementSystem {
	if config == nil {
		config = DefaultAlertManagementConfig()
	}

	system := &AlertManagementSystem{
		config:    config,
		logger:    logger,
		isRunning: false,
		stopChan:  make(chan struct{}),
	}

	// 初始化子组件
	if config.EnableRuleManagement {
		system.ruleManager = NewAlertRuleManager(logger)
	}

	if config.EnableStatusTracking {
		system.statusTracker = NewAlertStatusTracker(logger)
	}

	if config.EnableWorkflowEngine {
		system.workflowEngine = NewAlertWorkflowEngine(logger)
	}

	if config.EnableEscalation {
		system.escalationEngine = NewAlertEscalationEngine(logger)
	}

	if config.EnableDashboard {
		system.dashboardManager = NewAlertDashboardManager(logger)
	}

	if config.EnableReporting {
		system.reportGenerator = NewAlertReportGenerator(logger)
	}

	if config.EnableAPI {
		system.apiServer = NewAlertAPIServer(config.APIPort, logger)
	}

	return system
}

// DefaultAlertManagementConfig 默认告警管理配置
func DefaultAlertManagementConfig() *AlertManagementConfig {
	return &AlertManagementConfig{
		EnableRuleManagement:     true,
		EnableStatusTracking:     true,
		EnableWorkflowEngine:     true,
		EnableEscalation:         true,
		EnableDashboard:          true,
		EnableReporting:          true,
		EnableAPI:                true,
		StatusUpdateInterval:     30 * time.Second,
		EscalationCheckInterval:  1 * time.Minute,
		ReportGenerationInterval: 24 * time.Hour,
		MaxConcurrentWorkflows:   10,
		AlertRetentionDays:       90,
		APIPort:                  8080,
	}
}

// Start 启动告警管理系统
func (ams *AlertManagementSystem) Start(ctx context.Context) error {
	ams.mutex.Lock()
	defer ams.mutex.Unlock()

	if ams.isRunning {
		return fmt.Errorf("alert management system is already running")
	}

	ams.logger.Info("Starting alert management system")

	// 启动子组件
	if ams.ruleManager != nil {
		if err := ams.ruleManager.Start(ctx); err != nil {
			return fmt.Errorf("failed to start rule manager: %w", err)
		}
	}

	if ams.statusTracker != nil {
		if err := ams.statusTracker.Start(ctx); err != nil {
			return fmt.Errorf("failed to start status tracker: %w", err)
		}
	}

	if ams.workflowEngine != nil {
		if err := ams.workflowEngine.Start(ctx); err != nil {
			return fmt.Errorf("failed to start workflow engine: %w", err)
		}
	}

	if ams.escalationEngine != nil {
		if err := ams.escalationEngine.Start(ctx); err != nil {
			return fmt.Errorf("failed to start escalation engine: %w", err)
		}
	}

	if ams.dashboardManager != nil {
		if err := ams.dashboardManager.Start(ctx); err != nil {
			return fmt.Errorf("failed to start dashboard manager: %w", err)
		}
	}

	if ams.reportGenerator != nil {
		if err := ams.reportGenerator.Start(ctx); err != nil {
			return fmt.Errorf("failed to start report generator: %w", err)
		}
	}

	if ams.apiServer != nil {
		if err := ams.apiServer.Start(ctx); err != nil {
			return fmt.Errorf("failed to start API server: %w", err)
		}
	}

	// 启动后台任务
	go ams.statusUpdateRoutine(ctx)
	go ams.escalationCheckRoutine(ctx)
	go ams.reportGenerationRoutine(ctx)
	go ams.cleanupRoutine(ctx)

	ams.isRunning = true
	ams.logger.Info("Alert management system started successfully")

	return nil
}

// Stop 停止告警管理系统
func (ams *AlertManagementSystem) Stop(ctx context.Context) error {
	ams.mutex.Lock()
	defer ams.mutex.Unlock()

	if !ams.isRunning {
		return nil
	}

	ams.logger.Info("Stopping alert management system")

	// 发送停止信号
	close(ams.stopChan)

	// 停止子组件
	if ams.ruleManager != nil {
		ams.ruleManager.Stop(ctx)
	}

	if ams.statusTracker != nil {
		ams.statusTracker.Stop(ctx)
	}

	if ams.workflowEngine != nil {
		ams.workflowEngine.Stop(ctx)
	}

	if ams.escalationEngine != nil {
		ams.escalationEngine.Stop(ctx)
	}

	if ams.dashboardManager != nil {
		ams.dashboardManager.Stop(ctx)
	}

	if ams.reportGenerator != nil {
		ams.reportGenerator.Stop(ctx)
	}

	if ams.apiServer != nil {
		ams.apiServer.Stop(ctx)
	}

	ams.isRunning = false
	ams.logger.Info("Alert management system stopped")

	return nil
}

// ProcessAlert 处理告警
func (ams *AlertManagementSystem) ProcessAlert(ctx context.Context, alert *Alert) error {
	ams.mutex.RLock()
	defer ams.mutex.RUnlock()

	if !ams.isRunning {
		return fmt.Errorf("alert management system is not running")
	}

	ams.logger.WithFields(logrus.Fields{
		"alert_id":   alert.ID,
		"alert_type": alert.Type,
		"severity":   alert.Severity,
		"source":     alert.Source,
	}).Info("Processing alert in management system")

	// 1. 状态跟踪
	if ams.statusTracker != nil {
		ams.statusTracker.TrackAlert(alert)
	}

	// 2. 触发工作流
	if ams.workflowEngine != nil {
		if err := ams.workflowEngine.TriggerWorkflows(ctx, alert); err != nil {
			ams.logger.WithError(err).Error("Failed to trigger workflows")
		}
	}

	// 3. 检查升级策略
	if ams.escalationEngine != nil {
		if err := ams.escalationEngine.CheckEscalation(ctx, alert); err != nil {
			ams.logger.WithError(err).Error("Failed to check escalation")
		}
	}

	return nil
}

// UpdateAlertStatus 更新告警状态
func (ams *AlertManagementSystem) UpdateAlertStatus(ctx context.Context, alertID, newStatus, reason, user string) error {
	if ams.statusTracker == nil {
		return fmt.Errorf("status tracker not enabled")
	}

	return ams.statusTracker.UpdateStatus(alertID, newStatus, reason, user)
}

// GetAlertStatus 获取告警状态
func (ams *AlertManagementSystem) GetAlertStatus(alertID string) (*AlertStatusChange, error) {
	if ams.statusTracker == nil {
		return nil, fmt.Errorf("status tracker not enabled")
	}

	return ams.statusTracker.GetCurrentStatus(alertID)
}

// CreateRule 创建告警规则
func (ams *AlertManagementSystem) CreateRule(rule *AlertRule) error {
	if ams.ruleManager == nil {
		return fmt.Errorf("rule manager not enabled")
	}

	return ams.ruleManager.CreateRule(rule)
}

// UpdateRule 更新告警规则
func (ams *AlertManagementSystem) UpdateRule(ruleID string, rule *AlertRule) error {
	if ams.ruleManager == nil {
		return fmt.Errorf("rule manager not enabled")
	}

	return ams.ruleManager.UpdateRule(ruleID, rule)
}

// DeleteRule 删除告警规则
func (ams *AlertManagementSystem) DeleteRule(ruleID string) error {
	if ams.ruleManager == nil {
		return fmt.Errorf("rule manager not enabled")
	}

	return ams.ruleManager.DeleteRule(ruleID)
}

// GetRules 获取告警规则列表
func (ams *AlertManagementSystem) GetRules() ([]*AlertRule, error) {
	if ams.ruleManager == nil {
		return nil, fmt.Errorf("rule manager not enabled")
	}

	return ams.ruleManager.GetRules()
}

// CreateWorkflow 创建工作流
func (ams *AlertManagementSystem) CreateWorkflow(workflow *AlertWorkflow) error {
	if ams.workflowEngine == nil {
		return fmt.Errorf("workflow engine not enabled")
	}

	return ams.workflowEngine.CreateWorkflow(workflow)
}

// ExecuteWorkflow 执行工作流
func (ams *AlertManagementSystem) ExecuteWorkflow(ctx context.Context, workflowID, alertID string) (*WorkflowInstance, error) {
	if ams.workflowEngine == nil {
		return nil, fmt.Errorf("workflow engine not enabled")
	}

	return ams.workflowEngine.ExecuteWorkflow(ctx, workflowID, alertID)
}

// GetWorkflowStatus 获取工作流状态
func (ams *AlertManagementSystem) GetWorkflowStatus(instanceID string) (*WorkflowInstance, error) {
	if ams.workflowEngine == nil {
		return nil, fmt.Errorf("workflow engine not enabled")
	}

	return ams.workflowEngine.GetInstance(instanceID)
}

// CreateDashboard 创建仪表盘
func (ams *AlertManagementSystem) CreateDashboard(dashboard *AlertDashboard) error {
	if ams.dashboardManager == nil {
		return fmt.Errorf("dashboard manager not enabled")
	}

	return ams.dashboardManager.CreateDashboard(dashboard)
}

// GetDashboard 获取仪表盘
func (ams *AlertManagementSystem) GetDashboard(dashboardID string) (*AlertDashboard, error) {
	if ams.dashboardManager == nil {
		return nil, fmt.Errorf("dashboard manager not enabled")
	}

	return ams.dashboardManager.GetDashboard(dashboardID)
}

// GenerateReport 生成报告
func (ams *AlertManagementSystem) GenerateReport(templateID string, filters map[string]interface{}) ([]byte, error) {
	if ams.reportGenerator == nil {
		return nil, fmt.Errorf("report generator not enabled")
	}

	return ams.reportGenerator.GenerateReport(templateID, filters)
}

// GetSystemStatus 获取系统状态
func (ams *AlertManagementSystem) GetSystemStatus() *AlertManagementSystemStatus {
	ams.mutex.RLock()
	defer ams.mutex.RUnlock()

	status := &AlertManagementSystemStatus{
		IsRunning:  ams.isRunning,
		Config:     ams.config,
		Components: make(map[string]interface{}),
	}

	if ams.ruleManager != nil {
		status.Components["rule_manager"] = ams.ruleManager.GetStatus()
	}

	if ams.statusTracker != nil {
		status.Components["status_tracker"] = ams.statusTracker.GetMetrics()
	}

	if ams.workflowEngine != nil {
		status.Components["workflow_engine"] = ams.workflowEngine.GetStatus()
	}

	if ams.escalationEngine != nil {
		status.Components["escalation_engine"] = ams.escalationEngine.GetStatus()
	}

	if ams.dashboardManager != nil {
		status.Components["dashboard_manager"] = ams.dashboardManager.GetStatus()
	}

	if ams.reportGenerator != nil {
		status.Components["report_generator"] = ams.reportGenerator.GetStatus()
	}

	return status
}

// AlertManagementSystemStatus 告警管理系统状态
type AlertManagementSystemStatus struct {
	IsRunning  bool                   `json:"is_running"`
	Config     *AlertManagementConfig `json:"config"`
	Components map[string]interface{} `json:"components"`
	Uptime     time.Duration          `json:"uptime"`
	Version    string                 `json:"version"`
}

// 后台任务

// statusUpdateRoutine 状态更新协程
func (ams *AlertManagementSystem) statusUpdateRoutine(ctx context.Context) {
	ticker := time.NewTicker(ams.config.StatusUpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ams.stopChan:
			return
		case <-ticker.C:
			if ams.statusTracker != nil {
				ams.statusTracker.UpdateMetrics()
			}
		}
	}
}

// escalationCheckRoutine 升级检查协程
func (ams *AlertManagementSystem) escalationCheckRoutine(ctx context.Context) {
	ticker := time.NewTicker(ams.config.EscalationCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ams.stopChan:
			return
		case <-ticker.C:
			if ams.escalationEngine != nil {
				ams.escalationEngine.CheckPendingEscalations(ctx)
			}
		}
	}
}

// reportGenerationRoutine 报告生成协程
func (ams *AlertManagementSystem) reportGenerationRoutine(ctx context.Context) {
	ticker := time.NewTicker(ams.config.ReportGenerationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ams.stopChan:
			return
		case <-ticker.C:
			if ams.reportGenerator != nil {
				ams.reportGenerator.GenerateScheduledReports(ctx)
			}
		}
	}
}

// cleanupRoutine 清理协程
func (ams *AlertManagementSystem) cleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ams.stopChan:
			return
		case <-ticker.C:
			// 清理过期数据
			cutoffTime := time.Now().AddDate(0, 0, -ams.config.AlertRetentionDays)

			if ams.statusTracker != nil {
				ams.statusTracker.CleanupExpired(cutoffTime)
			}

			if ams.workflowEngine != nil {
				ams.workflowEngine.CleanupExpired(cutoffTime)
			}

			if ams.escalationEngine != nil {
				ams.escalationEngine.CleanupExpired(cutoffTime)
			}
		}
	}
}
