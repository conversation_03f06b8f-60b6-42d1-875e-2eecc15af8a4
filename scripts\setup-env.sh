#!/bin/bash
# AI运维管理平台 - 环境变量设置脚本 (Bash)
# 用于Linux/Mac环境

echo "=== AI运维管理平台环境变量设置 ==="

# 生成32字节的随机加密密钥
generate_encryption_key() {
    openssl rand -base64 32 | cut -c1-32
}

# 生成JWT密钥
generate_jwt_secret() {
    openssl rand -base64 64
}

echo "正在生成安全密钥..."

# 生成密钥
ENCRYPTION_KEY=$(generate_encryption_key)
JWT_SECRET=$(generate_jwt_secret)

echo "生成的密钥信息:"
echo "加密密钥长度: ${#ENCRYPTION_KEY} 字节"
echo "JWT密钥长度: ${#JWT_SECRET} 字符"

# 创建.env文件
cat > .env << EOF
# AI运维管理平台环境变量配置
# 自动生成于 $(date)

# 安全配置
AIOPS_ENCRYPTION_KEY=$ENCRYPTION_KEY
AIOPS_JWT_SECRET=$JWT_SECRET

# DeepSeek API配置 (请替换为真实的API密钥)
AIOPS_DEEPSEEK_API_KEY=your-deepseek-api-key-here

# 可选配置
# AIOPS_APP_ENV=production
# AIOPS_APP_DEBUG=false
# AIOPS_APP_PORT=8080
EOF

echo ""
echo "已创建 .env 文件，包含生成的密钥配置。"
echo ""
echo "请编辑 .env 文件，设置真实的 DeepSeek API 密钥:"
echo "AIOPS_DEEPSEEK_API_KEY=你的API密钥"
echo ""
echo "要加载环境变量，请运行:"
echo "source .env"
echo ""
echo "或者将以下内容添加到你的 ~/.bashrc 或 ~/.zshrc:"
echo "export AIOPS_ENCRYPTION_KEY=\"$ENCRYPTION_KEY\""
echo "export AIOPS_JWT_SECRET=\"$JWT_SECRET\""
echo "export AIOPS_DEEPSEEK_API_KEY=\"你的DeepSeek API密钥\""
echo ""
echo "设置完成！"
