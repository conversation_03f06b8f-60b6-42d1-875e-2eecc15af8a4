package agent

import (
	"context"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// TaskManager 任务管理器
type TaskManager struct {
	config        *TaskManagerConfig
	logger        *logrus.Logger
	tasks         map[string]*Task
	taskQueue     *PriorityQueue
	executors     map[string]*TaskExecutor
	resultTracker *ResultTracker
	scheduler     *TaskScheduler
	mutex         sync.RWMutex
	isRunning     bool
	stopChan      chan struct{}
	task<PERSON>han      chan *Task
	resultChan    chan *TaskResult
}

// TaskManagerConfig 任务管理器配置
type TaskManagerConfig struct {
	MaxConcurrentTasks    int           `json:"max_concurrent_tasks"`
	MaxQueueSize          int           `json:"max_queue_size"`
	TaskTimeout           time.Duration `json:"task_timeout"`
	RetryAttempts         int           `json:"retry_attempts"`
	RetryDelay            time.Duration `json:"retry_delay"`
	EnableScheduling      bool          `json:"enable_scheduling"`
	EnableResultTracking  bool          `json:"enable_result_tracking"`
	ResultRetentionHours  int           `json:"result_retention_hours"`
	HealthCheckInterval   time.Duration `json:"health_check_interval"`
	MetricsUpdateInterval time.Duration `json:"metrics_update_interval"`
}

// Task 任务定义
type Task struct {
	ID              string                 `json:"id"`
	Type            string                 `json:"type"` // command, script, workflow
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	Command         string                 `json:"command"`
	Args            []string               `json:"args"`
	WorkingDir      string                 `json:"working_dir"`
	Environment     map[string]string      `json:"environment"`
	Priority        int                    `json:"priority"` // 1-10, 10最高
	Status          string                 `json:"status"`   // pending, queued, running, completed, failed, cancelled, retry
	UserID          int64                  `json:"user_id"`
	SessionID       string                 `json:"session_id"`
	CreatedAt       time.Time              `json:"created_at"`
	ScheduledAt     *time.Time             `json:"scheduled_at,omitempty"`
	StartedAt       *time.Time             `json:"started_at,omitempty"`
	CompletedAt     *time.Time             `json:"completed_at,omitempty"`
	Timeout         time.Duration          `json:"timeout"`
	RetryCount      int                    `json:"retry_count"`
	MaxRetries      int                    `json:"max_retries"`
	Dependencies    []string               `json:"dependencies,omitempty"`
	Tags            []string               `json:"tags,omitempty"`
	Metadata        map[string]interface{} `json:"metadata"`
	ExecutorID      string                 `json:"executor_id,omitempty"`
	SandboxID       string                 `json:"sandbox_id,omitempty"`
	PermissionCheck *PermissionCheck       `json:"permission_check,omitempty"`
	mutex           sync.RWMutex
}

// TaskResult 任务结果
type TaskResult struct {
	TaskID        string                  `json:"task_id"`
	ExecutorID    string                  `json:"executor_id"`
	Success       bool                    `json:"success"`
	ExitCode      int                     `json:"exit_code"`
	Stdout        string                  `json:"stdout"`
	Stderr        string                  `json:"stderr"`
	Error         string                  `json:"error,omitempty"`
	Duration      time.Duration           `json:"duration"`
	ResourceUsage *ExecutionResourceUsage `json:"resource_usage,omitempty"`
	StartTime     time.Time               `json:"start_time"`
	EndTime       time.Time               `json:"end_time"`
	Metadata      map[string]interface{}  `json:"metadata"`
}

// PriorityQueue 优先级队列
type PriorityQueue struct {
	tasks []*Task
	mutex sync.RWMutex
}

// TaskExecutor 任务执行器
type TaskExecutor struct {
	ID             string                 `json:"id"`
	Type           string                 `json:"type"`   // local, sandbox, remote
	Status         string                 `json:"status"` // idle, busy, error, offline
	CurrentTask    *Task                  `json:"current_task,omitempty"`
	Capabilities   []string               `json:"capabilities"`
	MaxConcurrency int                    `json:"max_concurrency"`
	ActiveTasks    int                    `json:"active_tasks"`
	TotalExecuted  int64                  `json:"total_executed"`
	SuccessRate    float64                `json:"success_rate"`
	AvgDuration    time.Duration          `json:"avg_duration"`
	LastActivity   time.Time              `json:"last_activity"`
	HealthStatus   string                 `json:"health_status"` // healthy, degraded, unhealthy
	Config         map[string]interface{} `json:"config"`
	mutex          sync.RWMutex
}

// ResultTracker 结果跟踪器
type ResultTracker struct {
	results       map[string]*TaskResult
	resultHistory []*TaskResult
	metrics       *TaskMetrics
	mutex         sync.RWMutex
}

// TaskScheduler 任务调度器
type TaskScheduler struct {
	scheduledTasks map[string]*ScheduledTask
	cronJobs       map[string]*CronJob
	mutex          sync.RWMutex
}

// ScheduledTask 计划任务
type ScheduledTask struct {
	ID         string        `json:"id"`
	Task       *Task         `json:"task"`
	ScheduleAt time.Time     `json:"schedule_at"`
	Recurring  bool          `json:"recurring"`
	Interval   time.Duration `json:"interval,omitempty"`
	CronExpr   string        `json:"cron_expr,omitempty"`
	NextRun    time.Time     `json:"next_run"`
	LastRun    *time.Time    `json:"last_run,omitempty"`
	Enabled    bool          `json:"enabled"`
}

// CronJob 定时任务
type CronJob struct {
	ID         string     `json:"id"`
	Name       string     `json:"name"`
	Expression string     `json:"expression"`
	Task       *Task      `json:"task"`
	NextRun    time.Time  `json:"next_run"`
	LastRun    *time.Time `json:"last_run,omitempty"`
	RunCount   int64      `json:"run_count"`
	Enabled    bool       `json:"enabled"`
	CreatedAt  time.Time  `json:"created_at"`
}

// TaskMetrics 任务指标
type TaskMetrics struct {
	TotalTasks        int64         `json:"total_tasks"`
	CompletedTasks    int64         `json:"completed_tasks"`
	FailedTasks       int64         `json:"failed_tasks"`
	CancelledTasks    int64         `json:"cancelled_tasks"`
	QueuedTasks       int           `json:"queued_tasks"`
	RunningTasks      int           `json:"running_tasks"`
	AvgExecutionTime  time.Duration `json:"avg_execution_time"`
	SuccessRate       float64       `json:"success_rate"`
	ThroughputPerHour float64       `json:"throughput_per_hour"`
	LastUpdateTime    time.Time     `json:"last_update_time"`
}

// NewTaskManager 创建任务管理器
func NewTaskManager(config *TaskManagerConfig, logger *logrus.Logger) *TaskManager {
	if config == nil {
		config = DefaultTaskManagerConfig()
	}

	return &TaskManager{
		config:        config,
		logger:        logger,
		tasks:         make(map[string]*Task),
		taskQueue:     NewPriorityQueue(),
		executors:     make(map[string]*TaskExecutor),
		resultTracker: NewResultTracker(),
		scheduler:     NewTaskScheduler(),
		isRunning:     false,
		stopChan:      make(chan struct{}),
		taskChan:      make(chan *Task, config.MaxQueueSize),
		resultChan:    make(chan *TaskResult, config.MaxQueueSize),
	}
}

// DefaultTaskManagerConfig 默认任务管理器配置
func DefaultTaskManagerConfig() *TaskManagerConfig {
	return &TaskManagerConfig{
		MaxConcurrentTasks:    10,
		MaxQueueSize:          1000,
		TaskTimeout:           5 * time.Minute,
		RetryAttempts:         3,
		RetryDelay:            30 * time.Second,
		EnableScheduling:      true,
		EnableResultTracking:  true,
		ResultRetentionHours:  24,
		HealthCheckInterval:   30 * time.Second,
		MetricsUpdateInterval: 1 * time.Minute,
	}
}

// NewPriorityQueue 创建优先级队列
func NewPriorityQueue() *PriorityQueue {
	return &PriorityQueue{
		tasks: make([]*Task, 0),
	}
}

// NewResultTracker 创建结果跟踪器
func NewResultTracker() *ResultTracker {
	return &ResultTracker{
		results:       make(map[string]*TaskResult),
		resultHistory: make([]*TaskResult, 0),
		metrics: &TaskMetrics{
			LastUpdateTime: time.Now(),
		},
	}
}

// NewTaskScheduler 创建任务调度器
func NewTaskScheduler() *TaskScheduler {
	return &TaskScheduler{
		scheduledTasks: make(map[string]*ScheduledTask),
		cronJobs:       make(map[string]*CronJob),
	}
}

// Start 启动任务管理器
func (tm *TaskManager) Start(ctx context.Context) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	if tm.isRunning {
		return fmt.Errorf("task manager is already running")
	}

	tm.logger.Info("Starting task manager")

	// 初始化执行器
	tm.initializeExecutors()

	// 启动工作协程
	go tm.taskDispatcher(ctx)
	go tm.resultProcessor(ctx)
	go tm.healthChecker(ctx)
	go tm.metricsUpdater(ctx)

	if tm.config.EnableScheduling {
		go tm.scheduler.Start(ctx, tm)
	}

	tm.isRunning = true
	tm.logger.Info("Task manager started successfully")

	return nil
}

// Stop 停止任务管理器
func (tm *TaskManager) Stop(ctx context.Context) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	if !tm.isRunning {
		return nil
	}

	tm.logger.Info("Stopping task manager")

	// 停止接收新任务
	close(tm.taskChan)
	close(tm.resultChan)
	close(tm.stopChan)

	// 等待当前任务完成或超时
	tm.waitForTasksCompletion(ctx)

	tm.isRunning = false
	tm.logger.Info("Task manager stopped")

	return nil
}

// SubmitTask 提交任务
func (tm *TaskManager) SubmitTask(task *Task) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	if !tm.isRunning {
		return fmt.Errorf("task manager is not running")
	}

	// 验证任务
	if err := tm.validateTask(task); err != nil {
		return fmt.Errorf("task validation failed: %w", err)
	}

	// 设置任务状态
	task.Status = "pending"
	task.CreatedAt = time.Now()

	// 存储任务
	tm.tasks[task.ID] = task

	// 添加到队列
	tm.taskQueue.Push(task)

	// 发送到任务通道
	select {
	case tm.taskChan <- task:
		tm.logger.WithFields(logrus.Fields{
			"task_id":  task.ID,
			"priority": task.Priority,
			"type":     task.Type,
		}).Info("Task submitted successfully")
	default:
		return fmt.Errorf("task queue is full")
	}

	return nil
}

// GetTask 获取任务
func (tm *TaskManager) GetTask(taskID string) (*Task, error) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	task, exists := tm.tasks[taskID]
	if !exists {
		return nil, fmt.Errorf("task not found: %s", taskID)
	}

	return task, nil
}

// CancelTask 取消任务
func (tm *TaskManager) CancelTask(taskID string) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	task, exists := tm.tasks[taskID]
	if !exists {
		return fmt.Errorf("task not found: %s", taskID)
	}

	task.mutex.Lock()
	defer task.mutex.Unlock()

	if task.Status == "completed" || task.Status == "failed" || task.Status == "cancelled" {
		return fmt.Errorf("task cannot be cancelled, current status: %s", task.Status)
	}

	task.Status = "cancelled"
	task.CompletedAt = &[]time.Time{time.Now()}[0]

	tm.logger.WithField("task_id", taskID).Info("Task cancelled")

	return nil
}

// GetTaskResult 获取任务结果
func (tm *TaskManager) GetTaskResult(taskID string) (*TaskResult, error) {
	return tm.resultTracker.GetResult(taskID)
}

// ListTasks 列出任务
func (tm *TaskManager) ListTasks(filter *TaskFilter) ([]*Task, error) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	tasks := make([]*Task, 0)
	for _, task := range tm.tasks {
		if filter == nil || filter.Matches(task) {
			tasks = append(tasks, task)
		}
	}

	// 排序
	sort.Slice(tasks, func(i, j int) bool {
		return tasks[i].CreatedAt.After(tasks[j].CreatedAt)
	})

	// 限制数量
	if filter != nil && filter.Limit > 0 && len(tasks) > filter.Limit {
		tasks = tasks[:filter.Limit]
	}

	return tasks, nil
}

// GetMetrics 获取任务指标
func (tm *TaskManager) GetMetrics() *TaskMetrics {
	return tm.resultTracker.GetMetrics()
}

// GetStatus 获取任务管理器状态
func (tm *TaskManager) GetStatus() *TaskManagerStatus {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	executorList := make([]*TaskExecutor, 0, len(tm.executors))
	for _, executor := range tm.executors {
		executorList = append(executorList, executor)
	}

	return &TaskManagerStatus{
		IsRunning:  tm.isRunning,
		QueueSize:  tm.taskQueue.Size(),
		TotalTasks: len(tm.tasks),
		Executors:  executorList,
		Metrics:    tm.GetMetrics(),
		Config:     tm.config,
	}
}

// TaskFilter 任务过滤器
type TaskFilter struct {
	Status    string    `json:"status,omitempty"`
	Type      string    `json:"type,omitempty"`
	UserID    int64     `json:"user_id,omitempty"`
	SessionID string    `json:"session_id,omitempty"`
	StartTime time.Time `json:"start_time,omitempty"`
	EndTime   time.Time `json:"end_time,omitempty"`
	Limit     int       `json:"limit,omitempty"`
}

// Matches 检查任务是否匹配过滤器
func (f *TaskFilter) Matches(task *Task) bool {
	if f.Status != "" && task.Status != f.Status {
		return false
	}
	if f.Type != "" && task.Type != f.Type {
		return false
	}
	if f.UserID != 0 && task.UserID != f.UserID {
		return false
	}
	if f.SessionID != "" && task.SessionID != f.SessionID {
		return false
	}
	if !f.StartTime.IsZero() && task.CreatedAt.Before(f.StartTime) {
		return false
	}
	if !f.EndTime.IsZero() && task.CreatedAt.After(f.EndTime) {
		return false
	}
	return true
}

// TaskManagerStatus 任务管理器状态
type TaskManagerStatus struct {
	IsRunning  bool               `json:"is_running"`
	QueueSize  int                `json:"queue_size"`
	TotalTasks int                `json:"total_tasks"`
	Executors  []*TaskExecutor    `json:"executors"`
	Metrics    *TaskMetrics       `json:"metrics"`
	Config     *TaskManagerConfig `json:"config"`
}

// 优先级队列方法

// Push 添加任务到队列
func (pq *PriorityQueue) Push(task *Task) {
	pq.mutex.Lock()
	defer pq.mutex.Unlock()

	pq.tasks = append(pq.tasks, task)
	pq.sort()
}

// Pop 从队列中取出最高优先级任务
func (pq *PriorityQueue) Pop() *Task {
	pq.mutex.Lock()
	defer pq.mutex.Unlock()

	if len(pq.tasks) == 0 {
		return nil
	}

	task := pq.tasks[0]
	pq.tasks = pq.tasks[1:]
	return task
}

// Size 获取队列大小
func (pq *PriorityQueue) Size() int {
	pq.mutex.RLock()
	defer pq.mutex.RUnlock()

	return len(pq.tasks)
}

// sort 按优先级排序
func (pq *PriorityQueue) sort() {
	sort.Slice(pq.tasks, func(i, j int) bool {
		// 优先级高的在前面
		if pq.tasks[i].Priority != pq.tasks[j].Priority {
			return pq.tasks[i].Priority > pq.tasks[j].Priority
		}
		// 优先级相同时，创建时间早的在前面
		return pq.tasks[i].CreatedAt.Before(pq.tasks[j].CreatedAt)
	})
}

// 结果跟踪器方法

// AddResult 添加任务结果
func (rt *ResultTracker) AddResult(result *TaskResult) {
	rt.mutex.Lock()
	defer rt.mutex.Unlock()

	rt.results[result.TaskID] = result
	rt.resultHistory = append(rt.resultHistory, result)

	// 更新指标
	rt.updateMetrics(result)
}

// GetResult 获取任务结果
func (rt *ResultTracker) GetResult(taskID string) (*TaskResult, error) {
	rt.mutex.RLock()
	defer rt.mutex.RUnlock()

	result, exists := rt.results[taskID]
	if !exists {
		return nil, fmt.Errorf("result not found for task: %s", taskID)
	}

	return result, nil
}

// GetMetrics 获取指标
func (rt *ResultTracker) GetMetrics() *TaskMetrics {
	rt.mutex.RLock()
	defer rt.mutex.RUnlock()

	return rt.metrics
}

// updateMetrics 更新指标
func (rt *ResultTracker) updateMetrics(result *TaskResult) {
	rt.metrics.TotalTasks++

	if result.Success {
		rt.metrics.CompletedTasks++
	} else {
		rt.metrics.FailedTasks++
	}

	// 更新平均执行时间
	if rt.metrics.TotalTasks > 0 {
		totalDuration := rt.metrics.AvgExecutionTime * time.Duration(rt.metrics.TotalTasks-1)
		rt.metrics.AvgExecutionTime = (totalDuration + result.Duration) / time.Duration(rt.metrics.TotalTasks)
	}

	// 更新成功率
	rt.metrics.SuccessRate = float64(rt.metrics.CompletedTasks) / float64(rt.metrics.TotalTasks) * 100

	rt.metrics.LastUpdateTime = time.Now()
}

// 任务管理器核心方法

// initializeExecutors 初始化执行器
func (tm *TaskManager) initializeExecutors() {
	// 创建本地执行器
	localExecutor := &TaskExecutor{
		ID:             "local-executor-1",
		Type:           "local",
		Status:         "idle",
		Capabilities:   []string{"command", "script"},
		MaxConcurrency: tm.config.MaxConcurrentTasks,
		ActiveTasks:    0,
		TotalExecuted:  0,
		SuccessRate:    100.0,
		LastActivity:   time.Now(),
		HealthStatus:   "healthy",
		Config:         make(map[string]interface{}),
	}

	tm.executors[localExecutor.ID] = localExecutor

	tm.logger.WithField("executor_id", localExecutor.ID).Info("Local executor initialized")
}

// taskDispatcher 任务分发器
func (tm *TaskManager) taskDispatcher(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-tm.stopChan:
			return
		case task := <-tm.taskChan:
			if task != nil {
				tm.dispatchTask(task)
			}
		}
	}
}

// dispatchTask 分发任务
func (tm *TaskManager) dispatchTask(task *Task) {
	// 查找可用的执行器
	executor := tm.findAvailableExecutor(task)
	if executor == nil {
		// 没有可用执行器，重新加入队列
		tm.taskQueue.Push(task)
		tm.logger.WithField("task_id", task.ID).Warn("No available executor, task requeued")
		return
	}

	// 分配任务给执行器
	go tm.executeTask(task, executor)
}

// findAvailableExecutor 查找可用执行器
func (tm *TaskManager) findAvailableExecutor(task *Task) *TaskExecutor {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	for _, executor := range tm.executors {
		if executor.Status == "idle" &&
			executor.ActiveTasks < executor.MaxConcurrency &&
			tm.executorSupportsTask(executor, task) {
			return executor
		}
	}

	return nil
}

// executorSupportsTask 检查执行器是否支持任务
func (tm *TaskManager) executorSupportsTask(executor *TaskExecutor, task *Task) bool {
	for _, capability := range executor.Capabilities {
		if capability == task.Type {
			return true
		}
	}
	return false
}

// executeTask 执行任务
func (tm *TaskManager) executeTask(task *Task, executor *TaskExecutor) {
	// 更新任务状态
	task.mutex.Lock()
	task.Status = "running"
	task.ExecutorID = executor.ID
	startTime := time.Now()
	task.StartedAt = &startTime
	task.mutex.Unlock()

	// 更新执行器状态
	executor.mutex.Lock()
	executor.Status = "busy"
	executor.ActiveTasks++
	executor.CurrentTask = task
	executor.LastActivity = time.Now()
	executor.mutex.Unlock()

	tm.logger.WithFields(logrus.Fields{
		"task_id":     task.ID,
		"executor_id": executor.ID,
	}).Info("Task execution started")

	// 执行任务
	result := tm.performTaskExecution(task, executor)

	// 更新任务状态
	task.mutex.Lock()
	if result.Success {
		task.Status = "completed"
	} else {
		task.Status = "failed"
	}
	endTime := time.Now()
	task.CompletedAt = &endTime
	task.mutex.Unlock()

	// 更新执行器状态
	executor.mutex.Lock()
	executor.Status = "idle"
	executor.ActiveTasks--
	executor.CurrentTask = nil
	executor.TotalExecuted++
	executor.LastActivity = time.Now()

	// 更新成功率
	if executor.TotalExecuted > 0 {
		successCount := int64(float64(executor.TotalExecuted) * executor.SuccessRate / 100.0)
		if result.Success {
			successCount++
		}
		executor.SuccessRate = float64(successCount) / float64(executor.TotalExecuted) * 100.0
	}
	executor.mutex.Unlock()

	// 发送结果
	select {
	case tm.resultChan <- result:
	default:
		tm.logger.Warn("Result channel is full, dropping result")
	}

	tm.logger.WithFields(logrus.Fields{
		"task_id":     task.ID,
		"executor_id": executor.ID,
		"success":     result.Success,
		"duration":    result.Duration.Milliseconds(),
	}).Info("Task execution completed")
}

// performTaskExecution 执行任务
func (tm *TaskManager) performTaskExecution(task *Task, executor *TaskExecutor) *TaskResult {
	startTime := time.Now()

	// 模拟任务执行
	// 在实际实现中，这里应该调用真实的命令执行逻辑

	// 模拟执行时间
	time.Sleep(100 * time.Millisecond)

	endTime := time.Now()
	duration := endTime.Sub(startTime)

	// 创建结果
	result := &TaskResult{
		TaskID:     task.ID,
		ExecutorID: executor.ID,
		Success:    true, // 简化实现，假设总是成功
		ExitCode:   0,
		Stdout:     fmt.Sprintf("Task %s executed successfully", task.ID),
		Stderr:     "",
		Duration:   duration,
		StartTime:  startTime,
		EndTime:    endTime,
		Metadata:   make(map[string]interface{}),
	}

	return result
}

// resultProcessor 结果处理器
func (tm *TaskManager) resultProcessor(ctx context.Context) {
	for {
		select {
		case <-ctx.Done():
			return
		case <-tm.stopChan:
			return
		case result := <-tm.resultChan:
			if result != nil {
				tm.resultTracker.AddResult(result)
			}
		}
	}
}

// healthChecker 健康检查器
func (tm *TaskManager) healthChecker(ctx context.Context) {
	ticker := time.NewTicker(tm.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-tm.stopChan:
			return
		case <-ticker.C:
			tm.performHealthCheck()
		}
	}
}

// performHealthCheck 执行健康检查
func (tm *TaskManager) performHealthCheck() {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	for _, executor := range tm.executors {
		executor.mutex.Lock()

		// 检查执行器是否长时间无活动
		if time.Since(executor.LastActivity) > 5*time.Minute {
			executor.HealthStatus = "degraded"
		} else {
			executor.HealthStatus = "healthy"
		}

		executor.mutex.Unlock()
	}
}

// metricsUpdater 指标更新器
func (tm *TaskManager) metricsUpdater(ctx context.Context) {
	ticker := time.NewTicker(tm.config.MetricsUpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-tm.stopChan:
			return
		case <-ticker.C:
			tm.updateMetrics()
		}
	}
}

// updateMetrics 更新指标
func (tm *TaskManager) updateMetrics() {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	metrics := tm.resultTracker.GetMetrics()

	// 更新队列中的任务数
	queuedCount := 0
	runningCount := 0

	for _, task := range tm.tasks {
		switch task.Status {
		case "queued", "pending":
			queuedCount++
		case "running":
			runningCount++
		}
	}

	metrics.QueuedTasks = queuedCount
	metrics.RunningTasks = runningCount

	// 计算吞吐量
	if metrics.TotalTasks > 0 {
		hoursElapsed := time.Since(metrics.LastUpdateTime).Hours()
		if hoursElapsed > 0 {
			metrics.ThroughputPerHour = float64(metrics.CompletedTasks) / hoursElapsed
		}
	}
}

// validateTask 验证任务
func (tm *TaskManager) validateTask(task *Task) error {
	if task.ID == "" {
		return fmt.Errorf("task ID is required")
	}

	if task.Type == "" {
		return fmt.Errorf("task type is required")
	}

	if task.Command == "" {
		return fmt.Errorf("task command is required")
	}

	if task.Priority < 1 || task.Priority > 10 {
		task.Priority = 5 // 默认优先级
	}

	if task.Timeout == 0 {
		task.Timeout = tm.config.TaskTimeout
	}

	if task.MaxRetries == 0 {
		task.MaxRetries = tm.config.RetryAttempts
	}

	return nil
}

// waitForTasksCompletion 等待任务完成
func (tm *TaskManager) waitForTasksCompletion(ctx context.Context) {
	timeout := time.NewTimer(30 * time.Second)
	defer timeout.Stop()

	ticker := time.NewTicker(1 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-timeout.C:
			tm.logger.Warn("Timeout waiting for tasks completion")
			return
		case <-ticker.C:
			if tm.allTasksCompleted() {
				tm.logger.Info("All tasks completed")
				return
			}
		}
	}
}

// allTasksCompleted 检查所有任务是否完成
func (tm *TaskManager) allTasksCompleted() bool {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	for _, executor := range tm.executors {
		if executor.ActiveTasks > 0 {
			return false
		}
	}

	return true
}

// 任务调度器方法

// Start 启动任务调度器
func (ts *TaskScheduler) Start(ctx context.Context, tm *TaskManager) {
	go ts.schedulerRoutine(ctx, tm)
}

// schedulerRoutine 调度器协程
func (ts *TaskScheduler) schedulerRoutine(ctx context.Context, tm *TaskManager) {
	ticker := time.NewTicker(10 * time.Second) // 每10秒检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			ts.processScheduledTasks(tm)
		}
	}
}

// processScheduledTasks 处理计划任务
func (ts *TaskScheduler) processScheduledTasks(tm *TaskManager) {
	ts.mutex.RLock()
	defer ts.mutex.RUnlock()

	now := time.Now()

	// 处理一次性计划任务
	for id, scheduledTask := range ts.scheduledTasks {
		if scheduledTask.Enabled && now.After(scheduledTask.ScheduleAt) {
			// 提交任务
			if err := tm.SubmitTask(scheduledTask.Task); err != nil {
				tm.logger.WithError(err).WithField("scheduled_task_id", id).Error("Failed to submit scheduled task")
				continue
			}

			// 更新最后运行时间
			scheduledTask.LastRun = &now

			// 如果是重复任务，计算下次运行时间
			if scheduledTask.Recurring && scheduledTask.Interval > 0 {
				scheduledTask.NextRun = now.Add(scheduledTask.Interval)
				scheduledTask.ScheduleAt = scheduledTask.NextRun
			} else {
				// 一次性任务，禁用
				scheduledTask.Enabled = false
			}

			tm.logger.WithFields(logrus.Fields{
				"scheduled_task_id": id,
				"task_id":           scheduledTask.Task.ID,
				"next_run":          scheduledTask.NextRun,
			}).Info("Scheduled task executed")
		}
	}

	// 处理Cron任务
	for id, cronJob := range ts.cronJobs {
		if cronJob.Enabled && now.After(cronJob.NextRun) {
			// 创建任务副本
			taskCopy := *cronJob.Task
			taskCopy.ID = fmt.Sprintf("%s_%d", cronJob.ID, time.Now().UnixNano())

			// 提交任务
			if err := tm.SubmitTask(&taskCopy); err != nil {
				tm.logger.WithError(err).WithField("cron_job_id", id).Error("Failed to submit cron job")
				continue
			}

			// 更新运行信息
			cronJob.LastRun = &now
			cronJob.RunCount++

			// 计算下次运行时间（简化实现）
			cronJob.NextRun = ts.calculateNextCronRun(cronJob.Expression, now)

			tm.logger.WithFields(logrus.Fields{
				"cron_job_id": id,
				"task_id":     taskCopy.ID,
				"run_count":   cronJob.RunCount,
				"next_run":    cronJob.NextRun,
			}).Info("Cron job executed")
		}
	}
}

// calculateNextCronRun 计算下次Cron运行时间（简化实现）
func (ts *TaskScheduler) calculateNextCronRun(expression string, from time.Time) time.Time {
	// 简化实现，支持基本的时间间隔
	switch expression {
	case "@hourly":
		return from.Add(1 * time.Hour)
	case "@daily":
		return from.Add(24 * time.Hour)
	case "@weekly":
		return from.Add(7 * 24 * time.Hour)
	case "@monthly":
		return from.AddDate(0, 1, 0)
	default:
		// 默认每小时执行
		return from.Add(1 * time.Hour)
	}
}

// AddScheduledTask 添加计划任务
func (ts *TaskScheduler) AddScheduledTask(scheduledTask *ScheduledTask) {
	ts.mutex.Lock()
	defer ts.mutex.Unlock()

	ts.scheduledTasks[scheduledTask.ID] = scheduledTask
}

// AddCronJob 添加Cron任务
func (ts *TaskScheduler) AddCronJob(cronJob *CronJob) {
	ts.mutex.Lock()
	defer ts.mutex.Unlock()

	ts.cronJobs[cronJob.ID] = cronJob
}

// RemoveScheduledTask 移除计划任务
func (ts *TaskScheduler) RemoveScheduledTask(id string) {
	ts.mutex.Lock()
	defer ts.mutex.Unlock()

	delete(ts.scheduledTasks, id)
}

// RemoveCronJob 移除Cron任务
func (ts *TaskScheduler) RemoveCronJob(id string) {
	ts.mutex.Lock()
	defer ts.mutex.Unlock()

	delete(ts.cronJobs, id)
}

// GetScheduledTasks 获取计划任务列表
func (ts *TaskScheduler) GetScheduledTasks() []*ScheduledTask {
	ts.mutex.RLock()
	defer ts.mutex.RUnlock()

	tasks := make([]*ScheduledTask, 0, len(ts.scheduledTasks))
	for _, task := range ts.scheduledTasks {
		tasks = append(tasks, task)
	}

	return tasks
}

// GetCronJobs 获取Cron任务列表
func (ts *TaskScheduler) GetCronJobs() []*CronJob {
	ts.mutex.RLock()
	defer ts.mutex.RUnlock()

	jobs := make([]*CronJob, 0, len(ts.cronJobs))
	for _, job := range ts.cronJobs {
		jobs = append(jobs, job)
	}

	return jobs
}
