package service

import (
	"encoding/json"
	"fmt"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// alertService 告警服务实现
type alertService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewAlertService 创建告警服务
func NewAlertService(db *gorm.DB, logger *logrus.Logger) AlertService {
	return &alertService{
		db:     db,
		logger: logger,
	}
}

// CreateAlert 创建告警
func (s *alertService) CreateAlert(req *model.AlertCreateRequest) (*model.AlertResponse, error) {
	// 生成指纹
	fingerprint := s.generateFingerprint(req.Title, req.Source, req.HostID)

	// 检查是否存在相同的活跃告警
	var existingAlert model.Alert
	err := s.db.Where("fingerprint = ? AND status IN (?)", fingerprint, []string{"open", "acknowledged"}).First(&existingAlert).Error
	if err == nil {
		// 存在相同的活跃告警，更新计数
		existingAlert.EscalationLevel++
		if err := s.db.Save(&existingAlert).Error; err != nil {
			return nil, fmt.Errorf("failed to update existing alert: %w", err)
		}
		return existingAlert.ToResponse(), nil
	}

	// 创建新告警
	alert := &model.Alert{
		Title:       req.Title,
		Message:     req.Message,
		Level:       req.Level,
		Source:      req.Source,
		Status:      string(model.AlertStatusOpen),
		AlertTime:   time.Now(),
		HostID:      req.HostID,
		RuleID:      req.RuleID,
		Fingerprint: fingerprint,
	}

	// 设置元数据
	if req.Metadata != nil {
		metadataBytes, err := json.Marshal(req.Metadata)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal metadata: %w", err)
		}
		alert.Metadata = string(metadataBytes)
	}

	if err := s.db.Create(alert).Error; err != nil {
		return nil, fmt.Errorf("failed to create alert: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"title":    alert.Title,
		"level":    alert.Level,
		"source":   alert.Source,
	}).Info("Alert created")

	return alert.ToResponse(), nil
}

// GetAlertByID 根据ID获取告警
func (s *alertService) GetAlertByID(id int64) (*model.AlertResponse, error) {
	var alert model.Alert
	err := s.db.Preload("Host").Preload("AssignedUser").Preload("Creator").First(&alert, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("alert not found")
		}
		return nil, fmt.Errorf("failed to get alert: %w", err)
	}

	return alert.ToResponse(), nil
}

// UpdateAlert 更新告警
func (s *alertService) UpdateAlert(id int64, req *model.AlertUpdateRequest) (*model.AlertResponse, error) {
	var alert model.Alert
	err := s.db.First(&alert, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("alert not found")
		}
		return nil, fmt.Errorf("failed to get alert: %w", err)
	}

	// 更新字段
	if req.Title != nil {
		alert.Title = *req.Title
	}
	if req.Message != nil {
		alert.Message = *req.Message
	}
	if req.Level != nil {
		alert.Level = *req.Level
	}
	if req.Status != nil {
		alert.Status = *req.Status
	}
	if req.AssignedTo != nil {
		alert.AssignedTo = req.AssignedTo
	}

	// 设置元数据
	if req.Metadata != nil {
		metadataBytes, err := json.Marshal(req.Metadata)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal metadata: %w", err)
		}
		alert.Metadata = string(metadataBytes)
	}

	if err := s.db.Save(&alert).Error; err != nil {
		return nil, fmt.Errorf("failed to update alert: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"title":    alert.Title,
	}).Info("Alert updated")

	return alert.ToResponse(), nil
}

// DeleteAlert 删除告警
func (s *alertService) DeleteAlert(id int64) error {
	var alert model.Alert
	err := s.db.First(&alert, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("alert not found")
		}
		return fmt.Errorf("failed to get alert: %w", err)
	}

	if err := s.db.Delete(&alert).Error; err != nil {
		return fmt.Errorf("failed to delete alert: %w", err)
	}

	s.logger.WithField("alert_id", id).Info("Alert deleted")
	return nil
}

// ListAlerts 获取告警列表
func (s *alertService) ListAlerts(req *model.AlertListQuery) (*model.AlertListResponse, error) {
	var alerts []model.Alert
	var total int64

	// 构建查询
	query := s.db.Model(&model.Alert{})

	// 添加过滤条件
	if req.Level != "" {
		query = query.Where("level = ?", req.Level)
	}
	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}
	if req.Source != "" {
		query = query.Where("source = ?", req.Source)
	}
	if req.HostID != nil {
		query = query.Where("host_id = ?", *req.HostID)
	}
	if req.Search != "" {
		query = query.Where("title LIKE ? OR message LIKE ?", "%"+req.Search+"%", "%"+req.Search+"%")
	}

	// 时间范围过滤
	if req.TimeRange != "" {
		var since time.Time
		switch req.TimeRange {
		case "1h":
			since = time.Now().Add(-1 * time.Hour)
		case "6h":
			since = time.Now().Add(-6 * time.Hour)
		case "24h":
			since = time.Now().Add(-24 * time.Hour)
		case "7d":
			since = time.Now().Add(-7 * 24 * time.Hour)
		case "30d":
			since = time.Now().Add(-30 * 24 * time.Hour)
		}
		if !since.IsZero() {
			query = query.Where("alert_time >= ?", since)
		}
	}

	// 获取总数
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count alerts: %w", err)
	}

	// 分页和排序
	offset := (req.Page - 1) * req.Limit
	if err := query.Preload("Host").Preload("AssignedUser").Preload("Creator").
		Order("alert_time DESC").
		Offset(offset).Limit(req.Limit).
		Find(&alerts).Error; err != nil {
		return nil, fmt.Errorf("failed to list alerts: %w", err)
	}

	// 转换为响应格式
	alertResponses := make([]*model.AlertResponse, len(alerts))
	for i, alert := range alerts {
		alertResponses[i] = alert.ToResponse()
	}

	// 获取摘要信息
	summary, err := s.GetAlertSummary()
	if err != nil {
		s.logger.WithError(err).Warn("Failed to get alert summary")
		summary = &model.AlertSummary{}
	}

	return &model.AlertListResponse{
		Alerts:     alertResponses,
		Pagination: model.NewPagination(total, req.Page, req.Limit),
		Summary:    summary,
	}, nil
}

// AcknowledgeAlert 确认告警
func (s *alertService) AcknowledgeAlert(id int64, userID int64, comment string) error {
	var alert model.Alert
	err := s.db.First(&alert, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("alert not found")
		}
		return fmt.Errorf("failed to get alert: %w", err)
	}

	// 确认告警
	alert.Acknowledge(userID)

	if err := s.db.Save(&alert).Error; err != nil {
		return fmt.Errorf("failed to acknowledge alert: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"alert_id": alert.ID,
		"user_id":  userID,
		"comment":  comment,
	}).Info("Alert acknowledged")

	return nil
}

// ResolveAlert 解决告警
func (s *alertService) ResolveAlert(id int64, resolution string) error {
	var alert model.Alert
	err := s.db.First(&alert, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("alert not found")
		}
		return fmt.Errorf("failed to get alert: %w", err)
	}

	// 解决告警
	alert.Resolve()

	if err := s.db.Save(&alert).Error; err != nil {
		return fmt.Errorf("failed to resolve alert: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"alert_id":   alert.ID,
		"resolution": resolution,
	}).Info("Alert resolved")

	return nil
}

// CloseAlert 关闭告警
func (s *alertService) CloseAlert(id int64) error {
	var alert model.Alert
	err := s.db.First(&alert, id).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("alert not found")
		}
		return fmt.Errorf("failed to get alert: %w", err)
	}

	// 关闭告警
	alert.Close()

	if err := s.db.Save(&alert).Error; err != nil {
		return fmt.Errorf("failed to close alert: %w", err)
	}

	s.logger.WithField("alert_id", alert.ID).Info("Alert closed")
	return nil
}

// GetAlertSummary 获取告警摘要
func (s *alertService) GetAlertSummary() (*model.AlertSummary, error) {
	var summary model.AlertSummary

	// 总数
	if err := s.db.Model(&model.Alert{}).Count(&summary.Total).Error; err != nil {
		return nil, fmt.Errorf("failed to count total alerts: %w", err)
	}

	// 按状态统计
	var statusStats []struct {
		Status string
		Count  int64
	}
	if err := s.db.Model(&model.Alert{}).
		Select("status, COUNT(*) as count").
		Group("status").
		Find(&statusStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get status stats: %w", err)
	}

	for _, stat := range statusStats {
		switch stat.Status {
		case "open":
			summary.Open = stat.Count
		case "acknowledged":
			summary.Acknowledged = stat.Count
		case "resolved":
			summary.Resolved = stat.Count
		}
	}

	// 按级别统计
	var levelStats []struct {
		Level string
		Count int64
	}
	if err := s.db.Model(&model.Alert{}).
		Select("level, COUNT(*) as count").
		Group("level").
		Find(&levelStats).Error; err != nil {
		return nil, fmt.Errorf("failed to get level stats: %w", err)
	}

	for _, stat := range levelStats {
		switch stat.Level {
		case "critical":
			summary.Critical = stat.Count
		case "warning":
			summary.Warning = stat.Count
		case "info":
			summary.Info = stat.Count
		}
	}

	return &summary, nil
}

// generateFingerprint 生成告警指纹
func (s *alertService) generateFingerprint(title, source string, hostID *int64) string {
	data := fmt.Sprintf("%s:%s", title, source)
	if hostID != nil {
		data = fmt.Sprintf("%s:%d", data, *hostID)
	}

	// 简单的哈希实现
	hash := uint32(0)
	for _, c := range data {
		hash = hash*31 + uint32(c)
	}

	return fmt.Sprintf("%08x", hash)
}
