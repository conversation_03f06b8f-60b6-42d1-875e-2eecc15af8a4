package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
)

// EnhancedToolManager 增强的工具管理器
type EnhancedToolManager struct {
	*ToolManager
	logger      *logrus.Logger
	hostService HostService
	aiService   AIService
}

// NewEnhancedToolManager 创建增强的工具管理器
func NewEnhancedToolManager(logger *logrus.Logger, hostService HostService, aiService AIService) *EnhancedToolManager {
	baseTM := NewToolManager(logger, hostService)

	etm := &EnhancedToolManager{
		ToolManager: baseTM,
		logger:      logger,
		hostService: hostService,
		aiService:   aiService,
	}

	// 注册增强工具
	etm.registerEnhancedTools()
	return etm
}

// registerEnhancedTools 注册增强工具
func (etm *EnhancedToolManager) registerEnhancedTools() {
	// 智能系统分析工具
	etm.tools["analyze_system_health"] = &AnalyzeSystemHealthTool{
		hostService: etm.hostService,
		aiService:   etm.aiService,
		logger:      etm.logger,
	}

	// 智能日志分析工具
	etm.tools["analyze_logs"] = &AnalyzeLogsTool{
		hostService: etm.hostService,
		aiService:   etm.aiService,
		logger:      etm.logger,
	}

	// 性能优化建议工具
	etm.tools["suggest_optimization"] = &SuggestOptimizationTool{
		hostService: etm.hostService,
		aiService:   etm.aiService,
		logger:      etm.logger,
	}

	// TODO: 后续实现更多工具
	// 安全检查工具
	// 故障诊断工具
	// 批量操作工具
}

// AnalyzeSystemHealthTool 系统健康分析工具
type AnalyzeSystemHealthTool struct {
	hostService HostService
	aiService   AIService
	logger      *logrus.Logger
}

func (t *AnalyzeSystemHealthTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	// 获取主机信息
	host, err := t.hostService.GetHostByID(int64(hostID))
	if err != nil {
		return nil, fmt.Errorf("failed to get host info: %w", err)
	}

	// 执行系统健康检查命令
	healthChecks := []struct {
		name    string
		command string
	}{
		{"CPU使用率", "top -bn1 | head -5"},
		{"内存使用", "free -h"},
		{"磁盘使用", "df -h"},
		{"系统负载", "uptime"},
		{"网络连接", "netstat -tuln | head -10"},
		{"进程状态", "ps aux | head -10"},
	}

	results := make(map[string]interface{})

	for _, check := range healthChecks {
		req := &model.CommandExecuteRequest{
			Command: check.command,
			Timeout: 10,
		}

		result, err := t.hostService.ExecuteCommand(int64(hostID), req)
		if err != nil {
			t.logger.WithError(err).Warnf("Failed to execute %s check", check.name)
			results[check.name] = map[string]interface{}{
				"success": false,
				"error":   err.Error(),
			}
			continue
		}

		results[check.name] = map[string]interface{}{
			"success": result.ExitCode == 0,
			"output":  result.Stdout,
			"error":   result.Stderr,
		}
	}

	// TODO: 使用AI分析结果
	// analysisPrompt := fmt.Sprintf(`请分析以下系统健康检查结果，提供专业的健康评估和建议：
	// 主机信息：%s (%s)
	// 检查结果：%s
	// 请提供：
	// 1. 整体健康状态评分（1-10分）
	// 2. 发现的问题和风险
	// 3. 优化建议
	// 4. 需要关注的指标`, host.Name, host.IPAddress, formatResults(results))

	// 这里可以调用AI服务进行分析
	// analysis, err := t.aiService.GenerateResponse(ctx, &GenerateResponseRequest{...})

	return map[string]interface{}{
		"host_info":     host,
		"check_results": results,
		"summary":       "系统健康检查完成",
		"timestamp":     time.Now(),
	}, nil
}

func (t *AnalyzeSystemHealthTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "analyze_system_health",
			Description: "对指定主机进行全面的系统健康分析，包括CPU、内存、磁盘、网络等指标",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "要分析的主机ID",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// AnalyzeLogsTool 日志分析工具
type AnalyzeLogsTool struct {
	hostService HostService
	aiService   AIService
	logger      *logrus.Logger
}

func (t *AnalyzeLogsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	logType := "syslog"
	if lt, ok := args["log_type"].(string); ok {
		logType = lt
	}

	lines := 50
	if l, ok := args["lines"].(float64); ok {
		lines = int(l)
	}

	// 根据日志类型构建命令
	var command string
	switch logType {
	case "syslog":
		command = fmt.Sprintf("tail -%d /var/log/syslog", lines)
	case "auth":
		command = fmt.Sprintf("tail -%d /var/log/auth.log", lines)
	case "error":
		command = fmt.Sprintf("tail -%d /var/log/syslog | grep -i error", lines)
	case "warning":
		command = fmt.Sprintf("tail -%d /var/log/syslog | grep -i warning", lines)
	default:
		command = fmt.Sprintf("tail -%d /var/log/%s", lines, logType)
	}

	req := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 15,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute log command: %w", err)
	}

	if result.ExitCode != 0 {
		return map[string]interface{}{
			"success": false,
			"error":   result.Stderr,
		}, nil
	}

	// 分析日志内容
	logContent := result.Stdout
	analysis := analyzeLogContent(logContent)

	return map[string]interface{}{
		"success":     true,
		"log_type":    logType,
		"lines_count": lines,
		"content":     logContent,
		"analysis":    analysis,
		"timestamp":   time.Now(),
	}, nil
}

func (t *AnalyzeLogsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "analyze_logs",
			Description: "分析指定主机的日志文件，提取关键信息和异常",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"log_type": map[string]interface{}{
						"type":        "string",
						"description": "日志类型 (syslog, auth, error, warning)",
						"enum":        []string{"syslog", "auth", "error", "warning"},
					},
					"lines": map[string]interface{}{
						"type":        "number",
						"description": "要分析的日志行数，默认50行",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// SuggestOptimizationTool 优化建议工具
type SuggestOptimizationTool struct {
	hostService HostService
	aiService   AIService
	logger      *logrus.Logger
}

func (t *SuggestOptimizationTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	// 收集系统信息
	systemInfo, err := t.collectSystemInfo(int64(hostID))
	if err != nil {
		return nil, fmt.Errorf("failed to collect system info: %w", err)
	}

	// 生成优化建议
	suggestions := generateOptimizationSuggestions(systemInfo)

	return map[string]interface{}{
		"host_id":     hostID,
		"system_info": systemInfo,
		"suggestions": suggestions,
		"timestamp":   time.Now(),
	}, nil
}

func (t *SuggestOptimizationTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "suggest_optimization",
			Description: "基于系统状态分析，提供性能优化建议",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

func (t *SuggestOptimizationTool) collectSystemInfo(hostID int64) (map[string]interface{}, error) {
	// 实现系统信息收集逻辑
	return map[string]interface{}{
		"cpu_usage":    "收集CPU使用率",
		"memory_usage": "收集内存使用率",
		"disk_usage":   "收集磁盘使用率",
		"network":      "收集网络状态",
	}, nil
}

// 辅助函数
func formatResults(results map[string]interface{}) string {
	data, _ := json.MarshalIndent(results, "", "  ")
	return string(data)
}

func analyzeLogContent(content string) map[string]interface{} {
	lines := strings.Split(content, "\n")

	analysis := map[string]interface{}{
		"total_lines":   len(lines),
		"error_count":   countMatches(content, "error"),
		"warning_count": countMatches(content, "warning"),
		"info_count":    countMatches(content, "info"),
	}

	return analysis
}

func countMatches(content, pattern string) int {
	return strings.Count(strings.ToLower(content), strings.ToLower(pattern))
}

func generateOptimizationSuggestions(systemInfo map[string]interface{}) []map[string]interface{} {
	suggestions := []map[string]interface{}{
		{
			"category":    "性能优化",
			"title":       "内存使用优化",
			"description": "建议清理不必要的进程以释放内存",
			"priority":    "medium",
		},
		{
			"category":    "安全加固",
			"title":       "更新系统补丁",
			"description": "建议定期更新系统安全补丁",
			"priority":    "high",
		},
	}

	return suggestions
}
