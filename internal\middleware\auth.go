package middleware

import (
	"fmt"
	"net/http"

	"aiops-platform/internal/auth"
	"aiops-platform/internal/model"

	"github.com/gin-gonic/gin"
)

// AuthMiddleware 认证中间件
func AuthMiddleware(jwtManager *auth.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.<PERSON>eader("Authorization")
		if authHeader == "" {
			AbortWithAppError(c, model.ErrCodeUnauthorized, "Authorization header is required", nil)
			return
		}

		// 提取Token
		token, err := auth.ExtractTokenFromHeader(authHeader)
		if err != nil {
			AbortWithAppError(c, model.ErrCodeTokenInvalid, "Invalid authorization header format", err)
			return
		}

		// 验证Token
		claims, err := jwtManager.ValidateToken(token)
		if err != nil {
			AbortWithAppError(c, model.ErrCodeTokenExpired, "Invalid or expired token", err)
			return
		}

		// 将用户信息存储到上下文
		userCtx := &auth.UserContext{
			UserID:    claims.UserID,
			Username:  claims.Username,
			Role:      claims.Role,
			SessionID: claims.SessionID,
		}
		c.Set("user", userCtx)
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("session_id", claims.SessionID)

		c.Next()
	}
}

// OptionalAuthMiddleware 可选认证中间件
func OptionalAuthMiddleware(jwtManager *auth.JWTManager) gin.HandlerFunc {
	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.Next()
			return
		}

		token, err := auth.ExtractTokenFromHeader(authHeader)
		if err != nil {
			c.Next()
			return
		}

		claims, err := jwtManager.ValidateToken(token)
		if err != nil {
			c.Next()
			return
		}

		// 将用户信息存储到上下文
		userCtx := &auth.UserContext{
			UserID:    claims.UserID,
			Username:  claims.Username,
			Role:      claims.Role,
			SessionID: claims.SessionID,
		}
		c.Set("user", userCtx)
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("role", claims.Role)
		c.Set("session_id", claims.SessionID)

		c.Next()
	}
}

// RequireRole 角色权限中间件
func RequireRole(roles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userCtx, exists := c.Get("user")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "Authentication required",
			})
			c.Abort()
			return
		}

		user, ok := userCtx.(*auth.UserContext)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Invalid user context",
			})
			c.Abort()
			return
		}

		if !user.HasAnyRole(roles...) {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    403,
				"message": "Insufficient permissions",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// RequireAdmin 管理员权限中间件
func RequireAdmin() gin.HandlerFunc {
	return RequireRole("admin", "super_admin")
}

// RequireSuperAdmin 超级管理员权限中间件
func RequireSuperAdmin() gin.HandlerFunc {
	return RequireRole("super_admin")
}

// GetUserFromContext 从上下文获取用户信息
func GetUserFromContext(c *gin.Context) (*auth.UserContext, bool) {
	userCtx, exists := c.Get("user")
	if !exists {
		return nil, false
	}

	user, ok := userCtx.(*auth.UserContext)
	return user, ok
}

// GetUserIDFromContext 从上下文获取用户ID
func GetUserIDFromContext(c *gin.Context) (int64, bool) {
	userID, exists := c.Get("user_id")
	if !exists {
		return 0, false
	}

	id, ok := userID.(int64)
	return id, ok
}

// GetUsernameFromContext 从上下文获取用户名
func GetUsernameFromContext(c *gin.Context) (string, bool) {
	username, exists := c.Get("username")
	if !exists {
		return "", false
	}

	name, ok := username.(string)
	return name, ok
}

// GetRoleFromContext 从上下文获取用户角色
func GetRoleFromContext(c *gin.Context) (string, bool) {
	role, exists := c.Get("role")
	if !exists {
		return "", false
	}

	r, ok := role.(string)
	return r, ok
}

// GetSessionIDFromContext 从上下文获取会话ID
func GetSessionIDFromContext(c *gin.Context) (string, bool) {
	sessionID, exists := c.Get("session_id")
	if !exists {
		return "", false
	}

	id, ok := sessionID.(string)
	return id, ok
}

// PermissionMiddleware 权限检查中间件
type PermissionMiddleware struct {
	permissionChecker PermissionChecker
}

// PermissionChecker 权限检查器接口
type PermissionChecker interface {
	HasPermission(userID int64, resource, action string) bool
}

// NewPermissionMiddleware 创建权限中间件
func NewPermissionMiddleware(checker PermissionChecker) *PermissionMiddleware {
	return &PermissionMiddleware{
		permissionChecker: checker,
	}
}

// RequirePermission 权限检查中间件
func (pm *PermissionMiddleware) RequirePermission(resource, action string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := GetUserIDFromContext(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "Authentication required",
			})
			c.Abort()
			return
		}

		if !pm.permissionChecker.HasPermission(userID, resource, action) {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    403,
				"message": "Insufficient permissions",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// OwnershipMiddleware 资源所有权检查中间件
type OwnershipMiddleware struct {
	ownershipChecker OwnershipChecker
}

// OwnershipChecker 所有权检查器接口
type OwnershipChecker interface {
	IsOwner(userID int64, resourceType string, resourceID int64) bool
	CanAccess(userID int64, resourceType string, resourceID int64) bool
}

// NewOwnershipMiddleware 创建所有权中间件
func NewOwnershipMiddleware(checker OwnershipChecker) *OwnershipMiddleware {
	return &OwnershipMiddleware{
		ownershipChecker: checker,
	}
}

// RequireOwnership 所有权检查中间件
func (om *OwnershipMiddleware) RequireOwnership(resourceType, resourceIDParam string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userID, exists := GetUserIDFromContext(c)
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "Authentication required",
			})
			c.Abort()
			return
		}

		// 从路径参数获取资源ID
		resourceIDStr := c.Param(resourceIDParam)
		if resourceIDStr == "" {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Resource ID is required",
			})
			c.Abort()
			return
		}

		// 转换为int64
		var resourceID int64
		if _, err := fmt.Sscanf(resourceIDStr, "%d", &resourceID); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": "Invalid resource ID",
			})
			c.Abort()
			return
		}

		// 检查所有权或访问权限
		if !om.ownershipChecker.IsOwner(userID, resourceType, resourceID) &&
			!om.ownershipChecker.CanAccess(userID, resourceType, resourceID) {
			c.JSON(http.StatusForbidden, gin.H{
				"code":    403,
				"message": "Access denied",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}

// APIKeyMiddleware API密钥认证中间件
func APIKeyMiddleware(validAPIKeys map[string]string) gin.HandlerFunc {
	return func(c *gin.Context) {
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "API key is required",
			})
			c.Abort()
			return
		}

		// 验证API密钥
		if _, exists := validAPIKeys[apiKey]; !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"code":    401,
				"message": "Invalid API key",
			})
			c.Abort()
			return
		}

		c.Next()
	}
}
