//go:build ignore
// +build ignore

package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/database"
	"aiops-platform/internal/logger"
	"aiops-platform/internal/service"

	"github.com/sirupsen/logrus"
)

func main() {
	fmt.Println("🧪 测试统一AI服务适配器")
	fmt.Println("================================")

	// 初始化配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化日志
	logger := logger.New(cfg.Log)
	logger.SetLevel(logrus.DebugLevel) // 设置为调试级别

	// 初始化数据库
	db, err := database.New(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// 创建主机服务
	hostService := service.NewHostService(db, cfg, logger)

	fmt.Println("✅ 基础组件初始化完成")

	// 测试1：创建增强AI服务
	fmt.Println("\n🔧 测试1: 创建增强AI服务")
	enhancedAIService := service.NewEnhancedAIService(db, cfg, logger, hostService)
	if enhancedAIService != nil {
		fmt.Println("✅ 增强AI服务创建成功")
	} else {
		fmt.Println("❌ 增强AI服务创建失败")
		return
	}

	// 测试2：创建革命性AI服务
	fmt.Println("\n🔧 测试2: 创建革命性AI服务")
	revolutionaryAIService := service.NewRevolutionaryAIService(db, cfg, logger, hostService)
	if revolutionaryAIService != nil {
		fmt.Println("✅ 革命性AI服务创建成功")
	} else {
		fmt.Println("❌ 革命性AI服务创建失败")
		return
	}

	// 测试3：创建统一适配器
	fmt.Println("\n🔧 测试3: 创建统一AI服务适配器")
	unifiedAdapter := service.NewUnifiedAIServiceAdapter(enhancedAIService, revolutionaryAIService, logger)
	if unifiedAdapter != nil {
		fmt.Println("✅ 统一AI服务适配器创建成功")
	} else {
		fmt.Println("❌ 统一AI服务适配器创建失败")
		return
	}

	// 测试4：测试消息处理
	fmt.Println("\n🔧 测试4: 测试消息处理")
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	req := &service.ProcessMessageRequest{
		SessionID: "test_session_123",
		UserID:    1,
		Message:   "列出主机",
	}

	fmt.Printf("📤 发送测试消息: %s\n", req.Message)

	response, err := unifiedAdapter.ProcessMessage(ctx, req)
	if err != nil {
		fmt.Printf("❌ 消息处理失败: %v\n", err)
		return
	}

	fmt.Println("✅ 消息处理成功")
	fmt.Printf("📥 响应内容: %s\n", response.Content)
	fmt.Printf("🎯 识别意图: %s\n", response.Intent)
	fmt.Printf("📊 置信度: %.2f\n", response.Confidence)
	fmt.Printf("⏱️ 处理时间: %v\n", response.ProcessingTime)

	// 测试5：检查是否使用了增强服务
	fmt.Println("\n🔧 测试5: 验证服务路由")

	if response.Parameters != nil {
		if fallbackUsed, ok := response.Parameters["fallback_used"]; ok && fallbackUsed.(bool) {
			fmt.Println("⚠️ 使用了降级服务")
		} else {
			fmt.Println("✅ 使用了增强服务")
		}
	} else {
		fmt.Println("✅ 使用了增强服务（无降级标记）")
	}

	// 测试6：测试其他消息类型
	fmt.Println("\n🔧 测试6: 测试其他消息类型")
	testMessages := []string{
		"添加主机 192.168.1.100 root password123",
		"查询主机状态",
		"系统监控",
		"你好",
	}

	for i, msg := range testMessages {
		fmt.Printf("\n📤 测试消息 %d: %s\n", i+1, msg)

		req.Message = msg
		response, err := unifiedAdapter.ProcessMessage(ctx, req)
		if err != nil {
			fmt.Printf("❌ 处理失败: %v\n", err)
			continue
		}

		fmt.Printf("📥 响应: %s\n", response.Content[:min(100, len(response.Content))])
		fmt.Printf("🎯 意图: %s (%.2f)\n", response.Intent, response.Confidence)
	}

	fmt.Println("\n🎉 统一AI服务适配器测试完成！")
	fmt.Println("================================")

	// 检查数据库连接
	if sqlDB, err := db.DB(); err == nil {
		sqlDB.Close()
	}
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
