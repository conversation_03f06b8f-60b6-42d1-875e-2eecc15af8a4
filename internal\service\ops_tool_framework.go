package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// OpsToolFramework 运维工具集成框架
type OpsToolFramework struct {
	logger    *logrus.Logger
	tools     map[string]OpsTool
	toolMutex sync.RWMutex

	// 工具执行器
	executor *ToolExecutor

	// 工具注册表
	registry *ToolRegistry
}

// OpsTool 运维工具接口
type OpsTool interface {
	// 基本信息
	GetName() string
	GetVersion() string
	GetDescription() string
	GetCategory() string
	GetTags() []string

	// 能力描述
	GetCapabilities() []ToolCapability
	GetSupportedTargets() []string
	GetRequiredParameters() []ParameterSpec

	// 执行方法
	Execute(ctx context.Context, request *ToolRequest) (*ToolResponse, error)
	Validate(request *ToolRequest) error

	// 生命周期
	Initialize(config map[string]interface{}) error
	Cleanup() error

	// 健康检查
	HealthCheck() error
}

// ToolCapability 工具能力
type ToolCapability struct {
	Name        string              `json:"name"`
	Description string              `json:"description"`
	InputType   string              `json:"input_type"`
	OutputType  string              `json:"output_type"`
	Parameters  []ParameterSpec     `json:"parameters"`
	Examples    []CapabilityExample `json:"examples"`
}

// ParameterSpec 参数规格
type ParameterSpec struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"` // string, int, bool, array, object
	Required    bool        `json:"required"`
	Description string      `json:"description"`
	Default     interface{} `json:"default,omitempty"`
	Validation  *Validation `json:"validation,omitempty"`
}

// Validation 参数验证规则
type Validation struct {
	MinLength *int     `json:"min_length,omitempty"`
	MaxLength *int     `json:"max_length,omitempty"`
	Pattern   *string  `json:"pattern,omitempty"`
	Enum      []string `json:"enum,omitempty"`
	Min       *float64 `json:"min,omitempty"`
	Max       *float64 `json:"max,omitempty"`
}

// CapabilityExample 能力示例
type CapabilityExample struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Input       map[string]interface{} `json:"input"`
	Output      map[string]interface{} `json:"output"`
}

// ToolRequest 工具请求
type ToolRequest struct {
	ToolName   string                 `json:"tool_name"`
	Capability string                 `json:"capability"`
	Parameters map[string]interface{} `json:"parameters"`
	Context    *RequestContext        `json:"context"`
	Timeout    time.Duration          `json:"timeout"`
}

// RequestContext 请求上下文
type RequestContext struct {
	UserID    int64                  `json:"user_id"`
	SessionID string                 `json:"session_id"`
	RequestID string                 `json:"request_id"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// ToolResponse 工具响应
type ToolResponse struct {
	Success   bool                   `json:"success"`
	Data      interface{}            `json:"data"`
	Message   string                 `json:"message"`
	Error     string                 `json:"error,omitempty"`
	Metadata  map[string]interface{} `json:"metadata"`
	Duration  time.Duration          `json:"duration"`
	Timestamp time.Time              `json:"timestamp"`
}

// ToolExecutor 工具执行器
type ToolExecutor struct {
	logger        *logrus.Logger
	maxConcurrent int
	semaphore     chan struct{}
}

// ToolRegistry 工具注册表
type ToolRegistry struct {
	tools        map[string]OpsTool
	categories   map[string][]string
	capabilities map[string][]ToolCapability
	mutex        sync.RWMutex
}

// NewOpsToolFramework 创建运维工具框架
func NewOpsToolFramework(logger *logrus.Logger) *OpsToolFramework {
	executor := &ToolExecutor{
		logger:        logger,
		maxConcurrent: 10,
		semaphore:     make(chan struct{}, 10),
	}

	registry := &ToolRegistry{
		tools:        make(map[string]OpsTool),
		categories:   make(map[string][]string),
		capabilities: make(map[string][]ToolCapability),
	}

	framework := &OpsToolFramework{
		logger:   logger,
		tools:    make(map[string]OpsTool),
		executor: executor,
		registry: registry,
	}

	// 注册内置工具
	framework.registerBuiltinTools()

	return framework
}

// RegisterTool 注册工具
func (otf *OpsToolFramework) RegisterTool(tool OpsTool) error {
	otf.toolMutex.Lock()
	defer otf.toolMutex.Unlock()

	name := tool.GetName()
	if _, exists := otf.tools[name]; exists {
		return fmt.Errorf("tool '%s' already registered", name)
	}

	// 验证工具
	if err := otf.validateTool(tool); err != nil {
		return fmt.Errorf("tool validation failed: %w", err)
	}

	// 初始化工具
	if err := tool.Initialize(nil); err != nil {
		return fmt.Errorf("tool initialization failed: %w", err)
	}

	// 注册到框架
	otf.tools[name] = tool
	otf.registry.registerTool(tool)

	otf.logger.WithFields(logrus.Fields{
		"tool_name":    name,
		"version":      tool.GetVersion(),
		"category":     tool.GetCategory(),
		"capabilities": len(tool.GetCapabilities()),
	}).Info("Tool registered successfully")

	return nil
}

// UnregisterTool 注销工具
func (otf *OpsToolFramework) UnregisterTool(toolName string) error {
	otf.toolMutex.Lock()
	defer otf.toolMutex.Unlock()

	tool, exists := otf.tools[toolName]
	if !exists {
		return fmt.Errorf("tool '%s' not found", toolName)
	}

	// 清理工具
	if err := tool.Cleanup(); err != nil {
		otf.logger.WithError(err).WithField("tool_name", toolName).Warn("Tool cleanup failed")
	}

	// 从注册表移除
	delete(otf.tools, toolName)
	otf.registry.unregisterTool(toolName)

	otf.logger.WithField("tool_name", toolName).Info("Tool unregistered successfully")

	return nil
}

// ExecuteTool 执行工具
func (otf *OpsToolFramework) ExecuteTool(ctx context.Context, request *ToolRequest) (*ToolResponse, error) {
	// 获取工具
	otf.toolMutex.RLock()
	tool, exists := otf.tools[request.ToolName]
	otf.toolMutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("tool '%s' not found", request.ToolName)
	}

	// 验证请求
	if err := tool.Validate(request); err != nil {
		return &ToolResponse{
			Success:   false,
			Error:     fmt.Sprintf("Request validation failed: %v", err),
			Timestamp: time.Now(),
		}, nil
	}

	// 执行工具
	return otf.executor.Execute(ctx, tool, request)
}

// GetAvailableTools 获取可用工具列表
func (otf *OpsToolFramework) GetAvailableTools() []ToolInfo {
	otf.toolMutex.RLock()
	defer otf.toolMutex.RUnlock()

	var tools []ToolInfo
	for _, tool := range otf.tools {
		tools = append(tools, ToolInfo{
			Name:         tool.GetName(),
			Version:      tool.GetVersion(),
			Description:  tool.GetDescription(),
			Category:     tool.GetCategory(),
			Tags:         tool.GetTags(),
			Capabilities: tool.GetCapabilities(),
		})
	}

	return tools
}

// GetToolsByCategory 按类别获取工具
func (otf *OpsToolFramework) GetToolsByCategory(category string) []ToolInfo {
	otf.toolMutex.RLock()
	defer otf.toolMutex.RUnlock()

	var tools []ToolInfo
	for _, tool := range otf.tools {
		if tool.GetCategory() == category {
			tools = append(tools, ToolInfo{
				Name:         tool.GetName(),
				Version:      tool.GetVersion(),
				Description:  tool.GetDescription(),
				Category:     tool.GetCategory(),
				Tags:         tool.GetTags(),
				Capabilities: tool.GetCapabilities(),
			})
		}
	}

	return tools
}

// HealthCheck 健康检查
func (otf *OpsToolFramework) HealthCheck() map[string]error {
	otf.toolMutex.RLock()
	defer otf.toolMutex.RUnlock()

	results := make(map[string]error)
	for name, tool := range otf.tools {
		results[name] = tool.HealthCheck()
	}

	return results
}

// ToolInfo 工具信息
type ToolInfo struct {
	Name         string           `json:"name"`
	Version      string           `json:"version"`
	Description  string           `json:"description"`
	Category     string           `json:"category"`
	Tags         []string         `json:"tags"`
	Capabilities []ToolCapability `json:"capabilities"`
}

// validateTool 验证工具
func (otf *OpsToolFramework) validateTool(tool OpsTool) error {
	if tool.GetName() == "" {
		return fmt.Errorf("tool name cannot be empty")
	}

	if tool.GetVersion() == "" {
		return fmt.Errorf("tool version cannot be empty")
	}

	if tool.GetCategory() == "" {
		return fmt.Errorf("tool category cannot be empty")
	}

	capabilities := tool.GetCapabilities()
	if len(capabilities) == 0 {
		return fmt.Errorf("tool must have at least one capability")
	}

	// 验证能力
	for _, capability := range capabilities {
		if capability.Name == "" {
			return fmt.Errorf("capability name cannot be empty")
		}
	}

	return nil
}

// registerBuiltinTools 注册内置工具
func (otf *OpsToolFramework) registerBuiltinTools() {
	// 这里可以注册一些内置的基础工具
	// 例如：系统信息工具、网络工具、文件操作工具等

	otf.logger.Info("Built-in tools registration completed")
}

// Execute 执行工具（ToolExecutor方法）
func (te *ToolExecutor) Execute(ctx context.Context, tool OpsTool, request *ToolRequest) (*ToolResponse, error) {
	// 获取信号量
	select {
	case te.semaphore <- struct{}{}:
		defer func() { <-te.semaphore }()
	case <-ctx.Done():
		return &ToolResponse{
			Success:   false,
			Error:     "Execution cancelled due to context timeout",
			Timestamp: time.Now(),
		}, ctx.Err()
	}

	start := time.Now()

	te.logger.WithFields(logrus.Fields{
		"tool_name":  request.ToolName,
		"capability": request.Capability,
		"user_id":    request.Context.UserID,
		"request_id": request.Context.RequestID,
	}).Info("Starting tool execution")

	// 设置超时
	execCtx := ctx
	if request.Timeout > 0 {
		var cancel context.CancelFunc
		execCtx, cancel = context.WithTimeout(ctx, request.Timeout)
		defer cancel()
	}

	// 执行工具
	response, err := tool.Execute(execCtx, request)
	duration := time.Since(start)

	if err != nil {
		te.logger.WithError(err).WithFields(logrus.Fields{
			"tool_name":  request.ToolName,
			"duration":   duration,
			"request_id": request.Context.RequestID,
		}).Error("Tool execution failed")

		return &ToolResponse{
			Success:   false,
			Error:     err.Error(),
			Duration:  duration,
			Timestamp: time.Now(),
		}, nil
	}

	// 设置响应时间信息
	if response != nil {
		response.Duration = duration
		response.Timestamp = time.Now()
	}

	te.logger.WithFields(logrus.Fields{
		"tool_name":  request.ToolName,
		"success":    response.Success,
		"duration":   duration,
		"request_id": request.Context.RequestID,
	}).Info("Tool execution completed")

	return response, nil
}

// registerTool 注册工具到注册表（ToolRegistry方法）
func (tr *ToolRegistry) registerTool(tool OpsTool) {
	tr.mutex.Lock()
	defer tr.mutex.Unlock()

	name := tool.GetName()
	category := tool.GetCategory()
	capabilities := tool.GetCapabilities()

	// 注册工具
	tr.tools[name] = tool

	// 按类别分组
	if _, exists := tr.categories[category]; !exists {
		tr.categories[category] = make([]string, 0)
	}
	tr.categories[category] = append(tr.categories[category], name)

	// 注册能力
	tr.capabilities[name] = capabilities
}

// unregisterTool 从注册表注销工具（ToolRegistry方法）
func (tr *ToolRegistry) unregisterTool(toolName string) {
	tr.mutex.Lock()
	defer tr.mutex.Unlock()

	tool, exists := tr.tools[toolName]
	if !exists {
		return
	}

	category := tool.GetCategory()

	// 从工具列表移除
	delete(tr.tools, toolName)

	// 从类别分组移除
	if tools, exists := tr.categories[category]; exists {
		for i, name := range tools {
			if name == toolName {
				tr.categories[category] = append(tools[:i], tools[i+1:]...)
				break
			}
		}
		// 如果类别下没有工具了，删除类别
		if len(tr.categories[category]) == 0 {
			delete(tr.categories, category)
		}
	}

	// 移除能力
	delete(tr.capabilities, toolName)
}

// GetToolsByCategory 按类别获取工具（ToolRegistry方法）
func (tr *ToolRegistry) GetToolsByCategory(category string) []string {
	tr.mutex.RLock()
	defer tr.mutex.RUnlock()

	if tools, exists := tr.categories[category]; exists {
		// 返回副本
		result := make([]string, len(tools))
		copy(result, tools)
		return result
	}

	return nil
}

// GetAllCategories 获取所有类别（ToolRegistry方法）
func (tr *ToolRegistry) GetAllCategories() []string {
	tr.mutex.RLock()
	defer tr.mutex.RUnlock()

	categories := make([]string, 0, len(tr.categories))
	for category := range tr.categories {
		categories = append(categories, category)
	}

	return categories
}

// GetToolCapabilities 获取工具能力（ToolRegistry方法）
func (tr *ToolRegistry) GetToolCapabilities(toolName string) []ToolCapability {
	tr.mutex.RLock()
	defer tr.mutex.RUnlock()

	if capabilities, exists := tr.capabilities[toolName]; exists {
		// 返回副本
		result := make([]ToolCapability, len(capabilities))
		copy(result, capabilities)
		return result
	}

	return nil
}
