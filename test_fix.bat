@echo off
echo 🧪 测试主机列表功能修复
echo ====================================================

echo.
echo 1️⃣ 创建测试会话...
curl -X POST "http://localhost:8080/api/v1/chat/sessions" ^
     -H "Content-Type: application/json" ^
     -d "{\"title\":\"测试会话\"}" > session_response.json

echo.
echo 📋 会话创建响应:
type session_response.json

echo.
echo.
echo 2️⃣ 测试"列出主机"指令...
echo 注意：需要手动从上面的响应中复制session_id来测试

echo.
echo 示例测试命令:
echo curl -X POST "http://localhost:8080/api/v1/chat/message" ^
echo      -H "Content-Type: application/json" ^
echo      -d "{\"session_id\":\"YOUR_SESSION_ID\",\"content\":\"列出主机\",\"stream\":false}"

echo.
echo ✅ 测试脚本完成！请手动执行上面的命令来测试
pause
