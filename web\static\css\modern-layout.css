/* ========================================
   现代化AI对话界面布局系统
   参考ChatGPT、Claude等主流产品设计
   ======================================== */

/* CSS变量系统 - 设计令牌 */
:root {
  /* 颜色系统 */
  --color-primary: #2563eb;
  --color-primary-hover: #1d4ed8;
  --color-primary-light: #dbeafe;
  --color-secondary: #64748b;
  --color-success: #059669;
  --color-warning: #d97706;
  --color-error: #dc2626;
  --color-info: #0891b2;

  /* 背景颜色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --bg-tertiary: #f1f5f9;
  --bg-hover: #f3f4f6;
  --bg-active: #e5e7eb;

  /* 文字颜色 */
  --text-primary: #0f172a;
  --text-secondary: #475569;
  --text-tertiary: #94a3b8;
  --text-inverse: #ffffff;

  /* 边框颜色 */
  --border-primary: #e2e8f0;
  --border-secondary: #cbd5e1;
  --border-focus: #2563eb;

  /* 阴影系统 */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
  --shadow-xl: 0 20px 25px -5px rgb(0 0 0 / 0.1), 0 8px 10px -6px rgb(0 0 0 / 0.1);

  /* 圆角系统 */
  --radius-sm: 0.25rem;
  --radius-md: 0.375rem;
  --radius-lg: 0.5rem;
  --radius-xl: 0.75rem;
  --radius-2xl: 1rem;

  /* 间距系统 */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;

  /* 字体系统 */
  --font-family-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  --font-family-mono: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  
  --font-size-xs: 0.75rem;
  --font-size-sm: 0.875rem;
  --font-size-base: 1rem;
  --font-size-lg: 1.125rem;
  --font-size-xl: 1.25rem;
  --font-size-2xl: 1.5rem;
  --font-size-3xl: 1.875rem;

  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* 动画系统 */
  --duration-fast: 150ms;
  --duration-normal: 250ms;
  --duration-slow: 350ms;
  --easing-ease: cubic-bezier(0.4, 0, 0.2, 1);
  --easing-ease-in: cubic-bezier(0.4, 0, 1, 1);
  --easing-ease-out: cubic-bezier(0, 0, 0.2, 1);

  /* 布局尺寸 */
  --sidebar-width: 240px;
  --sidebar-collapsed-width: 60px;
  --navbar-height: 60px;
  --assistant-panel-width: 280px;
}

/* 深色主题 */
[data-theme="dark"] {
  --bg-primary: #0f172a;
  --bg-secondary: #1e293b;
  --bg-tertiary: #334155;
  --bg-hover: #475569;
  --bg-active: #64748b;

  --text-primary: #f8fafc;
  --text-secondary: #cbd5e1;
  --text-tertiary: #94a3b8;

  --border-primary: #334155;
  --border-secondary: #475569;

  --color-primary: #3b82f6;
  --color-primary-hover: #2563eb;
  --color-primary-light: #1e3a8a;
}

/* 基础重置和全局样式 */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family-sans);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  margin: 0;
  padding: 0;
  overflow: hidden;
}

/* 主应用容器 */
.app-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background-color: var(--bg-primary);
}

/* 顶部导航栏 */
.app-navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--navbar-height);
  padding: 0 var(--space-4);
  background-color: var(--bg-secondary);
  border-bottom: 1px solid var(--border-primary);
  z-index: 50;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: var(--space-3);
}

.navbar-center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: var(--space-2);
}

/* 侧边栏切换按钮 */
.sidebar-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  background: none;
  color: var(--text-secondary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.sidebar-toggle:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

/* 应用品牌 */
.app-brand {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  text-decoration: none;
  color: var(--text-primary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-lg);
}

.brand-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border-radius: var(--radius-lg);
  color: white;
  font-size: 18px;
}

/* 当前对话标题 */
.current-chat-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 主布局区域 */
.app-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧边栏 */
.sidebar {
  width: var(--sidebar-width);
  background-color: var(--bg-secondary);
  border-right: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  transition: width var(--duration-normal) var(--easing-ease);
  z-index: 40;
}

.sidebar.collapsed {
  width: var(--sidebar-collapsed-width);
}

.sidebar-header {
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.new-chat-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-3) var(--space-4);
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  color: white;
  border: none;
  border-radius: var(--radius-lg);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.new-chat-btn:hover {
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.sidebar-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4);
}

/* 对话历史区域 */
.chat-history-section {
  margin-bottom: var(--space-6);
}

.section-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-3);
}

.section-header h6 {
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.search-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.search-toggle:hover {
  background-color: var(--bg-hover);
  color: var(--text-secondary);
}

/* 对话搜索 */
.chat-search {
  margin-bottom: var(--space-3);
}

.chat-search input {
  width: 100%;
  padding: var(--space-2) var(--space-3);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  background-color: var(--bg-primary);
  color: var(--text-primary);
  font-size: var(--font-size-sm);
  transition: all var(--duration-fast) var(--easing-ease);
}

.chat-search input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

/* 快捷功能区域 */
.quick-functions-section {
  margin-bottom: var(--space-6);
}

.quick-functions {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.quick-function-btn {
  display: flex;
  align-items: center;
  gap: var(--space-3);
  width: 100%;
  padding: var(--space-3);
  background: none;
  border: none;
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
  text-align: left;
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.quick-function-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-primary);
}

.quick-function-btn i {
  width: 16px;
  text-align: center;
}

/* 主对话区域 */
.chat-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: var(--bg-primary);
  overflow: hidden;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 100%;
  margin: 0 auto;
  padding: var(--space-6);
  overflow: hidden;
}

/* 对话消息区域 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4) 0;
  scroll-behavior: smooth;
}

/* 欢迎界面 */
.welcome-screen {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  text-align: center;
  padding: var(--space-8);
}

.welcome-avatar {
  width: 80px;
  height: 80px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 2rem;
  margin-bottom: var(--space-6);
  box-shadow: var(--shadow-lg);
}

.welcome-title {
  font-size: var(--font-size-3xl);
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  margin-bottom: var(--space-4);
}

.welcome-description {
  font-size: var(--font-size-lg);
  color: var(--text-secondary);
  max-width: 600px;
  line-height: 1.6;
  margin-bottom: var(--space-8);
}

/* 功能卡片 */
.feature-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: var(--space-4);
  margin-bottom: var(--space-8);
  max-width: 1000px;
}

.feature-card {
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-6);
  cursor: pointer;
  transition: all var(--duration-normal) var(--easing-ease);
}

.feature-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-lg);
  border-color: var(--color-primary);
}

.feature-icon {
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border-radius: var(--radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1.5rem;
  margin-bottom: var(--space-4);
}

.feature-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
  margin-bottom: var(--space-2);
}

.feature-desc {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-bottom: var(--space-3);
}

.feature-example {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  font-style: italic;
}

/* 快捷操作按钮 */
.quick-actions {
  display: flex;
  gap: var(--space-3);
  justify-content: center;
  flex-wrap: wrap;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-4);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  min-width: 100px;
}

.quick-action-btn:hover {
  background-color: var(--bg-hover);
  border-color: var(--color-primary);
  color: var(--text-primary);
  transform: translateY(-1px);
}

.quick-action-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 1rem;
}

.quick-action-text {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
}

/* 对话输入区域 */
.chat-input-area {
  padding: var(--space-4) 0;
  border-top: 1px solid var(--border-primary);
  background-color: var(--bg-primary);
}

.chat-input-container {
  max-width: 800px;
  margin: 0 auto;
}

.input-wrapper {
  display: flex;
  align-items: flex-end;
  gap: var(--space-3);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-3);
  transition: all var(--duration-fast) var(--easing-ease);
}

.input-wrapper:focus-within {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 3px rgb(37 99 235 / 0.1);
}

#message-input {
  flex: 1;
  border: none;
  background: none;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  line-height: 1.5;
  resize: none;
  outline: none;
  min-height: 24px;
  max-height: 120px;
  padding: var(--space-2) 0;
}

#message-input::placeholder {
  color: var(--text-tertiary);
}

.send-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  border: none;
  border-radius: var(--radius-lg);
  color: white;
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.send-button:hover {
  transform: scale(1.05);
  box-shadow: var(--shadow-md);
}

.send-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* 输入建议 */
.input-suggestions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-3);
  padding: 0 var(--space-2);
}

.suggestion-label {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  font-weight: var(--font-weight-medium);
}

.suggestion-btn {
  padding: var(--space-1) var(--space-3);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.suggestion-btn:hover {
  background-color: var(--bg-hover);
  border-color: var(--color-primary);
  color: var(--text-primary);
}

/* 右侧辅助面板 */
.assistant-panel {
  width: var(--assistant-panel-width);
  background-color: var(--bg-secondary);
  border-left: 1px solid var(--border-primary);
  display: flex;
  flex-direction: column;
  transition: width var(--duration-normal) var(--easing-ease);
  z-index: 30;
}

.assistant-panel.collapsed {
  width: 60px;
}

/* 简化后的状态卡片样式 */
.status-card {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.status-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
  font-weight: 600;
  color: var(--text-primary);
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
  border-bottom: 1px solid var(--border-secondary);
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  color: var(--text-secondary);
  font-size: var(--font-size-sm);
}

.status-value {
  color: var(--text-primary);
  font-weight: 600;
  font-size: var(--font-size-sm);
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-4);
  border-bottom: 1px solid var(--border-primary);
}

.panel-header h6 {
  margin: 0;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.panel-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.panel-toggle:hover {
  background-color: var(--bg-hover);
  color: var(--text-secondary);
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: var(--space-4);
}

/* 状态卡片 */
.status-card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.status-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.status-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--space-2) 0;
}

.status-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.status-value {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

/* 快速操作卡片 */
.quick-ops-card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.ops-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.ops-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.ops-btn {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  width: 100%;
  padding: var(--space-2) var(--space-3);
  background: none;
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  color: var(--text-secondary);
  font-size: var(--font-size-xs);
  text-align: left;
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.ops-btn:hover {
  background-color: var(--bg-hover);
  border-color: var(--color-primary);
  color: var(--text-primary);
}

/* 告警卡片 */
.alerts-card {
  background-color: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  padding: var(--space-4);
  margin-bottom: var(--space-4);
}

.alerts-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-bottom: var(--space-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.alerts-content {
  display: flex;
  flex-direction: column;
  gap: var(--space-2);
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-2);
  padding: var(--space-2);
  border-radius: var(--radius-md);
  transition: all var(--duration-fast) var(--easing-ease);
}

.alert-item:hover {
  background-color: var(--bg-hover);
}

.alert-item.warning {
  border-left: 3px solid var(--color-warning);
}

.alert-item.info {
  border-left: 3px solid var(--color-info);
}

.alert-item.error {
  border-left: 3px solid var(--color-error);
}

.alert-icon {
  font-size: var(--font-size-sm);
}

.alert-text {
  flex: 1;
}

.alert-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
}

.alert-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .assistant-panel {
    display: none;
  }

  .chat-container {
    padding: var(--space-4);
  }

  .feature-cards {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: var(--space-3);
  }
}

@media (max-width: 768px) {
  :root {
    --sidebar-width: 100vw;
    --navbar-height: 56px;
  }

  .app-navbar {
    padding: 0 var(--space-3);
  }

  .navbar-center {
    display: none;
  }

  .sidebar {
    position: fixed;
    top: var(--navbar-height);
    left: 0;
    height: calc(100vh - var(--navbar-height));
    z-index: 60;
    transform: translateX(-100%);
    transition: transform var(--duration-normal) var(--easing-ease);
  }

  .sidebar.open {
    transform: translateX(0);
  }

  .chat-container {
    padding: var(--space-3);
  }

  .feature-cards {
    grid-template-columns: 1fr;
    gap: var(--space-3);
  }

  .quick-actions {
    grid-template-columns: repeat(2, 1fr);
  }

  .welcome-title {
    font-size: var(--font-size-2xl);
  }

  .welcome-description {
    font-size: var(--font-size-base);
  }
}

@media (max-width: 480px) {
  .app-navbar {
    padding: 0 var(--space-2);
  }

  .brand-text {
    display: none;
  }

  .navbar-right {
    gap: var(--space-1);
  }

  .chat-container {
    padding: var(--space-2);
  }

  .welcome-avatar {
    width: 60px;
    height: 60px;
    font-size: 1.5rem;
  }

  .welcome-title {
    font-size: var(--font-size-xl);
  }

  .feature-card {
    padding: var(--space-4);
  }

  .quick-actions {
    grid-template-columns: 1fr;
  }
}

/* 动画和过渡效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* 应用动画 */
.fade-in {
  animation: fadeIn var(--duration-normal) var(--easing-ease);
}

.slide-in-left {
  animation: slideInLeft var(--duration-normal) var(--easing-ease);
}

.slide-in-right {
  animation: slideInRight var(--duration-normal) var(--easing-ease);
}

.pulse {
  animation: pulse 2s infinite;
}

/* 滚动条样式 */
.sidebar-content::-webkit-scrollbar,
.chat-messages::-webkit-scrollbar,
.panel-content::-webkit-scrollbar {
  width: 6px;
}

.sidebar-content::-webkit-scrollbar-track,
.chat-messages::-webkit-scrollbar-track,
.panel-content::-webkit-scrollbar-track {
  background: transparent;
}

.sidebar-content::-webkit-scrollbar-thumb,
.chat-messages::-webkit-scrollbar-thumb,
.panel-content::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover,
.chat-messages::-webkit-scrollbar-thumb:hover,
.panel-content::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
}

/* 焦点可见性 */
.keyboard-user *:focus {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* 减少动画偏好 */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* 高对比度支持 */
@media (prefers-contrast: high) {
  :root {
    --border-primary: #000000;
    --border-secondary: #000000;
    --text-tertiary: var(--text-secondary);
  }

  [data-theme="dark"] {
    --border-primary: #ffffff;
    --border-secondary: #ffffff;
  }
}
