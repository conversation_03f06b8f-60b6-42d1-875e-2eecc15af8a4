package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EmotionMessage 情感分析专用消息类型
type EmotionMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// EmotionChatResponse 情感分析专用响应类型
type EmotionChatResponse struct {
	Choices []EmotionChoice `json:"choices"`
	Usage   EmotionUsage    `json:"usage"`
}

// EmotionChoice 情感分析选择项
type EmotionChoice struct {
	Message EmotionChatMessage `json:"message"`
}

// EmotionChatMessage 情感分析聊天消息
type EmotionChatMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// EmotionUsage 情感分析使用情况
type EmotionUsage struct {
	TotalTokens int `json:"total_tokens"`
}

// EmotionalIntelligenceEngine 情感智能引擎
type EmotionalIntelligenceEngine struct {
	db                *gorm.DB
	logger            *logrus.Logger
	emotionRecognizer *EmotionRecognizer
	emotionExpressor  *EmotionExpressor
	emotionMemory     *EmotionMemory
	deepseekService   interface {
		Chat(ctx context.Context, messages []EmotionMessage) (*EmotionChatResponse, error)
	}
	config            *EmotionalIntelligenceConfig
	mutex             sync.RWMutex
	isRunning         bool
	processedMessages int64
	emotionAccuracy   float64
	lastOptimization  time.Time
}

// EmotionalIntelligenceConfig 情感智能配置
type EmotionalIntelligenceConfig struct {
	EnableEmotionRecognition bool          `json:"enable_emotion_recognition"`
	EnableEmotionExpression  bool          `json:"enable_emotion_expression"`
	EnableEmotionMemory      bool          `json:"enable_emotion_memory"`
	EmotionThreshold         float64       `json:"emotion_threshold"`
	MemoryRetentionDays      int           `json:"memory_retention_days"`
	AdaptationRate           float64       `json:"adaptation_rate"`
	MaxEmotionHistory        int           `json:"max_emotion_history"`
	EnableDeepSeekEmotion    bool          `json:"enable_deepseek_emotion"`
	EmotionUpdateInterval    time.Duration `json:"emotion_update_interval"`
}

// EmotionType 情感类型
type EmotionType string

const (
	EmotionJoy         EmotionType = "joy"         // 喜悦
	EmotionSadness     EmotionType = "sadness"     // 悲伤
	EmotionAnger       EmotionType = "anger"       // 愤怒
	EmotionFear        EmotionType = "fear"        // 恐惧
	EmotionSurprise    EmotionType = "surprise"    // 惊讶
	EmotionDisgust     EmotionType = "disgust"     // 厌恶
	EmotionNeutral     EmotionType = "neutral"     // 中性
	EmotionFrustration EmotionType = "frustration" // 沮丧
	EmotionExcitement  EmotionType = "excitement"  // 兴奋
	EmotionAnxiety     EmotionType = "anxiety"     // 焦虑
	EmotionRelief      EmotionType = "relief"      // 放松
	EmotionConfidence  EmotionType = "confidence"  // 自信
)

// EmotionState 情感状态
type EmotionState struct {
	PrimaryEmotion   EmotionType            `json:"primary_emotion"`
	SecondaryEmotion EmotionType            `json:"secondary_emotion"`
	Intensity        float64                `json:"intensity"`  // 0.0-1.0
	Confidence       float64                `json:"confidence"` // 0.0-1.0
	Context          string                 `json:"context"`
	Timestamp        time.Time              `json:"timestamp"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// EmotionAnalysisResult 情感分析结果
type EmotionAnalysisResult struct {
	UserEmotion    *EmotionState          `json:"user_emotion"`
	SuggestedTone  string                 `json:"suggested_tone"`
	ResponseStyle  string                 `json:"response_style"`
	EmpatheticCues []string               `json:"empathetic_cues"`
	Adaptations    map[string]interface{} `json:"adaptations"`
	Confidence     float64                `json:"confidence"`
}

// EmotionMemoryRecord 情感记忆记录
type EmotionMemoryRecord struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID        int64     `json:"user_id" gorm:"index;not null"`
	SessionID     string    `json:"session_id" gorm:"index"`
	EmotionType   string    `json:"emotion_type" gorm:"size:50;not null"`
	Intensity     float64   `json:"intensity"`
	Confidence    float64   `json:"confidence"`
	Context       string    `json:"context" gorm:"type:text"`
	MessageText   string    `json:"message_text" gorm:"type:text"`
	ResponseStyle string    `json:"response_style" gorm:"size:100"`
	Metadata      string    `json:"metadata" gorm:"type:text"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// NewEmotionalIntelligenceEngine 创建情感智能引擎
func NewEmotionalIntelligenceEngine(
	db *gorm.DB,
	logger *logrus.Logger,
	deepseekService interface {
		Chat(ctx context.Context, messages []EmotionMessage) (*EmotionChatResponse, error)
	},
	config *EmotionalIntelligenceConfig,
) *EmotionalIntelligenceEngine {
	if config == nil {
		config = &EmotionalIntelligenceConfig{
			EnableEmotionRecognition: true,
			EnableEmotionExpression:  true,
			EnableEmotionMemory:      true,
			EmotionThreshold:         0.6,
			MemoryRetentionDays:      30,
			AdaptationRate:           0.1,
			MaxEmotionHistory:        100,
			EnableDeepSeekEmotion:    true,
			EmotionUpdateInterval:    time.Hour,
		}
	}

	engine := &EmotionalIntelligenceEngine{
		db:               db,
		logger:           logger,
		deepseekService:  deepseekService,
		config:           config,
		isRunning:        false,
		lastOptimization: time.Now(),
	}

	// 初始化子模块
	engine.emotionRecognizer = NewEmotionRecognizer(logger, deepseekService, config)
	engine.emotionExpressor = NewEmotionExpressor(logger, config)
	engine.emotionMemory = NewEmotionMemory(db, logger, config)

	// 确保数据库表存在
	if err := db.AutoMigrate(&EmotionMemoryRecord{}); err != nil {
		logger.WithError(err).Error("Failed to migrate emotion memory table")
	}

	logger.Info("情感智能引擎初始化完成")
	return engine
}

// Start 启动情感智能引擎
func (eie *EmotionalIntelligenceEngine) Start() error {
	eie.mutex.Lock()
	defer eie.mutex.Unlock()

	if eie.isRunning {
		return fmt.Errorf("emotional intelligence engine is already running")
	}

	eie.isRunning = true
	eie.logger.Info("🧠 情感智能引擎启动成功")

	// 启动后台优化任务
	go eie.runOptimizationLoop()

	return nil
}

// Stop 停止情感智能引擎
func (eie *EmotionalIntelligenceEngine) Stop() error {
	eie.mutex.Lock()
	defer eie.mutex.Unlock()

	if !eie.isRunning {
		return fmt.Errorf("emotional intelligence engine is not running")
	}

	eie.isRunning = false
	eie.logger.Info("情感智能引擎已停止")
	return nil
}

// AnalyzeEmotion 分析用户情感
func (eie *EmotionalIntelligenceEngine) AnalyzeEmotion(
	ctx context.Context,
	userID int64,
	sessionID string,
	message string,
	context map[string]interface{},
) (*EmotionAnalysisResult, error) {
	start := time.Now()

	eie.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"session_id": sessionID,
		"message":    message,
	}).Info("🎭 开始情感分析")

	// 1. 识别用户情感
	userEmotion, err := eie.emotionRecognizer.RecognizeEmotion(ctx, message, context)
	if err != nil {
		eie.logger.WithError(err).Error("情感识别失败")
		return eie.createFallbackAnalysis(message), nil
	}

	// 2. 获取历史情感记忆
	emotionHistory, err := eie.emotionMemory.GetUserEmotionHistory(userID, 10)
	if err != nil {
		eie.logger.WithError(err).Warn("获取情感历史失败")
	}

	// 3. 生成响应策略
	responseStrategy := eie.emotionExpressor.GenerateResponseStrategy(userEmotion, emotionHistory)

	// 4. 保存情感记忆
	if eie.config.EnableEmotionMemory {
		err = eie.emotionMemory.SaveEmotionRecord(&EmotionMemoryRecord{
			UserID:        userID,
			SessionID:     sessionID,
			EmotionType:   string(userEmotion.PrimaryEmotion),
			Intensity:     userEmotion.Intensity,
			Confidence:    userEmotion.Confidence,
			Context:       userEmotion.Context,
			MessageText:   message,
			ResponseStyle: responseStrategy.Style,
			Metadata:      eie.serializeMetadata(userEmotion.Metadata),
			CreatedAt:     time.Now(),
		})
		if err != nil {
			eie.logger.WithError(err).Error("保存情感记忆失败")
		}
	}

	// 5. 构建分析结果
	result := &EmotionAnalysisResult{
		UserEmotion:    userEmotion,
		SuggestedTone:  responseStrategy.Tone,
		ResponseStyle:  responseStrategy.Style,
		EmpatheticCues: responseStrategy.EmpatheticCues,
		Adaptations:    responseStrategy.Adaptations,
		Confidence:     userEmotion.Confidence,
	}

	// 更新统计信息
	eie.processedMessages++
	processingTime := time.Since(start)

	eie.logger.WithFields(logrus.Fields{
		"user_id":         userID,
		"primary_emotion": userEmotion.PrimaryEmotion,
		"intensity":       userEmotion.Intensity,
		"confidence":      userEmotion.Confidence,
		"processing_time": processingTime,
	}).Info("🎭 情感分析完成")

	return result, nil
}

// EnhanceResponse 增强AI响应的情感表达
func (eie *EmotionalIntelligenceEngine) EnhanceResponse(
	ctx context.Context,
	originalResponse string,
	emotionAnalysis *EmotionAnalysisResult,
	userID int64,
) (string, error) {
	if !eie.config.EnableEmotionExpression {
		return originalResponse, nil
	}

	eie.logger.WithFields(logrus.Fields{
		"user_id":         userID,
		"original_length": len(originalResponse),
		"emotion":         emotionAnalysis.UserEmotion.PrimaryEmotion,
		"suggested_tone":  emotionAnalysis.SuggestedTone,
	}).Info("🎨 开始增强响应的情感表达")

	// 使用情感表达器增强响应
	enhancedResponse := eie.emotionExpressor.EnhanceResponse(
		originalResponse,
		emotionAnalysis,
	)

	eie.logger.WithFields(logrus.Fields{
		"user_id":          userID,
		"enhanced_length":  len(enhancedResponse),
		"enhancement_rate": float64(len(enhancedResponse)) / float64(len(originalResponse)),
	}).Info("🎨 响应情感增强完成")

	return enhancedResponse, nil
}

// GetUserEmotionalProfile 获取用户情感画像
func (eie *EmotionalIntelligenceEngine) GetUserEmotionalProfile(userID int64) (*UserEmotionalProfile, error) {
	return eie.emotionMemory.GetUserEmotionalProfile(userID)
}

// createFallbackAnalysis 创建降级分析结果
func (eie *EmotionalIntelligenceEngine) createFallbackAnalysis(message string) *EmotionAnalysisResult {
	// 简单的关键词情感分析作为降级方案
	emotion := EmotionNeutral
	intensity := 0.5

	// 基础关键词检测
	messageLower := strings.ToLower(message)
	if strings.Contains(messageLower, "错误") || strings.Contains(messageLower, "失败") || strings.Contains(messageLower, "问题") {
		emotion = EmotionFrustration
		intensity = 0.7
	} else if strings.Contains(messageLower, "谢谢") || strings.Contains(messageLower, "好的") || strings.Contains(messageLower, "成功") {
		emotion = EmotionJoy
		intensity = 0.6
	} else if strings.Contains(messageLower, "急") || strings.Contains(messageLower, "快") || strings.Contains(messageLower, "紧急") {
		emotion = EmotionAnxiety
		intensity = 0.8
	}

	return &EmotionAnalysisResult{
		UserEmotion: &EmotionState{
			PrimaryEmotion: emotion,
			Intensity:      intensity,
			Confidence:     0.5,
			Context:        "fallback_analysis",
			Timestamp:      time.Now(),
		},
		SuggestedTone:  "professional",
		ResponseStyle:  "supportive",
		EmpatheticCues: []string{"我理解您的情况"},
		Adaptations:    make(map[string]interface{}),
		Confidence:     0.5,
	}
}

// serializeMetadata 序列化元数据
func (eie *EmotionalIntelligenceEngine) serializeMetadata(metadata map[string]interface{}) string {
	if metadata == nil {
		return "{}"
	}
	data, _ := json.Marshal(metadata)
	return string(data)
}

// runOptimizationLoop 运行优化循环
func (eie *EmotionalIntelligenceEngine) runOptimizationLoop() {
	ticker := time.NewTicker(eie.config.EmotionUpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !eie.isRunning {
				return
			}
			eie.performOptimization()
		}
	}
}

// performOptimization 执行优化
func (eie *EmotionalIntelligenceEngine) performOptimization() {
	eie.logger.Info("🔧 开始情感智能引擎优化")

	// 清理过期的情感记忆
	if err := eie.emotionMemory.CleanupExpiredRecords(); err != nil {
		eie.logger.WithError(err).Error("清理过期情感记忆失败")
	}

	// 更新情感识别准确率
	eie.updateEmotionAccuracy()

	eie.lastOptimization = time.Now()
	eie.logger.Info("🔧 情感智能引擎优化完成")
}

// updateEmotionAccuracy 更新情感识别准确率
func (eie *EmotionalIntelligenceEngine) updateEmotionAccuracy() {
	// 这里可以实现基于用户反馈的准确率计算
	// 暂时使用模拟数据
	eie.emotionAccuracy = 0.85 + math.Sin(float64(time.Now().Unix()))*0.1
}

// GetMetrics 获取引擎指标
func (eie *EmotionalIntelligenceEngine) GetMetrics() map[string]interface{} {
	eie.mutex.RLock()
	defer eie.mutex.RUnlock()

	return map[string]interface{}{
		"is_running":         eie.isRunning,
		"processed_messages": eie.processedMessages,
		"emotion_accuracy":   eie.emotionAccuracy,
		"last_optimization":  eie.lastOptimization,
		"config":             eie.config,
	}
}
