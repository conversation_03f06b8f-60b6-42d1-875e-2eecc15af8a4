package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/service"
	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockHostService 模拟主机服务
type MockHostService struct{}

func (m *MockHostService) CreateHost(req *model.HostCreateRequest) (*model.HostResponse, error) {
	return nil, nil
}

func (m *MockHostService) GetHostByID(id int64) (*model.HostResponse, error) {
	return nil, nil
}

func (m *MockHostService) GetHostByName(name string) (*model.HostResponse, error) {
	return nil, nil
}

func (m *MockHostService) UpdateHost(id int64, req *model.HostUpdateRequest) (*model.HostResponse, error) {
	return nil, nil
}

func (m *MockHostService) DeleteHost(id int64) error {
	return nil
}

func (m *MockHostService) ListHosts(req *model.HostListQuery) (*model.HostListResponse, error) {
	return nil, nil
}

func (m *MockHostService) TestConnection(id int64) (*model.HostTestResponse, error) {
	return &model.HostTestResponse{
		Success:  true,
		Message:  "Connection successful",
		Duration: 100,
		TestedAt: time.Now(),
	}, nil
}

func (m *MockHostService) ExecuteCommand(id int64, req *model.CommandExecuteRequest) (*model.CommandExecuteResponse, error) {
	// 模拟不同命令的输出
	var stdout string
	switch {
	case req.Command == "uptime && whoami && uname -a":
		stdout = " 10:30:01 up 5 days,  2:15,  1 user,  load average: 0.15, 0.05, 0.01\nroot\nLinux test-server 5.4.0-74-generic #83-Ubuntu SMP Sat May 8 02:35:39 UTC 2021 x86_64 x86_64 x86_64 GNU/Linux"
	case req.Command == "top -bn1 | grep 'Cpu(s)' && cat /proc/loadavg":
		stdout = "%Cpu(s): 85.3 us,  5.0 sy,  0.0 ni,  9.7 id,  0.0 wa,  0.0 hi,  0.0 si,  0.0 st\n2.15 1.85 1.65 3/123 12345"
	case req.Command == "free -h && cat /proc/meminfo | head -10":
		stdout = "              total        used        free      shared  buff/cache   available\nMem:           7.8G        6.1G        0.2G        123M        1.5G        1.4G\nSwap:          2.0G        0.5G        1.5G\nMemTotal:        8000000 kB\nMemFree:          200000 kB\nMemAvailable:    1400000 kB"
	case req.Command == "df -h && du -sh /var/log /tmp":
		stdout = "Filesystem      Size  Used Avail Use% Mounted on\n/dev/sda1        20G   18G  1.2G  95% /\n/dev/sda2       100G   85G   10G  89% /home\n2.5G\t/var/log\n1.2G\t/tmp"
	case req.Command == "netstat -tuln | head -10 && ss -tuln | head -5":
		stdout = "Active Internet connections (only servers)\nProto Recv-Q Send-Q Local Address           Foreign Address         State\ntcp        0      0 0.0.0.0:22              0.0.0.0:*               LISTEN\ntcp        0      0 127.0.0.1:3306          0.0.0.0:*               LISTEN\nState      Recv-Q Send-Q Local Address:Port               Peer Address:Port\nLISTEN     0      128          0.0.0.0:22                     0.0.0.0:*"
	case req.Command == "systemctl list-units --type=service --state=running | head -10 && systemctl --failed":
		stdout = "UNIT                     LOAD   ACTIVE SUB     DESCRIPTION\nsshd.service             loaded active running OpenBSD Secure Shell server\nnginx.service            loaded failed failed  A high performance web server\nmysql.service            loaded active running MySQL Community Server\n1 loaded units listed.\n● nginx.service - A high performance web server\n   Loaded: loaded (/lib/systemd/system/nginx.service; enabled; vendor preset: enabled)\n   Active: failed (Result: exit-code) since Wed 2023-10-25 10:30:00 UTC; 5min ago"
	case req.Command == "last -n 5 && who && ps aux | grep -E '(ssh|sshd)' | head -5":
		stdout = "root     pts/0        *************    Wed Oct 25 10:30   still logged in\nroot     console                       Wed Oct 25 09:00 - 10:29  (01:29)\nroot     pts/0        ************* Wed Oct 25 10:30:01 (pts/0)\nroot      1234  0.0  0.1  12345  6789 ?        Ss   09:00   0:00 /usr/sbin/sshd -D"
	case req.Command == "tail -20 /var/log/syslog 2>/dev/null || tail -20 /var/log/messages 2>/dev/null || echo 'No system logs found'":
		stdout = "Oct 25 10:30:01 test-server systemd[1]: Started Session 123 of user root.\nOct 25 10:29:45 test-server sshd[1234]: Accepted publickey for root from ************* port 12345 ssh2\nOct 25 10:25:01 test-server kernel: [12345.678901] Out of memory: Kill process 9999 (java) score 900 or sacrifice child\nOct 25 10:24:30 test-server systemd[1]: nginx.service: Main process exited, code=exited, status=1/FAILURE\nOct 25 10:24:30 test-server systemd[1]: nginx.service: Failed with result 'exit-code'."
	default:
		stdout = "Command executed successfully"
	}

	return &model.CommandExecuteResponse{
		Command:    req.Command,
		ExitCode:   0,
		Stdout:     stdout,
		Stderr:     "",
		Duration:   100,
		ExecutedAt: time.Now(),
	}, nil
}

func (m *MockHostService) GetHostStatus(id int64) (*model.HostResponse, error) {
	return &model.HostResponse{
		ID:     id,
		Status: "online",
	}, nil
}

func (m *MockHostService) UpdateHostStatus(id int64, status string) error {
	return nil
}

// TestScenario 测试场景
type TestScenario struct {
	Name        string
	Description string
	Operation   string
	Params      map[string]interface{}
	ExpectedSuccess bool
}

func main() {
	// 设置日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 自动迁移
	err = db.AutoMigrate(&model.Host{})
	if err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// 创建测试主机
	testHost := &model.Host{
		Name:      "test-server",
		IPAddress: "**************",
		Port:      22,
		Username:  "root",
		Status:    "online",
	}
	db.Create(testHost)

	// 创建模拟主机服务
	mockHostService := &MockHostService{}

	// 创建监控执行器
	monitoringExecutor := service.NewMonitoringExecutor(db, logger, mockHostService)

	fmt.Println("🚀 智能执行引擎架构验证测试")
	fmt.Println("============================================================")

	// 定义测试场景
	testScenarios := []TestScenario{
		{
			Name:        "comprehensive_inspection",
			Description: "全面主机巡检（DeepSeek新支持的操作）",
			Operation:   "comprehensive_inspection",
			Params: map[string]interface{}{
				"target_host": map[string]interface{}{"ip": "**************"},
				"inspection_items": []interface{}{"system_status", "cpu_usage", "memory_usage", "disk_usage", "network_status", "service_status", "security_status", "log_analysis"},
				"inspection_level": "full",
			},
			ExpectedSuccess: true,
		},
		{
			Name:        "system_monitor",
			Description: "系统监控（原有支持的操作）",
			Operation:   "system_monitor",
			Params: map[string]interface{}{
				"target_host": map[string]interface{}{"ip": "**************"},
				"metric": "cpu",
			},
			ExpectedSuccess: true,
		},
		{
			Name:        "log_analysis",
			Description: "日志分析（智能降级处理）",
			Operation:   "log_analysis",
			Params: map[string]interface{}{
				"target_host": map[string]interface{}{"ip": "**************"},
				"log_type": "system",
			},
			ExpectedSuccess: true,
		},
		{
			Name:        "unknown_operation",
			Description: "未知操作（智能降级处理）",
			Operation:   "unknown_monitoring_operation",
			Params: map[string]interface{}{
				"target_host": map[string]interface{}{"ip": "**************"},
			},
			ExpectedSuccess: true,
		},
	}

	// 执行测试场景
	successCount := 0
	for i, scenario := range testScenarios {
		fmt.Printf("\n📋 测试场景 %d: %s\n", i+1, scenario.Name)
		fmt.Printf("📝 描述: %s\n", scenario.Description)
		fmt.Printf("⚙️  操作: %s\n", scenario.Operation)

		// 添加operation参数
		scenario.Params["operation"] = scenario.Operation

		// 创建执行请求
		req := &service.ExecutionRequest{
			UserID: 1,
			Intent: &service.IntentResult{
				Type:       "monitoring_operations",
				Parameters: scenario.Params,
			},
			OriginalMsg: fmt.Sprintf("测试%s操作", scenario.Operation),
			SessionID:   fmt.Sprintf("test_session_%d", i+1),
		}

		// 执行测试
		ctx := context.Background()
		start := time.Now()
		result, err := monitoringExecutor.Execute(ctx, req)
		duration := time.Since(start)

		// 验证结果
		if err != nil {
			fmt.Printf("❌ 执行失败: %v\n", err)
			continue
		}

		fmt.Printf("⏱️  执行时间: %v\n", duration)
		fmt.Printf("✅ 执行成功: %v\n", result.Success)
		fmt.Printf("📋 操作类型: %s\n", result.Action)

		if result.Success == scenario.ExpectedSuccess {
			successCount++
			fmt.Printf("🎯 测试结果: ✅ 通过\n")
		} else {
			fmt.Printf("🎯 测试结果: ❌ 失败\n")
		}

		// 显示部分内容
		if len(result.Content) > 200 {
			fmt.Printf("📄 内容预览: %s...\n", result.Content[:200])
		} else {
			fmt.Printf("📄 完整内容: %s\n", result.Content)
		}
	}

	// 测试总结
	fmt.Println("\n" + "============================================================")
	fmt.Printf("📊 测试总结: %d/%d 通过 (%.1f%%)\n", successCount, len(testScenarios), float64(successCount)/float64(len(testScenarios))*100)
	
	if successCount == len(testScenarios) {
		fmt.Println("🎉 所有测试通过！智能执行引擎架构优化成功！")
		fmt.Println("✨ 系统现已支持:")
		fmt.Println("   - DeepSeek智能决策层直接操作支持")
		fmt.Println("   - 动态操作路由和扩展")
		fmt.Println("   - 智能降级处理机制")
		fmt.Println("   - 全面主机巡检功能")
	} else {
		fmt.Println("⚠️  部分测试未通过，需要进一步优化")
	}
}
