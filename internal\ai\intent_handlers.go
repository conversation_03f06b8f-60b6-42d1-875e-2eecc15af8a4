package ai

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// IntentHandler 意图处理器接口
type IntentHandler interface {
	CanHandle(intentType string) bool
	Handle(ctx context.Context, result *EnhancedIntentResult, sessionID string) (*IntentHandleResult, error)
	GetSupportedIntents() []string
}

// IntentHandleResult 意图处理结果
type IntentHandleResult struct {
	Success     bool                   `json:"success"`
	Message     string                 `json:"message"`
	Data        map[string]interface{} `json:"data"`
	Actions     []ActionItem           `json:"actions"`
	NextSteps   []string               `json:"next_steps"`
	Warnings    []string               `json:"warnings"`
	ExecutionID string                 `json:"execution_id,omitempty"`
}

// ActionItem 操作项
type ActionItem struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Description string                 `json:"description"`
	Status      string                 `json:"status"`
	Parameters  map[string]interface{} `json:"parameters"`
	Result      string                 `json:"result,omitempty"`
	Error       string                 `json:"error,omitempty"`
}

// HostManagementHandler 主机管理处理器
type HostManagementHandler struct {
	hostService interface{} // 简化为interface{}
	logger      *logrus.Logger
}

// NewHostManagementHandler 创建主机管理处理器
func NewHostManagementHandler(hostService interface{}, logger *logrus.Logger) *HostManagementHandler {
	return &HostManagementHandler{
		hostService: hostService,
		logger:      logger,
	}
}

// CanHandle 检查是否能处理该意图
func (h *HostManagementHandler) CanHandle(intentType string) bool {
	supportedIntents := []string{"host_management", "infrastructure_monitoring"}
	for _, intent := range supportedIntents {
		if intent == intentType {
			return true
		}
	}
	return false
}

// GetSupportedIntents 获取支持的意图类型
func (h *HostManagementHandler) GetSupportedIntents() []string {
	return []string{"host_management", "infrastructure_monitoring"}
}

// Handle 处理主机管理意图
func (h *HostManagementHandler) Handle(ctx context.Context, result *EnhancedIntentResult, sessionID string) (*IntentHandleResult, error) {
	h.logger.WithFields(logrus.Fields{
		"intent_type": result.Type,
		"session_id":  sessionID,
		"parameters":  result.Parameters,
	}).Info("Handling host management intent")

	switch result.Type {
	case "host_management":
		return h.handleHostManagement(ctx, result)
	case "infrastructure_monitoring":
		return h.handleInfrastructureMonitoring(ctx, result)
	default:
		return &IntentHandleResult{
			Success: false,
			Message: fmt.Sprintf("不支持的意图类型: %s", result.Type),
		}, nil
	}
}

// handleHostManagement 处理主机管理
func (h *HostManagementHandler) handleHostManagement(ctx context.Context, result *EnhancedIntentResult) (*IntentHandleResult, error) {
	// 检查参数中的操作类型
	operation, exists := result.Parameters["operation"]
	if !exists {
		return &IntentHandleResult{
			Success: false,
			Message: "缺少操作类型参数",
			NextSteps: []string{
				"请指定要执行的操作：添加主机、删除主机、修改主机信息、查看主机列表",
			},
		}, nil
	}

	switch operation {
	case "add_host":
		return h.handleAddHost(ctx, result)
	case "list_hosts":
		return h.handleListHosts(ctx, result)
	case "update_host":
		return h.handleUpdateHost(ctx, result)
	case "delete_host":
		return h.handleDeleteHost(ctx, result)
	default:
		return &IntentHandleResult{
			Success: false,
			Message: fmt.Sprintf("不支持的主机操作: %s", operation),
			NextSteps: []string{
				"支持的操作：add_host, list_hosts, update_host, delete_host",
			},
		}, nil
	}
}

// handleAddHost 处理添加主机
func (h *HostManagementHandler) handleAddHost(ctx context.Context, result *EnhancedIntentResult) (*IntentHandleResult, error) {
	// 提取主机信息
	ipAddress, hasIP := result.Parameters["ip_address"].(string)
	username, hasUsername := result.Parameters["username"].(string)
	password, hasPassword := result.Parameters["password"].(string)

	if !hasIP || !hasUsername || !hasPassword {
		return &IntentHandleResult{
			Success: false,
			Message: "添加主机需要提供完整信息：IP地址、用户名、密码",
			NextSteps: []string{
				"请提供主机IP地址",
				"请提供SSH用户名",
				"请提供SSH密码",
			},
		}, nil
	}

	// 验证IP地址格式
	if !isValidIP(ipAddress) {
		return &IntentHandleResult{
			Success: false,
			Message: fmt.Sprintf("无效的IP地址格式: %s", ipAddress),
			NextSteps: []string{
				"请提供有效的IPv4地址，例如：*************",
			},
		}, nil
	}

	// 如果是高风险操作，需要确认
	if result.RequiresConfirm {
		return &IntentHandleResult{
			Success: false,
			Message: fmt.Sprintf("确认添加主机 %s (用户名: %s)？", ipAddress, username),
			Actions: []ActionItem{
				{
					ID:          "confirm_add_host",
					Type:        "confirmation",
					Description: "确认添加主机",
					Status:      "pending",
					Parameters: map[string]interface{}{
						"ip_address": ipAddress,
						"username":   username,
						"password":   password,
					},
				},
			},
			NextSteps: []string{
				"回复'确认'继续添加主机",
				"回复'取消'放弃操作",
			},
		}, nil
	}

	// 执行添加主机操作
	// TODO: 实际调用hostService添加主机
	return &IntentHandleResult{
		Success: true,
		Message: fmt.Sprintf("✅ 成功添加主机 %s", ipAddress),
		Data: map[string]interface{}{
			"host_ip":   ipAddress,
			"username":  username,
			"operation": "add_host",
		},
		Actions: []ActionItem{
			{
				ID:          "add_host_action",
				Type:        "host_operation",
				Description: "添加主机到系统",
				Status:      "completed",
				Parameters: map[string]interface{}{
					"ip_address": ipAddress,
					"username":   username,
				},
				Result: "主机添加成功",
			},
		},
		NextSteps: []string{
			"可以使用'测试连接'验证主机连通性",
			"可以使用'查看主机列表'查看所有主机",
		},
	}, nil
}

// handleListHosts 处理查看主机列表
func (h *HostManagementHandler) handleListHosts(ctx context.Context, result *EnhancedIntentResult) (*IntentHandleResult, error) {
	// TODO: 实际调用hostService获取主机列表
	// 这里返回模拟数据
	hosts := []map[string]interface{}{
		{
			"id":          1,
			"name":        "web-server-01",
			"ip_address":  "*************",
			"status":      "online",
			"environment": "production",
		},
		{
			"id":          2,
			"name":        "db-server-01",
			"ip_address":  "*************",
			"status":      "offline",
			"environment": "production",
		},
	}

	return &IntentHandleResult{
		Success: true,
		Message: fmt.Sprintf("📋 当前系统中共有 %d 台主机", len(hosts)),
		Data: map[string]interface{}{
			"hosts": hosts,
			"total": len(hosts),
		},
		NextSteps: []string{
			"可以选择特定主机进行操作",
			"可以使用'检查主机状态'查看详细信息",
		},
	}, nil
}

// handleUpdateHost 处理更新主机
func (h *HostManagementHandler) handleUpdateHost(ctx context.Context, result *EnhancedIntentResult) (*IntentHandleResult, error) {
	return &IntentHandleResult{
		Success: false,
		Message: "主机更新功能正在开发中",
		NextSteps: []string{
			"请稍后再试",
			"或联系管理员获取帮助",
		},
	}, nil
}

// handleDeleteHost 处理删除主机
func (h *HostManagementHandler) handleDeleteHost(ctx context.Context, result *EnhancedIntentResult) (*IntentHandleResult, error) {
	return &IntentHandleResult{
		Success: false,
		Message: "主机删除功能正在开发中",
		NextSteps: []string{
			"请稍后再试",
			"或联系管理员获取帮助",
		},
	}, nil
}

// handleInfrastructureMonitoring 处理基础设施监控
func (h *HostManagementHandler) handleInfrastructureMonitoring(ctx context.Context, result *EnhancedIntentResult) (*IntentHandleResult, error) {
	return &IntentHandleResult{
		Success: true,
		Message: "🔍 基础设施监控功能",
		Data: map[string]interface{}{
			"monitoring_type": "infrastructure",
			"timestamp":       time.Now(),
		},
		NextSteps: []string{
			"选择要监控的指标：CPU、内存、磁盘、网络",
			"选择要监控的主机或主机组",
		},
	}, nil
}

// isValidIP 验证IP地址格式
func isValidIP(ip string) bool {
	parts := strings.Split(ip, ".")
	if len(parts) != 4 {
		return false
	}

	for _, part := range parts {
		if len(part) == 0 || len(part) > 3 {
			return false
		}

		num := 0
		for _, char := range part {
			if char < '0' || char > '9' {
				return false
			}
			num = num*10 + int(char-'0')
		}

		if num > 255 {
			return false
		}
	}

	return true
}
