# AI运维管理平台 - 环境变量设置脚本
# Environment Variables Setup Script

Write-Host "AI运维管理平台环境变量设置" -ForegroundColor Cyan
Write-Host "================================" -ForegroundColor Cyan

# 设置基础环境变量
$env:AIOPS_APP_PORT = "8082"
$env:AIOPS_APP_ENV = "development"
$env:AIOPS_APP_DEBUG = "true"

# JWT密钥（开发环境使用固定值，生产环境请使用随机生成的密钥）
$env:AIOPS_JWT_SECRET = "aiops-development-jwt-secret-key-2024"

# 加密密钥（开发环境使用固定值）
$env:AIOPS_ENCRYPTION_KEY = "aiops-dev-encryption-key-32bytes"

# DeepSeek API密钥（请替换为您的真实API密钥）
if (-not $env:AIOPS_DEEPSEEK_API_KEY) {
    Write-Host "警告: 未设置DeepSeek API密钥" -ForegroundColor Yellow
    Write-Host "请设置环境变量: AIOPS_DEEPSEEK_API_KEY = 'your-api-key'" -ForegroundColor Yellow
    Write-Host "或者运行: Set-Item -Path Env:AIOPS_DEEPSEEK_API_KEY -Value 'your-api-key'" -ForegroundColor Yellow

    # 设置一个占位符，避免启动错误
    $env:AIOPS_DEEPSEEK_API_KEY = "sk-placeholder-please-set-real-api-key"
}

Write-Host ""
Write-Host "环境变量设置完成:" -ForegroundColor Green
Write-Host "  AIOPS_APP_PORT: $env:AIOPS_APP_PORT" -ForegroundColor White
Write-Host "  AIOPS_APP_ENV: $env:AIOPS_APP_ENV" -ForegroundColor White
Write-Host "  AIOPS_APP_DEBUG: $env:AIOPS_APP_DEBUG" -ForegroundColor White
Write-Host "  AIOPS_JWT_SECRET: [已设置]" -ForegroundColor White
Write-Host "  AIOPS_ENCRYPTION_KEY: [已设置]" -ForegroundColor White
Write-Host "  AIOPS_DEEPSEEK_API_KEY: [已设置]" -ForegroundColor White

Write-Host ""
Write-Host "现在可以启动服务器了!" -ForegroundColor Green
Write-Host "运行: go run cmd/server/main.go" -ForegroundColor Cyan
