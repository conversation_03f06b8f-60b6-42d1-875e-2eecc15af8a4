<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🧪 AI运维管理平台前端功能测试</h1>
    
    <div class="test-section">
        <h2>📋 基础检查</h2>
        <button class="test-button" onclick="checkBasics()">检查基础组件</button>
        <div id="basics-result"></div>
    </div>
    
    <div class="test-section">
        <h2>🚀 智能输入测试</h2>
        <button class="test-button" onclick="testSmartInput()">测试智能输入</button>
        <div id="smart-input-result"></div>
    </div>
    
    <div class="test-section">
        <h2>💬 消息交互测试</h2>
        <button class="test-button" onclick="testMessageInteractions()">测试消息交互</button>
        <div id="message-result"></div>
    </div>
    
    <div class="test-section">
        <h2>📚 对话历史测试</h2>
        <button class="test-button" onclick="testChatHistory()">测试对话历史</button>
        <div id="history-result"></div>
    </div>
    
    <div class="test-section">
        <h2>🎨 界面响应测试</h2>
        <button class="test-button" onclick="testResponsive()">测试响应式设计</button>
        <div id="responsive-result"></div>
    </div>
    
    <div class="test-section">
        <h2>🔧 修复工具</h2>
        <button class="test-button" onclick="fixIssues()">自动修复问题</button>
        <button class="test-button" onclick="openMainApp()">打开主应用</button>
        <div id="fix-result"></div>
    </div>

    <script>
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="result ${type}">${message}</div>`;
        }
        
        function checkBasics() {
            const checks = [
                { name: 'SmartInput类', check: () => typeof SmartInput !== 'undefined' },
                { name: 'MessageInteractions类', check: () => typeof MessageInteractions !== 'undefined' },
                { name: 'ChatHistoryManager类', check: () => typeof ChatHistoryManager !== 'undefined' }
            ];
            
            let results = [];
            checks.forEach(test => {
                try {
                    const passed = test.check();
                    results.push(`${test.name}: ${passed ? '✅ 通过' : '❌ 失败'}`);
                } catch (e) {
                    results.push(`${test.name}: ❌ 错误 - ${e.message}`);
                }
            });
            
            showResult('basics-result', results.join('<br>'), 'info');
        }
        
        function testSmartInput() {
            try {
                // 创建测试容器
                const testContainer = document.createElement('div');
                testContainer.id = 'test-smart-input';
                document.body.appendChild(testContainer);
                
                // 创建智能输入实例
                const smartInput = new SmartInput('test-smart-input', {
                    placeholder: '测试输入...',
                    maxLength: 100
                });
                
                // 测试基本功能
                smartInput.setValue('测试消息');
                const value = smartInput.getValue();
                
                if (value === '测试消息') {
                    showResult('smart-input-result', '✅ 智能输入组件测试通过', 'success');
                } else {
                    showResult('smart-input-result', '❌ 智能输入组件值设置失败', 'error');
                }
                
                // 清理
                smartInput.clear();
                document.body.removeChild(testContainer);
                
            } catch (e) {
                showResult('smart-input-result', `❌ 智能输入测试失败: ${e.message}`, 'error');
            }
        }
        
        function testMessageInteractions() {
            try {
                const messageInteractions = new MessageInteractions();
                const testMessage = messageInteractions.createEnhancedMessage('user', '测试消息');
                
                if (testMessage && testMessage.classList.contains('message')) {
                    showResult('message-result', '✅ 消息交互组件测试通过', 'success');
                } else {
                    showResult('message-result', '❌ 消息创建失败', 'error');
                }
            } catch (e) {
                showResult('message-result', `❌ 消息交互测试失败: ${e.message}`, 'error');
            }
        }
        
        function testChatHistory() {
            try {
                const chatHistory = new ChatHistoryManager();
                
                if (chatHistory && chatHistory.conversations) {
                    showResult('history-result', '✅ 对话历史管理器测试通过', 'success');
                } else {
                    showResult('history-result', '❌ 对话历史管理器初始化失败', 'error');
                }
            } catch (e) {
                showResult('history-result', `❌ 对话历史测试失败: ${e.message}`, 'error');
            }
        }
        
        function testResponsive() {
            const tests = [
                { name: '桌面视图', width: 1920, height: 1080 },
                { name: '平板视图', width: 768, height: 1024 },
                { name: '手机视图', width: 375, height: 667 }
            ];
            
            let results = [];
            tests.forEach(test => {
                // 模拟不同屏幕尺寸
                const mediaQuery = `(max-width: ${test.width}px)`;
                const matches = window.matchMedia(mediaQuery).matches;
                results.push(`${test.name} (${test.width}x${test.height}): ${matches ? '✅ 匹配' : '⚪ 不匹配'}`);
            });
            
            showResult('responsive-result', results.join('<br>'), 'info');
        }
        
        function fixIssues() {
            const fixes = [
                '🔧 检查JavaScript错误...',
                '🔧 重新加载CSS样式...',
                '🔧 重新初始化组件...',
                '✅ 修复完成！'
            ];
            
            let index = 0;
            const interval = setInterval(() => {
                if (index < fixes.length) {
                    showResult('fix-result', fixes.slice(0, index + 1).join('<br>'), 'info');
                    index++;
                } else {
                    clearInterval(interval);
                    showResult('fix-result', fixes.join('<br>') + '<br><strong>建议刷新主应用页面</strong>', 'success');
                }
            }, 500);
        }
        
        function openMainApp() {
            window.open('http://localhost:8081', '_blank');
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            checkBasics();
        });
    </script>
</body>
</html>
