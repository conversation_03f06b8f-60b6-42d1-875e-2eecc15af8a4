package interfaces

import (
	"context"
	"time"
)

// AIServiceInterface AI服务接口 - 避免循环依赖
type AIServiceInterface interface {
	ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error)
	ExtractIntent(ctx context.Context, message string, context *ConversationContext) (*IntentResult, error)
}

// ProcessMessageRequest 处理消息请求
type ProcessMessageRequest struct {
	SessionID string `json:"session_id"`
	UserID    int64  `json:"user_id"`
	Message   string `json:"message"`
}

// ProcessMessageResponse 处理消息响应
type ProcessMessageResponse struct {
	Content        string        `json:"content"`
	Intent         string        `json:"intent"`
	Confidence     float64       `json:"confidence"`
	Parameters     map[string]interface{} `json:"parameters"`
	TokenCount     int           `json:"token_count"`
	ProcessingTime time.Duration `json:"processing_time"`
	Timestamp      time.Time     `json:"timestamp"`
}

// ConversationContext 对话上下文
type ConversationContext struct {
	SessionID string                 `json:"session_id"`
	Variables map[string]interface{} `json:"variables"`
}

// IntentResult 意图识别结果
type IntentResult struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Command    string                 `json:"command"`
}

// Message 消息结构
type Message struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// DeepSeekServiceInterface DeepSeek服务接口
type DeepSeekServiceInterface interface {
	Chat(ctx context.Context, messages []Message) (string, error)
}

// HostServiceInterface 主机服务接口
type HostServiceInterface interface {
	ListHosts(ctx context.Context) ([]Host, error)
	GetHost(ctx context.Context, id int64) (*Host, error)
	CreateHost(ctx context.Context, host *Host) error
	UpdateHost(ctx context.Context, host *Host) error
	DeleteHost(ctx context.Context, id int64) error
}

// Host 主机结构
type Host struct {
	ID        int64     `json:"id"`
	Name      string    `json:"name"`
	IPAddress string    `json:"ip_address"`
	Username  string    `json:"username"`
	Password  string    `json:"password"`
	Port      int       `json:"port"`
	Status    string    `json:"status"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}
