/* ========================================
   消息交互增强样式
   类似ChatGPT的消息交互功能
   ======================================== */

/* 消息容器 */
.message {
  display: flex;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
  padding: var(--space-4);
  border-radius: var(--radius-lg);
  transition: all var(--duration-fast) var(--easing-ease);
  position: relative;
  max-width: 100%;
}

.message:hover {
  background-color: var(--bg-hover);
}

.message.user {
  flex-direction: row-reverse;
  background-color: var(--bg-secondary);
  margin-left: var(--space-8);
}

.message.assistant {
  background-color: var(--bg-primary);
  margin-right: var(--space-8);
}

/* 消息头像 */
.message-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  flex-shrink: 0;
}

.message.user .message-avatar {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  color: white;
}

.message.assistant .message-avatar {
  background: linear-gradient(135deg, #10b981, #059669);
  color: white;
}

/* 消息内容区域 */
.message-content-area {
  flex: 1;
  min-width: 0;
}

.message-content {
  color: var(--text-primary);
  line-height: 1.6;
  word-wrap: break-word;
  margin-bottom: var(--space-2);
}

.message-content p {
  margin: 0 0 var(--space-3) 0;
}

.message-content p:last-child {
  margin-bottom: 0;
}

.message-content code {
  background-color: var(--bg-tertiary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-sm);
  font-family: var(--font-family-mono);
  font-size: 0.9em;
}

.message-content pre {
  background-color: var(--bg-tertiary);
  padding: var(--space-3);
  border-radius: var(--radius-md);
  overflow-x: auto;
  margin: var(--space-3) 0;
}

.message-content pre code {
  background: none;
  padding: 0;
}

/* 消息元数据 */
.message-meta {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-2);
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.message-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.message-status {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.status-icon {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.status-icon.sending {
  background-color: var(--color-warning);
  animation: pulse 2s infinite;
}

.status-icon.sent {
  background-color: var(--color-success);
}

.status-icon.error {
  background-color: var(--color-error);
}

/* 消息操作按钮 */
.message-actions {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  opacity: 0;
  transform: translateY(4px);
  transition: all var(--duration-fast) var(--easing-ease);
  margin-top: var(--space-2);
}

.message:hover .message-actions {
  opacity: 1;
  transform: translateY(0);
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  font-size: var(--font-size-sm);
}

.action-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-secondary);
}

.action-btn.active {
  background-color: var(--color-primary);
  color: white;
}

.action-btn.like.active {
  background-color: var(--color-success);
}

.action-btn.dislike.active {
  background-color: var(--color-error);
}

/* 复制成功提示 */
.copy-success {
  position: absolute;
  top: -30px;
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--text-primary);
  color: var(--bg-primary);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
  white-space: nowrap;
  z-index: 100;
  opacity: 0;
  animation: copyFeedback 2s ease-out;
}

@keyframes copyFeedback {
  0% {
    opacity: 0;
    transform: translateX(-50%) translateY(10px);
  }
  20%, 80% {
    opacity: 1;
    transform: translateX(-50%) translateY(0);
  }
  100% {
    opacity: 0;
    transform: translateX(-50%) translateY(-10px);
  }
}

/* 引用回复 */
.message-quote {
  background-color: var(--bg-tertiary);
  border-left: 3px solid var(--color-primary);
  padding: var(--space-2) var(--space-3);
  margin-bottom: var(--space-3);
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.quote-author {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  margin-bottom: var(--space-1);
}

.quote-content {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  font-style: italic;
}

/* 消息反应 */
.message-reactions {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  margin-top: var(--space-2);
  flex-wrap: wrap;
}

.reaction-item {
  display: flex;
  align-items: center;
  gap: var(--space-1);
  padding: var(--space-1) var(--space-2);
  background-color: var(--bg-tertiary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.reaction-item:hover {
  background-color: var(--bg-hover);
  border-color: var(--color-primary);
}

.reaction-item.active {
  background-color: var(--color-primary-light);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.reaction-emoji {
  font-size: 14px;
}

.reaction-count {
  font-weight: var(--font-weight-medium);
}

/* 重新生成指示器 */
.regenerating-indicator {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-3);
  background-color: var(--bg-secondary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-md);
  margin-top: var(--space-2);
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.regenerating-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-primary);
  border-top: 2px solid var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 消息版本切换 */
.message-versions {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  margin-top: var(--space-2);
  padding: var(--space-2);
  background-color: var(--bg-secondary);
  border-radius: var(--radius-md);
  font-size: var(--font-size-xs);
}

.version-info {
  color: var(--text-tertiary);
}

.version-nav {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.version-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  border-radius: var(--radius-sm);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.version-btn:hover:not(:disabled) {
  background-color: var(--bg-hover);
  color: var(--text-secondary);
}

.version-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .message {
    margin-left: 0;
    margin-right: 0;
    padding: var(--space-3);
  }
  
  .message.user {
    margin-left: var(--space-4);
  }
  
  .message.assistant {
    margin-right: var(--space-4);
  }
  
  .message-avatar {
    width: 32px;
    height: 32px;
    font-size: var(--font-size-xs);
  }
  
  .action-btn {
    width: 24px;
    height: 24px;
    font-size: var(--font-size-xs);
  }
}

@media (max-width: 480px) {
  .message {
    padding: var(--space-2);
  }
  
  .message.user,
  .message.assistant {
    margin-left: 0;
    margin-right: 0;
  }
  
  .message-actions {
    flex-wrap: wrap;
  }
  
  .message-reactions {
    justify-content: flex-start;
  }
}
