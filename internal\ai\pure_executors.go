package ai

import (
	"context"
	"fmt"
	"os/exec"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 🔧 纯执行器组件 - 只负责执行，不包含任何业务逻辑

// PureSQLExecutor 纯SQL执行器
type PureSQLExecutor struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// PureShellExecutor 纯Shell执行器
type PureShellExecutor struct {
	logger *logrus.Logger
}

// PureConfigExecutor 纯配置执行器
type PureConfigExecutor struct {
	logger *logrus.Logger
}

// SQLExecutionResult SQL执行结果
type SQLExecutionResult struct {
	Rows         []map[string]interface{} `json:"rows"`
	RowCount     int                      `json:"row_count"`
	AffectedRows int64                    `json:"affected_rows"`
	Columns      []string                 `json:"columns"`
	ExecutionTime time.Duration           `json:"execution_time"`
	Query        string                   `json:"query"`
}

// ShellExecutionResult Shell执行结果
type ShellExecutionResult struct {
	Output       string        `json:"output"`
	Error        string        `json:"error"`
	ExitCode     int           `json:"exit_code"`
	Command      string        `json:"command"`
	ExecutionTime time.Duration `json:"execution_time"`
}

// ConfigExecutionResult 配置执行结果
type ConfigExecutionResult struct {
	Success      bool          `json:"success"`
	Message      string        `json:"message"`
	ConfigData   interface{}   `json:"config_data"`
	ExecutionTime time.Duration `json:"execution_time"`
}

// NewPureSQLExecutor 创建纯SQL执行器
func NewPureSQLExecutor(db *gorm.DB, logger *logrus.Logger) *PureSQLExecutor {
	return &PureSQLExecutor{
		db:     db,
		logger: logger,
	}
}

// Execute 执行SQL语句
func (pse *PureSQLExecutor) Execute(ctx context.Context, sqlQuery string) (*SQLExecutionResult, error) {
	start := time.Now()

	pse.logger.WithField("sql", sqlQuery).Info("PureSQLExecutor: 执行SQL")

	// 清理SQL语句
	sqlQuery = strings.TrimSpace(sqlQuery)
	if sqlQuery == "" {
		return nil, fmt.Errorf("SQL语句不能为空")
	}

	// 判断SQL类型
	sqlLower := strings.ToLower(sqlQuery)
	isSelect := strings.HasPrefix(sqlLower, "select")

	result := &SQLExecutionResult{
		Query:         sqlQuery,
		ExecutionTime: 0,
	}

	if isSelect {
		// 执行SELECT查询
		var rows []map[string]interface{}
		if err := pse.db.Raw(sqlQuery).Scan(&rows).Error; err != nil {
			return nil, fmt.Errorf("SQL执行失败: %w", err)
		}

		result.Rows = rows
		result.RowCount = len(rows)

		// 提取列名
		if len(rows) > 0 {
			for col := range rows[0] {
				result.Columns = append(result.Columns, col)
			}
		}
	} else {
		// 执行非SELECT语句（INSERT, UPDATE, DELETE等）
		tx := pse.db.Exec(sqlQuery)
		if tx.Error != nil {
			return nil, fmt.Errorf("SQL执行失败: %w", tx.Error)
		}
		result.AffectedRows = tx.RowsAffected
	}

	result.ExecutionTime = time.Since(start)

	pse.logger.WithFields(logrus.Fields{
		"row_count":      result.RowCount,
		"affected_rows":  result.AffectedRows,
		"execution_time": result.ExecutionTime,
	}).Info("PureSQLExecutor: SQL执行完成")

	return result, nil
}

// NewPureShellExecutor 创建纯Shell执行器
func NewPureShellExecutor(logger *logrus.Logger) *PureShellExecutor {
	return &PureShellExecutor{
		logger: logger,
	}
}

// Execute 执行Shell命令
func (pse *PureShellExecutor) Execute(ctx context.Context, command string) (*ShellExecutionResult, error) {
	start := time.Now()

	pse.logger.WithField("command", command).Info("PureShellExecutor: 执行Shell命令")

	// 清理命令
	command = strings.TrimSpace(command)
	if command == "" {
		return nil, fmt.Errorf("Shell命令不能为空")
	}

	// 安全检查 - 基本的危险命令过滤
	if pse.isDangerousCommand(command) {
		return nil, fmt.Errorf("拒绝执行危险命令: %s", command)
	}

	// 执行命令
	cmd := exec.CommandContext(ctx, "sh", "-c", command)
	output, err := cmd.CombinedOutput()

	result := &ShellExecutionResult{
		Command:       command,
		Output:        string(output),
		ExecutionTime: time.Since(start),
	}

	if err != nil {
		result.Error = err.Error()
		if exitError, ok := err.(*exec.ExitError); ok {
			result.ExitCode = exitError.ExitCode()
		} else {
			result.ExitCode = -1
		}
	} else {
		result.ExitCode = 0
	}

	pse.logger.WithFields(logrus.Fields{
		"exit_code":      result.ExitCode,
		"execution_time": result.ExecutionTime,
		"output_length":  len(result.Output),
	}).Info("PureShellExecutor: Shell命令执行完成")

	return result, nil
}

// isDangerousCommand 检查是否为危险命令
func (pse *PureShellExecutor) isDangerousCommand(command string) bool {
	dangerousPatterns := []string{
		"rm -rf",
		"dd if=",
		"mkfs",
		"fdisk",
		"format",
		"shutdown",
		"reboot",
		"halt",
		"init 0",
		"init 6",
		"> /dev/",
		"chmod 777",
		"chown -R",
	}

	commandLower := strings.ToLower(command)
	for _, pattern := range dangerousPatterns {
		if strings.Contains(commandLower, pattern) {
			return true
		}
	}
	return false
}

// NewPureConfigExecutor 创建纯配置执行器
func NewPureConfigExecutor(logger *logrus.Logger) *PureConfigExecutor {
	return &PureConfigExecutor{
		logger: logger,
	}
}

// Execute 执行配置操作
func (pce *PureConfigExecutor) Execute(ctx context.Context, configContent string) (*ConfigExecutionResult, error) {
	start := time.Now()

	pce.logger.WithField("config_length", len(configContent)).Info("PureConfigExecutor: 处理配置")

	// 这里只是示例实现，实际应该根据配置类型进行处理
	result := &ConfigExecutionResult{
		Success:       true,
		Message:       "配置处理完成",
		ConfigData:    configContent,
		ExecutionTime: time.Since(start),
	}

	pce.logger.WithFields(logrus.Fields{
		"success":        result.Success,
		"execution_time": result.ExecutionTime,
	}).Info("PureConfigExecutor: 配置处理完成")

	return result, nil
}

// ExecutionMetrics 执行指标
type ExecutionMetrics struct {
	TotalExecutions   int64         `json:"total_executions"`
	SuccessfulExecutions int64      `json:"successful_executions"`
	FailedExecutions  int64         `json:"failed_executions"`
	AverageExecutionTime time.Duration `json:"average_execution_time"`
	LastExecutionTime time.Time     `json:"last_execution_time"`
}

// GetMetrics 获取执行指标
func (pse *PureSQLExecutor) GetMetrics() *ExecutionMetrics {
	// 这里应该实现真实的指标收集
	return &ExecutionMetrics{
		TotalExecutions:      0,
		SuccessfulExecutions: 0,
		FailedExecutions:     0,
		AverageExecutionTime: 0,
		LastExecutionTime:    time.Now(),
	}
}

// GetMetrics 获取执行指标
func (pse *PureShellExecutor) GetMetrics() *ExecutionMetrics {
	// 这里应该实现真实的指标收集
	return &ExecutionMetrics{
		TotalExecutions:      0,
		SuccessfulExecutions: 0,
		FailedExecutions:     0,
		AverageExecutionTime: 0,
		LastExecutionTime:    time.Now(),
	}
}

// GetMetrics 获取执行指标
func (pce *PureConfigExecutor) GetMetrics() *ExecutionMetrics {
	// 这里应该实现真实的指标收集
	return &ExecutionMetrics{
		TotalExecutions:      0,
		SuccessfulExecutions: 0,
		FailedExecutions:     0,
		AverageExecutionTime: 0,
		LastExecutionTime:    time.Now(),
	}
}
