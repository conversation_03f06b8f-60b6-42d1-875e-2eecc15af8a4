package ai

import (
	"encoding/json"
	"time"
)

// PredictiveInsightRecord 预测洞察数据库记录
type PredictiveInsightRecord struct {
	ID              int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	InsightID       string    `json:"insight_id" gorm:"uniqueIndex;size:100;not null"`
	Type            string    `json:"type" gorm:"size:50;not null"`
	Title           string    `json:"title" gorm:"size:200;not null"`
	Description     string    `json:"description" gorm:"type:text"`
	Severity        string    `json:"severity" gorm:"size:20;not null"`
	Confidence      float64   `json:"confidence"`
	PredictedTime   time.Time `json:"predicted_time"`
	AffectedSystems string    `json:"affected_systems" gorm:"type:text"`
	Indicators      string    `json:"indicators" gorm:"type:text"`
	Recommendations string    `json:"recommendations" gorm:"type:text"`
	Metadata        string    `json:"metadata" gorm:"type:text"`
	Status          string    `json:"status" gorm:"size:20;default:'active'"`
	CreatedAt       time.Time `json:"created_at"`
	UpdatedAt       time.Time `json:"updated_at"`
}

// TableName 指定表名
func (PredictiveInsightRecord) TableName() string {
	return "predictive_insights"
}

// ToInsight 转换为洞察对象
func (record *PredictiveInsightRecord) ToInsight() (*PredictiveInsight, error) {
	// 解析影响的系统
	var affectedSystems []string
	if record.AffectedSystems != "" {
		if err := json.Unmarshal([]byte(record.AffectedSystems), &affectedSystems); err != nil {
			affectedSystems = []string{}
		}
	}

	// 解析指标
	var indicators []PredictiveIndicator
	if record.Indicators != "" {
		if err := json.Unmarshal([]byte(record.Indicators), &indicators); err != nil {
			indicators = []PredictiveIndicator{}
		}
	}

	// 解析建议
	var recommendations []OperationRecommendation
	if record.Recommendations != "" {
		if err := json.Unmarshal([]byte(record.Recommendations), &recommendations); err != nil {
			recommendations = []OperationRecommendation{}
		}
	}

	// 解析元数据
	var metadata map[string]interface{}
	if record.Metadata != "" {
		if err := json.Unmarshal([]byte(record.Metadata), &metadata); err != nil {
			metadata = make(map[string]interface{})
		}
	} else {
		metadata = make(map[string]interface{})
	}

	return &PredictiveInsight{
		ID:              record.InsightID,
		Type:            InsightType(record.Type),
		Title:           record.Title,
		Description:     record.Description,
		Severity:        SeverityLevel(record.Severity),
		Confidence:      record.Confidence,
		PredictedTime:   record.PredictedTime,
		AffectedSystems: affectedSystems,
		Indicators:      indicators,
		Recommendations: recommendations,
		Metadata:        metadata,
		CreatedAt:       record.CreatedAt,
		Status:          InsightStatus(record.Status),
	}, nil
}

// FromInsight 从洞察对象创建记录
func (record *PredictiveInsightRecord) FromInsight(insight *PredictiveInsight) error {
	record.InsightID = insight.ID
	record.Type = string(insight.Type)
	record.Title = insight.Title
	record.Description = insight.Description
	record.Severity = string(insight.Severity)
	record.Confidence = insight.Confidence
	record.PredictedTime = insight.PredictedTime
	record.Status = string(insight.Status)
	record.CreatedAt = insight.CreatedAt
	record.UpdatedAt = time.Now()

	// 序列化影响的系统
	if affectedSystemsData, err := json.Marshal(insight.AffectedSystems); err == nil {
		record.AffectedSystems = string(affectedSystemsData)
	}

	// 序列化指标
	if indicatorsData, err := json.Marshal(insight.Indicators); err == nil {
		record.Indicators = string(indicatorsData)
	}

	// 序列化建议
	if recommendationsData, err := json.Marshal(insight.Recommendations); err == nil {
		record.Recommendations = string(recommendationsData)
	}

	// 序列化元数据
	if metadataData, err := json.Marshal(insight.Metadata); err == nil {
		record.Metadata = string(metadataData)
	}

	return nil
}

// PredictiveAnalysisRecord 预测分析记录
type PredictiveAnalysisRecord struct {
	ID                  int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	AnalysisID          string    `json:"analysis_id" gorm:"uniqueIndex;size:100;not null"`
	TotalInsights       int       `json:"total_insights"`
	CriticalInsights    int       `json:"critical_insights"`
	HighPriorityActions int       `json:"high_priority_actions"`
	PredictionAccuracy  float64   `json:"prediction_accuracy"`
	SystemHealth        string    `json:"system_health" gorm:"size:20"`
	RiskLevel           string    `json:"risk_level" gorm:"size:20"`
	ModelUsed           string    `json:"model_used" gorm:"size:100"`
	ProcessingTime      float64   `json:"processing_time"` // 秒
	DataQuality         string    `json:"data_quality" gorm:"size:20"`
	Metadata            string    `json:"metadata" gorm:"type:text"`
	CreatedAt           time.Time `json:"created_at"`
}

// TableName 指定表名
func (PredictiveAnalysisRecord) TableName() string {
	return "predictive_analyses"
}

// MLModelRecord 机器学习模型记录
type MLModelRecord struct {
	ID           int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	Name         string    `json:"name" gorm:"uniqueIndex;size:100;not null"`
	Type         string    `json:"type" gorm:"size:50;not null"`
	Version      string    `json:"version" gorm:"size:20"`
	Accuracy     float64   `json:"accuracy"`
	LastTrained  time.Time `json:"last_trained"`
	TrainingData int       `json:"training_data"`
	Parameters   string    `json:"parameters" gorm:"type:text"`
	Features     string    `json:"features" gorm:"type:text"`
	IsActive     bool      `json:"is_active" gorm:"default:true"`
	Description  string    `json:"description" gorm:"type:text"`
	Metadata     string    `json:"metadata" gorm:"type:text"`
	CreatedAt    time.Time `json:"created_at"`
	UpdatedAt    time.Time `json:"updated_at"`
}

// TableName 指定表名
func (MLModelRecord) TableName() string {
	return "ml_models"
}

// ToMLModel 转换为ML模型对象
func (record *MLModelRecord) ToMLModel() (*MLModel, error) {
	// 解析参数
	var parameters map[string]interface{}
	if record.Parameters != "" {
		if err := json.Unmarshal([]byte(record.Parameters), &parameters); err != nil {
			parameters = make(map[string]interface{})
		}
	} else {
		parameters = make(map[string]interface{})
	}

	// 解析特征
	var features []string
	if record.Features != "" {
		if err := json.Unmarshal([]byte(record.Features), &features); err != nil {
			features = []string{}
		}
	}

	return &MLModel{
		Name:         record.Name,
		Type:         ModelType(record.Type),
		Version:      record.Version,
		Accuracy:     record.Accuracy,
		LastTrained:  record.LastTrained,
		TrainingData: record.TrainingData,
		Parameters:   parameters,
		Features:     features,
		IsActive:     record.IsActive,
	}, nil
}

// FromMLModel 从ML模型对象创建记录
func (record *MLModelRecord) FromMLModel(model *MLModel) error {
	record.Name = model.Name
	record.Type = string(model.Type)
	record.Version = model.Version
	record.Accuracy = model.Accuracy
	record.LastTrained = model.LastTrained
	record.TrainingData = model.TrainingData
	record.IsActive = model.IsActive
	record.UpdatedAt = time.Now()

	// 序列化参数
	if parametersData, err := json.Marshal(model.Parameters); err == nil {
		record.Parameters = string(parametersData)
	}

	// 序列化特征
	if featuresData, err := json.Marshal(model.Features); err == nil {
		record.Features = string(featuresData)
	}

	return nil
}

// OperationsDataRecord 运维数据记录
type OperationsDataRecord struct {
	ID                   int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	Timestamp            time.Time `json:"timestamp" gorm:"index"`
	TotalHosts           int       `json:"total_hosts"`
	ActiveHosts          int       `json:"active_hosts"`
	FailedHosts          int       `json:"failed_hosts"`
	AverageUptime        float64   `json:"average_uptime"`
	SystemLoad           float64   `json:"system_load"`
	MemoryUtilization    float64   `json:"memory_utilization"`
	DiskUtilization      float64   `json:"disk_utilization"`
	NetworkTraffic       float64   `json:"network_traffic"`
	RequestsPerSecond    float64   `json:"requests_per_second"`
	AverageResponseTime  float64   `json:"average_response_time"`
	ErrorRate            float64   `json:"error_rate"`
	TotalErrors          int       `json:"total_errors"`
	CriticalErrors       int       `json:"critical_errors"`
	ActiveUsers          int       `json:"active_users"`
	SessionsPerHour      float64   `json:"sessions_per_hour"`
	UserSatisfaction     float64   `json:"user_satisfaction"`
	AverageLatency       float64   `json:"average_latency"`
	P95Latency           float64   `json:"p95_latency"`
	CacheHitRate         float64   `json:"cache_hit_rate"`
	DatabaseConnections  int       `json:"database_connections"`
	QueueDepth           int       `json:"queue_depth"`
	FailedLogins         int       `json:"failed_logins"`
	SecurityAlerts       int       `json:"security_alerts"`
	VulnerabilityCount   int       `json:"vulnerability_count"`
	CurrentResourceUsage float64   `json:"current_resource_usage"`
	DataQuality          string    `json:"data_quality" gorm:"size:20"`
	Metadata             string    `json:"metadata" gorm:"type:text"`
	CreatedAt            time.Time `json:"created_at"`
}

// TableName 指定表名
func (OperationsDataRecord) TableName() string {
	return "operations_data"
}

// AlertRecord 告警记录（数据库版本）
type AlertRecordDB struct {
	ID          int64      `json:"id" gorm:"primaryKey;autoIncrement"`
	AlertID     string     `json:"alert_id" gorm:"uniqueIndex;size:100;not null"`
	RuleName    string     `json:"rule_name" gorm:"size:100;not null"`
	InsightID   string     `json:"insight_id" gorm:"size:100;index"`
	Severity    string     `json:"severity" gorm:"size:20;not null"`
	Message     string     `json:"message" gorm:"type:text"`
	Channels    string     `json:"channels" gorm:"type:text"`
	Status      string     `json:"status" gorm:"size:20;not null"`
	CreatedAt   time.Time  `json:"created_at"`
	SentAt      *time.Time `json:"sent_at"`
	Metadata    string     `json:"metadata" gorm:"type:text"`
}

// TableName 指定表名
func (AlertRecordDB) TableName() string {
	return "alert_records"
}

// ToAlertRecord 转换为告警记录对象
func (record *AlertRecordDB) ToAlertRecord() (*AlertRecord, error) {
	// 解析通道
	var channels []string
	if record.Channels != "" {
		if err := json.Unmarshal([]byte(record.Channels), &channels); err != nil {
			channels = []string{}
		}
	}

	// 解析元数据
	var metadata map[string]interface{}
	if record.Metadata != "" {
		if err := json.Unmarshal([]byte(record.Metadata), &metadata); err != nil {
			metadata = make(map[string]interface{})
		}
	} else {
		metadata = make(map[string]interface{})
	}

	return &AlertRecord{
		ID:        record.AlertID,
		RuleName:  record.RuleName,
		InsightID: record.InsightID,
		Severity:  SeverityLevel(record.Severity),
		Message:   record.Message,
		Channels:  channels,
		Status:    AlertStatus(record.Status),
		CreatedAt: record.CreatedAt,
		SentAt:    record.SentAt,
		Metadata:  metadata,
	}, nil
}

// FromAlertRecord 从告警记录对象创建数据库记录
func (record *AlertRecordDB) FromAlertRecord(alert *AlertRecord) error {
	record.AlertID = alert.ID
	record.RuleName = alert.RuleName
	record.InsightID = alert.InsightID
	record.Severity = string(alert.Severity)
	record.Message = alert.Message
	record.Status = string(alert.Status)
	record.CreatedAt = alert.CreatedAt
	record.SentAt = alert.SentAt

	// 序列化通道
	if channelsData, err := json.Marshal(alert.Channels); err == nil {
		record.Channels = string(channelsData)
	}

	// 序列化元数据
	if metadataData, err := json.Marshal(alert.Metadata); err == nil {
		record.Metadata = string(metadataData)
	}

	return nil
}

// PredictiveMetricsRecord 预测指标记录
type PredictiveMetricsRecord struct {
	ID                 int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	Date               time.Time `json:"date" gorm:"index"`
	TotalPredictions   int64     `json:"total_predictions"`
	CorrectPredictions int64     `json:"correct_predictions"`
	Accuracy           float64   `json:"accuracy"`
	TotalInsights      int       `json:"total_insights"`
	CriticalInsights   int       `json:"critical_insights"`
	TotalAlerts        int       `json:"total_alerts"`
	AlertSuccessRate   float64   `json:"alert_success_rate"`
	AvgProcessingTime  float64   `json:"avg_processing_time"`
	SystemHealth       float64   `json:"system_health"`
	UserSatisfaction   float64   `json:"user_satisfaction"`
	CreatedAt          time.Time `json:"created_at"`
}

// TableName 指定表名
func (PredictiveMetricsRecord) TableName() string {
	return "predictive_metrics"
}

// RecommendationRecord 建议记录
type RecommendationRecord struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	RecID       string    `json:"rec_id" gorm:"uniqueIndex;size:100;not null"`
	InsightID   string    `json:"insight_id" gorm:"size:100;index"`
	Title       string    `json:"title" gorm:"size:200;not null"`
	Description string    `json:"description" gorm:"type:text"`
	Action      string    `json:"action" gorm:"size:50;not null"`
	Priority    string    `json:"priority" gorm:"size:20;not null"`
	Impact      string    `json:"impact" gorm:"size:20"`
	Effort      string    `json:"effort" gorm:"size:20"`
	Commands    string    `json:"commands" gorm:"type:text"`
	Parameters  string    `json:"parameters" gorm:"type:text"`
	Benefits    string    `json:"benefits" gorm:"type:text"`
	Risks       string    `json:"risks" gorm:"type:text"`
	Timeline    string    `json:"timeline" gorm:"size:100"`
	Status      string    `json:"status" gorm:"size:20;default:'pending'"`
	ExecutedAt  *time.Time `json:"executed_at"`
	Result      string    `json:"result" gorm:"type:text"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (RecommendationRecord) TableName() string {
	return "recommendations"
}

// ToOperationRecommendation 转换为运维建议对象
func (record *RecommendationRecord) ToOperationRecommendation() (*OperationRecommendation, error) {
	// 解析命令
	var commands []string
	if record.Commands != "" {
		if err := json.Unmarshal([]byte(record.Commands), &commands); err != nil {
			commands = []string{}
		}
	}

	// 解析参数
	var parameters map[string]interface{}
	if record.Parameters != "" {
		if err := json.Unmarshal([]byte(record.Parameters), &parameters); err != nil {
			parameters = make(map[string]interface{})
		}
	} else {
		parameters = make(map[string]interface{})
	}

	// 解析收益
	var benefits []string
	if record.Benefits != "" {
		if err := json.Unmarshal([]byte(record.Benefits), &benefits); err != nil {
			benefits = []string{}
		}
	}

	// 解析风险
	var risks []string
	if record.Risks != "" {
		if err := json.Unmarshal([]byte(record.Risks), &risks); err != nil {
			risks = []string{}
		}
	}

	return &OperationRecommendation{
		ID:          record.RecID,
		Title:       record.Title,
		Description: record.Description,
		Action:      RecommendationAction(record.Action),
		Priority:    PriorityLevel(record.Priority),
		Impact:      ImpactLevel(record.Impact),
		Effort:      EffortLevel(record.Effort),
		Commands:    commands,
		Parameters:  parameters,
		Benefits:    benefits,
		Risks:       risks,
		Timeline:    record.Timeline,
	}, nil
}

// FromOperationRecommendation 从运维建议对象创建记录
func (record *RecommendationRecord) FromOperationRecommendation(rec *OperationRecommendation) error {
	record.RecID = rec.ID
	record.Title = rec.Title
	record.Description = rec.Description
	record.Action = string(rec.Action)
	record.Priority = string(rec.Priority)
	record.Impact = string(rec.Impact)
	record.Effort = string(rec.Effort)
	record.Timeline = rec.Timeline
	record.UpdatedAt = time.Now()

	// 序列化命令
	if commandsData, err := json.Marshal(rec.Commands); err == nil {
		record.Commands = string(commandsData)
	}

	// 序列化参数
	if parametersData, err := json.Marshal(rec.Parameters); err == nil {
		record.Parameters = string(parametersData)
	}

	// 序列化收益
	if benefitsData, err := json.Marshal(rec.Benefits); err == nil {
		record.Benefits = string(benefitsData)
	}

	// 序列化风险
	if risksData, err := json.Marshal(rec.Risks); err == nil {
		record.Risks = string(risksData)
	}

	return nil
}
