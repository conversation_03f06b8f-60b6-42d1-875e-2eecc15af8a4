package resilience

import (
	"context"
	"fmt"
	"math"
	"math/rand"
	"time"

	"github.com/sirupsen/logrus"
)

// RetryConfig 重试配置
type RetryConfig struct {
	MaxAttempts     int           `json:"max_attempts"`      // 最大重试次数
	InitialDelay    time.Duration `json:"initial_delay"`     // 初始延迟
	MaxDelay        time.Duration `json:"max_delay"`         // 最大延迟
	BackoffStrategy BackoffStrategy `json:"backoff_strategy"` // 退避策略
	Jitter          bool          `json:"jitter"`            // 是否添加抖动
	RetryableErrors []string      `json:"retryable_errors"`  // 可重试的错误类型
	OnRetry         func(attempt int, err error) `json:"-"`  // 重试回调
}

// BackoffStrategy 退避策略
type BackoffStrategy string

const (
	BackoffFixed       BackoffStrategy = "fixed"       // 固定延迟
	BackoffLinear      BackoffStrategy = "linear"      // 线性增长
	BackoffExponential BackoffStrategy = "exponential" // 指数退避
)

// RetryableFunc 可重试的函数类型
type RetryableFunc func() error

// RetryableFuncWithResult 带返回值的可重试函数类型
type RetryableFuncWithResult func() (interface{}, error)

// RetryableFuncWithContext 带上下文的可重试函数类型
type RetryableFuncWithContext func(ctx context.Context) error

// RetryableFuncWithContextAndResult 带上下文和返回值的可重试函数类型
type RetryableFuncWithContextAndResult func(ctx context.Context) (interface{}, error)

// Retryer 重试器
type Retryer struct {
	config RetryConfig
	logger *logrus.Logger
}

// NewRetryer 创建重试器
func NewRetryer(config RetryConfig, logger *logrus.Logger) *Retryer {
	// 设置默认值
	if config.MaxAttempts <= 0 {
		config.MaxAttempts = 3
	}
	if config.InitialDelay <= 0 {
		config.InitialDelay = 100 * time.Millisecond
	}
	if config.MaxDelay <= 0 {
		config.MaxDelay = 30 * time.Second
	}
	if config.BackoffStrategy == "" {
		config.BackoffStrategy = BackoffExponential
	}

	return &Retryer{
		config: config,
		logger: logger,
	}
}

// Execute 执行重试
func (r *Retryer) Execute(fn RetryableFunc) error {
	var lastErr error

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		err := fn()
		if err == nil {
			if attempt > 1 {
				r.logger.WithFields(logrus.Fields{
					"attempt": attempt,
					"total_attempts": r.config.MaxAttempts,
				}).Info("Retry succeeded")
			}
			return nil
		}

		lastErr = err

		// 检查是否为可重试错误
		if !r.isRetryableError(err) {
			r.logger.WithFields(logrus.Fields{
				"error": err.Error(),
				"attempt": attempt,
			}).Debug("Error is not retryable")
			return err
		}

		// 如果是最后一次尝试，不再重试
		if attempt == r.config.MaxAttempts {
			break
		}

		// 计算延迟时间
		delay := r.calculateDelay(attempt)

		r.logger.WithFields(logrus.Fields{
			"error": err.Error(),
			"attempt": attempt,
			"next_delay": delay,
			"max_attempts": r.config.MaxAttempts,
		}).Warn("Retrying after error")

		// 调用重试回调
		if r.config.OnRetry != nil {
			r.config.OnRetry(attempt, err)
		}

		// 等待延迟时间
		time.Sleep(delay)
	}

	return fmt.Errorf("max retry attempts (%d) exceeded, last error: %w", r.config.MaxAttempts, lastErr)
}

// ExecuteWithResult 执行重试并返回结果
func (r *Retryer) ExecuteWithResult(fn RetryableFuncWithResult) (interface{}, error) {
	var lastErr error

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		result, err := fn()
		if err == nil {
			if attempt > 1 {
				r.logger.WithFields(logrus.Fields{
					"attempt": attempt,
					"total_attempts": r.config.MaxAttempts,
				}).Info("Retry succeeded")
			}
			return result, nil
		}

		lastErr = err

		// 检查是否为可重试错误
		if !r.isRetryableError(err) {
			r.logger.WithFields(logrus.Fields{
				"error": err.Error(),
				"attempt": attempt,
			}).Debug("Error is not retryable")
			return nil, err
		}

		// 如果是最后一次尝试，不再重试
		if attempt == r.config.MaxAttempts {
			break
		}

		// 计算延迟时间
		delay := r.calculateDelay(attempt)

		r.logger.WithFields(logrus.Fields{
			"error": err.Error(),
			"attempt": attempt,
			"next_delay": delay,
			"max_attempts": r.config.MaxAttempts,
		}).Warn("Retrying after error")

		// 调用重试回调
		if r.config.OnRetry != nil {
			r.config.OnRetry(attempt, err)
		}

		// 等待延迟时间
		time.Sleep(delay)
	}

	return nil, fmt.Errorf("max retry attempts (%d) exceeded, last error: %w", r.config.MaxAttempts, lastErr)
}

// ExecuteWithContext 带上下文执行重试
func (r *Retryer) ExecuteWithContext(ctx context.Context, fn RetryableFuncWithContext) error {
	var lastErr error

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return ctx.Err()
		default:
		}

		err := fn(ctx)
		if err == nil {
			if attempt > 1 {
				r.logger.WithFields(logrus.Fields{
					"attempt": attempt,
					"total_attempts": r.config.MaxAttempts,
				}).Info("Retry succeeded")
			}
			return nil
		}

		lastErr = err

		// 检查是否为可重试错误
		if !r.isRetryableError(err) {
			r.logger.WithFields(logrus.Fields{
				"error": err.Error(),
				"attempt": attempt,
			}).Debug("Error is not retryable")
			return err
		}

		// 如果是最后一次尝试，不再重试
		if attempt == r.config.MaxAttempts {
			break
		}

		// 计算延迟时间
		delay := r.calculateDelay(attempt)

		r.logger.WithFields(logrus.Fields{
			"error": err.Error(),
			"attempt": attempt,
			"next_delay": delay,
			"max_attempts": r.config.MaxAttempts,
		}).Warn("Retrying after error")

		// 调用重试回调
		if r.config.OnRetry != nil {
			r.config.OnRetry(attempt, err)
		}

		// 带上下文的等待
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(delay):
		}
	}

	return fmt.Errorf("max retry attempts (%d) exceeded, last error: %w", r.config.MaxAttempts, lastErr)
}

// ExecuteWithContextAndResult 带上下文执行重试并返回结果
func (r *Retryer) ExecuteWithContextAndResult(ctx context.Context, fn RetryableFuncWithContextAndResult) (interface{}, error) {
	var lastErr error

	for attempt := 1; attempt <= r.config.MaxAttempts; attempt++ {
		// 检查上下文是否已取消
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		default:
		}

		result, err := fn(ctx)
		if err == nil {
			if attempt > 1 {
				r.logger.WithFields(logrus.Fields{
					"attempt": attempt,
					"total_attempts": r.config.MaxAttempts,
				}).Info("Retry succeeded")
			}
			return result, nil
		}

		lastErr = err

		// 检查是否为可重试错误
		if !r.isRetryableError(err) {
			r.logger.WithFields(logrus.Fields{
				"error": err.Error(),
				"attempt": attempt,
			}).Debug("Error is not retryable")
			return nil, err
		}

		// 如果是最后一次尝试，不再重试
		if attempt == r.config.MaxAttempts {
			break
		}

		// 计算延迟时间
		delay := r.calculateDelay(attempt)

		r.logger.WithFields(logrus.Fields{
			"error": err.Error(),
			"attempt": attempt,
			"next_delay": delay,
			"max_attempts": r.config.MaxAttempts,
		}).Warn("Retrying after error")

		// 调用重试回调
		if r.config.OnRetry != nil {
			r.config.OnRetry(attempt, err)
		}

		// 带上下文的等待
		select {
		case <-ctx.Done():
			return nil, ctx.Err()
		case <-time.After(delay):
		}
	}

	return nil, fmt.Errorf("max retry attempts (%d) exceeded, last error: %w", r.config.MaxAttempts, lastErr)
}

// calculateDelay 计算延迟时间
func (r *Retryer) calculateDelay(attempt int) time.Duration {
	var delay time.Duration

	switch r.config.BackoffStrategy {
	case BackoffFixed:
		delay = r.config.InitialDelay
	case BackoffLinear:
		delay = time.Duration(attempt) * r.config.InitialDelay
	case BackoffExponential:
		delay = time.Duration(math.Pow(2, float64(attempt-1))) * r.config.InitialDelay
	default:
		delay = r.config.InitialDelay
	}

	// 限制最大延迟
	if delay > r.config.MaxDelay {
		delay = r.config.MaxDelay
	}

	// 添加抖动
	if r.config.Jitter {
		jitter := time.Duration(rand.Float64() * float64(delay) * 0.1)
		delay += jitter
	}

	return delay
}

// isRetryableError 检查错误是否可重试
func (r *Retryer) isRetryableError(err error) bool {
	if len(r.config.RetryableErrors) == 0 {
		// 如果没有配置可重试错误，默认所有错误都可重试
		return true
	}

	errStr := err.Error()
	for _, retryableErr := range r.config.RetryableErrors {
		if errStr == retryableErr {
			return true
		}
	}

	return false
}

// 便捷函数

// Retry 简单重试函数
func Retry(maxAttempts int, delay time.Duration, fn RetryableFunc) error {
	retryer := NewRetryer(RetryConfig{
		MaxAttempts:  maxAttempts,
		InitialDelay: delay,
	}, logrus.New())
	return retryer.Execute(fn)
}

// RetryWithExponentialBackoff 指数退避重试
func RetryWithExponentialBackoff(maxAttempts int, initialDelay, maxDelay time.Duration, fn RetryableFunc) error {
	retryer := NewRetryer(RetryConfig{
		MaxAttempts:     maxAttempts,
		InitialDelay:    initialDelay,
		MaxDelay:        maxDelay,
		BackoffStrategy: BackoffExponential,
		Jitter:          true,
	}, logrus.New())
	return retryer.Execute(fn)
}

// RetryWithContext 带上下文的重试
func RetryWithContext(ctx context.Context, maxAttempts int, delay time.Duration, fn RetryableFuncWithContext) error {
	retryer := NewRetryer(RetryConfig{
		MaxAttempts:  maxAttempts,
		InitialDelay: delay,
	}, logrus.New())
	return retryer.ExecuteWithContext(ctx, fn)
}
