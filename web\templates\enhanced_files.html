<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强文件管理 - AI运维管理平台</title>
    <link rel="stylesheet" href="/static/css/design-system.css">
    <style>
        .files-container {
            max-width: 1600px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-primary);
            min-height: 100vh;
        }

        .files-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding: 20px;
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }

        .files-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .files-actions {
            display: flex;
            gap: 12px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: var(--bg-hover);
        }

        .files-layout {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 200px);
        }

        .files-sidebar {
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 20px;
            overflow-y: auto;
        }

        .files-main {
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .sidebar-section {
            margin-bottom: 30px;
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--border-color);
        }

        .host-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .host-item {
            padding: 12px;
            margin-bottom: 8px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .host-item:hover {
            background: var(--bg-hover);
            transform: translateX(4px);
        }

        .host-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .host-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .host-ip {
            font-size: 12px;
            opacity: 0.7;
        }

        .files-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 15px 20px;
            background: var(--bg-primary);
            border-bottom: 1px solid var(--border-color);
        }

        .breadcrumb {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: var(--text-secondary);
        }

        .breadcrumb-item {
            cursor: pointer;
            transition: color 0.3s ease;
        }

        .breadcrumb-item:hover {
            color: var(--text-primary);
        }

        .breadcrumb-separator {
            color: var(--text-secondary);
        }

        .toolbar-actions {
            display: flex;
            gap: 8px;
        }

        .toolbar-btn {
            padding: 8px 16px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .toolbar-btn:hover {
            background: var(--bg-hover);
        }

        .files-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .file-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
            gap: 16px;
        }

        .file-item {
            padding: 16px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }

        .file-item:hover {
            background: var(--bg-hover);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .file-item.selected {
            background: rgba(102, 126, 234, 0.1);
            border-color: #667eea;
        }

        .file-icon {
            font-size: 48px;
            margin-bottom: 12px;
            color: var(--text-secondary);
        }

        .file-name {
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 4px;
            word-break: break-all;
        }

        .file-meta {
            font-size: 12px;
            color: var(--text-secondary);
        }

        .file-list {
            display: none;
        }

        .file-list.active {
            display: block;
        }

        .file-table {
            width: 100%;
            border-collapse: collapse;
        }

        .file-table th,
        .file-table td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid var(--border-color);
        }

        .file-table th {
            background: var(--bg-primary);
            font-weight: 600;
            color: var(--text-primary);
        }

        .file-table tr:hover {
            background: var(--bg-hover);
        }

        .upload-area {
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 40px;
            text-align: center;
            margin: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: none;
        }

        .upload-area.active {
            display: block;
        }

        .upload-area:hover,
        .upload-area.dragover {
            border-color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .upload-progress {
            margin: 20px;
            display: none;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: var(--bg-primary);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .context-menu {
            position: fixed;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 8px;
            padding: 8px 0;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            z-index: 1000;
            display: none;
        }

        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            transition: background 0.3s ease;
        }

        .context-menu-item:hover {
            background: var(--bg-hover);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .notification.error {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }

        .notification.info {
            background: linear-gradient(135deg, #17a2b8, #007bff);
        }

        .modal {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 1000;
        }

        .modal.show {
            display: flex;
        }

        .modal-content {
            background: var(--bg-secondary);
            border-radius: 12px;
            padding: 30px;
            width: 500px;
            max-width: 90vw;
            border: 1px solid var(--border-color);
        }

        .modal-title {
            font-size: 20px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 8px;
        }

        .form-input {
            width: 100%;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-primary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .modal-actions {
            display: flex;
            gap: 12px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .view-toggle {
            display: flex;
            gap: 4px;
            background: var(--bg-primary);
            border-radius: 6px;
            padding: 4px;
        }

        .view-btn {
            padding: 8px 12px;
            background: transparent;
            border: none;
            border-radius: 4px;
            color: var(--text-secondary);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .view-btn.active {
            background: var(--bg-secondary);
            color: var(--text-primary);
        }
    </style>
</head>
<body>
    <div class="files-container">
        <!-- 文件管理头部 -->
        <div class="files-header">
            <h1 class="files-title">📁 增强文件管理</h1>
            <div class="files-actions">
                <button class="btn-secondary" onclick="showUploadArea()">📤 上传文件</button>
                <button class="btn-secondary" onclick="showCreateFolder()">📁 新建文件夹</button>
                <button class="btn-primary" onclick="refreshCurrentDirectory()">🔄 刷新</button>
            </div>
        </div>

        <!-- 文件管理主体 -->
        <div class="files-layout">
            <!-- 侧边栏 -->
            <div class="files-sidebar">
                <div class="sidebar-section">
                    <h3 class="sidebar-title">🖥️ 主机列表</h3>
                    <ul class="host-list" id="hostList">
                        <!-- 动态加载主机列表 -->
                    </ul>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-title">📊 统计信息</h3>
                    <div id="fileStats">
                        <div style="margin-bottom: 10px;">
                            <strong>总操作数:</strong> <span id="totalOperations">0</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>上传文件:</strong> <span id="totalUploads">0</span>
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>下载文件:</strong> <span id="totalDownloads">0</span>
                        </div>
                        <div>
                            <strong>数据传输:</strong> <span id="dataTransferred">0 MB</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="files-main">
                <!-- 工具栏 -->
                <div class="files-toolbar">
                    <div class="breadcrumb" id="breadcrumb">
                        <span class="breadcrumb-item" onclick="navigateToPath('/')">🏠 根目录</span>
                    </div>
                    <div class="toolbar-actions">
                        <div class="view-toggle">
                            <button class="view-btn active" onclick="switchView('grid')">🔲 网格</button>
                            <button class="view-btn" onclick="switchView('list')">📋 列表</button>
                        </div>
                        <button class="toolbar-btn" onclick="selectAll()">全选</button>
                        <button class="toolbar-btn" onclick="deleteSelected()">删除</button>
                    </div>
                </div>

                <!-- 文件内容区 -->
                <div class="files-content">
                    <!-- 网格视图 -->
                    <div class="file-grid active" id="fileGrid">
                        <!-- 动态加载文件列表 -->
                    </div>

                    <!-- 列表视图 -->
                    <div class="file-list" id="fileList">
                        <table class="file-table">
                            <thead>
                                <tr>
                                    <th>📄 名称</th>
                                    <th>📏 大小</th>
                                    <th>🕒 修改时间</th>
                                    <th>👤 所有者</th>
                                    <th>🔒 权限</th>
                                </tr>
                            </thead>
                            <tbody id="fileTableBody">
                                <!-- 动态加载文件列表 -->
                            </tbody>
                        </table>
                    </div>

                    <!-- 上传区域 -->
                    <div class="upload-area" id="uploadArea">
                        <p>📁 拖拽文件到此处或点击选择文件</p>
                        <input type="file" id="fileInput" style="display: none;" multiple>
                    </div>

                    <!-- 上传进度 -->
                    <div class="upload-progress" id="uploadProgress">
                        <div style="margin-bottom: 10px;">上传进度:</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progressFill"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 右键菜单 -->
    <div class="context-menu" id="contextMenu">
        <div class="context-menu-item" onclick="downloadFile()">📥 下载</div>
        <div class="context-menu-item" onclick="renameFile()">✏️ 重命名</div>
        <div class="context-menu-item" onclick="copyFile()">📋 复制</div>
        <div class="context-menu-item" onclick="moveFile()">✂️ 移动</div>
        <div class="context-menu-item" onclick="deleteFile()">🗑️ 删除</div>
        <div class="context-menu-item" onclick="showProperties()">ℹ️ 属性</div>
    </div>

    <!-- 创建文件夹模态框 -->
    <div class="modal" id="createFolderModal">
        <div class="modal-content">
            <h2 class="modal-title">创建新文件夹</h2>
            <div class="form-group">
                <label class="form-label">文件夹名称</label>
                <input type="text" class="form-input" id="folderName" placeholder="请输入文件夹名称">
            </div>
            <div class="modal-actions">
                <button class="btn-secondary" onclick="hideCreateFolder()">取消</button>
                <button class="btn-primary" onclick="createFolder()">创建</button>
            </div>
        </div>
    </div>

    <!-- 通知组件 -->
    <div class="notification" id="notification"></div>

    <script>
        // 全局变量
        let currentHost = null;
        let currentPath = '/';
        let selectedFiles = new Set();
        let currentView = 'grid';

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadHosts();
            loadStats();
            setupFileUpload();
            setupContextMenu();
            
            // 定期更新统计信息
            setInterval(loadStats, 30000);
        });

        // 加载主机列表
        async function loadHosts() {
            try {
                const response = await fetch('/api/v1/hosts');
                const result = await response.json();
                
                if (result.success) {
                    renderHostList(result.data.hosts);
                }
            } catch (error) {
                console.error('Failed to load hosts:', error);
                showNotification('加载主机列表失败', 'error');
            }
        }

        // 渲染主机列表
        function renderHostList(hosts) {
            const hostList = document.getElementById('hostList');
            hostList.innerHTML = '';

            hosts.forEach(host => {
                const li = document.createElement('li');
                li.className = 'host-item';
                li.onclick = () => selectHost(host);
                
                li.innerHTML = `
                    <div class="host-name">${host.name}</div>
                    <div class="host-ip">${host.ip_address}</div>
                `;
                
                hostList.appendChild(li);
            });
        }

        // 选择主机
        function selectHost(host) {
            // 移除之前的选中状态
            document.querySelectorAll('.host-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加选中状态
            event.currentTarget.classList.add('active');
            
            currentHost = host;
            currentPath = '/';
            
            // 加载根目录
            loadDirectory('/');
            
            showNotification(`已连接到 ${host.name}`, 'success');
        }

        // 加载目录内容
        async function loadDirectory(path) {
            if (!currentHost) {
                showNotification('请先选择主机', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/v1/enhanced-files/list?host_id=${currentHost.id}&path=${encodeURIComponent(path)}`);
                const result = await response.json();
                
                if (result.success) {
                    currentPath = path;
                    updateBreadcrumb(path);
                    renderFileList(result.data);
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Failed to load directory:', error);
                showNotification('加载目录失败: ' + error.message, 'error');
            }
        }

        // 更新面包屑导航
        function updateBreadcrumb(path) {
            const breadcrumb = document.getElementById('breadcrumb');
            const parts = path.split('/').filter(part => part);
            
            let html = '<span class="breadcrumb-item" onclick="navigateToPath(\'/\')">🏠 根目录</span>';
            
            let currentPath = '';
            parts.forEach((part, index) => {
                currentPath += '/' + part;
                html += '<span class="breadcrumb-separator"> / </span>';
                html += `<span class="breadcrumb-item" onclick="navigateToPath('${currentPath}')">${part}</span>`;
            });
            
            breadcrumb.innerHTML = html;
        }

        // 渲染文件列表
        function renderFileList(data) {
            if (currentView === 'grid') {
                renderGridView(data);
            } else {
                renderListView(data);
            }
        }

        // 渲染网格视图
        function renderGridView(data) {
            const fileGrid = document.getElementById('fileGrid');
            fileGrid.innerHTML = '';

            // 添加返回上级目录
            if (currentPath !== '/') {
                const parentItem = createFileItem({
                    name: '..',
                    is_directory: true,
                    path: getParentPath(currentPath)
                }, true);
                fileGrid.appendChild(parentItem);
            }

            // 添加目录
            data.directories.forEach(dir => {
                const item = createFileItem(dir);
                fileGrid.appendChild(item);
            });

            // 添加文件
            data.files.forEach(file => {
                const item = createFileItem(file);
                fileGrid.appendChild(item);
            });
        }

        // 创建文件项
        function createFileItem(fileInfo, isParent = false) {
            const div = document.createElement('div');
            div.className = 'file-item';
            div.dataset.path = fileInfo.path;
            div.dataset.isDirectory = fileInfo.is_directory;
            
            if (isParent) {
                div.onclick = () => navigateToPath(fileInfo.path);
            } else {
                div.onclick = (e) => handleFileClick(e, fileInfo);
                div.oncontextmenu = (e) => showContextMenu(e, fileInfo);
            }
            
            const icon = getFileIcon(fileInfo);
            const size = fileInfo.is_directory ? '' : formatFileSize(fileInfo.size);
            
            div.innerHTML = `
                <div class="file-icon">${icon}</div>
                <div class="file-name">${fileInfo.name}</div>
                <div class="file-meta">${size}</div>
            `;
            
            return div;
        }

        // 获取文件图标
        function getFileIcon(fileInfo) {
            if (fileInfo.name === '..') return '⬆️';
            if (fileInfo.is_directory) return '📁';
            
            const ext = fileInfo.extension?.toLowerCase();
            const iconMap = {
                '.txt': '📄',
                '.log': '📋',
                '.conf': '⚙️',
                '.json': '📊',
                '.yaml': '📊',
                '.yml': '📊',
                '.xml': '📊',
                '.csv': '📈',
                '.md': '📝',
                '.sh': '⚡',
                '.py': '🐍',
                '.js': '🟨',
                '.html': '🌐',
                '.css': '🎨',
                '.sql': '🗃️',
                '.zip': '📦',
                '.tar': '📦',
                '.gz': '📦'
            };
            
            return iconMap[ext] || '📄';
        }

        // 格式化文件大小
        function formatFileSize(bytes) {
            if (bytes === 0) return '0 B';
            const k = 1024;
            const sizes = ['B', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        // 处理文件点击
        function handleFileClick(event, fileInfo) {
            if (fileInfo.is_directory) {
                navigateToPath(fileInfo.path);
            } else {
                // 选择文件
                toggleFileSelection(event.currentTarget);
            }
        }

        // 切换文件选择
        function toggleFileSelection(element) {
            const path = element.dataset.path;
            
            if (selectedFiles.has(path)) {
                selectedFiles.delete(path);
                element.classList.remove('selected');
            } else {
                selectedFiles.add(path);
                element.classList.add('selected');
            }
        }

        // 导航到路径
        function navigateToPath(path) {
            loadDirectory(path);
        }

        // 获取父路径
        function getParentPath(path) {
            const parts = path.split('/').filter(part => part);
            parts.pop();
            return '/' + parts.join('/');
        }

        // 切换视图
        function switchView(view) {
            currentView = view;
            
            // 更新按钮状态
            document.querySelectorAll('.view-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.currentTarget.classList.add('active');
            
            // 切换视图
            if (view === 'grid') {
                document.getElementById('fileGrid').classList.add('active');
                document.getElementById('fileList').classList.remove('active');
            } else {
                document.getElementById('fileGrid').classList.remove('active');
                document.getElementById('fileList').classList.add('active');
            }
            
            // 重新渲染当前目录
            if (currentHost) {
                loadDirectory(currentPath);
            }
        }

        // 设置文件上传
        function setupFileUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            uploadArea.onclick = () => fileInput.click();

            uploadArea.ondragover = (e) => {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            };

            uploadArea.ondragleave = () => {
                uploadArea.classList.remove('dragover');
            };

            uploadArea.ondrop = (e) => {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
                handleFileUpload(e.dataTransfer.files);
            };

            fileInput.onchange = (e) => {
                handleFileUpload(e.target.files);
            };
        }

        // 处理文件上传
        function handleFileUpload(files) {
            if (!currentHost) {
                showNotification('请先选择主机', 'error');
                return;
            }

            Array.from(files).forEach(file => {
                uploadFile(file);
            });
        }

        // 上传文件
        async function uploadFile(file) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('host_id', currentHost.id);
            formData.append('target_path', currentPath);

            const progressBar = document.getElementById('progressFill');
            const uploadProgress = document.getElementById('uploadProgress');
            
            uploadProgress.style.display = 'block';
            progressBar.style.width = '0%';

            try {
                const response = await fetch('/api/v1/enhanced-files/upload', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();
                
                if (result.success) {
                    progressBar.style.width = '100%';
                    showNotification(`文件 ${file.name} 上传成功`, 'success');
                    setTimeout(() => {
                        uploadProgress.style.display = 'none';
                        refreshCurrentDirectory();
                    }, 2000);
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Failed to upload file:', error);
                showNotification('文件上传失败: ' + error.message, 'error');
                uploadProgress.style.display = 'none';
            }
        }

        // 加载统计信息
        async function loadStats() {
            try {
                const response = await fetch('/api/v1/enhanced-files/metrics');
                const result = await response.json();
                
                if (result.success) {
                    const stats = result.data;
                    document.getElementById('totalOperations').textContent = stats.total_operations || 0;
                    document.getElementById('totalUploads').textContent = stats.total_uploads || 0;
                    document.getElementById('totalDownloads').textContent = stats.total_downloads || 0;
                    document.getElementById('dataTransferred').textContent = formatFileSize(stats.total_bytes_uploaded + stats.total_bytes_downloaded || 0);
                }
            } catch (error) {
                console.error('Failed to load stats:', error);
            }
        }

        // 显示上传区域
        function showUploadArea() {
            document.getElementById('fileGrid').classList.remove('active');
            document.getElementById('fileList').classList.remove('active');
            document.getElementById('uploadArea').classList.add('active');
        }

        // 显示创建文件夹
        function showCreateFolder() {
            document.getElementById('createFolderModal').classList.add('show');
        }

        // 隐藏创建文件夹
        function hideCreateFolder() {
            document.getElementById('createFolderModal').classList.remove('show');
            document.getElementById('folderName').value = '';
        }

        // 创建文件夹
        async function createFolder() {
            const folderName = document.getElementById('folderName').value.trim();
            
            if (!folderName) {
                showNotification('请输入文件夹名称', 'error');
                return;
            }

            if (!currentHost) {
                showNotification('请先选择主机', 'error');
                return;
            }

            try {
                const response = await fetch('/api/v1/enhanced-files/create-directory', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        host_id: currentHost.id,
                        path: currentPath + '/' + folderName
                    })
                });

                const result = await response.json();
                
                if (result.success) {
                    hideCreateFolder();
                    showNotification('文件夹创建成功', 'success');
                    refreshCurrentDirectory();
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Failed to create folder:', error);
                showNotification('创建文件夹失败: ' + error.message, 'error');
            }
        }

        // 刷新当前目录
        function refreshCurrentDirectory() {
            if (currentHost && currentPath) {
                loadDirectory(currentPath);
            }
            
            // 隐藏上传区域，显示文件列表
            document.getElementById('uploadArea').classList.remove('active');
            if (currentView === 'grid') {
                document.getElementById('fileGrid').classList.add('active');
            } else {
                document.getElementById('fileList').classList.add('active');
            }
        }

        // 设置右键菜单
        function setupContextMenu() {
            document.addEventListener('click', () => {
                document.getElementById('contextMenu').style.display = 'none';
            });
        }

        // 显示右键菜单
        function showContextMenu(event, fileInfo) {
            event.preventDefault();
            const contextMenu = document.getElementById('contextMenu');
            contextMenu.style.display = 'block';
            contextMenu.style.left = event.pageX + 'px';
            contextMenu.style.top = event.pageY + 'px';
            
            // 保存当前文件信息
            contextMenu.dataset.filePath = fileInfo.path;
            contextMenu.dataset.fileName = fileInfo.name;
        }

        // 右键菜单操作
        function downloadFile() {
            const contextMenu = document.getElementById('contextMenu');
            const filePath = contextMenu.dataset.filePath;
            
            if (currentHost && filePath) {
                window.open(`/api/v1/enhanced-files/download?host_id=${currentHost.id}&source_path=${encodeURIComponent(filePath)}`, '_blank');
                showNotification('开始下载文件', 'info');
            }
        }

        function renameFile() {
            showNotification('重命名功能开发中', 'info');
        }

        function copyFile() {
            showNotification('复制功能开发中', 'info');
        }

        function moveFile() {
            showNotification('移动功能开发中', 'info');
        }

        function deleteFile() {
            showNotification('删除功能开发中', 'info');
        }

        function showProperties() {
            showNotification('属性功能开发中', 'info');
        }

        function selectAll() {
            showNotification('全选功能开发中', 'info');
        }

        function deleteSelected() {
            showNotification('批量删除功能开发中', 'info');
        }

        // 渲染列表视图
        function renderListView(data) {
            const tableBody = document.getElementById('fileTableBody');
            tableBody.innerHTML = '';

            // 添加返回上级目录
            if (currentPath !== '/') {
                const row = createTableRow({
                    name: '..',
                    is_directory: true,
                    path: getParentPath(currentPath),
                    size: 0,
                    mod_time: new Date(),
                    owner: '',
                    permissions: ''
                }, true);
                tableBody.appendChild(row);
            }

            // 添加目录
            data.directories.forEach(dir => {
                const row = createTableRow(dir);
                tableBody.appendChild(row);
            });

            // 添加文件
            data.files.forEach(file => {
                const row = createTableRow(file);
                tableBody.appendChild(row);
            });
        }

        // 创建表格行
        function createTableRow(fileInfo, isParent = false) {
            const tr = document.createElement('tr');
            tr.dataset.path = fileInfo.path;
            tr.dataset.isDirectory = fileInfo.is_directory;
            
            if (isParent) {
                tr.onclick = () => navigateToPath(fileInfo.path);
            } else {
                tr.onclick = (e) => handleFileClick(e, fileInfo);
                tr.oncontextmenu = (e) => showContextMenu(e, fileInfo);
            }
            
            const icon = getFileIcon(fileInfo);
            const size = fileInfo.is_directory ? '-' : formatFileSize(fileInfo.size);
            const modTime = fileInfo.mod_time ? new Date(fileInfo.mod_time).toLocaleString() : '-';
            
            tr.innerHTML = `
                <td>${icon} ${fileInfo.name}</td>
                <td>${size}</td>
                <td>${modTime}</td>
                <td>${fileInfo.owner || '-'}</td>
                <td>${fileInfo.permissions || '-'}</td>
            `;
            
            return tr;
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
    </script>
</body>
</html>
