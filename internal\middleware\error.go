package middleware

import (
	"errors"
	"fmt"
	"net/http"
	"runtime/debug"
	"strings"

	"aiops-platform/internal/model"

	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"
	"github.com/sirupsen/logrus"
)

// ErrorHandlerMiddleware 全局错误处理中间件
func ErrorHandlerMiddleware(logger *logrus.Logger) gin.HandlerFunc {
	return gin.CustomRecovery(func(c *gin.Context, recovered interface{}) {
		requestID := GetRequestID(c)
		traceID := GetTraceID(c)

		// 记录panic信息
		if recovered != nil {
			logger.WithFields(logrus.Fields{
				"request_id": requestID,
				"trace_id":   traceID,
				"method":     c.Request.Method,
				"path":       c.Request.URL.Path,
				"panic":      recovered,
				"stack":      string(debug.Stack()),
			}).Error("Panic recovered")

			// 返回内部错误响应
			response := model.InternalErrorResponse(requestID, traceID)
			c.JSO<PERSON>(http.StatusInternalServerError, response)
			c.Abort()
			return
		}

		// 处理其他错误
		if len(c.Errors) > 0 {
			handleErrors(c, logger, requestID, traceID)
		}
	})
}

// handleErrors 处理错误列表
func handleErrors(c *gin.Context, logger *logrus.Logger, requestID, traceID string) {
	err := c.Errors.Last().Err

	// 记录错误日志
	logger.WithFields(logrus.Fields{
		"request_id": requestID,
		"trace_id":   traceID,
		"method":     c.Request.Method,
		"path":       c.Request.URL.Path,
		"error":      err.Error(),
	}).Error("Request error")

	// 根据错误类型返回相应的响应
	switch {
	case isAppError(err):
		handleAppError(c, err.(*model.AppError), requestID, traceID)
	case isValidationError(err):
		handleValidationError(c, err, requestID, traceID)
	case isBindingError(err):
		handleBindingError(c, err, requestID, traceID)
	default:
		handleGenericError(c, err, requestID, traceID)
	}
}

// isAppError 检查是否为应用错误
func isAppError(err error) bool {
	_, ok := err.(*model.AppError)
	return ok
}

// isValidationError 检查是否为验证错误
func isValidationError(err error) bool {
	var validationErrors validator.ValidationErrors
	return errors.As(err, &validationErrors)
}

// isBindingError 检查是否为绑定错误
func isBindingError(err error) bool {
	return strings.Contains(err.Error(), "bind") ||
		strings.Contains(err.Error(), "unmarshal") ||
		strings.Contains(err.Error(), "invalid character")
}

// handleAppError 处理应用错误
func handleAppError(c *gin.Context, appErr *model.AppError, requestID, traceID string) {
	appErr.RequestID = requestID
	response := model.ErrorResponseFromAppError(appErr, requestID, traceID)
	c.JSON(appErr.HTTPStatus(), response)
}

// handleValidationError 处理验证错误
func handleValidationError(c *gin.Context, err error, requestID, traceID string) {
	var validationErrors validator.ValidationErrors
	if errors.As(err, &validationErrors) {
		var modelErrors model.ValidationErrors
		for _, fieldError := range validationErrors {
			modelErrors = append(modelErrors, model.ValidationError{
				Field:   getJSONFieldName(fieldError),
				Message: getValidationErrorMessage(fieldError),
				Value:   fieldError.Value(),
			})
		}
		response := model.ValidationErrorResponse(modelErrors, requestID, traceID)
		c.JSON(http.StatusBadRequest, response)
		return
	}

	// 如果不是标准验证错误，按通用错误处理
	handleGenericError(c, err, requestID, traceID)
}

// handleBindingError 处理绑定错误
func handleBindingError(c *gin.Context, err error, requestID, traceID string) {
	appErr := model.NewAppError(model.ErrCodeInvalidParams, "请求参数格式错误", err)
	response := model.ErrorResponseFromAppError(appErr, requestID, traceID)
	c.JSON(http.StatusBadRequest, response)
}

// handleGenericError 处理通用错误
func handleGenericError(c *gin.Context, err error, requestID, traceID string) {
	appErr := model.NewAppError(model.ErrCodeInternalError, err.Error(), err)
	response := model.ErrorResponseFromAppError(appErr, requestID, traceID)
	c.JSON(http.StatusInternalServerError, response)
}

// getJSONFieldName 获取JSON字段名
func getJSONFieldName(fieldError validator.FieldError) string {
	// 这里可以根据需要实现更复杂的字段名映射逻辑
	return fieldError.Field()
}

// getValidationErrorMessage 获取验证错误消息
func getValidationErrorMessage(fieldError validator.FieldError) string {
	field := fieldError.Field()
	tag := fieldError.Tag()
	param := fieldError.Param()

	switch tag {
	case "required":
		return fmt.Sprintf("%s是必填字段", field)
	case "min":
		return fmt.Sprintf("%s最小值为%s", field, param)
	case "max":
		return fmt.Sprintf("%s最大值为%s", field, param)
	case "len":
		return fmt.Sprintf("%s长度必须为%s", field, param)
	case "email":
		return fmt.Sprintf("%s必须是有效的邮箱地址", field)
	case "url":
		return fmt.Sprintf("%s必须是有效的URL", field)
	case "oneof":
		return fmt.Sprintf("%s必须是以下值之一: %s", field, param)
	case "uuid":
		return fmt.Sprintf("%s必须是有效的UUID", field)
	case "alphanum":
		return fmt.Sprintf("%s只能包含字母和数字", field)
	case "numeric":
		return fmt.Sprintf("%s必须是数字", field)
	case "alpha":
		return fmt.Sprintf("%s只能包含字母", field)
	default:
		return fmt.Sprintf("%s验证失败", field)
	}
}

// AbortWithError 中止请求并返回错误
func AbortWithError(c *gin.Context, err error) {
	c.Error(err)
	c.Abort()
}

// AbortWithAppError 中止请求并返回应用错误
func AbortWithAppError(c *gin.Context, code model.ErrorCode, details string, cause error) {
	appErr := model.NewAppError(code, details, cause)
	AbortWithError(c, appErr)
}

// AbortWithValidationError 中止请求并返回验证错误
func AbortWithValidationError(c *gin.Context, errors model.ValidationErrors) {
	c.Error(errors)
	c.Abort()
}

// HandleError 处理错误的辅助函数
func HandleError(c *gin.Context, err error) {
	if err != nil {
		c.Error(err)
	}
}

// HandleAppError 处理应用错误的辅助函数
func HandleAppError(c *gin.Context, code model.ErrorCode, details string, cause error) {
	appErr := model.NewAppError(code, details, cause)
	c.Error(appErr)
}

// MustNotError 确保没有错误，如果有错误则panic
func MustNotError(err error) {
	if err != nil {
		panic(err)
	}
}

// WrapDatabaseError 包装数据库错误
func WrapDatabaseError(err error) error {
	if err == nil {
		return nil
	}
	return model.NewAppError(model.ErrCodeDatabaseError, "数据库操作失败", err)
}

// WrapConfigError 包装配置错误
func WrapConfigError(err error) error {
	if err == nil {
		return nil
	}
	return model.NewAppError(model.ErrCodeConfigError, "配置错误", err)
}

// WrapValidationError 包装验证错误
func WrapValidationError(field, message string, value interface{}) error {
	return model.ValidationErrors{
		{Field: field, Message: message, Value: value},
	}
}
