package visualization

import (
	"encoding/json"
	"fmt"
	"reflect"
	"time"

	"github.com/sirupsen/logrus"
)

// 数据处理器实现

// TimeSeriesDataProcessor 时间序列数据处理器
type TimeSeriesDataProcessor struct {
	logger *logrus.Logger
}

// Process 处理时间序列数据
func (tsdp *TimeSeriesDataProcessor) Process(rawData interface{}) (*ChartData, error) {
	// 简化实现：将原始数据转换为图表数据
	data := &ChartData{
		Type:      "timeseries",
		Labels:    []string{"00:00", "06:00", "12:00", "18:00", "24:00"},
		Datasets:  []*Dataset{},
		Metadata:  make(map[string]interface{}),
		Timestamp: time.Now(),
		Source:    "TimeSeriesDataProcessor",
		Version:   "1.0",
	}

	// 创建示例数据集
	dataset := &Dataset{
		Label:           "时间序列数据",
		Data:            []interface{}{10, 20, 15, 25, 30},
		BackgroundColor: []string{"rgba(75, 192, 192, 0.2)"},
		BorderColor:     []string{"rgba(75, 192, 192, 1)"},
		BorderWidth:     2,
		Fill:            false,
		Tension:         0.1,
	}

	data.Datasets = append(data.Datasets, dataset)

	return data, nil
}

// GetDataType 获取数据类型
func (tsdp *TimeSeriesDataProcessor) GetDataType() string {
	return "timeseries"
}

// ValidateInput 验证输入
func (tsdp *TimeSeriesDataProcessor) ValidateInput(rawData interface{}) error {
	if rawData == nil {
		return fmt.Errorf("input data cannot be nil")
	}
	return nil
}

// StatisticsDataProcessor 统计数据处理器
type StatisticsDataProcessor struct {
	logger *logrus.Logger
}

// Process 处理统计数据
func (sdp *StatisticsDataProcessor) Process(rawData interface{}) (*ChartData, error) {
	data := &ChartData{
		Type:      "statistics",
		Labels:    []string{"成功", "失败", "超时", "错误"},
		Datasets:  []*Dataset{},
		Metadata:  make(map[string]interface{}),
		Timestamp: time.Now(),
		Source:    "StatisticsDataProcessor",
		Version:   "1.0",
	}

	dataset := &Dataset{
		Label: "统计数据",
		Data:  []interface{}{950, 30, 15, 5},
		BackgroundColor: []string{
			"rgba(54, 162, 235, 0.2)",
			"rgba(255, 99, 132, 0.2)",
			"rgba(255, 205, 86, 0.2)",
			"rgba(75, 192, 192, 0.2)",
		},
		BorderColor: []string{
			"rgba(54, 162, 235, 1)",
			"rgba(255, 99, 132, 1)",
			"rgba(255, 205, 86, 1)",
			"rgba(75, 192, 192, 1)",
		},
		BorderWidth: 1,
	}

	data.Datasets = append(data.Datasets, dataset)

	return data, nil
}

// GetDataType 获取数据类型
func (sdp *StatisticsDataProcessor) GetDataType() string {
	return "statistics"
}

// ValidateInput 验证输入
func (sdp *StatisticsDataProcessor) ValidateInput(rawData interface{}) error {
	if rawData == nil {
		return fmt.Errorf("input data cannot be nil")
	}
	return nil
}

// PerformanceDataProcessor 性能数据处理器
type PerformanceDataProcessor struct {
	logger *logrus.Logger
}

// Process 处理性能数据
func (pdp *PerformanceDataProcessor) Process(rawData interface{}) (*ChartData, error) {
	data := &ChartData{
		Type:      "performance",
		Labels:    []string{"CPU", "内存", "磁盘", "网络"},
		Datasets:  []*Dataset{},
		Metadata:  make(map[string]interface{}),
		Timestamp: time.Now(),
		Source:    "PerformanceDataProcessor",
		Version:   "1.0",
	}

	dataset := &Dataset{
		Label: "性能指标",
		Data:  []interface{}{45.5, 68.2, 23.1, 12.8},
		BackgroundColor: []string{
			"rgba(255, 99, 132, 0.2)",
			"rgba(54, 162, 235, 0.2)",
			"rgba(255, 205, 86, 0.2)",
			"rgba(75, 192, 192, 0.2)",
		},
		BorderColor: []string{
			"rgba(255, 99, 132, 1)",
			"rgba(54, 162, 235, 1)",
			"rgba(255, 205, 86, 1)",
			"rgba(75, 192, 192, 1)",
		},
		BorderWidth: 2,
	}

	data.Datasets = append(data.Datasets, dataset)

	return data, nil
}

// GetDataType 获取数据类型
func (pdp *PerformanceDataProcessor) GetDataType() string {
	return "performance"
}

// ValidateInput 验证输入
func (pdp *PerformanceDataProcessor) ValidateInput(rawData interface{}) error {
	if rawData == nil {
		return fmt.Errorf("input data cannot be nil")
	}
	return nil
}

// MonitoringDataProcessor 监控数据处理器
type MonitoringDataProcessor struct {
	logger *logrus.Logger
}

// Process 处理监控数据
func (mdp *MonitoringDataProcessor) Process(rawData interface{}) (*ChartData, error) {
	data := &ChartData{
		Type:      "monitoring",
		Labels:    []string{"正常", "警告", "错误", "严重"},
		Datasets:  []*Dataset{},
		Metadata:  make(map[string]interface{}),
		Timestamp: time.Now(),
		Source:    "MonitoringDataProcessor",
		Version:   "1.0",
	}

	dataset := &Dataset{
		Label: "监控状态",
		Data:  []interface{}{850, 120, 25, 5},
		BackgroundColor: []string{
			"rgba(75, 192, 192, 0.2)",
			"rgba(255, 205, 86, 0.2)",
			"rgba(255, 159, 64, 0.2)",
			"rgba(255, 99, 132, 0.2)",
		},
		BorderColor: []string{
			"rgba(75, 192, 192, 1)",
			"rgba(255, 205, 86, 1)",
			"rgba(255, 159, 64, 1)",
			"rgba(255, 99, 132, 1)",
		},
		BorderWidth: 1,
	}

	data.Datasets = append(data.Datasets, dataset)

	return data, nil
}

// GetDataType 获取数据类型
func (mdp *MonitoringDataProcessor) GetDataType() string {
	return "monitoring"
}

// ValidateInput 验证输入
func (mdp *MonitoringDataProcessor) ValidateInput(rawData interface{}) error {
	if rawData == nil {
		return fmt.Errorf("input data cannot be nil")
	}
	return nil
}

// LogDataProcessor 日志数据处理器
type LogDataProcessor struct {
	logger *logrus.Logger
}

// Process 处理日志数据
func (ldp *LogDataProcessor) Process(rawData interface{}) (*ChartData, error) {
	data := &ChartData{
		Type:      "logs",
		Labels:    []string{"INFO", "WARN", "ERROR", "DEBUG"},
		Datasets:  []*Dataset{},
		Metadata:  make(map[string]interface{}),
		Timestamp: time.Now(),
		Source:    "LogDataProcessor",
		Version:   "1.0",
	}

	dataset := &Dataset{
		Label: "日志级别分布",
		Data:  []interface{}{1200, 150, 45, 800},
		BackgroundColor: []string{
			"rgba(54, 162, 235, 0.2)",
			"rgba(255, 205, 86, 0.2)",
			"rgba(255, 99, 132, 0.2)",
			"rgba(153, 102, 255, 0.2)",
		},
		BorderColor: []string{
			"rgba(54, 162, 235, 1)",
			"rgba(255, 205, 86, 1)",
			"rgba(255, 99, 132, 1)",
			"rgba(153, 102, 255, 1)",
		},
		BorderWidth: 1,
	}

	data.Datasets = append(data.Datasets, dataset)

	return data, nil
}

// GetDataType 获取数据类型
func (ldp *LogDataProcessor) GetDataType() string {
	return "logs"
}

// ValidateInput 验证输入
func (ldp *LogDataProcessor) ValidateInput(rawData interface{}) error {
	if rawData == nil {
		return fmt.Errorf("input data cannot be nil")
	}
	return nil
}

// GenericDataProcessor 通用数据处理器
type GenericDataProcessor struct {
	logger *logrus.Logger
}

// Process 处理通用数据
func (gdp *GenericDataProcessor) Process(rawData interface{}) (*ChartData, error) {
	data := &ChartData{
		Type:      "generic",
		Labels:    []string{},
		Datasets:  []*Dataset{},
		Metadata:  make(map[string]interface{}),
		Timestamp: time.Now(),
		Source:    "GenericDataProcessor",
		Version:   "1.0",
	}

	// 尝试解析原始数据
	if err := gdp.parseRawData(rawData, data); err != nil {
		return nil, fmt.Errorf("failed to parse raw data: %w", err)
	}

	return data, nil
}

// GetDataType 获取数据类型
func (gdp *GenericDataProcessor) GetDataType() string {
	return "generic"
}

// ValidateInput 验证输入
func (gdp *GenericDataProcessor) ValidateInput(rawData interface{}) error {
	if rawData == nil {
		return fmt.Errorf("input data cannot be nil")
	}
	return nil
}

// parseRawData 解析原始数据
func (gdp *GenericDataProcessor) parseRawData(rawData interface{}, data *ChartData) error {
	// 简化实现：根据数据类型进行基本解析
	switch v := rawData.(type) {
	case map[string]interface{}:
		return gdp.parseMapData(v, data)
	case []interface{}:
		return gdp.parseArrayData(v, data)
	case string:
		return gdp.parseStringData(v, data)
	default:
		return gdp.parseDefaultData(v, data)
	}
}

// parseMapData 解析映射数据
func (gdp *GenericDataProcessor) parseMapData(mapData map[string]interface{}, data *ChartData) error {
	// 提取标签和数据
	for key, value := range mapData {
		data.Labels = append(data.Labels, key)

		// 尝试转换值为数字
		if numValue, ok := gdp.toNumber(value); ok {
			if len(data.Datasets) == 0 {
				data.Datasets = append(data.Datasets, &Dataset{
					Label: "数据",
					Data:  []interface{}{},
				})
			}
			data.Datasets[0].Data = append(data.Datasets[0].Data, numValue)
		}
	}

	return nil
}

// parseArrayData 解析数组数据
func (gdp *GenericDataProcessor) parseArrayData(arrayData []interface{}, data *ChartData) error {
	dataset := &Dataset{
		Label: "数据序列",
		Data:  arrayData,
	}

	data.Datasets = append(data.Datasets, dataset)

	// 生成标签
	for i := range arrayData {
		data.Labels = append(data.Labels, fmt.Sprintf("项目 %d", i+1))
	}

	return nil
}

// parseStringData 解析字符串数据
func (gdp *GenericDataProcessor) parseStringData(stringData string, data *ChartData) error {
	// 尝试解析为JSON
	var jsonData interface{}
	if err := json.Unmarshal([]byte(stringData), &jsonData); err == nil {
		return gdp.parseRawData(jsonData, data)
	}

	// 如果不是JSON，创建简单数据
	data.Labels = []string{"数据"}
	data.Datasets = append(data.Datasets, &Dataset{
		Label: "字符串数据",
		Data:  []interface{}{len(stringData)},
	})

	return nil
}

// parseDefaultData 解析默认数据
func (gdp *GenericDataProcessor) parseDefaultData(defaultData interface{}, data *ChartData) error {
	// 使用反射获取基本信息
	value := reflect.ValueOf(defaultData)

	data.Labels = []string{"值"}
	data.Datasets = append(data.Datasets, &Dataset{
		Label: fmt.Sprintf("数据 (%s)", value.Type().String()),
		Data:  []interface{}{fmt.Sprintf("%v", defaultData)},
	})

	return nil
}

// toNumber 尝试将值转换为数字
func (gdp *GenericDataProcessor) toNumber(value interface{}) (float64, bool) {
	switch v := value.(type) {
	case int:
		return float64(v), true
	case int64:
		return float64(v), true
	case float32:
		return float64(v), true
	case float64:
		return v, true
	default:
		return 0, false
	}
}
