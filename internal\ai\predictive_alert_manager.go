package ai

import (
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// PredictiveAlertManager 预测告警管理器
type PredictiveAlertManager struct {
	logger      *logrus.Logger
	config      *PredictiveConfig
	alertRules  []AlertRule
	channels    map[string]AlertChannel
	history     []AlertRecord
}

// AlertRule 告警规则
type AlertRule struct {
	Name        string                 `json:"name"`
	Type        InsightType            `json:"type"`
	Severity    SeverityLevel          `json:"severity"`
	Condition   func(*PredictiveInsight) bool `json:"-"`
	Channels    []string               `json:"channels"`
	Cooldown    time.Duration          `json:"cooldown"`
	LastFired   time.Time              `json:"last_fired"`
	Enabled     bool                   `json:"enabled"`
	Template    AlertTemplate          `json:"template"`
}

// AlertChannel 告警通道
type AlertChannel struct {
	Name     string                 `json:"name"`
	Type     AlertChannelType       `json:"type"`
	Config   map[string]interface{} `json:"config"`
	Enabled  bool                   `json:"enabled"`
}

// AlertChannelType 告警通道类型
type AlertChannelType string

const (
	AlertChannelEmail    AlertChannelType = "email"
	AlertChannelWebhook  AlertChannelType = "webhook"
	AlertChannelSlack    AlertChannelType = "slack"
	AlertChannelSMS      AlertChannelType = "sms"
	AlertChannelConsole  AlertChannelType = "console"
)

// AlertTemplate 告警模板
type AlertTemplate struct {
	Subject string `json:"subject"`
	Body    string `json:"body"`
	Format  string `json:"format"` // text, html, markdown
}

// AlertRecord 告警记录
type AlertRecord struct {
	ID          string                 `json:"id"`
	RuleName    string                 `json:"rule_name"`
	InsightID   string                 `json:"insight_id"`
	Severity    SeverityLevel          `json:"severity"`
	Message     string                 `json:"message"`
	Channels    []string               `json:"channels"`
	Status      AlertStatus            `json:"status"`
	CreatedAt   time.Time              `json:"created_at"`
	SentAt      *time.Time             `json:"sent_at,omitempty"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// AlertStatus 告警状态
type AlertStatus string

const (
	AlertStatusPending   AlertStatus = "pending"
	AlertStatusSent      AlertStatus = "sent"
	AlertStatusFailed    AlertStatus = "failed"
	AlertStatusSuppressed AlertStatus = "suppressed"
)

// NewPredictiveAlertManager 创建预测告警管理器
func NewPredictiveAlertManager(logger *logrus.Logger, config *PredictiveConfig) *PredictiveAlertManager {
	manager := &PredictiveAlertManager{
		logger:   logger,
		config:   config,
		channels: make(map[string]AlertChannel),
		history:  make([]AlertRecord, 0),
	}

	// 初始化默认告警规则
	manager.initializeAlertRules()

	// 初始化默认告警通道
	manager.initializeAlertChannels()

	logger.Info("🚨 预测告警管理器初始化完成")
	return manager
}

// ProcessInsights 处理洞察并触发告警
func (pam *PredictiveAlertManager) ProcessInsights(insights []PredictiveInsight) error {
	pam.logger.Info("🚨 开始处理预测告警")

	alertCount := 0

	for _, insight := range insights {
		// 检查是否需要告警
		if pam.shouldAlert(&insight) {
			if err := pam.triggerAlert(&insight); err != nil {
				pam.logger.WithError(err).WithField("insight_id", insight.ID).Error("触发告警失败")
			} else {
				alertCount++
			}
		}
	}

	pam.logger.WithField("alert_count", alertCount).Info("🚨 预测告警处理完成")
	return nil
}

// shouldAlert 判断是否应该告警
func (pam *PredictiveAlertManager) shouldAlert(insight *PredictiveInsight) bool {
	// 检查置信度阈值
	if insight.Confidence < pam.config.AlertThreshold {
		return false
	}

	// 检查严重程度
	if insight.Severity == SeverityLow {
		return false
	}

	// 检查是否有匹配的告警规则
	for _, rule := range pam.alertRules {
		if rule.Enabled && rule.Type == insight.Type && rule.Condition(insight) {
			// 检查冷却时间
			if time.Since(rule.LastFired) < rule.Cooldown {
				pam.logger.WithFields(logrus.Fields{
					"rule":     rule.Name,
					"cooldown": rule.Cooldown,
				}).Debug("告警规则在冷却期内")
				return false
			}
			return true
		}
	}

	return false
}

// triggerAlert 触发告警
func (pam *PredictiveAlertManager) triggerAlert(insight *PredictiveInsight) error {
	// 找到匹配的规则
	var matchedRule *AlertRule
	for i, rule := range pam.alertRules {
		if rule.Enabled && rule.Type == insight.Type && rule.Condition(insight) {
			matchedRule = &pam.alertRules[i]
			break
		}
	}

	if matchedRule == nil {
		return fmt.Errorf("no matching alert rule found for insight %s", insight.ID)
	}

	// 创建告警记录
	alertRecord := AlertRecord{
		ID:        pam.generateAlertID(),
		RuleName:  matchedRule.Name,
		InsightID: insight.ID,
		Severity:  insight.Severity,
		Message:   pam.formatAlertMessage(matchedRule, insight),
		Channels:  matchedRule.Channels,
		Status:    AlertStatusPending,
		CreatedAt: time.Now(),
		Metadata: map[string]interface{}{
			"insight_type": insight.Type,
			"confidence":   insight.Confidence,
		},
	}

	// 发送告警
	if err := pam.sendAlert(&alertRecord, matchedRule); err != nil {
		alertRecord.Status = AlertStatusFailed
		pam.history = append(pam.history, alertRecord)
		return fmt.Errorf("failed to send alert: %w", err)
	}

	// 更新状态
	now := time.Now()
	alertRecord.Status = AlertStatusSent
	alertRecord.SentAt = &now
	matchedRule.LastFired = now

	// 记录告警历史
	pam.history = append(pam.history, alertRecord)

	pam.logger.WithFields(logrus.Fields{
		"alert_id":   alertRecord.ID,
		"rule":       matchedRule.Name,
		"insight_id": insight.ID,
		"channels":   matchedRule.Channels,
	}).Info("告警发送成功")

	return nil
}

// sendAlert 发送告警
func (pam *PredictiveAlertManager) sendAlert(record *AlertRecord, rule *AlertRule) error {
	for _, channelName := range rule.Channels {
		channel, exists := pam.channels[channelName]
		if !exists || !channel.Enabled {
			pam.logger.WithField("channel", channelName).Warn("告警通道不存在或已禁用")
			continue
		}

		if err := pam.sendToChannel(&channel, record, rule); err != nil {
			pam.logger.WithError(err).WithField("channel", channelName).Error("发送告警到通道失败")
			return err
		}
	}

	return nil
}

// sendToChannel 发送告警到指定通道
func (pam *PredictiveAlertManager) sendToChannel(channel *AlertChannel, record *AlertRecord, rule *AlertRule) error {
	switch channel.Type {
	case AlertChannelConsole:
		return pam.sendToConsole(record, rule)
	case AlertChannelEmail:
		return pam.sendToEmail(channel, record, rule)
	case AlertChannelWebhook:
		return pam.sendToWebhook(channel, record, rule)
	case AlertChannelSlack:
		return pam.sendToSlack(channel, record, rule)
	case AlertChannelSMS:
		return pam.sendToSMS(channel, record, rule)
	default:
		return fmt.Errorf("unsupported alert channel type: %s", channel.Type)
	}
}

// sendToConsole 发送到控制台
func (pam *PredictiveAlertManager) sendToConsole(record *AlertRecord, rule *AlertRule) error {
	pam.logger.WithFields(logrus.Fields{
		"alert_id": record.ID,
		"severity": record.Severity,
		"rule":     record.RuleName,
	}).Warn(fmt.Sprintf("🚨 预测告警: %s", record.Message))
	return nil
}

// sendToEmail 发送邮件告警
func (pam *PredictiveAlertManager) sendToEmail(channel *AlertChannel, record *AlertRecord, rule *AlertRule) error {
	// 这里应该集成实际的邮件发送服务
	pam.logger.WithFields(logrus.Fields{
		"channel": channel.Name,
		"alert":   record.ID,
	}).Info("📧 邮件告警已发送（模拟）")
	return nil
}

// sendToWebhook 发送Webhook告警
func (pam *PredictiveAlertManager) sendToWebhook(channel *AlertChannel, record *AlertRecord, rule *AlertRule) error {
	// 这里应该发送HTTP请求到Webhook URL
	pam.logger.WithFields(logrus.Fields{
		"channel": channel.Name,
		"alert":   record.ID,
	}).Info("🔗 Webhook告警已发送（模拟）")
	return nil
}

// sendToSlack 发送Slack告警
func (pam *PredictiveAlertManager) sendToSlack(channel *AlertChannel, record *AlertRecord, rule *AlertRule) error {
	// 这里应该集成Slack API
	pam.logger.WithFields(logrus.Fields{
		"channel": channel.Name,
		"alert":   record.ID,
	}).Info("💬 Slack告警已发送（模拟）")
	return nil
}

// sendToSMS 发送短信告警
func (pam *PredictiveAlertManager) sendToSMS(channel *AlertChannel, record *AlertRecord, rule *AlertRule) error {
	// 这里应该集成短信服务
	pam.logger.WithFields(logrus.Fields{
		"channel": channel.Name,
		"alert":   record.ID,
	}).Info("📱 短信告警已发送（模拟）")
	return nil
}

// formatAlertMessage 格式化告警消息
func (pam *PredictiveAlertManager) formatAlertMessage(rule *AlertRule, insight *PredictiveInsight) string {
	if rule.Template.Body != "" {
		// 使用自定义模板
		return pam.applyTemplate(rule.Template.Body, insight)
	}

	// 使用默认格式
	return fmt.Sprintf("[%s] %s - %s (置信度: %.1f%%)",
		insight.Severity,
		insight.Title,
		insight.Description,
		insight.Confidence*100,
	)
}

// applyTemplate 应用模板
func (pam *PredictiveAlertManager) applyTemplate(template string, insight *PredictiveInsight) string {
	// 简单的模板替换
	message := template
	message = fmt.Sprintf(message, insight.Title, insight.Description, insight.Confidence*100)
	return message
}

// generateAlertID 生成告警ID
func (pam *PredictiveAlertManager) generateAlertID() string {
	return fmt.Sprintf("alert_%d", time.Now().UnixNano())
}

// initializeAlertRules 初始化告警规则
func (pam *PredictiveAlertManager) initializeAlertRules() {
	pam.alertRules = []AlertRule{
		{
			Name:     "critical_system_health",
			Type:     InsightTypePerformanceDegradation,
			Severity: SeverityCritical,
			Condition: func(insight *PredictiveInsight) bool {
				return insight.Severity == SeverityCritical
			},
			Channels: []string{"console", "email"},
			Cooldown: 30 * time.Minute,
			Enabled:  true,
			Template: AlertTemplate{
				Subject: "🚨 严重系统健康告警",
				Body:    "检测到严重的系统健康问题：%s - %s (置信度: %.1f%%)",
				Format:  "text",
			},
		},
		{
			Name:     "resource_exhaustion_warning",
			Type:     InsightTypeResourceExhaustion,
			Severity: SeverityHigh,
			Condition: func(insight *PredictiveInsight) bool {
				return insight.Severity >= SeverityHigh
			},
			Channels: []string{"console", "slack"},
			Cooldown: 15 * time.Minute,
			Enabled:  true,
			Template: AlertTemplate{
				Subject: "⚠️ 资源耗尽预警",
				Body:    "资源即将耗尽：%s - %s (置信度: %.1f%%)",
				Format:  "text",
			},
		},
		{
			Name:     "security_threat_alert",
			Type:     InsightTypeSecurityThreat,
			Severity: SeverityMedium,
			Condition: func(insight *PredictiveInsight) bool {
				return insight.Confidence > 0.8
			},
			Channels: []string{"console", "email", "sms"},
			Cooldown: 10 * time.Minute,
			Enabled:  true,
			Template: AlertTemplate{
				Subject: "🔒 安全威胁告警",
				Body:    "检测到安全威胁：%s - %s (置信度: %.1f%%)",
				Format:  "text",
			},
		},
	}
}

// initializeAlertChannels 初始化告警通道
func (pam *PredictiveAlertManager) initializeAlertChannels() {
	pam.channels["console"] = AlertChannel{
		Name:    "console",
		Type:    AlertChannelConsole,
		Enabled: true,
		Config:  map[string]interface{}{},
	}

	pam.channels["email"] = AlertChannel{
		Name:    "email",
		Type:    AlertChannelEmail,
		Enabled: false, // 默认禁用，需要配置
		Config: map[string]interface{}{
			"smtp_server": "smtp.example.com",
			"port":        587,
			"username":    "",
			"password":    "",
			"to":          []string{},
		},
	}

	pam.channels["slack"] = AlertChannel{
		Name:    "slack",
		Type:    AlertChannelSlack,
		Enabled: false, // 默认禁用，需要配置
		Config: map[string]interface{}{
			"webhook_url": "",
			"channel":     "#alerts",
			"username":    "AIOps Bot",
		},
	}

	pam.channels["sms"] = AlertChannel{
		Name:    "sms",
		Type:    AlertChannelSMS,
		Enabled: false, // 默认禁用，需要配置
		Config: map[string]interface{}{
			"provider": "",
			"api_key":  "",
			"numbers":  []string{},
		},
	}
}

// GetAlertHistory 获取告警历史
func (pam *PredictiveAlertManager) GetAlertHistory(limit int) []AlertRecord {
	if limit <= 0 || limit > len(pam.history) {
		return pam.history
	}

	// 返回最近的告警记录
	start := len(pam.history) - limit
	return pam.history[start:]
}

// GetAlertMetrics 获取告警指标
func (pam *PredictiveAlertManager) GetAlertMetrics() map[string]interface{} {
	totalAlerts := len(pam.history)
	sentAlerts := 0
	failedAlerts := 0

	for _, record := range pam.history {
		switch record.Status {
		case AlertStatusSent:
			sentAlerts++
		case AlertStatusFailed:
			failedAlerts++
		}
	}

	successRate := 0.0
	if totalAlerts > 0 {
		successRate = float64(sentAlerts) / float64(totalAlerts)
	}

	return map[string]interface{}{
		"total_alerts":  totalAlerts,
		"sent_alerts":   sentAlerts,
		"failed_alerts": failedAlerts,
		"success_rate":  successRate,
		"active_rules":  len(pam.alertRules),
		"active_channels": len(pam.channels),
	}
}
