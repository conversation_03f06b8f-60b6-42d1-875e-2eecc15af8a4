package observability

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// TracingSystem 全链路追踪系统
type TracingSystem struct {
	config *TracingConfig
	logger *logrus.Logger
	tracer *Tracer
	spans  map[string]*Span
	mutex  sync.RWMutex
}

// TracingConfig 追踪配置
type TracingConfig struct {
	ServiceName     string        `json:"service_name"`
	SamplingRate    float64       `json:"sampling_rate"`
	MaxSpans        int           `json:"max_spans"`
	RetentionPeriod time.Duration `json:"retention_period"`
	EnableMetrics   bool          `json:"enable_metrics"`
	EnableLogging   bool          `json:"enable_logging"`
}

// Tracer 追踪器
type Tracer struct {
	serviceName string
	logger      *logrus.Logger
	spans       map[string]*Span
	mutex       sync.RWMutex
}

// Span 追踪跨度
type Span struct {
	TraceID       string                 `json:"trace_id"`
	SpanID        string                 `json:"span_id"`
	ParentSpanID  string                 `json:"parent_span_id"`
	OperationName string                 `json:"operation_name"`
	ServiceName   string                 `json:"service_name"`
	StartTime     time.Time              `json:"start_time"`
	EndTime       time.Time              `json:"end_time"`
	Duration      time.Duration          `json:"duration"`
	Tags          map[string]interface{} `json:"tags"`
	Logs          []*LogEntry            `json:"logs"`
	Status        SpanStatus             `json:"status"`
	Error         error                  `json:"error,omitempty"`
	Children      []*Span                `json:"children"`
	mutex         sync.RWMutex           `json:"-"`
}

// SpanStatus 跨度状态
type SpanStatus string

const (
	SpanStatusOK     SpanStatus = "ok"
	SpanStatusError  SpanStatus = "error"
	SpanStatusCancel SpanStatus = "cancelled"
)

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time              `json:"timestamp"`
	Level     string                 `json:"level"`
	Message   string                 `json:"message"`
	Fields    map[string]interface{} `json:"fields"`
}

// TraceContext 追踪上下文
type TraceContext struct {
	TraceID      string
	SpanID       string
	ParentSpanID string
	Baggage      map[string]string
}

// NewTracingSystem 创建追踪系统
func NewTracingSystem(config *TracingConfig, logger *logrus.Logger) *TracingSystem {
	if config == nil {
		config = &TracingConfig{
			ServiceName:     "aiops-platform",
			SamplingRate:    1.0,
			MaxSpans:        10000,
			RetentionPeriod: 24 * time.Hour,
			EnableMetrics:   true,
			EnableLogging:   true,
		}
	}

	tracer := &Tracer{
		serviceName: config.ServiceName,
		logger:      logger,
		spans:       make(map[string]*Span),
	}

	ts := &TracingSystem{
		config: config,
		logger: logger,
		tracer: tracer,
		spans:  make(map[string]*Span),
	}

	// 启动清理任务
	go ts.startCleanupTask()

	logger.WithFields(logrus.Fields{
		"service_name":  config.ServiceName,
		"sampling_rate": config.SamplingRate,
		"max_spans":     config.MaxSpans,
	}).Info("Tracing system initialized")

	return ts
}

// StartSpan 开始新的跨度
func (ts *TracingSystem) StartSpan(ctx context.Context, operationName string) (*Span, context.Context) {
	// 检查采样率
	if !ts.shouldSample() {
		return nil, ctx
	}

	traceID := ts.generateTraceID()
	spanID := ts.generateSpanID()

	// 从上下文中获取父跨度信息
	var parentSpanID string
	if parentSpan := SpanFromContext(ctx); parentSpan != nil {
		parentSpanID = parentSpan.SpanID
		traceID = parentSpan.TraceID // 使用相同的TraceID
	}

	span := &Span{
		TraceID:       traceID,
		SpanID:        spanID,
		ParentSpanID:  parentSpanID,
		OperationName: operationName,
		ServiceName:   ts.config.ServiceName,
		StartTime:     time.Now(),
		Tags:          make(map[string]interface{}),
		Logs:          make([]*LogEntry, 0),
		Status:        SpanStatusOK,
		Children:      make([]*Span, 0),
	}

	// 存储跨度
	ts.mutex.Lock()
	ts.spans[spanID] = span
	ts.mutex.Unlock()

	// 将跨度添加到上下文
	newCtx := ContextWithSpan(ctx, span)

	ts.logger.WithFields(logrus.Fields{
		"trace_id":       traceID,
		"span_id":        spanID,
		"parent_span_id": parentSpanID,
		"operation":      operationName,
	}).Debug("Started new span")

	return span, newCtx
}

// FinishSpan 完成跨度
func (ts *TracingSystem) FinishSpan(span *Span) {
	if span == nil {
		return
	}

	span.mutex.Lock()
	span.EndTime = time.Now()
	span.Duration = span.EndTime.Sub(span.StartTime)
	span.mutex.Unlock()

	ts.logger.WithFields(logrus.Fields{
		"trace_id":  span.TraceID,
		"span_id":   span.SpanID,
		"operation": span.OperationName,
		"duration":  span.Duration.Milliseconds(),
		"status":    span.Status,
	}).Debug("Finished span")

	// 如果启用了指标收集，记录指标
	if ts.config.EnableMetrics {
		ts.recordSpanMetrics(span)
	}
}

// SetSpanTag 设置跨度标签
func (ts *TracingSystem) SetSpanTag(span *Span, key string, value interface{}) {
	if span == nil {
		return
	}

	span.mutex.Lock()
	span.Tags[key] = value
	span.mutex.Unlock()
}

// LogSpanEvent 记录跨度事件
func (ts *TracingSystem) LogSpanEvent(span *Span, level, message string, fields map[string]interface{}) {
	if span == nil {
		return
	}

	logEntry := &LogEntry{
		Timestamp: time.Now(),
		Level:     level,
		Message:   message,
		Fields:    fields,
	}

	span.mutex.Lock()
	span.Logs = append(span.Logs, logEntry)
	span.mutex.Unlock()

	if ts.config.EnableLogging {
		ts.logger.WithFields(logrus.Fields{
			"trace_id": span.TraceID,
			"span_id":  span.SpanID,
			"level":    level,
			"message":  message,
		}).Debug("Span event logged")
	}
}

// SetSpanError 设置跨度错误
func (ts *TracingSystem) SetSpanError(span *Span, err error) {
	if span == nil {
		return
	}

	span.mutex.Lock()
	span.Status = SpanStatusError
	span.Error = err
	span.Tags["error"] = true
	span.Tags["error.message"] = err.Error()
	span.mutex.Unlock()

	ts.LogSpanEvent(span, "error", err.Error(), map[string]interface{}{
		"error.type": fmt.Sprintf("%T", err),
	})
}

// GetTrace 获取完整的追踪信息
func (ts *TracingSystem) GetTrace(traceID string) *Trace {
	ts.mutex.RLock()
	defer ts.mutex.RUnlock()

	var rootSpan *Span
	spans := make([]*Span, 0)

	// 收集所有相关的跨度
	for _, span := range ts.spans {
		if span.TraceID == traceID {
			spans = append(spans, span)
			if span.ParentSpanID == "" {
				rootSpan = span
			}
		}
	}

	if rootSpan == nil && len(spans) > 0 {
		rootSpan = spans[0]
	}

	if rootSpan == nil {
		return nil
	}

	// 构建追踪树
	trace := &Trace{
		TraceID:   traceID,
		RootSpan:  rootSpan,
		Spans:     spans,
		StartTime: rootSpan.StartTime,
		Duration:  time.Since(rootSpan.StartTime),
	}

	// 计算总持续时间
	var endTime time.Time
	for _, span := range spans {
		if !span.EndTime.IsZero() && span.EndTime.After(endTime) {
			endTime = span.EndTime
		}
	}
	if !endTime.IsZero() {
		trace.Duration = endTime.Sub(trace.StartTime)
	}

	return trace
}

// Trace 完整的追踪信息
type Trace struct {
	TraceID   string        `json:"trace_id"`
	RootSpan  *Span         `json:"root_span"`
	Spans     []*Span       `json:"spans"`
	StartTime time.Time     `json:"start_time"`
	Duration  time.Duration `json:"duration"`
}

// shouldSample 检查是否应该采样
func (ts *TracingSystem) shouldSample() bool {
	// 简化的采样逻辑
	return ts.config.SamplingRate >= 1.0
}

// generateTraceID 生成追踪ID
func (ts *TracingSystem) generateTraceID() string {
	return fmt.Sprintf("trace_%d_%d", time.Now().UnixNano(), time.Now().Unix())
}

// generateSpanID 生成跨度ID
func (ts *TracingSystem) generateSpanID() string {
	return fmt.Sprintf("span_%d_%d", time.Now().UnixNano(), time.Now().Unix())
}

// recordSpanMetrics 记录跨度指标
func (ts *TracingSystem) recordSpanMetrics(span *Span) {
	// 这里可以集成到指标系统
	ts.logger.WithFields(logrus.Fields{
		"operation":    span.OperationName,
		"duration_ms":  span.Duration.Milliseconds(),
		"status":       span.Status,
		"service_name": span.ServiceName,
	}).Debug("Span metrics recorded")
}

// startCleanupTask 启动清理任务
func (ts *TracingSystem) startCleanupTask() {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		ts.cleanup()
	}
}

// cleanup 清理过期的跨度
func (ts *TracingSystem) cleanup() {
	ts.mutex.Lock()
	defer ts.mutex.Unlock()

	cutoff := time.Now().Add(-ts.config.RetentionPeriod)
	toDelete := make([]string, 0)

	for spanID, span := range ts.spans {
		if span.StartTime.Before(cutoff) {
			toDelete = append(toDelete, spanID)
		}
	}

	for _, spanID := range toDelete {
		delete(ts.spans, spanID)
	}

	ts.logger.WithField("cleaned_spans", len(toDelete)).Debug("Span cleanup completed")
}

// GetStats 获取追踪统计
func (ts *TracingSystem) GetStats() map[string]interface{} {
	ts.mutex.RLock()
	defer ts.mutex.RUnlock()

	totalSpans := len(ts.spans)
	errorSpans := 0

	for _, span := range ts.spans {
		if span.Status == SpanStatusError {
			errorSpans++
		}
	}

	return map[string]interface{}{
		"total_spans":   totalSpans,
		"error_spans":   errorSpans,
		"success_rate":  float64(totalSpans-errorSpans) / float64(totalSpans) * 100,
		"service_name":  ts.config.ServiceName,
		"sampling_rate": ts.config.SamplingRate,
		"last_updated":  time.Now(),
	}
}

// 上下文相关的辅助函数

type spanContextKey struct{}

// ContextWithSpan 将跨度添加到上下文
func ContextWithSpan(ctx context.Context, span *Span) context.Context {
	return context.WithValue(ctx, spanContextKey{}, span)
}

// SpanFromContext 从上下文中获取跨度
func SpanFromContext(ctx context.Context) *Span {
	if span, ok := ctx.Value(spanContextKey{}).(*Span); ok {
		return span
	}
	return nil
}

// StartSpanFromContext 从上下文开始新跨度
func StartSpanFromContext(ctx context.Context, ts *TracingSystem, operationName string) (*Span, context.Context) {
	return ts.StartSpan(ctx, operationName)
}

// MiddlewareTracing 追踪中间件
func (ts *TracingSystem) MiddlewareTracing(operationName string) func(next func()) {
	return func(next func()) {
		ctx := context.Background()
		span, ctx := ts.StartSpan(ctx, operationName)
		defer ts.FinishSpan(span)

		// 设置基本标签
		ts.SetSpanTag(span, "component", "middleware")
		ts.SetSpanTag(span, "service.name", ts.tracer.serviceName)

		next()
	}
}
