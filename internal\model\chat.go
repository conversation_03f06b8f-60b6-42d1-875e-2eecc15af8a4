package model

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// ChatSession 对话会话模型
type ChatSession struct {
	ID           int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	SessionID    string         `json:"session_id" gorm:"uniqueIndex;size:36;not null"`
	UserID       int64          `json:"user_id" gorm:"not null"`
	Title        string         `json:"title" gorm:"size:200"`
	StartedAt    time.Time      `json:"started_at" gorm:"not null"`
	EndedAt      *time.Time     `json:"ended_at"`
	Status       string         `json:"status" gorm:"size:20;not null;default:active"`
	ContextData  string         `json:"-" gorm:"type:text"`
	MessageCount int            `json:"message_count" gorm:"default:0"`
	TokenUsage   int            `json:"token_usage" gorm:"default:0"`
	LastActivity time.Time      `json:"last_activity" gorm:"not null"`
	ClientInfo   string         `json:"-" gorm:"type:text"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联（暂时注释外键约束）
	// User     User          `json:"user" gorm:"foreignKey:UserID"`
	Messages []ChatMessage `json:"messages,omitempty" gorm:"foreignKey:SessionID"`
}

// TableName 指定表名
func (ChatSession) TableName() string {
	return "chat_sessions"
}

// BeforeCreate GORM钩子：创建前
func (cs *ChatSession) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if cs.StartedAt.IsZero() {
		cs.StartedAt = now
	}
	cs.LastActivity = now
	return nil
}

// BeforeUpdate GORM钩子：更新前
func (cs *ChatSession) BeforeUpdate(tx *gorm.DB) error {
	cs.LastActivity = time.Now()
	return nil
}

// GetContextData 获取上下文数据
func (cs *ChatSession) GetContextData() map[string]interface{} {
	if cs.ContextData == "" {
		return make(map[string]interface{})
	}

	var contextData map[string]interface{}
	if err := json.Unmarshal([]byte(cs.ContextData), &contextData); err != nil {
		return make(map[string]interface{})
	}
	return contextData
}

// SetContextData 设置上下文数据
func (cs *ChatSession) SetContextData(contextData map[string]interface{}) error {
	if contextData == nil {
		contextData = make(map[string]interface{})
	}

	data, err := json.Marshal(contextData)
	if err != nil {
		return err
	}
	cs.ContextData = string(data)
	return nil
}

// GetClientInfo 获取客户端信息
func (cs *ChatSession) GetClientInfo() map[string]interface{} {
	if cs.ClientInfo == "" {
		return make(map[string]interface{})
	}

	var clientInfo map[string]interface{}
	if err := json.Unmarshal([]byte(cs.ClientInfo), &clientInfo); err != nil {
		return make(map[string]interface{})
	}
	return clientInfo
}

// SetClientInfo 设置客户端信息
func (cs *ChatSession) SetClientInfo(clientInfo map[string]interface{}) error {
	if clientInfo == nil {
		clientInfo = make(map[string]interface{})
	}

	data, err := json.Marshal(clientInfo)
	if err != nil {
		return err
	}
	cs.ClientInfo = string(data)
	return nil
}

// IsActive 检查会话是否活跃
func (cs *ChatSession) IsActive() bool {
	return cs.Status == string(SessionStatusActive)
}

// End 结束会话
func (cs *ChatSession) End() {
	now := time.Now()
	cs.Status = string(SessionStatusEnded)
	cs.EndedAt = &now
}

// ChatMessage 对话消息模型
type ChatMessage struct {
	ID               int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	SessionID        int64     `json:"session_id" gorm:"not null"`
	MessageType      string    `json:"message_type" gorm:"size:20;not null"`
	Content          string    `json:"content" gorm:"type:text;not null"`
	AIResponse       string    `json:"ai_response" gorm:"type:text"`
	CreatedAt        time.Time `json:"created_at" gorm:"not null"`
	Intent           string    `json:"intent" gorm:"size:50"`
	ExtractedParams  string    `json:"-" gorm:"type:text"`
	ToolCalls        string    `json:"-" gorm:"type:text"`
	ToolResults      string    `json:"-" gorm:"type:text"`
	TokenCount       int       `json:"token_count"`
	ProcessingTimeMs int       `json:"processing_time_ms"`
	ErrorMessage     string    `json:"error_message" gorm:"type:text"`

	// 关联（暂时注释外键约束）
	// Session ChatSession `json:"-" gorm:"foreignKey:SessionID"`
}

// TableName 指定表名
func (ChatMessage) TableName() string {
	return "chat_messages"
}

// BeforeCreate GORM钩子：创建前
func (cm *ChatMessage) BeforeCreate(tx *gorm.DB) error {
	if cm.CreatedAt.IsZero() {
		cm.CreatedAt = time.Now()
	}
	return nil
}

// GetExtractedParams 获取提取的参数
func (cm *ChatMessage) GetExtractedParams() map[string]interface{} {
	if cm.ExtractedParams == "" {
		return make(map[string]interface{})
	}

	var params map[string]interface{}
	if err := json.Unmarshal([]byte(cm.ExtractedParams), &params); err != nil {
		return make(map[string]interface{})
	}
	return params
}

// SetExtractedParams 设置提取的参数
func (cm *ChatMessage) SetExtractedParams(params map[string]interface{}) error {
	if params == nil {
		params = make(map[string]interface{})
	}

	data, err := json.Marshal(params)
	if err != nil {
		return err
	}
	cm.ExtractedParams = string(data)
	return nil
}

// GetToolCalls 获取工具调用
func (cm *ChatMessage) GetToolCalls() []map[string]interface{} {
	if cm.ToolCalls == "" {
		return []map[string]interface{}{}
	}

	var toolCalls []map[string]interface{}
	if err := json.Unmarshal([]byte(cm.ToolCalls), &toolCalls); err != nil {
		return []map[string]interface{}{}
	}
	return toolCalls
}

// SetToolCalls 设置工具调用
func (cm *ChatMessage) SetToolCalls(toolCalls []map[string]interface{}) error {
	if toolCalls == nil {
		toolCalls = []map[string]interface{}{}
	}

	data, err := json.Marshal(toolCalls)
	if err != nil {
		return err
	}
	cm.ToolCalls = string(data)
	return nil
}

// GetToolResults 获取工具执行结果
func (cm *ChatMessage) GetToolResults() []map[string]interface{} {
	if cm.ToolResults == "" {
		return []map[string]interface{}{}
	}

	var toolResults []map[string]interface{}
	if err := json.Unmarshal([]byte(cm.ToolResults), &toolResults); err != nil {
		return []map[string]interface{}{}
	}
	return toolResults
}

// SetToolResults 设置工具执行结果
func (cm *ChatMessage) SetToolResults(toolResults []map[string]interface{}) error {
	if toolResults == nil {
		toolResults = []map[string]interface{}{}
	}

	data, err := json.Marshal(toolResults)
	if err != nil {
		return err
	}
	cm.ToolResults = string(data)
	return nil
}

// SessionStatus 会话状态枚举
type SessionStatus string

const (
	SessionStatusActive   SessionStatus = "active"
	SessionStatusEnded    SessionStatus = "ended"
	SessionStatusArchived SessionStatus = "archived"
)

// MessageType 消息类型枚举
type MessageType string

const (
	MessageTypeUser      MessageType = "user"
	MessageTypeAssistant MessageType = "assistant"
	MessageTypeSystem    MessageType = "system"
	MessageTypeTool      MessageType = "tool"
)

// IsValidSessionStatus 检查会话状态是否有效
func IsValidSessionStatus(status string) bool {
	switch SessionStatus(status) {
	case SessionStatusActive, SessionStatusEnded, SessionStatusArchived:
		return true
	default:
		return false
	}
}

// IsValidMessageType 检查消息类型是否有效
func IsValidMessageType(msgType string) bool {
	switch MessageType(msgType) {
	case MessageTypeUser, MessageTypeAssistant, MessageTypeSystem, MessageTypeTool:
		return true
	default:
		return false
	}
}

// ChatSessionCreateRequest 创建会话请求
type ChatSessionCreateRequest struct {
	Title string `json:"title" binding:"omitempty,max=200"`
}

// ChatMessageRequest 发送消息请求
type ChatMessageRequest struct {
	SessionID string `json:"session_id" binding:"required"`
	Content   string `json:"content" binding:"required,min=1"`
	Stream    bool   `json:"stream" binding:"omitempty"`
}

// ChatSessionResponse 会话响应
type ChatSessionResponse struct {
	ID           int64                  `json:"id"`
	SessionID    string                 `json:"session_id"`
	UserID       int64                  `json:"user_id"`
	Title        string                 `json:"title"`
	StartedAt    time.Time              `json:"started_at"`
	EndedAt      *time.Time             `json:"ended_at"`
	Status       string                 `json:"status"`
	ContextData  map[string]interface{} `json:"context_data"`
	MessageCount int                    `json:"message_count"`
	TokenUsage   int                    `json:"token_usage"`
	LastActivity time.Time              `json:"last_activity"`
	User         *UserResponse          `json:"user,omitempty"`
	Messages     []*ChatMessageResponse `json:"messages,omitempty"`
}

// ToResponse 转换为响应格式
func (cs *ChatSession) ToResponse() *ChatSessionResponse {
	resp := &ChatSessionResponse{
		ID:           cs.ID,
		SessionID:    cs.SessionID,
		UserID:       cs.UserID,
		Title:        cs.Title,
		StartedAt:    cs.StartedAt,
		EndedAt:      cs.EndedAt,
		Status:       cs.Status,
		ContextData:  cs.GetContextData(),
		MessageCount: cs.MessageCount,
		TokenUsage:   cs.TokenUsage,
		LastActivity: cs.LastActivity,
	}

	// TODO: 暂时注释用户信息，后续恢复
	// if cs.User.ID != 0 {
	// 	resp.User = cs.User.ToResponse()
	// }

	if len(cs.Messages) > 0 {
		resp.Messages = make([]*ChatMessageResponse, len(cs.Messages))
		for i, msg := range cs.Messages {
			resp.Messages[i] = msg.ToResponse()
		}
	}

	return resp
}

// ChatMessageResponse 消息响应
type ChatMessageResponse struct {
	ID               int64                    `json:"id"`
	SessionID        int64                    `json:"session_id"`
	MessageType      string                   `json:"message_type"`
	Content          string                   `json:"content"`
	AIResponse       string                   `json:"ai_response"`
	CreatedAt        time.Time                `json:"created_at"`
	Intent           string                   `json:"intent"`
	ExtractedParams  map[string]interface{}   `json:"extracted_params"`
	ToolCalls        []map[string]interface{} `json:"tool_calls"`
	ToolResults      []map[string]interface{} `json:"tool_results"`
	TokenCount       int                      `json:"token_count"`
	ProcessingTimeMs int                      `json:"processing_time_ms"`
	ErrorMessage     string                   `json:"error_message"`
}

// ToResponse 转换为响应格式
func (cm *ChatMessage) ToResponse() *ChatMessageResponse {
	return &ChatMessageResponse{
		ID:               cm.ID,
		SessionID:        cm.SessionID,
		MessageType:      cm.MessageType,
		Content:          cm.Content,
		AIResponse:       cm.AIResponse,
		CreatedAt:        cm.CreatedAt,
		Intent:           cm.Intent,
		ExtractedParams:  cm.GetExtractedParams(),
		ToolCalls:        cm.GetToolCalls(),
		ToolResults:      cm.GetToolResults(),
		TokenCount:       cm.TokenCount,
		ProcessingTimeMs: cm.ProcessingTimeMs,
		ErrorMessage:     cm.ErrorMessage,
	}
}

// ChatSessionListQuery 会话列表查询参数
type ChatSessionListQuery struct {
	Page   int    `form:"page,default=1" binding:"min=1"`
	Limit  int    `form:"limit,default=20" binding:"min=1,max=100"`
	Status string `form:"status" binding:"omitempty,oneof=active ended archived"`
	Search string `form:"search" binding:"omitempty,max=100"`
}

// ChatSessionListResponse 会话列表响应
type ChatSessionListResponse struct {
	Sessions   []*ChatSessionResponse `json:"sessions"`
	Pagination *Pagination            `json:"pagination"`
}

// CommandExecution 命令执行记录模型
type CommandExecution struct {
	ID          uint           `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID      int64          `json:"user_id" gorm:"not null"`
	SessionID   string         `json:"session_id" gorm:"size:36"`
	Command     string         `json:"command" gorm:"size:500;not null"`
	Args        string         `json:"args" gorm:"type:text"`
	Status      string         `json:"status" gorm:"size:20;not null;default:pending"`
	ExitCode    int            `json:"exit_code" gorm:"default:0"`
	Stdout      string         `json:"stdout" gorm:"type:text"`
	Stderr      string         `json:"stderr" gorm:"type:text"`
	Error       string         `json:"error" gorm:"type:text"`
	Duration    int64          `json:"duration" gorm:"default:0"` // 毫秒
	RequestedAt time.Time      `json:"requested_at" gorm:"not null"`
	CompletedAt time.Time      `json:"completed_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
}

// TableName 指定表名
func (CommandExecution) TableName() string {
	return "command_executions"
}

// BeforeCreate GORM钩子：创建前
func (ce *CommandExecution) BeforeCreate(tx *gorm.DB) error {
	if ce.RequestedAt.IsZero() {
		ce.RequestedAt = time.Now()
	}
	return nil
}
