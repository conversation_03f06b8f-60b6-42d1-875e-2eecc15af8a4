package agent

import (
	"context"
	"os"
	"os/user"
	"runtime"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// ContextAnalyzer 上下文分析器
type ContextAnalyzer struct {
	logger *logrus.Logger
}

// NewContextAnalyzer 创建上下文分析器
func NewContextAnalyzer(logger *logrus.Logger) *ContextAnalyzer {
	return &ContextAnalyzer{
		logger: logger,
	}
}

// Analyze 分析执行上下文
func (ca *ContextAnalyzer) Analyze(ctx context.Context, assessmentContext *AssessmentContext) *ContextAnalysis {
	analysis := &ContextAnalysis{
		Metadata: make(map[string]interface{}),
	}

	// 分析工作目录
	analysis.WorkingDirectory = ca.analyzeWorkingDirectory(assessmentContext)

	// 分析用户上下文
	analysis.UserContext = ca.analyzeUserContext(assessmentContext)

	// 分析时间上下文
	analysis.TimeContext = ca.analyzeTimeContext()

	// 分析系统负载
	analysis.SystemLoad = ca.analyzeSystemLoad(assessmentContext)

	// 分析最近活动
	analysis.RecentActivity = ca.analyzeRecentActivity(assessmentContext)

	// 分析环境风险
	analysis.EnvironmentRisk = ca.analyzeEnvironmentRisk(assessmentContext)

	// 分析依赖关系
	analysis.Dependencies = ca.analyzeDependencies(assessmentContext)

	return analysis
}

// analyzeWorkingDirectory 分析工作目录
func (ca *ContextAnalyzer) analyzeWorkingDirectory(assessmentContext *AssessmentContext) string {
	if assessmentContext != nil && assessmentContext.WorkingDir != "" {
		return assessmentContext.WorkingDir
	}

	// 获取当前工作目录
	if wd, err := os.Getwd(); err == nil {
		return wd
	}

	return "/unknown"
}

// analyzeUserContext 分析用户上下文
func (ca *ContextAnalyzer) analyzeUserContext(assessmentContext *AssessmentContext) string {
	// 检查是否为root用户
	if ca.isRootUser() {
		return "root"
	}

	// 检查用户权限
	if assessmentContext != nil && assessmentContext.UserPermissions != nil {
		if assessmentContext.UserPermissions.IsAdmin || assessmentContext.UserPermissions.IsSudo {
			return "privileged"
		}
	}

	return "normal"
}

// analyzeTimeContext 分析时间上下文
func (ca *ContextAnalyzer) analyzeTimeContext() string {
	now := time.Now()
	hour := now.Hour()

	// 判断是否为工作时间 (9:00-18:00)
	if hour >= 9 && hour < 18 {
		return "business_hours"
	}

	// 判断是否为维护时间窗口 (2:00-6:00)
	if hour >= 2 && hour < 6 {
		return "maintenance"
	}

	return "off_hours"
}

// analyzeSystemLoad 分析系统负载
func (ca *ContextAnalyzer) analyzeSystemLoad(assessmentContext *AssessmentContext) string {
	if assessmentContext != nil && assessmentContext.SystemState != nil {
		state := assessmentContext.SystemState

		// 基于CPU和内存使用率判断负载
		if state.CPUUsage > 80 || state.MemoryUsage > 90 {
			return "high"
		} else if state.CPUUsage > 50 || state.MemoryUsage > 70 {
			return "medium"
		}
	}

	return "low"
}

// analyzeRecentActivity 分析最近活动
func (ca *ContextAnalyzer) analyzeRecentActivity(assessmentContext *AssessmentContext) []string {
	activities := make([]string, 0)

	if assessmentContext != nil && len(assessmentContext.PreviousCommands) > 0 {
		// 分析最近的命令
		for _, cmd := range assessmentContext.PreviousCommands {
			if ca.isSignificantCommand(cmd) {
				activities = append(activities, cmd)
			}
		}
	}

	return activities
}

// analyzeEnvironmentRisk 分析环境风险
func (ca *ContextAnalyzer) analyzeEnvironmentRisk(assessmentContext *AssessmentContext) string {
	if assessmentContext != nil && assessmentContext.Environment != nil {
		// 检查环境变量
		if env, exists := assessmentContext.Environment["ENVIRONMENT"]; exists {
			switch strings.ToLower(env) {
			case "production", "prod":
				return "production"
			case "staging", "stage":
				return "staging"
			case "development", "dev":
				return "development"
			}
		}

		// 检查其他环境指标
		if _, exists := assessmentContext.Environment["PROD_MODE"]; exists {
			return "production"
		}
	}

	// 基于主机名判断
	hostname, _ := os.Hostname()
	hostname = strings.ToLower(hostname)

	if strings.Contains(hostname, "prod") || strings.Contains(hostname, "production") {
		return "production"
	} else if strings.Contains(hostname, "stage") || strings.Contains(hostname, "staging") {
		return "staging"
	} else if strings.Contains(hostname, "dev") || strings.Contains(hostname, "development") {
		return "development"
	}

	return "unknown"
}

// analyzeDependencies 分析依赖关系
func (ca *ContextAnalyzer) analyzeDependencies(assessmentContext *AssessmentContext) []string {
	dependencies := make([]string, 0)

	if assessmentContext != nil && assessmentContext.SystemState != nil {
		// 分析运行中的服务
		for service, status := range assessmentContext.SystemState.Services {
			if status == "running" {
				dependencies = append(dependencies, service)
			}
		}
	}

	return dependencies
}

// isRootUser 检查是否为root用户
func (ca *ContextAnalyzer) isRootUser() bool {
	if runtime.GOOS == "windows" {
		// Windows系统检查
		return false // 简化实现
	}

	// Unix/Linux系统检查
	currentUser, err := user.Current()
	if err != nil {
		return false
	}

	return currentUser.Uid == "0"
}

// isSignificantCommand 检查是否为重要命令
func (ca *ContextAnalyzer) isSignificantCommand(command string) bool {
	significantCommands := []string{
		"rm", "rmdir", "dd", "mkfs", "fdisk", "format",
		"kill", "killall", "shutdown", "reboot", "halt",
		"chmod", "chown", "su", "sudo", "systemctl",
		"service", "mount", "umount", "iptables",
	}

	for _, cmd := range significantCommands {
		if strings.HasPrefix(command, cmd) {
			return true
		}
	}

	return false
}
