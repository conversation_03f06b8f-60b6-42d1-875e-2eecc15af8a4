/* ========================================
   对话历史管理JavaScript
   提供完整的对话管理功能
   ======================================== */

class ChatHistoryManager {
    constructor() {
        this.conversations = [];
        this.currentFilter = 'all';
        this.searchQuery = '';
        this.groups = {
            today: { title: '今天', conversations: [] },
            yesterday: { title: '昨天', conversations: [] },
            thisWeek: { title: '本周', conversations: [] },
            thisMonth: { title: '本月', conversations: [] },
            older: { title: '更早', conversations: [] },
            pinned: { title: '置顶对话', conversations: [] },
            archived: { title: '已归档', conversations: [] }
        };
        
        this.init();
    }
    
    init() {
        this.loadConversations();
        this.bindEvents();
        this.renderChatList();
    }
    
    bindEvents() {
        // 搜索事件
        const searchInput = document.getElementById('chat-search-input');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.searchQuery = e.target.value;
                this.renderChatList();
            });
        }
        
        // 过滤器事件
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('filter-btn')) {
                this.handleFilterClick(e.target);
            }
        });
        
        // 对话项点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.chat-item')) {
                this.handleChatItemClick(e.target.closest('.chat-item'));
            }
        });
        
        // 对话操作事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.chat-action-btn')) {
                e.stopPropagation();
                this.handleChatAction(e.target.closest('.chat-action-btn'));
            }
        });
        
        // 分组折叠事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.chat-group-header')) {
                this.toggleGroup(e.target.closest('.chat-group-header'));
            }
        });
    }
    
    loadConversations() {
        // 从localStorage加载对话历史
        const saved = localStorage.getItem('chatConversations');
        if (saved) {
            this.conversations = JSON.parse(saved);
        } else {
            // 创建示例对话
            this.conversations = this.createSampleConversations();
            this.saveConversations();
        }
        
        this.organizeConversations();
    }
    
    createSampleConversations() {
        const now = new Date();
        const today = new Date(now);
        const yesterday = new Date(now.getTime() - 24 * 60 * 60 * 1000);
        const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        return [
            {
                id: 'conv_1',
                title: '系统状态检查',
                preview: '查看所有主机状态',
                messageCount: 8,
                lastMessage: today,
                tags: ['系统', '监控'],
                pinned: true,
                archived: false,
                messages: []
            },
            {
                id: 'conv_2',
                title: '数据库性能优化',
                preview: '分析数据库查询性能',
                messageCount: 15,
                lastMessage: yesterday,
                tags: ['数据库', '性能'],
                pinned: false,
                archived: false,
                messages: []
            },
            {
                id: 'conv_3',
                title: '服务器部署问题',
                preview: '解决nginx配置问题',
                messageCount: 12,
                lastMessage: lastWeek,
                tags: ['部署', '错误'],
                pinned: false,
                archived: false,
                messages: []
            }
        ];
    }
    
    organizeConversations() {
        // 重置分组
        Object.keys(this.groups).forEach(key => {
            this.groups[key].conversations = [];
        });
        
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thisMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        this.conversations.forEach(conv => {
            const lastMessage = new Date(conv.lastMessage);
            
            if (conv.pinned) {
                this.groups.pinned.conversations.push(conv);
            } else if (conv.archived) {
                this.groups.archived.conversations.push(conv);
            } else if (lastMessage >= today) {
                this.groups.today.conversations.push(conv);
            } else if (lastMessage >= yesterday) {
                this.groups.yesterday.conversations.push(conv);
            } else if (lastMessage >= thisWeek) {
                this.groups.thisWeek.conversations.push(conv);
            } else if (lastMessage >= thisMonth) {
                this.groups.thisMonth.conversations.push(conv);
            } else {
                this.groups.older.conversations.push(conv);
            }
        });
        
        // 按时间排序
        Object.keys(this.groups).forEach(key => {
            this.groups[key].conversations.sort((a, b) => 
                new Date(b.lastMessage) - new Date(a.lastMessage)
            );
        });
    }
    
    renderChatList() {
        const chatList = document.getElementById('chat-list');
        if (!chatList) return;

        // 过滤对话
        const filteredConversations = this.filterConversations();
        
        if (filteredConversations.length === 0) {
            this.renderEmptyState(chatList);
            return;
        }
        
        // 重新组织过滤后的对话
        const filteredGroups = this.organizeFilteredConversations(filteredConversations);
        
        // 渲染分组
        const groupsHtml = Object.keys(filteredGroups)
            .filter(key => filteredGroups[key].conversations.length > 0)
            .map(key => this.renderGroup(key, filteredGroups[key]))
            .join('');
        
        chatList.innerHTML = `
            <div class="chat-filters">
                <button class="filter-btn ${this.currentFilter === 'all' ? 'active' : ''}" data-filter="all">全部</button>
                <button class="filter-btn ${this.currentFilter === 'pinned' ? 'active' : ''}" data-filter="pinned">置顶</button>
                <button class="filter-btn ${this.currentFilter === 'archived' ? 'active' : ''}" data-filter="archived">归档</button>
                <button class="filter-btn ${this.currentFilter === 'tagged' ? 'active' : ''}" data-filter="tagged">标签</button>
            </div>
            ${groupsHtml}
        `;
    }
    
    renderGroup(groupKey, group) {
        if (group.conversations.length === 0) return '';
        
        const isCollapsed = localStorage.getItem(`group_${groupKey}_collapsed`) === 'true';
        
        return `
            <div class="chat-group ${isCollapsed ? 'collapsed' : ''}" data-group="${groupKey}">
                <div class="chat-group-header">
                    <div class="chat-group-title">
                        <i class="bi bi-chevron-down chat-group-toggle"></i>
                        <span>${group.title}</span>
                    </div>
                    <span class="chat-group-count">${group.conversations.length}</span>
                </div>
                <div class="chat-group-content" style="max-height: ${isCollapsed ? '0' : 'none'}">
                    ${group.conversations.map(conv => this.renderChatItem(conv)).join('')}
                </div>
            </div>
        `;
    }
    
    renderChatItem(conversation) {
        const isActive = conversation.id === this.getCurrentConversationId();
        const highlightedTitle = this.highlightSearchText(conversation.title);
        const highlightedPreview = this.highlightSearchText(conversation.preview);
        
        return `
            <div class="chat-item ${isActive ? 'active' : ''} ${conversation.pinned ? 'pinned' : ''}" 
                 data-conversation-id="${conversation.id}">
                <div class="chat-icon ${conversation.archived ? 'archived' : ''}">
                    <i class="bi bi-chat-dots"></i>
                </div>
                <div class="chat-content">
                    <div class="chat-title">${highlightedTitle}</div>
                    <div class="chat-preview">${highlightedPreview}</div>
                    <div class="chat-meta">
                        <span class="chat-time">${this.formatTime(conversation.lastMessage)}</span>
                        <span class="chat-count">${conversation.messageCount}条</span>
                    </div>
                    ${conversation.tags.length > 0 ? `
                    <div class="chat-tags">
                        ${conversation.tags.map(tag => `<span class="chat-tag">${tag}</span>`).join('')}
                    </div>
                    ` : ''}
                </div>
                <div class="chat-actions">
                    <button class="chat-action-btn pin ${conversation.pinned ? 'active' : ''}" 
                            data-action="pin" title="${conversation.pinned ? '取消置顶' : '置顶'}">
                        <i class="bi bi-pin${conversation.pinned ? '-fill' : ''}"></i>
                    </button>
                    <button class="chat-action-btn archive ${conversation.archived ? 'active' : ''}" 
                            data-action="archive" title="${conversation.archived ? '取消归档' : '归档'}">
                        <i class="bi bi-archive${conversation.archived ? '-fill' : ''}"></i>
                    </button>
                    <button class="chat-action-btn export" data-action="export" title="导出对话">
                        <i class="bi bi-download"></i>
                    </button>
                    <button class="chat-action-btn delete" data-action="delete" title="删除对话">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </div>
        `;
    }
    
    renderEmptyState(container) {
        container.innerHTML = `
            <div class="chat-list-empty">
                <div class="empty-icon">💬</div>
                <div class="empty-title">暂无对话</div>
                <div class="empty-description">开始新的对话来体验AI运维助手</div>
            </div>
        `;
    }
    
    filterConversations() {
        let filtered = this.conversations;
        
        // 按过滤器过滤
        if (this.currentFilter !== 'all') {
            switch (this.currentFilter) {
                case 'pinned':
                    filtered = filtered.filter(conv => conv.pinned);
                    break;
                case 'archived':
                    filtered = filtered.filter(conv => conv.archived);
                    break;
                case 'tagged':
                    filtered = filtered.filter(conv => conv.tags.length > 0);
                    break;
            }
        }
        
        // 按搜索查询过滤
        if (this.searchQuery) {
            const query = this.searchQuery.toLowerCase();
            filtered = filtered.filter(conv => 
                conv.title.toLowerCase().includes(query) ||
                conv.preview.toLowerCase().includes(query) ||
                conv.tags.some(tag => tag.toLowerCase().includes(query))
            );
        }
        
        return filtered;
    }
    
    organizeFilteredConversations(conversations) {
        const groups = {
            pinned: { title: '置顶对话', conversations: [] },
            today: { title: '今天', conversations: [] },
            yesterday: { title: '昨天', conversations: [] },
            thisWeek: { title: '本周', conversations: [] },
            thisMonth: { title: '本月', conversations: [] },
            older: { title: '更早', conversations: [] },
            archived: { title: '已归档', conversations: [] }
        };
        
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
        const thisWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
        const thisMonth = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
        
        conversations.forEach(conv => {
            const lastMessage = new Date(conv.lastMessage);
            
            if (conv.pinned && this.currentFilter !== 'archived') {
                groups.pinned.conversations.push(conv);
            } else if (conv.archived) {
                groups.archived.conversations.push(conv);
            } else if (lastMessage >= today) {
                groups.today.conversations.push(conv);
            } else if (lastMessage >= yesterday) {
                groups.yesterday.conversations.push(conv);
            } else if (lastMessage >= thisWeek) {
                groups.thisWeek.conversations.push(conv);
            } else if (lastMessage >= thisMonth) {
                groups.thisMonth.conversations.push(conv);
            } else {
                groups.older.conversations.push(conv);
            }
        });
        
        return groups;
    }
    
    // 事件处理方法
    handleFilterClick(button) {
        document.querySelectorAll('.filter-btn').forEach(btn => btn.classList.remove('active'));
        button.classList.add('active');
        this.currentFilter = button.dataset.filter;
        this.renderChatList();
    }

    handleChatItemClick(chatItem) {
        const conversationId = chatItem.dataset.conversationId;
        this.loadConversation(conversationId);
    }

    handleChatAction(button) {
        const action = button.dataset.action;
        const chatItem = button.closest('.chat-item');
        const conversationId = chatItem.dataset.conversationId;

        switch (action) {
            case 'pin':
                this.togglePin(conversationId);
                break;
            case 'archive':
                this.toggleArchive(conversationId);
                break;
            case 'export':
                this.exportConversation(conversationId);
                break;
            case 'delete':
                this.deleteConversation(conversationId);
                break;
        }
    }

    toggleGroup(header) {
        const group = header.closest('.chat-group');
        const groupKey = group.dataset.group;
        const isCollapsed = group.classList.contains('collapsed');

        group.classList.toggle('collapsed');

        const content = group.querySelector('.chat-group-content');
        if (isCollapsed) {
            content.style.maxHeight = content.scrollHeight + 'px';
        } else {
            content.style.maxHeight = '0';
        }

        localStorage.setItem(`group_${groupKey}_collapsed`, !isCollapsed);
    }

    // 对话操作方法
    togglePin(conversationId) {
        const conversation = this.conversations.find(conv => conv.id === conversationId);
        if (conversation) {
            conversation.pinned = !conversation.pinned;
            this.saveConversations();
            this.organizeConversations();
            this.renderChatList();
        }
    }

    toggleArchive(conversationId) {
        const conversation = this.conversations.find(conv => conv.id === conversationId);
        if (conversation) {
            conversation.archived = !conversation.archived;
            if (conversation.archived) {
                conversation.pinned = false; // 归档时取消置顶
            }
            this.saveConversations();
            this.organizeConversations();
            this.renderChatList();
        }
    }

    deleteConversation(conversationId) {
        if (confirm('确定要删除这个对话吗？此操作无法撤销。')) {
            this.conversations = this.conversations.filter(conv => conv.id !== conversationId);
            this.saveConversations();
            this.organizeConversations();
            this.renderChatList();

            // 如果删除的是当前对话，创建新对话
            if (conversationId === this.getCurrentConversationId()) {
                this.createNewConversation();
            }
        }
    }

    exportConversation(conversationId) {
        const conversation = this.conversations.find(conv => conv.id === conversationId);
        if (!conversation) return;

        // 显示导出选项
        this.showExportOptions(conversationId);
    }

    showExportOptions(conversationId) {
        // 创建导出选项菜单
        const exportMenu = document.createElement('div');
        exportMenu.className = 'export-options show';
        exportMenu.innerHTML = `
            <div class="export-option" data-format="markdown">
                <i class="bi bi-markdown"></i>
                <span>Markdown</span>
            </div>
            <div class="export-option" data-format="json">
                <i class="bi bi-file-code"></i>
                <span>JSON</span>
            </div>
            <div class="export-option" data-format="txt">
                <i class="bi bi-file-text"></i>
                <span>纯文本</span>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(exportMenu);

        // 绑定点击事件
        exportMenu.addEventListener('click', (e) => {
            const option = e.target.closest('.export-option');
            if (option) {
                this.doExport(conversationId, option.dataset.format);
                document.body.removeChild(exportMenu);
            }
        });

        // 点击外部关闭
        setTimeout(() => {
            document.addEventListener('click', function closeExport(e) {
                if (!exportMenu.contains(e.target)) {
                    if (exportMenu.parentNode) {
                        document.body.removeChild(exportMenu);
                    }
                    document.removeEventListener('click', closeExport);
                }
            });
        }, 100);
    }

    doExport(conversationId, format) {
        const conversation = this.conversations.find(conv => conv.id === conversationId);
        if (!conversation) return;

        let content = '';
        const filename = `${conversation.title}_${new Date().toISOString().split('T')[0]}`;

        switch (format) {
            case 'markdown':
                content = this.exportToMarkdown(conversation);
                this.downloadFile(content, `${filename}.md`, 'text/markdown');
                break;
            case 'json':
                content = JSON.stringify(conversation, null, 2);
                this.downloadFile(content, `${filename}.json`, 'application/json');
                break;
            case 'txt':
                content = this.exportToText(conversation);
                this.downloadFile(content, `${filename}.txt`, 'text/plain');
                break;
        }
    }

    exportToMarkdown(conversation) {
        let content = `# ${conversation.title}\n\n`;
        content += `**创建时间**: ${this.formatTime(conversation.lastMessage)}\n`;
        content += `**消息数量**: ${conversation.messageCount}\n`;
        if (conversation.tags.length > 0) {
            content += `**标签**: ${conversation.tags.join(', ')}\n`;
        }
        content += '\n---\n\n';

        // 这里应该添加实际的消息内容
        content += '对话内容将在实际实现中添加...\n';

        return content;
    }

    exportToText(conversation) {
        let content = `${conversation.title}\n`;
        content += '='.repeat(conversation.title.length) + '\n\n';
        content += `创建时间: ${this.formatTime(conversation.lastMessage)}\n`;
        content += `消息数量: ${conversation.messageCount}\n`;
        if (conversation.tags.length > 0) {
            content += `标签: ${conversation.tags.join(', ')}\n`;
        }
        content += '\n';

        // 这里应该添加实际的消息内容
        content += '对话内容将在实际实现中添加...\n';

        return content;
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // 工具方法
    loadConversation(conversationId) {
        // 设置当前对话
        localStorage.setItem('currentConversationId', conversationId);

        // 更新UI
        document.querySelectorAll('.chat-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-conversation-id="${conversationId}"]`)?.classList.add('active');

        // 更新导航栏标题
        const conversation = this.conversations.find(conv => conv.id === conversationId);
        if (conversation) {
            const titleElement = document.getElementById('current-chat-title');
            if (titleElement) {
                titleElement.textContent = conversation.title;
            }
        }

        // 触发对话加载事件
        const event = new CustomEvent('conversationLoaded', {
            detail: { conversationId, conversation }
        });
        document.dispatchEvent(event);
    }

    createNewConversation() {
        const newConversation = {
            id: 'conv_' + Date.now(),
            title: '新对话',
            preview: '',
            messageCount: 0,
            lastMessage: new Date(),
            tags: [],
            pinned: false,
            archived: false,
            messages: []
        };

        this.conversations.unshift(newConversation);
        this.saveConversations();
        this.organizeConversations();
        this.renderChatList();
        this.loadConversation(newConversation.id);

        return newConversation;
    }

    getCurrentConversationId() {
        return localStorage.getItem('currentConversationId');
    }

    saveConversations() {
        localStorage.setItem('chatConversations', JSON.stringify(this.conversations));
    }

    highlightSearchText(text) {
        if (!this.searchQuery) return text;

        const regex = new RegExp(`(${this.searchQuery})`, 'gi');
        return text.replace(regex, '<span class="search-highlight">$1</span>');
    }

    formatTime(date) {
        const now = new Date();
        const messageDate = new Date(date);
        const diffMs = now - messageDate;
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffDays === 0) {
            return messageDate.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
        } else if (diffDays === 1) {
            return '昨天';
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return messageDate.toLocaleDateString('zh-CN');
        }
    }
}

// 初始化对话历史管理器
window.chatHistoryManager = new ChatHistoryManager();
