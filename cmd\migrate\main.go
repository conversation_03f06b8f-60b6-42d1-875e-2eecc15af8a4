package main

import (
	"flag"
	"fmt"
	"os"

	"aiops-platform/internal/config"
	"aiops-platform/internal/database"
	"aiops-platform/internal/logger"

	"github.com/sirupsen/logrus"
)

func main() {
	var (
		dbPath = flag.String("db-path", "", "Database file path")
		action = flag.String("action", "up", "Migration action: up, down, reset, check")
	)
	flag.Parse()

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		logrus.Fatalf("Failed to load config: %v", err)
	}

	// 如果指定了数据库路径，则覆盖配置
	if *dbPath != "" {
		cfg.Database.Path = *dbPath
	}

	// 初始化日志
	log := logger.New(cfg.Log)

	log.Infof("Database migration tool - Action: %s, Path: %s", *action, cfg.Database.Path)

	// 初始化数据库
	db, err := database.New(cfg.Database)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer func() {
		if sqlDB, err := db.DB(); err == nil {
			sqlDB.Close()
		}
	}()

	switch *action {
	case "up":
		if err := database.Migrate(db); err != nil {
			log.Fatalf("Migration failed: %v", err)
		}
		log.Info("Migration completed successfully")

	case "down":
		if err := database.Rollback(db); err != nil {
			log.Fatalf("Rollback failed: %v", err)
		}
		log.Info("Rollback completed successfully")

	case "reset":
		if err := database.Reset(db); err != nil {
			log.Fatalf("Reset failed: %v", err)
		}
		log.Info("Database reset completed successfully")

	case "check":
		version, err := database.GetVersion(db)
		if err != nil {
			log.Fatalf("Failed to get database version: %v", err)
		}
		log.Infof("Current database version: %d", version)

	default:
		fmt.Printf("Unknown action: %s\n", *action)
		fmt.Println("Available actions: up, down, reset, check")
		os.Exit(1)
	}
}
