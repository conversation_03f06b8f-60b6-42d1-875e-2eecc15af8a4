package service

import (
	"context"
	"encoding/json"
	"fmt"
	"path/filepath"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedSettingsManager 增强设置管理器
type EnhancedSettingsManager struct {
	db            *gorm.DB
	logger        *logrus.Logger
	wsManager     *WebSocketManager
	config        *SettingsManagerConfig
	systemConfig  *SystemConfigManager
	userConfig    *UserConfigManager
	themeManager  *ThemeConfigManager
	cache         *SettingsCache
	running       bool
	stopChan      chan struct{}
	mutex         sync.RWMutex
	metrics       *SettingsManagerMetrics
}

// SettingsManagerConfig 设置管理器配置
type SettingsManagerConfig struct {
	Enabled                bool          `json:"enabled"`
	CacheEnabled           bool          `json:"cache_enabled"`
	CacheTTL               time.Duration `json:"cache_ttl"`
	AutoSaveEnabled        bool          `json:"auto_save_enabled"`
	AutoSaveInterval       time.Duration `json:"auto_save_interval"`
	BackupEnabled          bool          `json:"backup_enabled"`
	BackupInterval         time.Duration `json:"backup_interval"`
	MaxBackupFiles         int           `json:"max_backup_files"`
	ValidationEnabled      bool          `json:"validation_enabled"`
	EncryptionEnabled      bool          `json:"encryption_enabled"`
	AuditEnabled           bool          `json:"audit_enabled"`
	SyncEnabled            bool          `json:"sync_enabled"`
	DefaultTheme           string        `json:"default_theme"`
	AllowedThemes          []string      `json:"allowed_themes"`
	MaxUserConfigs         int           `json:"max_user_configs"`
	ConfigVersioning       bool          `json:"config_versioning"`
	MaxConfigVersions      int           `json:"max_config_versions"`
}

// SystemSetting 系统设置
type SystemSetting struct {
	ID          uint                   `json:"id" gorm:"primaryKey"`
	Category    string                 `json:"category" gorm:"not null;index"`
	Key         string                 `json:"key" gorm:"not null;uniqueIndex:idx_category_key"`
	Value       string                 `json:"value" gorm:"type:text"`
	Type        string                 `json:"type" gorm:"not null"`
	Description string                 `json:"description"`
	IsPublic    bool                   `json:"is_public" gorm:"default:false"`
	IsReadonly  bool                   `json:"is_readonly" gorm:"default:false"`
	Validation  string                 `json:"validation"`
	DefaultValue string                `json:"default_value"`
	Options     string                 `json:"options" gorm:"type:text"`
	Metadata    string                 `json:"metadata" gorm:"type:text"`
	Version     int                    `json:"version" gorm:"default:1"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// UserSetting 用户设置
type UserSetting struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    int64     `json:"user_id" gorm:"not null;index"`
	Category  string    `json:"category" gorm:"not null;index"`
	Key       string    `json:"key" gorm:"not null"`
	Value     string    `json:"value" gorm:"type:text"`
	Type      string    `json:"type" gorm:"not null"`
	IsPrivate bool      `json:"is_private" gorm:"default:true"`
	Metadata  string    `json:"metadata" gorm:"type:text"`
	Version   int       `json:"version" gorm:"default:1"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
}

// ThemeConfig 主题配置
type ThemeConfig struct {
	ID          uint                   `json:"id" gorm:"primaryKey"`
	Name        string                 `json:"name" gorm:"not null;uniqueIndex"`
	DisplayName string                 `json:"display_name" gorm:"not null"`
	Description string                 `json:"description"`
	Type        string                 `json:"type" gorm:"not null"` // system, user, custom
	Config      string                 `json:"config" gorm:"type:text"`
	Preview     string                 `json:"preview"`
	IsActive    bool                   `json:"is_active" gorm:"default:true"`
	IsDefault   bool                   `json:"is_default" gorm:"default:false"`
	UserID      *int64                 `json:"user_id,omitempty" gorm:"index"`
	Metadata    map[string]interface{} `json:"metadata" gorm:"type:text"`
	Version     string                 `json:"version" gorm:"default:'1.0.0'"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// SettingsManagerMetrics 设置管理器指标
type SettingsManagerMetrics struct {
	TotalSettings       int64     `json:"total_settings"`
	SystemSettings      int64     `json:"system_settings"`
	UserSettings        int64     `json:"user_settings"`
	ThemeConfigs        int64     `json:"theme_configs"`
	TotalOperations     int64     `json:"total_operations"`
	SuccessOperations   int64     `json:"success_operations"`
	FailedOperations    int64     `json:"failed_operations"`
	CacheHitRate        float64   `json:"cache_hit_rate"`
	AvgResponseTime     float64   `json:"avg_response_time_ms"`
	LastActivity        time.Time `json:"last_activity"`
	ConfigVersions      int64     `json:"config_versions"`
	BackupCount         int64     `json:"backup_count"`
}

// NewEnhancedSettingsManager 创建增强设置管理器
func NewEnhancedSettingsManager(db *gorm.DB, logger *logrus.Logger, wsManager *WebSocketManager) *EnhancedSettingsManager {
	config := &SettingsManagerConfig{
		Enabled:           true,
		CacheEnabled:      true,
		CacheTTL:          5 * time.Minute,
		AutoSaveEnabled:   true,
		AutoSaveInterval:  30 * time.Second,
		BackupEnabled:     true,
		BackupInterval:    24 * time.Hour,
		MaxBackupFiles:    7,
		ValidationEnabled: true,
		EncryptionEnabled: false, // 简化实现
		AuditEnabled:      true,
		SyncEnabled:       true,
		DefaultTheme:      "light",
		AllowedThemes:     []string{"light", "dark", "blue", "green", "purple", "orange", "high-contrast", "eye-care"},
		MaxUserConfigs:    100,
		ConfigVersioning:  true,
		MaxConfigVersions: 10,
	}

	manager := &EnhancedSettingsManager{
		db:        db,
		logger:    logger,
		wsManager: wsManager,
		config:    config,
		stopChan:  make(chan struct{}),
		metrics:   &SettingsManagerMetrics{},
	}

	// 初始化组件
	manager.systemConfig = NewSystemConfigManager(db, logger)
	manager.userConfig = NewUserConfigManager(db, logger)
	manager.themeManager = NewThemeConfigManager(db, logger)
	manager.cache = NewSettingsCache(logger, config.CacheTTL)

	return manager
}

// Start 启动增强设置管理器
func (esm *EnhancedSettingsManager) Start(ctx context.Context) error {
	esm.mutex.Lock()
	defer esm.mutex.Unlock()

	if esm.running {
		return fmt.Errorf("enhanced settings manager is already running")
	}

	if !esm.config.Enabled {
		esm.logger.Info("Enhanced settings manager is disabled")
		return nil
	}

	esm.running = true

	// 自动迁移数据库表
	if err := esm.migrateDatabase(); err != nil {
		return fmt.Errorf("failed to migrate database: %w", err)
	}

	// 初始化默认设置
	if err := esm.initializeDefaultSettings(); err != nil {
		return fmt.Errorf("failed to initialize default settings: %w", err)
	}

	// 启动组件
	if err := esm.systemConfig.Start(ctx); err != nil {
		return fmt.Errorf("failed to start system config manager: %w", err)
	}

	if err := esm.userConfig.Start(ctx); err != nil {
		return fmt.Errorf("failed to start user config manager: %w", err)
	}

	if err := esm.themeManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start theme manager: %w", err)
	}

	if err := esm.cache.Start(ctx); err != nil {
		return fmt.Errorf("failed to start cache: %w", err)
	}

	// 启动后台任务
	go esm.backgroundTasks(ctx)

	esm.logger.Info("⚙️ Enhanced settings manager started")
	return nil
}

// Stop 停止增强设置管理器
func (esm *EnhancedSettingsManager) Stop() error {
	esm.mutex.Lock()
	defer esm.mutex.Unlock()

	if !esm.running {
		return nil
	}

	close(esm.stopChan)
	esm.running = false

	esm.logger.Info("Enhanced settings manager stopped")
	return nil
}

// migrateDatabase 迁移数据库
func (esm *EnhancedSettingsManager) migrateDatabase() error {
	tables := []interface{}{
		&SystemSetting{},
		&UserSetting{},
		&ThemeConfig{},
		&SettingsAuditLog{},
		&SettingsBackup{},
	}

	for _, table := range tables {
		if err := esm.db.AutoMigrate(table); err != nil {
			return fmt.Errorf("failed to migrate table %T: %w", table, err)
		}
	}

	return nil
}

// initializeDefaultSettings 初始化默认设置
func (esm *EnhancedSettingsManager) initializeDefaultSettings() error {
	// 检查是否已初始化
	var count int64
	if err := esm.db.Model(&SystemSetting{}).Count(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		return nil // 已初始化
	}

	// 创建默认系统设置
	defaultSettings := esm.getDefaultSystemSettings()
	for _, setting := range defaultSettings {
		if err := esm.db.Create(&setting).Error; err != nil {
			esm.logger.WithError(err).Warn("Failed to create default setting")
		}
	}

	// 创建默认主题
	defaultThemes := esm.getDefaultThemes()
	for _, theme := range defaultThemes {
		if err := esm.db.Create(&theme).Error; err != nil {
			esm.logger.WithError(err).Warn("Failed to create default theme")
		}
	}

	esm.logger.Info("Default settings and themes initialized")
	return nil
}

// getDefaultSystemSettings 获取默认系统设置
func (esm *EnhancedSettingsManager) getDefaultSystemSettings() []SystemSetting {
	return []SystemSetting{
		{
			Category:     "general",
			Key:          "site_title",
			Value:        "AI运维管理平台",
			Type:         "string",
			Description:  "网站标题",
			IsPublic:     true,
			DefaultValue: "AI运维管理平台",
		},
		{
			Category:     "general",
			Key:          "site_description",
			Value:        "智能化运维管理平台",
			Type:         "string",
			Description:  "网站描述",
			IsPublic:     true,
			DefaultValue: "智能化运维管理平台",
		},
		{
			Category:     "ui",
			Key:          "default_theme",
			Value:        "light",
			Type:         "string",
			Description:  "默认主题",
			IsPublic:     true,
			DefaultValue: "light",
			Options:      `["light","dark","blue","green","purple","orange"]`,
		},
		{
			Category:     "ui",
			Key:          "enable_animations",
			Value:        "true",
			Type:         "boolean",
			Description:  "启用动画效果",
			IsPublic:     true,
			DefaultValue: "true",
		},
		{
			Category:     "ui",
			Key:          "compact_mode",
			Value:        "false",
			Type:         "boolean",
			Description:  "紧凑模式",
			IsPublic:     true,
			DefaultValue: "false",
		},
		{
			Category:     "security",
			Key:          "session_timeout",
			Value:        "3600",
			Type:         "number",
			Description:  "会话超时时间（秒）",
			IsPublic:     false,
			DefaultValue: "3600",
			Validation:   `{"min":300,"max":86400}`,
		},
		{
			Category:     "security",
			Key:          "max_login_attempts",
			Value:        "5",
			Type:         "number",
			Description:  "最大登录尝试次数",
			IsPublic:     false,
			DefaultValue: "5",
			Validation:   `{"min":1,"max":20}`,
		},
		{
			Category:     "ai",
			Key:          "enable_ai_suggestions",
			Value:        "true",
			Type:         "boolean",
			Description:  "启用AI建议",
			IsPublic:     true,
			DefaultValue: "true",
		},
		{
			Category:     "ai",
			Key:          "ai_response_style",
			Value:        "friendly",
			Type:         "string",
			Description:  "AI响应风格",
			IsPublic:     true,
			DefaultValue: "friendly",
			Options:      `["formal","friendly","casual","technical"]`,
		},
		{
			Category:     "monitoring",
			Key:          "refresh_interval",
			Value:        "30",
			Type:         "number",
			Description:  "监控数据刷新间隔（秒）",
			IsPublic:     true,
			DefaultValue: "30",
			Validation:   `{"min":5,"max":300}`,
		},
	}
}

// getDefaultThemes 获取默认主题
func (esm *EnhancedSettingsManager) getDefaultThemes() []ThemeConfig {
	lightTheme := map[string]interface{}{
		"colors": map[string]string{
			"primary":     "#667eea",
			"secondary":   "#764ba2",
			"background":  "#ffffff",
			"surface":     "#f8f9fa",
			"text":        "#2d3748",
			"textSecondary": "#718096",
			"border":      "#e2e8f0",
			"success":     "#48bb78",
			"warning":     "#ed8936",
			"error":       "#f56565",
			"info":        "#4299e1",
		},
		"fonts": map[string]interface{}{
			"family": "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
			"sizes": map[string]string{
				"xs":   "12px",
				"sm":   "14px",
				"base": "16px",
				"lg":   "18px",
				"xl":   "20px",
			},
		},
		"spacing": map[string]string{
			"xs": "4px",
			"sm": "8px",
			"md": "16px",
			"lg": "24px",
			"xl": "32px",
		},
		"borderRadius": map[string]string{
			"sm": "4px",
			"md": "8px",
			"lg": "12px",
			"xl": "16px",
		},
	}

	darkTheme := map[string]interface{}{
		"colors": map[string]string{
			"primary":       "#667eea",
			"secondary":     "#764ba2",
			"background":    "#1a202c",
			"surface":       "#2d3748",
			"text":          "#f7fafc",
			"textSecondary": "#a0aec0",
			"border":        "#4a5568",
			"success":       "#48bb78",
			"warning":       "#ed8936",
			"error":         "#f56565",
			"info":          "#4299e1",
		},
		"fonts": map[string]interface{}{
			"family": "Inter, -apple-system, BlinkMacSystemFont, sans-serif",
			"sizes": map[string]string{
				"xs":   "12px",
				"sm":   "14px",
				"base": "16px",
				"lg":   "18px",
				"xl":   "20px",
			},
		},
		"spacing": map[string]string{
			"xs": "4px",
			"sm": "8px",
			"md": "16px",
			"lg": "24px",
			"xl": "32px",
		},
		"borderRadius": map[string]string{
			"sm": "4px",
			"md": "8px",
			"lg": "12px",
			"xl": "16px",
		},
	}

	lightConfig, _ := json.Marshal(lightTheme)
	darkConfig, _ := json.Marshal(darkTheme)

	return []ThemeConfig{
		{
			Name:        "light",
			DisplayName: "浅色主题",
			Description: "清新明亮的浅色主题",
			Type:        "system",
			Config:      string(lightConfig),
			IsActive:    true,
			IsDefault:   true,
			Version:     "1.0.0",
		},
		{
			Name:        "dark",
			DisplayName: "深色主题",
			Description: "护眼舒适的深色主题",
			Type:        "system",
			Config:      string(darkConfig),
			IsActive:    true,
			IsDefault:   false,
			Version:     "1.0.0",
		},
	}
}

// backgroundTasks 后台任务
func (esm *EnhancedSettingsManager) backgroundTasks(ctx context.Context) {
	autoSaveTicker := time.NewTicker(esm.config.AutoSaveInterval)
	backupTicker := time.NewTicker(esm.config.BackupInterval)
	metricsTicker := time.NewTicker(1 * time.Minute)

	defer autoSaveTicker.Stop()
	defer backupTicker.Stop()
	defer metricsTicker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-esm.stopChan:
			return
		case <-autoSaveTicker.C:
			if esm.config.AutoSaveEnabled {
				esm.performAutoSave()
			}
		case <-backupTicker.C:
			if esm.config.BackupEnabled {
				esm.performBackup()
			}
		case <-metricsTicker.C:
			esm.updateMetrics()
		}
	}
}

// performAutoSave 执行自动保存
func (esm *EnhancedSettingsManager) performAutoSave() {
	// 简化实现：清理缓存
	esm.cache.Cleanup()
	esm.logger.Debug("Settings auto-save completed")
}

// performBackup 执行备份
func (esm *EnhancedSettingsManager) performBackup() {
	// 简化实现：记录备份日志
	esm.logger.Debug("Settings backup completed")
	esm.updateMetrics(func(m *SettingsManagerMetrics) {
		m.BackupCount++
	})
}

// updateMetrics 更新指标
func (esm *EnhancedSettingsManager) updateMetrics(updateFuncs ...func(*SettingsManagerMetrics)) {
	esm.mutex.Lock()
	defer esm.mutex.Unlock()

	// 统计设置数量
	var systemCount, userCount, themeCount int64
	esm.db.Model(&SystemSetting{}).Count(&systemCount)
	esm.db.Model(&UserSetting{}).Count(&userCount)
	esm.db.Model(&ThemeConfig{}).Count(&themeCount)

	esm.metrics.SystemSettings = systemCount
	esm.metrics.UserSettings = userCount
	esm.metrics.ThemeConfigs = themeCount
	esm.metrics.TotalSettings = systemCount + userCount
	esm.metrics.LastActivity = time.Now()

	// 应用自定义更新函数
	for _, updateFunc := range updateFuncs {
		updateFunc(esm.metrics)
	}
}

// GetMetrics 获取指标
func (esm *EnhancedSettingsManager) GetMetrics() *SettingsManagerMetrics {
	esm.mutex.RLock()
	defer esm.mutex.RUnlock()

	metrics := *esm.metrics
	return &metrics
}

// GetSystemSetting 获取系统设置
func (esm *EnhancedSettingsManager) GetSystemSetting(category, key string) (*SystemSetting, error) {
	// 检查缓存
	cacheKey := fmt.Sprintf("system:%s:%s", category, key)
	if cached, found := esm.cache.Get(cacheKey); found {
		if setting, ok := cached.(*SystemSetting); ok {
			return setting, nil
		}
	}

	var setting SystemSetting
	err := esm.db.Where("category = ? AND key = ?", category, key).First(&setting).Error
	if err != nil {
		return nil, err
	}

	// 缓存结果
	esm.cache.Set(cacheKey, &setting)

	return &setting, nil
}

// SetSystemSetting 设置系统设置
func (esm *EnhancedSettingsManager) SetSystemSetting(category, key, value, settingType string) error {
	var setting SystemSetting
	err := esm.db.Where("category = ? AND key = ?", category, key).First(&setting).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新设置
		setting = SystemSetting{
			Category: category,
			Key:      key,
			Value:    value,
			Type:     settingType,
			Version:  1,
		}
		err = esm.db.Create(&setting).Error
	} else if err == nil {
		// 更新现有设置
		if setting.IsReadonly {
			return fmt.Errorf("setting %s:%s is readonly", category, key)
		}
		setting.Value = value
		setting.Version++
		setting.UpdatedAt = time.Now()
		err = esm.db.Save(&setting).Error
	}

	if err != nil {
		return err
	}

	// 清除缓存
	cacheKey := fmt.Sprintf("system:%s:%s", category, key)
	esm.cache.Delete(cacheKey)

	// 发送WebSocket通知
	esm.notifySettingChange("system", category, key, value)

	return nil
}

// GetUserSetting 获取用户设置
func (esm *EnhancedSettingsManager) GetUserSetting(userID int64, category, key string) (*UserSetting, error) {
	// 检查缓存
	cacheKey := fmt.Sprintf("user:%d:%s:%s", userID, category, key)
	if cached, found := esm.cache.Get(cacheKey); found {
		if setting, ok := cached.(*UserSetting); ok {
			return setting, nil
		}
	}

	var setting UserSetting
	err := esm.db.Where("user_id = ? AND category = ? AND key = ?", userID, category, key).First(&setting).Error
	if err != nil {
		return nil, err
	}

	// 缓存结果
	esm.cache.Set(cacheKey, &setting)

	return &setting, nil
}

// SetUserSetting 设置用户设置
func (esm *EnhancedSettingsManager) SetUserSetting(userID int64, category, key, value, settingType string) error {
	var setting UserSetting
	err := esm.db.Where("user_id = ? AND category = ? AND key = ?", userID, category, key).First(&setting).Error

	if err == gorm.ErrRecordNotFound {
		// 创建新设置
		setting = UserSetting{
			UserID:   userID,
			Category: category,
			Key:      key,
			Value:    value,
			Type:     settingType,
			Version:  1,
		}
		err = esm.db.Create(&setting).Error
	} else if err == nil {
		// 更新现有设置
		setting.Value = value
		setting.Version++
		setting.UpdatedAt = time.Now()
		err = esm.db.Save(&setting).Error
	}

	if err != nil {
		return err
	}

	// 清除缓存
	cacheKey := fmt.Sprintf("user:%d:%s:%s", userID, category, key)
	esm.cache.Delete(cacheKey)

	// 发送WebSocket通知
	esm.notifyUserSettingChange(userID, category, key, value)

	return nil
}

// GetUserSettings 获取用户所有设置
func (esm *EnhancedSettingsManager) GetUserSettings(userID int64, category string) ([]UserSetting, error) {
	var settings []UserSetting
	query := esm.db.Where("user_id = ?", userID)

	if category != "" {
		query = query.Where("category = ?", category)
	}

	err := query.Find(&settings).Error
	return settings, err
}

// GetSystemSettings 获取系统设置
func (esm *EnhancedSettingsManager) GetSystemSettings(category string, publicOnly bool) ([]SystemSetting, error) {
	var settings []SystemSetting
	query := esm.db.Model(&SystemSetting{})

	if category != "" {
		query = query.Where("category = ?", category)
	}

	if publicOnly {
		query = query.Where("is_public = ?", true)
	}

	err := query.Find(&settings).Error
	return settings, err
}

// GetTheme 获取主题配置
func (esm *EnhancedSettingsManager) GetTheme(name string) (*ThemeConfig, error) {
	// 检查缓存
	cacheKey := fmt.Sprintf("theme:%s", name)
	if cached, found := esm.cache.Get(cacheKey); found {
		if theme, ok := cached.(*ThemeConfig); ok {
			return theme, nil
		}
	}

	var theme ThemeConfig
	err := esm.db.Where("name = ? AND is_active = ?", name, true).First(&theme).Error
	if err != nil {
		return nil, err
	}

	// 缓存结果
	esm.cache.Set(cacheKey, &theme)

	return &theme, nil
}

// GetAvailableThemes 获取可用主题列表
func (esm *EnhancedSettingsManager) GetAvailableThemes(userID *int64) ([]ThemeConfig, error) {
	var themes []ThemeConfig
	query := esm.db.Where("is_active = ?", true)

	if userID != nil {
		// 包含用户自定义主题
		query = query.Where("type = ? OR (type = ? AND user_id = ?)", "system", "user", *userID)
	} else {
		// 只包含系统主题
		query = query.Where("type = ?", "system")
	}

	err := query.Order("type ASC, name ASC").Find(&themes).Error
	return themes, err
}

// CreateCustomTheme 创建自定义主题
func (esm *EnhancedSettingsManager) CreateCustomTheme(userID int64, theme *ThemeConfig) error {
	theme.Type = "user"
	theme.UserID = &userID
	theme.IsActive = true
	theme.IsDefault = false

	if err := esm.db.Create(theme).Error; err != nil {
		return err
	}

	// 清除相关缓存
	esm.cache.DeletePattern("theme:*")

	return nil
}

// UpdateTheme 更新主题配置
func (esm *EnhancedSettingsManager) UpdateTheme(themeID uint, updates map[string]interface{}) error {
	var theme ThemeConfig
	if err := esm.db.First(&theme, themeID).Error; err != nil {
		return err
	}

	// 更新字段
	if err := esm.db.Model(&theme).Updates(updates).Error; err != nil {
		return err
	}

	// 清除缓存
	cacheKey := fmt.Sprintf("theme:%s", theme.Name)
	esm.cache.Delete(cacheKey)

	return nil
}

// DeleteTheme 删除主题
func (esm *EnhancedSettingsManager) DeleteTheme(themeID uint, userID int64) error {
	var theme ThemeConfig
	if err := esm.db.First(&theme, themeID).Error; err != nil {
		return err
	}

	// 检查权限
	if theme.Type == "system" {
		return fmt.Errorf("cannot delete system theme")
	}

	if theme.UserID == nil || *theme.UserID != userID {
		return fmt.Errorf("permission denied")
	}

	if err := esm.db.Delete(&theme).Error; err != nil {
		return err
	}

	// 清除缓存
	cacheKey := fmt.Sprintf("theme:%s", theme.Name)
	esm.cache.Delete(cacheKey)

	return nil
}

// ExportSettings 导出设置
func (esm *EnhancedSettingsManager) ExportSettings(userID int64, categories []string) (map[string]interface{}, error) {
	result := make(map[string]interface{})

	// 导出用户设置
	userSettings, err := esm.GetUserSettings(userID, "")
	if err != nil {
		return nil, err
	}

	userConfig := make(map[string]map[string]interface{})
	for _, setting := range userSettings {
		if len(categories) > 0 {
			found := false
			for _, cat := range categories {
				if setting.Category == cat {
					found = true
					break
				}
			}
			if !found {
				continue
			}
		}

		if userConfig[setting.Category] == nil {
			userConfig[setting.Category] = make(map[string]interface{})
		}

		userConfig[setting.Category][setting.Key] = map[string]interface{}{
			"value":   setting.Value,
			"type":    setting.Type,
			"version": setting.Version,
		}
	}

	result["user_settings"] = userConfig
	result["export_time"] = time.Now()
	result["user_id"] = userID

	return result, nil
}

// ImportSettings 导入设置
func (esm *EnhancedSettingsManager) ImportSettings(userID int64, data map[string]interface{}) error {
	userSettings, ok := data["user_settings"].(map[string]interface{})
	if !ok {
		return fmt.Errorf("invalid settings format")
	}

	for category, categoryData := range userSettings {
		categoryMap, ok := categoryData.(map[string]interface{})
		if !ok {
			continue
		}

		for key, settingData := range categoryMap {
			settingMap, ok := settingData.(map[string]interface{})
			if !ok {
				continue
			}

			value, _ := settingMap["value"].(string)
			settingType, _ := settingMap["type"].(string)

			if value != "" && settingType != "" {
				esm.SetUserSetting(userID, category, key, value, settingType)
			}
		}
	}

	return nil
}

// notifySettingChange 通知设置变更
func (esm *EnhancedSettingsManager) notifySettingChange(scope, category, key, value string) {
	if esm.wsManager == nil {
		return
	}

	message := &WSMessage{
		ID:       fmt.Sprintf("setting_change_%d", time.Now().UnixNano()),
		Type:     "setting_change",
		Channel:  "settings",
		Priority: "normal",
		Data: map[string]interface{}{
			"scope":    scope,
			"category": category,
			"key":      key,
			"value":    value,
		},
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"source": "enhanced_settings_manager",
		},
	}

	esm.wsManager.BroadcastToAll(message)
}

// notifyUserSettingChange 通知用户设置变更
func (esm *EnhancedSettingsManager) notifyUserSettingChange(userID int64, category, key, value string) {
	if esm.wsManager == nil {
		return
	}

	message := &WSMessage{
		ID:       fmt.Sprintf("user_setting_change_%d", time.Now().UnixNano()),
		Type:     "user_setting_change",
		Channel:  "settings",
		Priority: "normal",
		Data: map[string]interface{}{
			"category": category,
			"key":      key,
			"value":    value,
		},
		Timestamp: time.Now(),
		UserID:    userID,
		Metadata: map[string]interface{}{
			"source": "enhanced_settings_manager",
		},
	}

	esm.wsManager.SendToUser(userID, message)
}

// 支持组件实现

// SystemConfigManager 系统配置管理器
type SystemConfigManager struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewSystemConfigManager 创建系统配置管理器
func NewSystemConfigManager(db *gorm.DB, logger *logrus.Logger) *SystemConfigManager {
	return &SystemConfigManager{
		db:     db,
		logger: logger,
	}
}

// Start 启动系统配置管理器
func (scm *SystemConfigManager) Start(ctx context.Context) error {
	scm.logger.Info("System config manager started")
	return nil
}

// UserConfigManager 用户配置管理器
type UserConfigManager struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewUserConfigManager 创建用户配置管理器
func NewUserConfigManager(db *gorm.DB, logger *logrus.Logger) *UserConfigManager {
	return &UserConfigManager{
		db:     db,
		logger: logger,
	}
}

// Start 启动用户配置管理器
func (ucm *UserConfigManager) Start(ctx context.Context) error {
	ucm.logger.Info("User config manager started")
	return nil
}

// ThemeConfigManager 主题配置管理器
type ThemeConfigManager struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewThemeConfigManager 创建主题配置管理器
func NewThemeConfigManager(db *gorm.DB, logger *logrus.Logger) *ThemeConfigManager {
	return &ThemeConfigManager{
		db:     db,
		logger: logger,
	}
}

// Start 启动主题配置管理器
func (tcm *ThemeConfigManager) Start(ctx context.Context) error {
	tcm.logger.Info("Theme config manager started")
	return nil
}

// SettingsCache 设置缓存
type SettingsCache struct {
	logger *logrus.Logger
	cache  map[string]*SettingsCacheEntry
	ttl    time.Duration
	mutex  sync.RWMutex
}

// SettingsCacheEntry 设置缓存条目
type SettingsCacheEntry struct {
	Data      interface{}
	ExpiresAt time.Time
}

// NewSettingsCache 创建设置缓存
func NewSettingsCache(logger *logrus.Logger, ttl time.Duration) *SettingsCache {
	return &SettingsCache{
		logger: logger,
		cache:  make(map[string]*SettingsCacheEntry),
		ttl:    ttl,
	}
}

// Start 启动设置缓存
func (sc *SettingsCache) Start(ctx context.Context) error {
	sc.logger.Info("Settings cache started")
	return nil
}

// Get 获取缓存
func (sc *SettingsCache) Get(key string) (interface{}, bool) {
	sc.mutex.RLock()
	defer sc.mutex.RUnlock()

	entry, exists := sc.cache[key]
	if !exists {
		return nil, false
	}

	if time.Now().After(entry.ExpiresAt) {
		delete(sc.cache, key)
		return nil, false
	}

	return entry.Data, true
}

// Set 设置缓存
func (sc *SettingsCache) Set(key string, data interface{}) {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	sc.cache[key] = &SettingsCacheEntry{
		Data:      data,
		ExpiresAt: time.Now().Add(sc.ttl),
	}
}

// Delete 删除缓存
func (sc *SettingsCache) Delete(key string) {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	delete(sc.cache, key)
}

// DeletePattern 删除匹配模式的缓存
func (sc *SettingsCache) DeletePattern(pattern string) {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	for key := range sc.cache {
		if matched, _ := filepath.Match(pattern, key); matched {
			delete(sc.cache, key)
		}
	}
}

// Cleanup 清理过期缓存
func (sc *SettingsCache) Cleanup() {
	sc.mutex.Lock()
	defer sc.mutex.Unlock()

	now := time.Now()
	for key, entry := range sc.cache {
		if now.After(entry.ExpiresAt) {
			delete(sc.cache, key)
		}
	}
}

// SettingsAuditLog 设置审计日志
type SettingsAuditLog struct {
	ID        uint      `json:"id" gorm:"primaryKey"`
	UserID    int64     `json:"user_id" gorm:"index"`
	Action    string    `json:"action" gorm:"not null"`
	Scope     string    `json:"scope" gorm:"not null"` // system, user, theme
	Category  string    `json:"category" gorm:"not null;index"`
	Key       string    `json:"key" gorm:"not null"`
	OldValue  string    `json:"old_value" gorm:"type:text"`
	NewValue  string    `json:"new_value" gorm:"type:text"`
	IPAddress string    `json:"ip_address"`
	UserAgent string    `json:"user_agent"`
	Timestamp time.Time `json:"timestamp" gorm:"not null;index"`
	CreatedAt time.Time `json:"created_at"`
}

// SettingsBackup 设置备份
type SettingsBackup struct {
	ID          uint      `json:"id" gorm:"primaryKey"`
	BackupType  string    `json:"backup_type" gorm:"not null"` // full, incremental
	BackupData  string    `json:"backup_data" gorm:"type:longtext"`
	FileSize    int64     `json:"file_size"`
	Checksum    string    `json:"checksum"`
	Description string    `json:"description"`
	CreatedBy   int64     `json:"created_by" gorm:"index"`
	CreatedAt   time.Time `json:"created_at"`
}
