<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI运维助手 - UI测试页面</title>

    <!-- 图标 -->
    <link href="vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">

    <!-- 现代化设计系统 V2.0 -->
    <link href="css/design-system.css" rel="stylesheet">
    
    <!-- 统一组件库 -->
    <link href="css/components.css" rel="stylesheet">

    <!-- 交互增强系统 -->
    <link href="css/interactions.css" rel="stylesheet">
    
    <!-- AI对话界面专用样式 -->
    <link href="css/chat.css" rel="stylesheet">
    
    <!-- 主题系统 -->
    <link href="css/themes.css" rel="stylesheet">
    
    <!-- 高级动画效果 -->
    <link href="css/animations.css" rel="stylesheet">
    
    <!-- 高级交互功能 -->
    <link href="css/advanced-interactions.css" rel="stylesheet">
</head>
<body>
    <div class="chat-container">
        <!-- 顶部导航栏 -->
        <nav class="chat-navbar">
            <a href="/" class="chat-brand">
                <div class="brand-icon">
                    <i class="bi bi-robot"></i>
                </div>
                <span>AI运维助手</span>
            </a>

            <div class="chat-actions">
                <!-- 主题切换器 -->
                <div class="theme-switcher tooltip" data-tooltip="切换主题">
                    <i class="bi bi-sun-fill theme-icon active" id="light-icon"></i>
                    <button class="theme-toggle" id="theme-toggle" onclick="toggleTheme()">
                    </button>
                    <i class="bi bi-moon-fill theme-icon" id="dark-icon"></i>
                </div>
                
                <!-- 设置按钮 -->
                <button class="btn btn-ghost btn-circle tooltip" data-tooltip="设置" onclick="toggleSettings()">
                    <i class="bi bi-gear"></i>
                </button>
                
                <!-- 用户菜单 -->
                <button class="user-menu">
                    <div class="user-avatar">A</div>
                    <span>管理员</span>
                    <i class="bi bi-chevron-down"></i>
                </button>
            </div>
        </nav>

        <!-- 主对话区域 -->
        <div class="chat-main">
            <div class="chat-area">
                <!-- 对话头部 -->
                <div class="chat-header">
                    <div class="chat-header-left">
                        <div class="chat-title">AI运维助手</div>
                        <div class="chat-subtitle">通过自然语言对话管理您的IT基础设施</div>
                    </div>
                    <div class="chat-header-right">
                        <div class="chat-controls">
                            <button class="btn btn-secondary btn-sm">
                                <i class="bi bi-clock-history"></i>
                                对话历史
                            </button>
                            <button class="btn btn-primary btn-sm">
                                <i class="bi bi-plus-circle"></i>
                                新建对话
                            </button>
                        </div>
                        <div class="session-info">
                            <small>当前对话：新对话</small>
                        </div>
                    </div>
                </div>

                <!-- 对话消息区域 -->
                <div class="chat-messages">
                    <!-- 欢迎界面 -->
                    <div class="welcome-screen">
                        <div class="welcome-avatar">
                            <i class="bi bi-robot"></i>
                        </div>
                        <div class="welcome-title">您好！我是您的AI运维助手</div>
                        <div class="welcome-description">
                            我可以帮您管理服务器、监控系统状态、处理告警等。请告诉我您需要什么帮助？
                        </div>

                        <!-- 功能介绍卡片 -->
                        <div class="feature-cards">
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-server"></i>
                                </div>
                                <div class="feature-title">主机管理</div>
                                <div class="feature-desc">查看服务器状态、执行命令、管理主机</div>
                                <div class="feature-example">例如："查看所有主机状态"</div>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="feature-title">监控告警</div>
                                <div class="feature-desc">实时监控、告警处理、状态检查</div>
                                <div class="feature-example">例如："显示最近的告警信息"</div>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-graph-up"></i>
                                </div>
                                <div class="feature-title">统计报表</div>
                                <div class="feature-desc">数据分析、性能统计、趋势报告</div>
                                <div class="feature-example">例如："生成今天的运维统计报表"</div>
                            </div>
                            <div class="feature-card">
                                <div class="feature-icon">
                                    <i class="bi bi-terminal"></i>
                                </div>
                                <div class="feature-title">命令执行</div>
                                <div class="feature-desc">远程执行命令、脚本运行、系统操作</div>
                                <div class="feature-example">例如："在web-server-01上执行 ps aux"</div>
                            </div>
                        </div>

                        <!-- 快捷操作按钮 -->
                        <div class="quick-actions">
                            <button class="quick-action-btn">
                                <div class="quick-action-icon">
                                    <i class="bi bi-server"></i>
                                </div>
                                <div class="quick-action-text">查看主机</div>
                            </button>
                            <button class="quick-action-btn">
                                <div class="quick-action-icon">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="quick-action-text">检查告警</div>
                            </button>
                            <button class="quick-action-btn">
                                <div class="quick-action-icon">
                                    <i class="bi bi-activity"></i>
                                </div>
                                <div class="quick-action-text">系统状态</div>
                            </button>
                            <button class="quick-action-btn">
                                <div class="quick-action-icon">
                                    <i class="bi bi-graph-up"></i>
                                </div>
                                <div class="quick-action-text">性能统计</div>
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 对话输入区域 -->
                <div class="chat-input-area">
                    <div class="chat-input-container">
                        <div class="input-wrapper">
                            <input
                                type="text"
                                class="chat-input"
                                placeholder="输入您的问题，例如：查看主机状态、检查告警信息..."
                                autocomplete="off"
                            >
                            <button class="send-button">
                                <i class="bi bi-send"></i>
                            </button>
                        </div>

                        <!-- 输入建议 -->
                        <div class="input-suggestions">
                            <div class="suggestion-label">建议：</div>
                            <button class="suggestion-btn">
                                <span>查看主机状态</span>
                            </button>
                            <button class="suggestion-btn">
                                <span>检查告警</span>
                            </button>
                            <button class="suggestion-btn">
                                <span>性能统计</span>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 粒子背景效果 -->
        <div class="particles-container" id="particles-container"></div>
    </div>

    <script>
        // 主题切换功能
        function toggleTheme() {
            const currentTheme = document.documentElement.getAttribute('data-theme');
            const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
            
            document.documentElement.classList.add('theme-transition');
            document.documentElement.setAttribute('data-theme', newTheme);
            
            const toggle = document.getElementById('theme-toggle');
            const lightIcon = document.getElementById('light-icon');
            const darkIcon = document.getElementById('dark-icon');
            
            if (newTheme === 'dark') {
                toggle.classList.add('dark');
                lightIcon.classList.remove('active');
                darkIcon.classList.add('active');
            } else {
                toggle.classList.remove('dark');
                lightIcon.classList.add('active');
                darkIcon.classList.remove('active');
            }
            
            setTimeout(() => {
                document.documentElement.classList.remove('theme-transition');
            }, 500);
        }

        // 粒子效果
        function initializeParticles() {
            const container = document.getElementById('particles-container');
            const particleCount = 30;
            
            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 20 + 's';
                particle.style.animationDuration = (Math.random() * 10 + 20) + 's';
                
                container.appendChild(particle);
            }
        }

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initializeParticles();
        });
    </script>
</body>
</html>
