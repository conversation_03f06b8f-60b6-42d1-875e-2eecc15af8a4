package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"aiops-platform/internal/ai_brain"
	"aiops-platform/internal/intent_engine"
	"aiops-platform/internal/config"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// RevolutionaryAIOpsServer 革命性AI运维服务器
type RevolutionaryAIOpsServer struct {
	// 核心组件
	aiBrain           *ai_brain.AIBrainCore
	intentEngine      *intent_engine.NextGenIntentRecognizer
	multimodalProcessor *intent_engine.MultimodalProcessor

	// 服务配置
	config *config.Config
	logger *logrus.Logger
	router *gin.Engine
	server *http.Server

	// 运行状态
	isRunning bool
	startTime time.Time
}

// NewRevolutionaryAIOpsServer 创建革命性AI运维服务器
func NewRevolutionaryAIOpsServer() (*RevolutionaryAIOpsServer, error) {
	// 初始化日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.JSONFormatter{})

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		return nil, fmt.Errorf("failed to load config: %w", err)
	}

	// 创建服务器实例
	server := &RevolutionaryAIOpsServer{
		config:    cfg,
		logger:    logger,
		isRunning: false,
	}

	// 初始化核心组件
	if err := server.initializeComponents(); err != nil {
		return nil, fmt.Errorf("failed to initialize components: %w", err)
	}

	// 设置路由
	server.setupRoutes()

	logger.Info("Revolutionary AI Ops Server created successfully")
	return server, nil
}

// initializeComponents 初始化核心组件
func (s *RevolutionaryAIOpsServer) initializeComponents() error {
	s.logger.Info("Initializing revolutionary AI ops components...")

	// 1. 初始化AI大脑
	brainConfig := &ai_brain.AIBrainConfig{
		Name:              "Revolutionary-AI-Brain",
		Version:           "2.0.0",
		MaxConcurrentOps:  1000,
		ResponseTimeout:   30 * time.Second,
		LearningEnabled:   true,
		PredictionEnabled: true,
	}

	var err error
	s.aiBrain, err = ai_brain.NewAIBrainCore(brainConfig, s.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize AI brain: %w", err)
	}

	// 2. 初始化下一代意图识别引擎
	intentConfig := &intent_engine.IntentEngineConfig{
		Name:                  "Revolutionary-Intent-Engine",
		Version:               "2.0.0",
		MaxConcurrentRequests: 1000,
		ProcessingTimeout:     10 * time.Second,
		ConfidenceThreshold:   0.7,
		MultimodalEnabled:     true,
		ContextWindowSize:     20,
		IntentChainMaxDepth:   10,
		SelfLearningEnabled:   true,
		CacheEnabled:          true,
	}

	s.intentEngine, err = intent_engine.NewNextGenIntentRecognizer(intentConfig, s.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize intent engine: %w", err)
	}

	// 3. 初始化多模态处理器
	s.multimodalProcessor, err = intent_engine.NewMultimodalProcessor(intentConfig, s.logger)
	if err != nil {
		return fmt.Errorf("failed to initialize multimodal processor: %w", err)
	}

	s.logger.Info("All revolutionary AI ops components initialized successfully")
	return nil
}

// setupRoutes 设置路由
func (s *RevolutionaryAIOpsServer) setupRoutes() {
	// 设置Gin模式
	gin.SetMode(gin.ReleaseMode)
	
	s.router = gin.New()
	s.router.Use(gin.Logger())
	s.router.Use(gin.Recovery())

	// CORS中间件
	s.router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})

	// 健康检查
	s.router.GET("/health", s.handleHealthCheck)
	s.router.GET("/status", s.handleStatus)

	// API路由组
	api := s.router.Group("/api/v2")
	{
		// AI大脑接口
		brain := api.Group("/brain")
		{
			brain.POST("/process", s.handleBrainProcess)
			brain.GET("/status", s.handleBrainStatus)
			brain.GET("/knowledge", s.handleKnowledgeQuery)
		}

		// 意图识别接口
		intent := api.Group("/intent")
		{
			intent.POST("/recognize", s.handleIntentRecognition)
			intent.GET("/supported", s.handleSupportedIntents)
			intent.POST("/multimodal", s.handleMultimodalInput)
		}

		// 多模态交互接口
		multimodal := api.Group("/multimodal")
		{
			multimodal.POST("/text", s.handleTextInput)
			multimodal.POST("/voice", s.handleVoiceInput)
			multimodal.POST("/image", s.handleImageInput)
			multimodal.POST("/gesture", s.handleGestureInput)
		}

		// 智能对话接口
		chat := api.Group("/chat")
		{
			chat.POST("/message", s.handleChatMessage)
			chat.GET("/history/:session_id", s.handleChatHistory)
			chat.DELETE("/session/:session_id", s.handleClearSession)
		}
	}

	// WebSocket接口
	s.router.GET("/ws", s.handleWebSocket)

	// 静态文件服务
	s.router.Static("/static", "./web/static")
	s.router.LoadHTMLGlob("web/templates/*")
	
	// 主页
	s.router.GET("/", s.handleIndex)
}

// Start 启动服务器
func (s *RevolutionaryAIOpsServer) Start() error {
	s.logger.Info("Starting Revolutionary AI Ops Server...")

	// 创建HTTP服务器
	s.server = &http.Server{
		Addr:    fmt.Sprintf(":%d", s.config.Server.Port),
		Handler: s.router,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	// 启动服务器
	s.isRunning = true
	s.startTime = time.Now()

	s.logger.WithFields(logrus.Fields{
		"port":    s.config.Server.Port,
		"version": "2.0.0",
		"mode":    "revolutionary",
	}).Info("Revolutionary AI Ops Server started successfully")

	return s.server.ListenAndServe()
}

// Stop 停止服务器
func (s *RevolutionaryAIOpsServer) Stop() error {
	s.logger.Info("Stopping Revolutionary AI Ops Server...")

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	s.isRunning = false
	
	if err := s.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("server shutdown failed: %w", err)
	}

	s.logger.Info("Revolutionary AI Ops Server stopped successfully")
	return nil
}

// 处理器方法

// handleHealthCheck 健康检查
func (s *RevolutionaryAIOpsServer) handleHealthCheck(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "2.0.0",
		"uptime":    time.Since(s.startTime).String(),
	})
}

// handleStatus 状态检查
func (s *RevolutionaryAIOpsServer) handleStatus(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"server": gin.H{
			"running":   s.isRunning,
			"start_time": s.startTime,
			"uptime":    time.Since(s.startTime).String(),
		},
		"components": gin.H{
			"ai_brain":      "operational",
			"intent_engine": "operational",
			"multimodal":    "operational",
		},
		"version": "2.0.0",
		"mode":    "revolutionary",
	})
}

// handleBrainProcess AI大脑处理
func (s *RevolutionaryAIOpsServer) handleBrainProcess(c *gin.Context) {
	var req ai_brain.OperationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置请求ID和时间戳
	if req.ID == "" {
		req.ID = fmt.Sprintf("req_%d", time.Now().UnixNano())
	}
	req.Timestamp = time.Now()

	// 处理请求
	response, err := s.aiBrain.ProcessOperation(c.Request.Context(), &req)
	if err != nil {
		s.logger.WithError(err).Error("AI brain processing failed")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// handleIntentRecognition 意图识别
func (s *RevolutionaryAIOpsServer) handleIntentRecognition(c *gin.Context) {
	var req intent_engine.IntentRecognitionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置请求ID和时间戳
	if req.ID == "" {
		req.ID = fmt.Sprintf("intent_%d", time.Now().UnixNano())
	}
	req.Timestamp = time.Now()

	// 识别意图
	response, err := s.intentEngine.RecognizeIntent(c.Request.Context(), &req)
	if err != nil {
		s.logger.WithError(err).Error("Intent recognition failed")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, response)
}

// handleChatMessage 处理聊天消息
func (s *RevolutionaryAIOpsServer) handleChatMessage(c *gin.Context) {
	var req struct {
		Message   string `json:"message"`
		SessionID string `json:"session_id"`
		UserID    int64  `json:"user_id"`
		InputType string `json:"input_type"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 1. 多模态处理
	processedInput, err := s.multimodalProcessor.ProcessInput(c.Request.Context(), req.Message, req.InputType)
	if err != nil {
		s.logger.WithError(err).Error("Multimodal processing failed")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 2. 意图识别
	intentReq := &intent_engine.IntentRecognitionRequest{
		ID:        fmt.Sprintf("chat_%d", time.Now().UnixNano()),
		UserInput: processedInput.ProcessedText,
		InputType: req.InputType,
		SessionID: req.SessionID,
		UserID:    req.UserID,
		Timestamp: time.Now(),
	}

	intentResp, err := s.intentEngine.RecognizeIntent(c.Request.Context(), intentReq)
	if err != nil {
		s.logger.WithError(err).Error("Intent recognition failed")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 3. AI大脑处理
	brainReq := &ai_brain.OperationRequest{
		ID:        fmt.Sprintf("brain_%d", time.Now().UnixNano()),
		Type:      intentResp.PrimaryIntent.Type,
		Intent:    intentResp.PrimaryIntent.Type,
		Parameters: intentResp.PrimaryIntent.Parameters,
		UserID:    req.UserID,
		SessionID: req.SessionID,
		Timestamp: time.Now(),
		Context: &ai_brain.OperationContext{
			Environment:   "production",
			TargetSystems: []string{},
			SecurityLevel: "medium",
		},
	}

	brainResp, err := s.aiBrain.ProcessOperation(c.Request.Context(), brainReq)
	if err != nil {
		s.logger.WithError(err).Error("AI brain processing failed")
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	// 构建响应
	response := gin.H{
		"message_id":       intentReq.ID,
		"processed_input":  processedInput,
		"intent_result":    intentResp,
		"brain_result":     brainResp,
		"response_text":    brainResp.Result.Message,
		"confidence":       intentResp.PrimaryIntent.Confidence,
		"processing_time":  time.Since(intentReq.Timestamp).String(),
	}

	c.JSON(http.StatusOK, response)
}

// handleIndex 主页
func (s *RevolutionaryAIOpsServer) handleIndex(c *gin.Context) {
	c.HTML(http.StatusOK, "revolutionary_index.html", gin.H{
		"title":   "Revolutionary AI Ops Platform",
		"version": "2.0.0",
	})
}

// handleWebSocket WebSocket处理
func (s *RevolutionaryAIOpsServer) handleWebSocket(c *gin.Context) {
	// WebSocket升级和处理逻辑
	c.JSON(http.StatusOK, gin.H{
		"message": "WebSocket endpoint - implementation needed",
	})
}

// 其他处理器的简化实现
func (s *RevolutionaryAIOpsServer) handleBrainStatus(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"status": "operational"})
}

func (s *RevolutionaryAIOpsServer) handleKnowledgeQuery(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Knowledge query endpoint"})
}

func (s *RevolutionaryAIOpsServer) handleSupportedIntents(c *gin.Context) {
	c.JSON(http.StatusOK, intent_engine.SupportedIntentTypes)
}

func (s *RevolutionaryAIOpsServer) handleMultimodalInput(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Multimodal input endpoint"})
}

func (s *RevolutionaryAIOpsServer) handleTextInput(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Text input endpoint"})
}

func (s *RevolutionaryAIOpsServer) handleVoiceInput(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Voice input endpoint"})
}

func (s *RevolutionaryAIOpsServer) handleImageInput(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Image input endpoint"})
}

func (s *RevolutionaryAIOpsServer) handleGestureInput(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Gesture input endpoint"})
}

func (s *RevolutionaryAIOpsServer) handleChatHistory(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Chat history endpoint"})
}

func (s *RevolutionaryAIOpsServer) handleClearSession(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"message": "Clear session endpoint"})
}

// main 主函数
func main() {
	// 创建服务器
	server, err := NewRevolutionaryAIOpsServer()
	if err != nil {
		log.Fatalf("Failed to create server: %v", err)
	}

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动服务器
	go func() {
		if err := server.Start(); err != nil && err != http.ErrServerClosed {
			log.Fatalf("Server failed to start: %v", err)
		}
	}()

	// 等待信号
	<-sigChan
	
	// 优雅关闭
	if err := server.Stop(); err != nil {
		log.Fatalf("Server failed to stop: %v", err)
	}
}
