package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"
)

// TestMessage 测试消息结构
type TestMessage struct {
	SessionID string `json:"session_id"`
	Content   string `json:"content"`
	Stream    bool   `json:"stream"`
}

// TestResponse 测试响应结构
type TestResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data"`
}

// MessageData 消息数据结构
type MessageData struct {
	Content    string  `json:"content"`
	Intent     string  `json:"intent"`
	Confidence float64 `json:"confidence"`
}

func main() {
	fmt.Println("🔧 开始验证AI运维管理平台意图识别修复效果...")

	// 等待服务器完全启动
	time.Sleep(3 * time.Second)

	// 创建测试会话
	sessionID, err := createTestSession()
	if err != nil {
		fmt.Printf("❌ 创建测试会话失败: %v\n", err)
		return
	}
	fmt.Printf("✅ 测试会话创建成功: %s\n", sessionID)

	// 测试用例
	testCases := []struct {
		name           string
		message        string
		expectedIntent string
		description    string
	}{
		{
			name:           "主机密码修改测试",
			message:        "修改**************这台主机密码为1qaz#EDC",
			expectedIntent: "database_operations",
			description:    "应该识别为主机管理操作，映射到database_operations",
		},
		{
			name:           "主机列表查询测试",
			message:        "列出主机",
			expectedIntent: "database_operations",
			description:    "应该识别为主机列表查询，映射到database_operations",
		},
		{
			name:           "添加主机测试",
			message:        "添加主机************* 用户名root 密码secure123",
			expectedIntent: "database_operations",
			description:    "应该识别为添加主机操作，映射到database_operations",
		},
		{
			name:           "查看主机状态测试",
			message:        "查看所有服务器状态",
			expectedIntent: "database_operations",
			description:    "应该识别为主机查询操作，映射到database_operations",
		},
	}

	successCount := 0
	totalTests := len(testCases)

	for i, testCase := range testCases {
		fmt.Printf("\n📋 测试 %d/%d: %s\n", i+1, totalTests, testCase.name)
		fmt.Printf("💬 用户输入: %s\n", testCase.message)
		fmt.Printf("🎯 期望意图: %s\n", testCase.expectedIntent)
		fmt.Printf("📝 测试说明: %s\n", testCase.description)

		// 发送测试消息
		success, actualIntent, confidence := sendTestMessage(testCase.message, sessionID)

		if success {
			fmt.Printf("✅ 实际意图: %s (置信度: %.2f)\n", actualIntent, confidence)

			if actualIntent == testCase.expectedIntent {
				fmt.Printf("🎉 测试通过！意图识别正确\n")
				successCount++
			} else {
				fmt.Printf("❌ 测试失败！期望 %s，实际 %s\n", testCase.expectedIntent, actualIntent)
			}
		} else {
			fmt.Printf("❌ 测试失败！无法获取响应\n")
		}

		// 等待一下再进行下一个测试
		time.Sleep(1 * time.Second)
	}

	// 计算准确率
	accuracy := float64(successCount) / float64(totalTests) * 100

	fmt.Printf("\n" + strings.Repeat("=", 60) + "\n")
	fmt.Printf("🏆 测试结果汇总\n")
	fmt.Printf("📊 总测试数: %d\n", totalTests)
	fmt.Printf("✅ 成功数: %d\n", successCount)
	fmt.Printf("❌ 失败数: %d\n", totalTests-successCount)
	fmt.Printf("🎯 准确率: %.1f%%\n", accuracy)

	if accuracy >= 95.0 {
		fmt.Printf("🎉 恭喜！意图识别准确率达到95%%+，修复成功！\n")
	} else if accuracy >= 75.0 {
		fmt.Printf("⚠️  意图识别准确率为%.1f%%，还需要进一步优化\n", accuracy)
	} else {
		fmt.Printf("🚨 意图识别准确率较低(%.1f%%)，需要重新检查修复方案\n", accuracy)
	}

	fmt.Printf(strings.Repeat("=", 60) + "\n")
}

func sendTestMessage(message, sessionID string) (bool, string, float64) {
	// 构建测试消息
	testMsg := TestMessage{
		SessionID: sessionID,
		Content:   message,
		Stream:    false,
	}

	// 序列化为JSON
	jsonData, err := json.Marshal(testMsg)
	if err != nil {
		fmt.Printf("❌ JSON序列化失败: %v\n", err)
		return false, "", 0
	}

	// 发送HTTP请求
	resp, err := http.Post("http://localhost:8080/api/v1/chat/message",
		"application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		fmt.Printf("❌ HTTP请求失败: %v\n", err)
		return false, "", 0
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		fmt.Printf("❌ 读取响应失败: %v\n", err)
		return false, "", 0
	}

	// 先打印原始响应进行调试
	fmt.Printf("🔍 消息响应: %s\n", string(body))

	// 解析响应
	var respMap map[string]interface{}
	if err := json.Unmarshal(body, &respMap); err != nil {
		fmt.Printf("❌ 解析响应失败: %v\n", err)
		return false, "", 0
	}

	// 检查响应状态
	if code, exists := respMap["code"]; exists {
		if codeFloat, ok := code.(float64); ok && codeFloat == 200 {
			// 响应成功，继续处理
		} else {
			fmt.Printf("❌ 服务器返回错误状态码: %.0f\n", codeFloat)
			if message, exists := respMap["message"]; exists {
				fmt.Printf("❌ 错误信息: %v\n", message)
			}
			return false, "", 0
		}
	} else {
		fmt.Printf("❌ 响应中没有状态码\n")
		return false, "", 0
	}

	// 提取消息数据
	if dataMap, ok := respMap["data"].(map[string]interface{}); ok {
		intent := ""
		confidence := 0.0

		if intentVal, exists := dataMap["intent"]; exists {
			intent = fmt.Sprintf("%v", intentVal)
		}

		if confVal, exists := dataMap["confidence"]; exists {
			if confFloat, ok := confVal.(float64); ok {
				confidence = confFloat
			}
		}

		return true, intent, confidence
	}

	fmt.Printf("❌ 响应数据格式异常\n")
	return false, "", 0
}

// createTestSession 创建测试会话
func createTestSession() (string, error) {
	// 创建会话请求
	createReq := map[string]interface{}{
		"title": "意图识别测试会话",
	}

	jsonData, err := json.Marshal(createReq)
	if err != nil {
		return "", fmt.Errorf("JSON序列化失败: %v", err)
	}

	// 发送创建会话请求
	resp, err := http.Post("http://localhost:8080/api/v1/chat/sessions",
		"application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", fmt.Errorf("HTTP请求失败: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("读取响应失败: %v", err)
	}

	// 解析响应
	var createResp map[string]interface{}
	if err := json.Unmarshal(body, &createResp); err != nil {
		return "", fmt.Errorf("解析响应失败: %v", err)
	}

	// 检查响应状态
	if code, exists := createResp["code"]; exists {
		if codeFloat, ok := code.(float64); ok && (codeFloat == 200 || codeFloat == 201) {
			// 提取会话ID
			if dataMap, ok := createResp["data"].(map[string]interface{}); ok {
				if sessionID, exists := dataMap["session_id"]; exists {
					return fmt.Sprintf("%v", sessionID), nil
				}
			}
		}
	}

	// 如果没有找到会话ID，打印响应内容进行调试
	fmt.Printf("🔍 创建会话响应: %s\n", string(body))
	return "", fmt.Errorf("无法从响应中提取会话ID")
}
