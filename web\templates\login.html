<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户登录 - AI对话运维管理平台</title>
    <link href="/static/vendor/bootstrap/css/bootstrap.min.css" rel="stylesheet">
    <link href="/static/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/fonts.css" rel="stylesheet">
    <link href="/static/css/main.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .login-container {
            background: white;
            border-radius: 15px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }
        
        .login-left {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
        }
        
        .login-right {
            padding: 3rem;
        }
        
        .brand-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .brand-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }
        
        .brand-description {
            opacity: 0.9;
            line-height: 1.6;
        }
        
        .login-form {
            max-width: 400px;
            margin: 0 auto;
        }
        
        .form-floating {
            margin-bottom: 1rem;
        }
        
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            padding: 0.75rem 2rem;
            font-weight: 600;
            border-radius: 50px;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.3);
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
            margin-top: 2rem;
        }
        
        .feature-list li {
            margin-bottom: 0.5rem;
            display: flex;
            align-items: center;
        }
        
        .feature-list i {
            margin-right: 0.5rem;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .alert {
            border-radius: 10px;
            border: none;
        }
        
        @media (max-width: 768px) {
            .login-left {
                display: none;
            }
            
            .login-container {
                margin: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <!-- 左侧品牌区域 -->
                <div class="col-md-6 login-left">
                    <div class="brand-logo">
                        <i class="bi bi-robot"></i>
                    </div>
                    <h1 class="brand-title">AI对话运维管理平台</h1>
                    <p class="brand-description">
                        通过自然语言与AI助手对话，轻松管理您的IT基础设施。
                        让运维工作变得更加智能、高效、简单。
                    </p>
                    <ul class="feature-list">
                        <li><i class="bi bi-check-circle"></i> 智能主机管理</li>
                        <li><i class="bi bi-check-circle"></i> AI对话操作</li>
                        <li><i class="bi bi-check-circle"></i> 实时监控告警</li>
                        <li><i class="bi bi-check-circle"></i> 统计分析报表</li>
                    </ul>
                </div>
                
                <!-- 右侧登录表单 -->
                <div class="col-md-6 login-right">
                    <div class="login-form">
                        <h2 class="text-center mb-4">
                            <i class="bi bi-person-circle text-primary"></i>
                            用户登录
                        </h2>
                        
                        <!-- 错误提示 -->
                        <div id="error-alert" class="alert alert-danger d-none" role="alert">
                            <i class="bi bi-exclamation-triangle"></i>
                            <span id="error-message"></span>
                        </div>
                        
                        <!-- 登录表单 -->
                        <form id="login-form">
                            <div class="form-floating">
                                <input type="text" class="form-control" id="username" name="username" 
                                       placeholder="用户名" required>
                                <label for="username">
                                    <i class="bi bi-person"></i> 用户名
                                </label>
                            </div>
                            
                            <div class="form-floating">
                                <input type="password" class="form-control" id="password" name="password" 
                                       placeholder="密码" required>
                                <label for="password">
                                    <i class="bi bi-lock"></i> 密码
                                </label>
                            </div>
                            
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    记住我
                                </label>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-login" id="login-btn">
                                    <span class="btn-text">
                                        <i class="bi bi-box-arrow-in-right"></i>
                                        登录
                                    </span>
                                    <span class="btn-loading d-none">
                                        <span class="spinner-border spinner-border-sm" role="status"></span>
                                        登录中...
                                    </span>
                                </button>
                            </div>
                        </form>
                        
                        <div class="text-center mt-4">
                            <small class="text-muted">
                                忘记密码？请联系系统管理员
                            </small>
                        </div>
                        
                        <!-- 演示账号 -->
                        <div class="mt-4 p-3 bg-light rounded">
                            <h6 class="text-muted mb-2">
                                <i class="bi bi-info-circle"></i>
                                演示账号
                            </h6>
                            <div class="row">
                                <div class="col-6">
                                    <small class="text-muted">管理员:</small><br>
                                    <code>admin / admin123</code>
                                </div>
                                <div class="col-6">
                                    <small class="text-muted">操作员:</small><br>
                                    <code>operator / op123</code>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript -->
    <script src="/static/vendor/bootstrap/js/bootstrap.bundle.min.js"></script>
    <script src="/static/js/main.js"></script>
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const loginForm = document.getElementById('login-form');
            const loginBtn = document.getElementById('login-btn');
            const errorAlert = document.getElementById('error-alert');
            const errorMessage = document.getElementById('error-message');

            // 检查是否已登录
            if (localStorage.getItem('access_token')) {
                window.location.href = '/';
                return;
            }

            loginForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const formData = new FormData(loginForm);
                const loginData = {
                    username: formData.get('username'),
                    password: formData.get('password'),
                    remember_me: formData.get('remember_me') === 'on'
                };

                // 显示加载状态
                setLoading(true);
                hideError();

                try {
                    const response = await fetch('/api/v1/auth/login', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(loginData)
                    });

                    const result = await response.json();

                    if (response.ok && result.code === 200) {
                        // 登录成功
                        localStorage.setItem('access_token', result.data.access_token);
                        localStorage.setItem('refresh_token', result.data.refresh_token);
                        
                        // 显示成功消息
                        Utils.showNotification('登录成功！正在跳转...', 'success');
                        
                        // 延迟跳转
                        setTimeout(() => {
                            window.location.href = '/';
                        }, 1000);
                    } else {
                        // 登录失败
                        showError(result.message || '登录失败，请检查用户名和密码');
                    }
                } catch (error) {
                    console.error('Login error:', error);
                    showError('网络错误，请稍后重试');
                } finally {
                    setLoading(false);
                }
            });

            // 设置加载状态
            function setLoading(loading) {
                const btnText = loginBtn.querySelector('.btn-text');
                const btnLoading = loginBtn.querySelector('.btn-loading');
                
                if (loading) {
                    btnText.classList.add('d-none');
                    btnLoading.classList.remove('d-none');
                    loginBtn.disabled = true;
                } else {
                    btnText.classList.remove('d-none');
                    btnLoading.classList.add('d-none');
                    loginBtn.disabled = false;
                }
            }

            // 显示错误
            function showError(message) {
                errorMessage.textContent = message;
                errorAlert.classList.remove('d-none');
            }

            // 隐藏错误
            function hideError() {
                errorAlert.classList.add('d-none');
            }

            // 演示账号快速填充
            document.addEventListener('click', function(e) {
                if (e.target.tagName === 'CODE') {
                    const credentials = e.target.textContent.split(' / ');
                    if (credentials.length === 2) {
                        document.getElementById('username').value = credentials[0];
                        document.getElementById('password').value = credentials[1];
                    }
                }
            });
        });
    </script>
</body>
</html>
