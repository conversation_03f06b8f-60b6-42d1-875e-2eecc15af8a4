package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"os"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/database"
	"aiops-platform/internal/service"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// EnhancedAIOpsServer 增强AI运维服务器
type EnhancedAIOpsServer struct {
	router              *gin.Engine
	logger              *logrus.Logger
	config              *config.Config
	db                  interface{} // 简化为interface{}
	enhancedIntegration *service.EnhancedAIIntegration
}

func main() {
	// 初始化日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	logger.SetFormatter(&logrus.TextFormatter{
		FullTimestamp: true,
	})

	logger.Info("🚀 启动增强AI运维管理平台...")

	// 加载配置
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Fatalf("加载配置失败: %v", err)
	}

	// 初始化数据库
	db, err := database.InitDatabase(cfg)
	if err != nil {
		log.Fatalf("初始化数据库失败: %v", err)
	}

	// 创建服务器
	server, err := NewEnhancedAIOpsServer(cfg, logger, db)
	if err != nil {
		log.Fatalf("创建服务器失败: %v", err)
	}

	// 启动服务器
	port := os.Getenv("PORT")
	if port == "" {
		port = "8080"
	}

	logger.WithField("port", port).Info("🌟 增强AI运维管理平台启动成功")
	
	if err := server.router.Run(":" + port); err != nil {
		log.Fatalf("启动服务器失败: %v", err)
	}
}

// NewEnhancedAIOpsServer 创建增强AI运维服务器
func NewEnhancedAIOpsServer(cfg *config.Config, logger *logrus.Logger, db interface{}) (*EnhancedAIOpsServer, error) {
	server := &EnhancedAIOpsServer{
		logger: logger,
		config: cfg,
		db:     db,
	}

	// 初始化路由
	server.initializeRouter()

	// 初始化增强AI集成
	if err := server.initializeEnhancedAI(); err != nil {
		return nil, fmt.Errorf("初始化增强AI失败: %w", err)
	}

	// 设置路由
	server.setupRoutes()

	logger.Info("✅ 增强AI运维服务器初始化完成")

	return server, nil
}

// initializeRouter 初始化路由
func (s *EnhancedAIOpsServer) initializeRouter() {
	s.router = gin.New()
	
	// 添加中间件
	s.router.Use(gin.Logger())
	s.router.Use(gin.Recovery())
	
	// CORS中间件
	s.router.Use(func(c *gin.Context) {
		c.Header("Access-Control-Allow-Origin", "*")
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Content-Type, Authorization")
		
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(204)
			return
		}
		
		c.Next()
	})
}

// initializeEnhancedAI 初始化增强AI集成
func (s *EnhancedAIOpsServer) initializeEnhancedAI() error {
	// 创建模拟的执行引擎和服务
	executionEngine := s.createMockExecutionEngine()
	hostService := s.createMockHostService()
	aiService := s.createMockAIService()

	// 创建增强AI集成
	integration, err := service.NewEnhancedAIIntegration(
		s.db.(*gorm.DB), // 类型断言，实际使用时需要正确的类型
		s.logger,
		s.config,
		executionEngine,
		hostService,
		aiService,
	)
	if err != nil {
		return fmt.Errorf("创建增强AI集成失败: %w", err)
	}

	// 验证配置
	if err := integration.ValidateConfiguration(); err != nil {
		return fmt.Errorf("配置验证失败: %w", err)
	}

	// 测试连接
	if err := integration.TestConnection(); err != nil {
		s.logger.WithError(err).Warn("DeepSeek连接测试失败，但继续启动")
	}

	s.enhancedIntegration = integration

	s.logger.Info("✅ 增强AI集成初始化完成")

	return nil
}

// setupRoutes 设置路由
func (s *EnhancedAIOpsServer) setupRoutes() {
	// 健康检查
	s.router.GET("/health", s.handleHealth)
	
	// 增强AI API
	api := s.router.Group("/api/v1")
	{
		// 聊天接口
		api.POST("/chat/message", s.handleEnhancedChatMessage)
		api.GET("/chat/health", s.handleChatHealth)
		api.GET("/chat/statistics", s.handleChatStatistics)
		
		// 系统状态
		api.GET("/system/status", s.handleSystemStatus)
		api.GET("/system/fallback", s.handleFallbackStatus)
	}

	// WebSocket接口
	s.router.GET("/ws", s.handleWebSocket)

	// 静态文件
	s.router.Static("/static", "./web/static")
	s.router.LoadHTMLGlob("web/templates/*")
	
	// 主页
	s.router.GET("/", s.handleIndex)
}

// handleEnhancedChatMessage 处理增强聊天消息
func (s *EnhancedAIOpsServer) handleEnhancedChatMessage(c *gin.Context) {
	var req struct {
		Message   string `json:"message" binding:"required"`
		SessionID string `json:"session_id"`
		UserID    int64  `json:"user_id"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 设置默认值
	if req.SessionID == "" {
		req.SessionID = fmt.Sprintf("session_%d", time.Now().Unix())
	}
	if req.UserID == 0 {
		req.UserID = 1 // 默认用户ID
	}

	// 创建处理请求
	processReq := &service.ProcessMessageRequest{
		SessionID: req.SessionID,
		UserID:    req.UserID,
		Message:   req.Message,
	}

	// 使用增强AI处理
	ctx, cancel := context.WithTimeout(c.Request.Context(), 180*time.Second)
	defer cancel()

	s.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("处理增强AI聊天请求")

	response, err := s.enhancedIntegration.ProcessMessage(ctx, processReq)
	if err != nil {
		s.logger.WithError(err).Error("增强AI处理失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "处理消息失败",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success":         true,
		"content":         response.Content,
		"intent":          response.Intent,
		"confidence":      response.Confidence,
		"token_count":     response.TokenCount,
		"processing_time": response.ProcessingTime.String(),
		"timestamp":       response.Timestamp,
		"enhanced":        true,
	})
}

// handleChatHealth 处理聊天健康检查
func (s *EnhancedAIOpsServer) handleChatHealth(c *gin.Context) {
	health := s.enhancedIntegration.GetHealthStatus()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"health":  health,
	})
}

// handleChatStatistics 处理聊天统计
func (s *EnhancedAIOpsServer) handleChatStatistics(c *gin.Context) {
	stats := s.enhancedIntegration.GetStatistics()
	
	c.JSON(http.StatusOK, gin.H{
		"success":    true,
		"statistics": stats,
	})
}

// handleSystemStatus 处理系统状态
func (s *EnhancedAIOpsServer) handleSystemStatus(c *gin.Context) {
	status := map[string]interface{}{
		"server":     "running",
		"timestamp":  time.Now(),
		"enhanced_ai": s.enhancedIntegration != nil,
	}
	
	if s.enhancedIntegration != nil {
		status["ai_health"] = s.enhancedIntegration.GetHealthStatus()
	}
	
	c.JSON(http.StatusOK, status)
}

// handleFallbackStatus 处理降级状态
func (s *EnhancedAIOpsServer) handleFallbackStatus(c *gin.Context) {
	if s.enhancedIntegration == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "增强AI集成未初始化",
		})
		return
	}
	
	fallbackManager := s.enhancedIntegration.GetFallbackManager()
	if fallbackManager == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "降级管理器未初始化",
		})
		return
	}
	
	stats := fallbackManager.GetStatistics()
	currentLevel := fallbackManager.GetCurrentLevel()
	
	c.JSON(http.StatusOK, gin.H{
		"success":       true,
		"current_level": currentLevel,
		"statistics":    stats,
	})
}

// handleWebSocket 处理WebSocket连接
func (s *EnhancedAIOpsServer) handleWebSocket(c *gin.Context) {
	if s.enhancedIntegration == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "增强AI集成未初始化",
		})
		return
	}
	
	websocketManager := s.enhancedIntegration.GetWebSocketManager()
	if websocketManager == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error": "WebSocket管理器未初始化",
		})
		return
	}
	
	// 获取用户信息（实际应用中应该从认证中获取）
	userID := int64(1)
	sessionID := fmt.Sprintf("ws_session_%d", time.Now().Unix())
	
	err := websocketManager.HandleWebSocket(c.Writer, c.Request, userID, sessionID)
	if err != nil {
		s.logger.WithError(err).Error("WebSocket连接处理失败")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error": "WebSocket连接失败",
		})
	}
}

// handleHealth 处理健康检查
func (s *EnhancedAIOpsServer) handleHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":    "healthy",
		"timestamp": time.Now(),
		"version":   "enhanced-v1.0.0",
	})
}

// handleIndex 处理主页
func (s *EnhancedAIOpsServer) handleIndex(c *gin.Context) {
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title":    "增强AI运维管理平台",
		"enhanced": true,
	})
}

// 模拟服务创建方法（实际使用时需要替换为真实实现）

func (s *EnhancedAIOpsServer) createMockExecutionEngine() *service.UnifiedExecutionEngine {
	// 返回nil，实际使用时需要创建真实的执行引擎
	return nil
}

func (s *EnhancedAIOpsServer) createMockHostService() service.HostService {
	// 返回nil，实际使用时需要创建真实的主机服务
	return nil
}

func (s *EnhancedAIOpsServer) createMockAIService() service.AIService {
	// 返回nil，实际使用时需要创建真实的AI服务
	return nil
}
