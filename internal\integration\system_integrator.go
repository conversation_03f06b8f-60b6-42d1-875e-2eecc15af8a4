package integration

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops-platform/internal/ai"
	"aiops-platform/internal/cache"
	"aiops-platform/internal/enterprise"
	"aiops-platform/internal/metrics"
	"aiops-platform/internal/observability"
	"aiops-platform/internal/pool"
	"aiops-platform/internal/service"
	"github.com/sirupsen/logrus"
)

// SystemIntegrator 系统集成器
type SystemIntegrator struct {
	config           *IntegrationConfig
	logger           *logrus.Logger
	
	// 核心组件
	unifiedAI        *ai.UnifiedAIService
	cacheSystem      *cache.MultiLayerCache
	haManager        *enterprise.HighAvailabilityManager
	multiTenant      *enterprise.MultiTenantManager
	securityMgr      *enterprise.SecurityEnhancementManager
	tracingSystem    *observability.TracingSystem
	perfMonitor      *metrics.PerformanceMonitor
	connectionPool   *pool.GenericConnectionPool
	
	// 服务管理
	services         map[string]Service
	healthCheckers   map[string]HealthChecker
	
	// 状态管理
	initialized      bool
	running          bool
	mutex            sync.RWMutex
}

// IntegrationConfig 集成配置
type IntegrationConfig struct {
	// 服务配置
	EnableHA            bool `json:"enable_ha"`
	EnableMultiTenant   bool `json:"enable_multi_tenant"`
	EnableSecurity      bool `json:"enable_security"`
	EnableTracing       bool `json:"enable_tracing"`
	EnableMetrics       bool `json:"enable_metrics"`
	EnableCaching       bool `json:"enable_caching"`
	
	// 启动配置
	StartupTimeout      time.Duration `json:"startup_timeout"`
	ShutdownTimeout     time.Duration `json:"shutdown_timeout"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	
	// 集成配置
	ServiceDependencies map[string][]string `json:"service_dependencies"`
	InitializationOrder []string            `json:"initialization_order"`
	
	// 监控配置
	EnableHealthEndpoint bool `json:"enable_health_endpoint"`
	EnableMetricsEndpoint bool `json:"enable_metrics_endpoint"`
	EnableDebugEndpoint   bool `json:"enable_debug_endpoint"`
}

// Service 服务接口
type Service interface {
	Name() string
	Initialize(ctx context.Context) error
	Start(ctx context.Context) error
	Stop(ctx context.Context) error
	Health() HealthStatus
	Dependencies() []string
}

// HealthChecker 健康检查器接口
type HealthChecker interface {
	Check(ctx context.Context) HealthStatus
}

// HealthStatus 健康状态
type HealthStatus struct {
	Status    string                 `json:"status"`
	Timestamp time.Time              `json:"timestamp"`
	Details   map[string]interface{} `json:"details"`
	Error     string                 `json:"error,omitempty"`
}

// ServiceWrapper 服务包装器
type ServiceWrapper struct {
	name         string
	service      interface{}
	dependencies []string
	initialized  bool
	running      bool
	healthCheck  func(ctx context.Context) HealthStatus
	mutex        sync.RWMutex
}

// NewSystemIntegrator 创建系统集成器
func NewSystemIntegrator(config *IntegrationConfig, logger *logrus.Logger) *SystemIntegrator {
	if config == nil {
		config = &IntegrationConfig{
			EnableHA:            true,
			EnableMultiTenant:   true,
			EnableSecurity:      true,
			EnableTracing:       true,
			EnableMetrics:       true,
			EnableCaching:       true,
			StartupTimeout:      5 * time.Minute,
			ShutdownTimeout:     2 * time.Minute,
			HealthCheckInterval: 30 * time.Second,
			ServiceDependencies: map[string][]string{
				"cache":       {},
				"security":    {"cache"},
				"tracing":     {"cache"},
				"metrics":     {"cache"},
				"ha":          {"cache", "security"},
				"multi_tenant": {"cache", "security", "ha"},
				"unified_ai":  {"cache", "security", "tracing", "metrics"},
			},
			InitializationOrder: []string{
				"cache", "security", "tracing", "metrics", 
				"ha", "multi_tenant", "unified_ai",
			},
			EnableHealthEndpoint:  true,
			EnableMetricsEndpoint: true,
			EnableDebugEndpoint:   false,
		}
	}

	si := &SystemIntegrator{
		config:         config,
		logger:         logger,
		services:       make(map[string]Service),
		healthCheckers: make(map[string]HealthChecker),
	}

	logger.WithFields(logrus.Fields{
		"ha_enabled":          config.EnableHA,
		"multi_tenant":        config.EnableMultiTenant,
		"security_enabled":    config.EnableSecurity,
		"tracing_enabled":     config.EnableTracing,
		"metrics_enabled":     config.EnableMetrics,
		"caching_enabled":     config.EnableCaching,
	}).Info("System integrator created")

	return si
}

// Initialize 初始化系统
func (si *SystemIntegrator) Initialize(ctx context.Context) error {
	si.mutex.Lock()
	defer si.mutex.Unlock()

	if si.initialized {
		return fmt.Errorf("system already initialized")
	}

	si.logger.Info("Starting system initialization")

	// 设置超时上下文
	initCtx, cancel := context.WithTimeout(ctx, si.config.StartupTimeout)
	defer cancel()

	// 按依赖顺序初始化组件
	if err := si.initializeComponents(initCtx); err != nil {
		return fmt.Errorf("failed to initialize components: %w", err)
	}

	// 注册服务
	if err := si.registerServices(); err != nil {
		return fmt.Errorf("failed to register services: %w", err)
	}

	// 设置健康检查
	si.setupHealthCheckers()

	si.initialized = true
	si.logger.Info("System initialization completed successfully")

	return nil
}

// initializeComponents 初始化组件
func (si *SystemIntegrator) initializeComponents(ctx context.Context) error {
	// 1. 初始化缓存系统
	if si.config.EnableCaching {
		cacheConfig := &cache.CacheConfig{
			L1MaxSize:       1000,
			L1TTL:           5 * time.Minute,
			L1Enabled:       true,
			L2MaxSize:       5000,
			L2TTL:           15 * time.Minute,
			L2Enabled:       true,
			L3MaxSize:       10000,
			L3TTL:           1 * time.Hour,
			L3Enabled:       true,
			L3Path:          "./cache",
			EnableStats:     true,
			CleanupInterval: 1 * time.Minute,
		}
		si.cacheSystem = cache.NewMultiLayerCache(cacheConfig, si.logger)
		si.logger.Info("Cache system initialized")
	}

	// 2. 初始化安全管理器
	if si.config.EnableSecurity {
		securityConfig := &enterprise.SecurityConfig{
			EnableEncryption:      true,
			EnableAuditLogging:    true,
			EnableThreatDetection: true,
			EnableCompliance:      true,
			EncryptionAlgorithm:   "AES-256-GCM",
			KeyRotationInterval:   24 * time.Hour,
			AuditRetentionDays:    90,
			MaxLoginAttempts:      5,
			SessionTimeout:        30 * time.Minute,
			ComplianceStandards:   []string{"SOC2", "ISO27001", "GDPR"},
		}
		si.securityMgr = enterprise.NewSecurityEnhancementManager(securityConfig, si.logger)
		si.logger.Info("Security manager initialized")
	}

	// 3. 初始化追踪系统
	if si.config.EnableTracing {
		tracingConfig := &observability.TracingConfig{
			ServiceName:     "aiops-platform",
			SamplingRate:    1.0,
			MaxSpans:        10000,
			RetentionPeriod: 24 * time.Hour,
			EnableMetrics:   true,
			EnableLogging:   true,
		}
		si.tracingSystem = observability.NewTracingSystem(tracingConfig, si.logger)
		si.logger.Info("Tracing system initialized")
	}

	// 4. 初始化性能监控
	if si.config.EnableMetrics {
		metricsConfig := &metrics.MonitorConfig{
			CollectionInterval: 10 * time.Second,
			RetentionPeriod:    24 * time.Hour,
			EnableCPUMonitor:   true,
			EnableMemMonitor:   true,
			EnableGCMonitor:    true,
			EnableGoRoutines:   true,
			AlertThresholds: &metrics.AlertThresholds{
				CPUUsagePercent: 80.0,
				MemoryUsageMB:   1024,
				GoroutineCount:  10000,
				GCPauseTimeMS:   100.0,
				ResponseTimeMS:  1000.0,
			},
		}
		si.perfMonitor = metrics.NewPerformanceMonitor(metricsConfig, si.logger)
		si.perfMonitor.Start()
		si.logger.Info("Performance monitor initialized")
	}

	// 5. 初始化高可用管理器
	if si.config.EnableHA {
		haConfig := &enterprise.HAConfig{
			ClusterName:         "aiops-cluster",
			NodeID:              generateNodeID(),
			EnableAutoFailover:  true,
			EnableReplication:   true,
			HealthCheckInterval: 30 * time.Second,
			FailoverTimeout:     5 * time.Minute,
			ReplicationDelay:    1 * time.Second,
			MinActiveNodes:      2,
			MaxFailoverAttempts: 3,
		}
		si.haManager = enterprise.NewHighAvailabilityManager(haConfig, si.logger)
		if err := si.haManager.Start(ctx); err != nil {
			return fmt.Errorf("failed to start HA manager: %w", err)
		}
		si.logger.Info("High availability manager initialized")
	}

	// 6. 初始化多租户管理器
	if si.config.EnableMultiTenant {
		tenantConfig := &enterprise.TenantConfig{
			EnableResourceIsolation: true,
			EnableDataIsolation:     true,
			EnableBilling:          true,
			DefaultQuotas: &enterprise.ResourceQuota{
				MaxCPUCores:      4,
				MaxMemoryMB:      8192,
				MaxStorageGB:     100,
				MaxNetworkMbps:   100,
				MaxHosts:         50,
				MaxUsers:         10,
				MaxAPIRequests:   10000,
				MaxConcurrentOps: 10,
				MaxRetentionDays: 30,
			},
			MaxTenantsPerNode: 100,
			TenantTimeout:     30 * time.Minute,
		}
		si.multiTenant = enterprise.NewMultiTenantManager(tenantConfig, si.logger)
		si.logger.Info("Multi-tenant manager initialized")
	}

	return nil
}

// registerServices 注册服务
func (si *SystemIntegrator) registerServices() error {
	// 注册缓存服务
	if si.cacheSystem != nil {
		cacheService := &ServiceWrapper{
			name:        "cache",
			service:     si.cacheSystem,
			dependencies: []string{},
			healthCheck: func(ctx context.Context) HealthStatus {
				stats := si.cacheSystem.GetStats()
				return HealthStatus{
					Status:    "healthy",
					Timestamp: time.Now(),
					Details: map[string]interface{}{
						"hit_rate":     si.cacheSystem.GetHitRate(),
						"total_hits":   stats.L1Hits + stats.L2Hits + stats.L3Hits,
						"total_misses": stats.Misses,
					},
				}
			},
		}
		si.services["cache"] = cacheService
	}

	// 注册安全服务
	if si.securityMgr != nil {
		securityService := &ServiceWrapper{
			name:         "security",
			service:      si.securityMgr,
			dependencies: []string{"cache"},
			healthCheck: func(ctx context.Context) HealthStatus {
				return HealthStatus{
					Status:    "healthy",
					Timestamp: time.Now(),
					Details: map[string]interface{}{
						"encryption_enabled": true,
						"audit_enabled":      true,
						"threat_detection":   true,
					},
				}
			},
		}
		si.services["security"] = securityService
	}

	// 注册追踪服务
	if si.tracingSystem != nil {
		tracingService := &ServiceWrapper{
			name:         "tracing",
			service:      si.tracingSystem,
			dependencies: []string{"cache"},
			healthCheck: func(ctx context.Context) HealthStatus {
				stats := si.tracingSystem.GetStats()
				return HealthStatus{
					Status:    "healthy",
					Timestamp: time.Now(),
					Details:   stats,
				}
			},
		}
		si.services["tracing"] = tracingService
	}

	// 注册性能监控服务
	if si.perfMonitor != nil {
		metricsService := &ServiceWrapper{
			name:         "metrics",
			service:      si.perfMonitor,
			dependencies: []string{"cache"},
			healthCheck: func(ctx context.Context) HealthStatus {
				metrics := si.perfMonitor.GetMetrics()
				return HealthStatus{
					Status:    "healthy",
					Timestamp: time.Now(),
					Details: map[string]interface{}{
						"memory_usage_mb": metrics.MemoryUsage / 1024 / 1024,
						"goroutines":      metrics.GoroutineCount,
						"uptime":          metrics.Uptime.String(),
					},
				}
			},
		}
		si.services["metrics"] = metricsService
	}

	// 注册高可用服务
	if si.haManager != nil {
		haService := &ServiceWrapper{
			name:         "ha",
			service:      si.haManager,
			dependencies: []string{"cache", "security"},
			healthCheck: func(ctx context.Context) HealthStatus {
				clusterState := si.haManager.GetClusterState()
				return HealthStatus{
					Status:    "healthy",
					Timestamp: time.Now(),
					Details: map[string]interface{}{
						"cluster_health": clusterState.ClusterHealth,
						"active_nodes":   clusterState.ActiveNodes,
						"total_nodes":    clusterState.TotalNodes,
						"current_role":   si.haManager.GetCurrentRole(),
					},
				}
			},
		}
		si.services["ha"] = haService
	}

	// 注册多租户服务
	if si.multiTenant != nil {
		tenantService := &ServiceWrapper{
			name:         "multi_tenant",
			service:      si.multiTenant,
			dependencies: []string{"cache", "security", "ha"},
			healthCheck: func(ctx context.Context) HealthStatus {
				return HealthStatus{
					Status:    "healthy",
					Timestamp: time.Now(),
					Details: map[string]interface{}{
						"tenant_count": 0, // 实际应该从多租户管理器获取
						"isolation":    "enabled",
						"billing":      "enabled",
					},
				}
			},
		}
		si.services["multi_tenant"] = tenantService
	}

	si.logger.WithField("service_count", len(si.services)).Info("Services registered successfully")
	return nil
}

// setupHealthCheckers 设置健康检查器
func (si *SystemIntegrator) setupHealthCheckers() {
	for name, service := range si.services {
		if wrapper, ok := service.(*ServiceWrapper); ok && wrapper.healthCheck != nil {
			si.healthCheckers[name] = &HealthCheckerWrapper{
				name:        name,
				checkFunc:   wrapper.healthCheck,
			}
		}
	}

	si.logger.WithField("checker_count", len(si.healthCheckers)).Info("Health checkers setup completed")
}

// Start 启动系统
func (si *SystemIntegrator) Start(ctx context.Context) error {
	si.mutex.Lock()
	defer si.mutex.Unlock()

	if !si.initialized {
		return fmt.Errorf("system not initialized")
	}

	if si.running {
		return fmt.Errorf("system already running")
	}

	si.logger.Info("Starting integrated system")

	// 按依赖顺序启动服务
	for _, serviceName := range si.config.InitializationOrder {
		if service, exists := si.services[serviceName]; exists {
			if err := si.startService(ctx, serviceName, service); err != nil {
				return fmt.Errorf("failed to start service %s: %w", serviceName, err)
			}
		}
	}

	// 启动健康检查
	go si.healthCheckLoop(ctx)

	si.running = true
	si.logger.Info("Integrated system started successfully")

	return nil
}

// startService 启动单个服务
func (si *SystemIntegrator) startService(ctx context.Context, name string, service Service) error {
	si.logger.WithField("service", name).Info("Starting service")

	if err := service.Start(ctx); err != nil {
		return fmt.Errorf("service start failed: %w", err)
	}

	si.logger.WithField("service", name).Info("Service started successfully")
	return nil
}

// Stop 停止系统
func (si *SystemIntegrator) Stop(ctx context.Context) error {
	si.mutex.Lock()
	defer si.mutex.Unlock()

	if !si.running {
		return nil
	}

	si.logger.Info("Stopping integrated system")

	// 设置停止超时
	stopCtx, cancel := context.WithTimeout(ctx, si.config.ShutdownTimeout)
	defer cancel()

	// 反向顺序停止服务
	order := si.config.InitializationOrder
	for i := len(order) - 1; i >= 0; i-- {
		serviceName := order[i]
		if service, exists := si.services[serviceName]; exists {
			if err := si.stopService(stopCtx, serviceName, service); err != nil {
				si.logger.WithError(err).WithField("service", serviceName).Error("Failed to stop service")
			}
		}
	}

	// 停止性能监控
	if si.perfMonitor != nil {
		si.perfMonitor.Stop()
	}

	// 停止高可用管理器
	if si.haManager != nil {
		si.haManager.Stop()
	}

	si.running = false
	si.logger.Info("Integrated system stopped")

	return nil
}

// stopService 停止单个服务
func (si *SystemIntegrator) stopService(ctx context.Context, name string, service Service) error {
	si.logger.WithField("service", name).Info("Stopping service")

	if err := service.Stop(ctx); err != nil {
		return fmt.Errorf("service stop failed: %w", err)
	}

	si.logger.WithField("service", name).Info("Service stopped successfully")
	return nil
}

// healthCheckLoop 健康检查循环
func (si *SystemIntegrator) healthCheckLoop(ctx context.Context) {
	ticker := time.NewTicker(si.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			si.performHealthChecks(ctx)
		case <-ctx.Done():
			return
		}
	}
}

// performHealthChecks 执行健康检查
func (si *SystemIntegrator) performHealthChecks(ctx context.Context) {
	for name, checker := range si.healthCheckers {
		status := checker.Check(ctx)
		
		if status.Status != "healthy" {
			si.logger.WithFields(logrus.Fields{
				"service": name,
				"status":  status.Status,
				"error":   status.Error,
			}).Warn("Service health check failed")
		}
	}
}

// GetSystemHealth 获取系统健康状态
func (si *SystemIntegrator) GetSystemHealth(ctx context.Context) map[string]HealthStatus {
	health := make(map[string]HealthStatus)

	for name, checker := range si.healthCheckers {
		health[name] = checker.Check(ctx)
	}

	return health
}

// HealthCheckerWrapper 健康检查器包装器
type HealthCheckerWrapper struct {
	name      string
	checkFunc func(ctx context.Context) HealthStatus
}

// Check 执行健康检查
func (hcw *HealthCheckerWrapper) Check(ctx context.Context) HealthStatus {
	return hcw.checkFunc(ctx)
}

// 实现Service接口的方法
func (sw *ServiceWrapper) Name() string {
	return sw.name
}

func (sw *ServiceWrapper) Initialize(ctx context.Context) error {
	sw.mutex.Lock()
	defer sw.mutex.Unlock()
	
	sw.initialized = true
	return nil
}

func (sw *ServiceWrapper) Start(ctx context.Context) error {
	sw.mutex.Lock()
	defer sw.mutex.Unlock()
	
	sw.running = true
	return nil
}

func (sw *ServiceWrapper) Stop(ctx context.Context) error {
	sw.mutex.Lock()
	defer sw.mutex.Unlock()
	
	sw.running = false
	return nil
}

func (sw *ServiceWrapper) Health() HealthStatus {
	if sw.healthCheck != nil {
		return sw.healthCheck(context.Background())
	}
	
	return HealthStatus{
		Status:    "unknown",
		Timestamp: time.Now(),
	}
}

func (sw *ServiceWrapper) Dependencies() []string {
	return sw.dependencies
}

// generateNodeID 生成节点ID
func generateNodeID() string {
	return fmt.Sprintf("node_%d", time.Now().UnixNano())
}
