package service

import (
	"context"
	"regexp"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// 🚀 情感智能引擎 - 情感分析和智能响应

// EmotionalIntelligenceEngine 情感智能引擎
type EmotionalIntelligenceEngine struct {
	logger            *logrus.Logger
	emotionRecognizer *EmotionRecognizer
	emotionExpressor  *EmotionExpressor
	emotionMemory     *EmotionMemoryManager
	adaptiveResponse  *AdaptiveResponseEngine
}

// EmotionRecognizer 情感识别器
type EmotionRecognizer struct {
	logger           *logrus.Logger
	emotionPatterns  map[string][]*EmotionPattern
	intensityAnalyzer *IntensityAnalyzer
}

// EmotionPattern 情感模式
type EmotionPattern struct {
	PatternID   string   `json:"pattern_id"`
	Emotion     string   `json:"emotion"`
	Keywords    []string `json:"keywords"`
	Phrases     []string `json:"phrases"`
	Regex       string   `json:"regex"`
	Weight      float64  `json:"weight"`
	Context     string   `json:"context"`
	Confidence  float64  `json:"confidence"`
}

// IntensityAnalyzer 强度分析器
type IntensityAnalyzer struct {
	logger              *logrus.Logger
	intensityIndicators map[string]float64
}

// EmotionExpressor 情感表达器
type EmotionExpressor struct {
	logger            *logrus.Logger
	responseTemplates map[string][]*EmotionalResponseTemplate
	toneAdjuster      *ToneAdjuster
}

// EmotionalResponseTemplate 情感响应模板
type EmotionalResponseTemplate struct {
	TemplateID  string                 `json:"template_id"`
	Emotion     string                 `json:"emotion"`
	Intensity   string                 `json:"intensity"`
	Template    string                 `json:"template"`
	Tone        string                 `json:"tone"`
	Variables   []string               `json:"variables"`
	Context     string                 `json:"context"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ToneAdjuster 语调调节器
type ToneAdjuster struct {
	logger      *logrus.Logger
	toneRules   map[string]*ToneRule
	styleGuides map[string]*StyleGuide
}

// ToneRule 语调规则
type ToneRule struct {
	RuleID      string  `json:"rule_id"`
	Emotion     string  `json:"emotion"`
	Intensity   float64 `json:"intensity"`
	TargetTone  string  `json:"target_tone"`
	Adjustments map[string]string `json:"adjustments"`
}

// StyleGuide 风格指南
type StyleGuide struct {
	StyleID     string            `json:"style_id"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Rules       map[string]string `json:"rules"`
	Examples    []string          `json:"examples"`
}

// EmotionMemoryManager 情感记忆管理器
type EmotionMemoryManager struct {
	logger          *logrus.Logger
	emotionHistory  map[string][]*EmotionRecord
	emotionTrends   map[string]*EmotionTrend
	satisfactionTracker *SatisfactionTracker
}

// EmotionRecord 情感记录
type EmotionRecord struct {
	RecordID    string    `json:"record_id"`
	SessionID   string    `json:"session_id"`
	UserID      int64     `json:"user_id"`
	Emotion     string    `json:"emotion"`
	Intensity   float64   `json:"intensity"`
	Context     string    `json:"context"`
	Trigger     string    `json:"trigger"`
	Response    string    `json:"response"`
	Outcome     string    `json:"outcome"`
	Timestamp   time.Time `json:"timestamp"`
}

// EmotionTrend 情感趋势
type EmotionTrend struct {
	UserID          int64                  `json:"user_id"`
	DominantEmotion string                 `json:"dominant_emotion"`
	EmotionCounts   map[string]int         `json:"emotion_counts"`
	IntensityAvg    map[string]float64     `json:"intensity_avg"`
	TrendDirection  string                 `json:"trend_direction"`
	LastUpdated     time.Time              `json:"last_updated"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// SatisfactionTracker 满意度跟踪器
type SatisfactionTracker struct {
	logger             *logrus.Logger
	satisfactionScores map[string]float64
	feedbackHistory    map[string][]*SatisfactionFeedback
}

// SatisfactionFeedback 满意度反馈
type SatisfactionFeedback struct {
	FeedbackID  string    `json:"feedback_id"`
	SessionID   string    `json:"session_id"`
	UserID      int64     `json:"user_id"`
	Score       float64   `json:"score"`
	Category    string    `json:"category"`
	Comments    string    `json:"comments"`
	Timestamp   time.Time `json:"timestamp"`
}

// AdaptiveResponseEngine 自适应响应引擎
type AdaptiveResponseEngine struct {
	logger           *logrus.Logger
	responseStrategies map[string]*ResponseStrategy
	adaptationRules  []*AdaptationRule
}

// ResponseStrategy 响应策略
type ResponseStrategy struct {
	StrategyID  string                 `json:"strategy_id"`
	Name        string                 `json:"name"`
	Emotion     string                 `json:"emotion"`
	Approach    string                 `json:"approach"`
	Techniques  []string               `json:"techniques"`
	Effectiveness float64              `json:"effectiveness"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// AdaptationRule 适应规则
type AdaptationRule struct {
	RuleID      string                 `json:"rule_id"`
	Condition   *EmotionCondition      `json:"condition"`
	Action      *AdaptationAction      `json:"action"`
	Priority    int                    `json:"priority"`
	Enabled     bool                   `json:"enabled"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// EmotionCondition 情感条件
type EmotionCondition struct {
	Emotion         string  `json:"emotion"`
	MinIntensity    float64 `json:"min_intensity"`
	MaxIntensity    float64 `json:"max_intensity"`
	Duration        string  `json:"duration"`
	Context         string  `json:"context"`
	PreviousEmotion string  `json:"previous_emotion"`
}

// AdaptationAction 适应动作
type AdaptationAction struct {
	Type        string                 `json:"type"`
	Parameters  map[string]interface{} `json:"parameters"`
	Description string                 `json:"description"`
	Impact      string                 `json:"impact"`
}

// NewEmotionalIntelligenceEngine 创建情感智能引擎
func NewEmotionalIntelligenceEngine(logger *logrus.Logger) *EmotionalIntelligenceEngine {
	engine := &EmotionalIntelligenceEngine{
		logger: logger,
	}

	// 初始化子组件
	engine.emotionRecognizer = NewEmotionRecognizer(logger)
	engine.emotionExpressor = NewEmotionExpressor(logger)
	engine.emotionMemory = NewEmotionMemoryManager(logger)
	engine.adaptiveResponse = NewAdaptiveResponseEngine(logger)

	logger.Info("🚀 情感智能引擎初始化完成")
	return engine
}

// AnalyzeEmotion 分析情感
func (eie *EmotionalIntelligenceEngine) AnalyzeEmotion(ctx context.Context, message string, currentState *EmotionalState) (*EmotionalState, error) {
	eie.logger.WithFields(logrus.Fields{
		"message_length": len(message),
		"current_emotion": func() string {
			if currentState != nil {
				return currentState.PrimaryEmotion
			}
			return "unknown"
		}(),
	}).Info("🎯 开始情感分析")

	// 1. 识别情感
	recognizedEmotion, confidence := eie.emotionRecognizer.RecognizeEmotion(message)

	// 2. 分析强度
	intensity := eie.emotionRecognizer.intensityAnalyzer.AnalyzeIntensity(message, recognizedEmotion)

	// 3. 计算压力水平
	stressLevel := eie.calculateStressLevel(message, recognizedEmotion, intensity)

	// 4. 评估参与度
	engagementLevel := eie.calculateEngagementLevel(message, recognizedEmotion)

	// 5. 计算满意度分数
	satisfactionScore := eie.calculateSatisfactionScore(recognizedEmotion, intensity, currentState)

	// 构建情感状态
	emotionalState := &EmotionalState{
		PrimaryEmotion:    recognizedEmotion,
		IntensityLevel:    intensity,
		StressLevel:       stressLevel,
		EngagementLevel:   engagementLevel,
		SatisfactionScore: satisfactionScore,
	}

	eie.logger.WithFields(logrus.Fields{
		"emotion":     recognizedEmotion,
		"intensity":   intensity,
		"confidence":  confidence,
		"stress":      stressLevel,
		"engagement":  engagementLevel,
		"satisfaction": satisfactionScore,
	}).Info("🚀 情感分析完成")

	return emotionalState, nil
}

// NewEmotionRecognizer 创建情感识别器
func NewEmotionRecognizer(logger *logrus.Logger) *EmotionRecognizer {
	recognizer := &EmotionRecognizer{
		logger:          logger,
		emotionPatterns: make(map[string][]*EmotionPattern),
		intensityAnalyzer: NewIntensityAnalyzer(logger),
	}

	// 初始化情感模式
	recognizer.initializeEmotionPatterns()

	return recognizer
}

// RecognizeEmotion 识别情感
func (er *EmotionRecognizer) RecognizeEmotion(message string) (string, float64) {
	message = strings.ToLower(message)
	emotionScores := make(map[string]float64)

	// 遍历所有情感模式
	for emotion, patterns := range er.emotionPatterns {
		score := 0.0
		for _, pattern := range patterns {
			if er.matchPattern(message, pattern) {
				score += pattern.Weight
			}
		}
		if score > 0 {
			emotionScores[emotion] = score
		}
	}

	// 找到得分最高的情感
	maxScore := 0.0
	dominantEmotion := "neutral"
	for emotion, score := range emotionScores {
		if score > maxScore {
			maxScore = score
			dominantEmotion = emotion
		}
	}

	// 计算置信度
	confidence := maxScore / 10.0 // 简化的置信度计算
	if confidence > 1.0 {
		confidence = 1.0
	}

	return dominantEmotion, confidence
}

// matchPattern 匹配模式
func (er *EmotionRecognizer) matchPattern(message string, pattern *EmotionPattern) bool {
	// 关键词匹配
	for _, keyword := range pattern.Keywords {
		if strings.Contains(message, strings.ToLower(keyword)) {
			return true
		}
	}

	// 短语匹配
	for _, phrase := range pattern.Phrases {
		if strings.Contains(message, strings.ToLower(phrase)) {
			return true
		}
	}

	// 正则表达式匹配
	if pattern.Regex != "" {
		if matched, _ := regexp.MatchString(pattern.Regex, message); matched {
			return true
		}
	}

	return false
}

// initializeEmotionPatterns 初始化情感模式
func (er *EmotionRecognizer) initializeEmotionPatterns() {
	// 沮丧情感模式
	er.emotionPatterns["frustrated"] = []*EmotionPattern{
		{
			PatternID: "frustrated_001",
			Emotion:   "frustrated",
			Keywords:  []string{"不行", "失败", "错误", "问题", "困难", "复杂"},
			Phrases:   []string{"试了好几次", "总是出错", "太复杂了", "不知道怎么办"},
			Weight:    3.0,
		},
		{
			PatternID: "frustrated_002",
			Emotion:   "frustrated",
			Regex:     `(?i)(为什么.*不.*|怎么.*都.*不.*|.*不.*工作)`,
			Weight:    2.5,
		},
	}

	// 好奇情感模式
	er.emotionPatterns["curious"] = []*EmotionPattern{
		{
			PatternID: "curious_001",
			Emotion:   "curious",
			Keywords:  []string{"学习", "了解", "知道", "教", "解释"},
			Phrases:   []string{"想学习", "能教我", "怎么做", "是什么"},
			Weight:    2.0,
		},
	}

	// 自信情感模式
	er.emotionPatterns["confident"] = []*EmotionPattern{
		{
			PatternID: "confident_001",
			Emotion:   "confident",
			Keywords:  []string{"高级", "复杂", "专业", "优化", "定制"},
			Phrases:   []string{"我需要", "我想要", "帮我设置"},
			Weight:    2.0,
		},
	}

	// 困惑情感模式
	er.emotionPatterns["confused"] = []*EmotionPattern{
		{
			PatternID: "confused_001",
			Emotion:   "confused",
			Keywords:  []string{"不懂", "不明白", "搞不清", "混乱"},
			Phrases:   []string{"不太理解", "有点困惑", "不清楚"},
			Weight:    2.5,
		},
	}

	// 中性情感模式
	er.emotionPatterns["neutral"] = []*EmotionPattern{
		{
			PatternID: "neutral_001",
			Emotion:   "neutral",
			Keywords:  []string{"查看", "显示", "列出", "检查"},
			Weight:    1.0,
		},
	}
}

// NewIntensityAnalyzer 创建强度分析器
func NewIntensityAnalyzer(logger *logrus.Logger) *IntensityAnalyzer {
	return &IntensityAnalyzer{
		logger: logger,
		intensityIndicators: map[string]float64{
			"!":     0.2,
			"!!":    0.4,
			"!!!":   0.6,
			"？？":    0.3,
			"太":     0.3,
			"非常":    0.4,
			"极其":    0.5,
			"完全":    0.4,
			"总是":    0.3,
			"从来":    0.4,
		},
	}
}

// AnalyzeIntensity 分析强度
func (ia *IntensityAnalyzer) AnalyzeIntensity(message, emotion string) float64 {
	baseIntensity := 0.5 // 基础强度

	// 检查强度指示符
	for indicator, weight := range ia.intensityIndicators {
		if strings.Contains(message, indicator) {
			baseIntensity += weight
		}
	}

	// 根据情感类型调整
	switch emotion {
	case "frustrated":
		baseIntensity += 0.2
	case "excited":
		baseIntensity += 0.3
	case "angry":
		baseIntensity += 0.4
	}

	// 限制在0-1范围内
	if baseIntensity > 1.0 {
		baseIntensity = 1.0
	}
	if baseIntensity < 0.0 {
		baseIntensity = 0.0
	}

	return baseIntensity
}

// 辅助方法
func (eie *EmotionalIntelligenceEngine) calculateStressLevel(message, emotion string, intensity float64) float64 {
	stressLevel := 0.3 // 基础压力水平

	switch emotion {
	case "frustrated", "angry", "anxious":
		stressLevel = 0.7 + intensity*0.3
	case "confused", "worried":
		stressLevel = 0.5 + intensity*0.2
	case "happy", "excited", "confident":
		stressLevel = 0.2 + intensity*0.1
	}

	if stressLevel > 1.0 {
		stressLevel = 1.0
	}

	return stressLevel
}

func (eie *EmotionalIntelligenceEngine) calculateEngagementLevel(message, emotion string) float64 {
	engagementLevel := 0.5 // 基础参与度

	// 根据消息长度调整
	messageLength := len(message)
	if messageLength > 100 {
		engagementLevel += 0.2
	} else if messageLength < 20 {
		engagementLevel -= 0.1
	}

	// 根据情感调整
	switch emotion {
	case "curious", "excited", "interested":
		engagementLevel += 0.3
	case "frustrated", "bored":
		engagementLevel -= 0.2
	}

	if engagementLevel > 1.0 {
		engagementLevel = 1.0
	}
	if engagementLevel < 0.0 {
		engagementLevel = 0.0
	}

	return engagementLevel
}

func (eie *EmotionalIntelligenceEngine) calculateSatisfactionScore(emotion string, intensity float64, currentState *EmotionalState) float64 {
	satisfactionScore := 0.7 // 基础满意度

	switch emotion {
	case "happy", "satisfied", "grateful":
		satisfactionScore = 0.8 + intensity*0.2
	case "frustrated", "angry", "disappointed":
		satisfactionScore = 0.3 - intensity*0.2
	case "confused", "uncertain":
		satisfactionScore = 0.5 - intensity*0.1
	}

	// 考虑历史状态
	if currentState != nil {
		// 如果情感有改善，提高满意度
		if eie.isEmotionImproving(currentState.PrimaryEmotion, emotion) {
			satisfactionScore += 0.1
		}
	}

	if satisfactionScore > 1.0 {
		satisfactionScore = 1.0
	}
	if satisfactionScore < 0.0 {
		satisfactionScore = 0.0
	}

	return satisfactionScore
}

func (eie *EmotionalIntelligenceEngine) isEmotionImproving(previousEmotion, currentEmotion string) bool {
	// 简化的情感改善判断
	improvementMap := map[string][]string{
		"frustrated": {"curious", "neutral", "confident"},
		"confused":   {"curious", "confident", "neutral"},
		"angry":      {"neutral", "calm", "satisfied"},
	}

	if improvements, exists := improvementMap[previousEmotion]; exists {
		for _, improvement := range improvements {
			if currentEmotion == improvement {
				return true
			}
		}
	}

	return false
}

// 创建其他子组件的构造函数
func NewEmotionExpressor(logger *logrus.Logger) *EmotionExpressor {
	return &EmotionExpressor{
		logger:            logger,
		responseTemplates: make(map[string][]*EmotionalResponseTemplate),
		toneAdjuster:      NewToneAdjuster(logger),
	}
}

func NewToneAdjuster(logger *logrus.Logger) *ToneAdjuster {
	return &ToneAdjuster{
		logger:      logger,
		toneRules:   make(map[string]*ToneRule),
		styleGuides: make(map[string]*StyleGuide),
	}
}

func NewEmotionMemoryManager(logger *logrus.Logger) *EmotionMemoryManager {
	return &EmotionMemoryManager{
		logger:              logger,
		emotionHistory:      make(map[string][]*EmotionRecord),
		emotionTrends:       make(map[string]*EmotionTrend),
		satisfactionTracker: NewSatisfactionTracker(logger),
	}
}

func NewSatisfactionTracker(logger *logrus.Logger) *SatisfactionTracker {
	return &SatisfactionTracker{
		logger:             logger,
		satisfactionScores: make(map[string]float64),
		feedbackHistory:    make(map[string][]*SatisfactionFeedback),
	}
}

func NewAdaptiveResponseEngine(logger *logrus.Logger) *AdaptiveResponseEngine {
	return &AdaptiveResponseEngine{
		logger:             logger,
		responseStrategies: make(map[string]*ResponseStrategy),
		adaptationRules:    []*AdaptationRule{},
	}
}
