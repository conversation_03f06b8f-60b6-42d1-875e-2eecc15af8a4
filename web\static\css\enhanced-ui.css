/**
 * Enhanced UI Styles for AI Ops Platform
 * 增强的用户界面样式
 */

/* ===== 全局变量 ===== */
:root {
  /* 主色调 */
  --primary-color: #007bff;
  --primary-dark: #0056b3;
  --primary-light: #66b3ff;
  
  /* 辅助色调 */
  --success-color: #28a745;
  --warning-color: #ffc107;
  --danger-color: #dc3545;
  --info-color: #17a2b8;
  
  /* 中性色调 */
  --dark-color: #343a40;
  --light-color: #f8f9fa;
  --muted-color: #6c757d;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-dark: #2c3e50;
  --bg-card: #ffffff;
  
  /* 边框和阴影 */
  --border-color: #dee2e6;
  --border-radius: 8px;
  --box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  --box-shadow-hover: 0 4px 20px rgba(0, 0, 0, 0.15);
  
  /* 字体 */
  --font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-size-base: 14px;
  --font-size-sm: 12px;
  --font-size-lg: 16px;
  --font-weight-normal: 400;
  --font-weight-bold: 600;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
  
  /* 动画 */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* ===== 基础样式重置 ===== */
* {
  box-sizing: border-box;
}

body {
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  line-height: 1.5;
  color: var(--dark-color);
  background-color: var(--bg-secondary);
  margin: 0;
  padding: 0;
}

/* ===== 智能聊天组件样式 ===== */
.chat-container {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.chat-header {
  padding: var(--spacing-md);
  background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
  color: white;
  font-weight: var(--font-weight-bold);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.chat-messages {
  flex: 1;
  padding: var(--spacing-md);
  overflow-y: auto;
  scroll-behavior: smooth;
}

.message {
  margin-bottom: var(--spacing-md);
  animation: messageSlideIn var(--transition-normal);
}

.message.user {
  text-align: right;
}

.message.assistant {
  text-align: left;
}

.message-content {
  display: inline-block;
  max-width: 80%;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius);
  position: relative;
}

.message.user .message-content {
  background: var(--primary-color);
  color: white;
  border-bottom-right-radius: 4px;
}

.message.assistant .message-content {
  background: var(--bg-secondary);
  color: var(--dark-color);
  border-bottom-left-radius: 4px;
}

.message-text {
  margin-bottom: var(--spacing-xs);
}

.message-time {
  font-size: var(--font-size-sm);
  opacity: 0.7;
}

.typing-indicator {
  display: flex;
  align-items: center;
  padding: var(--spacing-md);
  color: var(--muted-color);
}

.typing-dots {
  display: flex;
  gap: var(--spacing-xs);
}

.typing-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--muted-color);
  animation: typingDots 1.4s infinite ease-in-out;
}

.typing-dots span:nth-child(1) { animation-delay: -0.32s; }
.typing-dots span:nth-child(2) { animation-delay: -0.16s; }

.chat-input-container {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background: var(--bg-primary);
}

.chat-input {
  width: 100%;
  min-height: 40px;
  max-height: 120px;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  resize: none;
  font-family: inherit;
  font-size: inherit;
  transition: border-color var(--transition-fast);
}

.chat-input:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

.chat-suggestions {
  position: absolute;
  bottom: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-bottom: none;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  box-shadow: var(--box-shadow);
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.suggestion-item {
  padding: var(--spacing-sm) var(--spacing-md);
  cursor: pointer;
  transition: background-color var(--transition-fast);
}

.suggestion-item:hover {
  background-color: var(--bg-secondary);
}

/* ===== 实时监控面板样式 ===== */
.monitoring-panel {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

.chart-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
}

.chart-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-hover);
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
  border-bottom: 1px solid var(--border-color);
}

.chart-header h6 {
  margin: 0;
  font-weight: var(--font-weight-bold);
  color: var(--dark-color);
}

.chart-value {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
}

.chart-body {
  height: 200px;
  position: relative;
}

/* ===== 通知系统样式 ===== */
.notification-container {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  z-index: 9999;
  max-width: 400px;
}

.notification {
  background: var(--bg-primary);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  display: flex;
  align-items: flex-start;
  transform: translateX(100%);
  opacity: 0;
  transition: all var(--transition-normal);
  border-left: 4px solid var(--info-color);
}

.notification.show {
  transform: translateX(0);
  opacity: 1;
}

.notification.hide {
  transform: translateX(100%);
  opacity: 0;
}

.notification-success {
  border-left-color: var(--success-color);
}

.notification-warning {
  border-left-color: var(--warning-color);
}

.notification-error {
  border-left-color: var(--danger-color);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: var(--font-weight-bold);
  margin-bottom: var(--spacing-xs);
  color: var(--dark-color);
}

.notification-message {
  color: var(--muted-color);
  font-size: var(--font-size-sm);
}

.notification-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: var(--muted-color);
  padding: 0;
  margin-left: var(--spacing-sm);
  transition: color var(--transition-fast);
}

.notification-close:hover {
  color: var(--dark-color);
}

/* ===== 主机管理界面样式 ===== */
.host-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

.host-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
  transition: all var(--transition-fast);
  border: 1px solid var(--border-color);
}

.host-card:hover {
  transform: translateY(-2px);
  box-shadow: var(--box-shadow-hover);
}

.host-status {
  display: inline-block;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: 12px;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
}

.host-status.online {
  background: rgba(40, 167, 69, 0.1);
  color: var(--success-color);
}

.host-status.offline {
  background: rgba(220, 53, 69, 0.1);
  color: var(--danger-color);
}

.host-status.unknown {
  background: rgba(108, 117, 125, 0.1);
  color: var(--muted-color);
}

/* ===== 性能仪表板样式 ===== */
.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(12, 1fr);
  gap: var(--spacing-lg);
  padding: var(--spacing-lg);
}

.dashboard-card {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  padding: var(--spacing-lg);
}

.dashboard-card.span-3 { grid-column: span 3; }
.dashboard-card.span-4 { grid-column: span 4; }
.dashboard-card.span-6 { grid-column: span 6; }
.dashboard-card.span-12 { grid-column: span 12; }

/* ===== 告警中心样式 ===== */
.alert-list {
  background: var(--bg-card);
  border-radius: var(--border-radius);
  box-shadow: var(--box-shadow);
  overflow: hidden;
}

.alert-item {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  display: flex;
  align-items: center;
  transition: background-color var(--transition-fast);
}

.alert-item:hover {
  background-color: var(--bg-secondary);
}

.alert-item:last-child {
  border-bottom: none;
}

.alert-severity {
  width: 4px;
  height: 40px;
  border-radius: 2px;
  margin-right: var(--spacing-md);
}

.alert-severity.critical { background: var(--danger-color); }
.alert-severity.high { background: var(--warning-color); }
.alert-severity.medium { background: var(--info-color); }
.alert-severity.low { background: var(--success-color); }

/* ===== 响应式设计 ===== */
@media (max-width: 768px) {
  .monitoring-panel {
    grid-template-columns: 1fr;
  }
  
  .host-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-grid {
    grid-template-columns: 1fr;
  }
  
  .dashboard-card.span-3,
  .dashboard-card.span-4,
  .dashboard-card.span-6,
  .dashboard-card.span-12 {
    grid-column: span 1;
  }
  
  .notification-container {
    left: var(--spacing-md);
    right: var(--spacing-md);
    max-width: none;
  }
}

/* ===== 动画定义 ===== */
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes typingDots {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* ===== 辅助类 ===== */
.fade-in { animation: fadeIn var(--transition-normal); }
.slide-in-right { animation: slideInRight var(--transition-normal); }
.pulse { animation: pulse 2s infinite; }

.text-primary { color: var(--primary-color) !important; }
.text-success { color: var(--success-color) !important; }
.text-warning { color: var(--warning-color) !important; }
.text-danger { color: var(--danger-color) !important; }
.text-muted { color: var(--muted-color) !important; }

.bg-primary { background-color: var(--primary-color) !important; }
.bg-success { background-color: var(--success-color) !important; }
.bg-warning { background-color: var(--warning-color) !important; }
.bg-danger { background-color: var(--danger-color) !important; }

.border-primary { border-color: var(--primary-color) !important; }
.border-success { border-color: var(--success-color) !important; }
.border-warning { border-color: var(--warning-color) !important; }
.border-danger { border-color: var(--danger-color) !important; }

/* ===== 暗色主题支持 ===== */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #2c3e50;
    --bg-secondary: #34495e;
    --bg-dark: #1a252f;
    --bg-card: #34495e;
    --dark-color: #ecf0f1;
    --border-color: #4a5568;
    --muted-color: #a0aec0;
  }
  
  body {
    background-color: var(--bg-dark);
    color: var(--dark-color);
  }
}
