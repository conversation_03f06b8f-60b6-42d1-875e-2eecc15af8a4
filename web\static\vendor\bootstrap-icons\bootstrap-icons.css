/*!
 * Bootstrap Icons v1.10.0 (https://icons.getbootstrap.com/)
 * Copyright 2019-2023 The Bootstrap Authors
 * Licensed under MIT (https://github.com/twbs/icons/blob/main/LICENSE.md)
 */

/* 基础图标样式 */
.bi {
  display: inline-block;
  width: 1em;
  height: 1em;
  fill: currentcolor;
  vertical-align: -0.125em;
}

/* 常用图标 - 使用Unicode字符作为fallback */
.bi-robot::before { content: "🤖"; }
.bi-house::before { content: "🏠"; }
.bi-server::before { content: "🖥️"; }
.bi-exclamation-triangle::before { content: "⚠️"; }
.bi-chat-dots::before { content: "💬"; }
.bi-people::before { content: "👥"; }
.bi-heart-pulse::before { content: "💓"; }
.bi-speedometer2::before { content: "📊"; }
.bi-clock-history::before { content: "🕐"; }
.bi-hourglass-split::before { content: "⏳"; }
.bi-check-circle::before { content: "✅"; }
.bi-x-circle::before { content: "❌"; }
.bi-exclamation-triangle::before { content: "⚠️"; }
.bi-info-circle::before { content: "ℹ️"; }
.bi-hdd::before { content: "💾"; }
.bi-clock::before { content: "🕐"; }
.bi-gear::before { content: "⚙️"; }
.bi-bell::before { content: "🔔"; }
.bi-person::before { content: "👤"; }
.bi-box-arrow-right::before { content: "🚪"; }
.bi-plus::before { content: "+"; }
.bi-search::before { content: "🔍"; }
.bi-filter::before { content: "🔽"; }
.bi-three-dots::before { content: "⋯"; }
.bi-pencil::before { content: "✏️"; }
.bi-trash::before { content: "🗑️"; }
.bi-eye::before { content: "👁️"; }
.bi-download::before { content: "⬇️"; }
.bi-upload::before { content: "⬆️"; }
.bi-refresh::before { content: "🔄"; }
.bi-arrow-left::before { content: "←"; }
.bi-arrow-right::before { content: "→"; }
.bi-arrow-up::before { content: "↑"; }
.bi-arrow-down::before { content: "↓"; }
.bi-chevron-left::before { content: "‹"; }
.bi-chevron-right::before { content: "›"; }
.bi-chevron-up::before { content: "⌃"; }
.bi-chevron-down::before { content: "⌄"; }
.bi-list::before { content: "☰"; }
.bi-grid::before { content: "⊞"; }
.bi-calendar::before { content: "📅"; }
.bi-envelope::before { content: "✉️"; }
.bi-telephone::before { content: "📞"; }
.bi-globe::before { content: "🌐"; }
.bi-shield::before { content: "🛡️"; }
.bi-lock::before { content: "🔒"; }
.bi-unlock::before { content: "🔓"; }
.bi-key::before { content: "🔑"; }
.bi-cpu::before { content: "🖥️"; }
.bi-memory::before { content: "💾"; }
.bi-wifi::before { content: "📶"; }
.bi-ethernet::before { content: "🔌"; }
.bi-database::before { content: "🗄️"; }
.bi-cloud::before { content: "☁️"; }
.bi-file::before { content: "📄"; }
.bi-folder::before { content: "📁"; }
.bi-image::before { content: "🖼️"; }
.bi-play::before { content: "▶️"; }
.bi-pause::before { content: "⏸️"; }
.bi-stop::before { content: "⏹️"; }
.bi-skip-backward::before { content: "⏮️"; }
.bi-skip-forward::before { content: "⏭️"; }
.bi-volume-up::before { content: "🔊"; }
.bi-volume-down::before { content: "🔉"; }
.bi-volume-mute::before { content: "🔇"; }
.bi-star::before { content: "⭐"; }
.bi-star-fill::before { content: "⭐"; }
.bi-heart::before { content: "❤️"; }
.bi-heart-fill::before { content: "❤️"; }
.bi-bookmark::before { content: "🔖"; }
.bi-bookmark-fill::before { content: "🔖"; }
.bi-flag::before { content: "🚩"; }
.bi-flag-fill::before { content: "🚩"; }
.bi-tag::before { content: "🏷️"; }
.bi-tags::before { content: "🏷️"; }
.bi-award::before { content: "🏆"; }
.bi-trophy::before { content: "🏆"; }
.bi-gift::before { content: "🎁"; }
.bi-cart::before { content: "🛒"; }
.bi-bag::before { content: "👜"; }
.bi-credit-card::before { content: "💳"; }
.bi-currency-dollar::before { content: "$"; }
.bi-currency-euro::before { content: "€"; }
.bi-currency-pound::before { content: "£"; }
.bi-currency-yen::before { content: "¥"; }
.bi-percent::before { content: "%"; }
.bi-hash::before { content: "#"; }
.bi-at::before { content: "@"; }
.bi-question::before { content: "?"; }
.bi-question-circle::before { content: "❓"; }
.bi-exclamation::before { content: "!"; }
.bi-exclamation-circle::before { content: "❗"; }
.bi-check::before { content: "✓"; }
.bi-check2::before { content: "✓"; }
.bi-x::before { content: "✗"; }
.bi-dash::before { content: "−"; }
.bi-plus-circle::before { content: "⊕"; }
.bi-dash-circle::before { content: "⊖"; }
.bi-slash-circle::before { content: "⊘"; }
.bi-circle::before { content: "○"; }
.bi-circle-fill::before { content: "●"; }
.bi-square::before { content: "□"; }
.bi-square-fill::before { content: "■"; }
.bi-triangle::before { content: "△"; }
.bi-triangle-fill::before { content: "▲"; }
.bi-diamond::before { content: "◇"; }
.bi-diamond-fill::before { content: "◆"; }
.bi-octagon::before { content: "⬟"; }
.bi-octagon-fill::before { content: "⬢"; }
.bi-hexagon::before { content: "⬡"; }
.bi-hexagon-fill::before { content: "⬢"; }
.bi-pentagon::before { content: "⬟"; }
.bi-pentagon-fill::before { content: "⬢"; }

/* 显示尺寸变体 */
.bi.fs-1 { font-size: 2.5rem; }
.bi.fs-2 { font-size: 2rem; }
.bi.fs-3 { font-size: 1.75rem; }
.bi.fs-4 { font-size: 1.5rem; }
.bi.fs-5 { font-size: 1.25rem; }
.bi.fs-6 { font-size: 1rem; }

.bi.display-1 { font-size: 6rem; }
.bi.display-2 { font-size: 5.5rem; }
.bi.display-3 { font-size: 4.5rem; }
.bi.display-4 { font-size: 3.5rem; }

/* 颜色变体 */
.bi.text-primary { color: #0d6efd; }
.bi.text-secondary { color: #6c757d; }
.bi.text-success { color: #198754; }
.bi.text-info { color: #0dcaf0; }
.bi.text-warning { color: #ffc107; }
.bi.text-danger { color: #dc3545; }
.bi.text-light { color: #f8f9fa; }
.bi.text-dark { color: #212529; }
.bi.text-muted { color: #6c757d; }
.bi.text-white { color: #fff; }

/* 动画效果 */
.bi.spin {
  animation: bi-spin 1s linear infinite;
}

@keyframes bi-spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.bi.pulse {
  animation: bi-pulse 1s ease-in-out infinite alternate;
}

@keyframes bi-pulse {
  from { opacity: 1; }
  to { opacity: 0.5; }
}

/* 特殊效果 */
.bi.flip-horizontal {
  transform: scaleX(-1);
}

.bi.flip-vertical {
  transform: scaleY(-1);
}

.bi.rotate-90 {
  transform: rotate(90deg);
}

.bi.rotate-180 {
  transform: rotate(180deg);
}

.bi.rotate-270 {
  transform: rotate(270deg);
}