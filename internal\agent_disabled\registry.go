package agent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// AgentRegistry Agent注册中心
type AgentRegistry struct {
	agents         map[string]*AgentRegistration
	agentInstances map[string]Agent
	mutex          sync.RWMutex
	logger         *logrus.Logger
	config         *RegistryConfig
	healthChecker  *HealthChecker
	eventBus       *EventBus
}

// RegistryConfig 注册中心配置
type RegistryConfig struct {
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	MaxAgents           int           `json:"max_agents"`
	RegistrationTTL     time.Duration `json:"registration_ttl"`
	CleanupInterval     time.Duration `json:"cleanup_interval"`
	EnableMetrics       bool          `json:"enable_metrics"`
}

// DefaultRegistryConfig 默认配置
func DefaultRegistryConfig() *RegistryConfig {
	return &RegistryConfig{
		HealthCheckInterval: 30 * time.Second,
		MaxAgents:           100,
		RegistrationTTL:     5 * time.Minute,
		CleanupInterval:     1 * time.Minute,
		EnableMetrics:       true,
	}
}

// NewAgentRegistry 创建Agent注册中心
func NewAgentRegistry(logger *logrus.Logger, config *RegistryConfig) *AgentRegistry {
	if config == nil {
		config = DefaultRegistryConfig()
	}

	registry := &AgentRegistry{
		agents:         make(map[string]*AgentRegistration),
		agentInstances: make(map[string]Agent),
		logger:         logger,
		config:         config,
		healthChecker:  NewHealthChecker(logger),
		eventBus:       NewEventBus(logger),
	}

	// 启动后台任务
	go registry.startHealthCheckRoutine()
	go registry.startCleanupRoutine()

	return registry
}

// RegisterAgent 注册Agent
func (ar *AgentRegistry) RegisterAgent(ctx context.Context, agent Agent) error {
	ar.mutex.Lock()
	defer ar.mutex.Unlock()

	metadata := agent.GetMetadata()
	if metadata == nil {
		return fmt.Errorf("agent metadata is required")
	}

	agentID := metadata.ID
	if !IsValidAgentID(agentID) {
		return fmt.Errorf("invalid agent ID: %s", agentID)
	}

	// 检查Agent数量限制
	if len(ar.agents) >= ar.config.MaxAgents {
		return fmt.Errorf("maximum number of agents reached: %d", ar.config.MaxAgents)
	}

	// 检查是否已注册
	if _, exists := ar.agents[agentID]; exists {
		return fmt.Errorf("agent already registered: %s", agentID)
	}

	// 验证Agent能力
	if err := ar.validateAgent(agent); err != nil {
		return fmt.Errorf("agent validation failed: %w", err)
	}

	// 创建注册信息
	registration := &AgentRegistration{
		Metadata:     metadata,
		Capabilities: agent.GetCapabilities(),
		Parameters:   append(agent.GetRequiredParameters(), agent.GetOptionalParameters()...),
		Conditions:   agent.GetExecutionConditions(),
		RegisteredAt: time.Now(),
		LastSeen:     time.Now(),
		Status:       agent.GetStatus(),
	}

	// 注册Agent
	ar.agents[agentID] = registration
	ar.agentInstances[agentID] = agent

	ar.logger.WithFields(logrus.Fields{
		"agent_id":   agentID,
		"agent_name": metadata.Name,
		"category":   metadata.Category,
	}).Info("Agent registered successfully")

	// 发送注册事件
	ar.eventBus.Publish(&AgentEvent{
		Type:      EventTypeAgentRegistered,
		AgentID:   agentID,
		Timestamp: time.Now(),
		Data:      registration,
	})

	return nil
}

// UnregisterAgent 注销Agent
func (ar *AgentRegistry) UnregisterAgent(ctx context.Context, agentID string) error {
	ar.mutex.Lock()
	defer ar.mutex.Unlock()

	registration, exists := ar.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	// 停止Agent
	if agent, exists := ar.agentInstances[agentID]; exists {
		if err := agent.Stop(ctx); err != nil {
			ar.logger.WithError(err).Warn("Failed to stop agent during unregistration")
		}
		delete(ar.agentInstances, agentID)
	}

	delete(ar.agents, agentID)

	ar.logger.WithField("agent_id", agentID).Info("Agent unregistered successfully")

	// 发送注销事件
	ar.eventBus.Publish(&AgentEvent{
		Type:      EventTypeAgentUnregistered,
		AgentID:   agentID,
		Timestamp: time.Now(),
		Data:      registration,
	})

	return nil
}

// GetAgent 获取Agent实例
func (ar *AgentRegistry) GetAgent(agentID string) (Agent, error) {
	ar.mutex.RLock()
	defer ar.mutex.RUnlock()

	agent, exists := ar.agentInstances[agentID]
	if !exists {
		return nil, fmt.Errorf("agent not found: %s", agentID)
	}

	return agent, nil
}

// GetAgentRegistration 获取Agent注册信息
func (ar *AgentRegistry) GetAgentRegistration(agentID string) (*AgentRegistration, error) {
	ar.mutex.RLock()
	defer ar.mutex.RUnlock()

	registration, exists := ar.agents[agentID]
	if !exists {
		return nil, fmt.Errorf("agent not found: %s", agentID)
	}

	// 返回副本
	regCopy := *registration
	return &regCopy, nil
}

// ListAgents 列出所有Agent
func (ar *AgentRegistry) ListAgents() []*AgentRegistration {
	ar.mutex.RLock()
	defer ar.mutex.RUnlock()

	agents := make([]*AgentRegistration, 0, len(ar.agents))
	for _, registration := range ar.agents {
		regCopy := *registration
		agents = append(agents, &regCopy)
	}

	return agents
}

// FindAgentsByCategory 按分类查找Agent
func (ar *AgentRegistry) FindAgentsByCategory(category AgentCategory) []*AgentRegistration {
	ar.mutex.RLock()
	defer ar.mutex.RUnlock()

	var agents []*AgentRegistration
	for _, registration := range ar.agents {
		if registration.Metadata.Category == category {
			regCopy := *registration
			agents = append(agents, &regCopy)
		}
	}

	return agents
}

// FindAgentsByCapability 按能力查找Agent
func (ar *AgentRegistry) FindAgentsByCapability(capability string) []*AgentRegistration {
	ar.mutex.RLock()
	defer ar.mutex.RUnlock()

	var agents []*AgentRegistration
	for _, registration := range ar.agents {
		for _, cap := range registration.Capabilities {
			if cap.Name == capability {
				regCopy := *registration
				agents = append(agents, &regCopy)
				break
			}
		}
	}

	return agents
}

// UpdateAgentStatus 更新Agent状态
func (ar *AgentRegistry) UpdateAgentStatus(agentID string, status AgentStatus) error {
	ar.mutex.Lock()
	defer ar.mutex.Unlock()

	registration, exists := ar.agents[agentID]
	if !exists {
		return fmt.Errorf("agent not found: %s", agentID)
	}

	oldStatus := registration.Status
	registration.Status = status
	registration.LastSeen = time.Now()

	ar.logger.WithFields(logrus.Fields{
		"agent_id":   agentID,
		"old_status": oldStatus,
		"new_status": status,
	}).Debug("Agent status updated")

	// 发送状态变更事件
	ar.eventBus.Publish(&AgentEvent{
		Type:      EventTypeAgentStatusChanged,
		AgentID:   agentID,
		Timestamp: time.Now(),
		Data: map[string]interface{}{
			"old_status": oldStatus,
			"new_status": status,
		},
	})

	return nil
}

// GetAgentCapabilities 获取所有Agent能力信息（供DeepSeek决策使用）
func (ar *AgentRegistry) GetAgentCapabilities() map[string][]Capability {
	ar.mutex.RLock()
	defer ar.mutex.RUnlock()

	capabilities := make(map[string][]Capability)
	for agentID, registration := range ar.agents {
		if registration.Status == StatusReady || registration.Status == StatusRunning {
			capabilities[agentID] = registration.Capabilities
		}
	}

	return capabilities
}

// GetHealthyAgents 获取健康的Agent列表
func (ar *AgentRegistry) GetHealthyAgents() []*AgentRegistration {
	ar.mutex.RLock()
	defer ar.mutex.RUnlock()

	var healthyAgents []*AgentRegistration
	for _, registration := range ar.agents {
		if registration.Status == StatusReady || registration.Status == StatusRunning {
			regCopy := *registration
			healthyAgents = append(healthyAgents, &regCopy)
		}
	}

	return healthyAgents
}

// validateAgent 验证Agent
func (ar *AgentRegistry) validateAgent(agent Agent) error {
	metadata := agent.GetMetadata()

	// 验证基本信息
	if metadata.Name == "" {
		return fmt.Errorf("agent name is required")
	}
	if metadata.Version == "" {
		return fmt.Errorf("agent version is required")
	}

	// 验证能力
	capabilities := agent.GetCapabilities()
	if len(capabilities) == 0 {
		return fmt.Errorf("agent must have at least one capability")
	}

	for _, cap := range capabilities {
		if !IsValidCapability(cap.Name) {
			return fmt.Errorf("invalid capability name: %s", cap.Name)
		}
	}

	return nil
}

// startHealthCheckRoutine 启动健康检查协程
func (ar *AgentRegistry) startHealthCheckRoutine() {
	ticker := time.NewTicker(ar.config.HealthCheckInterval)
	defer ticker.Stop()

	for range ticker.C {
		ar.performHealthChecks()
	}
}

// performHealthChecks 执行健康检查
func (ar *AgentRegistry) performHealthChecks() {
	ar.mutex.RLock()
	agentInstances := make(map[string]Agent)
	for id, agent := range ar.agentInstances {
		agentInstances[id] = agent
	}
	ar.mutex.RUnlock()

	for agentID, agent := range agentInstances {
		go func(id string, a Agent) {
			ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
			defer cancel()

			health := a.HealthCheck(ctx)

			var newStatus AgentStatus
			switch health.Status {
			case HealthStatusHealthy:
				newStatus = StatusReady
			case HealthStatusDegraded:
				newStatus = StatusRunning
			default:
				newStatus = StatusError
			}

			ar.UpdateAgentStatus(id, newStatus)
		}(agentID, agent)
	}
}

// startCleanupRoutine 启动清理协程
func (ar *AgentRegistry) startCleanupRoutine() {
	ticker := time.NewTicker(ar.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		ar.cleanupExpiredAgents()
	}
}

// cleanupExpiredAgents 清理过期Agent
func (ar *AgentRegistry) cleanupExpiredAgents() {
	ar.mutex.Lock()
	defer ar.mutex.Unlock()

	now := time.Now()
	expiredAgents := make([]string, 0)

	for agentID, registration := range ar.agents {
		if now.Sub(registration.LastSeen) > ar.config.RegistrationTTL {
			expiredAgents = append(expiredAgents, agentID)
		}
	}

	for _, agentID := range expiredAgents {
		ar.logger.WithField("agent_id", agentID).Warn("Removing expired agent")
		delete(ar.agents, agentID)
		delete(ar.agentInstances, agentID)
	}
}

// GetStatistics 获取统计信息
func (ar *AgentRegistry) GetStatistics() map[string]interface{} {
	ar.mutex.RLock()
	defer ar.mutex.RUnlock()

	stats := map[string]interface{}{
		"total_agents": len(ar.agents),
		"by_status":    make(map[AgentStatus]int),
		"by_category":  make(map[AgentCategory]int),
	}

	statusCounts := stats["by_status"].(map[AgentStatus]int)
	categoryCounts := stats["by_category"].(map[AgentCategory]int)

	for _, registration := range ar.agents {
		statusCounts[registration.Status]++
		categoryCounts[registration.Metadata.Category]++
	}

	return stats
}
