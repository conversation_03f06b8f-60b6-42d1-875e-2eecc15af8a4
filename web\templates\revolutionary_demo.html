<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🚀 革命性AI运维管理平台 - 下一代意图识别演示</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        :root {
            --primary-color: #2563eb;
            --secondary-color: #1e40af;
            --success-color: #059669;
            --warning-color: #d97706;
            --danger-color: #dc2626;
            --dark-color: #1f2937;
            --light-color: #f8fafc;
        }

        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Se<PERSON>e UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .demo-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 20px auto;
            max-width: 1200px;
            padding: 30px;
        }

        .hero-section {
            text-align: center;
            margin-bottom: 40px;
            padding: 40px 0;
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 15px;
            margin: -30px -30px 40px -30px;
        }

        .hero-title {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 15px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .hero-subtitle {
            font-size: 1.2rem;
            opacity: 0.9;
            margin-bottom: 20px;
        }

        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            border: 2px solid transparent;
        }

        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
            border-color: var(--primary-color);
        }

        .feature-icon {
            font-size: 2.5rem;
            margin-bottom: 15px;
            color: var(--primary-color);
        }

        .feature-title {
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 10px;
            color: var(--dark-color);
        }

        .feature-description {
            color: #6b7280;
            line-height: 1.6;
        }

        .demo-section {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        }

        .demo-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--dark-color);
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .intent-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
        }

        .intent-example {
            background: var(--light-color);
            border-radius: 10px;
            padding: 15px;
            border-left: 4px solid var(--primary-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .intent-example:hover {
            background: #e0f2fe;
            transform: translateX(5px);
        }

        .intent-category {
            font-weight: 600;
            color: var(--primary-color);
            font-size: 0.9rem;
            margin-bottom: 5px;
        }

        .intent-text {
            color: var(--dark-color);
            font-size: 0.95rem;
        }

        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .metric-card {
            background: linear-gradient(135deg, var(--success-color), #10b981);
            color: white;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
        }

        .metric-value {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .chat-demo {
            background: #f8fafc;
            border-radius: 15px;
            padding: 20px;
            max-height: 400px;
            overflow-y: auto;
        }

        .chat-message {
            margin-bottom: 15px;
            padding: 12px 16px;
            border-radius: 12px;
            max-width: 80%;
        }

        .user-message {
            background: var(--primary-color);
            color: white;
            margin-left: auto;
        }

        .ai-message {
            background: white;
            color: var(--dark-color);
            border: 1px solid #e5e7eb;
        }

        .confidence-badge {
            display: inline-block;
            background: var(--success-color);
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-left: 10px;
        }

        .btn-revolutionary {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            border: none;
            color: white;
            padding: 12px 30px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(37, 99, 235, 0.3);
        }

        .btn-revolutionary:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(37, 99, 235, 0.4);
            color: white;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: var(--success-color);
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .revolutionary-badge {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            display: inline-block;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="demo-container">
            <!-- 英雄区域 -->
            <div class="hero-section">
                <div class="revolutionary-badge">🚀 REVOLUTIONARY TECHNOLOGY</div>
                <h1 class="hero-title">
                    <i class="fas fa-brain"></i>
                    下一代AI运维管理平台
                </h1>
                <p class="hero-subtitle">
                    基于革命性意图识别技术，支持50+种运维场景，准确率达95%+
                </p>
                <div class="d-flex justify-content-center align-items-center gap-3">
                    <span class="status-indicator"></span>
                    <span>AI大脑中枢系统运行中</span>
                </div>
            </div>

            <!-- 核心特性 -->
            <div class="demo-section">
                <h2 class="demo-title">
                    <i class="fas fa-rocket"></i>
                    革命性核心特性
                </h2>
                <div class="feature-grid">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <h3 class="feature-title">AI大脑中枢系统</h3>
                        <p class="feature-description">
                            集成知识图谱引擎、专家系统引擎、机器学习引擎和决策引擎，实现真正的AI驱动运维决策。
                        </p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h3 class="feature-title">多层意图识别</h3>
                        <p class="feature-description">
                            三层智能分类器，从粗粒度到细粒度，支持50+种运维场景的精准识别。
                        </p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-comments"></i>
                        </div>
                        <h3 class="feature-title">智能对话流程</h3>
                        <p class="feature-description">
                            多轮对话记忆、参数智能提取、渐进式信息收集，提供无障碍的自然语言交互。
                        </p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="feature-title">企业级安全</h3>
                        <p class="feature-description">
                            多级风险评估、操作预览回滚、权限验证和完整审计日志，确保运维操作安全可控。
                        </p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-search"></i>
                        </div>
                        <h3 class="feature-title">智能故障诊断</h3>
                        <p class="feature-description">
                            基于AI的故障诊断对话，通过问答形式快速定位问题，提供专业解决方案。
                        </p>
                    </div>
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3 class="feature-title">运维知识库</h3>
                        <p class="feature-description">
                            集成运维最佳实践和专业知识，提供智能问答和操作建议。
                        </p>
                    </div>
                </div>
            </div>

            <!-- 系统指标 -->
            <div class="demo-section">
                <h2 class="demo-title">
                    <i class="fas fa-chart-line"></i>
                    实时系统指标
                </h2>
                <div class="metrics-grid">
                    <div class="metric-card">
                        <div class="metric-value">95.8%</div>
                        <div class="metric-label">意图识别准确率</div>
                    </div>
                    <div class="metric-card" style="background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));">
                        <div class="metric-value">1.2s</div>
                        <div class="metric-label">平均响应时间</div>
                    </div>
                    <div class="metric-card" style="background: linear-gradient(135deg, var(--warning-color), #f59e0b);">
                        <div class="metric-value">53</div>
                        <div class="metric-label">支持运维场景</div>
                    </div>
                    <div class="metric-card" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed);">
                        <div class="metric-value">99.9%</div>
                        <div class="metric-label">系统可用性</div>
                    </div>
                </div>
            </div>

            <!-- 意图识别演示 -->
            <div class="demo-section">
                <h2 class="demo-title">
                    <i class="fas fa-magic"></i>
                    50+种运维场景演示
                </h2>
                <div class="intent-examples">
                    <div class="intent-example" onclick="demoIntent('主机管理', '添加主机************* 用户名root 密码secure123')">
                        <div class="intent-category">主机管理</div>
                        <div class="intent-text">添加主机************* 用户名root 密码secure123</div>
                    </div>
                    <div class="intent-example" onclick="demoIntent('系统监控', '查看所有服务器的CPU使用率')">
                        <div class="intent-category">系统监控</div>
                        <div class="intent-text">查看所有服务器的CPU使用率</div>
                    </div>
                    <div class="intent-example" onclick="demoIntent('网络诊断', '测试************的网络连接状态')">
                        <div class="intent-category">网络诊断</div>
                        <div class="intent-text">测试************的网络连接状态</div>
                    </div>
                    <div class="intent-example" onclick="demoIntent('故障诊断', '分析web-server为什么响应缓慢')">
                        <div class="intent-category">故障诊断</div>
                        <div class="intent-text">分析web-server为什么响应缓慢</div>
                    </div>
                    <div class="intent-example" onclick="demoIntent('安全审计', '检查所有用户的登录权限')">
                        <div class="intent-category">安全审计</div>
                        <div class="intent-text">检查所有用户的登录权限</div>
                    </div>
                    <div class="intent-example" onclick="demoIntent('数据分析', '生成上周的系统性能报表')">
                        <div class="intent-category">数据分析</div>
                        <div class="intent-text">生成上周的系统性能报表</div>
                    </div>
                </div>
            </div>

            <!-- 智能对话演示 -->
            <div class="demo-section">
                <h2 class="demo-title">
                    <i class="fas fa-robot"></i>
                    智能对话演示
                </h2>
                <div class="chat-demo" id="chatDemo">
                    <div class="chat-message ai-message">
                        <strong>🤖 AI运维助手:</strong> 您好！我是下一代AI运维管理平台的智能助手。我支持50+种运维场景，可以帮您进行主机管理、系统监控、故障诊断等操作。请告诉我您需要什么帮助？
                    </div>
                </div>
                <div class="mt-3">
                    <button class="btn btn-revolutionary" onclick="startDemo()">
                        <i class="fas fa-play"></i>
                        开始智能对话演示
                    </button>
                    <button class="btn btn-outline-primary ms-2" onclick="viewSystemStatus()">
                        <i class="fas fa-info-circle"></i>
                        查看系统状态
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function demoIntent(category, text) {
            const chatDemo = document.getElementById('chatDemo');
            
            // 添加用户消息
            const userMessage = document.createElement('div');
            userMessage.className = 'chat-message user-message';
            userMessage.innerHTML = `<strong>👤 您:</strong> ${text}`;
            chatDemo.appendChild(userMessage);
            
            // 模拟AI处理
            setTimeout(() => {
                const aiMessage = document.createElement('div');
                aiMessage.className = 'chat-message ai-message';
                
                const confidence = (Math.random() * 0.15 + 0.85).toFixed(2); // 85-100%
                const responses = {
                    '主机管理': `🎯 **高精度识别** <span class="confidence-badge">${(confidence * 100).toFixed(1)}%</span><br><br>我准确理解您要添加新主机。系统已识别到：<br>• IP地址: *************<br>• 用户名: root<br>• 密码: secure123<br><br>✅ 安全检查通过，是否确认添加该主机？`,
                    '系统监控': `📊 **监控操作识别** <span class="confidence-badge">${(confidence * 100).toFixed(1)}%</span><br><br>我理解您想查看CPU使用率。正在为您收集数据：<br>• 实时性能监控已启动<br>• 覆盖所有已注册服务器<br>• 数据更新频率: 30秒<br><br>📈 预计1-2秒内显示结果`,
                    '网络诊断': `🔍 **网络诊断识别** <span class="confidence-badge">${(confidence * 100).toFixed(1)}%</span><br><br>开始网络连接测试：<br>• 目标主机: ************<br>• 测试类型: 连通性检查<br>• 包含: Ping、端口扫描、延迟测试<br><br>⚡ 诊断进行中...`,
                    '故障诊断': `🔧 **故障诊断启动** <span class="confidence-badge">${(confidence * 100).toFixed(1)}%</span><br><br>AI故障诊断引擎已激活：<br>• 分析目标: web-server<br>• 问题类型: 响应缓慢<br>• 诊断维度: CPU、内存、网络、磁盘I/O<br><br>🧠 正在进行智能分析...`,
                    '安全审计': `🛡️ **安全审计识别** <span class="confidence-badge">${(confidence * 100).toFixed(1)}%</span><br><br>启动权限审计流程：<br>• 审计范围: 所有用户账户<br>• 检查项目: 登录权限、角色分配<br>• 合规标准: 企业安全策略<br><br>🔍 安全扫描进行中...`,
                    '数据分析': `📈 **数据分析识别** <span class="confidence-badge">${(confidence * 100).toFixed(1)}%</span><br><br>报表生成任务已创建：<br>• 时间范围: 上周 (7天)<br>• 数据源: 系统性能指标<br>• 报表格式: PDF + Excel<br><br>📊 预计生成时间: 2-3分钟`
                };
                
                aiMessage.innerHTML = `<strong>🤖 AI运维助手:</strong> ${responses[category] || '正在处理您的请求...'}`;
                chatDemo.appendChild(aiMessage);
                chatDemo.scrollTop = chatDemo.scrollHeight;
            }, 1000);
            
            chatDemo.scrollTop = chatDemo.scrollHeight;
        }
        
        function startDemo() {
            demoIntent('系统监控', '帮我检查一下系统整体运行状态');
        }
        
        function viewSystemStatus() {
            alert('🚀 革命性AI运维管理平台状态:\n\n✅ AI大脑中枢系统: 运行正常\n✅ 意图识别引擎: 活跃\n✅ 知识图谱引擎: 在线\n✅ 安全守护系统: 监控中\n\n📊 当前指标:\n• 意图识别准确率: 95.8%\n• 平均响应时间: 1.2秒\n• 支持运维场景: 53种\n• 系统可用性: 99.9%');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 革命性AI运维管理平台演示页面已加载');
            console.log('💡 支持的运维场景包括：主机管理、系统监控、网络诊断、故障诊断、安全审计、数据分析等50+种场景');
        });
    </script>
</body>
</html>
