<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>消息显示测试</title>
    <link href="/static/css/fonts.css" rel="stylesheet">
    <link href="/static/vendor/bootstrap-icons/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/design-system.css" rel="stylesheet">
    <link href="/static/css/components.css" rel="stylesheet">
    <link href="/static/css/chat.css" rel="stylesheet">
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(180deg, var(--color-gray-50) 0%, var(--color-primary-50) 100%);
            min-height: 100vh;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0,0,0,0.1);
        }
        .test-messages {
            background: linear-gradient(180deg, var(--color-gray-50) 0%, var(--color-primary-50) 100%);
            padding: 20px;
            border-radius: 12px;
            position: relative;
            min-height: 400px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>消息显示测试 - 修复视觉异常</h2>
        <p>测试cache-server-01状态显示是否还有视觉异常（印子）</p>
        
        <div class="test-messages">
            <!-- 模拟AI回复消息 -->
            <div class="message assistant">
                <div class="message-avatar">AI</div>
                <div class="message-content">
                    正在查询主机状态...<br><br>
                    📊 <strong>系统状态概览</strong><br><br>
                    🟢 <strong>web-server-01</strong>: 运行正常<br>
                    - CPU使用率: 15%<br>
                    - 内存使用率: 45%<br>
                    - 磁盘使用率: 60%<br><br>
                    🟢 <strong>db-server-01</strong>: 运行正常<br>
                    - CPU使用率: 8%<br>
                    - 内存使用率: 70%<br>
                    - 磁盘使用率: 40%<br><br>
                    🟡 <strong>cache-server-01</strong>: 需要关注<br>
                    - CPU使用率: 85%<br>
                    - 内存使用率: 90%<br>
                    - 磁盘使用率: 30%<br><br>
                    所有主机网络连接正常，建议关注cache-server-01的资源使用情况。
                </div>
                <div class="message-time">11:14</div>
            </div>
            
            <!-- 用户消息 -->
            <div class="message user">
                <div class="message-content">
                    查看主机状态
                </div>
                <div class="message-avatar">U</div>
                <div class="message-time">11:13</div>
            </div>
            
            <!-- 另一条AI消息 -->
            <div class="message assistant">
                <div class="message-avatar">AI</div>
                <div class="message-content">
                    正在检查系统告警...<br><br>
                    🚨 <strong>当前告警信息</strong><br><br>
                    ⚠️ <strong>高优先级告警 (1条)</strong><br>
                    - cache-server-01: CPU使用率超过80% (当前85%)<br>
                    - 时间: 2025-01-25 08:45:32<br>
                    - 持续时间: 15分钟<br><br>
                    💡 <strong>建议操作</strong><br>
                    1. 检查cache-server-01上的进程<br>
                    2. 考虑重启缓存服务<br>
                    3. 监控后续状态变化
                </div>
                <div class="message-time">11:15</div>
            </div>
        </div>
        
        <div style="margin-top: 20px;">
            <h3>检查要点：</h3>
            <ul>
                <li>cache-server-01状态显示区域是否有异常的"印子"或视觉重叠</li>
                <li>消息气泡的背景是否清晰，没有多余的阴影或边框</li>
                <li>文字是否清晰可读，没有被遮挡</li>
                <li>伪元素（箭头等）是否正常显示</li>
            </ul>
        </div>
    </div>
</body>
</html>
