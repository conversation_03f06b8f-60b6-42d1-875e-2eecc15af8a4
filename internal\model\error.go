package model

import (
	"fmt"
	"net/http"
)

// ErrorCode 错误码类型
type ErrorCode int

// 系统级错误码 (1000-1999)
const (
	ErrCodeSuccess            ErrorCode = 0
	ErrCodeInternalError      ErrorCode = 1000
	ErrCodeInvalidParams      ErrorCode = 1001
	ErrCodeValidationFailed   ErrorCode = 1002
	ErrCodeDatabaseError      ErrorCode = 1003
	ErrCodeConfigError        ErrorCode = 1004
	ErrCodeServiceUnavailable ErrorCode = 1005
	ErrCodeTimeout            ErrorCode = 1006
	ErrCodeRateLimited        ErrorCode = 1007
)

// 认证授权错误码 (2000-2999)
const (
	ErrCodeUnauthorized      ErrorCode = 2000
	ErrCodeForbidden         ErrorCode = 2001
	ErrCodeTokenExpired      ErrorCode = 2002
	ErrCodeTokenInvalid      ErrorCode = 2003
	ErrCodeUserNotFound      ErrorCode = 2004
	ErrCodeUserDisabled      ErrorCode = 2005
	ErrCodePasswordIncorrect ErrorCode = 2006
	ErrCodeUserExists        ErrorCode = 2007
)

// 业务逻辑错误码 (3000-3999)
const (
	ErrCodeHostNotFound      ErrorCode = 3000
	ErrCodeHostConnectFailed ErrorCode = 3001
	ErrCodeCommandFailed     ErrorCode = 3002
	ErrCodeSessionNotFound   ErrorCode = 3003
	ErrCodeSessionExpired    ErrorCode = 3004
	ErrCodeAlertNotFound     ErrorCode = 3005
	ErrCodeConfigNotFound    ErrorCode = 3006
)

// AI服务错误码 (4000-4999)
const (
	ErrCodeAIServiceError    ErrorCode = 4000
	ErrCodeAIQuotaExceeded   ErrorCode = 4001
	ErrCodeAIModelNotFound   ErrorCode = 4002
	ErrCodeAIRequestTooLarge ErrorCode = 4003
)

// 错误码映射到HTTP状态码
var errorCodeToHTTPStatus = map[ErrorCode]int{
	ErrCodeSuccess:            http.StatusOK,
	ErrCodeInternalError:      http.StatusInternalServerError,
	ErrCodeInvalidParams:      http.StatusBadRequest,
	ErrCodeValidationFailed:   http.StatusBadRequest,
	ErrCodeDatabaseError:      http.StatusInternalServerError,
	ErrCodeConfigError:        http.StatusInternalServerError,
	ErrCodeServiceUnavailable: http.StatusServiceUnavailable,
	ErrCodeTimeout:            http.StatusRequestTimeout,
	ErrCodeRateLimited:        http.StatusTooManyRequests,
	ErrCodeUnauthorized:       http.StatusUnauthorized,
	ErrCodeForbidden:          http.StatusForbidden,
	ErrCodeTokenExpired:       http.StatusUnauthorized,
	ErrCodeTokenInvalid:       http.StatusUnauthorized,
	ErrCodeUserNotFound:       http.StatusNotFound,
	ErrCodeUserDisabled:       http.StatusForbidden,
	ErrCodePasswordIncorrect:  http.StatusUnauthorized,
	ErrCodeUserExists:         http.StatusConflict,
	ErrCodeHostNotFound:       http.StatusNotFound,
	ErrCodeHostConnectFailed:  http.StatusBadGateway,
	ErrCodeCommandFailed:      http.StatusBadRequest,
	ErrCodeSessionNotFound:    http.StatusNotFound,
	ErrCodeSessionExpired:     http.StatusGone,
	ErrCodeAlertNotFound:      http.StatusNotFound,
	ErrCodeConfigNotFound:     http.StatusNotFound,
	ErrCodeAIServiceError:     http.StatusBadGateway,
	ErrCodeAIQuotaExceeded:    http.StatusTooManyRequests,
	ErrCodeAIModelNotFound:    http.StatusNotFound,
	ErrCodeAIRequestTooLarge:  http.StatusRequestEntityTooLarge,
}

// 错误码对应的消息
var errorCodeMessages = map[ErrorCode]string{
	ErrCodeSuccess:            "操作成功",
	ErrCodeInternalError:      "内部服务器错误",
	ErrCodeInvalidParams:      "请求参数无效",
	ErrCodeValidationFailed:   "数据验证失败",
	ErrCodeDatabaseError:      "数据库操作失败",
	ErrCodeConfigError:        "配置错误",
	ErrCodeServiceUnavailable: "服务暂时不可用",
	ErrCodeTimeout:            "请求超时",
	ErrCodeRateLimited:        "请求频率过高",
	ErrCodeUnauthorized:       "未授权访问",
	ErrCodeForbidden:          "访问被禁止",
	ErrCodeTokenExpired:       "令牌已过期",
	ErrCodeTokenInvalid:       "令牌无效",
	ErrCodeUserNotFound:       "用户不存在",
	ErrCodeUserDisabled:       "用户已被禁用",
	ErrCodePasswordIncorrect:  "密码错误",
	ErrCodeUserExists:         "用户已存在",
	ErrCodeHostNotFound:       "主机不存在",
	ErrCodeHostConnectFailed:  "主机连接失败",
	ErrCodeCommandFailed:      "命令执行失败",
	ErrCodeSessionNotFound:    "会话不存在",
	ErrCodeSessionExpired:     "会话已过期",
	ErrCodeAlertNotFound:      "告警不存在",
	ErrCodeConfigNotFound:     "配置不存在",
	ErrCodeAIServiceError:     "AI服务错误",
	ErrCodeAIQuotaExceeded:    "AI服务配额已用完",
	ErrCodeAIModelNotFound:    "AI模型不存在",
	ErrCodeAIRequestTooLarge:  "AI请求内容过大",
}

// AppError 应用错误类型
type AppError struct {
	Code      ErrorCode `json:"code"`
	Message   string    `json:"message"`
	Details   string    `json:"details,omitempty"`
	Cause     error     `json:"-"`
	RequestID string    `json:"request_id,omitempty"`
}

// Error 实现error接口
func (e *AppError) Error() string {
	if e.Details != "" {
		return fmt.Sprintf("[%d] %s: %s", e.Code, e.Message, e.Details)
	}
	return fmt.Sprintf("[%d] %s", e.Code, e.Message)
}

// Unwrap 支持errors.Unwrap
func (e *AppError) Unwrap() error {
	return e.Cause
}

// HTTPStatus 获取对应的HTTP状态码
func (e *AppError) HTTPStatus() int {
	if status, ok := errorCodeToHTTPStatus[e.Code]; ok {
		return status
	}
	return http.StatusInternalServerError
}

// NewAppError 创建应用错误
func NewAppError(code ErrorCode, details string, cause error) *AppError {
	message := errorCodeMessages[code]
	if message == "" {
		message = "未知错误"
	}

	return &AppError{
		Code:    code,
		Message: message,
		Details: details,
		Cause:   cause,
	}
}

// NewAppErrorWithMessage 创建带自定义消息的应用错误
func NewAppErrorWithMessage(code ErrorCode, message, details string, cause error) *AppError {
	return &AppError{
		Code:    code,
		Message: message,
		Details: details,
		Cause:   cause,
	}
}

// ValidationError 验证错误
type ValidationError struct {
	Field   string      `json:"field"`
	Message string      `json:"message"`
	Value   interface{} `json:"value,omitempty"`
}

// ValidationErrors 验证错误列表
type ValidationErrors []ValidationError

// Error 实现error接口
func (ve ValidationErrors) Error() string {
	if len(ve) == 0 {
		return "validation failed"
	}
	return fmt.Sprintf("validation failed: %s", ve[0].Message)
}

// IsAppError 检查是否为应用错误
func IsAppError(err error) (*AppError, bool) {
	if appErr, ok := err.(*AppError); ok {
		return appErr, true
	}
	return nil, false
}

// GetErrorCode 获取错误码
func GetErrorCode(err error) ErrorCode {
	if appErr, ok := IsAppError(err); ok {
		return appErr.Code
	}
	return ErrCodeInternalError
}

// GetHTTPStatus 获取HTTP状态码
func GetHTTPStatus(err error) int {
	if appErr, ok := IsAppError(err); ok {
		return appErr.HTTPStatus()
	}
	return http.StatusInternalServerError
}
