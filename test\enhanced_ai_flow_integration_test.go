package test

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"testing"
	"time"

	"aiops-platform/internal/config"
	"aiops-platform/internal/database"
	"aiops-platform/internal/service"
	"github.com/sirupsen/logrus"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

// TestEnhancedAIFlowIntegration 测试增强AI流程集成
func TestEnhancedAIFlowIntegration(t *testing.T) {
	// 跳过测试如果没有API密钥
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		t.Skip("跳过集成测试：未设置DEEPSEEK_API_KEY环境变量")
	}

	// 初始化测试环境
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 初始化数据库
	db, err := database.InitTestDatabase()
	require.NoError(t, err, "初始化测试数据库失败")

	// 创建增强DeepSeek配置
	deepseekConfig := &service.EnhancedDeepSeekConfig{
		APIKey:            apiKey,
		BaseURL:           "https://api.deepseek.com",
		Model:             "deepseek-chat",
		MaxTokens:         4000,
		Temperature:       0.7,
		TopP:              0.9,
		DefaultTimeout:    30 * time.Second,
		ComplexTimeout:    120 * time.Second,
		MaxRetries:        3,
		RetryDelay:        2 * time.Second,
		BackoffMultiplier: 2.0,
		EnableSmartRetry:  true,
		RetryableErrors: []string{
			"context deadline exceeded",
			"timeout",
			"connection reset",
		},
	}

	// 创建服务组件
	enhancedDeepSeek := service.NewEnhancedDeepSeekService(deepseekConfig, logger)
	
	// 创建执行引擎（模拟）
	executionEngine := createMockExecutionEngine(db, logger)
	
	// 创建主机服务（模拟）
	hostService := createMockHostService(db, logger)
	
	// 创建增强WebSocket处理器
	enhancedHandler := service.NewEnhancedWebSocketHandler(
		db,
		logger,
		enhancedDeepSeek,
		executionEngine,
		hostService,
	)

	// 测试用例
	testCases := []struct {
		name           string
		userInput      string
		expectedIntent string
		shouldSucceed  bool
		timeout        time.Duration
	}{
		{
			name:           "简单查询测试",
			userInput:      "列出所有主机",
			expectedIntent: "database_operations",
			shouldSucceed:  true,
			timeout:        60 * time.Second,
		},
		{
			name:           "复杂巡检测试",
			userInput:      "对**************做一个全面的巡检",
			expectedIntent: "monitoring_operations",
			shouldSucceed:  true,
			timeout:        180 * time.Second,
		},
		{
			name:           "SSH操作测试",
			userInput:      "在主机*************上执行 ps aux | grep nginx",
			expectedIntent: "ssh_operations",
			shouldSucceed:  true,
			timeout:        90 * time.Second,
		},
		{
			name:           "通用对话测试",
			userInput:      "你好，请介绍一下你的功能",
			expectedIntent: "general_chat",
			shouldSucceed:  true,
			timeout:        30 * time.Second,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ctx, cancel := context.WithTimeout(context.Background(), tc.timeout)
			defer cancel()

			// 构建请求
			req := &service.ProcessMessageRequest{
				SessionID: fmt.Sprintf("test_session_%d", time.Now().Unix()),
				UserID:    1,
				Message:   tc.userInput,
			}

			logger.WithFields(logrus.Fields{
				"test_case":   tc.name,
				"user_input":  tc.userInput,
				"timeout":     tc.timeout,
			}).Info("开始测试用例")

			start := time.Now()

			// 执行完整的5步骤流程
			response, err := enhancedHandler.ProcessMessage(ctx, req)

			duration := time.Since(start)

			if tc.shouldSucceed {
				assert.NoError(t, err, "处理消息应该成功")
				assert.NotNil(t, response, "响应不应为空")
				
				if response != nil {
					assert.NotEmpty(t, response.Content, "响应内容不应为空")
					assert.Contains(t, response.Intent, tc.expectedIntent, "意图类型应该匹配")
					assert.Greater(t, response.Confidence, 0.0, "置信度应该大于0")
					assert.Greater(t, response.TokenCount, 0, "Token数量应该大于0")
					
					logger.WithFields(logrus.Fields{
						"test_case":       tc.name,
						"duration":        duration,
						"intent":          response.Intent,
						"confidence":      response.Confidence,
						"token_count":     response.TokenCount,
						"processing_time": response.ProcessingTime,
						"content_length":  len(response.Content),
					}).Info("测试用例完成")
					
					// 验证响应格式
					assert.True(t, isValidMarkdown(response.Content), "响应应该是有效的Markdown格式")
				}
			} else {
				assert.Error(t, err, "处理消息应该失败")
			}

			// 性能验证
			assert.Less(t, duration, tc.timeout, "处理时间应该在超时限制内")
		})
	}
}

// TestEnhancedDeepSeekService 测试增强DeepSeek服务
func TestEnhancedDeepSeekService(t *testing.T) {
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		t.Skip("跳过测试：未设置DEEPSEEK_API_KEY环境变量")
	}

	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	config := service.DefaultEnhancedDeepSeekConfig()
	config.APIKey = apiKey

	deepseekService := service.NewEnhancedDeepSeekService(config, logger)

	t.Run("意图识别测试", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()

		response, err := deepseekService.RecognizeIntent(ctx, "列出所有主机")
		
		assert.NoError(t, err, "意图识别应该成功")
		assert.NotNil(t, response, "响应不应为空")
		assert.Greater(t, len(response.Choices), 0, "应该有响应选择")
		
		// 验证响应内容是否为有效JSON
		content := response.Choices[0].Message.Content
		var intent map[string]interface{}
		err = json.Unmarshal([]byte(extractJSONFromResponse(content)), &intent)
		assert.NoError(t, err, "响应应该包含有效的JSON")
		
		assert.Contains(t, intent, "intent_type", "应该包含意图类型")
		assert.Contains(t, intent, "confidence", "应该包含置信度")
	})

	t.Run("复杂度超时测试", func(t *testing.T) {
		ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
		defer cancel()

		req := &service.DeepSeekRequest{
			Messages: []service.DeepSeekMessage{
				{
					Role:    "user",
					Content: "这是一个复杂的测试请求",
				},
			},
		}

		// 测试不同复杂度的超时处理
		complexities := []service.OperationComplexity{
			service.ComplexitySimple,
			service.ComplexityMedium,
			service.ComplexityComplex,
		}

		for _, complexity := range complexities {
			t.Run(fmt.Sprintf("复杂度_%s", complexity), func(t *testing.T) {
				start := time.Now()
				response, err := deepseekService.CallWithComplexity(ctx, req, complexity)
				duration := time.Since(start)

				// 验证响应或错误
				if err != nil {
					logger.WithFields(logrus.Fields{
						"complexity": complexity,
						"duration":   duration,
						"error":      err.Error(),
					}).Info("复杂度测试结果")
				} else {
					assert.NotNil(t, response, "响应不应为空")
					logger.WithFields(logrus.Fields{
						"complexity":  complexity,
						"duration":    duration,
						"token_count": response.Usage.TotalTokens,
					}).Info("复杂度测试成功")
				}
			})
		}
	})
}

// TestFallbackMechanism 测试降级机制
func TestFallbackMechanism(t *testing.T) {
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	fallbackManager := service.NewIntelligentFallbackManager(logger)

	t.Run("错误分析测试", func(t *testing.T) {
		testErrors := map[string]service.FallbackLevel{
			"context deadline exceeded": service.FallbackLevelPartial,
			"service unavailable":       service.FallbackLevelTraditional,
			"rate limit exceeded":       service.FallbackLevelTraditional,
			"parsing error":             service.FallbackLevelPartial,
		}

		for errMsg, expectedLevel := range testErrors {
			err := fmt.Errorf(errMsg)
			context := map[string]interface{}{
				"user_id":    int64(1),
				"session_id": "test_session",
				"message":    "test message",
			}

			level := fallbackManager.DetermineFallbackLevel(err, context)
			
			logger.WithFields(logrus.Fields{
				"error":          errMsg,
				"expected_level": expectedLevel,
				"actual_level":   level,
			}).Info("降级测试结果")

			// 验证降级级别合理性
			assert.True(t, level >= service.FallbackLevelNone, "降级级别应该有效")
			assert.True(t, level <= service.FallbackLevelEmergency, "降级级别应该在范围内")
		}
	})

	t.Run("统计信息测试", func(t *testing.T) {
		stats := fallbackManager.GetStatistics()
		
		assert.Contains(t, stats, "current_level", "应该包含当前级别")
		assert.Contains(t, stats, "total_requests", "应该包含总请求数")
		assert.Contains(t, stats, "success_rate", "应该包含成功率")
		
		logger.WithField("stats", stats).Info("降级管理器统计信息")
	})
}

// 辅助函数

func createMockExecutionEngine(db interface{}, logger *logrus.Logger) *service.UnifiedExecutionEngine {
	// 这里应该创建一个模拟的执行引擎
	// 为了测试目的，返回nil，实际使用时需要实现
	return nil
}

func createMockHostService(db interface{}, logger *logrus.Logger) service.HostService {
	// 这里应该创建一个模拟的主机服务
	// 为了测试目的，返回nil，实际使用时需要实现
	return nil
}

func isValidMarkdown(content string) bool {
	// 简单的Markdown格式验证
	return len(content) > 0 && 
		   (contains(content, "#") || contains(content, "**") || contains(content, "*"))
}

func contains(s, substr string) bool {
	return len(s) >= len(substr) && 
		   (s == substr || len(s) > len(substr) && 
		   (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr || 
		   containsInMiddle(s, substr)))
}

func containsInMiddle(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

func extractJSONFromResponse(content string) string {
	// 简化的JSON提取逻辑
	start := -1
	end := -1
	braceCount := 0
	
	for i, char := range content {
		if char == '{' {
			if start == -1 {
				start = i
			}
			braceCount++
		} else if char == '}' {
			braceCount--
			if braceCount == 0 && start != -1 {
				end = i
				break
			}
		}
	}
	
	if start != -1 && end != -1 {
		return content[start : end+1]
	}
	
	return content
}
