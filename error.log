﻿go : internal\agent\platform.go:8:2: package aiops-platform/internal/agent/agents is not in std (C:\Program Files\Go\src\aiops-platform\internal\age
nt\agents)
所在位置 行:1 字符: 1
+ go run .\cmd\server\main.go 2>&1 | Out-File -Encoding UTF8 error.log
+ ~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
    + CategoryInfo          : NotSpecified: (internal\agent\...l\agent\agents):String) [], RemoteException
    + FullyQualifiedErrorId : NativeCommandError
 
package command-line-arguments
	imports aiops-platform/internal/router from main.go
	imports aiops-platform/internal/handler from router.go
	imports aiops-platform/internal/service from action.go
	imports aiops-platform/internal/agent from agent_service.go
	imports aiops-platform/internal/service from decision_engine.go: import cycle not allowed
