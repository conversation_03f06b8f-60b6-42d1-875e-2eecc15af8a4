package security

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"github.com/sirupsen/logrus"
)

// PolicyEvaluator 策略评估器
type PolicyEvaluator struct {
	logger *logrus.Logger
}

// NewPolicyEvaluator 创建策略评估器
func NewPolicyEvaluator(logger *logrus.Logger) *PolicyEvaluator {
	return &PolicyEvaluator{
		logger: logger,
	}
}

// EvaluatePolicies 评估策略
func (pe *PolicyEvaluator) EvaluatePolicies(request *AccessRequest, policies []*Policy) (AccessDecision, string) {
	pe.logger.WithFields(logrus.Fields{
		"request_id":    request.ID,
		"identity_id":   request.Identity.ID,
		"resource":      request.Resource,
		"action":        request.Action,
		"policy_count":  len(policies),
	}).Debug("Evaluating policies")

	// 按优先级排序策略
	sortedPolicies := pe.sortPoliciesByPriority(policies)

	// 评估每个策略
	for _, policy := range sortedPolicies {
		if pe.policyMatches(request, policy) {
			decision, reason := pe.evaluatePolicyActions(policy)
			
			pe.logger.WithFields(logrus.Fields{
				"request_id":  request.ID,
				"policy_id":   policy.ID,
				"policy_name": policy.Name,
				"decision":    decision.String(),
				"reason":      reason,
			}).Info("Policy matched")

			return decision, reason
		}
	}

	// 如果没有策略匹配，返回默认决策
	pe.logger.WithField("request_id", request.ID).Debug("No policies matched, using default decision")
	return AccessDeny, "No matching policy found, default deny"
}

// policyMatches 检查策略是否匹配请求
func (pe *PolicyEvaluator) policyMatches(request *AccessRequest, policy *Policy) bool {
	// 检查资源匹配
	if !pe.resourceMatches(request.Resource, policy.Resources) {
		return false
	}

	// 检查主体匹配
	if !pe.subjectMatches(request.Identity, policy.Subjects) {
		return false
	}

	// 检查所有条件
	for _, condition := range policy.Conditions {
		if !pe.evaluateCondition(request, condition) {
			return false
		}
	}

	return true
}

// resourceMatches 检查资源是否匹配
func (pe *PolicyEvaluator) resourceMatches(resource string, policyResources []string) bool {
	if len(policyResources) == 0 {
		return true // 空列表表示匹配所有资源
	}

	for _, policyResource := range policyResources {
		if pe.matchesPattern(resource, policyResource) {
			return true
		}
	}

	return false
}

// subjectMatches 检查主体是否匹配
func (pe *PolicyEvaluator) subjectMatches(identity *Identity, policySubjects []string) bool {
	if len(policySubjects) == 0 {
		return true // 空列表表示匹配所有主体
	}

	for _, subject := range policySubjects {
		if pe.identityMatches(identity, subject) {
			return true
		}
	}

	return false
}

// identityMatches 检查身份是否匹配主体
func (pe *PolicyEvaluator) identityMatches(identity *Identity, subject string) bool {
	// 直接ID匹配
	if identity.ID == subject {
		return true
	}

	// 角色匹配
	if strings.HasPrefix(subject, "role:") {
		role := strings.TrimPrefix(subject, "role:")
		for _, identityRole := range identity.Roles {
			if identityRole == role {
				return true
			}
		}
	}

	// 组匹配
	if strings.HasPrefix(subject, "group:") {
		group := strings.TrimPrefix(subject, "group:")
		for _, identityGroup := range identity.Groups {
			if identityGroup == group {
				return true
			}
		}
	}

	// 类型匹配
	if strings.HasPrefix(subject, "type:") {
		identityType := strings.TrimPrefix(subject, "type:")
		if identity.Type == identityType {
			return true
		}
	}

	// 通配符匹配
	if subject == "*" {
		return true
	}

	return false
}

// evaluateCondition 评估条件
func (pe *PolicyEvaluator) evaluateCondition(request *AccessRequest, condition PolicyCondition) bool {
	value := pe.extractFieldValue(request, condition.Field)
	result := pe.compareValues(value, condition.Operator, condition.Value)

	// 应用否定
	if condition.Negate {
		result = !result
	}

	pe.logger.WithFields(logrus.Fields{
		"field":    condition.Field,
		"operator": condition.Operator,
		"expected": condition.Value,
		"actual":   value,
		"negate":   condition.Negate,
		"result":   result,
	}).Debug("Condition evaluated")

	return result
}

// extractFieldValue 提取字段值
func (pe *PolicyEvaluator) extractFieldValue(request *AccessRequest, field string) interface{} {
	parts := strings.Split(field, ".")
	if len(parts) < 2 {
		return nil
	}

	switch parts[0] {
	case "identity":
		return pe.extractIdentityField(request.Identity, parts[1:])
	case "device":
		return pe.extractDeviceField(request.Device, parts[1:])
	case "context":
		return pe.extractContextField(request.Context, parts[1:])
	case "request":
		return pe.extractRequestField(request, parts[1:])
	case "trust_score":
		return request.TrustScore
	case "risk_score":
		return request.RiskScore
	default:
		return nil
	}
}

// extractIdentityField 提取身份字段
func (pe *PolicyEvaluator) extractIdentityField(identity *Identity, fields []string) interface{} {
	if len(fields) == 0 {
		return nil
	}

	switch fields[0] {
	case "id":
		return identity.ID
	case "type":
		return identity.Type
	case "name":
		return identity.Name
	case "email":
		return identity.Email
	case "roles":
		return identity.Roles
	case "groups":
		return identity.Groups
	case "is_active":
		return identity.IsActive
	case "trust_score":
		return identity.TrustScore
	case "attributes":
		if len(fields) > 1 {
			if attr, exists := identity.Attributes[fields[1]]; exists {
				return attr
			}
		}
		return identity.Attributes
	default:
		return nil
	}
}

// extractDeviceField 提取设备字段
func (pe *PolicyEvaluator) extractDeviceField(device *Device, fields []string) interface{} {
	if len(fields) == 0 {
		return nil
	}

	switch fields[0] {
	case "id":
		return device.ID
	case "type":
		return device.Type
	case "os":
		return device.OS
	case "version":
		return device.Version
	case "ip_address":
		return device.IPAddress
	case "mac_address":
		return device.MACAddress
	case "location":
		return device.Location
	case "is_managed":
		return device.IsManaged
	case "is_compliant":
		return device.IsCompliant
	case "trust_level":
		return device.TrustLevel.String()
	case "attributes":
		if len(fields) > 1 {
			if attr, exists := device.Attributes[fields[1]]; exists {
				return attr
			}
		}
		return device.Attributes
	default:
		return nil
	}
}

// extractContextField 提取上下文字段
func (pe *PolicyEvaluator) extractContextField(context *AccessContext, fields []string) interface{} {
	if len(fields) == 0 {
		return nil
	}

	switch fields[0] {
	case "ip_address":
		return context.IPAddress
	case "user_agent":
		return context.UserAgent
	case "location":
		return context.Location
	case "time_of_day":
		return context.TimeOfDay
	case "day_of_week":
		return context.DayOfWeek
	case "is_vpn":
		return context.IsVPN
	case "is_tor":
		return context.IsTor
	case "is_proxy":
		return context.IsProxy
	case "network_type":
		return context.NetworkType
	case "security_level":
		return context.SecurityLevel
	case "headers":
		if len(fields) > 1 {
			if header, exists := context.Headers[fields[1]]; exists {
				return header
			}
		}
		return context.Headers
	default:
		return nil
	}
}

// extractRequestField 提取请求字段
func (pe *PolicyEvaluator) extractRequestField(request *AccessRequest, fields []string) interface{} {
	if len(fields) == 0 {
		return nil
	}

	switch fields[0] {
	case "resource":
		return request.Resource
	case "action":
		return request.Action
	case "timestamp":
		return request.Timestamp
	default:
		return nil
	}
}

// compareValues 比较值
func (pe *PolicyEvaluator) compareValues(actual interface{}, operator string, expected interface{}) bool {
	switch operator {
	case "equals", "==":
		return pe.valuesEqual(actual, expected)
	case "not_equals", "!=":
		return !pe.valuesEqual(actual, expected)
	case "contains":
		return pe.valueContains(actual, expected)
	case "not_contains":
		return !pe.valueContains(actual, expected)
	case "in":
		return pe.valueIn(actual, expected)
	case "not_in":
		return !pe.valueIn(actual, expected)
	case "greater_than", ">":
		return pe.valueGreaterThan(actual, expected)
	case "greater_equal", ">=":
		return pe.valueGreaterEqual(actual, expected)
	case "less_than", "<":
		return pe.valueLessThan(actual, expected)
	case "less_equal", "<=":
		return pe.valueLessEqual(actual, expected)
	case "matches":
		return pe.valueMatches(actual, expected)
	case "starts_with":
		return pe.valueStartsWith(actual, expected)
	case "ends_with":
		return pe.valueEndsWith(actual, expected)
	default:
		pe.logger.WithField("operator", operator).Warn("Unknown operator")
		return false
	}
}

// valuesEqual 检查值是否相等
func (pe *PolicyEvaluator) valuesEqual(actual, expected interface{}) bool {
	return reflect.DeepEqual(actual, expected)
}

// valueContains 检查值是否包含
func (pe *PolicyEvaluator) valueContains(actual, expected interface{}) bool {
	actualStr := pe.toString(actual)
	expectedStr := pe.toString(expected)
	return strings.Contains(actualStr, expectedStr)
}

// valueIn 检查值是否在列表中
func (pe *PolicyEvaluator) valueIn(actual, expected interface{}) bool {
	expectedSlice, ok := expected.([]interface{})
	if !ok {
		return false
	}

	for _, item := range expectedSlice {
		if pe.valuesEqual(actual, item) {
			return true
		}
	}

	return false
}

// valueGreaterThan 检查值是否大于
func (pe *PolicyEvaluator) valueGreaterThan(actual, expected interface{}) bool {
	actualFloat := pe.toFloat64(actual)
	expectedFloat := pe.toFloat64(expected)
	return actualFloat > expectedFloat
}

// valueGreaterEqual 检查值是否大于等于
func (pe *PolicyEvaluator) valueGreaterEqual(actual, expected interface{}) bool {
	actualFloat := pe.toFloat64(actual)
	expectedFloat := pe.toFloat64(expected)
	return actualFloat >= expectedFloat
}

// valueLessThan 检查值是否小于
func (pe *PolicyEvaluator) valueLessThan(actual, expected interface{}) bool {
	actualFloat := pe.toFloat64(actual)
	expectedFloat := pe.toFloat64(expected)
	return actualFloat < expectedFloat
}

// valueLessEqual 检查值是否小于等于
func (pe *PolicyEvaluator) valueLessEqual(actual, expected interface{}) bool {
	actualFloat := pe.toFloat64(actual)
	expectedFloat := pe.toFloat64(expected)
	return actualFloat <= expectedFloat
}

// valueMatches 检查值是否匹配模式
func (pe *PolicyEvaluator) valueMatches(actual, expected interface{}) bool {
	actualStr := pe.toString(actual)
	expectedStr := pe.toString(expected)
	return pe.matchesPattern(actualStr, expectedStr)
}

// valueStartsWith 检查值是否以指定字符串开始
func (pe *PolicyEvaluator) valueStartsWith(actual, expected interface{}) bool {
	actualStr := pe.toString(actual)
	expectedStr := pe.toString(expected)
	return strings.HasPrefix(actualStr, expectedStr)
}

// valueEndsWith 检查值是否以指定字符串结束
func (pe *PolicyEvaluator) valueEndsWith(actual, expected interface{}) bool {
	actualStr := pe.toString(actual)
	expectedStr := pe.toString(expected)
	return strings.HasSuffix(actualStr, expectedStr)
}

// toString 转换为字符串
func (pe *PolicyEvaluator) toString(value interface{}) string {
	if value == nil {
		return ""
	}

	switch v := value.(type) {
	case string:
		return v
	case []string:
		return strings.Join(v, ",")
	default:
		return fmt.Sprintf("%v", v)
	}
}

// toFloat64 转换为浮点数
func (pe *PolicyEvaluator) toFloat64(value interface{}) float64 {
	switch v := value.(type) {
	case float64:
		return v
	case float32:
		return float64(v)
	case int:
		return float64(v)
	case int64:
		return float64(v)
	case string:
		if f, err := strconv.ParseFloat(v, 64); err == nil {
			return f
		}
	}
	return 0
}

// matchesPattern 检查是否匹配模式
func (pe *PolicyEvaluator) matchesPattern(value, pattern string) bool {
	// 简化的通配符匹配
	if pattern == "*" {
		return true
	}

	if strings.Contains(pattern, "*") {
		// 简单的通配符匹配
		parts := strings.Split(pattern, "*")
		if len(parts) == 2 {
			prefix := parts[0]
			suffix := parts[1]
			return strings.HasPrefix(value, prefix) && strings.HasSuffix(value, suffix)
		}
	}

	return value == pattern
}

// evaluatePolicyActions 评估策略动作
func (pe *PolicyEvaluator) evaluatePolicyActions(policy *Policy) (AccessDecision, string) {
	for _, action := range policy.Actions {
		switch action.Type {
		case "allow":
			return AccessAllow, fmt.Sprintf("Allowed by policy: %s", policy.Name)
		case "deny":
			return AccessDeny, fmt.Sprintf("Denied by policy: %s", policy.Name)
		case "challenge":
			return AccessChallenge, fmt.Sprintf("Challenge required by policy: %s", policy.Name)
		case "monitor":
			return AccessMonitor, fmt.Sprintf("Monitoring required by policy: %s", policy.Name)
		}
	}

	// 默认拒绝
	return AccessDeny, fmt.Sprintf("No valid action in policy: %s", policy.Name)
}

// sortPoliciesByPriority 按优先级排序策略
func (pe *PolicyEvaluator) sortPoliciesByPriority(policies []*Policy) []*Policy {
	// 创建副本以避免修改原始切片
	sorted := make([]*Policy, len(policies))
	copy(sorted, policies)

	// 简单的冒泡排序，按优先级降序
	for i := 0; i < len(sorted)-1; i++ {
		for j := 0; j < len(sorted)-i-1; j++ {
			if sorted[j].Priority < sorted[j+1].Priority {
				sorted[j], sorted[j+1] = sorted[j+1], sorted[j]
			}
		}
	}

	return sorted
}
