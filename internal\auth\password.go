package auth

import (
	"crypto/rand"
	"crypto/subtle"
	"encoding/base64"
	"errors"
	"fmt"
	"regexp"
	"strings"

	"golang.org/x/crypto/bcrypt"
)

// PasswordManager 密码管理器
type PasswordManager struct {
	cost int
}

// NewPasswordManager 创建密码管理器
func NewPasswordManager(cost int) *PasswordManager {
	if cost < bcrypt.MinCost || cost > bcrypt.MaxCost {
		cost = bcrypt.DefaultCost
	}
	return &PasswordManager{cost: cost}
}

// HashPassword 哈希密码
func (pm *PasswordManager) HashPassword(password string) (string, error) {
	if err := pm.ValidatePassword(password); err != nil {
		return "", err
	}

	hash, err := bcrypt.GenerateFromPassword([]byte(password), pm.cost)
	if err != nil {
		return "", fmt.Errorf("failed to hash password: %w", err)
	}

	return string(hash), nil
}

// VerifyPassword 验证密码
func (pm *PasswordManager) VerifyPassword(hashedPassword, password string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	if err != nil {
		if errors.Is(err, bcrypt.ErrMismatchedHashAndPassword) {
			return errors.New("invalid password")
		}
		return fmt.Errorf("failed to verify password: %w", err)
	}
	return nil
}

// ValidatePassword 验证密码强度
func (pm *PasswordManager) ValidatePassword(password string) error {
	if len(password) < 8 {
		return errors.New("password must be at least 8 characters long")
	}

	if len(password) > 128 {
		return errors.New("password must be no more than 128 characters long")
	}

	var (
		hasUpper   = regexp.MustCompile(`[A-Z]`).MatchString(password)
		hasLower   = regexp.MustCompile(`[a-z]`).MatchString(password)
		hasNumber  = regexp.MustCompile(`[0-9]`).MatchString(password)
		hasSpecial = regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`).MatchString(password)
	)

	var missing []string
	if !hasUpper {
		missing = append(missing, "uppercase letter")
	}
	if !hasLower {
		missing = append(missing, "lowercase letter")
	}
	if !hasNumber {
		missing = append(missing, "number")
	}
	if !hasSpecial {
		missing = append(missing, "special character")
	}

	if len(missing) > 0 {
		return fmt.Errorf("password must contain at least one %s", strings.Join(missing, ", "))
	}

	// 检查常见弱密码
	if pm.isCommonPassword(password) {
		return errors.New("password is too common, please choose a stronger password")
	}

	return nil
}

// GenerateRandomPassword 生成随机密码
func (pm *PasswordManager) GenerateRandomPassword(length int) (string, error) {
	if length < 8 {
		length = 12
	}
	if length > 128 {
		length = 128
	}

	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*"

	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate random password: %w", err)
	}

	for i, b := range bytes {
		bytes[i] = charset[b%byte(len(charset))]
	}

	return string(bytes), nil
}

// isCommonPassword 检查是否为常见弱密码
func (pm *PasswordManager) isCommonPassword(password string) bool {
	commonPasswords := []string{
		"password", "123456", "123456789", "12345678", "12345",
		"1234567", "admin", "administrator", "root", "user",
		"guest", "test", "demo", "qwerty", "abc123",
		"password123", "admin123", "123123", "111111", "000000",
	}

	lowerPassword := strings.ToLower(password)
	for _, common := range commonPasswords {
		if lowerPassword == common {
			return true
		}
	}

	return false
}

// GenerateSalt 生成盐值
func GenerateSalt(length int) (string, error) {
	if length <= 0 {
		length = 32
	}

	bytes := make([]byte, length)
	if _, err := rand.Read(bytes); err != nil {
		return "", fmt.Errorf("failed to generate salt: %w", err)
	}

	return base64.URLEncoding.EncodeToString(bytes), nil
}

// SecureCompare 安全比较字符串（防止时序攻击）
func SecureCompare(a, b string) bool {
	return subtle.ConstantTimeCompare([]byte(a), []byte(b)) == 1
}

// PasswordPolicy 密码策略
type PasswordPolicy struct {
	MinLength        int  `json:"min_length"`
	MaxLength        int  `json:"max_length"`
	RequireUppercase bool `json:"require_uppercase"`
	RequireLowercase bool `json:"require_lowercase"`
	RequireNumbers   bool `json:"require_numbers"`
	RequireSpecial   bool `json:"require_special"`
	MaxAge           int  `json:"max_age"` // days
	HistoryCount     int  `json:"history_count"`
}

// DefaultPasswordPolicy 默认密码策略
func DefaultPasswordPolicy() *PasswordPolicy {
	return &PasswordPolicy{
		MinLength:        8,
		MaxLength:        128,
		RequireUppercase: true,
		RequireLowercase: true,
		RequireNumbers:   true,
		RequireSpecial:   true,
		MaxAge:           90,
		HistoryCount:     5,
	}
}

// ValidateWithPolicy 根据策略验证密码
func (pp *PasswordPolicy) ValidateWithPolicy(password string) error {
	if len(password) < pp.MinLength {
		return fmt.Errorf("password must be at least %d characters long", pp.MinLength)
	}

	if len(password) > pp.MaxLength {
		return fmt.Errorf("password must be no more than %d characters long", pp.MaxLength)
	}

	var missing []string

	if pp.RequireUppercase && !regexp.MustCompile(`[A-Z]`).MatchString(password) {
		missing = append(missing, "uppercase letter")
	}

	if pp.RequireLowercase && !regexp.MustCompile(`[a-z]`).MatchString(password) {
		missing = append(missing, "lowercase letter")
	}

	if pp.RequireNumbers && !regexp.MustCompile(`[0-9]`).MatchString(password) {
		missing = append(missing, "number")
	}

	if pp.RequireSpecial && !regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`).MatchString(password) {
		missing = append(missing, "special character")
	}

	if len(missing) > 0 {
		return fmt.Errorf("password must contain at least one %s", strings.Join(missing, ", "))
	}

	return nil
}

// PasswordStrength 密码强度枚举
type PasswordStrength int

const (
	PasswordStrengthWeak PasswordStrength = iota
	PasswordStrengthFair
	PasswordStrengthGood
	PasswordStrengthStrong
)

// String 返回密码强度的字符串表示
func (ps PasswordStrength) String() string {
	switch ps {
	case PasswordStrengthWeak:
		return "weak"
	case PasswordStrengthFair:
		return "fair"
	case PasswordStrengthGood:
		return "good"
	case PasswordStrengthStrong:
		return "strong"
	default:
		return "unknown"
	}
}

// CalculatePasswordStrength 计算密码强度
func CalculatePasswordStrength(password string) PasswordStrength {
	score := 0

	// 长度评分
	if len(password) >= 8 {
		score++
	}
	if len(password) >= 12 {
		score++
	}
	if len(password) >= 16 {
		score++
	}

	// 字符类型评分
	if regexp.MustCompile(`[a-z]`).MatchString(password) {
		score++
	}
	if regexp.MustCompile(`[A-Z]`).MatchString(password) {
		score++
	}
	if regexp.MustCompile(`[0-9]`).MatchString(password) {
		score++
	}
	if regexp.MustCompile(`[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]`).MatchString(password) {
		score++
	}

	// 复杂性评分
	if len(password) > 0 {
		uniqueChars := make(map[rune]bool)
		for _, char := range password {
			uniqueChars[char] = true
		}
		if len(uniqueChars) >= len(password)/2 {
			score++
		}
	}

	// 根据评分返回强度
	switch {
	case score <= 3:
		return PasswordStrengthWeak
	case score <= 5:
		return PasswordStrengthFair
	case score <= 7:
		return PasswordStrengthGood
	default:
		return PasswordStrengthStrong
	}
}
