package agent

import (
	"context"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ===== 事件系统 =====

// EventType 事件类型
type EventType string

const (
	EventTypeAgentRegistered     EventType = "agent_registered"
	EventTypeAgentUnregistered   EventType = "agent_unregistered"
	EventTypeAgentStatusChanged  EventType = "agent_status_changed"
	EventTypeAgentExecutionStart EventType = "agent_execution_start"
	EventTypeAgentExecutionEnd   EventType = "agent_execution_end"
	EventTypeAgentHealthCheck    EventType = "agent_health_check"
	EventTypeAgentError          EventType = "agent_error"
)

// AgentEvent Agent事件
type AgentEvent struct {
	Type      EventType   `json:"type"`
	AgentID   string      `json:"agent_id"`
	Timestamp time.Time   `json:"timestamp"`
	Data      interface{} `json:"data"`
	TraceID   string      `json:"trace_id,omitempty"`
}

// EventHandler 事件处理器
type EventHandler func(*AgentEvent)

// EventBus 事件总线
type EventBus struct {
	handlers  map[EventType][]EventHandler
	mutex     sync.RWMutex
	logger    *logrus.Logger
	buffer    []*AgentEvent
	bufferMu  sync.Mutex
	maxBuffer int
}

// NewEventBus 创建事件总线
func NewEventBus(logger *logrus.Logger) *EventBus {
	return &EventBus{
		handlers:  make(map[EventType][]EventHandler),
		logger:    logger,
		buffer:    make([]*AgentEvent, 0),
		maxBuffer: 1000,
	}
}

// Subscribe 订阅事件
func (eb *EventBus) Subscribe(eventType EventType, handler EventHandler) {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	eb.handlers[eventType] = append(eb.handlers[eventType], handler)
	eb.logger.WithField("event_type", eventType).Debug("Event handler subscribed")
}

// Publish 发布事件
func (eb *EventBus) Publish(event *AgentEvent) {
	eb.mutex.RLock()
	handlers := eb.handlers[event.Type]
	eb.mutex.RUnlock()

	// 添加到缓冲区
	eb.addToBuffer(event)

	// 异步处理事件
	go func() {
		for _, handler := range handlers {
			func() {
				defer func() {
					if r := recover(); r != nil {
						eb.logger.WithFields(logrus.Fields{
							"event_type": event.Type,
							"agent_id":   event.AgentID,
							"error":      r,
						}).Error("Event handler panicked")
					}
				}()
				handler(event)
			}()
		}
	}()

	eb.logger.WithFields(logrus.Fields{
		"event_type": event.Type,
		"agent_id":   event.AgentID,
	}).Debug("Event published")
}

// addToBuffer 添加到事件缓冲区
func (eb *EventBus) addToBuffer(event *AgentEvent) {
	eb.bufferMu.Lock()
	defer eb.bufferMu.Unlock()

	eb.buffer = append(eb.buffer, event)

	// 限制缓冲区大小
	if len(eb.buffer) > eb.maxBuffer {
		eb.buffer = eb.buffer[1:]
	}
}

// GetRecentEvents 获取最近的事件
func (eb *EventBus) GetRecentEvents(count int) []*AgentEvent {
	eb.bufferMu.Lock()
	defer eb.bufferMu.Unlock()

	if count <= 0 || count > len(eb.buffer) {
		count = len(eb.buffer)
	}

	start := len(eb.buffer) - count
	events := make([]*AgentEvent, count)
	copy(events, eb.buffer[start:])

	return events
}

// ===== 健康检查器 =====

// HealthChecker 健康检查器
type HealthChecker struct {
	logger *logrus.Logger
	checks map[string]*HealthCheckResult
	mutex  sync.RWMutex
}

// HealthCheckResult 健康检查结果
type HealthCheckResult struct {
	AgentID    string                 `json:"agent_id"`
	Status     HealthStatusType       `json:"status"`
	Message    string                 `json:"message"`
	Details    map[string]interface{} `json:"details"`
	CheckTime  time.Time              `json:"check_time"`
	Duration   time.Duration          `json:"duration"`
	ErrorCount int                    `json:"error_count"`
	LastError  string                 `json:"last_error,omitempty"`
}

// NewHealthChecker 创建健康检查器
func NewHealthChecker(logger *logrus.Logger) *HealthChecker {
	return &HealthChecker{
		logger: logger,
		checks: make(map[string]*HealthCheckResult),
	}
}

// CheckAgent 检查Agent健康状态
func (hc *HealthChecker) CheckAgent(agent Agent) *HealthCheckResult {
	start := time.Now()
	agentID := agent.GetID()

	hc.logger.WithField("agent_id", agentID).Debug("Starting health check")

	// 执行健康检查
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	health := agent.HealthCheck(ctx)
	duration := time.Since(start)

	// 创建检查结果
	result := &HealthCheckResult{
		AgentID:   agentID,
		Status:    health.Status,
		Message:   health.Message,
		Details:   health.Details,
		CheckTime: time.Now(),
		Duration:  duration,
	}

	// 更新错误计数
	hc.mutex.Lock()
	if prevResult, exists := hc.checks[agentID]; exists {
		result.ErrorCount = prevResult.ErrorCount
		if health.Status != HealthStatusHealthy {
			result.ErrorCount++
			result.LastError = health.Message
		} else {
			result.ErrorCount = 0
		}
	} else if health.Status != HealthStatusHealthy {
		result.ErrorCount = 1
		result.LastError = health.Message
	}

	hc.checks[agentID] = result
	hc.mutex.Unlock()

	hc.logger.WithFields(logrus.Fields{
		"agent_id": agentID,
		"status":   health.Status,
		"duration": duration,
	}).Debug("Health check completed")

	return result
}

// GetHealthStatus 获取Agent健康状态
func (hc *HealthChecker) GetHealthStatus(agentID string) *HealthCheckResult {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	if result, exists := hc.checks[agentID]; exists {
		resultCopy := *result
		return &resultCopy
	}

	return nil
}

// GetAllHealthStatus 获取所有Agent健康状态
func (hc *HealthChecker) GetAllHealthStatus() map[string]*HealthCheckResult {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	results := make(map[string]*HealthCheckResult)
	for agentID, result := range hc.checks {
		resultCopy := *result
		results[agentID] = &resultCopy
	}

	return results
}

// RemoveHealthStatus 移除Agent健康状态
func (hc *HealthChecker) RemoveHealthStatus(agentID string) {
	hc.mutex.Lock()
	defer hc.mutex.Unlock()

	delete(hc.checks, agentID)
}

// GetHealthSummary 获取健康状态摘要
func (hc *HealthChecker) GetHealthSummary() map[string]interface{} {
	hc.mutex.RLock()
	defer hc.mutex.RUnlock()

	summary := map[string]interface{}{
		"total_agents": len(hc.checks),
		"by_status":    make(map[HealthStatusType]int),
		"error_agents": make([]string, 0),
	}

	statusCounts := summary["by_status"].(map[HealthStatusType]int)
	errorAgents := summary["error_agents"].([]string)

	for agentID, result := range hc.checks {
		statusCounts[result.Status]++
		if result.Status != HealthStatusHealthy {
			errorAgents = append(errorAgents, agentID)
		}
	}

	summary["error_agents"] = errorAgents
	return summary
}

// ===== 事件监听器示例 =====

// LoggingEventHandler 日志事件处理器
func LoggingEventHandler(logger *logrus.Logger) EventHandler {
	return func(event *AgentEvent) {
		logger.WithFields(logrus.Fields{
			"event_type": event.Type,
			"agent_id":   event.AgentID,
			"timestamp":  event.Timestamp,
		}).Info("Agent event occurred")
	}
}

// MetricsEventHandler 指标事件处理器
func MetricsEventHandler() EventHandler {
	return func(event *AgentEvent) {
		// TODO: 发送指标到监控系统
		// metrics.Counter("agent_events_total").WithTags(map[string]string{
		//     "event_type": string(event.Type),
		//     "agent_id":   event.AgentID,
		// }).Inc()
	}
}

// AlertEventHandler 告警事件处理器
func AlertEventHandler(logger *logrus.Logger) EventHandler {
	return func(event *AgentEvent) {
		// 处理需要告警的事件
		switch event.Type {
		case EventTypeAgentError:
			logger.WithFields(logrus.Fields{
				"agent_id": event.AgentID,
				"error":    event.Data,
			}).Error("Agent error occurred - alert triggered")
			// TODO: 发送告警通知
		case EventTypeAgentStatusChanged:
			if data, ok := event.Data.(map[string]interface{}); ok {
				if newStatus, ok := data["new_status"].(AgentStatus); ok && newStatus == StatusError {
					logger.WithField("agent_id", event.AgentID).Warn("Agent status changed to error - alert triggered")
					// TODO: 发送告警通知
				}
			}
		}
	}
}
