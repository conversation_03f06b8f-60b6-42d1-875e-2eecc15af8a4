package model

import (
	"encoding/json"
	"time"

	"gorm.io/gorm"
)

// Alert 告警模型
type Alert struct {
	ID               int64          `json:"id" gorm:"primaryKey;autoIncrement"`
	Title            string         `json:"title" gorm:"size:200;not null"`
	Message          string         `json:"message" gorm:"type:text;not null"`
	Level            string         `json:"level" gorm:"size:20;not null"`
	Source           string         `json:"source" gorm:"size:50;not null"`
	Status           string         `json:"status" gorm:"size:20;not null;default:open"`
	AlertTime        time.Time      `json:"alert_time" gorm:"not null"`
	AcknowledgedTime *time.Time     `json:"acknowledged_time"`
	ResolvedTime     *time.Time     `json:"resolved_time"`
	ClosedTime       *time.Time     `json:"closed_time"`
	HostID           *int64         `json:"host_id"`
	AssignedTo       *int64         `json:"assigned_to"`
	CreatedBy        *int64         `json:"created_by"`
	RuleID           string         `json:"rule_id" gorm:"size:50"`
	Fingerprint      string         `json:"fingerprint" gorm:"size:64;index"`
	Metadata         string         `json:"-" gorm:"type:text"`
	NotificationSent bool           `json:"notification_sent" gorm:"default:false"`
	EscalationLevel  int            `json:"escalation_level" gorm:"default:0"`
	ParentAlertID    *int64         `json:"parent_alert_id"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`

	// 关联
	Host         *Host   `json:"host,omitempty" gorm:"foreignKey:HostID"`
	AssignedUser *User   `json:"assigned_user,omitempty" gorm:"foreignKey:AssignedTo"`
	Creator      *User   `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
	ParentAlert  *Alert  `json:"parent_alert,omitempty" gorm:"foreignKey:ParentAlertID"`
	ChildAlerts  []Alert `json:"child_alerts,omitempty" gorm:"foreignKey:ParentAlertID"`
}

// TableName 指定表名
func (Alert) TableName() string {
	return "alerts"
}

// BeforeCreate GORM钩子：创建前
func (a *Alert) BeforeCreate(tx *gorm.DB) error {
	if a.AlertTime.IsZero() {
		a.AlertTime = time.Now()
	}
	return nil
}

// GetMetadata 获取元数据
func (a *Alert) GetMetadata() map[string]interface{} {
	if a.Metadata == "" {
		return make(map[string]interface{})
	}

	var metadata map[string]interface{}
	if err := json.Unmarshal([]byte(a.Metadata), &metadata); err != nil {
		return make(map[string]interface{})
	}
	return metadata
}

// SetMetadata 设置元数据
func (a *Alert) SetMetadata(metadata map[string]interface{}) error {
	if metadata == nil {
		metadata = make(map[string]interface{})
	}

	data, err := json.Marshal(metadata)
	if err != nil {
		return err
	}
	a.Metadata = string(data)
	return nil
}

// IsOpen 检查告警是否为开放状态
func (a *Alert) IsOpen() bool {
	return a.Status == string(AlertStatusOpen)
}

// IsResolved 检查告警是否已解决
func (a *Alert) IsResolved() bool {
	return a.Status == string(AlertStatusResolved)
}

// Acknowledge 确认告警
func (a *Alert) Acknowledge(userID int64) {
	now := time.Now()
	a.Status = string(AlertStatusAcknowledged)
	a.AcknowledgedTime = &now
	a.AssignedTo = &userID
}

// Resolve 解决告警
func (a *Alert) Resolve() {
	now := time.Now()
	a.Status = string(AlertStatusResolved)
	a.ResolvedTime = &now
}

// Close 关闭告警
func (a *Alert) Close() {
	now := time.Now()
	a.Status = string(AlertStatusClosed)
	a.ClosedTime = &now
}

// AlertLevel 告警级别枚举
type AlertLevel string

const (
	AlertLevelCritical AlertLevel = "critical"
	AlertLevelWarning  AlertLevel = "warning"
	AlertLevelInfo     AlertLevel = "info"
)

// AlertStatus 告警状态枚举
type AlertStatus string

const (
	AlertStatusOpen         AlertStatus = "open"
	AlertStatusAcknowledged AlertStatus = "acknowledged"
	AlertStatusResolved     AlertStatus = "resolved"
	AlertStatusClosed       AlertStatus = "closed"
)

// AlertSource 告警来源枚举
type AlertSource string

const (
	AlertSourceSystem      AlertSource = "system"
	AlertSourceHost        AlertSource = "host"
	AlertSourceApplication AlertSource = "application"
	AlertSourceMonitoring  AlertSource = "monitoring"
	AlertSourceManual      AlertSource = "manual"
)

// IsValidLevel 检查告警级别是否有效
func IsValidAlertLevel(level string) bool {
	switch AlertLevel(level) {
	case AlertLevelCritical, AlertLevelWarning, AlertLevelInfo:
		return true
	default:
		return false
	}
}

// IsValidStatus 检查告警状态是否有效
func IsValidAlertStatus(status string) bool {
	switch AlertStatus(status) {
	case AlertStatusOpen, AlertStatusAcknowledged, AlertStatusResolved, AlertStatusClosed:
		return true
	default:
		return false
	}
}

// IsValidSource 检查告警来源是否有效
func IsValidAlertSource(source string) bool {
	switch AlertSource(source) {
	case AlertSourceSystem, AlertSourceHost, AlertSourceApplication, AlertSourceMonitoring, AlertSourceManual:
		return true
	default:
		return false
	}
}

// AlertCreateRequest 创建告警请求
type AlertCreateRequest struct {
	Title    string                 `json:"title" binding:"required,min=1,max=200"`
	Message  string                 `json:"message" binding:"required,min=1"`
	Level    string                 `json:"level" binding:"required,oneof=critical warning info"`
	Source   string                 `json:"source" binding:"required,oneof=system host application monitoring manual"`
	HostID   *int64                 `json:"host_id" binding:"omitempty"`
	RuleID   string                 `json:"rule_id" binding:"omitempty,max=50"`
	Metadata map[string]interface{} `json:"metadata" binding:"omitempty"`
}

// AlertUpdateRequest 更新告警请求
type AlertUpdateRequest struct {
	Title      *string                `json:"title" binding:"omitempty,min=1,max=200"`
	Message    *string                `json:"message" binding:"omitempty,min=1"`
	Level      *string                `json:"level" binding:"omitempty,oneof=critical warning info"`
	Status     *string                `json:"status" binding:"omitempty,oneof=open acknowledged resolved closed"`
	AssignedTo *int64                 `json:"assigned_to" binding:"omitempty"`
	Metadata   map[string]interface{} `json:"metadata" binding:"omitempty"`
}

// AlertResponse 告警响应
type AlertResponse struct {
	ID               int64                  `json:"id"`
	Title            string                 `json:"title"`
	Message          string                 `json:"message"`
	Level            string                 `json:"level"`
	Source           string                 `json:"source"`
	Status           string                 `json:"status"`
	AlertTime        time.Time              `json:"alert_time"`
	AcknowledgedTime *time.Time             `json:"acknowledged_time"`
	ResolvedTime     *time.Time             `json:"resolved_time"`
	ClosedTime       *time.Time             `json:"closed_time"`
	HostID           *int64                 `json:"host_id"`
	AssignedTo       *int64                 `json:"assigned_to"`
	CreatedBy        *int64                 `json:"created_by"`
	RuleID           string                 `json:"rule_id"`
	Fingerprint      string                 `json:"fingerprint"`
	Metadata         map[string]interface{} `json:"metadata"`
	NotificationSent bool                   `json:"notification_sent"`
	EscalationLevel  int                    `json:"escalation_level"`
	ParentAlertID    *int64                 `json:"parent_alert_id"`
	Host             *HostResponse          `json:"host,omitempty"`
	AssignedUser     *UserResponse          `json:"assigned_user,omitempty"`
	Creator          *UserResponse          `json:"creator,omitempty"`
	Duration         *int64                 `json:"duration,omitempty"` // 持续时间（秒）
}

// ToResponse 转换为响应格式
func (a *Alert) ToResponse() *AlertResponse {
	resp := &AlertResponse{
		ID:               a.ID,
		Title:            a.Title,
		Message:          a.Message,
		Level:            a.Level,
		Source:           a.Source,
		Status:           a.Status,
		AlertTime:        a.AlertTime,
		AcknowledgedTime: a.AcknowledgedTime,
		ResolvedTime:     a.ResolvedTime,
		ClosedTime:       a.ClosedTime,
		HostID:           a.HostID,
		AssignedTo:       a.AssignedTo,
		CreatedBy:        a.CreatedBy,
		RuleID:           a.RuleID,
		Fingerprint:      a.Fingerprint,
		Metadata:         a.GetMetadata(),
		NotificationSent: a.NotificationSent,
		EscalationLevel:  a.EscalationLevel,
		ParentAlertID:    a.ParentAlertID,
	}

	// 计算持续时间
	if a.ResolvedTime != nil {
		duration := int64(a.ResolvedTime.Sub(a.AlertTime).Seconds())
		resp.Duration = &duration
	} else if a.ClosedTime != nil {
		duration := int64(a.ClosedTime.Sub(a.AlertTime).Seconds())
		resp.Duration = &duration
	}

	// 关联数据
	if a.Host != nil {
		resp.Host = a.Host.ToResponse()
	}
	if a.AssignedUser != nil {
		resp.AssignedUser = a.AssignedUser.ToResponse()
	}
	if a.Creator != nil {
		resp.Creator = a.Creator.ToResponse()
	}

	return resp
}

// AlertListQuery 告警列表查询参数
type AlertListQuery struct {
	Page      int    `form:"page,default=1" binding:"min=1"`
	Limit     int    `form:"limit,default=20" binding:"min=1,max=100"`
	Level     string `form:"level" binding:"omitempty,oneof=critical warning info"`
	Status    string `form:"status" binding:"omitempty,oneof=open acknowledged resolved closed"`
	Source    string `form:"source" binding:"omitempty,oneof=system host application monitoring manual"`
	HostID    *int64 `form:"host_id" binding:"omitempty"`
	TimeRange string `form:"time_range" binding:"omitempty,oneof=1h 6h 24h 7d 30d"`
	Search    string `form:"search" binding:"omitempty,max=100"`
}

// AlertListResponse 告警列表响应
type AlertListResponse struct {
	Alerts     []*AlertResponse `json:"alerts"`
	Pagination *Pagination      `json:"pagination"`
	Summary    *AlertSummary    `json:"summary"`
}

// AlertSummary 告警摘要
type AlertSummary struct {
	Total        int64 `json:"total"`
	Open         int64 `json:"open"`
	Acknowledged int64 `json:"acknowledged"`
	Resolved     int64 `json:"resolved"`
	Critical     int64 `json:"critical"`
	Warning      int64 `json:"warning"`
	Info         int64 `json:"info"`
}

// AlertAcknowledgeRequest 确认告警请求
type AlertAcknowledgeRequest struct {
	Comment string `json:"comment" binding:"omitempty,max=500"`
}

// AlertResolveRequest 解决告警请求
type AlertResolveRequest struct {
	Resolution string `json:"resolution" binding:"required,min=1,max=1000"`
}
