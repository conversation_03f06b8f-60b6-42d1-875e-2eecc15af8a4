package service

import (
	"time"
)

// ProcessMessageRequest 处理消息请求
type ProcessMessageRequest struct {
	SessionID string                 `json:"session_id" binding:"required"`
	UserID    int64                  `json:"user_id" binding:"required"`
	Message   string                 `json:"message" binding:"required"`
	Context   map[string]interface{} `json:"context,omitempty"`
}

// ProcessMessageResponse 处理消息响应
type ProcessMessageResponse struct {
	Content            string                 `json:"content"`
	Intent             string                 `json:"intent,omitempty"`
	Confidence         float64                `json:"confidence,omitempty"`
	Parameters         map[string]interface{} `json:"parameters,omitempty"`
	ToolCalls          []ToolCallResult       `json:"tool_calls,omitempty"`
	ToolResults        []ToolResult           `json:"tool_results,omitempty"`
	RecognizedActions  []RecognizedAction     `json:"recognized_actions,omitempty"`
	ActionSuggestions  []string               `json:"action_suggestions,omitempty"`
	WorkflowSuggestion *WorkflowSuggestion    `json:"workflow_suggestion,omitempty"`
	TokenCount         int                    `json:"token_count"`
	ProcessingTime     time.Duration          `json:"processing_time"`
	Timestamp          time.Time              `json:"timestamp"`
}

// WorkflowSuggestion 工作流建议
type WorkflowSuggestion struct {
	WorkflowType    string                 `json:"workflow_type"`
	SuggestedAction string                 `json:"suggested_action"`
	Parameters      map[string]interface{} `json:"parameters"`
	Confidence      float64                `json:"confidence"`
	Description     string                 `json:"description"`
}

// RecognizedAction 识别到的操作
type RecognizedAction struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	ActionType  string                 `json:"action_type"`
	Category    string                 `json:"category"`
	RiskLevel   string                 `json:"risk_level"`
	Command     string                 `json:"command"`
	Parameters  map[string]interface{} `json:"parameters"`
	Context     string                 `json:"context"`
	Confidence  float64                `json:"confidence"`
	CreatedAt   time.Time              `json:"created_at"`
}

// ConversationContext 对话上下文
type ConversationContext struct {
	SessionID    string                 `json:"session_id"`
	UserID       int64                  `json:"user_id"`
	Messages     []ConversationMessage  `json:"messages"`
	Variables    map[string]interface{} `json:"variables"`
	LastActivity time.Time              `json:"last_activity"`
}

// ConversationMessage 对话消息
type ConversationMessage struct {
	Role      string                 `json:"role"`
	Content   string                 `json:"content"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// IntentResult 意图识别结果
type IntentResult struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Command    string                 `json:"command,omitempty"`
}

// ToolDefinition 工具定义
type ToolDefinition struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

// ToolCallResult 工具调用结果
type ToolCallResult struct {
	ID       string `json:"id"`
	Type     string `json:"type"`
	Function struct {
		Name      string `json:"name"`
		Arguments string `json:"arguments"`
	} `json:"function"`
}

// ToolResult 工具执行结果
type ToolResult struct {
	Success bool        `json:"success"`
	Data    interface{} `json:"data,omitempty"`
	Error   string      `json:"error,omitempty"`
	Raw     string      `json:"raw,omitempty"`
}

// GenerateResponseRequest 生成响应请求
type GenerateResponseRequest struct {
	SystemPrompt string               `json:"system_prompt"`
	UserMessage  string               `json:"user_message"`
	Context      *ConversationContext `json:"context,omitempty"`
	Tools        []Tool               `json:"tools,omitempty"`
}

// GenerateResponseResult 生成响应结果
type GenerateResponseResult struct {
	Content      string           `json:"content"`
	ToolCalls    []ToolCallResult `json:"tool_calls,omitempty"`
	TokenCount   int              `json:"token_count"`
	FinishReason string           `json:"finish_reason"`
}

// ConversationSummary 对话总结
type ConversationSummary struct {
	SessionID    string    `json:"session_id"`
	Summary      string    `json:"summary"`
	KeyPoints    []string  `json:"key_points"`
	ActionsTaken []string  `json:"actions_taken,omitempty"`
	NextSteps    []string  `json:"next_steps,omitempty"`
	GeneratedAt  time.Time `json:"generated_at"`
}

// CommandValidation 命令验证
type CommandValidation struct {
	Command     string    `json:"command"`
	IsSafe      bool      `json:"is_safe"`
	RiskLevel   string    `json:"risk_level"`
	Risks       []string  `json:"risks"`
	Suggestions []string  `json:"suggestions"`
	Alternative string    `json:"alternative,omitempty"`
	ValidatedAt time.Time `json:"validated_at"`
}

// EnhancedToolCall 增强的工具调用
type EnhancedToolCall struct {
	ID       string `json:"id"`
	Type     string `json:"type"`
	Function struct {
		Name      string `json:"name"`
		Arguments string `json:"arguments"`
	} `json:"function"`
	Context   *ConversationContext `json:"context,omitempty"`
	UserID    int64                `json:"user_id,omitempty"`
	SessionID string               `json:"session_id,omitempty"`
}

// AIMetrics AI服务指标
type AIMetrics struct {
	TotalRequests     int64         `json:"total_requests"`
	SuccessfulCalls   int64         `json:"successful_calls"`
	FailedCalls       int64         `json:"failed_calls"`
	AverageLatency    time.Duration `json:"average_latency"`
	TotalTokens       int64         `json:"total_tokens"`
	ToolCallsExecuted int64         `json:"tool_calls_executed"`
	LastUpdated       time.Time     `json:"last_updated"`
}

// ContextVariable 上下文变量
type ContextVariable struct {
	Key       string      `json:"key"`
	Value     interface{} `json:"value"`
	Type      string      `json:"type"`
	UpdatedAt time.Time   `json:"updated_at"`
}

// IntentPattern 意图模式
type IntentPattern struct {
	Pattern     string                 `json:"pattern"`
	IntentType  string                 `json:"intent_type"`
	Confidence  float64                `json:"confidence"`
	Parameters  map[string]interface{} `json:"parameters"`
	Examples    []string               `json:"examples"`
	Description string                 `json:"description"`
}

// ToolExecutionContext 工具执行上下文
type ToolExecutionContext struct {
	UserID      int64                  `json:"user_id"`
	SessionID   string                 `json:"session_id"`
	HostID      *int64                 `json:"host_id,omitempty"`
	Environment string                 `json:"environment,omitempty"`
	Permissions []string               `json:"permissions"`
	Variables   map[string]interface{} `json:"variables"`
	Timeout     time.Duration          `json:"timeout"`
	MaxRetries  int                    `json:"max_retries"`
}

// ResponseTemplate 响应模板
type ResponseTemplate struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Template    string                 `json:"template"`
	Variables   []string               `json:"variables"`
	IntentTypes []string               `json:"intent_types"`
	Examples    []string               `json:"examples"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ConversationFlow 对话流程
type ConversationFlow struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Steps       []FlowStep             `json:"steps"`
	Conditions  []FlowCondition        `json:"conditions"`
	Variables   map[string]interface{} `json:"variables"`
	Timeout     time.Duration          `json:"timeout"`
}

// FlowStep 流程步骤
type FlowStep struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"` // message, tool_call, condition, input
	Content   string                 `json:"content"`
	ToolCall  *ToolDefinition        `json:"tool_call,omitempty"`
	Condition *FlowCondition         `json:"condition,omitempty"`
	NextSteps []string               `json:"next_steps"`
	Variables map[string]interface{} `json:"variables"`
	Timeout   time.Duration          `json:"timeout"`
}

// FlowCondition 流程条件
type FlowCondition struct {
	Type      string      `json:"type"` // equals, contains, greater_than, less_than, exists
	Variable  string      `json:"variable"`
	Value     interface{} `json:"value"`
	Operator  string      `json:"operator"`
	TrueStep  string      `json:"true_step"`
	FalseStep string      `json:"false_step"`
}

// AIServiceConfig AI服务配置
type AIServiceConfig struct {
	MaxContextLength   int           `json:"max_context_length"`
	DefaultTemperature float64       `json:"default_temperature"`
	MaxRetries         int           `json:"max_retries"`
	Timeout            time.Duration `json:"timeout"`
	EnableToolCalls    bool          `json:"enable_tool_calls"`
	EnableContextCache bool          `json:"enable_context_cache"`
	MaxConcurrentCalls int           `json:"max_concurrent_calls"`
	RateLimitPerMinute int           `json:"rate_limit_per_minute"`
}

// ToolCallMetrics 工具调用指标
type ToolCallMetrics struct {
	ToolName        string        `json:"tool_name"`
	TotalCalls      int64         `json:"total_calls"`
	SuccessfulCalls int64         `json:"successful_calls"`
	FailedCalls     int64         `json:"failed_calls"`
	AverageLatency  time.Duration `json:"average_latency"`
	LastCalled      time.Time     `json:"last_called"`
	ErrorRate       float64       `json:"error_rate"`
}

// ConversationAnalytics 对话分析
type ConversationAnalytics struct {
	SessionID         string         `json:"session_id"`
	UserID            int64          `json:"user_id"`
	MessageCount      int            `json:"message_count"`
	Duration          time.Duration  `json:"duration"`
	IntentTypes       map[string]int `json:"intent_types"`
	ToolsUsed         map[string]int `json:"tools_used"`
	TokensUsed        int            `json:"tokens_used"`
	SatisfactionScore float64        `json:"satisfaction_score,omitempty"`
	CompletedTasks    []string       `json:"completed_tasks"`
	Issues            []string       `json:"issues,omitempty"`
	CreatedAt         time.Time      `json:"created_at"`
}

// SmartSuggestion 智能建议
type SmartSuggestion struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"` // command, action, question, tip
	Content     string                 `json:"content"`
	Context     string                 `json:"context"`
	Confidence  float64                `json:"confidence"`
	Priority    int                    `json:"priority"`
	Category    string                 `json:"category"`
	Metadata    map[string]interface{} `json:"metadata"`
	GeneratedAt time.Time              `json:"generated_at"`
}
