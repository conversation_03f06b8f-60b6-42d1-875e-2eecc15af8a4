/* ========================================
   高级搜索与过滤系统样式
   提供强大的搜索和过滤功能
   ======================================== */

/* 全局搜索容器 */
.global-search {
  position: relative;
  max-width: 500px;
  margin: 0 auto;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--bg-secondary);
  border: 2px solid var(--border-primary);
  border-radius: var(--radius-xl);
  padding: var(--space-2) var(--space-4);
  transition: all var(--duration-fast) var(--easing-ease);
}

.search-input-wrapper:focus-within {
  border-color: var(--border-focus);
  box-shadow: 0 0 0 4px rgb(37 99 235 / 0.1);
  background-color: var(--bg-primary);
}

.search-input-wrapper.has-results {
  border-bottom-left-radius: 0;
  border-bottom-right-radius: 0;
}

.search-icon {
  color: var(--text-tertiary);
  margin-right: var(--space-2);
  font-size: 18px;
}

.search-input {
  flex: 1;
  border: none;
  background: none;
  color: var(--text-primary);
  font-size: var(--font-size-base);
  outline: none;
  padding: var(--space-1) 0;
}

.search-input::placeholder {
  color: var(--text-tertiary);
}

.search-actions {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.search-filter-btn,
.search-clear-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border: none;
  background: none;
  color: var(--text-tertiary);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  font-size: 14px;
}

.search-filter-btn:hover,
.search-clear-btn:hover {
  background-color: var(--bg-hover);
  color: var(--text-secondary);
}

.search-filter-btn.active {
  background-color: var(--color-primary);
  color: white;
}

/* 搜索结果下拉 */
.search-results {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--bg-primary);
  border: 2px solid var(--border-focus);
  border-top: none;
  border-radius: 0 0 var(--radius-xl) var(--radius-xl);
  box-shadow: var(--shadow-lg);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  display: none;
}

.search-results.show {
  display: block;
}

.search-results-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--space-3) var(--space-4);
  border-bottom: 1px solid var(--border-primary);
  background: var(--bg-secondary);
}

.search-results-count {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
}

.search-results-actions {
  display: flex;
  gap: var(--space-2);
}

.search-action-btn {
  padding: var(--space-1) var(--space-2);
  border: 1px solid var(--border-primary);
  background: var(--bg-primary);
  color: var(--text-secondary);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.search-action-btn:hover {
  background: var(--bg-hover);
  border-color: var(--color-primary);
}

/* 搜索结果分组 */
.search-group {
  border-bottom: 1px solid var(--border-primary);
}

.search-group:last-child {
  border-bottom: none;
}

.search-group-header {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-2) var(--space-4);
  background: var(--bg-tertiary);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
}

.search-group-icon {
  font-size: 16px;
}

.search-group-count {
  background: var(--bg-primary);
  color: var(--text-tertiary);
  padding: 1px var(--space-1);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
}

/* 搜索结果项 */
.search-result-item {
  display: flex;
  align-items: flex-start;
  gap: var(--space-3);
  padding: var(--space-3) var(--space-4);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
  border-bottom: 1px solid var(--border-primary);
}

.search-result-item:last-child {
  border-bottom: none;
}

.search-result-item:hover {
  background: var(--bg-hover);
}

.search-result-item.selected {
  background: var(--color-primary-light);
  border-color: var(--color-primary);
}

.search-result-icon {
  width: 32px;
  height: 32px;
  border-radius: var(--radius-md);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.search-result-icon.message {
  background: linear-gradient(135deg, var(--color-info), #0c7489);
  color: white;
}

.search-result-icon.conversation {
  background: linear-gradient(135deg, var(--color-primary), var(--color-primary-hover));
  color: white;
}

.search-result-icon.command {
  background: linear-gradient(135deg, var(--color-success), #047857);
  color: white;
}

.search-result-icon.host {
  background: linear-gradient(135deg, var(--color-warning), #d97706);
  color: white;
}

.search-result-content {
  flex: 1;
  min-width: 0;
}

.search-result-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: var(--space-1);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-result-description {
  font-size: var(--font-size-xs);
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: var(--space-1);
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.search-result-meta {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

.search-result-time {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.search-result-tags {
  display: flex;
  gap: var(--space-1);
}

.search-result-tag {
  padding: 1px var(--space-1);
  background: var(--bg-tertiary);
  border-radius: var(--radius-sm);
  font-size: var(--font-size-xs);
}

/* 搜索高亮 */
.search-highlight {
  background: var(--color-warning);
  color: var(--text-primary);
  padding: 0 2px;
  border-radius: 2px;
  font-weight: var(--font-weight-medium);
}

/* 搜索过滤器面板 */
.search-filters-panel {
  position: absolute;
  top: 100%;
  right: 0;
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-lg);
  padding: var(--space-4);
  min-width: 280px;
  z-index: 1001;
  display: none;
}

.search-filters-panel.show {
  display: block;
}

.filters-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-3);
}

.filters-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-primary);
}

.filters-clear {
  font-size: var(--font-size-xs);
  color: var(--color-primary);
  cursor: pointer;
  text-decoration: none;
}

.filters-clear:hover {
  text-decoration: underline;
}

.filter-group {
  margin-bottom: var(--space-4);
}

.filter-group:last-child {
  margin-bottom: 0;
}

.filter-group-title {
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.filter-options {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.filter-option {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) 0;
  cursor: pointer;
}

.filter-checkbox {
  width: 16px;
  height: 16px;
  border: 1px solid var(--border-secondary);
  border-radius: var(--radius-sm);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all var(--duration-fast) var(--easing-ease);
}

.filter-checkbox.checked {
  background: var(--color-primary);
  border-color: var(--color-primary);
  color: white;
}

.filter-label {
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  flex: 1;
}

.filter-count {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
  background: var(--bg-tertiary);
  padding: 1px var(--space-1);
  border-radius: var(--radius-sm);
}

/* 搜索历史 */
.search-history {
  padding: var(--space-3) var(--space-4);
}

.search-history-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--space-2);
}

.search-history-title {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-semibold);
  color: var(--text-secondary);
}

.search-history-clear {
  font-size: var(--font-size-xs);
  color: var(--color-primary);
  cursor: pointer;
  text-decoration: none;
}

.search-history-clear:hover {
  text-decoration: underline;
}

.search-history-items {
  display: flex;
  flex-direction: column;
  gap: var(--space-1);
}

.search-history-item {
  display: flex;
  align-items: center;
  gap: var(--space-2);
  padding: var(--space-1) var(--space-2);
  border-radius: var(--radius-md);
  cursor: pointer;
  transition: all var(--duration-fast) var(--easing-ease);
}

.search-history-item:hover {
  background: var(--bg-hover);
}

.search-history-icon {
  color: var(--text-tertiary);
  font-size: 14px;
}

.search-history-text {
  flex: 1;
  font-size: var(--font-size-sm);
  color: var(--text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.search-history-time {
  font-size: var(--font-size-xs);
  color: var(--text-tertiary);
}

/* 空状态 */
.search-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: var(--space-8) var(--space-4);
  text-align: center;
}

.search-empty-icon {
  font-size: 3rem;
  color: var(--text-tertiary);
  margin-bottom: var(--space-3);
  opacity: 0.5;
}

.search-empty-title {
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-medium);
  color: var(--text-secondary);
  margin-bottom: var(--space-2);
}

.search-empty-description {
  font-size: var(--font-size-sm);
  color: var(--text-tertiary);
  line-height: 1.4;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .global-search {
    max-width: 100%;
  }
  
  .search-filters-panel {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border-radius: 0;
    max-width: none;
    min-width: auto;
    padding: var(--space-6) var(--space-4);
  }
  
  .search-results {
    max-height: 60vh;
  }
  
  .search-result-item {
    padding: var(--space-4);
  }
}

@media (max-width: 480px) {
  .search-input-wrapper {
    padding: var(--space-2) var(--space-3);
  }
  
  .search-results-header {
    padding: var(--space-2) var(--space-3);
  }
  
  .search-result-item {
    padding: var(--space-3);
  }
  
  .search-result-icon {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}
