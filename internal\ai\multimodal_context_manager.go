package ai

import (
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// MultimodalContextManager 多模态上下文管理器
type MultimodalContextManager struct {
	logger         *logrus.Logger
	config         *MultimodalConfig
	sessions       map[string]*MultimodalSession
	contextHistory map[string][]ContextEntry
	mutex          sync.RWMutex
}

// MultimodalSession 多模态会话
type MultimodalSession struct {
	ID                string                 `json:"id"`
	UserID            int64                  `json:"user_id"`
	StartTime         time.Time              `json:"start_time"`
	LastActivity      time.Time              `json:"last_activity"`
	PreferredModes    []InteractionMode      `json:"preferred_modes"`
	CurrentContext    *SessionContext        `json:"current_context"`
	InteractionCount  int                    `json:"interaction_count"`
	UserPreferences   map[string]interface{} `json:"user_preferences"`
	AdaptationProfile *AdaptationProfile     `json:"adaptation_profile"`
	IsActive          bool                   `json:"is_active"`
}

// SessionContext 会话上下文
type SessionContext struct {
	Topic             string                 `json:"topic"`
	Intent            string                 `json:"intent"`
	Entities          map[string]interface{} `json:"entities"`
	LastInputMode     InteractionMode        `json:"last_input_mode"`
	LastOutputMode    InteractionMode        `json:"last_output_mode"`
	ConversationState string                 `json:"conversation_state"`
	TaskContext       map[string]interface{} `json:"task_context"`
	EnvironmentInfo   map[string]interface{} `json:"environment_info"`
}

// ContextEntry 上下文条目
type ContextEntry struct {
	Timestamp   time.Time              `json:"timestamp"`
	InputMode   InteractionMode        `json:"input_mode"`
	OutputMode  InteractionMode        `json:"output_mode"`
	Content     string                 `json:"content"`
	Intent      string                 `json:"intent"`
	Entities    map[string]interface{} `json:"entities"`
	Confidence  float64                `json:"confidence"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// AdaptationProfile 适应性配置文件
type AdaptationProfile struct {
	ModePreferences   map[InteractionMode]float64 `json:"mode_preferences"`
	ResponseStyle     string                      `json:"response_style"`
	VerbosityLevel    float64                     `json:"verbosity_level"`
	TechnicalLevel    float64                     `json:"technical_level"`
	LanguagePreference string                     `json:"language_preference"`
	AccessibilityNeeds []string                   `json:"accessibility_needs"`
	LearningRate      float64                     `json:"learning_rate"`
	LastUpdated       time.Time                   `json:"last_updated"`
}

// NewMultimodalContextManager 创建多模态上下文管理器
func NewMultimodalContextManager(logger *logrus.Logger, config *MultimodalConfig) *MultimodalContextManager {
	manager := &MultimodalContextManager{
		logger:         logger,
		config:         config,
		sessions:       make(map[string]*MultimodalSession),
		contextHistory: make(map[string][]ContextEntry),
	}

	// 启动会话清理任务
	go manager.runSessionCleanup()

	logger.Info("🧠 多模态上下文管理器初始化完成")
	return manager
}

// EnhanceWithContext 使用上下文增强输入
func (mcm *MultimodalContextManager) EnhanceWithContext(sessionID string, input interface{}) (interface{}, error) {
	mcm.mutex.Lock()
	defer mcm.mutex.Unlock()

	mcm.logger.WithField("session_id", sessionID).Info("🧠 增强输入上下文")

	// 获取或创建会话
	session := mcm.getOrCreateSession(sessionID)

	// 更新会话活动时间
	session.LastActivity = time.Now()
	session.InteractionCount++

	// 分析输入内容
	inputAnalysis := mcm.analyzeInput(input)

	// 更新会话上下文
	mcm.updateSessionContext(session, inputAnalysis)

	// 创建增强的输入
	enhancedInput := mcm.createEnhancedInput(input, session, inputAnalysis)

	// 记录上下文条目
	mcm.recordContextEntry(sessionID, inputAnalysis)

	mcm.logger.WithFields(logrus.Fields{
		"session_id":        sessionID,
		"interaction_count": session.InteractionCount,
		"current_topic":     session.CurrentContext.Topic,
		"current_intent":    session.CurrentContext.Intent,
	}).Info("🧠 上下文增强完成")

	return enhancedInput, nil
}

// GetSession 获取会话信息
func (mcm *MultimodalContextManager) GetSession(sessionID string) (*MultimodalSession, error) {
	mcm.mutex.RLock()
	defer mcm.mutex.RUnlock()

	session, exists := mcm.sessions[sessionID]
	if !exists {
		return nil, fmt.Errorf("session not found: %s", sessionID)
	}

	return session, nil
}

// UpdateUserPreferences 更新用户偏好
func (mcm *MultimodalContextManager) UpdateUserPreferences(sessionID string, preferences map[string]interface{}) error {
	mcm.mutex.Lock()
	defer mcm.mutex.Unlock()

	session := mcm.getOrCreateSession(sessionID)
	session.UserPreferences = preferences

	// 更新适应性配置文件
	mcm.updateAdaptationProfile(session, preferences)

	mcm.logger.WithFields(logrus.Fields{
		"session_id":  sessionID,
		"preferences": preferences,
	}).Info("更新用户偏好")

	return nil
}

// GetContextHistory 获取上下文历史
func (mcm *MultimodalContextManager) GetContextHistory(sessionID string, limit int) ([]ContextEntry, error) {
	mcm.mutex.RLock()
	defer mcm.mutex.RUnlock()

	history, exists := mcm.contextHistory[sessionID]
	if !exists {
		return []ContextEntry{}, nil
	}

	if limit <= 0 || limit > len(history) {
		return history, nil
	}

	// 返回最近的条目
	start := len(history) - limit
	return history[start:], nil
}

// 私有方法

func (mcm *MultimodalContextManager) getOrCreateSession(sessionID string) *MultimodalSession {
	session, exists := mcm.sessions[sessionID]
	if !exists {
		session = &MultimodalSession{
			ID:           sessionID,
			StartTime:    time.Now(),
			LastActivity: time.Now(),
			PreferredModes: []InteractionMode{ModeText, ModeSpeech},
			CurrentContext: &SessionContext{
				Topic:             "general",
				Intent:            "unknown",
				Entities:          make(map[string]interface{}),
				ConversationState: "active",
				TaskContext:       make(map[string]interface{}),
				EnvironmentInfo:   make(map[string]interface{}),
			},
			InteractionCount: 0,
			UserPreferences:  make(map[string]interface{}),
			AdaptationProfile: &AdaptationProfile{
				ModePreferences: map[InteractionMode]float64{
					ModeText:    1.0,
					ModeSpeech:  0.8,
					ModeImage:   0.6,
					ModeGesture: 0.4,
					ModeVideo:   0.5,
				},
				ResponseStyle:      "friendly",
				VerbosityLevel:     0.7,
				TechnicalLevel:     0.5,
				LanguagePreference: "zh-CN",
				AccessibilityNeeds: []string{},
				LearningRate:       0.1,
				LastUpdated:        time.Now(),
			},
			IsActive: true,
		}
		mcm.sessions[sessionID] = session
		mcm.contextHistory[sessionID] = make([]ContextEntry, 0)
	}

	return session
}

func (mcm *MultimodalContextManager) analyzeInput(input interface{}) *InputAnalysis {
	analysis := &InputAnalysis{
		Content:    "",
		Intent:     "unknown",
		Entities:   make(map[string]interface{}),
		Confidence: 0.5,
		Mode:       ModeText,
		Metadata:   make(map[string]interface{}),
	}

	// 根据输入类型进行分析
	switch v := input.(type) {
	case *FusedInput:
		analysis.Content = v.Content
		analysis.Confidence = v.Confidence
		analysis.Mode = v.PrimaryMode
		analysis.Intent = mcm.extractIntent(v.Content)
		analysis.Entities = mcm.extractEntities(v.Content)

	case map[string]interface{}:
		if content, exists := v["content"]; exists {
			analysis.Content = fmt.Sprintf("%v", content)
		}
		if confidence, exists := v["confidence"]; exists {
			if conf, ok := confidence.(float64); ok {
				analysis.Confidence = conf
			}
		}
		analysis.Intent = mcm.extractIntent(analysis.Content)
		analysis.Entities = mcm.extractEntities(analysis.Content)

	default:
		analysis.Content = fmt.Sprintf("%v", input)
		analysis.Intent = mcm.extractIntent(analysis.Content)
		analysis.Entities = mcm.extractEntities(analysis.Content)
	}

	return analysis
}

func (mcm *MultimodalContextManager) extractIntent(content string) string {
	// 简化的意图识别
	content = fmt.Sprintf("%v", content)
	
	if content == "" {
		return "unknown"
	}

	// 基于关键词的简单意图识别
	keywords := map[string]string{
		"查看":   "query",
		"显示":   "display",
		"添加":   "add",
		"删除":   "delete",
		"修改":   "modify",
		"帮助":   "help",
		"状态":   "status",
		"监控":   "monitor",
		"告警":   "alert",
		"报表":   "report",
		"配置":   "config",
		"登录":   "login",
		"退出":   "logout",
	}

	for keyword, intent := range keywords {
		if contains(content, keyword) {
			return intent
		}
	}

	return "general"
}

func (mcm *MultimodalContextManager) extractEntities(content string) map[string]interface{} {
	entities := make(map[string]interface{})
	
	// 简化的实体提取
	// 在实际应用中，这里应该使用NER模型
	
	// 提取IP地址
	if ipPattern := extractIPAddress(content); ipPattern != "" {
		entities["ip_address"] = ipPattern
	}

	// 提取主机名
	if hostname := extractHostname(content); hostname != "" {
		entities["hostname"] = hostname
	}

	// 提取数字
	if numbers := extractNumbers(content); len(numbers) > 0 {
		entities["numbers"] = numbers
	}

	return entities
}

func (mcm *MultimodalContextManager) updateSessionContext(session *MultimodalSession, analysis *InputAnalysis) {
	context := session.CurrentContext

	// 更新主题（基于意图）
	if analysis.Intent != "unknown" {
		context.Topic = analysis.Intent
	}

	// 更新意图
	context.Intent = analysis.Intent

	// 合并实体
	for key, value := range analysis.Entities {
		context.Entities[key] = value
	}

	// 更新输入模态
	context.LastInputMode = analysis.Mode

	// 更新任务上下文
	mcm.updateTaskContext(context, analysis)
}

func (mcm *MultimodalContextManager) updateTaskContext(context *SessionContext, analysis *InputAnalysis) {
	// 根据意图更新任务上下文
	switch analysis.Intent {
	case "query":
		context.TaskContext["operation"] = "查询"
		context.TaskContext["target"] = analysis.Entities
	case "add":
		context.TaskContext["operation"] = "添加"
		context.TaskContext["target"] = analysis.Entities
	case "delete":
		context.TaskContext["operation"] = "删除"
		context.TaskContext["target"] = analysis.Entities
	case "monitor":
		context.TaskContext["operation"] = "监控"
		context.TaskContext["target"] = analysis.Entities
	default:
		context.TaskContext["operation"] = "通用"
	}
}

func (mcm *MultimodalContextManager) createEnhancedInput(input interface{}, session *MultimodalSession, analysis *InputAnalysis) interface{} {
	enhanced := map[string]interface{}{
		"original_input":     input,
		"session_context":    session.CurrentContext,
		"user_preferences":   session.UserPreferences,
		"adaptation_profile": session.AdaptationProfile,
		"analysis":           analysis,
		"session_info": map[string]interface{}{
			"id":                session.ID,
			"interaction_count": session.InteractionCount,
			"duration":          time.Since(session.StartTime),
		},
	}

	return enhanced
}

func (mcm *MultimodalContextManager) recordContextEntry(sessionID string, analysis *InputAnalysis) {
	entry := ContextEntry{
		Timestamp:  time.Now(),
		InputMode:  analysis.Mode,
		Content:    analysis.Content,
		Intent:     analysis.Intent,
		Entities:   analysis.Entities,
		Confidence: analysis.Confidence,
		Metadata:   analysis.Metadata,
	}

	history := mcm.contextHistory[sessionID]
	history = append(history, entry)

	// 限制历史记录数量
	maxHistory := 100
	if len(history) > maxHistory {
		history = history[len(history)-maxHistory:]
	}

	mcm.contextHistory[sessionID] = history
}

func (mcm *MultimodalContextManager) updateAdaptationProfile(session *MultimodalSession, preferences map[string]interface{}) {
	profile := session.AdaptationProfile

	// 更新模态偏好
	if modePrefs, exists := preferences["mode_preferences"]; exists {
		if prefs, ok := modePrefs.(map[string]interface{}); ok {
			for mode, pref := range prefs {
				if prefFloat, ok := pref.(float64); ok {
					profile.ModePreferences[InteractionMode(mode)] = prefFloat
				}
			}
		}
	}

	// 更新响应风格
	if style, exists := preferences["response_style"]; exists {
		if styleStr, ok := style.(string); ok {
			profile.ResponseStyle = styleStr
		}
	}

	// 更新详细程度
	if verbosity, exists := preferences["verbosity_level"]; exists {
		if verbosityFloat, ok := verbosity.(float64); ok {
			profile.VerbosityLevel = verbosityFloat
		}
	}

	// 更新技术水平
	if technical, exists := preferences["technical_level"]; exists {
		if technicalFloat, ok := technical.(float64); ok {
			profile.TechnicalLevel = technicalFloat
		}
	}

	// 更新语言偏好
	if language, exists := preferences["language"]; exists {
		if languageStr, ok := language.(string); ok {
			profile.LanguagePreference = languageStr
		}
	}

	profile.LastUpdated = time.Now()
}

func (mcm *MultimodalContextManager) runSessionCleanup() {
	ticker := time.NewTicker(30 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		mcm.cleanupInactiveSessions()
	}
}

func (mcm *MultimodalContextManager) cleanupInactiveSessions() {
	mcm.mutex.Lock()
	defer mcm.mutex.Unlock()

	now := time.Now()
	inactiveThreshold := mcm.config.MaxSessionDuration

	for sessionID, session := range mcm.sessions {
		if now.Sub(session.LastActivity) > inactiveThreshold {
			session.IsActive = false
			mcm.logger.WithField("session_id", sessionID).Info("会话已过期")
			
			// 可以选择删除过期会话或标记为非活跃
			// delete(mcm.sessions, sessionID)
			// delete(mcm.contextHistory, sessionID)
		}
	}
}

// InputAnalysis 输入分析结果
type InputAnalysis struct {
	Content    string                 `json:"content"`
	Intent     string                 `json:"intent"`
	Entities   map[string]interface{} `json:"entities"`
	Confidence float64                `json:"confidence"`
	Mode       InteractionMode        `json:"mode"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// 辅助函数

func contains(text, keyword string) bool {
	return len(text) > 0 && len(keyword) > 0 && 
		   (text == keyword || 
		    (len(text) > len(keyword) && 
		     (text[:len(keyword)] == keyword || 
		      text[len(text)-len(keyword):] == keyword ||
		      findSubstring(text, keyword))))
}

func findSubstring(text, keyword string) bool {
	for i := 0; i <= len(text)-len(keyword); i++ {
		if text[i:i+len(keyword)] == keyword {
			return true
		}
	}
	return false
}

func extractIPAddress(content string) string {
	// 简化的IP地址提取
	// 实际应该使用正则表达式
	return ""
}

func extractHostname(content string) string {
	// 简化的主机名提取
	return ""
}

func extractNumbers(content string) []float64 {
	// 简化的数字提取
	return []float64{}
}
