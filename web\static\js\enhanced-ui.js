/**
 * Enhanced UI Components for AI Ops Platform
 * 增强的用户界面组件
 */

class EnhancedUI {
    constructor() {
        this.components = new Map();
        this.animations = new AnimationManager();
        this.notifications = new NotificationManager();
        this.performance = new PerformanceMonitor();
        this.accessibility = new AccessibilityManager();
        
        this.init();
    }

    init() {
        this.initializeComponents();
        this.setupEventListeners();
        this.enablePerformanceMonitoring();
        this.setupAccessibility();
        
        console.log('Enhanced UI initialized');
    }

    initializeComponents() {
        // 初始化智能聊天组件
        this.components.set('chat', new IntelligentChatComponent());
        
        // 初始化实时监控面板
        this.components.set('monitoring', new RealTimeMonitoringPanel());
        
        // 初始化主机管理界面
        this.components.set('hostManagement', new HostManagementInterface());
        
        // 初始化性能仪表板
        this.components.set('dashboard', new PerformanceDashboard());
        
        // 初始化告警中心
        this.components.set('alerts', new AlertCenter());
    }

    setupEventListeners() {
        // 全局键盘快捷键
        document.addEventListener('keydown', this.handleGlobalKeyboard.bind(this));
        
        // 窗口大小变化
        window.addEventListener('resize', this.handleResize.bind(this));
        
        // 网络状态变化
        window.addEventListener('online', this.handleNetworkChange.bind(this));
        window.addEventListener('offline', this.handleNetworkChange.bind(this));
    }

    handleGlobalKeyboard(event) {
        // Ctrl+K: 快速搜索
        if (event.ctrlKey && event.key === 'k') {
            event.preventDefault();
            this.openQuickSearch();
        }
        
        // Ctrl+Shift+C: 打开聊天
        if (event.ctrlKey && event.shiftKey && event.key === 'C') {
            event.preventDefault();
            this.focusChat();
        }
        
        // Esc: 关闭模态框
        if (event.key === 'Escape') {
            this.closeModals();
        }
    }

    handleResize() {
        // 响应式布局调整
        this.components.forEach(component => {
            if (component.handleResize) {
                component.handleResize();
            }
        });
    }

    handleNetworkChange(event) {
        const isOnline = navigator.onLine;
        this.notifications.show({
            type: isOnline ? 'success' : 'warning',
            title: isOnline ? '网络已连接' : '网络已断开',
            message: isOnline ? '系统功能已恢复' : '部分功能可能受限',
            duration: 3000
        });
    }

    openQuickSearch() {
        const searchModal = new QuickSearchModal();
        searchModal.show();
    }

    focusChat() {
        const chatComponent = this.components.get('chat');
        if (chatComponent) {
            chatComponent.focus();
        }
    }

    closeModals() {
        document.querySelectorAll('.modal.show').forEach(modal => {
            const modalInstance = bootstrap.Modal.getInstance(modal);
            if (modalInstance) {
                modalInstance.hide();
            }
        });
    }

    enablePerformanceMonitoring() {
        this.performance.start();
    }

    setupAccessibility() {
        this.accessibility.init();
    }
}

/**
 * 智能聊天组件
 */
class IntelligentChatComponent {
    constructor() {
        this.container = document.getElementById('chat-container');
        this.input = document.getElementById('chat-input');
        this.messages = document.getElementById('chat-messages');
        this.isTyping = false;
        this.messageQueue = [];
        
        this.init();
    }

    init() {
        this.setupAutoResize();
        this.setupTypingIndicator();
        this.setupMessageQueue();
        this.setupSmartSuggestions();
    }

    setupAutoResize() {
        if (this.input) {
            this.input.addEventListener('input', () => {
                this.input.style.height = 'auto';
                this.input.style.height = Math.min(this.input.scrollHeight, 120) + 'px';
            });
        }
    }

    setupTypingIndicator() {
        // 实现打字指示器
        let typingTimer;
        if (this.input) {
            this.input.addEventListener('input', () => {
                if (!this.isTyping) {
                    this.showTypingIndicator();
                    this.isTyping = true;
                }
                
                clearTimeout(typingTimer);
                typingTimer = setTimeout(() => {
                    this.hideTypingIndicator();
                    this.isTyping = false;
                }, 1000);
            });
        }
    }

    setupMessageQueue() {
        // 实现消息队列，确保消息按顺序显示
        setInterval(() => {
            if (this.messageQueue.length > 0) {
                const message = this.messageQueue.shift();
                this.displayMessage(message);
            }
        }, 100);
    }

    setupSmartSuggestions() {
        // 实现智能建议
        if (this.input) {
            this.input.addEventListener('input', debounce(() => {
                this.showSmartSuggestions(this.input.value);
            }, 300));
        }
    }

    showTypingIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'typing-indicator';
        indicator.innerHTML = `
            <div class="typing-dots">
                <span></span>
                <span></span>
                <span></span>
            </div>
        `;
        indicator.id = 'typing-indicator';
        
        if (this.messages) {
            this.messages.appendChild(indicator);
            this.scrollToBottom();
        }
    }

    hideTypingIndicator() {
        const indicator = document.getElementById('typing-indicator');
        if (indicator) {
            indicator.remove();
        }
    }

    displayMessage(message) {
        const messageElement = this.createMessageElement(message);
        if (this.messages) {
            this.messages.appendChild(messageElement);
            this.scrollToBottom();
            this.animateMessage(messageElement);
        }
    }

    createMessageElement(message) {
        const div = document.createElement('div');
        div.className = `message ${message.type}`;
        div.innerHTML = `
            <div class="message-content">
                <div class="message-text">${message.content}</div>
                <div class="message-time">${this.formatTime(message.timestamp)}</div>
            </div>
        `;
        return div;
    }

    animateMessage(element) {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        
        requestAnimationFrame(() => {
            element.style.transition = 'all 0.3s ease';
            element.style.opacity = '1';
            element.style.transform = 'translateY(0)';
        });
    }

    scrollToBottom() {
        if (this.messages) {
            this.messages.scrollTop = this.messages.scrollHeight;
        }
    }

    formatTime(timestamp) {
        return new Date(timestamp).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit'
        });
    }

    showSmartSuggestions(input) {
        // 实现智能建议逻辑
        const suggestions = this.generateSuggestions(input);
        this.displaySuggestions(suggestions);
    }

    generateSuggestions(input) {
        const commonCommands = [
            '查看主机列表',
            '检查系统状态',
            '查看告警信息',
            '重启服务',
            '查看日志',
            '性能监控'
        ];

        return commonCommands.filter(cmd => 
            cmd.toLowerCase().includes(input.toLowerCase())
        ).slice(0, 5);
    }

    displaySuggestions(suggestions) {
        // 显示建议列表
        let suggestionContainer = document.getElementById('chat-suggestions');
        if (!suggestionContainer) {
            suggestionContainer = document.createElement('div');
            suggestionContainer.id = 'chat-suggestions';
            suggestionContainer.className = 'chat-suggestions';
            this.container.appendChild(suggestionContainer);
        }

        if (suggestions.length === 0) {
            suggestionContainer.style.display = 'none';
            return;
        }

        suggestionContainer.innerHTML = suggestions.map(suggestion => 
            `<div class="suggestion-item" onclick="selectSuggestion('${suggestion}')">${suggestion}</div>`
        ).join('');
        
        suggestionContainer.style.display = 'block';
    }

    focus() {
        if (this.input) {
            this.input.focus();
        }
    }
}

/**
 * 实时监控面板
 */
class RealTimeMonitoringPanel {
    constructor() {
        this.container = document.getElementById('monitoring-panel');
        this.charts = new Map();
        this.updateInterval = 5000; // 5秒更新一次
        
        this.init();
    }

    init() {
        this.createCharts();
        this.startRealTimeUpdates();
    }

    createCharts() {
        // CPU使用率图表
        this.charts.set('cpu', this.createChart('cpu-chart', {
            type: 'line',
            title: 'CPU使用率',
            unit: '%',
            color: '#007bff'
        }));

        // 内存使用率图表
        this.charts.set('memory', this.createChart('memory-chart', {
            type: 'line',
            title: '内存使用率',
            unit: '%',
            color: '#28a745'
        }));

        // 网络流量图表
        this.charts.set('network', this.createChart('network-chart', {
            type: 'area',
            title: '网络流量',
            unit: 'MB/s',
            color: '#ffc107'
        }));
    }

    createChart(containerId, config) {
        // 简化的图表创建逻辑
        const container = document.getElementById(containerId);
        if (!container) return null;

        // 这里应该集成真实的图表库，如Chart.js或ECharts
        container.innerHTML = `
            <div class="chart-header">
                <h6>${config.title}</h6>
                <span class="chart-value">--${config.unit}</span>
            </div>
            <div class="chart-body">
                <canvas id="${containerId}-canvas"></canvas>
            </div>
        `;

        return {
            container,
            config,
            update: (data) => this.updateChart(containerId, data)
        };
    }

    updateChart(chartId, data) {
        // 更新图表数据
        const valueElement = document.querySelector(`#${chartId} .chart-value`);
        if (valueElement && data.current !== undefined) {
            valueElement.textContent = `${data.current}${data.unit || ''}`;
        }
    }

    startRealTimeUpdates() {
        setInterval(() => {
            this.fetchMetrics();
        }, this.updateInterval);
    }

    async fetchMetrics() {
        try {
            const response = await fetch('/api/metrics/realtime');
            const metrics = await response.json();
            
            this.updateCharts(metrics);
        } catch (error) {
            console.error('Failed to fetch metrics:', error);
        }
    }

    updateCharts(metrics) {
        if (metrics.cpu) {
            this.charts.get('cpu')?.update({
                current: metrics.cpu.usage,
                unit: '%'
            });
        }

        if (metrics.memory) {
            this.charts.get('memory')?.update({
                current: metrics.memory.usage_percent,
                unit: '%'
            });
        }

        if (metrics.network) {
            this.charts.get('network')?.update({
                current: metrics.network.throughput,
                unit: 'MB/s'
            });
        }
    }

    handleResize() {
        // 响应式图表调整
        this.charts.forEach(chart => {
            if (chart.resize) {
                chart.resize();
            }
        });
    }
}

/**
 * 通知管理器
 */
class NotificationManager {
    constructor() {
        this.container = this.createContainer();
        this.notifications = new Map();
        this.maxNotifications = 5;
    }

    createContainer() {
        let container = document.getElementById('notification-container');
        if (!container) {
            container = document.createElement('div');
            container.id = 'notification-container';
            container.className = 'notification-container';
            document.body.appendChild(container);
        }
        return container;
    }

    show(options) {
        const notification = this.createNotification(options);
        const id = this.generateId();
        
        this.notifications.set(id, notification);
        this.container.appendChild(notification.element);
        
        // 限制通知数量
        if (this.notifications.size > this.maxNotifications) {
            const firstId = this.notifications.keys().next().value;
            this.remove(firstId);
        }

        // 自动移除
        if (options.duration && options.duration > 0) {
            setTimeout(() => {
                this.remove(id);
            }, options.duration);
        }

        // 动画显示
        requestAnimationFrame(() => {
            notification.element.classList.add('show');
        });

        return id;
    }

    createNotification(options) {
        const element = document.createElement('div');
        element.className = `notification notification-${options.type || 'info'}`;
        
        element.innerHTML = `
            <div class="notification-content">
                <div class="notification-title">${options.title || ''}</div>
                <div class="notification-message">${options.message || ''}</div>
            </div>
            <button class="notification-close" onclick="this.parentElement.remove()">×</button>
        `;

        return { element, options };
    }

    remove(id) {
        const notification = this.notifications.get(id);
        if (notification) {
            notification.element.classList.add('hide');
            setTimeout(() => {
                if (notification.element.parentNode) {
                    notification.element.parentNode.removeChild(notification.element);
                }
                this.notifications.delete(id);
            }, 300);
        }
    }

    generateId() {
        return `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
}

/**
 * 性能监控器
 */
class PerformanceMonitor {
    constructor() {
        this.metrics = {
            pageLoadTime: 0,
            renderTime: 0,
            memoryUsage: 0,
            networkLatency: 0
        };
        this.observers = [];
    }

    start() {
        this.measurePageLoad();
        this.measureRenderTime();
        this.measureMemoryUsage();
        this.setupPerformanceObserver();
    }

    measurePageLoad() {
        if (performance.timing) {
            this.metrics.pageLoadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
        }
    }

    measureRenderTime() {
        if (performance.getEntriesByType) {
            const paintEntries = performance.getEntriesByType('paint');
            const firstPaint = paintEntries.find(entry => entry.name === 'first-paint');
            if (firstPaint) {
                this.metrics.renderTime = firstPaint.startTime;
            }
        }
    }

    measureMemoryUsage() {
        if (performance.memory) {
            this.metrics.memoryUsage = performance.memory.usedJSHeapSize / 1024 / 1024; // MB
        }
    }

    setupPerformanceObserver() {
        if ('PerformanceObserver' in window) {
            const observer = new PerformanceObserver((list) => {
                for (const entry of list.getEntries()) {
                    this.handlePerformanceEntry(entry);
                }
            });

            observer.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
            this.observers.push(observer);
        }
    }

    handlePerformanceEntry(entry) {
        // 处理性能条目
        if (entry.entryType === 'navigation') {
            console.log('Navigation timing:', entry);
        } else if (entry.entryType === 'resource') {
            if (entry.duration > 1000) { // 超过1秒的资源加载
                console.warn('Slow resource:', entry.name, entry.duration);
            }
        }
    }

    getMetrics() {
        return { ...this.metrics };
    }
}

/**
 * 辅助函数
 */
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

function selectSuggestion(suggestion) {
    const chatInput = document.getElementById('chat-input');
    if (chatInput) {
        chatInput.value = suggestion;
        chatInput.focus();
    }
    
    const suggestions = document.getElementById('chat-suggestions');
    if (suggestions) {
        suggestions.style.display = 'none';
    }
}

// 初始化增强UI
document.addEventListener('DOMContentLoaded', () => {
    window.enhancedUI = new EnhancedUI();
});
