package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// 🎯 智能报表意图识别器 - 专门处理报表生成相关的意图识别
type IntelligentReportIntentRecognizer struct {
	deepseekClient *DeepSeekClient
	logger         *logrus.Logger
	
	// 报表类型映射
	reportTypeMap map[string]string
	
	// 时间范围映射
	timeRangeMap map[string]string
	
	// 导出格式映射
	exportFormatMap map[string]string
}

// ReportIntentResult 报表意图识别结果
type ReportIntentResult struct {
	Type           string                 `json:"type"`           // 固定为 "report_operations"
	Confidence     float64                `json:"confidence"`
	ReportType     string                 `json:"report_type"`    // operation/health/ai_usage/custom
	TimeRange      string                 `json:"time_range"`     // 24h/7d/30d/custom
	ExportFormat   string                 `json:"export_format"`  // json/csv/pdf/html
	AnalysisLevel  string                 `json:"analysis_level"` // basic/detailed/comprehensive
	IncludeInsights bool                  `json:"include_insights"`
	CustomFilters  map[string]interface{} `json:"custom_filters"`
	Description    string                 `json:"description"`
	Command        string                 `json:"command"`
}

// NewIntelligentReportIntentRecognizer 创建智能报表意图识别器
func NewIntelligentReportIntentRecognizer(deepseekClient *DeepSeekClient, logger *logrus.Logger) *IntelligentReportIntentRecognizer {
	recognizer := &IntelligentReportIntentRecognizer{
		deepseekClient: deepseekClient,
		logger:         logger,
	}
	
	// 初始化映射表
	recognizer.initializeMappings()
	
	logger.Info("🎯 智能报表意图识别器初始化完成")
	return recognizer
}

// initializeMappings 初始化映射表
func (irir *IntelligentReportIntentRecognizer) initializeMappings() {
	// 报表类型映射
	irir.reportTypeMap = map[string]string{
		"运维":     "operation",
		"操作":     "operation",
		"运维操作":   "operation",
		"系统健康":   "health",
		"健康":     "health",
		"健康度":    "health",
		"系统状态":   "health",
		"AI使用":   "ai_usage",
		"AI":      "ai_usage",
		"人工智能":   "ai_usage",
		"助手使用":   "ai_usage",
		"自定义":    "custom",
		"定制":     "custom",
		"综合":     "comprehensive",
		"全面":     "comprehensive",
	}
	
	// 时间范围映射
	irir.timeRangeMap = map[string]string{
		"今天":    "24h",
		"今日":    "24h",
		"24小时":  "24h",
		"一天":    "24h",
		"本周":    "7d",
		"这周":    "7d",
		"7天":    "7d",
		"一周":    "7d",
		"本月":    "30d",
		"这个月":   "30d",
		"30天":   "30d",
		"一个月":   "30d",
		"最近":    "24h",
		"近期":    "7d",
	}
	
	// 导出格式映射
	irir.exportFormatMap = map[string]string{
		"json":  "json",
		"csv":   "csv",
		"excel": "csv",
		"表格":   "csv",
		"pdf":   "pdf",
		"html":  "html",
		"网页":   "html",
		"默认":   "json",
	}
}

// RecognizeReportIntent 识别报表意图
func (irir *IntelligentReportIntentRecognizer) RecognizeReportIntent(ctx context.Context, message string) (*ReportIntentResult, error) {
	start := time.Now()
	
	irir.logger.WithField("message", message).Info("🎯 开始智能报表意图识别")
	
	// 预处理消息
	normalizedMessage := strings.ToLower(strings.TrimSpace(message))
	
	// 快速模式识别
	if quickResult := irir.quickRecognition(normalizedMessage, message); quickResult != nil {
		irir.logger.WithFields(logrus.Fields{
			"report_type":    quickResult.ReportType,
			"time_range":     quickResult.TimeRange,
			"export_format":  quickResult.ExportFormat,
			"processing_time": time.Since(start),
		}).Info("🎯 快速报表意图识别完成")
		return quickResult, nil
	}
	
	// AI深度识别
	aiResult, err := irir.aiRecognition(ctx, message)
	if err != nil {
		irir.logger.WithError(err).Warn("AI报表意图识别失败，使用降级模式")
		return irir.fallbackRecognition(message), nil
	}
	
	irir.logger.WithFields(logrus.Fields{
		"report_type":    aiResult.ReportType,
		"confidence":     aiResult.Confidence,
		"processing_time": time.Since(start),
	}).Info("🎯 AI报表意图识别完成")
	
	return aiResult, nil
}

// quickRecognition 快速识别模式
func (irir *IntelligentReportIntentRecognizer) quickRecognition(normalizedMessage, originalMessage string) *ReportIntentResult {
	// 检查是否包含报表关键词
	reportKeywords := []string{"报表", "报告", "统计", "分析", "生成"}
	hasReportKeyword := false
	for _, keyword := range reportKeywords {
		if strings.Contains(normalizedMessage, keyword) {
			hasReportKeyword = true
			break
		}
	}
	
	if !hasReportKeyword {
		return nil
	}
	
	result := &ReportIntentResult{
		Type:            "report_operations",
		Confidence:      0.85,
		ReportType:      "operation", // 默认运维报表
		TimeRange:       "24h",       // 默认24小时
		ExportFormat:    "json",      // 默认JSON格式
		AnalysisLevel:   "detailed",  // 默认详细分析
		IncludeInsights: true,        // 默认包含洞察
		CustomFilters:   make(map[string]interface{}),
		Description:     "生成运维报表",
		Command:         originalMessage,
	}
	
	// 识别报表类型
	for keyword, reportType := range irir.reportTypeMap {
		if strings.Contains(normalizedMessage, keyword) {
			result.ReportType = reportType
			break
		}
	}
	
	// 识别时间范围
	for keyword, timeRange := range irir.timeRangeMap {
		if strings.Contains(normalizedMessage, keyword) {
			result.TimeRange = timeRange
			break
		}
	}
	
	// 识别导出格式
	for keyword, format := range irir.exportFormatMap {
		if strings.Contains(normalizedMessage, keyword) {
			result.ExportFormat = format
			break
		}
	}
	
	// 识别分析级别
	if strings.Contains(normalizedMessage, "简单") || strings.Contains(normalizedMessage, "基础") {
		result.AnalysisLevel = "basic"
	} else if strings.Contains(normalizedMessage, "详细") || strings.Contains(normalizedMessage, "详尽") {
		result.AnalysisLevel = "detailed"
	} else if strings.Contains(normalizedMessage, "全面") || strings.Contains(normalizedMessage, "综合") {
		result.AnalysisLevel = "comprehensive"
	}
	
	// 更新描述
	result.Description = fmt.Sprintf("生成%s报表（%s，%s格式）", 
		irir.getReportTypeName(result.ReportType), 
		irir.getTimeRangeName(result.TimeRange),
		result.ExportFormat)
	
	return result
}

// aiRecognition AI深度识别
func (irir *IntelligentReportIntentRecognizer) aiRecognition(ctx context.Context, message string) (*ReportIntentResult, error) {
	systemPrompt := `你是一个专业的报表意图识别专家。请分析用户的报表需求并返回结构化结果。

支持的报表类型：
- operation: 运维操作报表
- health: 系统健康报表  
- ai_usage: AI使用报表
- custom: 自定义报表

支持的时间范围：
- 24h: 最近24小时
- 7d: 最近7天
- 30d: 最近30天

支持的导出格式：
- json: JSON格式
- csv: CSV表格格式
- pdf: PDF文档格式
- html: HTML网页格式

请返回JSON格式的结果，包含以下字段：
{
  "report_type": "报表类型",
  "time_range": "时间范围", 
  "export_format": "导出格式",
  "analysis_level": "分析级别(basic/detailed/comprehensive)",
  "include_insights": "是否包含AI洞察(true/false)",
  "description": "操作描述"
}`

	userPrompt := fmt.Sprintf("用户请求：%s\n\n请分析这个报表需求并返回结构化结果。", message)
	
	response, err := irir.deepseekClient.GenerateContent(ctx, &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{Role: "system", Content: systemPrompt},
			{Role: "user", Content: userPrompt},
		},
		MaxTokens:   1000,
		Temperature: 0.1,
	})
	
	if err != nil {
		return nil, fmt.Errorf("DeepSeek API调用失败: %w", err)
	}
	
	// 解析AI响应
	var aiResponse map[string]interface{}
	if err := json.Unmarshal([]byte(response.Content), &aiResponse); err != nil {
		return nil, fmt.Errorf("解析AI响应失败: %w", err)
	}
	
	result := &ReportIntentResult{
		Type:           "report_operations",
		Confidence:     0.9,
		CustomFilters:  make(map[string]interface{}),
		Command:        message,
	}
	
	// 提取字段
	if reportType, ok := aiResponse["report_type"].(string); ok {
		result.ReportType = reportType
	} else {
		result.ReportType = "operation"
	}
	
	if timeRange, ok := aiResponse["time_range"].(string); ok {
		result.TimeRange = timeRange
	} else {
		result.TimeRange = "24h"
	}
	
	if exportFormat, ok := aiResponse["export_format"].(string); ok {
		result.ExportFormat = exportFormat
	} else {
		result.ExportFormat = "json"
	}
	
	if analysisLevel, ok := aiResponse["analysis_level"].(string); ok {
		result.AnalysisLevel = analysisLevel
	} else {
		result.AnalysisLevel = "detailed"
	}
	
	if includeInsights, ok := aiResponse["include_insights"].(bool); ok {
		result.IncludeInsights = includeInsights
	} else {
		result.IncludeInsights = true
	}
	
	if description, ok := aiResponse["description"].(string); ok {
		result.Description = description
	} else {
		result.Description = "生成运维报表"
	}
	
	return result, nil
}

// fallbackRecognition 降级识别模式
func (irir *IntelligentReportIntentRecognizer) fallbackRecognition(message string) *ReportIntentResult {
	return &ReportIntentResult{
		Type:            "report_operations",
		Confidence:      0.7,
		ReportType:      "operation",
		TimeRange:       "24h",
		ExportFormat:    "json",
		AnalysisLevel:   "detailed",
		IncludeInsights: true,
		CustomFilters:   make(map[string]interface{}),
		Description:     "生成运维报表（降级模式）",
		Command:         message,
	}
}

// 辅助方法
func (irir *IntelligentReportIntentRecognizer) getReportTypeName(reportType string) string {
	names := map[string]string{
		"operation": "运维操作",
		"health":    "系统健康",
		"ai_usage":  "AI使用",
		"custom":    "自定义",
	}
	if name, ok := names[reportType]; ok {
		return name
	}
	return "运维操作"
}

func (irir *IntelligentReportIntentRecognizer) getTimeRangeName(timeRange string) string {
	names := map[string]string{
		"24h": "最近24小时",
		"7d":  "最近7天",
		"30d": "最近30天",
	}
	if name, ok := names[timeRange]; ok {
		return name
	}
	return "最近24小时"
}
