package security

import (
	"fmt"
	"math"
	"net"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// RiskFactor 风险因子
type RiskFactor struct {
	Name        string  `json:"name"`
	Weight      float64 `json:"weight"`
	Score       float64 `json:"score"`
	Description string  `json:"description"`
}

// RiskAssessment 风险评估结果
type RiskAssessment struct {
	OverallScore float64       `json:"overall_score"`
	RiskLevel    string        `json:"risk_level"` // low, medium, high, critical
	Factors      []RiskFactor  `json:"factors"`
	Timestamp    time.Time     `json:"timestamp"`
	Reason       string        `json:"reason"`
}

// RiskAssessor 风险评估器
type RiskAssessor struct {
	logger *logrus.Logger
}

// NewRiskAssessor 创建风险评估器
func NewRiskAssessor(logger *logrus.Logger) *RiskAssessor {
	return &RiskAssessor{
		logger: logger,
	}
}

// AssessRisk 评估风险
func (ra *RiskAssessor) AssessRisk(request *AccessRequest) float64 {
	assessment := &RiskAssessment{
		Factors:   make([]RiskFactor, 0),
		Timestamp: time.Now(),
	}

	var totalScore float64 = 0.0
	var totalWeight float64 = 0.0

	// 1. 身份风险因子
	identityRisk := ra.assessIdentityRisk(request.Identity)
	assessment.Factors = append(assessment.Factors, identityRisk)
	totalScore += identityRisk.Score * identityRisk.Weight
	totalWeight += identityRisk.Weight

	// 2. 设备风险因子
	deviceRisk := ra.assessDeviceRisk(request.Device)
	assessment.Factors = append(assessment.Factors, deviceRisk)
	totalScore += deviceRisk.Score * deviceRisk.Weight
	totalWeight += deviceRisk.Weight

	// 3. 网络风险因子
	networkRisk := ra.assessNetworkRisk(request.Context)
	assessment.Factors = append(assessment.Factors, networkRisk)
	totalScore += networkRisk.Score * networkRisk.Weight
	totalWeight += networkRisk.Weight

	// 4. 行为风险因子
	behaviorRisk := ra.assessBehaviorRisk(request)
	assessment.Factors = append(assessment.Factors, behaviorRisk)
	totalScore += behaviorRisk.Score * behaviorRisk.Weight
	totalWeight += behaviorRisk.Weight

	// 5. 资源风险因子
	resourceRisk := ra.assessResourceRisk(request.Resource, request.Action)
	assessment.Factors = append(assessment.Factors, resourceRisk)
	totalScore += resourceRisk.Score * resourceRisk.Weight
	totalWeight += resourceRisk.Weight

	// 6. 时间风险因子
	timeRisk := ra.assessTimeRisk(request.Context)
	assessment.Factors = append(assessment.Factors, timeRisk)
	totalScore += timeRisk.Score * timeRisk.Weight
	totalWeight += timeRisk.Weight

	// 计算总体风险分数
	if totalWeight > 0 {
		assessment.OverallScore = totalScore / totalWeight
	}

	// 确保分数在0-1范围内
	assessment.OverallScore = math.Max(0, math.Min(1, assessment.OverallScore))

	// 确定风险级别
	assessment.RiskLevel = ra.determineRiskLevel(assessment.OverallScore)
	assessment.Reason = ra.generateRiskReason(assessment)

	ra.logger.WithFields(logrus.Fields{
		"request_id":    request.ID,
		"identity_id":   request.Identity.ID,
		"device_id":     request.Device.ID,
		"resource":      request.Resource,
		"overall_score": assessment.OverallScore,
		"risk_level":    assessment.RiskLevel,
	}).Debug("Risk assessment completed")

	return assessment.OverallScore
}

// assessIdentityRisk 评估身份风险
func (ra *RiskAssessor) assessIdentityRisk(identity *Identity) RiskFactor {
	score := 0.0

	// 身份类型风险
	switch identity.Type {
	case "admin":
		score += 0.3 // 管理员账户风险较高
	case "service":
		score += 0.2 // 服务账户风险中等
	case "user":
		score += 0.1 // 普通用户风险较低
	}

	// 账户状态风险
	if !identity.IsActive {
		score += 0.5 // 非活跃账户风险很高
	}

	// 最近活跃时间风险
	timeSinceLastSeen := time.Since(identity.LastSeen)
	if timeSinceLastSeen > 30*24*time.Hour { // 超过30天未活跃
		score += 0.3
	} else if timeSinceLastSeen > 7*24*time.Hour { // 超过7天未活跃
		score += 0.1
	}

	// 信任分数风险（信任分数越低，风险越高）
	score += (1.0 - identity.TrustScore) * 0.3

	// 特权角色风险
	for _, role := range identity.Roles {
		switch role {
		case "admin", "root", "superuser":
			score += 0.2
		case "operator", "manager":
			score += 0.1
		}
	}

	return RiskFactor{
		Name:        "identity_risk",
		Weight:      0.25, // 25%权重
		Score:       math.Min(score, 1.0),
		Description: "Risk based on identity characteristics",
	}
}

// assessDeviceRisk 评估设备风险
func (ra *RiskAssessor) assessDeviceRisk(device *Device) RiskFactor {
	score := 0.0

	// 设备管理状态风险
	if !device.IsManaged {
		score += 0.4 // 非托管设备风险较高
	}

	// 设备合规状态风险
	if !device.IsCompliant {
		score += 0.3 // 非合规设备风险较高
	}

	// 设备类型风险
	switch device.Type {
	case "mobile":
		score += 0.2 // 移动设备风险较高
	case "iot":
		score += 0.3 // IoT设备风险更高
	case "laptop":
		score += 0.1 // 笔记本电脑风险中等
	case "server":
		score += 0.05 // 服务器风险较低
	}

	// 设备信任级别风险
	switch device.TrustLevel {
	case TrustNone:
		score += 0.5
	case TrustLow:
		score += 0.3
	case TrustMedium:
		score += 0.1
	case TrustHigh:
		score += 0.05
	case TrustFull:
		score += 0.0
	}

	// 设备最近活跃时间风险
	timeSinceLastSeen := time.Since(device.LastSeen)
	if timeSinceLastSeen > 7*24*time.Hour {
		score += 0.2
	} else if timeSinceLastSeen > 24*time.Hour {
		score += 0.1
	}

	// 操作系统风险
	if ra.isVulnerableOS(device.OS, device.Version) {
		score += 0.2
	}

	return RiskFactor{
		Name:        "device_risk",
		Weight:      0.20, // 20%权重
		Score:       math.Min(score, 1.0),
		Description: "Risk based on device characteristics",
	}
}

// assessNetworkRisk 评估网络风险
func (ra *RiskAssessor) assessNetworkRisk(context *AccessContext) RiskFactor {
	score := 0.0

	// 网络类型风险
	switch context.NetworkType {
	case "public":
		score += 0.4 // 公共网络风险高
	case "home":
		score += 0.2 // 家庭网络风险中等
	case "corporate":
		score += 0.0 // 企业网络风险低
	}

	// VPN/代理风险
	if context.IsTor {
		score += 0.6 // Tor网络风险很高
	} else if context.IsProxy {
		score += 0.3 // 代理风险较高
	} else if context.IsVPN {
		score += 0.1 // VPN风险较低
	}

	// IP地址风险
	if ra.isMaliciousIP(context.IPAddress) {
		score += 0.8 // 恶意IP风险很高
	} else if ra.isSuspiciousIP(context.IPAddress) {
		score += 0.4 // 可疑IP风险较高
	}

	// 地理位置风险
	if ra.isHighRiskLocation(context.Location) {
		score += 0.3
	}

	// 安全级别风险
	switch context.SecurityLevel {
	case "low":
		score += 0.3
	case "medium":
		score += 0.1
	case "high":
		score += 0.0
	}

	return RiskFactor{
		Name:        "network_risk",
		Weight:      0.20, // 20%权重
		Score:       math.Min(score, 1.0),
		Description: "Risk based on network characteristics",
	}
}

// assessBehaviorRisk 评估行为风险
func (ra *RiskAssessor) assessBehaviorRisk(request *AccessRequest) RiskFactor {
	score := 0.0

	// 异常时间访问风险
	if ra.isUnusualTime(request.Context.TimeOfDay, request.Context.DayOfWeek) {
		score += 0.2
	}

	// 异常地理位置风险
	if ra.isUnusualLocation(request.Identity.ID, request.Context.Location) {
		score += 0.3
	}

	// 异常设备风险
	if ra.isUnusualDevice(request.Identity.ID, request.Device.ID) {
		score += 0.2
	}

	// 频繁访问风险
	if ra.isFrequentAccess(request.Identity.ID, request.Resource) {
		score += 0.1
	}

	// 权限提升风险
	if ra.isPrivilegeEscalation(request.Action) {
		score += 0.4
	}

	return RiskFactor{
		Name:        "behavior_risk",
		Weight:      0.15, // 15%权重
		Score:       math.Min(score, 1.0),
		Description: "Risk based on behavioral patterns",
	}
}

// assessResourceRisk 评估资源风险
func (ra *RiskAssessor) assessResourceRisk(resource, action string) RiskFactor {
	score := 0.0

	// 敏感资源风险
	if ra.isSensitiveResource(resource) {
		score += 0.4
	}

	// 危险操作风险
	if ra.isDangerousAction(action) {
		score += 0.3
	}

	// 系统级资源风险
	if ra.isSystemResource(resource) {
		score += 0.2
	}

	// 数据库操作风险
	if ra.isDatabaseOperation(action) {
		score += 0.2
	}

	// 配置修改风险
	if ra.isConfigurationChange(action) {
		score += 0.3
	}

	return RiskFactor{
		Name:        "resource_risk",
		Weight:      0.15, // 15%权重
		Score:       math.Min(score, 1.0),
		Description: "Risk based on resource and action",
	}
}

// assessTimeRisk 评估时间风险
func (ra *RiskAssessor) assessTimeRisk(context *AccessContext) RiskFactor {
	score := 0.0

	// 非工作时间风险
	if !ra.isBusinessHours(context.TimeOfDay) {
		score += 0.2
	}

	// 非工作日风险
	if !ra.isBusinessDay(context.DayOfWeek) {
		score += 0.1
	}

	// 深夜访问风险
	if ra.isLateNight(context.TimeOfDay) {
		score += 0.3
	}

	// 节假日访问风险
	if ra.isHoliday(time.Now()) {
		score += 0.2
	}

	return RiskFactor{
		Name:        "time_risk",
		Weight:      0.05, // 5%权重
		Score:       math.Min(score, 1.0),
		Description: "Risk based on timing",
	}
}

// determineRiskLevel 确定风险级别
func (ra *RiskAssessor) determineRiskLevel(score float64) string {
	if score >= 0.8 {
		return "critical"
	} else if score >= 0.6 {
		return "high"
	} else if score >= 0.4 {
		return "medium"
	} else {
		return "low"
	}
}

// generateRiskReason 生成风险原因
func (ra *RiskAssessor) generateRiskReason(assessment *RiskAssessment) string {
	var reasons []string

	for _, factor := range assessment.Factors {
		if factor.Score > 0.5 {
			switch factor.Name {
			case "identity_risk":
				reasons = append(reasons, "高风险身份特征")
			case "device_risk":
				reasons = append(reasons, "不可信设备")
			case "network_risk":
				reasons = append(reasons, "可疑网络环境")
			case "behavior_risk":
				reasons = append(reasons, "异常行为模式")
			case "resource_risk":
				reasons = append(reasons, "敏感资源访问")
			case "time_risk":
				reasons = append(reasons, "异常时间访问")
			}
		}
	}

	if len(reasons) == 0 {
		return "正常访问模式"
	}

	return strings.Join(reasons, ", ")
}

// 辅助方法

func (ra *RiskAssessor) isVulnerableOS(os, version string) bool {
	// 简化实现：检查已知的易受攻击的操作系统版本
	vulnerableVersions := map[string][]string{
		"windows": {"7", "8", "8.1"},
		"ubuntu":  {"14.04", "16.04"},
		"centos":  {"6", "7"},
	}

	osLower := strings.ToLower(os)
	for osName, versions := range vulnerableVersions {
		if strings.Contains(osLower, osName) {
			for _, vulnVersion := range versions {
				if strings.Contains(version, vulnVersion) {
					return true
				}
			}
		}
	}

	return false
}

func (ra *RiskAssessor) isMaliciousIP(ipStr string) bool {
	// 简化实现：检查已知的恶意IP
	// 实际实现应该集成威胁情报源
	maliciousIPs := []string{
		"*********",
		"************",
	}

	for _, maliciousIP := range maliciousIPs {
		if ipStr == maliciousIP {
			return true
		}
	}

	return false
}

func (ra *RiskAssessor) isSuspiciousIP(ipStr string) bool {
	// 检查可疑IP段
	suspiciousCIDRs := []string{
		"*********/24",
		"************/24",
	}

	ip := net.ParseIP(ipStr)
	if ip == nil {
		return false
	}

	for _, cidrStr := range suspiciousCIDRs {
		_, cidr, err := net.ParseCIDR(cidrStr)
		if err != nil {
			continue
		}
		if cidr.Contains(ip) {
			return true
		}
	}

	return false
}

func (ra *RiskAssessor) isHighRiskLocation(location string) bool {
	highRiskCountries := []string{
		"CN", "RU", "KP", "IR", "SY",
	}

	locationUpper := strings.ToUpper(location)
	for _, country := range highRiskCountries {
		if strings.Contains(locationUpper, country) {
			return true
		}
	}

	return false
}

func (ra *RiskAssessor) isUnusualTime(timeOfDay, dayOfWeek string) bool {
	// 简化实现：非工作时间被认为是异常的
	return !ra.isBusinessHours(timeOfDay) || !ra.isBusinessDay(dayOfWeek)
}

func (ra *RiskAssessor) isUnusualLocation(identityID, location string) bool {
	// 简化实现：应该基于用户的历史位置数据
	// 这里假设所有位置都是正常的
	return false
}

func (ra *RiskAssessor) isUnusualDevice(identityID, deviceID string) bool {
	// 简化实现：应该基于用户的历史设备数据
	// 这里假设所有设备都是正常的
	return false
}

func (ra *RiskAssessor) isFrequentAccess(identityID, resource string) bool {
	// 简化实现：应该基于访问频率统计
	// 这里假设没有频繁访问
	return false
}

func (ra *RiskAssessor) isPrivilegeEscalation(action string) bool {
	escalationActions := []string{
		"sudo", "su", "runas", "elevate", "admin", "root",
	}

	actionLower := strings.ToLower(action)
	for _, escalation := range escalationActions {
		if strings.Contains(actionLower, escalation) {
			return true
		}
	}

	return false
}

func (ra *RiskAssessor) isSensitiveResource(resource string) bool {
	sensitiveResources := []string{
		"database", "config", "secret", "key", "password", "credential",
		"admin", "root", "system", "security",
	}

	resourceLower := strings.ToLower(resource)
	for _, sensitive := range sensitiveResources {
		if strings.Contains(resourceLower, sensitive) {
			return true
		}
	}

	return false
}

func (ra *RiskAssessor) isDangerousAction(action string) bool {
	dangerousActions := []string{
		"delete", "drop", "truncate", "destroy", "remove", "kill",
		"shutdown", "reboot", "format", "wipe",
	}

	actionLower := strings.ToLower(action)
	for _, dangerous := range dangerousActions {
		if strings.Contains(actionLower, dangerous) {
			return true
		}
	}

	return false
}

func (ra *RiskAssessor) isSystemResource(resource string) bool {
	systemResources := []string{
		"system", "kernel", "boot", "init", "service", "daemon",
	}

	resourceLower := strings.ToLower(resource)
	for _, system := range systemResources {
		if strings.Contains(resourceLower, system) {
			return true
		}
	}

	return false
}

func (ra *RiskAssessor) isDatabaseOperation(action string) bool {
	dbActions := []string{
		"select", "insert", "update", "delete", "create", "alter", "drop",
	}

	actionLower := strings.ToLower(action)
	for _, dbAction := range dbActions {
		if strings.Contains(actionLower, dbAction) {
			return true
		}
	}

	return false
}

func (ra *RiskAssessor) isConfigurationChange(action string) bool {
	configActions := []string{
		"configure", "config", "setting", "preference", "option",
	}

	actionLower := strings.ToLower(action)
	for _, configAction := range configActions {
		if strings.Contains(actionLower, configAction) {
			return true
		}
	}

	return false
}

func (ra *RiskAssessor) isBusinessHours(timeOfDay string) bool {
	if timeOfDay == "" {
		return false
	}

	var hour, minute int
	if _, err := fmt.Sscanf(timeOfDay, "%d:%d", &hour, &minute); err != nil {
		return false
	}

	return hour >= 9 && hour < 18
}

func (ra *RiskAssessor) isBusinessDay(dayOfWeek string) bool {
	businessDays := []string{
		"Monday", "Tuesday", "Wednesday", "Thursday", "Friday",
	}

	for _, day := range businessDays {
		if strings.EqualFold(dayOfWeek, day) {
			return true
		}
	}

	return false
}

func (ra *RiskAssessor) isLateNight(timeOfDay string) bool {
	if timeOfDay == "" {
		return false
	}

	var hour, minute int
	if _, err := fmt.Sscanf(timeOfDay, "%d:%d", &hour, &minute); err != nil {
		return false
	}

	return hour >= 22 || hour < 6
}

func (ra *RiskAssessor) isHoliday(date time.Time) bool {
	// 简化实现：检查是否为周末
	weekday := date.Weekday()
	return weekday == time.Saturday || weekday == time.Sunday
}
