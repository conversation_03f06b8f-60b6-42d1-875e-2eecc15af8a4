#!/bin/bash

echo "========================================"
echo "🚀 AI运维管理平台 - 统一执行引擎测试"
echo "========================================"
echo

echo "📋 检查Go环境..."
if ! command -v go &> /dev/null; then
    echo "❌ Go环境未安装或配置错误"
    exit 1
fi
go version

echo
echo "📦 下载依赖..."
go mod tidy
if [ $? -ne 0 ]; then
    echo "❌ 依赖下载失败"
    exit 1
fi

echo
echo "🔧 编译测试服务器..."
go build -o test_unified_execution integration_test_unified_execution.go
if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo
echo "✅ 编译成功！"
echo
echo "🚀 启动统一执行引擎测试服务器..."
echo "📡 服务器将在 http://localhost:8081 启动"
echo "🔗 测试页面: http://localhost:8081"
echo
echo "按 Ctrl+C 停止服务器"
echo "========================================"
echo

# 设置信号处理
cleanup() {
    echo
    echo "🧹 清理临时文件..."
    if [ -f test_unified_execution ]; then
        rm test_unified_execution
    fi
    echo "✅ 测试完成"
    exit 0
}

trap cleanup SIGINT SIGTERM

./test_unified_execution

cleanup
