package service

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ExecutionResultCollector 执行结果收集器
type ExecutionResultCollector struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewExecutionResultCollector 创建执行结果收集器
func NewExecutionResultCollector(db *gorm.DB, logger *logrus.Logger) *ExecutionResultCollector {
	return &ExecutionResultCollector{
		db:     db,
		logger: logger,
	}
}

// UnifiedExecutionResult 统一执行结果
type UnifiedExecutionResult struct {
	// 基础信息
	Success       bool                   `json:"success"`
	Content       string                 `json:"content"`
	Action        string                 `json:"action"`
	ExecutionTime time.Duration          `json:"execution_time"`
	Timestamp     time.Time              `json:"timestamp"`
	
	// 执行详情
	ExecutionType string                 `json:"execution_type"` // sql, shell, monitoring, config
	GeneratedCode string                 `json:"generated_code"` // 生成的SQL或命令
	RawData       interface{}            `json:"raw_data"`       // 原始执行数据
	
	// 元数据
	Data          map[string]interface{} `json:"data"`
	Metadata      map[string]interface{} `json:"metadata"`
	
	// 错误信息
	Error         string                 `json:"error,omitempty"`
	ErrorCode     string                 `json:"error_code,omitempty"`
	
	// 确认相关
	RequireConfirm bool                  `json:"require_confirm"`
	ConfirmToken   string                `json:"confirm_token,omitempty"`
}

// CollectionContext 收集上下文
type CollectionContext struct {
	UserID      int64                  `json:"user_id"`
	SessionID   string                 `json:"session_id"`
	UserInput   string                 `json:"user_input"`
	Intent      map[string]interface{} `json:"intent"`
	Operation   string                 `json:"operation"`
	StartTime   time.Time              `json:"start_time"`
}

// CollectFromDatabaseResult 从数据库执行结果收集
func (erc *ExecutionResultCollector) CollectFromDatabaseResult(
	ctx context.Context,
	result interface{},
	context *CollectionContext,
) (*UnifiedExecutionResult, error) {
	erc.logger.WithFields(logrus.Fields{
		"user_id":    context.UserID,
		"session_id": context.SessionID,
		"operation":  context.Operation,
	}).Info("ExecutionResultCollector: 收集数据库执行结果")
	
	unified := &UnifiedExecutionResult{
		Success:       true,
		ExecutionType: "sql",
		Action:        "database_query_executed",
		ExecutionTime: time.Since(context.StartTime),
		Timestamp:     time.Now(),
		RawData:       result,
		Data:          make(map[string]interface{}),
		Metadata:      make(map[string]interface{}),
	}
	
	// 根据结果类型进行处理
	switch v := result.(type) {
	case []map[string]interface{}:
		// 查询结果
		unified.Content = erc.formatDatabaseQueryResult(v, context)
		unified.Data["rows"] = v
		unified.Data["row_count"] = len(v)
		
	case map[string]interface{}:
		// 单行结果或执行结果
		if rowsAffected, ok := v["rows_affected"]; ok {
			unified.Content = erc.formatDatabaseExecutionResult(v, context)
			unified.Data["rows_affected"] = rowsAffected
		} else {
			unified.Content = erc.formatDatabaseQueryResult([]map[string]interface{}{v}, context)
			unified.Data["rows"] = []map[string]interface{}{v}
			unified.Data["row_count"] = 1
		}
		
	case string:
		// 字符串结果
		unified.Content = fmt.Sprintf("📋 **数据库操作结果**\n\n```\n%s\n```", v)
		unified.Data["message"] = v
		
	default:
		// 其他类型
		jsonData, _ := json.MarshalIndent(result, "", "  ")
		unified.Content = fmt.Sprintf("📋 **数据库操作结果**\n\n```json\n%s\n```", string(jsonData))
		unified.Data["raw_result"] = result
	}
	
	// 添加元数据
	unified.Metadata["user_input"] = context.UserInput
	unified.Metadata["operation"] = context.Operation
	unified.Metadata["intent"] = context.Intent
	
	return unified, nil
}

// CollectFromSSHResult 从SSH执行结果收集
func (erc *ExecutionResultCollector) CollectFromSSHResult(
	ctx context.Context,
	result interface{},
	context *CollectionContext,
) (*UnifiedExecutionResult, error) {
	erc.logger.WithFields(logrus.Fields{
		"user_id":    context.UserID,
		"session_id": context.SessionID,
		"operation":  context.Operation,
	}).Info("ExecutionResultCollector: 收集SSH执行结果")
	
	unified := &UnifiedExecutionResult{
		Success:       true,
		ExecutionType: "shell",
		Action:        "ssh_command_executed",
		ExecutionTime: time.Since(context.StartTime),
		Timestamp:     time.Now(),
		RawData:       result,
		Data:          make(map[string]interface{}),
		Metadata:      make(map[string]interface{}),
	}
	
	// 处理SSH执行结果
	switch v := result.(type) {
	case map[string]interface{}:
		unified.Content = erc.formatSSHResult(v, context)
		unified.Data = v
		
		// 检查执行状态
		if exitCode, ok := v["exit_code"].(int); ok {
			unified.Success = exitCode == 0
			if exitCode != 0 {
				unified.Error = fmt.Sprintf("命令执行失败，退出码: %d", exitCode)
				unified.ErrorCode = "ssh_execution_failed"
			}
		}
		
	case string:
		unified.Content = fmt.Sprintf("🖥️ **SSH命令执行结果**\n\n```\n%s\n```", v)
		unified.Data["output"] = v
		
	default:
		jsonData, _ := json.MarshalIndent(result, "", "  ")
		unified.Content = fmt.Sprintf("🖥️ **SSH命令执行结果**\n\n```json\n%s\n```", string(jsonData))
		unified.Data["raw_result"] = result
	}
	
	// 添加元数据
	unified.Metadata["user_input"] = context.UserInput
	unified.Metadata["operation"] = context.Operation
	unified.Metadata["intent"] = context.Intent
	
	return unified, nil
}

// CollectFromMonitoringResult 从监控执行结果收集
func (erc *ExecutionResultCollector) CollectFromMonitoringResult(
	ctx context.Context,
	result interface{},
	context *CollectionContext,
) (*UnifiedExecutionResult, error) {
	erc.logger.WithFields(logrus.Fields{
		"user_id":    context.UserID,
		"session_id": context.SessionID,
		"operation":  context.Operation,
	}).Info("ExecutionResultCollector: 收集监控执行结果")
	
	unified := &UnifiedExecutionResult{
		Success:       true,
		ExecutionType: "monitoring",
		Action:        "monitoring_operation_executed",
		ExecutionTime: time.Since(context.StartTime),
		Timestamp:     time.Now(),
		RawData:       result,
		Data:          make(map[string]interface{}),
		Metadata:      make(map[string]interface{}),
	}
	
	// 处理监控结果
	switch v := result.(type) {
	case map[string]interface{}:
		unified.Content = erc.formatMonitoringResult(v, context)
		unified.Data = v
		
	case string:
		unified.Content = fmt.Sprintf("📊 **监控操作结果**\n\n%s", v)
		unified.Data["report"] = v
		
	default:
		jsonData, _ := json.MarshalIndent(result, "", "  ")
		unified.Content = fmt.Sprintf("📊 **监控操作结果**\n\n```json\n%s\n```", string(jsonData))
		unified.Data["raw_result"] = result
	}
	
	// 添加元数据
	unified.Metadata["user_input"] = context.UserInput
	unified.Metadata["operation"] = context.Operation
	unified.Metadata["intent"] = context.Intent
	
	return unified, nil
}

// formatDatabaseQueryResult 格式化数据库查询结果
func (erc *ExecutionResultCollector) formatDatabaseQueryResult(rows []map[string]interface{}, context *CollectionContext) string {
	if len(rows) == 0 {
		return "📋 **数据库查询结果**\n\n暂无数据"
	}
	
	content := fmt.Sprintf("📋 **数据库查询结果** - %s\n\n", context.Operation)
	content += fmt.Sprintf("📊 **统计信息**: 共查询到 %d 条记录\n\n", len(rows))
	
	// 生成表格
	if len(rows) <= 10 {
		content += "### 📈 查询结果\n\n"
		content += erc.generateMarkdownTable(rows)
	} else {
		content += "### 📈 查询结果（前10条）\n\n"
		content += erc.generateMarkdownTable(rows[:10])
		content += fmt.Sprintf("\n*注：共%d条记录，仅显示前10条*\n", len(rows))
	}
	
	return content
}

// formatDatabaseExecutionResult 格式化数据库执行结果
func (erc *ExecutionResultCollector) formatDatabaseExecutionResult(result map[string]interface{}, context *CollectionContext) string {
	content := fmt.Sprintf("✅ **数据库操作成功** - %s\n\n", context.Operation)
	
	if rowsAffected, ok := result["rows_affected"]; ok {
		content += fmt.Sprintf("📊 **影响行数**: %v\n", rowsAffected)
	}
	
	if sql, ok := result["sql"]; ok {
		content += fmt.Sprintf("\n🔍 **执行的SQL**:\n```sql\n%s\n```\n", sql)
	}
	
	return content
}

// formatSSHResult 格式化SSH执行结果
func (erc *ExecutionResultCollector) formatSSHResult(result map[string]interface{}, context *CollectionContext) string {
	content := fmt.Sprintf("🖥️ **SSH命令执行结果** - %s\n\n", context.Operation)
	
	if command, ok := result["command"]; ok {
		content += fmt.Sprintf("📝 **执行命令**: `%s`\n\n", command)
	}
	
	if exitCode, ok := result["exit_code"]; ok {
		if exitCode == 0 {
			content += "✅ **状态**: 成功\n"
		} else {
			content += fmt.Sprintf("❌ **状态**: 失败 (退出码: %v)\n", exitCode)
		}
	}
	
	if output, ok := result["output"]; ok && output != "" {
		content += fmt.Sprintf("\n📤 **输出**:\n```\n%s\n```\n", output)
	}
	
	if stderr, ok := result["stderr"]; ok && stderr != "" {
		content += fmt.Sprintf("\n📥 **错误输出**:\n```\n%s\n```\n", stderr)
	}
	
	return content
}

// formatMonitoringResult 格式化监控结果
func (erc *ExecutionResultCollector) formatMonitoringResult(result map[string]interface{}, context *CollectionContext) string {
	content := fmt.Sprintf("📊 **监控操作结果** - %s\n\n", context.Operation)
	
	if report, ok := result["report"]; ok {
		content += fmt.Sprintf("%s\n", report)
	}
	
	if healthScore, ok := result["health_score"]; ok {
		content += fmt.Sprintf("\n🏥 **健康评分**: %.1f/100\n", healthScore)
	}
	
	if issues, ok := result["issues_found"]; ok {
		if issuesList, ok := issues.([]interface{}); ok && len(issuesList) > 0 {
			content += fmt.Sprintf("\n⚠️ **发现问题**: %d个\n", len(issuesList))
		}
	}
	
	return content
}

// generateMarkdownTable 生成Markdown表格
func (erc *ExecutionResultCollector) generateMarkdownTable(rows []map[string]interface{}) string {
	if len(rows) == 0 {
		return "暂无数据"
	}
	
	// 获取列名
	var columns []string
	for key := range rows[0] {
		columns = append(columns, key)
	}
	
	// 生成表头
	table := "|"
	for _, col := range columns {
		table += fmt.Sprintf(" %s |", col)
	}
	table += "\n|"
	for range columns {
		table += " --- |"
	}
	table += "\n"
	
	// 生成数据行
	for _, row := range rows {
		table += "|"
		for _, col := range columns {
			value := ""
			if v, ok := row[col]; ok && v != nil {
				value = fmt.Sprintf("%v", v)
			}
			table += fmt.Sprintf(" %s |", value)
		}
		table += "\n"
	}
	
	return table
}
