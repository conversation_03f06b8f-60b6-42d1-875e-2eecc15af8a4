package ai

import (
	"encoding/json"
	"fmt"
	"time"
)

// MultimodalInteractionRecord 多模态交互记录
type MultimodalInteractionRecord struct {
	ID               int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	SessionID        string    `json:"session_id" gorm:"index;size:100;not null"`
	UserID           int64     `json:"user_id" gorm:"index"`
	InteractionID    string    `json:"interaction_id" gorm:"uniqueIndex;size:100;not null"`
	InputMode        string    `json:"input_mode" gorm:"size:20;not null"`
	OutputMode       string    `json:"output_mode" gorm:"size:20;not null"`
	InputContent     string    `json:"input_content" gorm:"type:text"`
	OutputContent    string    `json:"output_content" gorm:"type:text"`
	ProcessingTime   int64     `json:"processing_time"` // 毫秒
	Confidence       float64   `json:"confidence"`
	UserSatisfaction float64   `json:"user_satisfaction"`
	QualityScore     float64   `json:"quality_score"`
	Success          bool      `json:"success" gorm:"default:true"`
	ErrorMessage     string    `json:"error_message" gorm:"type:text"`
	Metadata         string    `json:"metadata" gorm:"type:text"`
	CreatedAt        time.Time `json:"created_at"`
}

// TableName 指定表名
func (MultimodalInteractionRecord) TableName() string {
	return "multimodal_interactions"
}

// ToInteractionRecord 转换为交互记录
func (record *MultimodalInteractionRecord) ToInteractionRecord() (*InteractionRecord, error) {
	var metadata map[string]interface{}
	if record.Metadata != "" {
		if err := json.Unmarshal([]byte(record.Metadata), &metadata); err != nil {
			metadata = make(map[string]interface{})
		}
	} else {
		metadata = make(map[string]interface{})
	}

	return &InteractionRecord{
		ID:               record.InteractionID,
		SessionID:        record.SessionID,
		UserID:           record.UserID,
		InputMode:        InteractionMode(record.InputMode),
		OutputMode:       InteractionMode(record.OutputMode),
		ProcessingTime:   time.Duration(record.ProcessingTime) * time.Millisecond,
		Success:          record.Success,
		ErrorMessage:     record.ErrorMessage,
		UserSatisfaction: record.UserSatisfaction,
		Timestamp:        record.CreatedAt,
		Metadata:         metadata,
	}, nil
}

// FromInteractionRecord 从交互记录创建数据库记录
func (record *MultimodalInteractionRecord) FromInteractionRecord(ir *InteractionRecord) error {
	record.InteractionID = ir.ID
	record.SessionID = ir.SessionID
	record.UserID = ir.UserID
	record.InputMode = string(ir.InputMode)
	record.OutputMode = string(ir.OutputMode)
	record.ProcessingTime = int64(ir.ProcessingTime / time.Millisecond)
	record.Success = ir.Success
	record.ErrorMessage = ir.ErrorMessage
	record.UserSatisfaction = ir.UserSatisfaction
	record.CreatedAt = ir.Timestamp

	// 序列化元数据
	if metadataData, err := json.Marshal(ir.Metadata); err == nil {
		record.Metadata = string(metadataData)
	}

	return nil
}

// MultimodalSessionRecord 多模态会话记录
type MultimodalSessionRecord struct {
	ID                int64      `json:"id" gorm:"primaryKey;autoIncrement"`
	SessionID         string     `json:"session_id" gorm:"uniqueIndex;size:100;not null"`
	UserID            int64      `json:"user_id" gorm:"index"`
	Status            string     `json:"status" gorm:"size:20;not null"`
	StartTime         time.Time  `json:"start_time"`
	EndTime           *time.Time `json:"end_time"`
	LastActivity      time.Time  `json:"last_activity"`
	TotalDuration     int64      `json:"total_duration"` // 毫秒
	InteractionCount  int        `json:"interaction_count"`
	PreferredModes    string     `json:"preferred_modes" gorm:"type:text"`
	QualityScore      float64    `json:"quality_score"`
	UserSatisfaction  float64    `json:"user_satisfaction"`
	UserPreferences   string     `json:"user_preferences" gorm:"type:text"`
	AdaptationProfile string     `json:"adaptation_profile" gorm:"type:text"`
	Metadata          string     `json:"metadata" gorm:"type:text"`
	CreatedAt         time.Time  `json:"created_at"`
	UpdatedAt         time.Time  `json:"updated_at"`
}

// TableName 指定表名
func (MultimodalSessionRecord) TableName() string {
	return "multimodal_sessions"
}

// ToSession 转换为会话对象
func (record *MultimodalSessionRecord) ToSession() (*Session, error) {
	// 解析首选模态
	var preferredModes []InteractionMode
	if record.PreferredModes != "" {
		if err := json.Unmarshal([]byte(record.PreferredModes), &preferredModes); err != nil {
			preferredModes = []InteractionMode{ModeText}
		}
	} else {
		preferredModes = []InteractionMode{ModeText}
	}

	// 解析元数据
	var metadata map[string]interface{}
	if record.Metadata != "" {
		if err := json.Unmarshal([]byte(record.Metadata), &metadata); err != nil {
			metadata = make(map[string]interface{})
		}
	} else {
		metadata = make(map[string]interface{})
	}

	return &Session{
		ID:               record.SessionID,
		UserID:           record.UserID,
		StartTime:        record.StartTime,
		LastActivity:     record.LastActivity,
		Status:           SessionStatus(record.Status),
		InteractionCount: record.InteractionCount,
		TotalDuration:    time.Duration(record.TotalDuration) * time.Millisecond,
		PreferredModes:   preferredModes,
		QualityScore:     record.QualityScore,
		UserSatisfaction: record.UserSatisfaction,
		Metadata:         metadata,
	}, nil
}

// FromSession 从会话对象创建数据库记录
func (record *MultimodalSessionRecord) FromSession(session *Session) error {
	record.SessionID = session.ID
	record.UserID = session.UserID
	record.Status = string(session.Status)
	record.StartTime = session.StartTime
	record.LastActivity = session.LastActivity
	record.TotalDuration = int64(session.TotalDuration / time.Millisecond)
	record.InteractionCount = session.InteractionCount
	record.QualityScore = session.QualityScore
	record.UserSatisfaction = session.UserSatisfaction
	record.UpdatedAt = time.Now()

	// 序列化首选模态
	if modesData, err := json.Marshal(session.PreferredModes); err == nil {
		record.PreferredModes = string(modesData)
	}

	// 序列化元数据
	if metadataData, err := json.Marshal(session.Metadata); err == nil {
		record.Metadata = string(metadataData)
	}

	return nil
}

// MultimodalContextRecord 多模态上下文记录
type MultimodalContextRecord struct {
	ID                int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	SessionID         string    `json:"session_id" gorm:"index;size:100;not null"`
	ContextID         string    `json:"context_id" gorm:"uniqueIndex;size:100;not null"`
	Topic             string    `json:"topic" gorm:"size:100"`
	Intent            string    `json:"intent" gorm:"size:50"`
	Entities          string    `json:"entities" gorm:"type:text"`
	LastInputMode     string    `json:"last_input_mode" gorm:"size:20"`
	LastOutputMode    string    `json:"last_output_mode" gorm:"size:20"`
	ConversationState string    `json:"conversation_state" gorm:"size:50"`
	TaskContext       string    `json:"task_context" gorm:"type:text"`
	EnvironmentInfo   string    `json:"environment_info" gorm:"type:text"`
	Metadata          string    `json:"metadata" gorm:"type:text"`
	CreatedAt         time.Time `json:"created_at"`
	UpdatedAt         time.Time `json:"updated_at"`
}

// TableName 指定表名
func (MultimodalContextRecord) TableName() string {
	return "multimodal_contexts"
}

// ToSessionContext 转换为会话上下文
func (record *MultimodalContextRecord) ToSessionContext() (*SessionContext, error) {
	// 解析实体
	var entities map[string]interface{}
	if record.Entities != "" {
		if err := json.Unmarshal([]byte(record.Entities), &entities); err != nil {
			entities = make(map[string]interface{})
		}
	} else {
		entities = make(map[string]interface{})
	}

	// 解析任务上下文
	var taskContext map[string]interface{}
	if record.TaskContext != "" {
		if err := json.Unmarshal([]byte(record.TaskContext), &taskContext); err != nil {
			taskContext = make(map[string]interface{})
		}
	} else {
		taskContext = make(map[string]interface{})
	}

	// 解析环境信息
	var environmentInfo map[string]interface{}
	if record.EnvironmentInfo != "" {
		if err := json.Unmarshal([]byte(record.EnvironmentInfo), &environmentInfo); err != nil {
			environmentInfo = make(map[string]interface{})
		}
	} else {
		environmentInfo = make(map[string]interface{})
	}

	return &SessionContext{
		Topic:             record.Topic,
		Intent:            record.Intent,
		Entities:          entities,
		LastInputMode:     InteractionMode(record.LastInputMode),
		LastOutputMode:    InteractionMode(record.LastOutputMode),
		ConversationState: record.ConversationState,
		TaskContext:       taskContext,
		EnvironmentInfo:   environmentInfo,
	}, nil
}

// FromSessionContext 从会话上下文创建数据库记录
func (record *MultimodalContextRecord) FromSessionContext(sessionID string, context *SessionContext) error {
	record.SessionID = sessionID
	record.ContextID = fmt.Sprintf("%s_%d", sessionID, time.Now().UnixNano())
	record.Topic = context.Topic
	record.Intent = context.Intent
	record.LastInputMode = string(context.LastInputMode)
	record.LastOutputMode = string(context.LastOutputMode)
	record.ConversationState = context.ConversationState
	record.UpdatedAt = time.Now()

	// 序列化实体
	if entitiesData, err := json.Marshal(context.Entities); err == nil {
		record.Entities = string(entitiesData)
	}

	// 序列化任务上下文
	if taskData, err := json.Marshal(context.TaskContext); err == nil {
		record.TaskContext = string(taskData)
	}

	// 序列化环境信息
	if envData, err := json.Marshal(context.EnvironmentInfo); err == nil {
		record.EnvironmentInfo = string(envData)
	}

	return nil
}

// MultimodalMetricsRecord 多模态指标记录
type MultimodalMetricsRecord struct {
	ID                     int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	Date                   time.Time `json:"date" gorm:"index"`
	TotalInteractions      int64     `json:"total_interactions"`
	SuccessfulInteractions int64     `json:"successful_interactions"`
	AverageProcessingTime  int64     `json:"average_processing_time"` // 毫秒
	ModeUsageStats         string    `json:"mode_usage_stats" gorm:"type:text"`
	UserSatisfactionAvg    float64   `json:"user_satisfaction_avg"`
	QualityScoreAvg        float64   `json:"quality_score_avg"`
	ErrorRate              float64   `json:"error_rate"`
	TotalSessions          int64     `json:"total_sessions"`
	ActiveSessions         int64     `json:"active_sessions"`
	AverageSessionTime     int64     `json:"average_session_time"` // 毫秒
	CreatedAt              time.Time `json:"created_at"`
}

// TableName 指定表名
func (MultimodalMetricsRecord) TableName() string {
	return "multimodal_metrics"
}

// ToPerformanceMetrics 转换为性能指标
func (record *MultimodalMetricsRecord) ToPerformanceMetrics() (*PerformanceMetrics, error) {
	// 解析模态使用统计
	var modeUsageStats map[InteractionMode]int64
	if record.ModeUsageStats != "" {
		var stats map[string]int64
		if err := json.Unmarshal([]byte(record.ModeUsageStats), &stats); err == nil {
			modeUsageStats = make(map[InteractionMode]int64)
			for mode, count := range stats {
				modeUsageStats[InteractionMode(mode)] = count
			}
		}
	}
	if modeUsageStats == nil {
		modeUsageStats = make(map[InteractionMode]int64)
	}

	return &PerformanceMetrics{
		TotalInteractions:      record.TotalInteractions,
		SuccessfulInteractions: record.SuccessfulInteractions,
		AverageProcessingTime:  time.Duration(record.AverageProcessingTime) * time.Millisecond,
		ModeUsageStats:         modeUsageStats,
		UserSatisfactionAvg:    record.UserSatisfactionAvg,
		ErrorRate:              record.ErrorRate,
		LastUpdated:            record.CreatedAt,
	}, nil
}

// FromPerformanceMetrics 从性能指标创建数据库记录
func (record *MultimodalMetricsRecord) FromPerformanceMetrics(metrics *PerformanceMetrics) error {
	record.Date = time.Now().Truncate(24 * time.Hour) // 按天聚合
	record.TotalInteractions = metrics.TotalInteractions
	record.SuccessfulInteractions = metrics.SuccessfulInteractions
	record.AverageProcessingTime = int64(metrics.AverageProcessingTime / time.Millisecond)
	record.UserSatisfactionAvg = metrics.UserSatisfactionAvg
	record.ErrorRate = metrics.ErrorRate
	record.CreatedAt = time.Now()

	// 序列化模态使用统计
	if len(metrics.ModeUsageStats) > 0 {
		stats := make(map[string]int64)
		for mode, count := range metrics.ModeUsageStats {
			stats[string(mode)] = count
		}
		if statsData, err := json.Marshal(stats); err == nil {
			record.ModeUsageStats = string(statsData)
		}
	}

	return nil
}

// UserPreferenceRecord 用户偏好记录
type UserPreferenceRecord struct {
	ID                 int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID             int64     `json:"user_id" gorm:"uniqueIndex"`
	PreferredModes     string    `json:"preferred_modes" gorm:"type:text"`
	ResponseStyle      string    `json:"response_style" gorm:"size:50"`
	VerbosityLevel     float64   `json:"verbosity_level"`
	TechnicalLevel     float64   `json:"technical_level"`
	LanguagePreference string    `json:"language_preference" gorm:"size:10"`
	AccessibilityNeeds string    `json:"accessibility_needs" gorm:"type:text"`
	CustomSettings     string    `json:"custom_settings" gorm:"type:text"`
	LearningRate       float64   `json:"learning_rate"`
	CreatedAt          time.Time `json:"created_at"`
	UpdatedAt          time.Time `json:"updated_at"`
}

// TableName 指定表名
func (UserPreferenceRecord) TableName() string {
	return "user_preferences"
}

// ToAdaptationProfile 转换为适应性配置文件
func (record *UserPreferenceRecord) ToAdaptationProfile() (*AdaptationProfile, error) {
	// 解析首选模态
	var modePreferences map[InteractionMode]float64
	if record.PreferredModes != "" {
		var prefs map[string]float64
		if err := json.Unmarshal([]byte(record.PreferredModes), &prefs); err == nil {
			modePreferences = make(map[InteractionMode]float64)
			for mode, pref := range prefs {
				modePreferences[InteractionMode(mode)] = pref
			}
		}
	}
	if modePreferences == nil {
		modePreferences = map[InteractionMode]float64{
			ModeText: 1.0,
		}
	}

	// 解析无障碍需求
	var accessibilityNeeds []string
	if record.AccessibilityNeeds != "" {
		if err := json.Unmarshal([]byte(record.AccessibilityNeeds), &accessibilityNeeds); err != nil {
			accessibilityNeeds = []string{}
		}
	} else {
		accessibilityNeeds = []string{}
	}

	return &AdaptationProfile{
		ModePreferences:    modePreferences,
		ResponseStyle:      record.ResponseStyle,
		VerbosityLevel:     record.VerbosityLevel,
		TechnicalLevel:     record.TechnicalLevel,
		LanguagePreference: record.LanguagePreference,
		AccessibilityNeeds: accessibilityNeeds,
		LearningRate:       record.LearningRate,
		LastUpdated:        record.UpdatedAt,
	}, nil
}

// FromAdaptationProfile 从适应性配置文件创建数据库记录
func (record *UserPreferenceRecord) FromAdaptationProfile(userID int64, profile *AdaptationProfile) error {
	record.UserID = userID
	record.ResponseStyle = profile.ResponseStyle
	record.VerbosityLevel = profile.VerbosityLevel
	record.TechnicalLevel = profile.TechnicalLevel
	record.LanguagePreference = profile.LanguagePreference
	record.LearningRate = profile.LearningRate
	record.UpdatedAt = time.Now()

	// 序列化模态偏好
	if len(profile.ModePreferences) > 0 {
		prefs := make(map[string]float64)
		for mode, pref := range profile.ModePreferences {
			prefs[string(mode)] = pref
		}
		if prefsData, err := json.Marshal(prefs); err == nil {
			record.PreferredModes = string(prefsData)
		}
	}

	// 序列化无障碍需求
	if len(profile.AccessibilityNeeds) > 0 {
		if needsData, err := json.Marshal(profile.AccessibilityNeeds); err == nil {
			record.AccessibilityNeeds = string(needsData)
		}
	}

	return nil
}
