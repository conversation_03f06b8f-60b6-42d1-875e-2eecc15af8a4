package ai

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// SimpleAIService 简单AI服务
type SimpleAIService struct {
	intentEngine    *SimpleIntentEngine
	executionEngine *SimpleExecutionEngine
	logger          *logrus.Logger
	db              *gorm.DB
}

// SimpleMessageRequest 简单消息请求
type SimpleMessageRequest struct {
	Message   string `json:"message"`
	SessionID string `json:"session_id"`
	UserID    int64  `json:"user_id"`
}

// SimpleMessageResponse 简单消息响应
type SimpleMessageResponse struct {
	Content        string                 `json:"content"`
	Success        bool                   `json:"success"`
	Intent         string                 `json:"intent"`
	Confidence     float64                `json:"confidence"`
	Parameters     map[string]interface{} `json:"parameters"`
	TokenCount     int                    `json:"token_count"`
	ProcessingTime time.Duration          `json:"processing_time"`
	Timestamp      time.Time              `json:"timestamp"`
	Source         string                 `json:"source"`
}

// NewSimpleAIService 创建简单AI服务
func NewSimpleAIService(db *gorm.DB, hostService interface{}, logger *logrus.Logger) *SimpleAIService {
	intentEngine := NewSimpleIntentEngineFixed(logger)
	executionEngine := NewSimpleExecutionEngineFromNew(db, hostService, logger)

	return &SimpleAIService{
		intentEngine:    intentEngine,
		executionEngine: executionEngine,
		logger:          logger,
		db:              db,
	}
}

// ProcessMessage 处理消息
func (sas *SimpleAIService) ProcessMessage(ctx context.Context, req *SimpleMessageRequest) (*SimpleMessageResponse, error) {
	start := time.Now()

	sas.logger.WithFields(logrus.Fields{
		"message":    req.Message,
		"session_id": req.SessionID,
		"user_id":    req.UserID,
	}).Info("SimpleAIService: 开始处理消息")

	// 第一步：意图识别
	intent, err := sas.intentEngine.RecognizeIntent(ctx, req.Message)
	if err != nil {
		sas.logger.WithError(err).Error("意图识别失败")
		return &SimpleMessageResponse{
			Content:        "抱歉，我无法理解您的请求，请重新描述。",
			Success:        false,
			Intent:         "error",
			Confidence:     0.0,
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
			Source:         "simple_ai_service",
		}, nil
	}

	sas.logger.WithFields(logrus.Fields{
		"intent_type": intent.Type,
		"confidence":  intent.Confidence,
		"command":     intent.Command,
	}).Info("意图识别完成")

	// 第二步：执行意图
	result, err := sas.executionEngine.Execute(ctx, intent, req.UserID)
	if err != nil {
		sas.logger.WithError(err).Error("意图执行失败")
		return &SimpleMessageResponse{
			Content:        "执行过程中发生错误，请稍后重试。",
			Success:        false,
			Intent:         intent.Type,
			Confidence:     intent.Confidence,
			Parameters:     intent.Parameters,
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
			Source:         "simple_ai_service",
		}, nil
	}

	sas.logger.WithFields(logrus.Fields{
		"execution_success": result.Success,
		"processing_time":   time.Since(start),
	}).Info("意图执行完成")

	// 第三步：构建响应
	response := &SimpleMessageResponse{
		Content:        result.Message,
		Success:        result.Success,
		Intent:         intent.Type,
		Confidence:     intent.Confidence,
		Parameters:     intent.Parameters,
		TokenCount:     sas.estimateTokenCount(result.Message),
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
		Source:         "simple_ai_service",
	}

	return response, nil
}

// estimateTokenCount 估算token数量
func (sas *SimpleAIService) estimateTokenCount(content string) int {
	// 简单估算：中文字符按2个token计算，英文单词按1个token计算
	chineseCount := 0
	englishCount := 0

	for _, char := range content {
		if char >= 0x4e00 && char <= 0x9fff {
			chineseCount++
		} else if (char >= 'a' && char <= 'z') || (char >= 'A' && char <= 'Z') {
			englishCount++
		}
	}

	return chineseCount*2 + englishCount/4 // 英文按平均4个字符一个单词计算
}

// GetSystemStatus 获取系统状态
func (sas *SimpleAIService) GetSystemStatus() map[string]interface{} {
	return map[string]interface{}{
		"service_name":     "simple_ai_service",
		"version":          "1.0.0",
		"status":           "running",
		"intent_engine":    "simple_intent_engine",
		"execution_engine": "simple_execution_engine",
		"timestamp":        time.Now(),
	}
}

// HealthCheck 健康检查
func (sas *SimpleAIService) HealthCheck() bool {
	// 检查数据库连接
	if sas.db != nil {
		sqlDB, err := sas.db.DB()
		if err != nil {
			return false
		}
		if err := sqlDB.Ping(); err != nil {
			return false
		}
	}

	// 检查意图引擎
	if sas.intentEngine == nil {
		return false
	}

	// 检查执行引擎
	if sas.executionEngine == nil {
		return false
	}

	return true
}

// GetSupportedIntents 获取支持的意图类型
func (sas *SimpleAIService) GetSupportedIntents() []string {
	return []string{
		"database_operations",
		"ssh_operations",
		"monitoring_operations",
		"general_chat",
	}
}

// GetSupportedOperations 获取支持的操作
func (sas *SimpleAIService) GetSupportedOperations() map[string][]string {
	return map[string][]string{
		"database_operations": {
			"查看主机列表",
			"添加主机",
			"删除主机",
		},
		"ssh_operations": {
			"执行命令 (开发中)",
		},
		"monitoring_operations": {
			"检查主机状态 (开发中)",
		},
		"general_chat": {
			"帮助",
			"问候",
			"通用对话",
		},
	}
}

// ProcessBatch 批量处理消息
func (sas *SimpleAIService) ProcessBatch(ctx context.Context, requests []*SimpleMessageRequest) ([]*SimpleMessageResponse, error) {
	responses := make([]*SimpleMessageResponse, len(requests))

	for i, req := range requests {
		response, err := sas.ProcessMessage(ctx, req)
		if err != nil {
			responses[i] = &SimpleMessageResponse{
				Content:        "批量处理中发生错误",
				Success:        false,
				Intent:         "error",
				Confidence:     0.0,
				ProcessingTime: 0,
				Timestamp:      time.Now(),
				Source:         "simple_ai_service_batch",
			}
		} else {
			responses[i] = response
		}
	}

	return responses, nil
}

// GetMetrics 获取服务指标
func (sas *SimpleAIService) GetMetrics() map[string]interface{} {
	return map[string]interface{}{
		"service_type":      "simple_ai_service",
		"uptime":            time.Now().Format("2006-01-02 15:04:05"),
		"health_status":     sas.HealthCheck(),
		"supported_intents": len(sas.GetSupportedIntents()),
	}
}

// Close 关闭服务
func (sas *SimpleAIService) Close() error {
	sas.logger.Info("SimpleAIService: 服务正在关闭")
	return nil
}

// ValidateMessage 验证消息
func (sas *SimpleAIService) ValidateMessage(message string) error {
	if message == "" {
		return fmt.Errorf("消息不能为空")
	}

	if len(message) > 1000 {
		return fmt.Errorf("消息长度不能超过1000个字符")
	}

	return nil
}

// PreprocessMessage 预处理消息
func (sas *SimpleAIService) PreprocessMessage(message string) string {
	// 去除首尾空格
	message = strings.TrimSpace(message)

	// 替换常见的同义词
	replacements := map[string]string{
		"机器":     "主机",
		"服务器":    "主机",
		"电脑":     "主机",
		"计算机":    "主机",
		"server": "主机",
		"host":   "主机",
	}

	for old, new := range replacements {
		message = strings.ReplaceAll(message, old, new)
	}

	return message
}
