package handler

import (
	"net/http"

	"aiops-platform/internal/auth"
	"aiops-platform/internal/middleware"
	"aiops-platform/internal/model"
	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// SessionHandler 会话管理处理器
type SessionHandler struct {
	services   *service.Services
	logger     *logrus.Logger
	jwtManager *auth.EnhancedJWTManager
}

// NewSessionHandler 创建会话管理处理器
func NewSessionHandler(services *service.Services, logger *logrus.Logger, jwtManager *auth.EnhancedJWTManager) *SessionHandler {
	return &SessionHandler{
		services:   services,
		logger:     logger,
		jwtManager: jwtManager,
	}
}

// GetUserSessions 获取用户会话列表
func (h *SessionHandler) GetUserSessions(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.AbortWithAppError(c, model.ErrCodeUnauthorized, "用户未认证", nil)
		return
	}

	ctx := c.Request.Context()
	sessions, err := h.jwtManager.GetUserSessions(ctx, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user sessions")
		middleware.AbortWithAppError(c, model.ErrCodeDatabaseError, "获取会话列表失败", err)
		return
	}

	// 转换为响应格式
	sessionResponses := make([]*SessionResponse, len(sessions))
	for i, session := range sessions {
		sessionResponses[i] = &SessionResponse{
			SessionID:    session.SessionID,
			DeviceID:     session.DeviceID,
			DeviceInfo:   session.DeviceInfo,
			IPAddress:    session.IPAddress,
			UserAgent:    session.UserAgent,
			LastActivity: session.LastActivity.Unix(),
			ExpiresAt:    session.ExpiresAt.Unix(),
			IsActive:     session.IsActive,
			IsCurrent:    false, // TODO: 检查是否为当前会话
		}
	}

	response := model.SuccessResponse(sessionResponses, requestID)
	c.JSON(http.StatusOK, response)
}

// RevokeSession 撤销指定会话
func (h *SessionHandler) RevokeSession(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.AbortWithAppError(c, model.ErrCodeUnauthorized, "用户未认证", nil)
		return
	}

	sessionID := c.Param("sessionId")
	if sessionID == "" {
		middleware.AbortWithAppError(c, model.ErrCodeInvalidParams, "会话ID不能为空", nil)
		return
	}

	ctx := c.Request.Context()

	// 验证会话属于当前用户
	session, err := h.jwtManager.GetUserSessions(ctx, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user sessions")
		middleware.AbortWithAppError(c, model.ErrCodeDatabaseError, "获取会话信息失败", err)
		return
	}

	found := false
	for _, s := range session {
		if s.SessionID == sessionID {
			found = true
			break
		}
	}

	if !found {
		middleware.AbortWithAppError(c, model.ErrCodeSessionNotFound, "会话不存在或不属于当前用户", nil)
		return
	}

	// 撤销会话
	if err := h.jwtManager.RevokeSession(ctx, sessionID); err != nil {
		h.logger.WithError(err).Error("Failed to revoke session")
		middleware.AbortWithAppError(c, model.ErrCodeInternalError, "撤销会话失败", err)
		return
	}

	response := model.SuccessResponse(gin.H{"message": "会话已撤销"}, requestID)
	c.JSON(http.StatusOK, response)
}

// RevokeAllSessions 撤销用户的所有会话
func (h *SessionHandler) RevokeAllSessions(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.AbortWithAppError(c, model.ErrCodeUnauthorized, "用户未认证", nil)
		return
	}

	ctx := c.Request.Context()
	if err := h.jwtManager.RevokeAllUserSessions(ctx, userID); err != nil {
		h.logger.WithError(err).Error("Failed to revoke all user sessions")
		middleware.AbortWithAppError(c, model.ErrCodeInternalError, "撤销所有会话失败", err)
		return
	}

	response := model.SuccessResponse(gin.H{"message": "所有会话已撤销"}, requestID)
	c.JSON(http.StatusOK, response)
}

// GetUserDevices 获取用户设备列表
func (h *SessionHandler) GetUserDevices(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.AbortWithAppError(c, model.ErrCodeUnauthorized, "用户未认证", nil)
		return
	}

	ctx := c.Request.Context()
	devices, err := h.jwtManager.GetUserDevices(ctx, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user devices")
		middleware.AbortWithAppError(c, model.ErrCodeDatabaseError, "获取设备列表失败", err)
		return
	}

	// 转换为响应格式
	deviceResponses := make([]*DeviceResponse, len(devices))
	for i, device := range devices {
		deviceResponses[i] = &DeviceResponse{
			DeviceID:   device.DeviceID,
			DeviceName: device.DeviceName,
			DeviceType: device.DeviceType,
			Platform:   device.Platform,
			IPAddress:  device.IPAddress,
			LastSeen:   device.LastSeen.Unix(),
			IsActive:   device.IsActive,
			IsTrusted:  device.IsTrusted,
		}
	}

	response := model.SuccessResponse(deviceResponses, requestID)
	c.JSON(http.StatusOK, response)
}

// TrustDevice 信任设备
func (h *SessionHandler) TrustDevice(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.AbortWithAppError(c, model.ErrCodeUnauthorized, "用户未认证", nil)
		return
	}

	deviceID := c.Param("deviceId")
	if deviceID == "" {
		middleware.AbortWithAppError(c, model.ErrCodeInvalidParams, "设备ID不能为空", nil)
		return
	}

	ctx := c.Request.Context()

	// 验证设备属于当前用户
	devices, err := h.jwtManager.GetUserDevices(ctx, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user devices")
		middleware.AbortWithAppError(c, model.ErrCodeDatabaseError, "获取设备信息失败", err)
		return
	}

	found := false
	for _, d := range devices {
		if d.DeviceID == deviceID {
			found = true
			break
		}
	}

	if !found {
		middleware.AbortWithAppError(c, model.ErrCodeHostNotFound, "设备不存在或不属于当前用户", nil)
		return
	}

	// 信任设备
	if err := h.jwtManager.TrustDevice(ctx, deviceID); err != nil {
		h.logger.WithError(err).Error("Failed to trust device")
		middleware.AbortWithAppError(c, model.ErrCodeInternalError, "信任设备失败", err)
		return
	}

	response := model.SuccessResponse(gin.H{"message": "设备已信任"}, requestID)
	c.JSON(http.StatusOK, response)
}

// RevokeDevice 撤销设备
func (h *SessionHandler) RevokeDevice(c *gin.Context) {
	requestID := middleware.GetRequestID(c)
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		middleware.AbortWithAppError(c, model.ErrCodeUnauthorized, "用户未认证", nil)
		return
	}

	deviceID := c.Param("deviceId")
	if deviceID == "" {
		middleware.AbortWithAppError(c, model.ErrCodeInvalidParams, "设备ID不能为空", nil)
		return
	}

	ctx := c.Request.Context()

	// 验证设备属于当前用户
	devices, err := h.jwtManager.GetUserDevices(ctx, userID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get user devices")
		middleware.AbortWithAppError(c, model.ErrCodeDatabaseError, "获取设备信息失败", err)
		return
	}

	found := false
	for _, d := range devices {
		if d.DeviceID == deviceID {
			found = true
			break
		}
	}

	if !found {
		middleware.AbortWithAppError(c, model.ErrCodeHostNotFound, "设备不存在或不属于当前用户", nil)
		return
	}

	// 撤销设备
	if err := h.jwtManager.RevokeDevice(ctx, deviceID); err != nil {
		h.logger.WithError(err).Error("Failed to revoke device")
		middleware.AbortWithAppError(c, model.ErrCodeInternalError, "撤销设备失败", err)
		return
	}

	response := model.SuccessResponse(gin.H{"message": "设备已撤销"}, requestID)
	c.JSON(http.StatusOK, response)
}

// RefreshToken 刷新令牌
func (h *SessionHandler) RefreshToken(c *gin.Context) {
	requestID := middleware.GetRequestID(c)

	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		middleware.AbortWithAppError(c, model.ErrCodeInvalidParams, "请求参数无效", err)
		return
	}

	ctx := c.Request.Context()
	tokenPair, err := h.jwtManager.RefreshTokenPair(ctx, req.RefreshToken)
	if err != nil {
		h.logger.WithError(err).Error("Failed to refresh token")
		middleware.AbortWithAppError(c, model.ErrCodeTokenExpired, "刷新令牌失败", err)
		return
	}

	response := model.SuccessResponse(tokenPair, requestID)
	c.JSON(http.StatusOK, response)
}

// SessionResponse 会话响应
type SessionResponse struct {
	SessionID    string `json:"session_id"`
	DeviceID     string `json:"device_id"`
	DeviceInfo   string `json:"device_info"`
	IPAddress    string `json:"ip_address"`
	UserAgent    string `json:"user_agent"`
	LastActivity int64  `json:"last_activity"`
	ExpiresAt    int64  `json:"expires_at"`
	IsActive     bool   `json:"is_active"`
	IsCurrent    bool   `json:"is_current"`
}

// DeviceResponse 设备响应
type DeviceResponse struct {
	DeviceID   string `json:"device_id"`
	DeviceName string `json:"device_name"`
	DeviceType string `json:"device_type"`
	Platform   string `json:"platform"`
	IPAddress  string `json:"ip_address"`
	LastSeen   int64  `json:"last_seen"`
	IsActive   bool   `json:"is_active"`
	IsTrusted  bool   `json:"is_trusted"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}
