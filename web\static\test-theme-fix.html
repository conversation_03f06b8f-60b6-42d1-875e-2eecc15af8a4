<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>主题管理器修复测试</title>
    <link rel="stylesheet" href="css/design-system.css">
    <link rel="stylesheet" href="css/theme-manager.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: var(--bg-primary);
            color: var(--text-primary);
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-secondary);
            border-radius: 12px;
            box-shadow: var(--shadow-medium);
        }
        
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-tertiary);
            border-radius: 8px;
            border: 1px solid var(--border-primary);
        }
        
        .test-button {
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        
        .test-button:hover {
            background: var(--accent-hover);
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        
        .status.success {
            background: var(--success-bg);
            color: var(--success-text);
            border: 1px solid var(--success-border);
        }
        
        .status.error {
            background: var(--error-bg);
            color: var(--error-text);
            border: 1px solid var(--error-border);
        }
        
        .status.warning {
            background: var(--warning-bg);
            color: var(--warning-text);
            border: 1px solid var(--warning-border);
        }
        
        .navbar-right {
            position: fixed;
            top: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
        }
        
        .settings-toggle-btn {
            background: var(--accent-primary);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 50%;
            cursor: pointer;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .log-output {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="navbar-right"></div>
    
    <div class="test-container">
        <h1>🎨 主题管理器修复测试</h1>
        <p>这个页面用于测试主题管理器的修复效果，特别是字体滑块的空指针异常问题。</p>
        
        <div class="test-section">
            <h3>🔧 自动检测</h3>
            <div id="auto-status" class="status warning">正在检测...</div>
            <button class="test-button" onclick="runAutoTest()">重新检测</button>
        </div>
        
        <div class="test-section">
            <h3>🧪 手动测试</h3>
            <button class="test-button" onclick="testThemeManager()">测试主题管理器</button>
            <button class="test-button" onclick="testSettingsPanel()">测试设置面板</button>
            <button class="test-button" onclick="testFontSlider()">测试字体滑块</button>
            <button class="test-button" onclick="fixIssues()">修复问题</button>
            <div id="manual-status" class="status">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h3>📊 控制台日志</h3>
            <button class="test-button" onclick="clearLog()">清空日志</button>
            <button class="test-button" onclick="exportLog()">导出日志</button>
            <div id="log-output" class="log-output"></div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/theme-manager.js"></script>
    <script src="js/debug-helper.js"></script>
    
    <script>
        // 日志捕获
        const logOutput = document.getElementById('log-output');
        const originalConsole = {
            log: console.log,
            error: console.error,
            warn: console.warn
        };
        
        function addToLog(type, ...args) {
            const timestamp = new Date().toLocaleTimeString();
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            
            logOutput.textContent += `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logOutput.scrollTop = logOutput.scrollHeight;
            
            // 调用原始console方法
            originalConsole[type](...args);
        }
        
        console.log = (...args) => addToLog('log', ...args);
        console.error = (...args) => addToLog('error', ...args);
        console.warn = (...args) => addToLog('warn', ...args);
        
        // 测试函数
        function runAutoTest() {
            const statusEl = document.getElementById('auto-status');
            
            try {
                console.log('🔍 开始自动检测...');
                
                // 检查主题管理器
                if (window.themeManager) {
                    console.log('✅ ThemeManager已加载');
                    
                    // 检查设置面板
                    const settingsPanel = document.getElementById('settings-panel');
                    if (settingsPanel) {
                        console.log('✅ 设置面板已创建');
                        
                        // 检查字体滑块
                        const fontSlider = settingsPanel.querySelector('.font-size-slider');
                        const fontValue = settingsPanel.querySelector('.font-size-value');
                        
                        if (fontSlider && fontValue) {
                            statusEl.className = 'status success';
                            statusEl.textContent = '✅ 所有组件正常工作';
                            console.log('✅ 字体滑块元素存在且正常');
                        } else {
                            statusEl.className = 'status error';
                            statusEl.textContent = '❌ 字体滑块元素缺失';
                            console.log('❌ 字体滑块元素缺失');
                        }
                    } else {
                        statusEl.className = 'status warning';
                        statusEl.textContent = '⚠️ 设置面板未创建';
                        console.log('⚠️ 设置面板未创建');
                    }
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = '❌ ThemeManager未加载';
                    console.log('❌ ThemeManager未加载');
                }
            } catch (error) {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 检测过程中发生错误';
                console.error('❌ 自动检测失败:', error);
            }
        }
        
        function testThemeManager() {
            const statusEl = document.getElementById('manual-status');
            
            if (window.themeManager) {
                statusEl.className = 'status success';
                statusEl.textContent = '✅ 主题管理器测试通过';
                console.log('✅ 主题管理器测试通过');
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 主题管理器未找到';
                console.error('❌ 主题管理器未找到');
            }
        }
        
        function testSettingsPanel() {
            const statusEl = document.getElementById('manual-status');
            const panel = document.getElementById('settings-panel');
            
            if (panel) {
                statusEl.className = 'status success';
                statusEl.textContent = '✅ 设置面板测试通过';
                console.log('✅ 设置面板测试通过');
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 设置面板未找到';
                console.error('❌ 设置面板未找到');
            }
        }
        
        function testFontSlider() {
            const statusEl = document.getElementById('manual-status');
            const panel = document.getElementById('settings-panel');
            
            if (panel) {
                const fontSlider = panel.querySelector('.font-size-slider');
                const fontValue = panel.querySelector('.font-size-value');
                
                if (fontSlider && fontValue) {
                    try {
                        // 测试事件触发
                        fontSlider.dispatchEvent(new Event('input'));
                        statusEl.className = 'status success';
                        statusEl.textContent = '✅ 字体滑块测试通过';
                        console.log('✅ 字体滑块测试通过');
                    } catch (error) {
                        statusEl.className = 'status error';
                        statusEl.textContent = '❌ 字体滑块事件测试失败';
                        console.error('❌ 字体滑块事件测试失败:', error);
                    }
                } else {
                    statusEl.className = 'status error';
                    statusEl.textContent = '❌ 字体滑块元素未找到';
                    console.error('❌ 字体滑块元素未找到');
                }
            } else {
                statusEl.className = 'status error';
                statusEl.textContent = '❌ 设置面板未找到';
                console.error('❌ 设置面板未找到');
            }
        }
        
        function clearLog() {
            logOutput.textContent = '';
        }
        
        function exportLog() {
            const blob = new Blob([logOutput.textContent], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `theme-fix-test-${new Date().toISOString().slice(0, 19)}.log`;
            a.click();
            URL.revokeObjectURL(url);
        }
        
        // 页面加载完成后自动运行检测
        window.addEventListener('load', () => {
            setTimeout(runAutoTest, 1000);
        });
        
        console.log('🧪 主题管理器修复测试页面已加载');
    </script>
</body>
</html>
