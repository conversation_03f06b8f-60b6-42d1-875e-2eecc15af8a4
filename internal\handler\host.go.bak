package handler

import (
	"net/http"
	"strconv"

	"aiops-platform/internal/middleware"
	"aiops-platform/internal/model"
	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// CreateHost 创建主机
func (h *HostHandler) CreateHost(c *gin.Context) {
	var req model.HostCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 获取当前用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "Authentication required",
		})
		return
	}

	// TODO: 设置创建者 (HostCreateRequest中没有CreatedBy字段)
	// req.CreatedBy = userID

	host, err := h.services.Host.CreateHost(&req)
	if err != nil {
		h.logger.WithFields(logrus.Fields{
			"user_id": userID,
			"error":   err.Error(),
		}).Error("Failed to create host")

		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": err.Error(),
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"host_id":   host.ID,
		"host_name": host.Name,
		"user_id":   userID,
	}).Info("Host created successfully")

	c.JSON(http.StatusCreated, gin.H{
		"code":    201,
		"message": "Host created successfully",
		"data":    host,
	})
}

// GetHost 获取主机详情
func (h *HostHandler) GetHost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	host, err := h.services.Host.GetHostByID(id)
	if err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to get host",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Host retrieved successfully",
		"data":    host,
	})
}

// UpdateHost 更新主机
func (h *HostHandler) UpdateHost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	var req service.UpdateHostRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	host, err := h.services.Host.UpdateHost(id, &req)
	if err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
		} else {
			c.JSON(http.StatusBadRequest, gin.H{
				"code":    400,
				"message": err.Error(),
			})
		}
		return
	}

	userID, _ := middleware.GetUserIDFromContext(c)
	h.logger.WithFields(logrus.Fields{
		"host_id":   host.ID,
		"host_name": host.Name,
		"user_id":   userID,
	}).Info("Host updated successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Host updated successfully",
		"data":    host,
	})
}

// DeleteHost 删除主机
func (h *HostHandler) DeleteHost(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	if err := h.services.Host.DeleteHost(id); err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to delete host",
			})
		}
		return
	}

	userID, _ := middleware.GetUserIDFromContext(c)
	h.logger.WithFields(logrus.Fields{
		"host_id": id,
		"user_id": userID,
	}).Info("Host deleted successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Host deleted successfully",
	})
}

// ListHosts 获取主机列表
func (h *HostHandler) ListHosts(c *gin.Context) {
	var req service.ListHostsRequest

	// 解析查询参数
	if pageStr := c.Query("page"); pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil && page > 0 {
			req.Page = page
		}
	}
	if req.Page == 0 {
		req.Page = 1
	}

	if limitStr := c.Query("limit"); limitStr != "" {
		if limit, err := strconv.Atoi(limitStr); err == nil && limit > 0 && limit <= 100 {
			req.Limit = limit
		}
	}
	if req.Limit == 0 {
		req.Limit = 20
	}

	req.Status = c.Query("status")
	req.Environment = c.Query("environment")
	req.GroupName = c.Query("group_name")
	req.Search = c.Query("search")
	req.Tags = c.Query("tags")

	hosts, err := h.services.Host.ListHosts(&req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to list hosts")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to get hosts",
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Hosts retrieved successfully",
		"data":    hosts,
	})
}

// TestConnection 测试主机连接
func (h *HostHandler) TestConnection(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	result, err := h.services.Host.TestConnection(id)
	if err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to test connection",
			})
		}
		return
	}

	userID, _ := middleware.GetUserIDFromContext(c)
	h.logger.WithFields(logrus.Fields{
		"host_id":  id,
		"success":  result.Success,
		"duration": result.Duration,
		"user_id":  userID,
	}).Info("Connection test completed")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Connection test completed",
		"data":    result,
	})
}

// ExecuteCommand 执行远程命令
func (h *HostHandler) ExecuteCommand(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	var req service.ExecuteCommandRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 设置默认超时
	if req.Timeout == 0 {
		req.Timeout = 30
	}

	result, err := h.services.Host.ExecuteCommand(id, &req)
	if err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to execute command",
			})
		}
		return
	}

	userID, _ := middleware.GetUserIDFromContext(c)
	h.logger.WithFields(logrus.Fields{
		"host_id":   id,
		"command":   req.Command,
		"exit_code": result.ExitCode,
		"duration":  result.Duration,
		"user_id":   userID,
	}).Info("Command executed")

	// 记录操作日志
	go h.logOperation(userID, id, "ssh_command", req.Command, result)

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Command executed successfully",
		"data":    result,
	})
}

// GetHostStatus 获取主机状态
func (h *HostHandler) GetHostStatus(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid host ID",
		})
		return
	}

	status, err := h.services.Host.GetHostStatus(id)
	if err != nil {
		if err.Error() == "host not found" {
			c.JSON(http.StatusNotFound, gin.H{
				"code":    404,
				"message": "Host not found",
			})
		} else {
			c.JSON(http.StatusInternalServerError, gin.H{
				"code":    500,
				"message": "Failed to get host status",
			})
		}
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Host status retrieved successfully",
		"data":    status,
	})
}

// logOperation 记录操作日志
func (h *HostHandler) logOperation(userID, hostID int64, operationType, command string, result *service.ExecuteCommandResponse) {
	// TODO: 实现操作日志记录
	// 这里应该调用操作日志服务来记录操作
	h.logger.WithFields(logrus.Fields{
		"user_id":        userID,
		"host_id":        hostID,
		"operation_type": operationType,
		"command":        command,
		"exit_code":      result.ExitCode,
		"duration":       result.Duration,
	}).Info("Operation logged")
}
