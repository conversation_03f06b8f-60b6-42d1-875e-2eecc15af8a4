package monitoring

import (
	"context"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// AIPerformanceMonitor AI性能监控器
type AIPerformanceMonitor struct {
	config  *PerformanceConfig
	logger  *logrus.Logger
	metrics *PerformanceMetrics
	alerts  *AlertManager
	mutex   sync.RWMutex
}

// PerformanceConfig 性能配置
type PerformanceConfig struct {
	Enabled                bool          `json:"enabled"`
	MonitoringInterval     time.Duration `json:"monitoring_interval"`
	ResponseTimeThreshold  time.Duration `json:"response_time_threshold"`
	ErrorRateThreshold     float64       `json:"error_rate_threshold"`
	ThroughputThreshold    float64       `json:"throughput_threshold"`
	AlertCooldown          time.Duration `json:"alert_cooldown"`
	MetricsRetentionPeriod time.Duration `json:"metrics_retention_period"`
}

// PerformanceMetrics AI性能指标
type PerformanceMetrics struct {
	// 响应时间指标
	AvgResponseTime    time.Duration `json:"avg_response_time"`
	P50ResponseTime    time.Duration `json:"p50_response_time"`
	P95ResponseTime    time.Duration `json:"p95_response_time"`
	P99ResponseTime    time.Duration `json:"p99_response_time"`
	MaxResponseTime    time.Duration `json:"max_response_time"`
	MinResponseTime    time.Duration `json:"min_response_time"`

	// 吞吐量指标
	RequestsPerSecond  float64 `json:"requests_per_second"`
	TotalRequests      int64   `json:"total_requests"`
	SuccessfulRequests int64   `json:"successful_requests"`
	FailedRequests     int64   `json:"failed_requests"`

	// 错误率指标
	ErrorRate          float64 `json:"error_rate"`
	TimeoutRate        float64 `json:"timeout_rate"`
	CacheHitRate       float64 `json:"cache_hit_rate"`

	// 资源使用指标
	CPUUsage           float64 `json:"cpu_usage"`
	MemoryUsage        float64 `json:"memory_usage"`
	ActiveConnections  int64   `json:"active_connections"`

	// 时间戳
	LastUpdated        time.Time `json:"last_updated"`
	MonitoringPeriod   time.Duration `json:"monitoring_period"`
}

// AlertManager 告警管理器
type AlertManager struct {
	config     *PerformanceConfig
	logger     *logrus.Logger
	lastAlerts map[string]time.Time
	mutex      sync.RWMutex
}

// PerformanceAlert 性能告警
type PerformanceAlert struct {
	Type        string                 `json:"type"`
	Level       string                 `json:"level"`
	Message     string                 `json:"message"`
	Metrics     map[string]interface{} `json:"metrics"`
	Timestamp   time.Time              `json:"timestamp"`
	Threshold   interface{}            `json:"threshold"`
	ActualValue interface{}            `json:"actual_value"`
}

// ResponseTimeRecord 响应时间记录
type ResponseTimeRecord struct {
	Timestamp    time.Time     `json:"timestamp"`
	ResponseTime time.Duration `json:"response_time"`
	Success      bool          `json:"success"`
	Intent       string        `json:"intent"`
	UserID       int64         `json:"user_id"`
	FromCache    bool          `json:"from_cache"`
}

// NewAIPerformanceMonitor 创建AI性能监控器
func NewAIPerformanceMonitor(config *PerformanceConfig, logger *logrus.Logger) *AIPerformanceMonitor {
	if config == nil {
		config = &PerformanceConfig{
			Enabled:                true,
			MonitoringInterval:     30 * time.Second,
			ResponseTimeThreshold:  10 * time.Second,
			ErrorRateThreshold:     0.05, // 5%
			ThroughputThreshold:    10.0, // 10 RPS
			AlertCooldown:          5 * time.Minute,
			MetricsRetentionPeriod: 24 * time.Hour,
		}
	}

	alertManager := &AlertManager{
		config:     config,
		logger:     logger,
		lastAlerts: make(map[string]time.Time),
	}

	return &AIPerformanceMonitor{
		config: config,
		logger: logger,
		metrics: &PerformanceMetrics{
			LastUpdated: time.Now(),
		},
		alerts: alertManager,
	}
}

// Start 启动性能监控
func (apm *AIPerformanceMonitor) Start(ctx context.Context) error {
	if !apm.config.Enabled {
		apm.logger.Info("AI performance monitoring is disabled")
		return nil
	}

	apm.logger.Info("Starting AI performance monitor")

	go apm.monitoringLoop(ctx)
	return nil
}

// RecordResponseTime 记录响应时间
func (apm *AIPerformanceMonitor) RecordResponseTime(record *ResponseTimeRecord) {
	if !apm.config.Enabled {
		return
	}

	apm.mutex.Lock()
	defer apm.mutex.Unlock()

	// 更新总请求数
	apm.metrics.TotalRequests++

	// 更新成功/失败计数
	if record.Success {
		apm.metrics.SuccessfulRequests++
	} else {
		apm.metrics.FailedRequests++
	}

	// 更新响应时间统计
	apm.updateResponseTimeMetrics(record.ResponseTime)

	// 更新错误率
	apm.updateErrorRate()

	// 更新缓存命中率
	if record.FromCache {
		// 这里需要实际的缓存统计逻辑
	}

	apm.metrics.LastUpdated = time.Now()

	// 检查告警条件
	apm.checkAlerts()
}

// updateResponseTimeMetrics 更新响应时间指标
func (apm *AIPerformanceMonitor) updateResponseTimeMetrics(responseTime time.Duration) {
	// 更新最大/最小响应时间
	if apm.metrics.MaxResponseTime == 0 || responseTime > apm.metrics.MaxResponseTime {
		apm.metrics.MaxResponseTime = responseTime
	}
	if apm.metrics.MinResponseTime == 0 || responseTime < apm.metrics.MinResponseTime {
		apm.metrics.MinResponseTime = responseTime
	}

	// 更新平均响应时间
	if apm.metrics.TotalRequests > 0 {
		totalTime := apm.metrics.AvgResponseTime * time.Duration(apm.metrics.TotalRequests-1)
		apm.metrics.AvgResponseTime = (totalTime + responseTime) / time.Duration(apm.metrics.TotalRequests)
	} else {
		apm.metrics.AvgResponseTime = responseTime
	}

	// 这里应该实现更复杂的百分位数计算
	// 简化版本：使用平均值作为P50，最大值作为P99
	apm.metrics.P50ResponseTime = apm.metrics.AvgResponseTime
	apm.metrics.P95ResponseTime = apm.metrics.AvgResponseTime + (apm.metrics.MaxResponseTime-apm.metrics.AvgResponseTime)/2
	apm.metrics.P99ResponseTime = apm.metrics.MaxResponseTime
}

// updateErrorRate 更新错误率
func (apm *AIPerformanceMonitor) updateErrorRate() {
	if apm.metrics.TotalRequests > 0 {
		apm.metrics.ErrorRate = float64(apm.metrics.FailedRequests) / float64(apm.metrics.TotalRequests)
	}
}

// checkAlerts 检查告警条件
func (apm *AIPerformanceMonitor) checkAlerts() {
	// 检查响应时间告警
	if apm.metrics.AvgResponseTime > apm.config.ResponseTimeThreshold {
		apm.triggerAlert("response_time", "warning", 
			"Average response time exceeded threshold",
			map[string]interface{}{
				"avg_response_time": apm.metrics.AvgResponseTime,
				"threshold":         apm.config.ResponseTimeThreshold,
			})
	}

	// 检查错误率告警
	if apm.metrics.ErrorRate > apm.config.ErrorRateThreshold {
		apm.triggerAlert("error_rate", "critical",
			"Error rate exceeded threshold",
			map[string]interface{}{
				"error_rate": apm.metrics.ErrorRate,
				"threshold":  apm.config.ErrorRateThreshold,
			})
	}

	// 检查吞吐量告警
	if apm.metrics.RequestsPerSecond < apm.config.ThroughputThreshold {
		apm.triggerAlert("throughput", "warning",
			"Throughput below threshold",
			map[string]interface{}{
				"requests_per_second": apm.metrics.RequestsPerSecond,
				"threshold":           apm.config.ThroughputThreshold,
			})
	}
}

// triggerAlert 触发告警
func (apm *AIPerformanceMonitor) triggerAlert(alertType, level, message string, metrics map[string]interface{}) {
	// 检查告警冷却时间
	if lastAlert, exists := apm.alerts.lastAlerts[alertType]; exists {
		if time.Since(lastAlert) < apm.config.AlertCooldown {
			return
		}
	}

	alert := &PerformanceAlert{
		Type:        alertType,
		Level:       level,
		Message:     message,
		Metrics:     metrics,
		Timestamp:   time.Now(),
		Threshold:   apm.getThreshold(alertType),
		ActualValue: apm.getActualValue(alertType),
	}

	apm.logger.WithFields(logrus.Fields{
		"alert_type": alert.Type,
		"level":      alert.Level,
		"message":    alert.Message,
		"metrics":    alert.Metrics,
	}).Warn("AI performance alert triggered")

	// 更新最后告警时间
	apm.alerts.mutex.Lock()
	apm.alerts.lastAlerts[alertType] = time.Now()
	apm.alerts.mutex.Unlock()
}

// getThreshold 获取阈值
func (apm *AIPerformanceMonitor) getThreshold(alertType string) interface{} {
	switch alertType {
	case "response_time":
		return apm.config.ResponseTimeThreshold
	case "error_rate":
		return apm.config.ErrorRateThreshold
	case "throughput":
		return apm.config.ThroughputThreshold
	default:
		return nil
	}
}

// getActualValue 获取实际值
func (apm *AIPerformanceMonitor) getActualValue(alertType string) interface{} {
	switch alertType {
	case "response_time":
		return apm.metrics.AvgResponseTime
	case "error_rate":
		return apm.metrics.ErrorRate
	case "throughput":
		return apm.metrics.RequestsPerSecond
	default:
		return nil
	}
}

// monitoringLoop 监控循环
func (apm *AIPerformanceMonitor) monitoringLoop(ctx context.Context) {
	ticker := time.NewTicker(apm.config.MonitoringInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			apm.logger.Info("AI performance monitoring stopped")
			return
		case <-ticker.C:
			apm.collectMetrics()
		}
	}
}

// collectMetrics 收集指标
func (apm *AIPerformanceMonitor) collectMetrics() {
	apm.mutex.Lock()
	defer apm.mutex.Unlock()

	// 计算吞吐量
	now := time.Now()
	if !apm.metrics.LastUpdated.IsZero() {
		duration := now.Sub(apm.metrics.LastUpdated).Seconds()
		if duration > 0 {
			apm.metrics.RequestsPerSecond = float64(apm.metrics.TotalRequests) / duration
		}
	}

	apm.metrics.MonitoringPeriod = apm.config.MonitoringInterval
	apm.metrics.LastUpdated = now

	apm.logger.WithFields(logrus.Fields{
		"avg_response_time":   apm.metrics.AvgResponseTime,
		"requests_per_second": apm.metrics.RequestsPerSecond,
		"error_rate":          apm.metrics.ErrorRate,
		"total_requests":      apm.metrics.TotalRequests,
	}).Debug("AI performance metrics collected")
}

// GetMetrics 获取性能指标
func (apm *AIPerformanceMonitor) GetMetrics() *PerformanceMetrics {
	apm.mutex.RLock()
	defer apm.mutex.RUnlock()

	metrics := *apm.metrics
	return &metrics
}

// Reset 重置指标
func (apm *AIPerformanceMonitor) Reset() {
	apm.mutex.Lock()
	defer apm.mutex.Unlock()

	apm.metrics = &PerformanceMetrics{
		LastUpdated: time.Now(),
	}

	apm.logger.Info("AI performance metrics reset")
}
