/* AI运维管理平台 - 国际化系统 */
/* 多语言支持和本地化功能 */

// ========================================
// 国际化核心类
// ========================================

class I18n {
    constructor() {
        this.currentLanguage = 'zh-CN';
        this.fallbackLanguage = 'en-US';
        this.translations = {};
        this.dateTimeFormats = {};
        this.numberFormats = {};
        
        this.init();
    }

    async init() {
        // 检测用户语言偏好
        this.detectLanguage();
        
        // 加载翻译文件
        await this.loadTranslations();
        
        // 初始化格式化器
        this.initFormatters();
        
        // 应用翻译
        this.applyTranslations();
    }

    detectLanguage() {
        // 优先级：URL参数 > localStorage > 浏览器语言 > 默认语言
        const urlParams = new URLSearchParams(window.location.search);
        const urlLang = urlParams.get('lang');
        const storedLang = localStorage.getItem('preferred-language');
        const browserLang = navigator.language || navigator.userLanguage;

        if (urlLang && this.isValidLanguage(urlLang)) {
            this.currentLanguage = urlLang;
        } else if (storedLang && this.isValidLanguage(storedLang)) {
            this.currentLanguage = storedLang;
        } else if (this.isValidLanguage(browserLang)) {
            this.currentLanguage = browserLang;
        }

        // 设置HTML lang属性
        document.documentElement.lang = this.currentLanguage;
        
        // 设置文本方向
        this.setTextDirection();
    }

    isValidLanguage(lang) {
        const supportedLanguages = ['zh-CN', 'en-US', 'ja-JP', 'ko-KR'];
        return supportedLanguages.includes(lang);
    }

    setTextDirection() {
        const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        const isRTL = rtlLanguages.some(lang => this.currentLanguage.startsWith(lang));
        document.documentElement.dir = isRTL ? 'rtl' : 'ltr';
    }

    async loadTranslations() {
        try {
            // 加载主要语言文件
            const response = await fetch(`/static/locales/${this.currentLanguage}.json`);
            if (response.ok) {
                this.translations[this.currentLanguage] = await response.json();
            }

            // 加载备用语言文件
            if (this.currentLanguage !== this.fallbackLanguage) {
                const fallbackResponse = await fetch(`/static/locales/${this.fallbackLanguage}.json`);
                if (fallbackResponse.ok) {
                    this.translations[this.fallbackLanguage] = await fallbackResponse.json();
                }
            }
        } catch (error) {
            console.warn('Failed to load translations:', error);
            // 使用内置翻译作为备用
            this.loadBuiltinTranslations();
        }
    }

    loadBuiltinTranslations() {
        this.translations = {
            'zh-CN': {
                'app.title': 'AI运维助手',
                'app.subtitle': '通过自然语言对话管理您的IT基础设施',
                'nav.settings': '设置',
                'nav.shortcuts': '快捷键',
                'nav.history': '对话历史',
                'nav.newChat': '新建对话',
                'chat.welcome.title': '您好！我是您的AI运维助手',
                'chat.welcome.description': '我可以帮您管理服务器、监控系统状态、处理告警等。请告诉我您需要什么帮助？',
                'chat.input.placeholder': '输入您的问题，例如：查看主机状态、检查告警信息...',
                'chat.send': '发送',
                'chat.typing': 'AI正在思考',
                'feature.hostManagement': '主机管理',
                'feature.monitoring': '监控告警',
                'feature.reports': '统计报表',
                'feature.commands': '命令执行',
                'settings.title': '个性化设置',
                'settings.appearance': '外观设置',
                'settings.theme': '主题模式',
                'settings.animations': '动画效果',
                'settings.particles': '粒子效果',
                'time.justNow': '刚刚',
                'time.minutesAgo': '{0}分钟前',
                'time.hoursAgo': '{0}小时前',
                'time.daysAgo': '{0}天前'
            },
            'en-US': {
                'app.title': 'AI Operations Assistant',
                'app.subtitle': 'Manage your IT infrastructure through natural language conversations',
                'nav.settings': 'Settings',
                'nav.shortcuts': 'Shortcuts',
                'nav.history': 'Chat History',
                'nav.newChat': 'New Chat',
                'chat.welcome.title': 'Hello! I am your AI Operations Assistant',
                'chat.welcome.description': 'I can help you manage servers, monitor system status, handle alerts, and more. What can I help you with?',
                'chat.input.placeholder': 'Enter your question, e.g.: check host status, view alerts...',
                'chat.send': 'Send',
                'chat.typing': 'AI is thinking',
                'feature.hostManagement': 'Host Management',
                'feature.monitoring': 'Monitoring & Alerts',
                'feature.reports': 'Reports',
                'feature.commands': 'Command Execution',
                'settings.title': 'Personalization Settings',
                'settings.appearance': 'Appearance',
                'settings.theme': 'Theme Mode',
                'settings.animations': 'Animations',
                'settings.particles': 'Particle Effects',
                'time.justNow': 'Just now',
                'time.minutesAgo': '{0} minutes ago',
                'time.hoursAgo': '{0} hours ago',
                'time.daysAgo': '{0} days ago'
            }
        };
    }

    initFormatters() {
        // 日期时间格式化
        this.dateTimeFormats[this.currentLanguage] = new Intl.DateTimeFormat(this.currentLanguage, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit'
        });

        // 数字格式化
        this.numberFormats[this.currentLanguage] = new Intl.NumberFormat(this.currentLanguage);

        // 相对时间格式化
        if (Intl.RelativeTimeFormat) {
            this.relativeTimeFormat = new Intl.RelativeTimeFormat(this.currentLanguage, {
                numeric: 'auto'
            });
        }
    }

    t(key, params = {}) {
        let translation = this.getTranslation(key);
        
        // 参数替换
        if (typeof params === 'object' && params !== null) {
            Object.keys(params).forEach(param => {
                translation = translation.replace(new RegExp(`{${param}}`, 'g'), params[param]);
            });
        }

        // 数组参数替换（{0}, {1}, etc.）
        if (Array.isArray(params)) {
            params.forEach((param, index) => {
                translation = translation.replace(new RegExp(`{${index}}`, 'g'), param);
            });
        }

        return translation;
    }

    getTranslation(key) {
        const keys = key.split('.');
        let translation = this.translations[this.currentLanguage];

        // 尝试获取当前语言的翻译
        for (const k of keys) {
            if (translation && typeof translation === 'object' && k in translation) {
                translation = translation[k];
            } else {
                translation = null;
                break;
            }
        }

        // 如果当前语言没有翻译，尝试备用语言
        if (!translation && this.currentLanguage !== this.fallbackLanguage) {
            translation = this.translations[this.fallbackLanguage];
            for (const k of keys) {
                if (translation && typeof translation === 'object' && k in translation) {
                    translation = translation[k];
                } else {
                    translation = null;
                    break;
                }
            }
        }

        // 如果都没有找到，返回key本身
        return translation || key;
    }

    formatDate(date, options = {}) {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }

        const formatter = new Intl.DateTimeFormat(this.currentLanguage, {
            year: 'numeric',
            month: 'short',
            day: 'numeric',
            hour: '2-digit',
            minute: '2-digit',
            ...options
        });

        return formatter.format(date);
    }

    formatRelativeTime(date) {
        if (!(date instanceof Date)) {
            date = new Date(date);
        }

        const now = new Date();
        const diffMs = now - date;
        const diffMins = Math.floor(diffMs / 60000);
        const diffHours = Math.floor(diffMs / 3600000);
        const diffDays = Math.floor(diffMs / 86400000);

        if (diffMins < 1) {
            return this.t('time.justNow');
        } else if (diffMins < 60) {
            return this.t('time.minutesAgo', [diffMins]);
        } else if (diffHours < 24) {
            return this.t('time.hoursAgo', [diffHours]);
        } else if (diffDays < 7) {
            return this.t('time.daysAgo', [diffDays]);
        } else {
            return this.formatDate(date);
        }
    }

    formatNumber(number, options = {}) {
        const formatter = new Intl.NumberFormat(this.currentLanguage, options);
        return formatter.format(number);
    }

    async changeLanguage(newLanguage) {
        if (!this.isValidLanguage(newLanguage)) {
            console.warn('Unsupported language:', newLanguage);
            return;
        }

        this.currentLanguage = newLanguage;
        localStorage.setItem('preferred-language', newLanguage);
        
        // 重新加载翻译
        await this.loadTranslations();
        
        // 重新初始化格式化器
        this.initFormatters();
        
        // 更新HTML属性
        document.documentElement.lang = newLanguage;
        this.setTextDirection();
        
        // 重新应用翻译
        this.applyTranslations();
        
        // 触发语言变更事件
        window.dispatchEvent(new CustomEvent('languageChanged', {
            detail: { language: newLanguage }
        }));
    }

    applyTranslations() {
        // 查找所有带有data-i18n属性的元素
        const elements = document.querySelectorAll('[data-i18n]');
        
        elements.forEach(element => {
            const key = element.getAttribute('data-i18n');
            const translation = this.t(key);
            
            // 根据元素类型设置内容
            if (element.tagName === 'INPUT' || element.tagName === 'TEXTAREA') {
                if (element.type === 'submit' || element.type === 'button') {
                    element.value = translation;
                } else {
                    element.placeholder = translation;
                }
            } else {
                element.textContent = translation;
            }
        });

        // 更新页面标题
        const titleKey = document.documentElement.getAttribute('data-title-i18n');
        if (titleKey) {
            document.title = this.t(titleKey);
        }
    }

    getSupportedLanguages() {
        return [
            { code: 'zh-CN', name: '简体中文', flag: '🇨🇳' },
            { code: 'en-US', name: 'English', flag: '🇺🇸' },
            { code: 'ja-JP', name: '日本語', flag: '🇯🇵' },
            { code: 'ko-KR', name: '한국어', flag: '🇰🇷' }
        ];
    }

    getCurrentLanguage() {
        return this.currentLanguage;
    }
}

// ========================================
// 全局实例和辅助函数
// ========================================

// 创建全局i18n实例
window.i18n = new I18n();

// 全局翻译函数
window.t = function(key, params) {
    return window.i18n.t(key, params);
};

// 语言切换函数
window.changeLanguage = function(language) {
    return window.i18n.changeLanguage(language);
};

// 格式化日期函数
window.formatDate = function(date, options) {
    return window.i18n.formatDate(date, options);
};

// 格式化相对时间函数
window.formatRelativeTime = function(date) {
    return window.i18n.formatRelativeTime(date);
};

// 格式化数字函数
window.formatNumber = function(number, options) {
    return window.i18n.formatNumber(number, options);
};

// ========================================
// DOM加载完成后初始化
// ========================================

document.addEventListener('DOMContentLoaded', function() {
    // 等待i18n初始化完成
    window.i18n.init().then(() => {
        console.log('I18n initialized with language:', window.i18n.getCurrentLanguage());
    });
});

// 导出模块（如果使用模块系统）
if (typeof module !== 'undefined' && module.exports) {
    module.exports = I18n;
}
