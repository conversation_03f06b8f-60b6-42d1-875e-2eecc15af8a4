package visualization

import (
	"context"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ThemeManager 主题管理器
type ThemeManager struct {
	logger       *logrus.Logger
	themes       map[string]*Theme
	defaultTheme string
	mutex        sync.RWMutex
}

// Theme 主题
type Theme struct {
	Name        string                 `json:"name"`
	Colors      *ColorPalette          `json:"colors"`
	Fonts       *FontSettings          `json:"fonts"`
	Styles      map[string]interface{} `json:"styles"`
	Description string                 `json:"description"`
	Version     string                 `json:"version"`
}

// ColorPalette 颜色调色板
type ColorPalette struct {
	Primary    []string `json:"primary"`
	Secondary  []string `json:"secondary"`
	Background []string `json:"background"`
	Border     []string `json:"border"`
	Text       []string `json:"text"`
	Success    []string `json:"success"`
	Warning    []string `json:"warning"`
	Error      []string `json:"error"`
	Info       []string `json:"info"`
}

// FontSettings 字体设置
type FontSettings struct {
	Family     string  `json:"family"`
	Size       int     `json:"size"`
	Weight     string  `json:"weight"`
	Style      string  `json:"style"`
	LineHeight float64 `json:"line_height"`
}

// NewThemeManager 创建主题管理器
func NewThemeManager(defaultTheme string, logger *logrus.Logger) *ThemeManager {
	tm := &ThemeManager{
		logger:       logger,
		themes:       make(map[string]*Theme),
		defaultTheme: defaultTheme,
	}

	// 注册默认主题
	tm.registerDefaultThemes()

	return tm
}

// ApplyTheme 应用主题
func (tm *ThemeManager) ApplyTheme(options *ChartOptions, themeName string) {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	theme, exists := tm.themes[themeName]
	if !exists {
		theme = tm.themes[tm.defaultTheme]
	}

	if theme == nil {
		return
	}

	// 应用颜色
	tm.applyColors(options, theme.Colors)

	// 应用字体
	tm.applyFonts(options, theme.Fonts)

	// 应用样式
	tm.applyStyles(options, theme.Styles)
}

// GetTheme 获取主题
func (tm *ThemeManager) GetTheme(name string) *Theme {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	return tm.themes[name]
}

// GetAvailableThemes 获取可用主题
func (tm *ThemeManager) GetAvailableThemes() []string {
	tm.mutex.RLock()
	defer tm.mutex.RUnlock()

	var themes []string
	for name := range tm.themes {
		themes = append(themes, name)
	}

	return themes
}

// RegisterTheme 注册主题
func (tm *ThemeManager) RegisterTheme(theme *Theme) {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	tm.themes[theme.Name] = theme
	tm.logger.WithField("theme", theme.Name).Info("Theme registered")
}

// registerDefaultThemes 注册默认主题
func (tm *ThemeManager) registerDefaultThemes() {
	// 默认主题
	defaultTheme := &Theme{
		Name: "default",
		Colors: &ColorPalette{
			Primary: []string{
				"rgba(54, 162, 235, 1)",
				"rgba(255, 99, 132, 1)",
				"rgba(255, 205, 86, 1)",
				"rgba(75, 192, 192, 1)",
				"rgba(153, 102, 255, 1)",
			},
			Secondary: []string{
				"rgba(54, 162, 235, 0.2)",
				"rgba(255, 99, 132, 0.2)",
				"rgba(255, 205, 86, 0.2)",
				"rgba(75, 192, 192, 0.2)",
				"rgba(153, 102, 255, 0.2)",
			},
			Background: []string{"rgba(255, 255, 255, 1)"},
			Border:     []string{"rgba(200, 200, 200, 1)"},
			Text:       []string{"rgba(0, 0, 0, 1)"},
			Success:    []string{"rgba(75, 192, 192, 1)"},
			Warning:    []string{"rgba(255, 205, 86, 1)"},
			Error:      []string{"rgba(255, 99, 132, 1)"},
			Info:       []string{"rgba(54, 162, 235, 1)"},
		},
		Fonts: &FontSettings{
			Family:     "Arial, sans-serif",
			Size:       12,
			Weight:     "normal",
			Style:      "normal",
			LineHeight: 1.2,
		},
		Description: "默认主题",
		Version:     "1.0",
	}

	// 深色主题
	darkTheme := &Theme{
		Name: "dark",
		Colors: &ColorPalette{
			Primary: []string{
				"rgba(99, 162, 255, 1)",
				"rgba(255, 119, 152, 1)",
				"rgba(255, 225, 106, 1)",
				"rgba(95, 212, 212, 1)",
				"rgba(173, 122, 255, 1)",
			},
			Secondary: []string{
				"rgba(99, 162, 255, 0.3)",
				"rgba(255, 119, 152, 0.3)",
				"rgba(255, 225, 106, 0.3)",
				"rgba(95, 212, 212, 0.3)",
				"rgba(173, 122, 255, 0.3)",
			},
			Background: []string{"rgba(33, 37, 41, 1)"},
			Border:     []string{"rgba(108, 117, 125, 1)"},
			Text:       []string{"rgba(255, 255, 255, 1)"},
			Success:    []string{"rgba(95, 212, 212, 1)"},
			Warning:    []string{"rgba(255, 225, 106, 1)"},
			Error:      []string{"rgba(255, 119, 152, 1)"},
			Info:       []string{"rgba(99, 162, 255, 1)"},
		},
		Fonts: &FontSettings{
			Family:     "Arial, sans-serif",
			Size:       12,
			Weight:     "normal",
			Style:      "normal",
			LineHeight: 1.2,
		},
		Description: "深色主题",
		Version:     "1.0",
	}

	tm.themes["default"] = defaultTheme
	tm.themes["dark"] = darkTheme
}

// applyColors 应用颜色
func (tm *ThemeManager) applyColors(options *ChartOptions, colors *ColorPalette) {
	// 简化实现：设置基本颜色
	if options.Plugins == nil {
		options.Plugins = make(map[string]interface{})
	}

	options.Plugins["colors"] = colors
}

// applyFonts 应用字体
func (tm *ThemeManager) applyFonts(options *ChartOptions, fonts *FontSettings) {
	// 简化实现：设置字体
	if options.Plugins == nil {
		options.Plugins = make(map[string]interface{})
	}

	options.Plugins["fonts"] = fonts
}

// applyStyles 应用样式
func (tm *ThemeManager) applyStyles(options *ChartOptions, styles map[string]interface{}) {
	// 简化实现：合并样式
	if options.Plugins == nil {
		options.Plugins = make(map[string]interface{})
	}

	for key, value := range styles {
		options.Plugins[key] = value
	}
}

// ChartCacheManager 图表缓存管理器
type ChartCacheManager struct {
	logger     *logrus.Logger
	cache      map[string]*CachedChart
	expiration time.Duration
	maxSize    int
	mutex      sync.RWMutex
	stats      *ChartCacheStats
}

// CachedChart 缓存的图表
type CachedChart struct {
	Key         string         `json:"key"`
	Chart       *RenderedChart `json:"chart"`
	CreatedAt   time.Time      `json:"created_at"`
	ExpiresAt   time.Time      `json:"expires_at"`
	AccessCount int64          `json:"access_count"`
	LastAccess  time.Time      `json:"last_access"`
}

// NewChartCacheManager 创建图表缓存管理器
func NewChartCacheManager(expiration time.Duration, maxSize int, logger *logrus.Logger) *ChartCacheManager {
	return &ChartCacheManager{
		logger:     logger,
		cache:      make(map[string]*CachedChart),
		expiration: expiration,
		maxSize:    maxSize,
		stats: &ChartCacheStats{
			MaxSize: maxSize,
		},
	}
}

// Start 启动缓存管理器
func (ccm *ChartCacheManager) Start(ctx context.Context) error {
	ccm.logger.Info("Starting chart cache manager")

	// 启动清理协程
	go ccm.cleanupRoutine(ctx)

	return nil
}

// Stop 停止缓存管理器
func (ccm *ChartCacheManager) Stop(ctx context.Context) error {
	ccm.logger.Info("Stopping chart cache manager")
	return nil
}

// Get 获取缓存的图表
func (ccm *ChartCacheManager) Get(key string) *RenderedChart {
	ccm.mutex.Lock()
	defer ccm.mutex.Unlock()

	cached, exists := ccm.cache[key]
	if !exists {
		ccm.stats.MissCount++
		ccm.updateHitRate()
		return nil
	}

	// 检查是否过期
	if time.Now().After(cached.ExpiresAt) {
		delete(ccm.cache, key)
		ccm.stats.MissCount++
		ccm.updateHitRate()
		return nil
	}

	// 更新访问信息
	cached.AccessCount++
	cached.LastAccess = time.Now()

	ccm.stats.HitCount++
	ccm.updateHitRate()

	return cached.Chart
}

// Set 设置缓存的图表
func (ccm *ChartCacheManager) Set(key string, chart *RenderedChart) {
	ccm.mutex.Lock()
	defer ccm.mutex.Unlock()

	// 检查缓存大小限制
	if len(ccm.cache) >= ccm.maxSize {
		ccm.evictOldest()
	}

	cached := &CachedChart{
		Key:         key,
		Chart:       chart,
		CreatedAt:   time.Now(),
		ExpiresAt:   time.Now().Add(ccm.expiration),
		AccessCount: 0,
		LastAccess:  time.Now(),
	}

	ccm.cache[key] = cached
	ccm.stats.CacheSize = len(ccm.cache)
}

// GetStats 获取缓存统计
func (ccm *ChartCacheManager) GetStats() *ChartCacheStats {
	ccm.mutex.RLock()
	defer ccm.mutex.RUnlock()

	ccm.stats.CacheSize = len(ccm.cache)
	return ccm.stats
}

// cleanupRoutine 清理协程
func (ccm *ChartCacheManager) cleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			ccm.cleanup()
		}
	}
}

// cleanup 清理过期缓存
func (ccm *ChartCacheManager) cleanup() {
	ccm.mutex.Lock()
	defer ccm.mutex.Unlock()

	now := time.Now()
	var removedCount int

	for key, cached := range ccm.cache {
		if now.After(cached.ExpiresAt) {
			delete(ccm.cache, key)
			removedCount++
		}
	}

	ccm.stats.CacheSize = len(ccm.cache)

	if removedCount > 0 {
		ccm.logger.WithField("removed_count", removedCount).Info("Cache cleanup completed")
	}
}

// evictOldest 驱逐最旧的缓存
func (ccm *ChartCacheManager) evictOldest() {
	var oldestKey string
	var oldestTime time.Time

	for key, cached := range ccm.cache {
		if oldestTime.IsZero() || cached.LastAccess.Before(oldestTime) {
			oldestKey = key
			oldestTime = cached.LastAccess
		}
	}

	if oldestKey != "" {
		delete(ccm.cache, oldestKey)
	}
}

// updateHitRate 更新命中率
func (ccm *ChartCacheManager) updateHitRate() {
	total := ccm.stats.HitCount + ccm.stats.MissCount
	if total > 0 {
		ccm.stats.HitRate = float64(ccm.stats.HitCount) / float64(total)
	}
}

// ChartExportManager 图表导出管理器
type ChartExportManager struct {
	logger           *logrus.Logger
	supportedFormats []string
	exporters        map[string]ChartExporter
	stats            *ChartExportStats
	mutex            sync.RWMutex
}

// ChartExporter 图表导出器接口
type ChartExporter interface {
	Export(chart *RenderedChart) ([]byte, error)
	GetFormat() string
	GetMimeType() string
}

// NewChartExportManager 创建图表导出管理器
func NewChartExportManager(supportedFormats []string, logger *logrus.Logger) *ChartExportManager {
	return &ChartExportManager{
		logger:           logger,
		supportedFormats: supportedFormats,
		exporters:        make(map[string]ChartExporter),
		stats: &ChartExportStats{
			ExportsByFormat: make(map[string]int64),
		},
	}
}

// Start 启动导出管理器
func (cem *ChartExportManager) Start(ctx context.Context) error {
	cem.logger.Info("Starting chart export manager")
	return nil
}

// Stop 停止导出管理器
func (cem *ChartExportManager) Stop(ctx context.Context) error {
	cem.logger.Info("Stopping chart export manager")
	return nil
}

// Export 导出图表
func (cem *ChartExportManager) Export(chartID string, format string) ([]byte, error) {
	cem.mutex.Lock()
	defer cem.mutex.Unlock()

	// 简化实现：返回模拟导出数据
	data := []byte(`{"chart_id":"` + chartID + `","format":"` + format + `","exported":true}`)

	// 更新统计
	cem.stats.TotalExports++
	cem.stats.ExportsByFormat[format]++
	cem.stats.LastExportTime = time.Now()

	return data, nil
}

// GetStats 获取导出统计
func (cem *ChartExportManager) GetStats() *ChartExportStats {
	cem.mutex.RLock()
	defer cem.mutex.RUnlock()

	return cem.stats
}
