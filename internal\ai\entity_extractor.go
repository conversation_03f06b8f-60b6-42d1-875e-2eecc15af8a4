package ai

import (
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
)

// 实体类型常量
const (
	EntityIPAddress   = "ip_address"
	EntityHostname    = "hostname"
	EntityPort        = "port"
	EntityServiceName = "service_name"
	EntityFilePath    = "file_path"
	EntityUserName    = "username"
	EntityProcessName = "process_name"
	EntityCommand     = "command"
	EntityTimeRange   = "time_range"
	EntityLogLevel    = "log_level"
)

// EntityExtractor 实体提取器
type EntityExtractor struct {
	logger   *logrus.Logger
	patterns map[string]*regexp.Regexp
}

// NewEntityExtractor 创建实体提取器
func NewEntityExtractor(logger *logrus.Logger) *EntityExtractor {
	extractor := &EntityExtractor{
		logger:   logger,
		patterns: make(map[string]*regexp.Regexp),
	}

	// 初始化正则表达式模式
	extractor.initializePatterns()

	return extractor
}

// initializePatterns 初始化实体识别模式
func (ee *EntityExtractor) initializePatterns() {
	// IP地址模式
	ee.patterns[EntityIPAddress] = regexp.MustCompile(`\b(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\b`)

	// 主机名模式
	ee.patterns[EntityHostname] = regexp.MustCompile(`\b[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*\b`)

	// 端口模式
	ee.patterns[EntityPort] = regexp.MustCompile(`\b(?:端口|port)\s*[:：]?\s*([1-9][0-9]{0,4})\b`)

	// 服务名模式
	ee.patterns[EntityServiceName] = regexp.MustCompile(`\b(nginx|apache|mysql|redis|docker|ssh|ftp|http|https|postgresql|mongodb|elasticsearch)\b`)

	// 文件路径模式
	ee.patterns[EntityFilePath] = regexp.MustCompile(`(?:/[^/\s]+)+/?|(?:[a-zA-Z]:\\(?:[^\\/:*?"<>|\s]+\\)*[^\\/:*?"<>|\s]*)|(?:\.{1,2}/[^/\s]*)+`)

	// 用户名模式
	ee.patterns[EntityUserName] = regexp.MustCompile(`\b(?:用户名?|user|username)\s*[:：]?\s*([a-zA-Z][a-zA-Z0-9_-]{0,31})\b`)

	// 进程名模式
	ee.patterns[EntityProcessName] = regexp.MustCompile(`\b(java|python|node|nginx|apache|mysql|redis|docker|systemd|ssh|cron)\b`)

	// 时间范围模式
	ee.patterns[EntityTimeRange] = regexp.MustCompile(`\b(?:最近|过去|前)\s*(\d+)\s*(分钟|小时|天|周|月)\b`)

	// 日志级别模式
	ee.patterns["log_level"] = regexp.MustCompile(`\b(DEBUG|INFO|WARN|WARNING|ERROR|FATAL|TRACE)\b`)
}

// ExtractEntities 提取实体
func (ee *EntityExtractor) ExtractEntities(message string) map[string]interface{} {
	entities := make(map[string]interface{})

	// 提取IP地址
	if ips := ee.extractIPAddresses(message); len(ips) > 0 {
		if len(ips) == 1 {
			entities[EntityIPAddress] = ips[0]
		} else {
			entities[EntityIPAddress] = ips
		}
	}

	// 提取端口
	if port := ee.extractPort(message); port != "" {
		entities[EntityPort] = port
	}

	// 提取服务名
	if services := ee.extractServices(message); len(services) > 0 {
		if len(services) == 1 {
			entities[EntityServiceName] = services[0]
		} else {
			entities[EntityServiceName] = services
		}
	}

	// 提取文件路径
	if paths := ee.extractFilePaths(message); len(paths) > 0 {
		if len(paths) == 1 {
			entities[EntityFilePath] = paths[0]
		} else {
			entities[EntityFilePath] = paths
		}
	}

	// 提取用户名
	if username := ee.extractUsername(message); username != "" {
		entities[EntityUserName] = username
	}

	// 提取进程名
	if processes := ee.extractProcesses(message); len(processes) > 0 {
		if len(processes) == 1 {
			entities[EntityProcessName] = processes[0]
		} else {
			entities[EntityProcessName] = processes
		}
	}

	// 提取时间范围
	if timeRange := ee.extractTimeRange(message); timeRange != "" {
		entities[EntityTimeRange] = timeRange
	}

	// 提取日志级别
	if logLevel := ee.extractLogLevel(message); logLevel != "" {
		entities[EntityLogLevel] = logLevel
	}

	// 提取动作类型
	if action := ee.extractAction(message); action != "" {
		entities["action"] = action
	}

	// 提取命令
	if command := ee.extractCommand(message); command != "" {
		entities[EntityCommand] = command
	}

	ee.logger.WithFields(logrus.Fields{
		"message":        message,
		"entities_count": len(entities),
		"entities":       entities,
	}).Debug("Entity extraction completed")

	return entities
}

// extractIPAddresses 提取IP地址
func (ee *EntityExtractor) extractIPAddresses(message string) []string {
	matches := ee.patterns[EntityIPAddress].FindAllString(message, -1)
	return ee.removeDuplicates(matches)
}

// extractPort 提取端口
func (ee *EntityExtractor) extractPort(message string) string {
	matches := ee.patterns[EntityPort].FindStringSubmatch(message)
	if len(matches) > 1 {
		return matches[1]
	}
	return ""
}

// extractServices 提取服务名
func (ee *EntityExtractor) extractServices(message string) []string {
	matches := ee.patterns[EntityServiceName].FindAllString(strings.ToLower(message), -1)
	return ee.removeDuplicates(matches)
}

// extractFilePaths 提取文件路径
func (ee *EntityExtractor) extractFilePaths(message string) []string {
	matches := ee.patterns[EntityFilePath].FindAllString(message, -1)
	// 过滤掉明显不是路径的匹配
	var validPaths []string
	for _, match := range matches {
		if ee.isValidPath(match) {
			validPaths = append(validPaths, match)
		}
	}
	return ee.removeDuplicates(validPaths)
}

// extractUsername 提取用户名
func (ee *EntityExtractor) extractUsername(message string) string {
	matches := ee.patterns[EntityUserName].FindStringSubmatch(message)
	if len(matches) > 1 {
		return matches[1]
	}

	// 尝试从常见格式中提取用户名
	// 格式：IP 用户名 密码
	parts := strings.Fields(message)
	if len(parts) >= 3 {
		// 检查第一个是否为IP，第二个可能是用户名
		if ee.patterns[EntityIPAddress].MatchString(parts[0]) {
			if ee.isValidUsername(parts[1]) {
				return parts[1]
			}
		}
	}

	return ""
}

// extractProcesses 提取进程名
func (ee *EntityExtractor) extractProcesses(message string) []string {
	matches := ee.patterns[EntityProcessName].FindAllString(strings.ToLower(message), -1)
	return ee.removeDuplicates(matches)
}

// extractTimeRange 提取时间范围
func (ee *EntityExtractor) extractTimeRange(message string) string {
	matches := ee.patterns[EntityTimeRange].FindStringSubmatch(message)
	if len(matches) > 0 {
		return matches[0]
	}
	return ""
}

// extractLogLevel 提取日志级别
func (ee *EntityExtractor) extractLogLevel(message string) string {
	matches := ee.patterns["log_level"].FindString(strings.ToUpper(message))
	return matches
}

// extractAction 提取动作类型
func (ee *EntityExtractor) extractAction(message string) string {
	message = strings.ToLower(message)

	// 连接相关动作
	if strings.Contains(message, "检查") && (strings.Contains(message, "连接") || strings.Contains(message, "登录")) {
		if strings.Contains(message, "登录") {
			return "check_login"
		}
		return "check_connection"
	}

	// 命令执行相关动作
	if strings.Contains(message, "执行") && strings.Contains(message, "命令") {
		return "execute_command"
	}

	// 测试相关动作
	if strings.Contains(message, "测试") {
		return "test"
	}

	// 查看相关动作
	if strings.Contains(message, "查看") || strings.Contains(message, "显示") {
		return "view"
	}

	// 监控相关动作
	if strings.Contains(message, "监控") {
		return "monitor"
	}

	return ""
}

// extractCommand 提取命令
func (ee *EntityExtractor) extractCommand(message string) string {
	// 查找常见的命令模式
	commandPatterns := []string{
		`执行\s*[:：]?\s*(.+)`,
		`运行\s*[:：]?\s*(.+)`,
		`命令\s*[:：]?\s*(.+)`,
	}

	for _, pattern := range commandPatterns {
		re := regexp.MustCompile(pattern)
		matches := re.FindStringSubmatch(message)
		if len(matches) > 1 {
			return strings.TrimSpace(matches[1])
		}
	}

	return ""
}

// isValidPath 验证是否为有效路径
func (ee *EntityExtractor) isValidPath(path string) bool {
	// 排除过短的匹配
	if len(path) < 3 {
		return false
	}

	// 排除明显不是路径的模式
	invalidPatterns := []string{
		`^\d+$`,              // 纯数字
		`^[a-zA-Z]$`,         // 单个字母
		`^\.\.$`,             // 仅..
		`^[^/\\]*\.[^/\\]*$`, // 简单的文件名.扩展名
	}

	for _, pattern := range invalidPatterns {
		if matched, _ := regexp.MatchString(pattern, path); matched {
			return false
		}
	}

	return true
}

// isValidUsername 验证是否为有效用户名
func (ee *EntityExtractor) isValidUsername(username string) bool {
	// 用户名长度检查
	if len(username) < 1 || len(username) > 32 {
		return false
	}

	// 用户名格式检查
	validUsername := regexp.MustCompile(`^[a-zA-Z][a-zA-Z0-9_-]*$`)
	return validUsername.MatchString(username)
}

// removeDuplicates 去除重复项
func (ee *EntityExtractor) removeDuplicates(items []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range items {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}
