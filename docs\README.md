# AI对话运维管理平台技术方案

## 📋 项目概述

基于Go语言和DeepSeek API开发的智能运维管理平台，通过自然语言对话实现运维操作自动化。

### 🎯 核心功能
- **AI智能对话**：基于DeepSeek API的自然语言交互
- **主机管理**：SSH连接、命令执行、状态监控
- **监控告警**：实时告警、状态展示、通知推送
- **统计报表**：数据可视化、趋势分析、报告生成
- **用户管理**：RBAC权限、用户认证、会话管理

### 🏗️ 技术架构
- **架构模式**：轻量级单体架构
- **后端框架**：Go + Gin + GORM + SQLite
- **前端技术**：HTML模板 + Bootstrap + WebSocket
- **AI集成**：DeepSeek API + Function Calling
- **部署方案**：Docker容器化 + 高可用部署

## 📚 文档结构

```
docs/
├── README.md                    # 项目概述（本文件）
├── architecture/                # 系统架构设计
│   ├── system-architecture.md   # 系统架构图和说明
│   ├── database-design.md       # 数据库设计方案
│   └── api-design.md           # API接口设计
├── security/                    # 安全方案设计
│   ├── security-architecture.md # 安全架构设计
│   ├── encryption-strategy.md   # 加密策略实现
│   └── audit-logging.md        # 审计日志方案
├── ai-integration/              # AI集成方案
│   ├── deepseek-integration.md  # DeepSeek API集成
│   ├── context-management.md    # 上下文管理策略
│   └── function-calling.md     # 工具调用设计
├── operations/                  # 运维监控方案
│   ├── monitoring.md           # 监控体系设计
│   ├── logging.md              # 日志收集分析
│   └── health-checks.md        # 健康检查机制
├── deployment/                  # 部署运维方案
│   ├── high-availability.md    # 高可用部署
│   ├── backup-recovery.md      # 备份恢复策略
│   └── canary-deployment.md    # 灰度发布机制
├── development/                 # 开发指南
│   ├── development-plan.md     # 开发计划和里程碑
│   ├── coding-standards.md     # 编码规范
│   └── testing-strategy.md     # 测试策略
└── implementation/              # 实现细节
    ├── performance-optimization.md # 性能优化策略
    ├── error-handling.md          # 错误处理机制
    └── user-experience.md         # 用户体验优化
```

## 🚀 快速开始

### 环境要求
- Go 1.21+
- Docker & Docker Compose
- SQLite 3.x
- Node.js 18+ (开发工具)

### 开发环境搭建
```bash
# 1. 克隆项目
git clone <repository-url>
cd aiops-platform

# 2. 安装依赖
go mod download

# 3. 配置环境变量
cp .env.example .env
# 编辑 .env 文件，配置 DeepSeek API Key 等

# 4. 初始化数据库
go run cmd/migrate/main.go

# 5. 启动开发服务器
go run cmd/server/main.go
```

### Docker部署
```bash
# 1. 构建镜像
docker-compose build

# 2. 启动服务
docker-compose up -d

# 3. 查看服务状态
docker-compose ps
```

## 📊 技术栈

### 后端技术
- **Go 1.21+** - 主要开发语言
- **Gin** - Web框架
- **GORM** - ORM框架
- **SQLite** - 数据库
- **Casbin** - 权限管理
- **JWT** - 认证授权
- **WebSocket** - 实时通信

### 安全技术
- **AES-256-GCM** - 数据加密
- **bcrypt** - 密码哈希
- **TLS 1.3** - 传输加密
- **PBKDF2** - 密钥派生

### 运维技术
- **Docker** - 容器化
- **Prometheus** - 监控指标
- **Grafana** - 数据可视化
- **Nginx** - 负载均衡
- **Redis** - 缓存

## 🔗 相关链接

- [系统架构设计](./architecture/system-architecture.md)
- [数据库设计方案](./architecture/database-design.md)
- [安全架构设计](./security/security-architecture.md)
- [DeepSeek API集成](./ai-integration/deepseek-integration.md)
- [开发计划](./development/development-plan.md)
- [部署指南](./deployment/high-availability.md)

## 📞 联系方式

如有问题或建议，请联系开发团队。

---

**版本**: v1.0.0  
**最后更新**: 2024-01-15  
**维护者**: AI运维平台开发团队
