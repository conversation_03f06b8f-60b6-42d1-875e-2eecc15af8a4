{"level":"fatal","msg":"Failed to start server: listen tcp :8084: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-08-06 08:11:16"}
{"body_size":40,"client_ip":"::1","latency":0,"level":"warning","method":"GET","msg":"HTTP request completed with client error","path":"/.well-known/appspecific/com.chrome.devtools.json","status":404,"time":"2025-08-06 09:13:11","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"body_size":40,"client_ip":"::1","latency":996100,"level":"warning","method":"GET","msg":"HTTP request completed with client error","path":"/.well-known/appspecific/com.chrome.devtools.json","status":404,"time":"2025-08-06 09:27:04","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"body_size":40,"client_ip":"::1","latency":0,"level":"warning","method":"GET","msg":"HTTP request completed with client error","path":"/.well-known/appspecific/com.chrome.devtools.json","status":404,"time":"2025-08-06 09:50:55","user_agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"}
{"level":"fatal","msg":"Failed to start server: listen tcp :8084: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.","time":"2025-08-06 10:33:24"}
