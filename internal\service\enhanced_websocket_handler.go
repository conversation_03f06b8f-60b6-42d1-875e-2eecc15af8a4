package service

import (
	"context"
	"encoding/json"
	"fmt"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedWebSocketHandler 增强型WebSocket处理器
type EnhancedWebSocketHandler struct {
	db                    *gorm.DB
	logger                *logrus.Logger
	enhancedDeepSeek      *EnhancedDeepSeekService
	resultCollector       *ExecutionResultCollector
	resultAnalyzer        *AIResultAnalyzer
	resultRenderer        *AIResultRenderer
	executionEngine       *UnifiedExecutionEngine
	sqlGenerator          *SQLGenerator
	hostService           HostService

	// 🔧 新增：Context管理用于支持停止操作
	activeContexts        map[string]context.CancelFunc
	contextMutex          sync.RWMutex
}

// NewEnhancedWebSocketHandler 创建增强型WebSocket处理器
func NewEnhancedWebSocketHandler(
	db *gorm.DB,
	logger *logrus.Logger,
	enhancedDeepSeek *EnhancedDeepSeekService,
	executionEngine *UnifiedExecutionEngine,
	hostService HostService,
) *EnhancedWebSocketHandler {
	resultCollector := NewExecutionResultCollector(db, logger)
	resultAnalyzer := NewAIResultAnalyzer(enhancedDeepSeek, logger)
	sqlGenerator := NewSQLGenerator(db, logger)
	resultRenderer := NewAIResultRenderer(enhancedDeepSeek, logger)
	
	return &EnhancedWebSocketHandler{
		db:                    db,
		logger:                logger,
		enhancedDeepSeek:      enhancedDeepSeek,
		resultCollector:       resultCollector,
		resultAnalyzer:        resultAnalyzer,
		resultRenderer:        resultRenderer,
		executionEngine:       executionEngine,
		sqlGenerator:          sqlGenerator,
		hostService:           hostService,
		activeContexts:        make(map[string]context.CancelFunc),
	}
}

// 注意：ProcessMessageRequest 和 ProcessMessageResponse 已在 ai_types.go 中定义

// ProcessMessage 处理消息 - 完整的5步骤AI驱动运维流程
func (ewh *EnhancedWebSocketHandler) ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	start := time.Now()

	// 🔧 新增：创建可取消的Context并注册
	sessionKey := fmt.Sprintf("%s_%d", req.SessionID, req.UserID)
	cancelableCtx, cancel := context.WithCancel(ctx)

	ewh.contextMutex.Lock()
	ewh.activeContexts[sessionKey] = cancel
	ewh.contextMutex.Unlock()

	// 确保在函数结束时清理Context
	defer func() {
		ewh.contextMutex.Lock()
		delete(ewh.activeContexts, sessionKey)
		ewh.contextMutex.Unlock()
		cancel()
	}()

	ewh.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
		"session_key": sessionKey,
	}).Info("🚀 EnhancedWebSocketHandler: 开始完整AI驱动运维流程")
	
	// 步骤1: DeepSeek意图识别
	ewh.logger.Info("📋 步骤1: DeepSeek意图识别")

	// 🔧 检查Context是否被取消
	select {
	case <-cancelableCtx.Done():
		ewh.logger.Info("🛑 步骤1被用户取消")
		return ewh.createCancelledResponse("步骤1: 意图识别被用户取消", start), nil
	default:
	}

	// 移除具体业务逻辑的快速处理，让DeepSeek完全决策

	intentResponse, err := ewh.enhancedDeepSeek.RecognizeIntent(cancelableCtx, req.Message)
	if err != nil {
		ewh.logger.WithError(err).Error("步骤1失败: 意图识别失败")
		return ewh.createErrorResponse("意图识别失败", err, start), nil
	}

	// 解析意图结果
	intent, err := ewh.parseIntentResponse(intentResponse.Choices[0].Message.Content)
	if err != nil {
		ewh.logger.WithError(err).Error("步骤1失败: 意图解析失败")
		return ewh.createErrorResponse("意图解析失败", err, start), nil
	}
	
	ewh.logger.WithFields(logrus.Fields{
		"intent_type": intent["intent_type"],
		"confidence":  intent["confidence"],
	}).Info("✅ 步骤1完成: 意图识别成功")
	
	// 步骤2: 智能SQL生成（完美方案）
	ewh.logger.Info("🧠 步骤2: 智能SQL生成")

	// 🔧 检查Context是否被取消
	select {
	case <-cancelableCtx.Done():
		ewh.logger.Info("🛑 步骤2被用户取消")
		return ewh.createCancelledResponse("步骤2: 智能SQL生成被用户取消", start), nil
	default:
	}

	var commandInfo map[string]interface{}
	var commandResponse *DeepSeekResponse

	// 检查是否为数据库操作
	if intentType, ok := intent["intent_type"].(string); ok && intentType == "database_operations" {
		// 🔧 特殊处理：密码修改操作使用API而不是直接SQL
		if ewh.isPasswordUpdateOperation(req.Message) {
			ewh.logger.Info("🔐 检测到密码修改操作，使用安全API")

			// 提取IP地址和新密码
			ipAddress, newPassword, err := ewh.extractPasswordUpdateInfo(req.Message)
			if err != nil {
				ewh.logger.WithError(err).Error("步骤2失败: 密码更新信息提取失败")
				return ewh.createErrorResponse("密码更新信息提取失败", err, start), nil
			}

			// 构建API调用命令信息
			commandInfo = map[string]interface{}{
				"execution_type": "password_update",
				"generated_code": fmt.Sprintf("update_host_password:%s:%s", ipAddress, newPassword),
				"description":    "安全的密码更新API调用",
				"estimated_risk": "medium",
				"require_confirm": false,
				"ip_address":     ipAddress,
				"new_password":   newPassword,
			}

			ewh.logger.WithFields(logrus.Fields{
				"ip_address": ipAddress,
				"operation":  "password_update",
			}).Info("✅ 步骤2完成: 密码更新API调用准备完成")

		} else {
			// 普通数据库操作，使用智能SQL生成器
			sqlReq := &IntelligentSQLGenerationRequest{
				UserInput:  req.Message,
				Intent:     intent,
				TableName:  "hosts", // 根据意图动态确定表名
				Operation:  ewh.determineOperation(req.Message),
				MaxRetries: 3,
			}

			sqlResult, err := ewh.sqlGenerator.GenerateIntelligentSQL(ctx, sqlReq, ewh.enhancedDeepSeek)
			if err != nil {
				ewh.logger.WithError(err).Error("步骤2失败: 智能SQL生成失败")
				return ewh.createErrorResponse("智能SQL生成失败", err, start), nil
			}

			if !sqlResult.IsValid {
				ewh.logger.WithField("error", sqlResult.ValidationError).Error("步骤2失败: SQL验证失败")
				return ewh.createErrorResponse("SQL验证失败", fmt.Errorf(sqlResult.ValidationError), start), nil
			}

			// 构建命令信息
			commandInfo = map[string]interface{}{
				"execution_type": "sql",
				"generated_code": sqlResult.SQL,
				"description":    fmt.Sprintf("智能生成的SQL语句 (置信度: %.2f)", sqlResult.Confidence),
				"estimated_risk": "low",
				"require_confirm": false,
				"confidence":     sqlResult.Confidence,
				"attempts":       sqlResult.Attempts,
				"used_fields":    sqlResult.UsedFields,
			}

			ewh.logger.WithFields(logrus.Fields{
				"sql":        sqlResult.SQL,
				"confidence": sqlResult.Confidence,
				"attempts":   sqlResult.Attempts,
			}).Info("✅ 步骤2完成: 智能SQL生成成功")
		}

	} else {
		// 非数据库操作，使用原有逻辑
		dbSchema := ewh.getDatabaseSchema(ctx)
		commandResponse, err = ewh.enhancedDeepSeek.GenerateExecutionCommandWithSchema(ctx, intent, req.Message, dbSchema)
		if err != nil {
			ewh.logger.WithError(err).Error("步骤2失败: 命令生成失败")
			return ewh.createErrorResponse("命令生成失败", err, start), nil
		}

		// 解析命令生成结果
		commandInfo, err = ewh.parseCommandResponse(commandResponse.Choices[0].Message.Content)
		if err != nil {
			ewh.logger.WithError(err).Error("步骤2失败: 命令解析失败")
			return ewh.createErrorResponse("命令解析失败", err, start), nil
		}

		ewh.logger.WithFields(logrus.Fields{
			"execution_type": commandInfo["execution_type"],
			"generated_code": commandInfo["generated_code"],
		}).Info("✅ 步骤2完成: 传统命令生成成功")
	}
	
	ewh.logger.WithFields(logrus.Fields{
		"execution_type": commandInfo["execution_type"],
		"generated_code": commandInfo["generated_code"],
	}).Info("✅ 步骤2完成: 命令生成成功")
	
	// 步骤3: 后端执行命令
	ewh.logger.Info("⚡ 步骤3: 后端执行命令")

	// 🔧 检查Context是否被取消
	select {
	case <-cancelableCtx.Done():
		ewh.logger.Info("🛑 步骤3被用户取消")
		return ewh.createCancelledResponse("步骤3: 后端执行命令被用户取消", start), nil
	default:
	}

	executionResult, err := ewh.executeCommand(cancelableCtx, commandInfo, intent, req)
	if err != nil {
		ewh.logger.WithError(err).Error("步骤3失败: 命令执行失败")
		return ewh.createErrorResponse("命令执行失败", err, start), nil
	}
	
	ewh.logger.WithFields(logrus.Fields{
		"success":        executionResult.Success,
		"execution_time": executionResult.ExecutionTime,
	}).Info("✅ 步骤3完成: 命令执行成功")

	// 删除快速路径，让所有请求都通过完整的AI分析和渲染流程

	// 步骤4: 执行结果回传DeepSeek进行分析
	ewh.logger.Info("🔍 步骤4: AI结果分析")

	// 🔧 检查Context是否被取消
	select {
	case <-cancelableCtx.Done():
		ewh.logger.Info("🛑 步骤4被用户取消")
		return ewh.createCancelledResponse("步骤4: AI结果分析被用户取消", start), nil
	default:
	}

	analysisResult, err := ewh.analyzeExecutionResult(cancelableCtx, executionResult, req)
	if err != nil {
		ewh.logger.WithError(err).Warn("步骤4警告: AI分析失败，继续使用基础结果")
		// 分析失败不影响整体流程，继续进行
	}
	
	if analysisResult != nil {
		ewh.logger.WithFields(logrus.Fields{
			"key_findings":    len(analysisResult.KeyFindings),
			"recommendations": len(analysisResult.Recommendations),
		}).Info("✅ 步骤4完成: AI分析成功")
	}
	
	// 步骤5: AI智能渲染结果 - 临时注释掉以减少等待时间
	// ewh.logger.Info("🎨 步骤5: AI智能渲染")
	// renderResult, err := ewh.renderFinalResult(ctx, executionResult, analysisResult, req)
	// if err != nil {
	// 	ewh.logger.WithError(err).Warn("步骤5警告: AI渲染失败，使用降级渲染")
	// 	// 渲染失败使用基础内容
	// 	renderResult = &RenderResult{
	// 		Success: true,
	// 		Content: executionResult.Content,
	// 	}
	// }

	// ewh.logger.WithFields(logrus.Fields{
	// 	"render_success":  renderResult.Success,
	// 	"content_length":  len(renderResult.Content),
	// 	"total_time":      time.Since(start),
	// }).Info("✅ 步骤5完成: AI渲染成功")

	ewh.logger.Info("🎨 步骤5: 跳过AI智能渲染，直接返回结果")

	// 构建基础格式化内容
	var finalContent strings.Builder
	finalContent.WriteString("## 🎯 操作摘要\n\n")
	finalContent.WriteString(fmt.Sprintf("**操作**: %s\n", req.Message))
	finalContent.WriteString(fmt.Sprintf("**状态**: %s\n", ewh.getStatusEmoji(executionResult.Success)))
	finalContent.WriteString(fmt.Sprintf("**执行时间**: %v\n\n", executionResult.ExecutionTime))

	finalContent.WriteString("## 📊 执行结果\n\n")
	if executionResult.Content != "" {
		finalContent.WriteString(executionResult.Content)
		finalContent.WriteString("\n\n")
	}

	// 如果有AI分析结果，添加分析信息
	if analysisResult != nil {
		finalContent.WriteString("## 🔍 AI分析结果\n\n")
		finalContent.WriteString(fmt.Sprintf("**摘要**: %s\n\n", analysisResult.Summary))

		if len(analysisResult.KeyFindings) > 0 {
			finalContent.WriteString("### 📋 关键发现\n")
			for _, finding := range analysisResult.KeyFindings {
				finalContent.WriteString(fmt.Sprintf("- %s\n", finding))
			}
			finalContent.WriteString("\n")
		}

		if len(analysisResult.Recommendations) > 0 {
			finalContent.WriteString("### 💡 专业建议\n")
			for _, rec := range analysisResult.Recommendations {
				finalContent.WriteString(fmt.Sprintf("- %s\n", rec))
			}
			finalContent.WriteString("\n")
		}

		if analysisResult.RiskAssessment != "" {
			finalContent.WriteString(fmt.Sprintf("### ⚠️ 风险评估\n%s\n\n", analysisResult.RiskAssessment))
		}
	}

	// 构建最终响应
	totalTokens := intentResponse.Usage.TotalTokens
	if commandInfo["execution_type"] != "sql" {
		// 只有非智能SQL生成的情况才有commandResponse
		if commandResponse != nil {
			totalTokens += commandResponse.Usage.TotalTokens
		}
	}

	response := &ProcessMessageResponse{
		Content:        finalContent.String(),
		Intent:         fmt.Sprintf("%v", intent["intent_type"]),
		Confidence:     ewh.getFloatValue(intent["confidence"], 0.8),
		Parameters:     intent,
		TokenCount:     totalTokens,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}
	
	ewh.logger.WithFields(logrus.Fields{
		"total_processing_time": response.ProcessingTime,
		"total_tokens":          response.TokenCount,
		"final_confidence":      response.Confidence,
	}).Info("🎉 完整AI驱动运维流程成功完成")
	
	return response, nil
}

// StopProcessing 停止指定会话的处理
func (ewh *EnhancedWebSocketHandler) StopProcessing(sessionID string, userID int64) bool {
	sessionKey := fmt.Sprintf("%s_%d", sessionID, userID)

	ewh.contextMutex.RLock()
	cancel, exists := ewh.activeContexts[sessionKey]
	ewh.contextMutex.RUnlock()

	if exists {
		ewh.logger.WithFields(logrus.Fields{
			"session_id": sessionID,
			"user_id":    userID,
			"session_key": sessionKey,
		}).Info("🛑 EnhancedWebSocketHandler: 取消正在进行的处理")

		cancel()
		return true
	}

	ewh.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
		"session_key": sessionKey,
	}).Warn("🛑 EnhancedWebSocketHandler: 没有找到活跃的处理Context")

	return false
}

// createCancelledResponse 创建取消响应
func (ewh *EnhancedWebSocketHandler) createCancelledResponse(message string, start time.Time) *ProcessMessageResponse {
	return &ProcessMessageResponse{
		Content:        "⏹️ 操作已被用户取消",
		Intent:         "cancelled",
		Confidence:     1.0,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
		TokenCount:     0,
		Parameters: map[string]interface{}{
			"status":         "cancelled",
			"message":        message,
			"cancelled_at":   time.Now(),
			"processing_time": time.Since(start).String(),
		},
	}
}

// 删除快速路径相关方法，让所有请求都通过完整的AI分析和渲染流程

// getDatabaseSchema 获取数据库表结构信息
func (ewh *EnhancedWebSocketHandler) getDatabaseSchema(ctx context.Context) string {
	var schema strings.Builder

	// 定义主要表结构
	tables := map[string]string{
		"hosts": `
### hosts 表（主机信息）
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | INTEGER | 主键ID | 1, 2, 3 |
| name | VARCHAR(255) | 主机名称 | "web-server-01" |
| ip_address | VARCHAR(45) | IP地址 | "*************" |
| username | VARCHAR(100) | SSH用户名 | "root" |
| password_encrypted | TEXT | SSH密码（加密） | "encrypted_password" |
| port | INTEGER | SSH端口 | 22 |
| status | VARCHAR(20) | 主机状态 | "online", "offline", "warning" |
| environment | VARCHAR(20) | 环境 | "production", "staging", "development" |
| last_connected | DATETIME | 最后连接时间 | "2025-08-04 09:30:00" |
| created_at | DATETIME | 创建时间 | "2025-08-04 08:00:00" |
| updated_at | DATETIME | 更新时间 | "2025-08-04 09:30:00" |
| deleted_at | DATETIME | 删除时间（软删除） | NULL 或时间戳 |`,

		"chat_sessions": `
### chat_sessions 表（聊天会话）
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | INTEGER | 主键ID | 1, 2, 3 |
| session_id | VARCHAR(255) | 会话ID | "session_1754270027870_5hx1a60ql" |
| user_id | INTEGER | 用户ID | 1 |
| title | VARCHAR(255) | 会话标题 | "WebSocket会话 - 2025-08-04 09:13" |
| created_at | DATETIME | 创建时间 | "2025-08-04 09:13:47" |
| updated_at | DATETIME | 更新时间 | "2025-08-04 09:13:47" |`,

		"chat_messages": `
### chat_messages 表（聊天消息）
| 字段名 | 类型 | 说明 | 示例 |
|--------|------|------|------|
| id | INTEGER | 主键ID | 1, 2, 3 |
| session_id | VARCHAR(255) | 会话ID | "session_1754270027870_5hx1a60ql" |
| role | VARCHAR(20) | 消息角色 | "user", "assistant" |
| content | TEXT | 消息内容 | "查看主机状态" |
| created_at | DATETIME | 创建时间 | "2025-08-04 09:13:50" |`,
	}

	schema.WriteString("## 🗄️ 数据库表结构信息\n\n")
	schema.WriteString("**重要提示**：生成SQL时必须使用下面列出的确切字段名，不要猜测或假设字段名！\n\n")

	for _, tableSchema := range tables {
		schema.WriteString(tableSchema)
		schema.WriteString("\n\n")
	}

	schema.WriteString("### 🔍 常用查询示例\n")
	schema.WriteString("- 查看所有主机：`SELECT * FROM hosts WHERE deleted_at IS NULL`\n")
	schema.WriteString("- 按IP查找主机：`SELECT * FROM hosts WHERE ip_address = '*************' AND deleted_at IS NULL`\n")
	schema.WriteString("- 删除主机（软删除）：`UPDATE hosts SET deleted_at = CURRENT_TIMESTAMP WHERE ip_address = '*************'`\n")
	schema.WriteString("- 查看在线主机：`SELECT * FROM hosts WHERE status = 'online' AND deleted_at IS NULL`\n")

	return schema.String()
}

// 移除具体业务逻辑，让DeepSeek完全负责决策和SQL生成

// parseIntentResponse 解析意图响应
func (ewh *EnhancedWebSocketHandler) parseIntentResponse(content string) (map[string]interface{}, error) {
	jsonContent := ewh.extractJSONFromResponse(content)
	if jsonContent == "" {
		return nil, fmt.Errorf("未找到有效的JSON内容")
	}
	
	var intent map[string]interface{}
	if err := json.Unmarshal([]byte(jsonContent), &intent); err != nil {
		return nil, fmt.Errorf("解析意图JSON失败: %w", err)
	}
	
	return intent, nil
}

// parseCommandResponse 解析命令响应
func (ewh *EnhancedWebSocketHandler) parseCommandResponse(content string) (map[string]interface{}, error) {
	jsonContent := ewh.extractJSONFromResponse(content)
	if jsonContent == "" {
		return nil, fmt.Errorf("未找到有效的JSON内容")
	}
	
	var command map[string]interface{}
	if err := json.Unmarshal([]byte(jsonContent), &command); err != nil {
		return nil, fmt.Errorf("解析命令JSON失败: %w", err)
	}
	
	return command, nil
}

// executeCommand 执行命令
func (ewh *EnhancedWebSocketHandler) executeCommand(
	ctx context.Context,
	commandInfo map[string]interface{},
	intent map[string]interface{},
	req *ProcessMessageRequest,
) (*UnifiedExecutionResult, error) {
	// 🔧 构建执行请求 - 确保DeepSeek生成的命令被正确传递
	intentParams := make(map[string]interface{})
	for k, v := range intent {
		intentParams[k] = v
	}

	// 🚀 关键修复：根据执行类型正确映射DeepSeek生成的命令
	if generatedCode, ok := commandInfo["generated_code"]; ok {
		if executionType, hasType := commandInfo["execution_type"]; hasType {
			switch executionType {
			case "sql":
				intentParams["sql"] = generatedCode
				ewh.logger.WithField("sql", generatedCode).Info("🔍 映射为SQL命令")
			case "shell":
				intentParams["ssh_command"] = generatedCode
				ewh.logger.WithField("ssh_command", generatedCode).Info("🔍 映射为SSH命令")
			case "password_update":
				// 🔐 密码更新操作的特殊处理
				intentParams["password_update"] = generatedCode
				if ipAddress, ok := commandInfo["ip_address"]; ok {
					intentParams["ip_address"] = ipAddress
				}
				if newPassword, ok := commandInfo["new_password"]; ok {
					intentParams["new_password"] = newPassword
				}
				ewh.logger.WithField("password_update", generatedCode).Info("🔍 映射为密码更新操作")
			default:
				// 默认情况，保持原有逻辑
				intentParams["sql"] = generatedCode
				ewh.logger.WithField("unknown_type", executionType).Warn("⚠️ 未知执行类型，默认映射为SQL")
			}
		}
		intentParams["generated_code"] = generatedCode
	}
	if executionType, ok := commandInfo["execution_type"]; ok {
		intentParams["execution_type"] = executionType
	}
	if description, ok := commandInfo["description"]; ok {
		intentParams["description"] = description
	}

	execReq := &ExecutionRequest{
		SessionID:   req.SessionID,
		UserID:      req.UserID,
		OriginalMsg: req.Message,
		Intent: &IntentResult{
			Type:       fmt.Sprintf("%v", intent["intent_type"]),
			Confidence: ewh.getFloatValue(intent["confidence"], 0.8),
			Parameters: intentParams, // 使用增强的参数
		},
		Context: map[string]interface{}{
			"command_info":     commandInfo,
			"user_input":       req.Message,
			"deepseek_driven":  true,
		},
	}
	
	// 使用统一执行引擎执行
	return ewh.executionEngine.Execute(ctx, execReq)
}

// analyzeExecutionResult 分析执行结果
func (ewh *EnhancedWebSocketHandler) analyzeExecutionResult(
	ctx context.Context,
	executionResult *UnifiedExecutionResult,
	req *ProcessMessageRequest,
) (*AnalysisResult, error) {
	analysisReq := &AnalysisRequest{
		UserInput:       req.Message,
		Operation:       executionResult.Action,
		ExecutionResult: executionResult,
		Context: map[string]interface{}{
			"session_id": req.SessionID,
			"user_id":    req.UserID,
		},
	}
	
	return ewh.resultAnalyzer.AnalyzeExecutionResult(ctx, analysisReq)
}

// renderFinalResult 渲染最终结果
func (ewh *EnhancedWebSocketHandler) renderFinalResult(
	ctx context.Context,
	executionResult *UnifiedExecutionResult,
	analysisResult *AnalysisResult,
	req *ProcessMessageRequest,
) (*RenderResult, error) {
	renderReq := &RenderRequest{
		UserInput:       req.Message,
		Operation:       executionResult.Action,
		ExecutionResult: executionResult,
		AnalysisResult:  analysisResult,
		RenderStyle:     "detailed", // 可以根据用户偏好调整
		Context: map[string]interface{}{
			"session_id": req.SessionID,
			"user_id":    req.UserID,
		},
	}
	
	return ewh.resultRenderer.RenderExecutionResult(ctx, renderReq)
}

// createErrorResponse 创建错误响应
func (ewh *EnhancedWebSocketHandler) createErrorResponse(message string, err error, start time.Time) *ProcessMessageResponse {
	return &ProcessMessageResponse{
		Content:        fmt.Sprintf("❌ %s: %s\n\n💡 请尝试重新描述您的需求或联系技术支持", message, err.Error()),
		Intent:         "error",
		Confidence:     0.0,
		Parameters:     map[string]interface{}{"error": err.Error()},
		TokenCount:     0,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}
}

// extractJSONFromResponse 从响应中提取JSON
func (ewh *EnhancedWebSocketHandler) extractJSONFromResponse(content string) string {
	// 实现与AIResultAnalyzer中相同的JSON提取逻辑
	return ewh.resultAnalyzer.extractJSONFromResponse(content)
}

// getFloatValue 安全获取float值
func (ewh *EnhancedWebSocketHandler) getFloatValue(value interface{}, defaultValue float64) float64 {
	if v, ok := value.(float64); ok {
		return v
	}
	if v, ok := value.(float32); ok {
		return float64(v)
	}
	return defaultValue
}

// getStatusEmoji 获取状态emoji
func (ewh *EnhancedWebSocketHandler) getStatusEmoji(success bool) string {
	if success {
		return "✅ 成功"
	}
	return "❌ 失败"
}

// determineOperation 根据用户输入确定操作类型
func (ewh *EnhancedWebSocketHandler) determineOperation(userInput string) string {
	input := strings.ToLower(userInput)

	// 查询操作
	if strings.Contains(input, "查看") || strings.Contains(input, "查询") ||
	   strings.Contains(input, "列出") || strings.Contains(input, "显示") ||
	   strings.Contains(input, "状态") || strings.Contains(input, "信息") {
		return "select"
	}

	// 更新操作
	if strings.Contains(input, "修改") || strings.Contains(input, "更新") ||
	   strings.Contains(input, "改变") || strings.Contains(input, "设置") {
		return "update"
	}

	// 插入操作
	if strings.Contains(input, "添加") || strings.Contains(input, "新增") ||
	   strings.Contains(input, "创建") || strings.Contains(input, "注册") {
		return "insert"
	}

	// 删除操作
	if strings.Contains(input, "删除") || strings.Contains(input, "移除") ||
	   strings.Contains(input, "清除") {
		return "delete"
	}

	// 默认为查询操作
	return "select"
}

// isPasswordUpdateOperation 检测是否为密码更新操作
func (ewh *EnhancedWebSocketHandler) isPasswordUpdateOperation(userInput string) bool {
	input := strings.ToLower(userInput)
	return (strings.Contains(input, "修改") || strings.Contains(input, "更新") || strings.Contains(input, "设置")) &&
		   (strings.Contains(input, "密码") || strings.Contains(input, "口令"))
}

// extractPasswordUpdateInfo 提取密码更新信息
func (ewh *EnhancedWebSocketHandler) extractPasswordUpdateInfo(userInput string) (string, string, error) {
	// 使用正则表达式提取IP地址和密码
	// 格式：修改**************主机密码为1qaz#EDC

	// 提取IP地址
	ipRegex := `(\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3})`
	ipMatches := regexp.MustCompile(ipRegex).FindStringSubmatch(userInput)
	if len(ipMatches) < 2 {
		return "", "", fmt.Errorf("未找到有效的IP地址")
	}
	ipAddress := ipMatches[1]

	// 提取密码（密码为关键词后的内容）
	passwordRegex := `(?:密码为|口令为|设置为|改为)\s*([^\s]+)`
	passwordMatches := regexp.MustCompile(passwordRegex).FindStringSubmatch(userInput)
	if len(passwordMatches) < 2 {
		return "", "", fmt.Errorf("未找到新密码")
	}
	newPassword := passwordMatches[1]

	return ipAddress, newPassword, nil
}
