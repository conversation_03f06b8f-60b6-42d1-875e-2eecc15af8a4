package pool

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ConnectionPool 连接池接口
type ConnectionPool interface {
	Get(ctx context.Context) (Connection, error)
	Put(conn Connection) error
	Close() error
	Stats() *PoolStats
}

// Connection 连接接口
type Connection interface {
	IsValid() bool
	Close() error
	LastUsed() time.Time
	SetLastUsed(time.Time)
}

// PoolConfig 连接池配置
type PoolConfig struct {
	MinConnections     int           `json:"min_connections"`
	MaxConnections     int           `json:"max_connections"`
	MaxIdleTime        time.Duration `json:"max_idle_time"`
	ConnectionTimeout  time.Duration `json:"connection_timeout"`
	ValidationInterval time.Duration `json:"validation_interval"`
	RetryAttempts      int           `json:"retry_attempts"`
	RetryDelay         time.Duration `json:"retry_delay"`
}

// PoolStats 连接池统计
type PoolStats struct {
	ActiveConnections int       `json:"active_connections"`
	IdleConnections   int       `json:"idle_connections"`
	TotalConnections  int       `json:"total_connections"`
	CreatedCount      int64     `json:"created_count"`
	DestroyedCount    int64     `json:"destroyed_count"`
	GetCount          int64     `json:"get_count"`
	PutCount          int64     `json:"put_count"`
	TimeoutCount      int64     `json:"timeout_count"`
	ErrorCount        int64     `json:"error_count"`
	LastUpdated       time.Time `json:"last_updated"`
}

// ConnectionFactory 连接工厂接口
type ConnectionFactory interface {
	Create(ctx context.Context) (Connection, error)
	Validate(conn Connection) bool
}

// GenericConnectionPool 通用连接池实现
type GenericConnectionPool struct {
	factory    ConnectionFactory
	config     *PoolConfig
	logger     *logrus.Logger
	
	// 连接管理
	idle       []Connection
	active     map[Connection]time.Time
	mutex      sync.RWMutex
	
	// 统计信息
	stats      *PoolStats
	statsMutex sync.RWMutex
	
	// 控制
	closed     bool
	closeChan  chan struct{}
}

// NewGenericConnectionPool 创建通用连接池
func NewGenericConnectionPool(
	factory ConnectionFactory,
	config *PoolConfig,
	logger *logrus.Logger,
) *GenericConnectionPool {
	if config == nil {
		config = &PoolConfig{
			MinConnections:     5,
			MaxConnections:     50,
			MaxIdleTime:        5 * time.Minute,
			ConnectionTimeout:  30 * time.Second,
			ValidationInterval: 1 * time.Minute,
			RetryAttempts:      3,
			RetryDelay:         1 * time.Second,
		}
	}

	pool := &GenericConnectionPool{
		factory:   factory,
		config:    config,
		logger:    logger,
		idle:      make([]Connection, 0, config.MaxConnections),
		active:    make(map[Connection]time.Time),
		stats:     &PoolStats{LastUpdated: time.Now()},
		closeChan: make(chan struct{}),
	}

	// 初始化最小连接数
	pool.initializeMinConnections()

	// 启动维护任务
	go pool.maintenanceTask()

	logger.WithFields(logrus.Fields{
		"min_connections": config.MinConnections,
		"max_connections": config.MaxConnections,
		"max_idle_time":   config.MaxIdleTime,
	}).Info("Connection pool initialized")

	return pool
}

// Get 获取连接
func (gcp *GenericConnectionPool) Get(ctx context.Context) (Connection, error) {
	gcp.updateStats("get")

	// 检查池是否已关闭
	if gcp.closed {
		return nil, fmt.Errorf("connection pool is closed")
	}

	// 设置超时
	timeoutCtx, cancel := context.WithTimeout(ctx, gcp.config.ConnectionTimeout)
	defer cancel()

	// 尝试获取连接
	for attempt := 0; attempt < gcp.config.RetryAttempts; attempt++ {
		conn, err := gcp.getConnection(timeoutCtx)
		if err == nil {
			return conn, nil
		}

		gcp.logger.WithFields(logrus.Fields{
			"attempt": attempt + 1,
			"error":   err,
		}).Warn("Failed to get connection, retrying")

		if attempt < gcp.config.RetryAttempts-1 {
			select {
			case <-time.After(gcp.config.RetryDelay):
				continue
			case <-timeoutCtx.Done():
				gcp.updateStats("timeout")
				return nil, fmt.Errorf("connection timeout after %d attempts", attempt+1)
			}
		}
	}

	gcp.updateStats("error")
	return nil, fmt.Errorf("failed to get connection after %d attempts", gcp.config.RetryAttempts)
}

// getConnection 内部获取连接方法
func (gcp *GenericConnectionPool) getConnection(ctx context.Context) (Connection, error) {
	gcp.mutex.Lock()
	defer gcp.mutex.Unlock()

	// 尝试从空闲连接中获取
	for len(gcp.idle) > 0 {
		conn := gcp.idle[len(gcp.idle)-1]
		gcp.idle = gcp.idle[:len(gcp.idle)-1]

		// 验证连接
		if gcp.factory.Validate(conn) {
			gcp.active[conn] = time.Now()
			conn.SetLastUsed(time.Now())
			return conn, nil
		} else {
			// 连接无效，关闭并销毁
			conn.Close()
			gcp.updateStats("destroyed")
		}
	}

	// 检查是否可以创建新连接
	if len(gcp.active) >= gcp.config.MaxConnections {
		return nil, fmt.Errorf("connection pool exhausted")
	}

	// 创建新连接
	conn, err := gcp.factory.Create(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to create connection: %w", err)
	}

	gcp.active[conn] = time.Now()
	conn.SetLastUsed(time.Now())
	gcp.updateStats("created")

	return conn, nil
}

// Put 归还连接
func (gcp *GenericConnectionPool) Put(conn Connection) error {
	if conn == nil {
		return fmt.Errorf("connection is nil")
	}

	gcp.updateStats("put")

	gcp.mutex.Lock()
	defer gcp.mutex.Unlock()

	// 从活跃连接中移除
	delete(gcp.active, conn)

	// 检查池是否已关闭
	if gcp.closed {
		conn.Close()
		return nil
	}

	// 验证连接
	if !gcp.factory.Validate(conn) {
		conn.Close()
		gcp.updateStats("destroyed")
		return nil
	}

	// 检查空闲连接数量限制
	if len(gcp.idle) >= gcp.config.MaxConnections {
		conn.Close()
		gcp.updateStats("destroyed")
		return nil
	}

	// 添加到空闲连接池
	gcp.idle = append(gcp.idle, conn)
	conn.SetLastUsed(time.Now())

	return nil
}

// Close 关闭连接池
func (gcp *GenericConnectionPool) Close() error {
	gcp.mutex.Lock()
	defer gcp.mutex.Unlock()

	if gcp.closed {
		return nil
	}

	gcp.closed = true
	close(gcp.closeChan)

	// 关闭所有空闲连接
	for _, conn := range gcp.idle {
		conn.Close()
	}
	gcp.idle = nil

	// 关闭所有活跃连接
	for conn := range gcp.active {
		conn.Close()
	}
	gcp.active = make(map[Connection]time.Time)

	gcp.logger.Info("Connection pool closed")
	return nil
}

// Stats 获取统计信息
func (gcp *GenericConnectionPool) Stats() *PoolStats {
	gcp.statsMutex.RLock()
	defer gcp.statsMutex.RUnlock()

	gcp.mutex.RLock()
	activeCount := len(gcp.active)
	idleCount := len(gcp.idle)
	gcp.mutex.RUnlock()

	// 创建副本
	stats := &PoolStats{
		ActiveConnections: activeCount,
		IdleConnections:   idleCount,
		TotalConnections:  activeCount + idleCount,
		CreatedCount:      gcp.stats.CreatedCount,
		DestroyedCount:    gcp.stats.DestroyedCount,
		GetCount:          gcp.stats.GetCount,
		PutCount:          gcp.stats.PutCount,
		TimeoutCount:      gcp.stats.TimeoutCount,
		ErrorCount:        gcp.stats.ErrorCount,
		LastUpdated:       time.Now(),
	}

	return stats
}

// initializeMinConnections 初始化最小连接数
func (gcp *GenericConnectionPool) initializeMinConnections() {
	ctx := context.Background()
	
	for i := 0; i < gcp.config.MinConnections; i++ {
		conn, err := gcp.factory.Create(ctx)
		if err != nil {
			gcp.logger.WithError(err).Warn("Failed to create initial connection")
			continue
		}

		gcp.idle = append(gcp.idle, conn)
		gcp.updateStats("created")
	}

	gcp.logger.WithField("connections", len(gcp.idle)).Info("Initialized minimum connections")
}

// maintenanceTask 维护任务
func (gcp *GenericConnectionPool) maintenanceTask() {
	ticker := time.NewTicker(gcp.config.ValidationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			gcp.performMaintenance()
		case <-gcp.closeChan:
			return
		}
	}
}

// performMaintenance 执行维护
func (gcp *GenericConnectionPool) performMaintenance() {
	gcp.mutex.Lock()
	defer gcp.mutex.Unlock()

	now := time.Now()

	// 清理过期的空闲连接
	validIdle := make([]Connection, 0, len(gcp.idle))
	for _, conn := range gcp.idle {
		if now.Sub(conn.LastUsed()) > gcp.config.MaxIdleTime {
			conn.Close()
			gcp.updateStats("destroyed")
		} else if gcp.factory.Validate(conn) {
			validIdle = append(validIdle, conn)
		} else {
			conn.Close()
			gcp.updateStats("destroyed")
		}
	}
	gcp.idle = validIdle

	// 检查活跃连接
	for conn, startTime := range gcp.active {
		if now.Sub(startTime) > gcp.config.ConnectionTimeout {
			gcp.logger.WithField("duration", now.Sub(startTime)).Warn("Long-running active connection detected")
		}
	}

	// 确保最小连接数
	if len(gcp.idle) < gcp.config.MinConnections {
		needed := gcp.config.MinConnections - len(gcp.idle)
		ctx := context.Background()
		
		for i := 0; i < needed; i++ {
			conn, err := gcp.factory.Create(ctx)
			if err != nil {
				gcp.logger.WithError(err).Warn("Failed to create connection during maintenance")
				break
			}
			gcp.idle = append(gcp.idle, conn)
			gcp.updateStats("created")
		}
	}

	gcp.logger.WithFields(logrus.Fields{
		"active_connections": len(gcp.active),
		"idle_connections":   len(gcp.idle),
	}).Debug("Connection pool maintenance completed")
}

// updateStats 更新统计信息
func (gcp *GenericConnectionPool) updateStats(operation string) {
	gcp.statsMutex.Lock()
	defer gcp.statsMutex.Unlock()

	switch operation {
	case "created":
		gcp.stats.CreatedCount++
	case "destroyed":
		gcp.stats.DestroyedCount++
	case "get":
		gcp.stats.GetCount++
	case "put":
		gcp.stats.PutCount++
	case "timeout":
		gcp.stats.TimeoutCount++
	case "error":
		gcp.stats.ErrorCount++
	}

	gcp.stats.LastUpdated = time.Now()
}
