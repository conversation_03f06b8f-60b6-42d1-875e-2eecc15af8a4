package ai

import (
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EmotionMemory 情感记忆管理器
type EmotionMemory struct {
	db     *gorm.DB
	logger *logrus.Logger
	config *EmotionalIntelligenceConfig
}

// NewEmotionMemory 创建情感记忆管理器
func NewEmotionMemory(
	db *gorm.DB,
	logger *logrus.Logger,
	config *EmotionalIntelligenceConfig,
) *EmotionMemory {
	return &EmotionMemory{
		db:     db,
		logger: logger,
		config: config,
	}
}

// SaveEmotionRecord 保存情感记录
func (em *EmotionMemory) SaveEmotionRecord(record *EmotionMemoryRecord) error {
	if record == nil {
		return fmt.Errorf("emotion record is nil")
	}

	record.CreatedAt = time.Now()
	record.UpdatedAt = time.Now()

	if err := em.db.Create(record).Error; err != nil {
		em.logger.WithError(err).Error("Failed to save emotion record")
		return fmt.Errorf("failed to save emotion record: %w", err)
	}

	em.logger.WithFields(logrus.Fields{
		"user_id":      record.UserID,
		"session_id":   record.SessionID,
		"emotion_type": record.EmotionType,
		"intensity":    record.Intensity,
		"confidence":   record.Confidence,
	}).Debug("Emotion record saved successfully")

	return nil
}

// GetUserEmotionHistory 获取用户情感历史
func (em *EmotionMemory) GetUserEmotionHistory(userID int64, limit int) ([]*EmotionMemoryRecord, error) {
	var records []*EmotionMemoryRecord

	query := em.db.Where("user_id = ?", userID).
		Order("created_at DESC")

	if limit > 0 {
		query = query.Limit(limit)
	}

	if err := query.Find(&records).Error; err != nil {
		em.logger.WithError(err).Error("Failed to get user emotion history")
		return nil, fmt.Errorf("failed to get user emotion history: %w", err)
	}

	em.logger.WithFields(logrus.Fields{
		"user_id":      userID,
		"record_count": len(records),
		"limit":        limit,
	}).Debug("Retrieved user emotion history")

	return records, nil
}

// GetSessionEmotionHistory 获取会话情感历史
func (em *EmotionMemory) GetSessionEmotionHistory(sessionID string) ([]*EmotionMemoryRecord, error) {
	var records []*EmotionMemoryRecord

	if err := em.db.Where("session_id = ?", sessionID).
		Order("created_at ASC").
		Find(&records).Error; err != nil {
		em.logger.WithError(err).Error("Failed to get session emotion history")
		return nil, fmt.Errorf("failed to get session emotion history: %w", err)
	}

	em.logger.WithFields(logrus.Fields{
		"session_id":   sessionID,
		"record_count": len(records),
	}).Debug("Retrieved session emotion history")

	return records, nil
}

// GetUserEmotionalProfile 获取用户情感画像
func (em *EmotionMemory) GetUserEmotionalProfile(userID int64) (*UserEmotionalProfile, error) {
	// 获取最近30天的情感记录
	thirtyDaysAgo := time.Now().AddDate(0, 0, -30)
	var records []*EmotionMemoryRecord

	if err := em.db.Where("user_id = ? AND created_at > ?", userID, thirtyDaysAgo).
		Order("created_at DESC").
		Find(&records).Error; err != nil {
		em.logger.WithError(err).Error("Failed to get user emotion records for profile")
		return nil, fmt.Errorf("failed to get user emotion records: %w", err)
	}

	if len(records) == 0 {
		// 返回默认画像
		return &UserEmotionalProfile{
			UserID:              userID,
			DominantEmotions:    []EmotionType{EmotionNeutral},
			EmotionalPatterns:   make(map[string]float64),
			PreferredTone:       "professional",
			ResponsePreferences: make(map[string]interface{}),
			LastUpdated:         time.Now(),
			InteractionHistory:  []EmotionInteraction{},
		}, nil
	}

	// 分析情感模式
	emotionCounts := make(map[EmotionType]int)
	emotionIntensities := make(map[EmotionType]float64)
	totalIntensity := 0.0

	for _, record := range records {
		emotionType := EmotionType(record.EmotionType)
		emotionCounts[emotionType]++
		emotionIntensities[emotionType] += record.Intensity
		totalIntensity += record.Intensity
	}

	// 计算主导情感
	dominantEmotions := em.calculateDominantEmotions(emotionCounts)

	// 计算情感模式
	emotionalPatterns := em.calculateEmotionalPatterns(emotionCounts, emotionIntensities, len(records))

	// 确定偏好语调
	preferredTone := em.determinePreferredTone(records)

	// 构建交互历史
	interactionHistory := em.buildInteractionHistory(records)

	profile := &UserEmotionalProfile{
		UserID:              userID,
		DominantEmotions:    dominantEmotions,
		EmotionalPatterns:   emotionalPatterns,
		PreferredTone:       preferredTone,
		ResponsePreferences: em.analyzeResponsePreferences(records),
		LastUpdated:         time.Now(),
		InteractionHistory:  interactionHistory,
	}

	em.logger.WithFields(logrus.Fields{
		"user_id":           userID,
		"record_count":      len(records),
		"dominant_emotions": dominantEmotions,
		"preferred_tone":    preferredTone,
	}).Info("Generated user emotional profile")

	return profile, nil
}

// GetEmotionTrends 获取情感趋势
func (em *EmotionMemory) GetEmotionTrends(userID int64, days int) (map[string]interface{}, error) {
	startDate := time.Now().AddDate(0, 0, -days)
	var records []*EmotionMemoryRecord

	if err := em.db.Where("user_id = ? AND created_at > ?", userID, startDate).
		Order("created_at ASC").
		Find(&records).Error; err != nil {
		return nil, fmt.Errorf("failed to get emotion trends: %w", err)
	}

	// 按日期分组统计
	dailyEmotions := make(map[string]map[EmotionType]int)
	dailyIntensities := make(map[string]float64)

	for _, record := range records {
		dateKey := record.CreatedAt.Format("2006-01-02")
		if dailyEmotions[dateKey] == nil {
			dailyEmotions[dateKey] = make(map[EmotionType]int)
		}

		emotionType := EmotionType(record.EmotionType)
		dailyEmotions[dateKey][emotionType]++
		dailyIntensities[dateKey] += record.Intensity
	}

	// 计算趋势
	trends := map[string]interface{}{
		"daily_emotions":    dailyEmotions,
		"daily_intensities": dailyIntensities,
		"total_records":     len(records),
		"analysis_period":   fmt.Sprintf("%d days", days),
		"trend_analysis":    em.analyzeTrends(dailyEmotions, dailyIntensities),
	}

	return trends, nil
}

// CleanupExpiredRecords 清理过期记录
func (em *EmotionMemory) CleanupExpiredRecords() error {
	if em.config.MemoryRetentionDays <= 0 {
		return nil // 不清理
	}

	cutoffDate := time.Now().AddDate(0, 0, -em.config.MemoryRetentionDays)

	result := em.db.Where("created_at < ?", cutoffDate).Delete(&EmotionMemoryRecord{})
	if result.Error != nil {
		em.logger.WithError(result.Error).Error("Failed to cleanup expired emotion records")
		return fmt.Errorf("failed to cleanup expired records: %w", result.Error)
	}

	if result.RowsAffected > 0 {
		em.logger.WithFields(logrus.Fields{
			"deleted_count": result.RowsAffected,
			"cutoff_date":   cutoffDate,
		}).Info("Cleaned up expired emotion records")
	}

	return nil
}

// UpdateEmotionRecord 更新情感记录
func (em *EmotionMemory) UpdateEmotionRecord(recordID int64, updates map[string]interface{}) error {
	updates["updated_at"] = time.Now()

	if err := em.db.Model(&EmotionMemoryRecord{}).Where("id = ?", recordID).Updates(updates).Error; err != nil {
		em.logger.WithError(err).Error("Failed to update emotion record")
		return fmt.Errorf("failed to update emotion record: %w", err)
	}

	em.logger.WithFields(logrus.Fields{
		"record_id": recordID,
		"updates":   updates,
	}).Debug("Emotion record updated successfully")

	return nil
}

// GetEmotionStatistics 获取情感统计
func (em *EmotionMemory) GetEmotionStatistics(userID int64, timeRange string) (map[string]interface{}, error) {
	var startTime time.Time
	switch timeRange {
	case "today":
		startTime = time.Now().Truncate(24 * time.Hour)
	case "week":
		startTime = time.Now().AddDate(0, 0, -7)
	case "month":
		startTime = time.Now().AddDate(0, -1, 0)
	default:
		startTime = time.Now().AddDate(0, 0, -7) // 默认一周
	}

	var records []*EmotionMemoryRecord
	if err := em.db.Where("user_id = ? AND created_at > ?", userID, startTime).
		Find(&records).Error; err != nil {
		return nil, fmt.Errorf("failed to get emotion statistics: %w", err)
	}

	// 统计各种情感的出现次数和平均强度
	emotionStats := make(map[string]map[string]interface{})
	totalRecords := len(records)

	emotionCounts := make(map[EmotionType]int)
	emotionIntensities := make(map[EmotionType][]float64)

	for _, record := range records {
		emotionType := EmotionType(record.EmotionType)
		emotionCounts[emotionType]++
		emotionIntensities[emotionType] = append(emotionIntensities[emotionType], record.Intensity)
	}

	for emotion, count := range emotionCounts {
		intensities := emotionIntensities[emotion]
		avgIntensity := em.calculateAverage(intensities)

		emotionStats[string(emotion)] = map[string]interface{}{
			"count":         count,
			"percentage":    float64(count) / float64(totalRecords) * 100,
			"avg_intensity": avgIntensity,
			"max_intensity": em.calculateMax(intensities),
			"min_intensity": em.calculateMin(intensities),
		}
	}

	return map[string]interface{}{
		"time_range":    timeRange,
		"total_records": totalRecords,
		"emotion_stats": emotionStats,
		"analysis_time": time.Now(),
	}, nil
}

// 私有辅助方法

func (em *EmotionMemory) calculateDominantEmotions(emotionCounts map[EmotionType]int) []EmotionType {
	type emotionCount struct {
		emotion EmotionType
		count   int
	}

	var emotions []emotionCount
	for emotion, count := range emotionCounts {
		emotions = append(emotions, emotionCount{emotion, count})
	}

	// 按计数排序
	for i := 0; i < len(emotions)-1; i++ {
		for j := i + 1; j < len(emotions); j++ {
			if emotions[i].count < emotions[j].count {
				emotions[i], emotions[j] = emotions[j], emotions[i]
			}
		}
	}

	// 返回前3个主导情感
	var dominantEmotions []EmotionType
	maxCount := 3
	if len(emotions) < maxCount {
		maxCount = len(emotions)
	}

	for i := 0; i < maxCount; i++ {
		dominantEmotions = append(dominantEmotions, emotions[i].emotion)
	}

	return dominantEmotions
}

func (em *EmotionMemory) calculateEmotionalPatterns(
	emotionCounts map[EmotionType]int,
	emotionIntensities map[EmotionType]float64,
	totalRecords int,
) map[string]float64 {
	patterns := make(map[string]float64)

	for emotion, count := range emotionCounts {
		percentage := float64(count) / float64(totalRecords)
		avgIntensity := emotionIntensities[emotion] / float64(count)

		patterns[string(emotion)+"_frequency"] = percentage
		patterns[string(emotion)+"_avg_intensity"] = avgIntensity
	}

	return patterns
}

func (em *EmotionMemory) determinePreferredTone(records []*EmotionMemoryRecord) string {
	// 基于历史响应风格确定偏好语调
	styleCount := make(map[string]int)

	for _, record := range records {
		if record.ResponseStyle != "" {
			styleCount[record.ResponseStyle]++
		}
	}

	maxCount := 0
	preferredTone := "professional"

	for style, count := range styleCount {
		if count > maxCount {
			maxCount = count
			preferredTone = style
		}
	}

	return preferredTone
}

func (em *EmotionMemory) buildInteractionHistory(records []*EmotionMemoryRecord) []EmotionInteraction {
	var interactions []EmotionInteraction

	// 取最近10条记录构建交互历史
	maxRecords := 10
	if len(records) < maxRecords {
		maxRecords = len(records)
	}

	for i := 0; i < maxRecords; i++ {
		record := records[i]
		interaction := EmotionInteraction{
			Timestamp:     record.CreatedAt,
			UserEmotion:   EmotionType(record.EmotionType),
			ResponseStyle: record.ResponseStyle,
			UserFeedback:  "",                // 暂时为空，后续可以添加用户反馈机制
			Effectiveness: record.Confidence, // 使用置信度作为效果评估
		}
		interactions = append(interactions, interaction)
	}

	return interactions
}

func (em *EmotionMemory) analyzeResponsePreferences(records []*EmotionMemoryRecord) map[string]interface{} {
	preferences := make(map[string]interface{})

	// 分析响应风格偏好
	stylePreferences := make(map[string]int)
	for _, record := range records {
		if record.ResponseStyle != "" {
			stylePreferences[record.ResponseStyle]++
		}
	}
	preferences["style_preferences"] = stylePreferences

	// 分析响应时间偏好（基于创建时间的小时）
	hourPreferences := make(map[int]int)
	for _, record := range records {
		hour := record.CreatedAt.Hour()
		hourPreferences[hour]++
	}
	preferences["time_preferences"] = hourPreferences

	return preferences
}

func (em *EmotionMemory) analyzeTrends(
	dailyEmotions map[string]map[EmotionType]int,
	dailyIntensities map[string]float64,
) map[string]interface{} {
	trends := make(map[string]interface{})

	// 简单的趋势分析
	var intensityValues []float64
	for _, intensity := range dailyIntensities {
		intensityValues = append(intensityValues, intensity)
	}

	if len(intensityValues) > 1 {
		// 计算强度趋势（简单的线性趋势）
		firstHalf := intensityValues[:len(intensityValues)/2]
		secondHalf := intensityValues[len(intensityValues)/2:]

		firstAvg := em.calculateAverage(firstHalf)
		secondAvg := em.calculateAverage(secondHalf)

		if secondAvg > firstAvg {
			trends["intensity_trend"] = "increasing"
		} else if secondAvg < firstAvg {
			trends["intensity_trend"] = "decreasing"
		} else {
			trends["intensity_trend"] = "stable"
		}

		trends["trend_strength"] = abs(secondAvg - firstAvg)
	}

	return trends
}

func (em *EmotionMemory) calculateAverage(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}

	sum := 0.0
	for _, v := range values {
		sum += v
	}
	return sum / float64(len(values))
}

func (em *EmotionMemory) calculateMax(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}

	max := values[0]
	for _, v := range values {
		if v > max {
			max = v
		}
	}
	return max
}

func (em *EmotionMemory) calculateMin(values []float64) float64 {
	if len(values) == 0 {
		return 0
	}

	min := values[0]
	for _, v := range values {
		if v < min {
			min = v
		}
	}
	return min
}

func abs(x float64) float64 {
	if x < 0 {
		return -x
	}
	return x
}
