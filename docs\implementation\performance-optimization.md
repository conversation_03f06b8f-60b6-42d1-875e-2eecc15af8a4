# 性能优化策略

## 🚀 性能优化概述

AI对话运维管理平台采用多层次性能优化策略，确保系统在高并发、大数据量场景下的稳定运行。

### 性能目标
- **响应时间**：API响应时间 < 500ms (P95)
- **并发能力**：支持1000并发用户
- **吞吐量**：处理10000 QPS
- **资源使用**：内存使用 < 1GB，CPU使用 < 70%

## 🔧 缓存优化策略

### 多级缓存架构
```go
type CacheManager struct {
    l1Cache cache.Cache      // 内存缓存（热数据）
    l2Cache cache.Cache      // Redis缓存（温数据）
    l3Cache database.DB      // 数据库（冷数据）
    metrics *CacheMetrics
}

type CacheConfig struct {
    L1 struct {
        Size     int           `yaml:"size"`
        TTL      time.Duration `yaml:"ttl"`
        MaxItems int           `yaml:"max_items"`
    } `yaml:"l1"`
    
    L2 struct {
        Address  string        `yaml:"address"`
        Password string        `yaml:"password"`
        DB       int           `yaml:"db"`
        TTL      time.Duration `yaml:"ttl"`
    } `yaml:"l2"`
    
    Strategy struct {
        WriteThrough bool `yaml:"write_through"`
        WriteBack    bool `yaml:"write_back"`
        ReadAhead    bool `yaml:"read_ahead"`
    } `yaml:"strategy"`
}

// 智能缓存获取
func (cm *CacheManager) Get(key string) (interface{}, error) {
    // L1缓存查找
    if value, found := cm.l1Cache.Get(key); found {
        cm.metrics.RecordHit("l1")
        return value, nil
    }
    
    // L2缓存查找
    if value, found := cm.l2Cache.Get(key); found {
        cm.metrics.RecordHit("l2")
        // 异步回写到L1缓存
        go cm.l1Cache.Set(key, value, cache.DefaultExpiration)
        return value, nil
    }
    
    // L3数据库查找
    value, err := cm.l3Cache.Get(key)
    if err != nil {
        cm.metrics.RecordMiss()
        return nil, err
    }
    
    cm.metrics.RecordHit("l3")
    
    // 异步回写到上级缓存
    go func() {
        cm.l2Cache.Set(key, value, cm.config.L2.TTL)
        cm.l1Cache.Set(key, value, cm.config.L1.TTL)
    }()
    
    return value, nil
}

// 缓存预热
func (cm *CacheManager) Warmup() error {
    // 预加载热点数据
    hotKeys := []string{
        "system:config",
        "user:permissions",
        "host:status",
    }
    
    for _, key := range hotKeys {
        if _, err := cm.Get(key); err != nil {
            log.Warnf("Failed to warmup cache for key %s: %v", key, err)
        }
    }
    
    return nil
}
```

### 缓存策略配置
```yaml
cache:
  l1_cache:
    size: 100MB
    ttl: 5m
    max_items: 10000
    
  l2_cache:
    address: "redis:6379"
    password: ""
    db: 0
    ttl: 1h
    
  strategy:
    write_through: true
    write_back: false
    read_ahead: true
    
  patterns:
    user_data:
      ttl: 30m
      refresh_ahead: 5m
    host_status:
      ttl: 1m
      refresh_ahead: 10s
    system_config:
      ttl: 1h
      refresh_ahead: 10m
```

## 🔗 连接池优化

### 数据库连接池
```go
type DatabasePool struct {
    db       *gorm.DB
    config   *PoolConfig
    metrics  *PoolMetrics
    monitor  *PoolMonitor
}

type PoolConfig struct {
    MaxOpenConns    int           `yaml:"max_open_conns"`
    MaxIdleConns    int           `yaml:"max_idle_conns"`
    ConnMaxLifetime time.Duration `yaml:"conn_max_lifetime"`
    ConnMaxIdleTime time.Duration `yaml:"conn_max_idle_time"`
    HealthCheck     bool          `yaml:"health_check"`
    HealthInterval  time.Duration `yaml:"health_interval"`
}

func (dp *DatabasePool) Initialize() error {
    sqlDB, err := dp.db.DB()
    if err != nil {
        return err
    }
    
    // 连接池配置
    sqlDB.SetMaxOpenConns(dp.config.MaxOpenConns)
    sqlDB.SetMaxIdleConns(dp.config.MaxIdleConns)
    sqlDB.SetConnMaxLifetime(dp.config.ConnMaxLifetime)
    sqlDB.SetConnMaxIdleTime(dp.config.ConnMaxIdleTime)
    
    // 启动健康检查
    if dp.config.HealthCheck {
        go dp.healthCheckLoop()
    }
    
    return nil
}

func (dp *DatabasePool) healthCheckLoop() {
    ticker := time.NewTicker(dp.config.HealthInterval)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            sqlDB, _ := dp.db.DB()
            stats := sqlDB.Stats()
            
            dp.metrics.RecordStats(stats)
            
            // 检查连接池状态
            if stats.OpenConnections > int(float64(dp.config.MaxOpenConns)*0.9) {
                log.Warn("Database connection pool is nearly full")
            }
        }
    }
}
```

### SSH连接池
```go
type SSHConnectionPool struct {
    pools   map[string]*ssh.Pool
    config  *SSHPoolConfig
    mutex   sync.RWMutex
    metrics *SSHPoolMetrics
}

type SSHPoolConfig struct {
    MaxConnections      int           `yaml:"max_connections"`
    IdleTimeout        time.Duration `yaml:"idle_timeout"`
    ConnectionTimeout  time.Duration `yaml:"connection_timeout"`
    HealthCheckInterval time.Duration `yaml:"health_check_interval"`
    RetryAttempts      int           `yaml:"retry_attempts"`
    RetryDelay         time.Duration `yaml:"retry_delay"`
}

func (scp *SSHConnectionPool) GetConnection(hostID string) (*ssh.Client, error) {
    scp.mutex.RLock()
    pool, exists := scp.pools[hostID]
    scp.mutex.RUnlock()
    
    if !exists {
        return nil, fmt.Errorf("host %s not found in pool", hostID)
    }
    
    // 尝试获取连接
    for attempt := 0; attempt < scp.config.RetryAttempts; attempt++ {
        conn, err := pool.Get()
        if err == nil {
            // 健康检查
            if scp.isConnectionHealthy(conn) {
                scp.metrics.RecordConnectionAcquired(hostID)
                return conn, nil
            }
            pool.Remove(conn)
        }
        
        if attempt < scp.config.RetryAttempts-1 {
            time.Sleep(scp.config.RetryDelay)
        }
    }
    
    return nil, errors.New("failed to acquire healthy connection")
}

// 连接健康检查
func (scp *SSHConnectionPool) isConnectionHealthy(conn *ssh.Client) bool {
    session, err := conn.NewSession()
    if err != nil {
        return false
    }
    defer session.Close()
    
    // 执行简单命令测试连接
    ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
    defer cancel()
    
    done := make(chan error, 1)
    go func() {
        done <- session.Run("echo 'health_check'")
    }()
    
    select {
    case err := <-done:
        return err == nil
    case <-ctx.Done():
        return false
    }
}
```

## ⚡ 并发控制优化

### 工作池模式
```go
type WorkerPool struct {
    workers     int
    jobQueue    chan Job
    resultQueue chan Result
    quit        chan bool
    wg          sync.WaitGroup
    metrics     *WorkerMetrics
}

type Job struct {
    ID       string
    Type     string
    Payload  interface{}
    Priority int
    Timeout  time.Duration
    Callback func(Result)
}

type Result struct {
    JobID    string
    Success  bool
    Data     interface{}
    Error    error
    Duration time.Duration
}

func NewWorkerPool(workers int, queueSize int) *WorkerPool {
    return &WorkerPool{
        workers:     workers,
        jobQueue:    make(chan Job, queueSize),
        resultQueue: make(chan Result, queueSize),
        quit:        make(chan bool),
        metrics:     NewWorkerMetrics(),
    }
}

func (wp *WorkerPool) Start() {
    for i := 0; i < wp.workers; i++ {
        wp.wg.Add(1)
        go wp.worker(i)
    }
    
    // 启动结果处理器
    go wp.resultProcessor()
}

func (wp *WorkerPool) worker(id int) {
    defer wp.wg.Done()
    
    for {
        select {
        case job := <-wp.jobQueue:
            start := time.Now()
            result := wp.processJob(job)
            result.Duration = time.Since(start)
            
            wp.metrics.RecordJobProcessed(job.Type, result.Success, result.Duration)
            
            select {
            case wp.resultQueue <- result:
            case <-time.After(1 * time.Second):
                log.Warn("Result queue is full, dropping result")
            }
            
        case <-wp.quit:
            return
        }
    }
}

func (wp *WorkerPool) Submit(job Job) error {
    select {
    case wp.jobQueue <- job:
        wp.metrics.RecordJobSubmitted(job.Type)
        return nil
    case <-time.After(5 * time.Second):
        wp.metrics.RecordJobRejected(job.Type)
        return errors.New("job queue is full")
    }
}
```

### 限流器实现
```go
type RateLimiter struct {
    limiters map[string]*rate.Limiter
    rules    []RateLimitRule
    mutex    sync.RWMutex
    metrics  *RateLimitMetrics
}

type RateLimitRule struct {
    Pattern   string     `yaml:"pattern"`
    Rate      rate.Limit `yaml:"rate"`
    Burst     int        `yaml:"burst"`
    UserBased bool       `yaml:"user_based"`
    IPBased   bool       `yaml:"ip_based"`
}

func (rl *RateLimiter) Allow(key string, rule RateLimitRule) bool {
    rl.mutex.RLock()
    limiter, exists := rl.limiters[key]
    rl.mutex.RUnlock()
    
    if !exists {
        rl.mutex.Lock()
        limiter = rate.NewLimiter(rule.Rate, rule.Burst)
        rl.limiters[key] = limiter
        rl.mutex.Unlock()
    }
    
    allowed := limiter.Allow()
    rl.metrics.RecordRequest(key, allowed)
    
    return allowed
}

// 自适应限流
type AdaptiveRateLimiter struct {
    baseLimiter *RateLimiter
    monitor     *SystemMonitor
    adjuster    *RateAdjuster
}

func (arl *AdaptiveRateLimiter) Allow(key string) bool {
    // 获取系统负载
    load := arl.monitor.GetSystemLoad()
    
    // 根据负载调整限流率
    adjustedRate := arl.adjuster.AdjustRate(load)
    
    rule := RateLimitRule{
        Rate:  adjustedRate,
        Burst: int(adjustedRate * 2),
    }
    
    return arl.baseLimiter.Allow(key, rule)
}
```

## 🗄️ 数据库优化

### 查询优化
```go
type QueryOptimizer struct {
    db      *gorm.DB
    cache   cache.Cache
    metrics *QueryMetrics
}

// 分页查询优化
func (qo *QueryOptimizer) PaginatedQuery(query *gorm.DB, page, limit int, result interface{}) error {
    // 使用游标分页替代OFFSET
    if page > 100 { // 大页码使用游标分页
        return qo.cursorPagination(query, page, limit, result)
    }
    
    // 小页码使用传统分页
    offset := (page - 1) * limit
    return query.Offset(offset).Limit(limit).Find(result).Error
}

// 游标分页
func (qo *QueryOptimizer) cursorPagination(query *gorm.DB, page, limit int, result interface{}) error {
    // 计算游标位置
    cursor := (page - 1) * limit
    
    // 使用ID作为游标
    return query.Where("id > ?", cursor).Limit(limit).Find(result).Error
}

// 批量操作优化
func (qo *QueryOptimizer) BatchInsert(data []interface{}, batchSize int) error {
    for i := 0; i < len(data); i += batchSize {
        end := i + batchSize
        if end > len(data) {
            end = len(data)
        }
        
        batch := data[i:end]
        if err := qo.db.CreateInBatches(batch, batchSize).Error; err != nil {
            return err
        }
    }
    
    return nil
}

// 索引优化建议
func (qo *QueryOptimizer) AnalyzeSlowQueries() []IndexSuggestion {
    var suggestions []IndexSuggestion
    
    // 分析慢查询日志
    slowQueries := qo.getSlowQueries()
    
    for _, query := range slowQueries {
        if suggestion := qo.suggestIndex(query); suggestion != nil {
            suggestions = append(suggestions, *suggestion)
        }
    }
    
    return suggestions
}

type IndexSuggestion struct {
    Table   string   `json:"table"`
    Columns []string `json:"columns"`
    Type    string   `json:"type"`
    Reason  string   `json:"reason"`
}
```

### 数据库配置优化
```yaml
database:
  sqlite:
    # 性能优化配置
    pragma:
      journal_mode: WAL
      synchronous: NORMAL
      cache_size: -64000  # 64MB
      temp_store: MEMORY
      mmap_size: 268435456  # 256MB
      
    # 连接池配置
    pool:
      max_open_conns: 25
      max_idle_conns: 5
      conn_max_lifetime: 1h
      conn_max_idle_time: 10m
      
    # 查询优化
    query:
      timeout: 30s
      retry_attempts: 3
      batch_size: 1000
      
    # 索引配置
    indexes:
      auto_create: true
      analyze_interval: 1h
```

## 🌐 网络优化

### HTTP/2 和连接复用
```go
type HTTPClient struct {
    client  *http.Client
    pool    *HTTPClientPool
    metrics *HTTPMetrics
}

func NewOptimizedHTTPClient() *HTTPClient {
    transport := &http.Transport{
        // 连接池配置
        MaxIdleConns:        100,
        MaxIdleConnsPerHost: 10,
        IdleConnTimeout:     90 * time.Second,
        
        // 连接超时配置
        DialContext: (&net.Dialer{
            Timeout:   30 * time.Second,
            KeepAlive: 30 * time.Second,
        }).DialContext,
        
        // HTTP/2支持
        ForceAttemptHTTP2:     true,
        MaxConnsPerHost:       10,
        TLSHandshakeTimeout:   10 * time.Second,
        ExpectContinueTimeout: 1 * time.Second,
        
        // 压缩配置
        DisableCompression: false,
    }
    
    client := &http.Client{
        Transport: transport,
        Timeout:   60 * time.Second,
    }
    
    return &HTTPClient{
        client:  client,
        metrics: NewHTTPMetrics(),
    }
}
```

### 响应压缩
```go
func CompressionMiddleware() gin.HandlerFunc {
    return gin.HandlerFunc(func(c *gin.Context) {
        // 检查客户端是否支持压缩
        if !strings.Contains(c.GetHeader("Accept-Encoding"), "gzip") {
            c.Next()
            return
        }
        
        // 检查响应大小阈值
        writer := &compressWriter{
            ResponseWriter: c.Writer,
            threshold:      1024, // 1KB
        }
        
        c.Writer = writer
        c.Next()
        
        writer.Close()
    })
}

type compressWriter struct {
    gin.ResponseWriter
    gzipWriter *gzip.Writer
    threshold  int
    buffer     *bytes.Buffer
}

func (cw *compressWriter) Write(data []byte) (int, error) {
    if cw.buffer == nil {
        cw.buffer = &bytes.Buffer{}
    }
    
    cw.buffer.Write(data)
    
    // 达到阈值时启用压缩
    if cw.buffer.Len() >= cw.threshold && cw.gzipWriter == nil {
        cw.Header().Set("Content-Encoding", "gzip")
        cw.gzipWriter = gzip.NewWriter(cw.ResponseWriter)
    }
    
    if cw.gzipWriter != nil {
        return cw.gzipWriter.Write(data)
    }
    
    return len(data), nil
}
```

## 📊 性能监控

### 性能指标收集
```go
type PerformanceMonitor struct {
    metrics    *prometheus.Registry
    collectors []prometheus.Collector
    profiler   *Profiler
}

type Profiler struct {
    cpuProfile    *os.File
    memProfile    *os.File
    goroutineProfile *os.File
    enabled       bool
}

func (pm *PerformanceMonitor) StartProfiling() error {
    if pm.profiler.enabled {
        return nil
    }
    
    // CPU性能分析
    cpuFile, err := os.Create("cpu.prof")
    if err != nil {
        return err
    }
    pm.profiler.cpuProfile = cpuFile
    
    if err := pprof.StartCPUProfile(cpuFile); err != nil {
        return err
    }
    
    // 内存性能分析
    go func() {
        ticker := time.NewTicker(30 * time.Second)
        defer ticker.Stop()
        
        for {
            select {
            case <-ticker.C:
                pm.captureMemoryProfile()
            }
        }
    }()
    
    pm.profiler.enabled = true
    return nil
}

func (pm *PerformanceMonitor) captureMemoryProfile() {
    memFile, err := os.Create(fmt.Sprintf("mem_%d.prof", time.Now().Unix()))
    if err != nil {
        log.Errorf("Failed to create memory profile: %v", err)
        return
    }
    defer memFile.Close()
    
    runtime.GC()
    if err := pprof.WriteHeapProfile(memFile); err != nil {
        log.Errorf("Failed to write memory profile: %v", err)
    }
}
```

### 性能基准测试
```go
func BenchmarkAPIEndpoints(b *testing.B) {
    router := setupRouter()
    
    testCases := []struct {
        name   string
        method string
        path   string
        body   string
    }{
        {"GetHosts", "GET", "/api/v1/hosts", ""},
        {"CreateHost", "POST", "/api/v1/hosts", `{"name":"test","ip":"127.0.0.1"}`},
        {"ChatMessage", "POST", "/api/v1/chat/message", `{"content":"hello"}`},
    }
    
    for _, tc := range testCases {
        b.Run(tc.name, func(b *testing.B) {
            b.ResetTimer()
            b.RunParallel(func(pb *testing.PB) {
                for pb.Next() {
                    req := httptest.NewRequest(tc.method, tc.path, strings.NewReader(tc.body))
                    w := httptest.NewRecorder()
                    router.ServeHTTP(w, req)
                    
                    if w.Code >= 400 {
                        b.Errorf("Request failed with status %d", w.Code)
                    }
                }
            })
        })
    }
}

// 负载测试
func LoadTest() {
    const (
        concurrency = 100
        duration    = 60 * time.Second
        targetRPS   = 1000
    )
    
    client := &http.Client{
        Timeout: 10 * time.Second,
    }
    
    var wg sync.WaitGroup
    results := make(chan time.Duration, concurrency*int(duration.Seconds()))
    
    start := time.Now()
    
    for i := 0; i < concurrency; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            
            for time.Since(start) < duration {
                reqStart := time.Now()
                
                resp, err := client.Get("http://localhost:8080/api/v1/health")
                if err != nil {
                    continue
                }
                resp.Body.Close()
                
                results <- time.Since(reqStart)
                
                // 控制请求频率
                time.Sleep(time.Duration(concurrency*1000/targetRPS) * time.Millisecond)
            }
        }()
    }
    
    wg.Wait()
    close(results)
    
    // 统计结果
    var durations []time.Duration
    for d := range results {
        durations = append(durations, d)
    }
    
    sort.Slice(durations, func(i, j int) bool {
        return durations[i] < durations[j]
    })
    
    fmt.Printf("Total requests: %d\n", len(durations))
    fmt.Printf("P50: %v\n", durations[len(durations)/2])
    fmt.Printf("P95: %v\n", durations[len(durations)*95/100])
    fmt.Printf("P99: %v\n", durations[len(durations)*99/100])
}
```
