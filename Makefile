# AI对话运维管理平台 - Makefile

# 变量定义
APP_NAME := aiops-platform
VERSION := $(shell git describe --tags --always --dirty 2>/dev/null || echo "dev")
BUILD_TIME := $(shell date -u '+%Y-%m-%d_%H:%M:%S')
GIT_COMMIT := $(shell git rev-parse --short HEAD 2>/dev/null || echo "unknown")

# Go相关变量
GO_VERSION := 1.21
GOOS := $(shell go env GOOS)
GOARCH := $(shell go env GOARCH)

# 构建标志
LDFLAGS := -ldflags "-w -s -X main.Version=$(VERSION) -X main.BuildTime=$(BUILD_TIME) -X main.GitCommit=$(GIT_COMMIT)"

# 目录
BUILD_DIR := build
DIST_DIR := dist
DOCKER_DIR := docker

.PHONY: help
help: ## 显示帮助信息
	@echo "AI对话运维管理平台 - 构建工具"
	@echo ""
	@echo "可用命令:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-15s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

.PHONY: deps
deps: ## 安装依赖
	@echo "安装Go依赖..."
	go mod download
	go mod verify
	@echo "依赖安装完成"

.PHONY: deps-dev
deps-dev: deps ## 安装开发依赖
	@echo "安装开发工具..."
	go install github.com/cosmtrek/air@latest
	go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
	go install github.com/swaggo/swag/cmd/swag@latest
	@echo "开发工具安装完成"

.PHONY: build
build: ## 构建应用
	@echo "构建应用..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=1 go build $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME) ./cmd/server
	CGO_ENABLED=1 go build $(LDFLAGS) -o $(BUILD_DIR)/migrate ./cmd/migrate
	@echo "构建完成: $(BUILD_DIR)/$(APP_NAME)"

.PHONY: build-linux
build-linux: ## 构建Linux版本
	@echo "构建Linux版本..."
	@mkdir -p $(BUILD_DIR)
	CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/$(APP_NAME)-linux ./cmd/server
	CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build $(LDFLAGS) -o $(BUILD_DIR)/migrate-linux ./cmd/migrate
	@echo "Linux版本构建完成"

.PHONY: run
run: ## 运行应用
	@echo "启动应用..."
	go run ./cmd/server

.PHONY: dev
dev: ## 开发模式运行（热重载）
	@echo "启动开发模式..."
	air

.PHONY: test
test: ## 运行测试
	@echo "运行测试..."
	go test -v -race -coverprofile=coverage.out ./...
	go tool cover -html=coverage.out -o coverage.html
	@echo "测试完成，覆盖率报告: coverage.html"

.PHONY: test-short
test-short: ## 运行快速测试
	@echo "运行快速测试..."
	go test -short ./...

.PHONY: bench
bench: ## 运行基准测试
	@echo "运行基准测试..."
	go test -bench=. -benchmem ./...

.PHONY: lint
lint: ## 代码检查
	@echo "运行代码检查..."
	golangci-lint run

.PHONY: fmt
fmt: ## 格式化代码
	@echo "格式化代码..."
	go fmt ./...
	goimports -w .

.PHONY: clean
clean: ## 清理构建文件
	@echo "清理构建文件..."
	rm -rf $(BUILD_DIR)
	rm -rf $(DIST_DIR)
	rm -f coverage.out coverage.html
	@echo "清理完成"

.PHONY: migrate-up
migrate-up: ## 执行数据库迁移
	@echo "执行数据库迁移..."
	go run ./cmd/migrate --action=up

.PHONY: migrate-down
migrate-down: ## 回滚数据库迁移
	@echo "回滚数据库迁移..."
	go run ./cmd/migrate --action=down

.PHONY: migrate-reset
migrate-reset: ## 重置数据库
	@echo "重置数据库..."
	go run ./cmd/migrate --action=reset

.PHONY: docker-build
docker-build: ## 构建Docker镜像
	@echo "构建Docker镜像..."
	docker build -t $(APP_NAME):$(VERSION) .
	docker tag $(APP_NAME):$(VERSION) $(APP_NAME):latest
	@echo "Docker镜像构建完成"

.PHONY: docker-run
docker-run: ## 运行Docker容器
	@echo "运行Docker容器..."
	docker run -d \
		--name $(APP_NAME) \
		-p 8080:8080 \
		-p 9090:9090 \
		-v $(PWD)/data:/app/data \
		-v $(PWD)/logs:/app/logs \
		--env-file .env \
		$(APP_NAME):latest

.PHONY: docker-stop
docker-stop: ## 停止Docker容器
	@echo "停止Docker容器..."
	docker stop $(APP_NAME) || true
	docker rm $(APP_NAME) || true

.PHONY: docker-compose-up
docker-compose-up: ## 启动Docker Compose
	@echo "启动Docker Compose..."
	docker-compose up -d

.PHONY: docker-compose-down
docker-compose-down: ## 停止Docker Compose
	@echo "停止Docker Compose..."
	docker-compose down

.PHONY: docker-compose-logs
docker-compose-logs: ## 查看Docker Compose日志
	docker-compose logs -f

.PHONY: install
install: build ## 安装到系统
	@echo "安装应用到系统..."
	sudo cp $(BUILD_DIR)/$(APP_NAME) /usr/local/bin/
	sudo cp $(BUILD_DIR)/migrate /usr/local/bin/$(APP_NAME)-migrate
	@echo "安装完成"

.PHONY: uninstall
uninstall: ## 从系统卸载
	@echo "从系统卸载..."
	sudo rm -f /usr/local/bin/$(APP_NAME)
	sudo rm -f /usr/local/bin/$(APP_NAME)-migrate
	@echo "卸载完成"

.PHONY: package
package: build-linux ## 打包发布版本
	@echo "打包发布版本..."
	@mkdir -p $(DIST_DIR)
	tar -czf $(DIST_DIR)/$(APP_NAME)-$(VERSION)-linux-amd64.tar.gz \
		-C $(BUILD_DIR) $(APP_NAME)-linux migrate-linux \
		-C .. configs/ web/ scripts/ README.md LICENSE
	@echo "打包完成: $(DIST_DIR)/$(APP_NAME)-$(VERSION)-linux-amd64.tar.gz"

.PHONY: release
release: clean test lint package ## 完整发布流程
	@echo "发布流程完成"

.PHONY: docs
docs: ## 生成API文档
	@echo "生成API文档..."
	swag init -g cmd/server/main.go
	@echo "API文档生成完成"

.PHONY: security-scan
security-scan: ## 安全扫描
	@echo "运行安全扫描..."
	go list -json -m all | nancy sleuth
	gosec ./...

.PHONY: init-env
init-env: ## 初始化环境
	@echo "初始化开发环境..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "已创建 .env 文件，请编辑配置"; \
	fi
	@mkdir -p data logs backups
	@echo "环境初始化完成"

.PHONY: health-check
health-check: ## 健康检查
	@echo "执行健康检查..."
	@curl -f http://localhost:8080/health || echo "服务未运行"

.PHONY: version
version: ## 显示版本信息
	@echo "应用名称: $(APP_NAME)"
	@echo "版本: $(VERSION)"
	@echo "构建时间: $(BUILD_TIME)"
	@echo "Git提交: $(GIT_COMMIT)"
	@echo "Go版本: $(shell go version)"

# 默认目标
.DEFAULT_GOAL := help
