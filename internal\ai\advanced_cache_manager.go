package ai

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// AdvancedCacheManager 高级缓存管理器
type AdvancedCacheManager struct {
	// 多层缓存
	l1Cache    *MemoryCache     // L1: 内存缓存 (最快)
	l2Cache    *PersistentCache // L2: 持久化缓存 (中等)
	l3Cache    *DistributedCache // L3: 分布式缓存 (最大)
	
	// 缓存策略
	strategies map[string]*CacheStrategy
	
	// 统计和监控
	stats   *CacheStatistics
	logger  *logrus.Logger
	config  *CacheConfig
	mutex   sync.RWMutex
}

// CacheConfig 缓存配置
type CacheConfig struct {
	L1MaxSize        int64         `json:"l1_max_size"`        // L1缓存最大大小 (字节)
	L1TTL            time.Duration `json:"l1_ttl"`             // L1缓存TTL
	L2MaxSize        int64         `json:"l2_max_size"`        // L2缓存最大大小
	L2TTL            time.Duration `json:"l2_ttl"`             // L2缓存TTL
	L3TTL            time.Duration `json:"l3_ttl"`             // L3缓存TTL
	EnableCompression bool         `json:"enable_compression"` // 启用压缩
	EnableEncryption  bool         `json:"enable_encryption"`  // 启用加密
	CleanupInterval   time.Duration `json:"cleanup_interval"`   // 清理间隔
	MaxKeyLength      int           `json:"max_key_length"`     // 最大键长度
}

// CacheStrategy 缓存策略
type CacheStrategy struct {
	Name            string        `json:"name"`
	TTL             time.Duration `json:"ttl"`
	MaxSize         int64         `json:"max_size"`
	EvictionPolicy  string        `json:"eviction_policy"`  // LRU, LFU, FIFO
	CompressionType string        `json:"compression_type"` // gzip, lz4, none
	Priority        int           `json:"priority"`         // 缓存优先级
	Tags            []string      `json:"tags"`             // 缓存标签
}

// CacheItem 缓存项
type CacheItem struct {
	Key         string                 `json:"key"`
	Value       interface{}            `json:"value"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	AccessedAt  time.Time              `json:"accessed_at"`
	ExpiresAt   time.Time              `json:"expires_at"`
	AccessCount int64                  `json:"access_count"`
	Size        int64                  `json:"size"`
	Compressed  bool                   `json:"compressed"`
	Encrypted   bool                   `json:"encrypted"`
	Tags        []string               `json:"tags"`
	Strategy    string                 `json:"strategy"`
}

// CacheStatistics 缓存统计
type CacheStatistics struct {
	L1Stats *LayerStats `json:"l1_stats"`
	L2Stats *LayerStats `json:"l2_stats"`
	L3Stats *LayerStats `json:"l3_stats"`
	
	TotalHits        int64   `json:"total_hits"`
	TotalMisses      int64   `json:"total_misses"`
	HitRatio         float64 `json:"hit_ratio"`
	AvgResponseTime  float64 `json:"avg_response_time_ms"`
	TotalOperations  int64   `json:"total_operations"`
	ErrorCount       int64   `json:"error_count"`
	
	StartTime        time.Time `json:"start_time"`
	LastResetTime    time.Time `json:"last_reset_time"`
}

// LayerStats 层级统计
type LayerStats struct {
	Hits            int64   `json:"hits"`
	Misses          int64   `json:"misses"`
	HitRatio        float64 `json:"hit_ratio"`
	Size            int64   `json:"size"`
	ItemCount       int64   `json:"item_count"`
	AvgResponseTime float64 `json:"avg_response_time_ms"`
	Evictions       int64   `json:"evictions"`
	Errors          int64   `json:"errors"`
}

// NewAdvancedCacheManager 创建高级缓存管理器
func NewAdvancedCacheManager(config *CacheConfig, logger *logrus.Logger) *AdvancedCacheManager {
	if config == nil {
		config = &CacheConfig{
			L1MaxSize:        100 * 1024 * 1024, // 100MB
			L1TTL:            5 * time.Minute,
			L2MaxSize:        1024 * 1024 * 1024, // 1GB
			L2TTL:            1 * time.Hour,
			L3TTL:            24 * time.Hour,
			EnableCompression: true,
			EnableEncryption:  false,
			CleanupInterval:   10 * time.Minute,
			MaxKeyLength:      256,
		}
	}

	acm := &AdvancedCacheManager{
		l1Cache:    NewMemoryCache(config.L1MaxSize, config.L1TTL),
		l2Cache:    NewPersistentCache(config.L2MaxSize, config.L2TTL),
		l3Cache:    NewDistributedCache(config.L3TTL),
		strategies: make(map[string]*CacheStrategy),
		stats:      NewCacheStatistics(),
		logger:     logger,
		config:     config,
	}

	// 初始化默认策略
	acm.initializeDefaultStrategies()

	// 启动后台清理任务
	go acm.startCleanupWorker()

	return acm
}

// Get 获取缓存项 (智能多层查找)
func (acm *AdvancedCacheManager) Get(ctx context.Context, key string) (*CacheItem, error) {
	start := time.Now()
	defer func() {
		acm.updateResponseTime(time.Since(start))
	}()

	// 验证键
	if err := acm.validateKey(key); err != nil {
		acm.stats.ErrorCount++
		return nil, err
	}

	// L1缓存查找
	if item, found := acm.l1Cache.Get(key); found {
		acm.stats.L1Stats.Hits++
		acm.stats.TotalHits++
		acm.updateItemAccess(item)
		return item, nil
	}
	acm.stats.L1Stats.Misses++

	// L2缓存查找
	if item, found := acm.l2Cache.Get(key); found {
		acm.stats.L2Stats.Hits++
		acm.stats.TotalHits++
		
		// 提升到L1缓存
		acm.l1Cache.Set(key, item)
		acm.updateItemAccess(item)
		return item, nil
	}
	acm.stats.L2Stats.Misses++

	// L3缓存查找
	if item, found := acm.l3Cache.Get(ctx, key); found {
		acm.stats.L3Stats.Hits++
		acm.stats.TotalHits++
		
		// 提升到L2和L1缓存
		acm.l2Cache.Set(key, item)
		acm.l1Cache.Set(key, item)
		acm.updateItemAccess(item)
		return item, nil
	}
	acm.stats.L3Stats.Misses++
	acm.stats.TotalMisses++

	return nil, fmt.Errorf("cache miss: key not found")
}

// Set 设置缓存项 (智能多层存储)
func (acm *AdvancedCacheManager) Set(ctx context.Context, key string, value interface{}, strategyName string) error {
	start := time.Now()
	defer func() {
		acm.updateResponseTime(time.Since(start))
	}()

	// 验证键
	if err := acm.validateKey(key); err != nil {
		acm.stats.ErrorCount++
		return err
	}

	// 获取缓存策略
	strategy := acm.getStrategy(strategyName)
	
	// 创建缓存项
	item := &CacheItem{
		Key:         key,
		Value:       value,
		Metadata:    make(map[string]interface{}),
		CreatedAt:   time.Now(),
		AccessedAt:  time.Now(),
		ExpiresAt:   time.Now().Add(strategy.TTL),
		AccessCount: 0,
		Strategy:    strategyName,
		Tags:        strategy.Tags,
	}

	// 计算大小
	item.Size = acm.calculateItemSize(item)

	// 应用压缩
	if acm.config.EnableCompression && strategy.CompressionType != "none" {
		if err := acm.compressItem(item, strategy.CompressionType); err != nil {
			acm.logger.WithError(err).Warn("Failed to compress cache item")
		}
	}

	// 应用加密
	if acm.config.EnableEncryption {
		if err := acm.encryptItem(item); err != nil {
			acm.logger.WithError(err).Warn("Failed to encrypt cache item")
		}
	}

	// 存储到各层缓存
	acm.l1Cache.Set(key, item)
	acm.l2Cache.Set(key, item)
	
	if err := acm.l3Cache.Set(ctx, key, item); err != nil {
		acm.logger.WithError(err).Warn("Failed to set L3 cache")
	}

	acm.stats.TotalOperations++
	return nil
}

// Delete 删除缓存项
func (acm *AdvancedCacheManager) Delete(ctx context.Context, key string) error {
	acm.l1Cache.Delete(key)
	acm.l2Cache.Delete(key)
	acm.l3Cache.Delete(ctx, key)
	
	acm.stats.TotalOperations++
	return nil
}

// DeleteByTags 根据标签删除缓存项
func (acm *AdvancedCacheManager) DeleteByTags(ctx context.Context, tags []string) error {
	// 实现标签删除逻辑
	deletedCount := 0
	
	// L1缓存标签删除
	deletedCount += acm.l1Cache.DeleteByTags(tags)
	
	// L2缓存标签删除
	deletedCount += acm.l2Cache.DeleteByTags(tags)
	
	// L3缓存标签删除
	if err := acm.l3Cache.DeleteByTags(ctx, tags); err != nil {
		acm.logger.WithError(err).Warn("Failed to delete L3 cache by tags")
	}

	acm.logger.WithFields(logrus.Fields{
		"tags":          tags,
		"deleted_count": deletedCount,
	}).Info("Cache items deleted by tags")

	return nil
}

// GetStatistics 获取缓存统计
func (acm *AdvancedCacheManager) GetStatistics() *CacheStatistics {
	acm.mutex.RLock()
	defer acm.mutex.RUnlock()

	// 更新命中率
	if acm.stats.TotalOperations > 0 {
		acm.stats.HitRatio = float64(acm.stats.TotalHits) / float64(acm.stats.TotalHits+acm.stats.TotalMisses)
	}

	// 更新各层统计
	acm.updateLayerStats()

	return acm.stats
}

// Warm 预热缓存
func (acm *AdvancedCacheManager) Warm(ctx context.Context, warmupData map[string]interface{}) error {
	acm.logger.Info("Starting cache warmup")
	
	for key, value := range warmupData {
		if err := acm.Set(ctx, key, value, "default"); err != nil {
			acm.logger.WithError(err).WithField("key", key).Warn("Failed to warm cache item")
		}
	}

	acm.logger.WithField("items", len(warmupData)).Info("Cache warmup completed")
	return nil
}

// 私有方法

func (acm *AdvancedCacheManager) initializeDefaultStrategies() {
	// DeepSeek响应缓存策略
	acm.strategies["deepseek_response"] = &CacheStrategy{
		Name:            "deepseek_response",
		TTL:             10 * time.Minute,
		MaxSize:         50 * 1024 * 1024, // 50MB
		EvictionPolicy:  "LRU",
		CompressionType: "gzip",
		Priority:        1,
		Tags:            []string{"ai", "deepseek"},
	}

	// Agent能力描述缓存策略
	acm.strategies["agent_capabilities"] = &CacheStrategy{
		Name:            "agent_capabilities",
		TTL:             1 * time.Hour,
		MaxSize:         10 * 1024 * 1024, // 10MB
		EvictionPolicy:  "LFU",
		CompressionType: "lz4",
		Priority:        2,
		Tags:            []string{"agent", "metadata"},
	}

	// 执行结果缓存策略
	acm.strategies["execution_result"] = &CacheStrategy{
		Name:            "execution_result",
		TTL:             5 * time.Minute,
		MaxSize:         20 * 1024 * 1024, // 20MB
		EvictionPolicy:  "LRU",
		CompressionType: "gzip",
		Priority:        3,
		Tags:            []string{"execution", "result"},
	}

	// 默认策略
	acm.strategies["default"] = &CacheStrategy{
		Name:            "default",
		TTL:             15 * time.Minute,
		MaxSize:         30 * 1024 * 1024, // 30MB
		EvictionPolicy:  "LRU",
		CompressionType: "none",
		Priority:        5,
		Tags:            []string{"default"},
	}
}

func (acm *AdvancedCacheManager) getStrategy(name string) *CacheStrategy {
	if strategy, exists := acm.strategies[name]; exists {
		return strategy
	}
	return acm.strategies["default"]
}

func (acm *AdvancedCacheManager) validateKey(key string) error {
	if len(key) == 0 {
		return fmt.Errorf("cache key cannot be empty")
	}
	if len(key) > acm.config.MaxKeyLength {
		return fmt.Errorf("cache key too long: %d > %d", len(key), acm.config.MaxKeyLength)
	}
	return nil
}

func (acm *AdvancedCacheManager) calculateItemSize(item *CacheItem) int64 {
	// 简化的大小计算
	data, _ := json.Marshal(item.Value)
	return int64(len(data))
}

func (acm *AdvancedCacheManager) compressItem(item *CacheItem, compressionType string) error {
	// 实现压缩逻辑
	item.Compressed = true
	return nil
}

func (acm *AdvancedCacheManager) encryptItem(item *CacheItem) error {
	// 实现加密逻辑
	item.Encrypted = true
	return nil
}

func (acm *AdvancedCacheManager) updateItemAccess(item *CacheItem) {
	item.AccessedAt = time.Now()
	item.AccessCount++
}

func (acm *AdvancedCacheManager) updateResponseTime(duration time.Duration) {
	acm.mutex.Lock()
	defer acm.mutex.Unlock()
	
	// 简化的响应时间更新
	acm.stats.AvgResponseTime = float64(duration.Nanoseconds()) / 1000000.0 // 转换为毫秒
}

func (acm *AdvancedCacheManager) updateLayerStats() {
	// 更新各层统计信息
	acm.stats.L1Stats = acm.l1Cache.GetStats()
	acm.stats.L2Stats = acm.l2Cache.GetStats()
	acm.stats.L3Stats = acm.l3Cache.GetStats()
}

func (acm *AdvancedCacheManager) startCleanupWorker() {
	ticker := time.NewTicker(acm.config.CleanupInterval)
	defer ticker.Stop()

	for range ticker.C {
		acm.performCleanup()
	}
}

func (acm *AdvancedCacheManager) performCleanup() {
	acm.logger.Debug("Performing cache cleanup")
	
	// 清理过期项
	acm.l1Cache.Cleanup()
	acm.l2Cache.Cleanup()
	
	// 记录清理统计
	acm.logger.Debug("Cache cleanup completed")
}

// GenerateCacheKey 生成缓存键
func (acm *AdvancedCacheManager) GenerateCacheKey(prefix string, params ...interface{}) string {
	hasher := md5.New()
	hasher.Write([]byte(prefix))
	
	for _, param := range params {
		data, _ := json.Marshal(param)
		hasher.Write(data)
	}
	
	return prefix + ":" + hex.EncodeToString(hasher.Sum(nil))
}

// 辅助类型和函数

func NewCacheStatistics() *CacheStatistics {
	return &CacheStatistics{
		L1Stats:       &LayerStats{},
		L2Stats:       &LayerStats{},
		L3Stats:       &LayerStats{},
		StartTime:     time.Now(),
		LastResetTime: time.Now(),
	}
}
