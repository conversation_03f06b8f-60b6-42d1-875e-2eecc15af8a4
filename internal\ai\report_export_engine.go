package ai

import (
	"bytes"
	"context"
	"encoding/csv"
	"encoding/json"
	"fmt"
	"html/template"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// 🚀 报表导出引擎 - 支持多种格式导出
type ReportExportEngine struct {
	logger *logrus.Logger
	
	// 导出器映射
	exporters map[string]ReportExporter
}

// ReportExporter 报表导出器接口
type ReportExporter interface {
	Export(ctx context.Context, data *ExportData) (*ExportResult, error)
	GetMimeType() string
	GetFileExtension() string
}

// ExportData 导出数据
type ExportData struct {
	ReportID     string                 `json:"report_id"`
	Title        string                 `json:"title"`
	ReportType   string                 `json:"report_type"`
	Data         interface{}            `json:"data"`
	Summary      *ReportSummary         `json:"summary"`
	Insights     []string               `json:"insights"`
	Charts       []ReportChart          `json:"charts"`
	GeneratedAt  time.Time              `json:"generated_at"`
	TimeRange    string                 `json:"time_range"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// ExportResult 导出结果
type ExportResult struct {
	Content     []byte    `json:"content"`
	MimeType    string    `json:"mime_type"`
	Extension   string    `json:"extension"`
	Size        int       `json:"size"`
	ExportedAt  time.Time `json:"exported_at"`
	Filename    string    `json:"filename"`
}

// NewReportExportEngine 创建报表导出引擎
func NewReportExportEngine(logger *logrus.Logger) *ReportExportEngine {
	engine := &ReportExportEngine{
		logger:    logger,
		exporters: make(map[string]ReportExporter),
	}
	
	// 注册导出器
	engine.registerExporters()
	
	logger.Info("🚀 报表导出引擎初始化完成")
	return engine
}

// registerExporters 注册导出器
func (ree *ReportExportEngine) registerExporters() {
	ree.exporters["json"] = &JSONExporter{logger: ree.logger}
	ree.exporters["csv"] = &CSVExporter{logger: ree.logger}
	ree.exporters["html"] = &HTMLExporter{logger: ree.logger}
	ree.exporters["pdf"] = &PDFExporter{logger: ree.logger}
}

// Export 导出报表
func (ree *ReportExportEngine) Export(ctx context.Context, format string, data *ExportData) (*ExportResult, error) {
	start := time.Now()
	
	ree.logger.WithFields(logrus.Fields{
		"format":     format,
		"report_id":  data.ReportID,
		"report_type": data.ReportType,
	}).Info("🚀 开始导出报表")
	
	exporter, exists := ree.exporters[format]
	if !exists {
		return nil, fmt.Errorf("不支持的导出格式: %s", format)
	}
	
	result, err := exporter.Export(ctx, data)
	if err != nil {
		return nil, fmt.Errorf("导出失败: %w", err)
	}
	
	// 生成文件名
	timestamp := time.Now().Format("20060102_150405")
	result.Filename = fmt.Sprintf("%s_%s_%s.%s", 
		data.ReportType, data.TimeRange, timestamp, result.Extension)
	
	ree.logger.WithFields(logrus.Fields{
		"format":       format,
		"size":         result.Size,
		"filename":     result.Filename,
		"export_time":  time.Since(start),
	}).Info("🚀 报表导出完成")
	
	return result, nil
}

// JSONExporter JSON导出器
type JSONExporter struct {
	logger *logrus.Logger
}

func (je *JSONExporter) Export(ctx context.Context, data *ExportData) (*ExportResult, error) {
	content, err := json.MarshalIndent(data, "", "  ")
	if err != nil {
		return nil, fmt.Errorf("JSON序列化失败: %w", err)
	}
	
	return &ExportResult{
		Content:    content,
		MimeType:   je.GetMimeType(),
		Extension:  je.GetFileExtension(),
		Size:       len(content),
		ExportedAt: time.Now(),
	}, nil
}

func (je *JSONExporter) GetMimeType() string {
	return "application/json"
}

func (je *JSONExporter) GetFileExtension() string {
	return "json"
}

// CSVExporter CSV导出器
type CSVExporter struct {
	logger *logrus.Logger
}

func (ce *CSVExporter) Export(ctx context.Context, data *ExportData) (*ExportResult, error) {
	var buffer bytes.Buffer
	writer := csv.NewWriter(&buffer)
	
	// 写入标题行
	writer.Write([]string{"报表信息", "值"})
	writer.Write([]string{"报表ID", data.ReportID})
	writer.Write([]string{"报表标题", data.Title})
	writer.Write([]string{"报表类型", data.ReportType})
	writer.Write([]string{"时间范围", data.TimeRange})
	writer.Write([]string{"生成时间", data.GeneratedAt.Format("2006-01-02 15:04:05")})
	writer.Write([]string{""}) // 空行
	
	// 写入数据
	if dataMap, ok := data.Data.(map[string]interface{}); ok {
		writer.Write([]string{"数据项", "数值"})
		for key, value := range dataMap {
			writer.Write([]string{key, fmt.Sprintf("%v", value)})
		}
	}
	
	// 写入洞察
	if len(data.Insights) > 0 {
		writer.Write([]string{""}) // 空行
		writer.Write([]string{"AI洞察"})
		for i, insight := range data.Insights {
			writer.Write([]string{fmt.Sprintf("洞察%d", i+1), insight})
		}
	}
	
	writer.Flush()
	content := buffer.Bytes()
	
	return &ExportResult{
		Content:    content,
		MimeType:   ce.GetMimeType(),
		Extension:  ce.GetFileExtension(),
		Size:       len(content),
		ExportedAt: time.Now(),
	}, nil
}

func (ce *CSVExporter) GetMimeType() string {
	return "text/csv"
}

func (ce *CSVExporter) GetFileExtension() string {
	return "csv"
}

// HTMLExporter HTML导出器
type HTMLExporter struct {
	logger *logrus.Logger
}

func (he *HTMLExporter) Export(ctx context.Context, data *ExportData) (*ExportResult, error) {
	tmpl := `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{.Title}}</title>
    <style>
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; margin: 20px; }
        .header { background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; }
        .section { margin-bottom: 30px; }
        .data-table { width: 100%; border-collapse: collapse; }
        .data-table th, .data-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        .data-table th { background-color: #f2f2f2; }
        .insights { background: #e7f3ff; padding: 15px; border-radius: 5px; }
        .footer { margin-top: 30px; text-align: center; color: #666; }
    </style>
</head>
<body>
    <div class="header">
        <h1>{{.Title}}</h1>
        <p><strong>报表ID:</strong> {{.ReportID}}</p>
        <p><strong>报表类型:</strong> {{.ReportType}}</p>
        <p><strong>时间范围:</strong> {{.TimeRange}}</p>
        <p><strong>生成时间:</strong> {{.GeneratedAt.Format "2006-01-02 15:04:05"}}</p>
    </div>
    
    <div class="section">
        <h2>📊 数据概览</h2>
        <table class="data-table">
            <thead>
                <tr><th>数据项</th><th>数值</th></tr>
            </thead>
            <tbody>
                {{range $key, $value := .DataMap}}
                <tr><td>{{$key}}</td><td>{{$value}}</td></tr>
                {{end}}
            </tbody>
        </table>
    </div>
    
    {{if .Insights}}
    <div class="section">
        <h2>💡 AI洞察</h2>
        <div class="insights">
            {{range $index, $insight := .Insights}}
            <p><strong>{{add $index 1}}.</strong> {{$insight}}</p>
            {{end}}
        </div>
    </div>
    {{end}}
    
    <div class="footer">
        <p>本报表由AI运维管理平台自动生成</p>
    </div>
</body>
</html>`

	// 准备模板数据
	templateData := struct {
		*ExportData
		DataMap map[string]interface{}
	}{
		ExportData: data,
		DataMap:    make(map[string]interface{}),
	}
	
	// 转换数据为map
	if dataMap, ok := data.Data.(map[string]interface{}); ok {
		templateData.DataMap = dataMap
	}
	
	// 创建模板函数
	funcMap := template.FuncMap{
		"add": func(a, b int) int { return a + b },
	}
	
	t, err := template.New("report").Funcs(funcMap).Parse(tmpl)
	if err != nil {
		return nil, fmt.Errorf("模板解析失败: %w", err)
	}
	
	var buffer bytes.Buffer
	if err := t.Execute(&buffer, templateData); err != nil {
		return nil, fmt.Errorf("模板执行失败: %w", err)
	}
	
	content := buffer.Bytes()
	
	return &ExportResult{
		Content:    content,
		MimeType:   he.GetMimeType(),
		Extension:  he.GetFileExtension(),
		Size:       len(content),
		ExportedAt: time.Now(),
	}, nil
}

func (he *HTMLExporter) GetMimeType() string {
	return "text/html"
}

func (he *HTMLExporter) GetFileExtension() string {
	return "html"
}

// PDFExporter PDF导出器（简化实现）
type PDFExporter struct {
	logger *logrus.Logger
}

func (pe *PDFExporter) Export(ctx context.Context, data *ExportData) (*ExportResult, error) {
	// 简化实现：生成PDF格式的文本内容
	var buffer strings.Builder
	
	buffer.WriteString(fmt.Sprintf("PDF报表: %s\n", data.Title))
	buffer.WriteString(fmt.Sprintf("报表ID: %s\n", data.ReportID))
	buffer.WriteString(fmt.Sprintf("报表类型: %s\n", data.ReportType))
	buffer.WriteString(fmt.Sprintf("时间范围: %s\n", data.TimeRange))
	buffer.WriteString(fmt.Sprintf("生成时间: %s\n\n", data.GeneratedAt.Format("2006-01-02 15:04:05")))
	
	// 添加数据
	if dataMap, ok := data.Data.(map[string]interface{}); ok {
		buffer.WriteString("数据概览:\n")
		for key, value := range dataMap {
			buffer.WriteString(fmt.Sprintf("- %s: %v\n", key, value))
		}
		buffer.WriteString("\n")
	}
	
	// 添加洞察
	if len(data.Insights) > 0 {
		buffer.WriteString("AI洞察:\n")
		for i, insight := range data.Insights {
			buffer.WriteString(fmt.Sprintf("%d. %s\n", i+1, insight))
		}
	}
	
	buffer.WriteString("\n本报表由AI运维管理平台自动生成")
	
	content := []byte(buffer.String())
	
	return &ExportResult{
		Content:    content,
		MimeType:   pe.GetMimeType(),
		Extension:  pe.GetFileExtension(),
		Size:       len(content),
		ExportedAt: time.Now(),
	}, nil
}

func (pe *PDFExporter) GetMimeType() string {
	return "application/pdf"
}

func (pe *PDFExporter) GetFileExtension() string {
	return "pdf"
}

// GetSupportedFormats 获取支持的导出格式
func (ree *ReportExportEngine) GetSupportedFormats() []string {
	formats := make([]string, 0, len(ree.exporters))
	for format := range ree.exporters {
		formats = append(formats, format)
	}
	return formats
}
