package service

import (
	"context"
	"regexp"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// SecurityValidator 安全验证器
type SecurityValidator struct {
	db               *gorm.DB
	logger           *logrus.Logger
	riskPatterns     map[string]*RiskPattern
	commandBlacklist []string
	userPermissions  map[int64][]string
	rateLimiter      *RateLimiter
}

// RiskPattern 风险模式
type RiskPattern struct {
	Name        string   `json:"name"`
	Pattern     string   `json:"pattern"`
	RiskLevel   string   `json:"risk_level"`
	Description string   `json:"description"`
	Keywords    []string `json:"keywords"`
	Mitigation  string   `json:"mitigation"`
}

// SecurityValidationRequest 安全验证请求
type SecurityValidationRequest struct {
	UserID     int64                  `json:"user_id"`
	Command    string                 `json:"command"`
	ActionType string                 `json:"action_type"`
	HostID     *int64                 `json:"host_id"`
	Parameters map[string]interface{} `json:"parameters"`
	SessionID  string                 `json:"session_id"`
	ClientIP   string                 `json:"client_ip"`
	UserAgent  string                 `json:"user_agent"`
}

// SecurityValidationResult 安全验证结果
type SecurityValidationResult struct {
	Allowed      bool          `json:"allowed"`
	RiskLevel    string        `json:"risk_level"`
	RiskScore    float64       `json:"risk_score"`
	Warnings     []string      `json:"warnings"`
	Requirements []string      `json:"requirements"`
	Reason       string        `json:"reason"`
	ValidatedAt  time.Time     `json:"validated_at"`
	RequiresAuth bool          `json:"requires_auth"`
	RequiresMFA  bool          `json:"requires_mfa"`
	MaxRetries   int           `json:"max_retries"`
	CooldownTime time.Duration `json:"cooldown_time"`
}

// RateLimiter 速率限制器
type RateLimiter struct {
	userLimits  map[int64]*UserLimit
	globalLimit *GlobalLimit
}

// UserLimit 用户限制
type UserLimit struct {
	UserID       int64         `json:"user_id"`
	RequestCount int           `json:"request_count"`
	LastRequest  time.Time     `json:"last_request"`
	WindowStart  time.Time     `json:"window_start"`
	MaxRequests  int           `json:"max_requests"`
	WindowSize   time.Duration `json:"window_size"`
}

// GlobalLimit 全局限制
type GlobalLimit struct {
	RequestCount int           `json:"request_count"`
	LastRequest  time.Time     `json:"last_request"`
	WindowStart  time.Time     `json:"window_start"`
	MaxRequests  int           `json:"max_requests"`
	WindowSize   time.Duration `json:"window_size"`
}

// NewSecurityValidator 创建安全验证器
func NewSecurityValidator(db *gorm.DB, logger *logrus.Logger) *SecurityValidator {
	sv := &SecurityValidator{
		db:               db,
		logger:           logger,
		riskPatterns:     make(map[string]*RiskPattern),
		commandBlacklist: make([]string, 0),
		userPermissions:  make(map[int64][]string),
		rateLimiter: &RateLimiter{
			userLimits: make(map[int64]*UserLimit),
			globalLimit: &GlobalLimit{
				MaxRequests: 1000,
				WindowSize:  time.Hour,
			},
		},
	}

	// 初始化风险模式和黑名单
	sv.initializeRiskPatterns()
	sv.initializeCommandBlacklist()

	return sv
}

// ValidateAction 验证操作安全性
func (sv *SecurityValidator) ValidateAction(ctx context.Context, req *SecurityValidationRequest) (*SecurityValidationResult, error) {
	sv.logger.WithFields(logrus.Fields{
		"user_id":     req.UserID,
		"command":     req.Command,
		"action_type": req.ActionType,
		"session_id":  req.SessionID,
	}).Info("Starting security validation")

	result := &SecurityValidationResult{
		Allowed:      true,
		RiskLevel:    "low",
		RiskScore:    0.0,
		Warnings:     make([]string, 0),
		Requirements: make([]string, 0),
		ValidatedAt:  time.Now(),
	}

	// 1. 速率限制检查
	if !sv.checkRateLimit(req.UserID) {
		result.Allowed = false
		result.Reason = "Rate limit exceeded"
		result.CooldownTime = time.Minute * 5
		return result, nil
	}

	// 2. 命令黑名单检查
	if sv.isCommandBlacklisted(req.Command) {
		result.Allowed = false
		result.Reason = "Command is blacklisted"
		result.RiskLevel = "critical"
		return result, nil
	}

	// 3. 风险模式匹配
	riskLevel, riskScore, warnings := sv.analyzeCommandRisk(req.Command)
	result.RiskLevel = riskLevel
	result.RiskScore = riskScore
	result.Warnings = warnings

	// 4. 权限检查
	if !sv.checkUserPermissions(req.UserID, req.ActionType, req.HostID) {
		result.Allowed = false
		result.Reason = "Insufficient permissions"
		return result, nil
	}

	// 5. 根据风险级别设置要求
	switch riskLevel {
	case "critical":
		result.RequiresAuth = true
		result.RequiresMFA = true
		result.MaxRetries = 1
		result.Requirements = append(result.Requirements, "需要管理员确认", "需要多因子认证")
	case "high":
		result.RequiresAuth = true
		result.MaxRetries = 2
		result.Requirements = append(result.Requirements, "需要用户确认")
	case "medium":
		result.MaxRetries = 3
		result.Requirements = append(result.Requirements, "建议谨慎执行")
	}

	// 6. 记录验证日志
	sv.logValidationResult(req, result)

	return result, nil
}

// checkRateLimit 检查速率限制
func (sv *SecurityValidator) checkRateLimit(userID int64) bool {
	now := time.Now()

	// 检查用户级别限制
	userLimit, exists := sv.rateLimiter.userLimits[userID]
	if !exists {
		userLimit = &UserLimit{
			UserID:      userID,
			MaxRequests: 100,
			WindowSize:  time.Hour,
			WindowStart: now,
		}
		sv.rateLimiter.userLimits[userID] = userLimit
	}

	// 重置窗口
	if now.Sub(userLimit.WindowStart) > userLimit.WindowSize {
		userLimit.RequestCount = 0
		userLimit.WindowStart = now
	}

	// 检查是否超限
	if userLimit.RequestCount >= userLimit.MaxRequests {
		return false
	}

	userLimit.RequestCount++
	userLimit.LastRequest = now

	return true
}

// isCommandBlacklisted 检查命令是否在黑名单中
func (sv *SecurityValidator) isCommandBlacklisted(command string) bool {
	command = strings.ToLower(strings.TrimSpace(command))

	for _, blacklisted := range sv.commandBlacklist {
		if strings.Contains(command, blacklisted) {
			return true
		}
	}

	return false
}

// analyzeCommandRisk 分析命令风险
func (sv *SecurityValidator) analyzeCommandRisk(command string) (string, float64, []string) {
	var maxRiskScore float64 = 0.0
	var riskLevel string = "low"
	var warnings []string

	command = strings.ToLower(command)

	for _, pattern := range sv.riskPatterns {
		if matched, _ := regexp.MatchString(pattern.Pattern, command); matched {
			score := sv.calculateRiskScore(pattern)
			if score > maxRiskScore {
				maxRiskScore = score
				riskLevel = pattern.RiskLevel
			}
			warnings = append(warnings, pattern.Description)
		}
	}

	return riskLevel, maxRiskScore, warnings
}

// calculateRiskScore 计算风险分数
func (sv *SecurityValidator) calculateRiskScore(pattern *RiskPattern) float64 {
	switch pattern.RiskLevel {
	case "critical":
		return 1.0
	case "high":
		return 0.8
	case "medium":
		return 0.5
	case "low":
		return 0.2
	default:
		return 0.1
	}
}

// checkUserPermissions 检查用户权限
func (sv *SecurityValidator) checkUserPermissions(userID int64, actionType string, hostID *int64) bool {
	// 简化的权限检查逻辑
	// 实际应该从数据库查询用户权限
	permissions, exists := sv.userPermissions[userID]
	if !exists {
		// 默认给予基础权限
		permissions = []string{"monitoring", "log_analysis"}
		sv.userPermissions[userID] = permissions
	}

	// 检查是否有对应的操作权限
	for _, permission := range permissions {
		if permission == actionType || permission == "admin" {
			return true
		}
	}

	return false
}

// logValidationResult 记录验证结果
func (sv *SecurityValidator) logValidationResult(req *SecurityValidationRequest, result *SecurityValidationResult) {
	// 创建安全日志记录
	securityLog := &model.SecurityLog{
		UserID:      req.UserID,
		Action:      req.ActionType,
		Command:     req.Command,
		HostID:      req.HostID,
		SessionID:   req.SessionID,
		ClientIP:    req.ClientIP,
		UserAgent:   req.UserAgent,
		RiskLevel:   result.RiskLevel,
		RiskScore:   result.RiskScore,
		Allowed:     result.Allowed,
		Reason:      result.Reason,
		ValidatedAt: result.ValidatedAt,
	}

	if err := sv.db.Create(securityLog).Error; err != nil {
		sv.logger.WithError(err).Error("Failed to create security log")
	}
}

// initializeRiskPatterns 初始化风险模式
func (sv *SecurityValidator) initializeRiskPatterns() {
	patterns := []*RiskPattern{
		{
			Name:        "system_shutdown",
			Pattern:     `(?i)(shutdown|halt|poweroff|reboot)`,
			RiskLevel:   "critical",
			Description: "系统关机/重启操作，可能导致服务中断",
			Keywords:    []string{"shutdown", "halt", "poweroff", "reboot"},
			Mitigation:  "确认无重要业务运行，建议在维护窗口执行",
		},
		{
			Name:        "file_deletion",
			Pattern:     `(?i)(rm\s+-rf|del\s+/s|rmdir\s+/s)`,
			RiskLevel:   "high",
			Description: "批量文件删除操作，可能导致数据丢失",
			Keywords:    []string{"rm -rf", "del /s", "rmdir /s"},
			Mitigation:  "确认删除路径正确，建议先备份",
		},
		{
			Name:        "service_stop",
			Pattern:     `(?i)(systemctl\s+stop|service\s+.*\s+stop)`,
			RiskLevel:   "medium",
			Description: "服务停止操作，可能影响业务功能",
			Keywords:    []string{"systemctl stop", "service stop"},
			Mitigation:  "确认服务停止不会影响关键业务",
		},
		{
			Name:        "network_config",
			Pattern:     `(?i)(iptables|firewall|netsh|ifconfig)`,
			RiskLevel:   "high",
			Description: "网络配置修改，可能导致连接中断",
			Keywords:    []string{"iptables", "firewall", "netsh", "ifconfig"},
			Mitigation:  "确保有备用连接方式，建议在本地执行",
		},
	}

	for _, pattern := range patterns {
		sv.riskPatterns[pattern.Name] = pattern
	}
}

// initializeCommandBlacklist 初始化命令黑名单
func (sv *SecurityValidator) initializeCommandBlacklist() {
	sv.commandBlacklist = []string{
		"format",
		"fdisk",
		"mkfs",
		"dd if=/dev/zero",
		":(){ :|:& };:", // fork bomb
		"chmod 777 /",
		"chown -R root:root /",
	}
}
