package service

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"
)

// DatabaseResultProcessor 数据库结果处理器
type DatabaseResultProcessor struct {
	logger *logrus.Logger
}

// NewDatabaseResultProcessor 创建数据库结果处理器
func NewDatabaseResultProcessor(logger *logrus.Logger) *DatabaseResultProcessor {
	return &DatabaseResultProcessor{
		logger: logger,
	}
}

// ProcessResult 处理数据库操作结果
func (p *DatabaseResultProcessor) ProcessResult(ctx context.Context, execResult *SQLExecutionResult, request *DatabaseOperationRequest) (*DatabaseOperationResult, error) {
	if !execResult.Success {
		return &DatabaseOperationResult{
			Success: false,
			Message: fmt.Sprintf("数据库操作失败: %s", execResult.Error),
		}, nil
	}

	result := &DatabaseOperationResult{
		Success:       true,
		RowsAffected:  execResult.RowsAffected,
		ExecutionTime: execResult.ExecutionTime,
	}

	// 根据操作类型处理结果
	switch request.OperationType {
	case "select":
		p.processSelectResult(execResult, request, result)
	case "insert":
		p.processInsertResult(execResult, request, result)
	case "update":
		p.processUpdateResult(execResult, request, result)
	case "delete":
		p.processDeleteResult(execResult, request, result)
	case "describe":
		p.processDescribeResult(execResult, request, result)
	}

	// 生成撤销建议
	p.generateUndoSuggestion(request, result)

	p.logger.WithFields(logrus.Fields{
		"operation_type": request.OperationType,
		"table_name":     request.TableName,
		"rows_affected":  result.RowsAffected,
		"execution_time": result.ExecutionTime,
	}).Info("Database operation result processed")

	return result, nil
}

// processSelectResult 处理查询结果
func (p *DatabaseResultProcessor) processSelectResult(execResult *SQLExecutionResult, request *DatabaseOperationRequest, result *DatabaseOperationResult) {
	data, ok := execResult.Data.([]map[string]interface{})
	if !ok {
		result.Message = "查询完成，但结果格式异常"
		return
	}

	result.Data = data

	if len(data) == 0 {
		result.Message = "查询完成，未找到匹配的记录"
	} else if len(data) == 1 {
		result.Message = "查询完成，找到1条记录"
	} else {
		result.Message = fmt.Sprintf("查询完成，找到%d条记录", len(data))
	}

	// 添加查询统计信息
	if len(data) > 0 {
		result.Data = map[string]interface{}{
			"records": data,
			"summary": map[string]interface{}{
				"total_records": len(data),
				"table_name":    request.TableName,
				"query_time":    result.ExecutionTime,
			},
		}
	}
}

// processInsertResult 处理插入结果
func (p *DatabaseResultProcessor) processInsertResult(execResult *SQLExecutionResult, request *DatabaseOperationRequest, result *DatabaseOperationResult) {
	if result.RowsAffected > 0 {
		result.Message = fmt.Sprintf("成功插入%d条记录到表 %s", result.RowsAffected, request.TableName)

		// 提供查看新记录的建议
		if len(request.Data) > 0 {
			// 尝试构建查看新记录的条件
			var keyField string
			var keyValue interface{}

			// 优先使用name字段
			if name, exists := request.Data["name"]; exists {
				keyField = "name"
				keyValue = name
			} else if id, exists := request.Data["id"]; exists {
				keyField = "id"
				keyValue = id
			}

			if keyField != "" {
				result.Data = map[string]interface{}{
					"inserted_record_hint": map[string]interface{}{
						"field": keyField,
						"value": keyValue,
					},
					"view_suggestion": fmt.Sprintf("您可以说'查看%s为%v的%s记录'来查看新插入的记录", keyField, keyValue, request.TableName),
				}
			}
		}
	} else {
		result.Message = "插入操作完成，但没有记录被插入"
	}
}

// processUpdateResult 处理更新结果
func (p *DatabaseResultProcessor) processUpdateResult(execResult *SQLExecutionResult, request *DatabaseOperationRequest, result *DatabaseOperationResult) {
	if result.RowsAffected > 0 {
		result.Message = fmt.Sprintf("成功更新表 %s 中的%d条记录", request.TableName, result.RowsAffected)

		// 提供更新详情
		updatedFields := make([]string, 0, len(request.Data))
		for field := range request.Data {
			updatedFields = append(updatedFields, field)
		}

		result.Data = map[string]interface{}{
			"updated_fields":    updatedFields,
			"update_conditions": request.Conditions,
			"rows_affected":     result.RowsAffected,
		}
	} else {
		result.Message = "更新操作完成，但没有记录被更新（可能是条件不匹配）"
	}
}

// processDeleteResult 处理删除结果
func (p *DatabaseResultProcessor) processDeleteResult(execResult *SQLExecutionResult, request *DatabaseOperationRequest, result *DatabaseOperationResult) {
	if result.RowsAffected > 0 {
		result.Message = fmt.Sprintf("成功从表 %s 删除%d条记录", request.TableName, result.RowsAffected)

		result.Data = map[string]interface{}{
			"deleted_conditions": request.Conditions,
			"rows_affected":      result.RowsAffected,
			"warning":            "删除操作不可逆，请确保这是您想要的结果",
		}
	} else {
		result.Message = "删除操作完成，但没有记录被删除（可能是条件不匹配）"
	}
}

// processDescribeResult 处理表结构查询结果
func (p *DatabaseResultProcessor) processDescribeResult(execResult *SQLExecutionResult, request *DatabaseOperationRequest, result *DatabaseOperationResult) {
	data, ok := execResult.Data.([]map[string]interface{})
	if !ok {
		result.Message = "表结构查询完成，但结果格式异常"
		return
	}

	result.Message = fmt.Sprintf("表 %s 的结构信息", request.TableName)

	// 格式化表结构信息
	fields := make([]map[string]interface{}, 0, len(data))
	for _, row := range data {
		field := map[string]interface{}{
			"name":     row["name"],
			"type":     row["type"],
			"not_null": row["notnull"],
			"default":  row["dflt_value"],
			"primary":  row["pk"],
		}
		fields = append(fields, field)
	}

	result.Data = map[string]interface{}{
		"table_name":  request.TableName,
		"fields":      fields,
		"field_count": len(fields),
	}
}

// generateUndoSuggestion 生成撤销建议
func (p *DatabaseResultProcessor) generateUndoSuggestion(request *DatabaseOperationRequest, result *DatabaseOperationResult) {
	if !result.Success || result.RowsAffected == 0 {
		return
	}

	switch request.OperationType {
	case "insert":
		// 插入操作的撤销是删除
		if len(request.Data) > 0 {
			// 尝试构建删除条件
			var undoConditions []string
			for field, value := range request.Data {
				if field == "name" || field == "id" || field == "username" {
					undoConditions = append(undoConditions, fmt.Sprintf("%s为'%v'", field, value))
				}
			}

			if len(undoConditions) > 0 {
				result.UndoSuggestion = fmt.Sprintf("如需撤销，请说'删除%s中%s的记录'",
					request.TableName, undoConditions[0])
			}
		}

	case "update":
		// 更新操作的撤销需要原始值（这里简化处理）
		if len(request.Conditions) > 0 {
			var conditionStr []string
			for field, value := range request.Conditions {
				conditionStr = append(conditionStr, fmt.Sprintf("%s为'%v'", field, value))
			}

			if len(conditionStr) > 0 {
				result.UndoSuggestion = fmt.Sprintf("如需修改回原值，请说'更新%s中%s的记录'",
					request.TableName, conditionStr[0])
			}
		}

	case "delete":
		// 删除操作无法撤销
		result.UndoSuggestion = "删除操作无法撤销，如需恢复数据请联系管理员"
	}
}

// formatDataForDisplay 格式化数据用于显示
func (p *DatabaseResultProcessor) formatDataForDisplay(data interface{}) interface{} {
	switch v := data.(type) {
	case []map[string]interface{}:
		// 处理查询结果
		if len(v) == 0 {
			return "无数据"
		}

		// 限制显示的字段数量和长度
		formatted := make([]map[string]interface{}, 0, len(v))
		for _, row := range v {
			formattedRow := make(map[string]interface{})
			fieldCount := 0

			for key, value := range row {
				if fieldCount >= 10 { // 最多显示10个字段
					formattedRow["..."] = "更多字段"
					break
				}

				// 格式化字段值
				if str, ok := value.(string); ok && len(str) > 100 {
					formattedRow[key] = str[:100] + "..."
				} else {
					formattedRow[key] = value
				}

				fieldCount++
			}

			formatted = append(formatted, formattedRow)
		}

		return formatted

	default:
		return data
	}
}

// generateOperationSummary 生成操作摘要
func (p *DatabaseResultProcessor) generateOperationSummary(request *DatabaseOperationRequest, result *DatabaseOperationResult) string {
	summary := fmt.Sprintf("操作类型: %s, 目标表: %s", request.OperationType, request.TableName)

	if result.Success {
		summary += fmt.Sprintf(", 影响行数: %d", result.RowsAffected)
		if result.ExecutionTime > 0 {
			summary += fmt.Sprintf(", 执行时间: %v", result.ExecutionTime)
		}
	} else {
		summary += ", 操作失败"
	}

	return summary
}

// sanitizeData 清理敏感数据
func (p *DatabaseResultProcessor) sanitizeData(data interface{}, tableName string) interface{} {
	// 定义敏感字段
	sensitiveFields := map[string][]string{
		"users": {"password_hash", "password_encrypted", "two_factor_secret"},
		"hosts": {"password_encrypted", "ssh_key_passphrase_encrypted"},
	}

	if fields, exists := sensitiveFields[tableName]; exists {
		if rows, ok := data.([]map[string]interface{}); ok {
			for _, row := range rows {
				for _, field := range fields {
					if _, exists := row[field]; exists {
						row[field] = "***隐藏***"
					}
				}
			}
		}
	}

	return data
}
