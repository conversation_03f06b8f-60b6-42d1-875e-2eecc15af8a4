package ai

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// MultimodalInteractionEngine 多模态交互引擎
type MultimodalInteractionEngine struct {
	db                 *gorm.DB
	logger             *logrus.Logger
	config             *MultimodalConfig
	speechProcessor    *SpeechProcessor
	imageProcessor     *ImageProcessor
	gestureProcessor   *GestureProcessor
	modalityFusion     *ModalityFusion
	contextManager     *MultimodalContextManager
	responseGenerator  *MultimodalResponseGenerator
	mutex              sync.RWMutex
	isRunning          bool
	sessionManager     *SessionManager
	interactionHistory []InteractionRecord
	performanceMetrics *PerformanceMetrics
}

// MultimodalConfig 多模态配置
type MultimodalConfig struct {
	EnableSpeechInput  bool          `json:"enable_speech_input"`
	EnableSpeechOutput bool          `json:"enable_speech_output"`
	EnableImageInput   bool          `json:"enable_image_input"`
	EnableGestureInput bool          `json:"enable_gesture_input"`
	EnableVideoInput   bool          `json:"enable_video_input"`
	FusionStrategy     string        `json:"fusion_strategy"`
	ResponseTimeout    time.Duration `json:"response_timeout"`
	MaxSessionDuration time.Duration `json:"max_session_duration"`
	SpeechLanguage     string        `json:"speech_language"`
	ImageQuality       string        `json:"image_quality"`
	GestureSensitivity float64       `json:"gesture_sensitivity"`
	EnableRealTimeMode bool          `json:"enable_realtime_mode"`
	CacheSize          int           `json:"cache_size"`
}

// InteractionMode 交互模式
type InteractionMode string

const (
	ModeText    InteractionMode = "text"
	ModeSpeech  InteractionMode = "speech"
	ModeImage   InteractionMode = "image"
	ModeGesture InteractionMode = "gesture"
	ModeVideo   InteractionMode = "video"
	ModeMixed   InteractionMode = "mixed"
	ModeAuto    InteractionMode = "auto"
)

// MultimodalInput 多模态输入
type MultimodalInput struct {
	SessionID    string                 `json:"session_id"`
	UserID       int64                  `json:"user_id"`
	Timestamp    time.Time              `json:"timestamp"`
	PrimaryMode  InteractionMode        `json:"primary_mode"`
	TextInput    *TextInput             `json:"text_input,omitempty"`
	SpeechInput  *SpeechInput           `json:"speech_input,omitempty"`
	ImageInput   *ImageInput            `json:"image_input,omitempty"`
	GestureInput *GestureInput          `json:"gesture_input,omitempty"`
	VideoInput   *VideoInput            `json:"video_input,omitempty"`
	Context      map[string]interface{} `json:"context"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// TextInput 文本输入
type TextInput struct {
	Content  string                 `json:"content"`
	Language string                 `json:"language"`
	Encoding string                 `json:"encoding"`
	Metadata map[string]interface{} `json:"metadata"`
}

// SpeechInput 语音输入
type SpeechInput struct {
	AudioData     []byte                 `json:"audio_data"`
	Format        string                 `json:"format"` // wav, mp3, etc.
	SampleRate    int                    `json:"sample_rate"`
	Duration      float64                `json:"duration"`
	Language      string                 `json:"language"`
	Quality       string                 `json:"quality"`
	Transcription string                 `json:"transcription,omitempty"`
	Confidence    float64                `json:"confidence"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// ImageInput 图像输入
type ImageInput struct {
	ImageData   []byte                 `json:"image_data"`
	Format      string                 `json:"format"` // jpg, png, etc.
	Width       int                    `json:"width"`
	Height      int                    `json:"height"`
	Quality     string                 `json:"quality"`
	Description string                 `json:"description,omitempty"`
	Objects     []DetectedObject       `json:"objects,omitempty"`
	Text        []ExtractedText        `json:"text,omitempty"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// GestureInput 手势输入
type GestureInput struct {
	GestureType string                 `json:"gesture_type"`
	Coordinates []Point3D              `json:"coordinates"`
	Confidence  float64                `json:"confidence"`
	Duration    float64                `json:"duration"`
	Velocity    float64                `json:"velocity"`
	Direction   string                 `json:"direction"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// VideoInput 视频输入
type VideoInput struct {
	VideoData  []byte                 `json:"video_data"`
	Format     string                 `json:"format"`
	Duration   float64                `json:"duration"`
	FrameRate  int                    `json:"frame_rate"`
	Resolution string                 `json:"resolution"`
	Frames     []VideoFrame           `json:"frames,omitempty"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// DetectedObject 检测到的对象
type DetectedObject struct {
	Name        string    `json:"name"`
	Confidence  float64   `json:"confidence"`
	BoundingBox Rectangle `json:"bounding_box"`
	Category    string    `json:"category"`
}

// ExtractedText 提取的文本
type ExtractedText struct {
	Text        string    `json:"text"`
	Confidence  float64   `json:"confidence"`
	BoundingBox Rectangle `json:"bounding_box"`
	Language    string    `json:"language"`
}

// Point3D 三维坐标点
type Point3D struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
	Z float64 `json:"z"`
}

// Rectangle 矩形区域
type Rectangle struct {
	X      int `json:"x"`
	Y      int `json:"y"`
	Width  int `json:"width"`
	Height int `json:"height"`
}

// VideoFrame 视频帧
type VideoFrame struct {
	Timestamp float64          `json:"timestamp"`
	ImageData []byte           `json:"image_data"`
	Objects   []DetectedObject `json:"objects"`
	Text      []ExtractedText  `json:"text"`
	Motion    MotionData       `json:"motion"`
}

// MotionData 运动数据
type MotionData struct {
	Direction string         `json:"direction"`
	Speed     float64        `json:"speed"`
	Objects   []MovingObject `json:"objects"`
}

// MovingObject 运动对象
type MovingObject struct {
	ID         string    `json:"id"`
	Name       string    `json:"name"`
	Trajectory []Point3D `json:"trajectory"`
	Speed      float64   `json:"speed"`
}

// MultimodalResponse 多模态响应
type MultimodalResponse struct {
	SessionID       string                 `json:"session_id"`
	ResponseID      string                 `json:"response_id"`
	Timestamp       time.Time              `json:"timestamp"`
	PrimaryMode     InteractionMode        `json:"primary_mode"`
	TextResponse    *TextResponse          `json:"text_response,omitempty"`
	SpeechResponse  *SpeechResponse        `json:"speech_response,omitempty"`
	ImageResponse   *ImageResponse         `json:"image_response,omitempty"`
	GestureResponse *GestureResponse       `json:"gesture_response,omitempty"`
	VideoResponse   *VideoResponse         `json:"video_response,omitempty"`
	UIResponse      *UIResponse            `json:"ui_response,omitempty"`
	ProcessingTime  time.Duration          `json:"processing_time"`
	Confidence      float64                `json:"confidence"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// TextResponse 文本响应
type TextResponse struct {
	Content    string                 `json:"content"`
	Language   string                 `json:"language"`
	Formatting map[string]interface{} `json:"formatting"`
	Links      []string               `json:"links"`
	Actions    []string               `json:"actions"`
}

// SpeechResponse 语音响应
type SpeechResponse struct {
	AudioData []byte  `json:"audio_data"`
	Format    string  `json:"format"`
	Duration  float64 `json:"duration"`
	Voice     string  `json:"voice"`
	Speed     float64 `json:"speed"`
	Volume    float64 `json:"volume"`
	Language  string  `json:"language"`
	SSML      string  `json:"ssml,omitempty"`
}

// ImageResponse 图像响应
type ImageResponse struct {
	ImageData   []byte                 `json:"image_data"`
	Format      string                 `json:"format"`
	Width       int                    `json:"width"`
	Height      int                    `json:"height"`
	Description string                 `json:"description"`
	Annotations []ImageAnnotation      `json:"annotations"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// GestureResponse 手势响应
type GestureResponse struct {
	GestureType  string                 `json:"gesture_type"`
	Instructions []string               `json:"instructions"`
	Feedback     string                 `json:"feedback"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// VideoResponse 视频响应
type VideoResponse struct {
	VideoData   []byte                 `json:"video_data"`
	Format      string                 `json:"format"`
	Duration    float64                `json:"duration"`
	Description string                 `json:"description"`
	Chapters    []VideoChapter         `json:"chapters"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// UIResponse UI响应
type UIResponse struct {
	ComponentType string                 `json:"component_type"`
	Layout        string                 `json:"layout"`
	Elements      []UIElement            `json:"elements"`
	Interactions  []UIInteraction        `json:"interactions"`
	Styling       map[string]interface{} `json:"styling"`
}

// ImageAnnotation 图像标注
type ImageAnnotation struct {
	Type        string    `json:"type"`
	Text        string    `json:"text"`
	Position    Point3D   `json:"position"`
	BoundingBox Rectangle `json:"bounding_box"`
	Color       string    `json:"color"`
}

// VideoChapter 视频章节
type VideoChapter struct {
	Title       string  `json:"title"`
	StartTime   float64 `json:"start_time"`
	EndTime     float64 `json:"end_time"`
	Description string  `json:"description"`
}

// UIElement UI元素
type UIElement struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"`
	Content    string                 `json:"content"`
	Properties map[string]interface{} `json:"properties"`
	Events     []string               `json:"events"`
}

// UIInteraction UI交互
type UIInteraction struct {
	Type       string                 `json:"type"`
	Target     string                 `json:"target"`
	Action     string                 `json:"action"`
	Parameters map[string]interface{} `json:"parameters"`
	Feedback   string                 `json:"feedback"`
}

// InteractionRecord 交互记录
type InteractionRecord struct {
	ID               string                 `json:"id"`
	SessionID        string                 `json:"session_id"`
	UserID           int64                  `json:"user_id"`
	InputMode        InteractionMode        `json:"input_mode"`
	OutputMode       InteractionMode        `json:"output_mode"`
	ProcessingTime   time.Duration          `json:"processing_time"`
	Success          bool                   `json:"success"`
	ErrorMessage     string                 `json:"error_message,omitempty"`
	UserSatisfaction float64                `json:"user_satisfaction"`
	Timestamp        time.Time              `json:"timestamp"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	TotalInteractions      int64                     `json:"total_interactions"`
	SuccessfulInteractions int64                     `json:"successful_interactions"`
	AverageProcessingTime  time.Duration             `json:"average_processing_time"`
	ModeUsageStats         map[InteractionMode]int64 `json:"mode_usage_stats"`
	UserSatisfactionAvg    float64                   `json:"user_satisfaction_avg"`
	ErrorRate              float64                   `json:"error_rate"`
	LastUpdated            time.Time                 `json:"last_updated"`
}

// NewMultimodalInteractionEngine 创建多模态交互引擎
func NewMultimodalInteractionEngine(
	db *gorm.DB,
	logger *logrus.Logger,
	config *MultimodalConfig,
) *MultimodalInteractionEngine {
	if config == nil {
		config = &MultimodalConfig{
			EnableSpeechInput:  true,
			EnableSpeechOutput: true,
			EnableImageInput:   true,
			EnableGestureInput: false, // 默认禁用手势
			EnableVideoInput:   false, // 默认禁用视频
			FusionStrategy:     "weighted_average",
			ResponseTimeout:    30 * time.Second,
			MaxSessionDuration: 2 * time.Hour,
			SpeechLanguage:     "zh-CN",
			ImageQuality:       "high",
			GestureSensitivity: 0.8,
			EnableRealTimeMode: true,
			CacheSize:          1000,
		}
	}

	engine := &MultimodalInteractionEngine{
		db:                 db,
		logger:             logger,
		config:             config,
		isRunning:          false,
		interactionHistory: make([]InteractionRecord, 0),
		performanceMetrics: &PerformanceMetrics{
			ModeUsageStats: make(map[InteractionMode]int64),
			LastUpdated:    time.Now(),
		},
	}

	// 初始化子组件
	engine.speechProcessor = NewSpeechProcessor(logger, config)
	engine.imageProcessor = NewImageProcessor(logger, config)
	engine.gestureProcessor = NewGestureProcessor(logger, config)
	engine.modalityFusion = NewModalityFusion(logger, config)
	engine.contextManager = NewMultimodalContextManager(logger, config)
	engine.responseGenerator = NewMultimodalResponseGenerator(logger, config)
	engine.sessionManager = NewSessionManager(logger, config)

	// 确保数据库表存在
	if err := db.AutoMigrate(&MultimodalInteractionRecord{}); err != nil {
		logger.WithError(err).Error("Failed to migrate multimodal interaction table")
	}

	logger.Info("🎤 多模态交互引擎初始化完成")
	return engine
}

// Start 启动多模态交互引擎
func (mie *MultimodalInteractionEngine) Start() error {
	mie.mutex.Lock()
	defer mie.mutex.Unlock()

	if mie.isRunning {
		return fmt.Errorf("multimodal interaction engine is already running")
	}

	mie.isRunning = true
	mie.logger.Info("🎤 多模态交互引擎启动成功")

	// 启动子组件
	if err := mie.speechProcessor.Start(); err != nil {
		mie.logger.WithError(err).Error("Failed to start speech processor")
	}

	if err := mie.imageProcessor.Start(); err != nil {
		mie.logger.WithError(err).Error("Failed to start image processor")
	}

	if err := mie.gestureProcessor.Start(); err != nil {
		mie.logger.WithError(err).Error("Failed to start gesture processor")
	}

	// 启动性能监控
	go mie.runPerformanceMonitoring()

	return nil
}

// Stop 停止多模态交互引擎
func (mie *MultimodalInteractionEngine) Stop() error {
	mie.mutex.Lock()
	defer mie.mutex.Unlock()

	if !mie.isRunning {
		return fmt.Errorf("multimodal interaction engine is not running")
	}

	mie.isRunning = false

	// 停止子组件
	mie.speechProcessor.Stop()
	mie.imageProcessor.Stop()
	mie.gestureProcessor.Stop()

	mie.logger.Info("多模态交互引擎已停止")
	return nil
}

// ProcessMultimodalInput 处理多模态输入
func (mie *MultimodalInteractionEngine) ProcessMultimodalInput(
	ctx context.Context,
	input *MultimodalInput,
) (*MultimodalResponse, error) {
	start := time.Now()

	mie.logger.WithFields(logrus.Fields{
		"session_id":   input.SessionID,
		"user_id":      input.UserID,
		"primary_mode": input.PrimaryMode,
	}).Info("🎤 开始处理多模态输入")

	// 1. 验证输入
	if err := mie.validateInput(input); err != nil {
		return nil, fmt.Errorf("input validation failed: %w", err)
	}

	// 2. 处理各种模态的输入
	processedInputs, err := mie.processInputModalities(ctx, input)
	if err != nil {
		return nil, fmt.Errorf("input processing failed: %w", err)
	}

	// 3. 模态融合
	fusedInput, err := mie.modalityFusion.FuseInputs(processedInputs)
	if err != nil {
		mie.logger.WithError(err).Warn("Modality fusion failed, using primary input")
		fusedInput = processedInputs[input.PrimaryMode]
	}

	// 4. 上下文管理
	contextualInput, err := mie.contextManager.EnhanceWithContext(input.SessionID, fusedInput)
	if err != nil {
		mie.logger.WithError(err).Warn("Context enhancement failed")
		contextualInput = fusedInput
	}

	// 5. 生成响应
	response, err := mie.responseGenerator.GenerateResponse(ctx, contextualInput, input)
	if err != nil {
		return nil, fmt.Errorf("response generation failed: %w", err)
	}

	// 6. 记录交互
	processingTime := time.Since(start)
	response.ProcessingTime = processingTime
	response.Timestamp = time.Now()

	if err := mie.recordInteraction(input, response, true, ""); err != nil {
		mie.logger.WithError(err).Error("Failed to record interaction")
	}

	// 7. 更新性能指标
	mie.updatePerformanceMetrics(input.PrimaryMode, processingTime, true)

	mie.logger.WithFields(logrus.Fields{
		"session_id":      input.SessionID,
		"processing_time": processingTime,
		"response_mode":   response.PrimaryMode,
	}).Info("🎤 多模态输入处理完成")

	return response, nil
}

// GetSupportedModes 获取支持的交互模式
func (mie *MultimodalInteractionEngine) GetSupportedModes() []InteractionMode {
	modes := []InteractionMode{ModeText} // 文本模式总是支持的

	if mie.config.EnableSpeechInput {
		modes = append(modes, ModeSpeech)
	}
	if mie.config.EnableImageInput {
		modes = append(modes, ModeImage)
	}
	if mie.config.EnableGestureInput {
		modes = append(modes, ModeGesture)
	}
	if mie.config.EnableVideoInput {
		modes = append(modes, ModeVideo)
	}

	modes = append(modes, ModeMixed, ModeAuto)
	return modes
}

// GetPerformanceMetrics 获取性能指标
func (mie *MultimodalInteractionEngine) GetPerformanceMetrics() *PerformanceMetrics {
	mie.mutex.RLock()
	defer mie.mutex.RUnlock()

	// 创建副本以避免并发访问问题
	metrics := &PerformanceMetrics{
		TotalInteractions:      mie.performanceMetrics.TotalInteractions,
		SuccessfulInteractions: mie.performanceMetrics.SuccessfulInteractions,
		AverageProcessingTime:  mie.performanceMetrics.AverageProcessingTime,
		ModeUsageStats:         make(map[InteractionMode]int64),
		UserSatisfactionAvg:    mie.performanceMetrics.UserSatisfactionAvg,
		ErrorRate:              mie.performanceMetrics.ErrorRate,
		LastUpdated:            mie.performanceMetrics.LastUpdated,
	}

	for mode, count := range mie.performanceMetrics.ModeUsageStats {
		metrics.ModeUsageStats[mode] = count
	}

	return metrics
}

// 私有方法

func (mie *MultimodalInteractionEngine) validateInput(input *MultimodalInput) error {
	if input.SessionID == "" {
		return fmt.Errorf("session ID is required")
	}
	if input.UserID <= 0 {
		return fmt.Errorf("valid user ID is required")
	}
	if input.PrimaryMode == "" {
		return fmt.Errorf("primary mode is required")
	}

	// 验证至少有一种输入模态
	hasInput := false
	if input.TextInput != nil && input.TextInput.Content != "" {
		hasInput = true
	}
	if input.SpeechInput != nil && len(input.SpeechInput.AudioData) > 0 {
		hasInput = true
	}
	if input.ImageInput != nil && len(input.ImageInput.ImageData) > 0 {
		hasInput = true
	}
	if input.GestureInput != nil && input.GestureInput.GestureType != "" {
		hasInput = true
	}
	if input.VideoInput != nil && len(input.VideoInput.VideoData) > 0 {
		hasInput = true
	}

	if !hasInput {
		return fmt.Errorf("at least one input modality is required")
	}

	return nil
}

func (mie *MultimodalInteractionEngine) processInputModalities(
	ctx context.Context,
	input *MultimodalInput,
) (map[InteractionMode]interface{}, error) {
	processedInputs := make(map[InteractionMode]interface{})

	// 处理文本输入
	if input.TextInput != nil {
		processed, err := mie.processTextInput(input.TextInput)
		if err != nil {
			mie.logger.WithError(err).Error("Failed to process text input")
		} else {
			processedInputs[ModeText] = processed
		}
	}

	// 处理语音输入
	if input.SpeechInput != nil && mie.config.EnableSpeechInput {
		processed, err := mie.speechProcessor.ProcessSpeech(ctx, input.SpeechInput)
		if err != nil {
			mie.logger.WithError(err).Error("Failed to process speech input")
		} else {
			processedInputs[ModeSpeech] = processed
		}
	}

	// 处理图像输入
	if input.ImageInput != nil && mie.config.EnableImageInput {
		processed, err := mie.imageProcessor.ProcessImage(ctx, input.ImageInput)
		if err != nil {
			mie.logger.WithError(err).Error("Failed to process image input")
		} else {
			processedInputs[ModeImage] = processed
		}
	}

	// 处理手势输入
	if input.GestureInput != nil && mie.config.EnableGestureInput {
		processed, err := mie.gestureProcessor.ProcessGesture(ctx, input.GestureInput)
		if err != nil {
			mie.logger.WithError(err).Error("Failed to process gesture input")
		} else {
			processedInputs[ModeGesture] = processed
		}
	}

	// 处理视频输入
	if input.VideoInput != nil && mie.config.EnableVideoInput {
		processed, err := mie.processVideoInput(ctx, input.VideoInput)
		if err != nil {
			mie.logger.WithError(err).Error("Failed to process video input")
		} else {
			processedInputs[ModeVideo] = processed
		}
	}

	if len(processedInputs) == 0 {
		return nil, fmt.Errorf("no input modalities were successfully processed")
	}

	return processedInputs, nil
}

func (mie *MultimodalInteractionEngine) processTextInput(input *TextInput) (interface{}, error) {
	// 简单的文本处理
	return map[string]interface{}{
		"content":  input.Content,
		"language": input.Language,
		"length":   len(input.Content),
		"type":     "text",
	}, nil
}

func (mie *MultimodalInteractionEngine) processVideoInput(ctx context.Context, input *VideoInput) (interface{}, error) {
	// 简化的视频处理
	return map[string]interface{}{
		"format":     input.Format,
		"duration":   input.Duration,
		"frame_rate": input.FrameRate,
		"resolution": input.Resolution,
		"type":       "video",
	}, nil
}

func (mie *MultimodalInteractionEngine) recordInteraction(
	input *MultimodalInput,
	response *MultimodalResponse,
	success bool,
	errorMessage string,
) error {
	record := InteractionRecord{
		ID:               fmt.Sprintf("interaction_%d", time.Now().UnixNano()),
		SessionID:        input.SessionID,
		UserID:           input.UserID,
		InputMode:        input.PrimaryMode,
		OutputMode:       response.PrimaryMode,
		ProcessingTime:   response.ProcessingTime,
		Success:          success,
		ErrorMessage:     errorMessage,
		UserSatisfaction: 0.8, // 默认满意度，实际应该从用户反馈获取
		Timestamp:        time.Now(),
		Metadata:         make(map[string]interface{}),
	}

	mie.interactionHistory = append(mie.interactionHistory, record)

	// 限制历史记录数量
	if len(mie.interactionHistory) > mie.config.CacheSize {
		mie.interactionHistory = mie.interactionHistory[1:]
	}

	return nil
}

func (mie *MultimodalInteractionEngine) updatePerformanceMetrics(
	mode InteractionMode,
	processingTime time.Duration,
	success bool,
) {
	mie.mutex.Lock()
	defer mie.mutex.Unlock()

	mie.performanceMetrics.TotalInteractions++
	if success {
		mie.performanceMetrics.SuccessfulInteractions++
	}

	mie.performanceMetrics.ModeUsageStats[mode]++

	// 更新平均处理时间
	totalTime := mie.performanceMetrics.AverageProcessingTime * time.Duration(mie.performanceMetrics.TotalInteractions-1)
	mie.performanceMetrics.AverageProcessingTime = (totalTime + processingTime) / time.Duration(mie.performanceMetrics.TotalInteractions)

	// 更新错误率
	mie.performanceMetrics.ErrorRate = 1.0 - (float64(mie.performanceMetrics.SuccessfulInteractions) / float64(mie.performanceMetrics.TotalInteractions))

	mie.performanceMetrics.LastUpdated = time.Now()
}

func (mie *MultimodalInteractionEngine) runPerformanceMonitoring() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !mie.isRunning {
				return
			}
			mie.logPerformanceMetrics()
		}
	}
}

func (mie *MultimodalInteractionEngine) logPerformanceMetrics() {
	metrics := mie.GetPerformanceMetrics()

	mie.logger.WithFields(logrus.Fields{
		"total_interactions":      metrics.TotalInteractions,
		"successful_interactions": metrics.SuccessfulInteractions,
		"error_rate":              metrics.ErrorRate,
		"avg_processing_time":     metrics.AverageProcessingTime,
		"user_satisfaction":       metrics.UserSatisfactionAvg,
	}).Info("📊 多模态交互性能指标")
}
