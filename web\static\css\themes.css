/* AI运维管理平台 - 主题系统 */
/* 支持亮色/暗色主题切换和个性化设置 */

/* ========================================
   主题系统基础架构
   ======================================== */

/* 主题切换器 */
.theme-switcher {
  position: relative;
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
}

.theme-toggle {
  width: var(--spacing-12);
  height: var(--spacing-6);
  background: var(--color-gray-300);
  border: none;
  border-radius: var(--radius-full);
  position: relative;
  cursor: pointer;
  transition: all var(--transition-base);
  outline: none;
}

.theme-toggle:focus-visible {
  box-shadow: 0 0 0 2px var(--color-primary-500);
}

.theme-toggle::before {
  content: '';
  position: absolute;
  top: 2px;
  left: 2px;
  width: var(--spacing-5);
  height: var(--spacing-5);
  background: white;
  border-radius: var(--radius-full);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-sm);
}

.theme-toggle.dark {
  background: var(--color-primary-600);
}

.theme-toggle.dark::before {
  transform: translateX(var(--spacing-6));
}

.theme-icon {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  transition: all var(--transition-base);
}

.theme-icon.active {
  color: var(--color-primary-600);
}

/* ========================================
   暗色主题变量
   ======================================== */

[data-theme="dark"] {
  /* 主色彩系统 - 暗色版本 */
  --color-primary-50: #1e1b4b;
  --color-primary-100: #312e81;
  --color-primary-200: #3730a3;
  --color-primary-300: #4338ca;
  --color-primary-400: #4f46e5;
  --color-primary-500: #6366f1;
  --color-primary-600: #818cf8;
  --color-primary-700: #a5b4fc;
  --color-primary-800: #c7d2fe;
  --color-primary-900: #e0e7ff;
  --color-primary-950: #f0f4ff;

  /* 辅助色彩 - 暗色版本 */
  --color-accent-50: #701a75;
  --color-accent-100: #86198f;
  --color-accent-200: #a21caf;
  --color-accent-300: #c026d3;
  --color-accent-400: #d946ef;
  --color-accent-500: #e879f9;
  --color-accent-600: #f0abfc;
  --color-accent-700: #f5d0fe;
  --color-accent-800: #fae8ff;
  --color-accent-900: #fdf4ff;

  /* 中性色系 - 暗色版本 */
  --color-gray-50: #09090b;
  --color-gray-100: #18181b;
  --color-gray-200: #27272a;
  --color-gray-300: #3f3f46;
  --color-gray-400: #52525b;
  --color-gray-500: #71717a;
  --color-gray-600: #a1a1aa;
  --color-gray-700: #d4d4d8;
  --color-gray-800: #e4e4e7;
  --color-gray-900: #f4f4f5;
  --color-gray-950: #fafafa;

  /* 语义化颜色 - 暗色版本 */
  --color-success-50: #14532d;
  --color-success-100: #166534;
  --color-success-200: #15803d;
  --color-success-300: #16a34a;
  --color-success-400: #22c55e;
  --color-success-500: #4ade80;
  --color-success-600: #86efac;
  --color-success-700: #bbf7d0;
  --color-success-800: #dcfce7;
  --color-success-900: #f0fdf4;

  --color-warning-50: #78350f;
  --color-warning-100: #92400e;
  --color-warning-200: #b45309;
  --color-warning-300: #d97706;
  --color-warning-400: #f59e0b;
  --color-warning-500: #fbbf24;
  --color-warning-600: #fcd34d;
  --color-warning-700: #fde68a;
  --color-warning-800: #fef3c7;
  --color-warning-900: #fffbeb;

  --color-error-50: #7f1d1d;
  --color-error-100: #991b1b;
  --color-error-200: #b91c1c;
  --color-error-300: #dc2626;
  --color-error-400: #ef4444;
  --color-error-500: #f87171;
  --color-error-600: #fca5a5;
  --color-error-700: #fecaca;
  --color-error-800: #fee2e2;
  --color-error-900: #fef2f2;

  --color-info-50: #1e3a8a;
  --color-info-100: #1e40af;
  --color-info-200: #1d4ed8;
  --color-info-300: #2563eb;
  --color-info-400: #3b82f6;
  --color-info-500: #60a5fa;
  --color-info-600: #93c5fd;
  --color-info-700: #bfdbfe;
  --color-info-800: #dbeafe;
  --color-info-900: #eff6ff;

  /* 阴影系统 - 暗色版本 */
  --shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px -1px rgba(0, 0, 0, 0.4);
  --shadow-base: 0 4px 6px -1px rgba(0, 0, 0, 0.4), 0 2px 4px -2px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.4), 0 4px 6px -4px rgba(0, 0, 0, 0.4);
  --shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.4), 0 8px 10px -6px rgba(0, 0, 0, 0.4);
  --shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.6);
  --shadow-inner: inset 0 2px 4px 0 rgba(0, 0, 0, 0.3);

  /* 彩色阴影 - 暗色版本 */
  --shadow-primary: 0 10px 15px -3px rgba(99, 102, 241, 0.3), 0 4px 6px -4px rgba(99, 102, 241, 0.3);
  --shadow-accent: 0 10px 15px -3px rgba(217, 70, 239, 0.3), 0 4px 6px -4px rgba(217, 70, 239, 0.3);
  --shadow-success: 0 10px 15px -3px rgba(34, 197, 94, 0.3), 0 4px 6px -4px rgba(34, 197, 94, 0.3);
  --shadow-warning: 0 10px 15px -3px rgba(251, 191, 36, 0.3), 0 4px 6px -4px rgba(251, 191, 36, 0.3);
  --shadow-error: 0 10px 15px -3px rgba(239, 68, 68, 0.3), 0 4px 6px -4px rgba(239, 68, 68, 0.3);
}

/* ========================================
   暗色主题特定样式
   ======================================== */

[data-theme="dark"] body {
  background: linear-gradient(135deg, var(--color-gray-100) 0%, var(--color-gray-200) 100%);
  color: var(--color-gray-900);
}

[data-theme="dark"] .chat-container {
  background: linear-gradient(135deg, var(--color-gray-100) 0%, var(--color-primary-100) 100%);
}

[data-theme="dark"] .chat-navbar {
  background: rgba(24, 24, 27, 0.95);
  border-bottom-color: var(--color-gray-300);
}

[data-theme="dark"] .chat-area {
  background: var(--color-gray-100);
  border-color: var(--color-gray-300);
}

[data-theme="dark"] .chat-header {
  background: linear-gradient(135deg, var(--color-primary-100) 0%, var(--color-gray-100) 100%);
  border-bottom-color: var(--color-gray-300);
}

[data-theme="dark"] .chat-messages {
  background: linear-gradient(180deg, var(--color-gray-100) 0%, var(--color-primary-100) 100%);
}

[data-theme="dark"] .chat-input-area {
  background: linear-gradient(135deg, var(--color-gray-100) 0%, var(--color-primary-100) 100%);
  border-top-color: var(--color-gray-300);
}

[data-theme="dark"] .input-wrapper {
  background: var(--color-gray-200);
  border-color: var(--color-gray-400);
}

[data-theme="dark"] .input-wrapper:focus-within {
  border-color: var(--color-primary-500);
  box-shadow: var(--shadow-lg), 0 0 0 4px rgba(99, 102, 241, 0.2);
}

[data-theme="dark"] .message.assistant .message-content {
  background: rgba(39, 39, 42, 0.95);
  color: var(--color-gray-800);
  border-color: var(--color-gray-400);
}

[data-theme="dark"] .message.assistant .message-content::after {
  border-right-color: rgba(39, 39, 42, 0.95);
}

[data-theme="dark"] .feature-card {
  background: var(--color-gray-200);
  border-color: var(--color-gray-400);
  color: var(--color-gray-800);
}

[data-theme="dark"] .feature-card::before {
  background: linear-gradient(135deg, var(--color-primary-200), var(--color-primary-300));
}

[data-theme="dark"] .quick-action-btn {
  background: var(--color-gray-200);
  border-color: var(--color-gray-400);
  color: var(--color-gray-800);
}

[data-theme="dark"] .quick-action-btn::before {
  background: linear-gradient(135deg, var(--color-primary-200), var(--color-primary-300));
}

[data-theme="dark"] .suggestion-btn {
  background: var(--color-gray-200);
  border-color: var(--color-gray-400);
  color: var(--color-gray-700);
}

[data-theme="dark"] .suggestion-btn::before {
  background: linear-gradient(135deg, var(--color-primary-200), var(--color-primary-300));
}

/* ========================================
   主题切换动画
   ======================================== */

* {
  transition: background-color var(--transition-base), 
              border-color var(--transition-base), 
              color var(--transition-base),
              box-shadow var(--transition-base);
}

/* 主题切换时的平滑过渡 */
.theme-transition {
  transition: all var(--duration-500) var(--ease-out) !important;
}

.theme-transition * {
  transition: all var(--duration-500) var(--ease-out) !important;
}

/* ========================================
   个性化设置面板
   ======================================== */

.settings-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: var(--color-gray-50);
  border-left: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-2xl);
  z-index: var(--z-modal);
  transition: right var(--transition-base);
  overflow-y: auto;
}

.settings-panel.show {
  right: 0;
}

.settings-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-gray-50) 100%);
}

.settings-title {
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-2);
}

.settings-subtitle {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
}

.settings-content {
  padding: var(--spacing-6);
}

.setting-group {
  margin-bottom: var(--spacing-8);
}

.setting-group-title {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-4);
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
}

.setting-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4);
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-lg);
  margin-bottom: var(--spacing-3);
  transition: all var(--transition-base);
}

.setting-item:hover {
  border-color: var(--color-primary-300);
  box-shadow: var(--shadow-sm);
}

.setting-info {
  flex: 1;
}

.setting-label {
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-800);
  margin-bottom: var(--spacing-1);
}

.setting-description {
  font-size: var(--font-size-xs);
  color: var(--color-gray-600);
  line-height: var(--line-height-relaxed);
}

.setting-control {
  margin-left: var(--spacing-4);
}

/* 颜色选择器 */
.color-picker {
  display: flex;
  gap: var(--spacing-2);
  flex-wrap: wrap;
}

.color-option {
  width: var(--spacing-8);
  height: var(--spacing-8);
  border-radius: var(--radius-full);
  border: 2px solid var(--color-gray-300);
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
}

.color-option:hover {
  transform: scale(1.1);
  box-shadow: var(--shadow-md);
}

.color-option.active {
  border-color: var(--color-primary-600);
  box-shadow: 0 0 0 2px var(--color-primary-200);
}

.color-option.active::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-bold);
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* ========================================
   开关按钮组件
   ======================================== */

.switch {
  position: relative;
  display: inline-block;
  width: var(--spacing-12);
  height: var(--spacing-6);
}

.switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--color-gray-300);
  transition: var(--transition-base);
  border-radius: var(--radius-full);
}

.slider:before {
  position: absolute;
  content: "";
  height: calc(var(--spacing-6) - 4px);
  width: calc(var(--spacing-6) - 4px);
  left: 2px;
  bottom: 2px;
  background-color: white;
  transition: var(--transition-base);
  border-radius: var(--radius-full);
  box-shadow: var(--shadow-sm);
}

input:checked + .slider {
  background-color: var(--color-primary-600);
}

input:focus + .slider {
  box-shadow: 0 0 0 2px var(--color-primary-200);
}

input:checked + .slider:before {
  transform: translateX(var(--spacing-6));
}

/* 开关按钮悬停效果 */
.slider:hover {
  background-color: var(--color-gray-400);
}

input:checked + .slider:hover {
  background-color: var(--color-primary-700);
}

/* ========================================
   表单控件增强
   ======================================== */

.form-control-sm {
  padding: var(--spacing-1_5) var(--spacing-2_5);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-gray-300);
  background: white;
  transition: all var(--transition-base);
}

.form-control-sm:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 2px var(--color-primary-100);
}

/* 暗色主题下的表单控件 */
[data-theme="dark"] .form-control-sm {
  background: var(--color-gray-200);
  border-color: var(--color-gray-400);
  color: var(--color-gray-800);
}

[data-theme="dark"] .form-control-sm:focus {
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
}

[data-theme="dark"] .slider {
  background-color: var(--color-gray-400);
}

[data-theme="dark"] .slider:hover {
  background-color: var(--color-gray-500);
}

[data-theme="dark"] input:checked + .slider {
  background-color: var(--color-primary-500);
}

[data-theme="dark"] input:checked + .slider:hover {
  background-color: var(--color-primary-600);
}
