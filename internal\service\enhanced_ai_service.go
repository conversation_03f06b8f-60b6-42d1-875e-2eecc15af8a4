package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/config"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedAIService 增强的AI服务 - 集成统一执行引擎
type EnhancedAIService struct {
	db                  *gorm.DB
	config              *config.Config
	logger              *logrus.Logger
	deepseekService     *DeepSeekService
	intentRecognizer    *IntentRecognizer
	executionEngine     *UnifiedExecutionEngine
	confirmationManager *ConfirmationManager
	hostService         HostService
}

// NewEnhancedAIService 创建增强的AI服务
func NewEnhancedAIService(
	db *gorm.DB,
	cfg *config.Config,
	logger *logrus.Logger,
	hostService HostService,
) *EnhancedAIService {
	// 创建DeepSeek服务
	deepseekService := NewDeepSeekService(&cfg.DeepSeek, logger)

	// 创建意图识别器
	intentRecognizer := NewIntentRecognizer(deepseekService, logger)

	// 创建确认管理器
	confirmationManager := &ConfirmationManager{
		pending: make(map[string]*PendingConfirmation),
	}

	// 创建统一执行引擎 - 传递DeepSeek API密钥
	executionEngine := NewUnifiedExecutionEngine(db, logger, hostService, confirmationManager, cfg.DeepSeek.APIKey)

	return &EnhancedAIService{
		db:                  db,
		config:              cfg,
		logger:              logger,
		deepseekService:     deepseekService,
		intentRecognizer:    intentRecognizer,
		executionEngine:     executionEngine,
		confirmationManager: confirmationManager,
		hostService:         hostService,
	}
}

// GetExecutionEngine 获取统一执行引擎
func (eas *EnhancedAIService) GetExecutionEngine() *UnifiedExecutionEngine {
	return eas.executionEngine
}

// ProcessMessage 处理消息 - 使用统一执行引擎
func (eas *EnhancedAIService) ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	start := time.Now()

	eas.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
		"service":    "enhanced_ai_service",
	}).Info("🚀 EnhancedAIService: 开始处理消息 - 统一执行引擎激活中")

	// 🔑 关键修复：在意图识别之前检查是否为确认消息
	if eas.IsConfirmationMessage(req.Message) {
		eas.logger.WithField("message", req.Message).Info("🎯 检测到确认消息，直接处理确认操作")
		return eas.handleConfirmationMessage(ctx, req, start)
	}

	// 第一步：意图识别
	intent, err := eas.intentRecognizer.RecognizeIntent(ctx, req.Message)
	if err != nil {
		eas.logger.WithError(err).Error("EnhancedAIService: Intent recognition failed")
		return &ProcessMessageResponse{
			Content:        fmt.Sprintf("❌ 意图识别失败：%s\n\n💡 请尝试重新描述您的需求", err.Error()),
			Intent:         "intent_recognition_failed",
			Confidence:     0.0,
			TokenCount:     0,
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
		}, nil
	}

	eas.logger.WithFields(logrus.Fields{
		"intent_type": intent.Type,
		"confidence":  intent.Confidence,
		"parameters":  intent.Parameters,
	}).Info("EnhancedAIService: Intent recognized successfully")

	// 第二步：转换Intent为IntentResult
	intentResult := &IntentResult{
		Type:       intent.Type,
		Confidence: intent.Confidence,
		Parameters: intent.Parameters,
		Command:    intent.Command,
	}

	// 第三步：构建执行请求
	execReq := &ExecutionRequest{
		SessionID:   req.SessionID,
		UserID:      req.UserID,
		Intent:      intentResult,
		OriginalMsg: req.Message,
		Context:     make(map[string]interface{}),
	}

	// 第三步：执行意图
	execResult, err := eas.executionEngine.Execute(ctx, execReq)
	if err != nil {
		eas.logger.WithError(err).Error("EnhancedAIService: Execution failed")
		return &ProcessMessageResponse{
			Content:        fmt.Sprintf("❌ 执行失败：%s", err.Error()),
			Intent:         intent.Type,
			Confidence:     intent.Confidence,
			Parameters:     intent.Parameters,
			TokenCount:     0,
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
		}, nil
	}

	eas.logger.WithFields(logrus.Fields{
		"session_id":     req.SessionID,
		"intent_type":    intent.Type,
		"success":        execResult.Success,
		"execution_time": execResult.ExecutionTime,
		"action":         execResult.Action,
	}).Info("EnhancedAIService: Execution completed")

	// 第四步：构建响应
	response := &ProcessMessageResponse{
		Content:        execResult.Content,
		Intent:         intentResult.Type,
		Confidence:     intentResult.Confidence,
		Parameters:     intentResult.Parameters,
		TokenCount:     50, // 估算token数
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
	}

	// 如果需要确认，添加动作建议
	if execResult.RequireConfirm {
		response.ActionSuggestions = []string{
			"请输入\"确认执行\"来完成操作",
			"或输入\"取消操作\"来放弃",
		}
		// 将确认令牌添加到参数中
		if response.Parameters == nil {
			response.Parameters = make(map[string]interface{})
		}
		response.Parameters["confirm_token"] = execResult.ConfirmToken
		response.Parameters["require_confirm"] = true
	}

	return response, nil
}

// ProcessConfirmation 处理确认操作
func (eas *EnhancedAIService) ProcessConfirmation(ctx context.Context, confirmToken string, userID int64) (*ProcessMessageResponse, error) {
	start := time.Now()

	eas.logger.WithFields(logrus.Fields{
		"confirm_token": confirmToken,
		"user_id":       userID,
	}).Info("EnhancedAIService: Processing confirmation")

	// 查找待确认的操作
	eas.confirmationManager.mutex.RLock()
	pending, exists := eas.confirmationManager.pending[confirmToken]
	eas.confirmationManager.mutex.RUnlock()

	if !exists {
		return &ProcessMessageResponse{
			Content:        "❌ 确认令牌无效或已过期",
			Intent:         "confirmation_failed",
			Confidence:     0.0,
			TokenCount:     0,
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
		}, nil
	}

	// 检查是否过期
	if time.Now().After(pending.ExpiresAt) {
		eas.confirmationManager.mutex.Lock()
		delete(eas.confirmationManager.pending, confirmToken)
		eas.confirmationManager.mutex.Unlock()

		return &ProcessMessageResponse{
			Content:        "❌ 确认令牌已过期，请重新发起操作",
			Intent:         "confirmation_expired",
			Confidence:     0.0,
			TokenCount:     0,
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
		}, nil
	}

	// 检查用户权限
	if pending.UserID != userID {
		return &ProcessMessageResponse{
			Content:        "❌ 无权限确认此操作",
			Intent:         "confirmation_unauthorized",
			Confidence:     0.0,
			TokenCount:     0,
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
		}, nil
	}

	// 执行确认的操作
	var content string
	var success bool

	switch pending.Operation {
	case "delete":
		// 执行删除操作
		result := eas.db.Exec(pending.SQL)
		if result.Error != nil {
			content = fmt.Sprintf("❌ 删除操作失败：%s", result.Error.Error())
			success = false
		} else {
			content = fmt.Sprintf("✅ %s 成功\n\n删除了 %d 条记录", pending.Description, result.RowsAffected)
			success = true
		}
	case "password_update":
		// 执行密码更新操作
		result := eas.db.Exec(pending.SQL)
		if result.Error != nil {
			content = fmt.Sprintf("❌ 密码更新失败：%s", result.Error.Error())
			success = false
		} else {
			content = "✅ **密码更新成功**\n\n🔐 主机登录密码已更新\n💡 建议立即测试新密码的连接性"
			success = true
		}
	default:
		content = fmt.Sprintf("❌ 不支持的操作类型: %s", pending.Operation)
		success = false
	}

	// 清理已确认的操作
	eas.confirmationManager.mutex.Lock()
	delete(eas.confirmationManager.pending, confirmToken)
	eas.confirmationManager.mutex.Unlock()

	eas.logger.WithFields(logrus.Fields{
		"confirm_token": confirmToken,
		"operation":     pending.Operation,
		"success":       success,
		"user_id":       userID,
	}).Info("EnhancedAIService: Confirmation processed")

	return &ProcessMessageResponse{
		Content:        content,
		Intent:         "confirmation_processed",
		Confidence:     1.0,
		TokenCount:     30,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
		Parameters: map[string]interface{}{
			"operation": pending.Operation,
			"success":   success,
		},
	}, nil
}

// IsConfirmationMessage 检查是否是确认消息
func (eas *EnhancedAIService) IsConfirmationMessage(message string) bool {
	confirmations := []string{
		"确认", "确认执行", "确认删除", "确认更新", "确认修改",
		"是", "yes", "y", "ok", "好的", "可以", "同意", "执行",
	}
	lowerMsg := strings.ToLower(strings.TrimSpace(message))
	for _, conf := range confirmations {
		if lowerMsg == strings.ToLower(conf) {
			return true
		}
	}
	return false
}

// handleConfirmationMessage 处理确认消息
func (eas *EnhancedAIService) handleConfirmationMessage(ctx context.Context, req *ProcessMessageRequest, start time.Time) (*ProcessMessageResponse, error) {
	eas.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
		"message":    req.Message,
	}).Info("🔑 EnhancedAIService: 处理确认消息")

	// 查找该用户的待确认操作
	eas.confirmationManager.mutex.Lock()
	defer eas.confirmationManager.mutex.Unlock()

	// 查找该用户的待确认操作
	var pendingOp *PendingConfirmation
	var confirmToken string
	for token, op := range eas.confirmationManager.pending {
		if op.UserID == req.UserID && time.Now().Before(op.ExpiresAt) {
			pendingOp = op
			confirmToken = token
			break
		}
	}

	if pendingOp == nil {
		eas.logger.WithField("user_id", req.UserID).Warn("没有找到待确认的操作")
		return &ProcessMessageResponse{
			Content:        "❌ 没有找到待确认的操作，或操作已过期",
			Intent:         "confirmation_failed",
			Confidence:     1.0,
			TokenCount:     0,
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
		}, nil
	}

	eas.logger.WithFields(logrus.Fields{
		"user_id":       req.UserID,
		"confirm_token": confirmToken,
		"operation":     pendingOp.Operation,
		"sql":           pendingOp.SQL,
	}).Info("🎯 找到待确认的操作，准备执行")

	// 执行SQL操作
	result := eas.db.WithContext(ctx).Exec(pendingOp.SQL)
	if result.Error != nil {
		eas.logger.WithError(result.Error).Error("SQL执行失败")
		return &ProcessMessageResponse{
			Content:        fmt.Sprintf("❌ 操作失败：%s", result.Error.Error()),
			Intent:         "sql_execution_failed",
			Confidence:     1.0,
			TokenCount:     0,
			ProcessingTime: time.Since(start),
			Timestamp:      time.Now(),
		}, nil
	}

	// 构建成功响应
	var content string
	if result.RowsAffected == 0 {
		content = fmt.Sprintf("⚠️ %s 执行完成，但没有记录被影响\n\n📊 影响行数：0", pendingOp.Description)
	} else {
		content = fmt.Sprintf("✅ %s 执行成功！\n\n📊 影响行数：%d", pendingOp.Description, result.RowsAffected)
	}

	// 添加执行的SQL信息（用于调试）
	content += fmt.Sprintf("\n\n🔍 执行的SQL：\n```sql\n%s\n```", pendingOp.SQL)

	// 从待确认列表中移除
	delete(eas.confirmationManager.pending, confirmToken)

	return &ProcessMessageResponse{
		Content:        content,
		Intent:         "confirmation_executed",
		Confidence:     1.0,
		TokenCount:     len(content) / 4,
		ProcessingTime: time.Since(start),
		Timestamp:      time.Now(),
		Parameters: map[string]interface{}{
			"rows_affected": result.RowsAffected,
			"sql":           pendingOp.SQL,
			"operation":     pendingOp.Operation,
		},
	}, nil
}

// GetPendingConfirmations 获取用户的待确认操作
func (eas *EnhancedAIService) GetPendingConfirmations(userID int64) []*PendingConfirmation {
	eas.confirmationManager.mutex.RLock()
	defer eas.confirmationManager.mutex.RUnlock()

	var pending []*PendingConfirmation
	for _, p := range eas.confirmationManager.pending {
		if p.UserID == userID && time.Now().Before(p.ExpiresAt) {
			pending = append(pending, p)
		}
	}

	return pending
}

// CleanupExpiredConfirmations 清理过期的确认操作
func (eas *EnhancedAIService) CleanupExpiredConfirmations() {
	eas.confirmationManager.mutex.Lock()
	defer eas.confirmationManager.mutex.Unlock()

	now := time.Now()
	for token, pending := range eas.confirmationManager.pending {
		if now.After(pending.ExpiresAt) {
			delete(eas.confirmationManager.pending, token)
			eas.logger.WithFields(logrus.Fields{
				"token":     token,
				"operation": pending.Operation,
				"user_id":   pending.UserID,
			}).Info("EnhancedAIService: Cleaned up expired confirmation")
		}
	}
}
