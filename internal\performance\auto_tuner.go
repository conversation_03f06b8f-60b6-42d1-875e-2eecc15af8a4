package performance

import (
	"context"
	"fmt"
	"runtime"
	"runtime/debug"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// TuningAction 调优动作类型
type TuningAction string

const (
	ActionIncreaseGCTarget     TuningAction = "increase_gc_target"      // 增加GC目标
	ActionDecreaseGCTarget     TuningAction = "decrease_gc_target"      // 减少GC目标
	ActionIncreaseMaxProcs     TuningAction = "increase_max_procs"      // 增加最大处理器数
	ActionDecreaseMaxProcs     TuningAction = "decrease_max_procs"      // 减少最大处理器数
	ActionTriggerGC            TuningAction = "trigger_gc"              // 触发GC
	ActionReduceGoroutines     TuningAction = "reduce_goroutines"       // 减少Goroutine
	ActionOptimizeMemory       TuningAction = "optimize_memory"         // 优化内存
	ActionAdjustCacheSize      TuningAction = "adjust_cache_size"       // 调整缓存大小
	ActionOptimizeConnPool     TuningAction = "optimize_conn_pool"      // 优化连接池
	ActionReduceLogLevel       TuningAction = "reduce_log_level"        // 降低日志级别
)

// TuningRule 调优规则
type TuningRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Enabled     bool                   `json:"enabled"`
	Conditions  []TuningCondition      `json:"conditions"`  // 触发条件
	Actions     []TuningAction         `json:"actions"`     // 调优动作
	Cooldown    time.Duration          `json:"cooldown"`    // 冷却期
	MaxExecutions int                  `json:"max_executions"` // 最大执行次数
	Priority    int                    `json:"priority"`    // 优先级
	Metadata    map[string]interface{} `json:"metadata"`
}

// TuningCondition 调优条件
type TuningCondition struct {
	Metric    string  `json:"metric"`     // 指标名称
	Operator  string  `json:"operator"`   // 操作符: >, <, >=, <=, ==
	Value     float64 `json:"value"`      // 阈值
	Duration  time.Duration `json:"duration"` // 持续时间
}

// TuningExecution 调优执行记录
type TuningExecution struct {
	RuleID      string        `json:"rule_id"`
	Actions     []TuningAction `json:"actions"`
	Timestamp   time.Time     `json:"timestamp"`
	Success     bool          `json:"success"`
	Duration    time.Duration `json:"duration"`
	Error       string        `json:"error,omitempty"`
	BeforeMetrics *PerformanceMetrics `json:"before_metrics"`
	AfterMetrics  *PerformanceMetrics `json:"after_metrics,omitempty"`
	Impact      map[string]float64    `json:"impact"` // 影响评估
}

// TuningConfig 调优配置
type TuningConfig struct {
	Enabled           bool          `json:"enabled"`
	CheckInterval     time.Duration `json:"check_interval"`     // 检查间隔
	MaxConcurrentTuning int         `json:"max_concurrent_tuning"` // 最大并发调优数
	SafeMode          bool          `json:"safe_mode"`          // 安全模式
	DryRun            bool          `json:"dry_run"`            // 试运行模式
	BackupEnabled     bool          `json:"backup_enabled"`     // 备份启用
	RollbackEnabled   bool          `json:"rollback_enabled"`   // 回滚启用
	ImpactThreshold   float64       `json:"impact_threshold"`   // 影响阈值
}

// AutoTuner 自动调优器
type AutoTuner struct {
	config         *TuningConfig
	monitor        *PerformanceMonitor
	rules          map[string]*TuningRule
	executions     []*TuningExecution
	actionHandlers map[TuningAction]ActionHandler
	logger         *logrus.Logger
	mutex          sync.RWMutex
	running        bool
	stopChan       chan struct{}
	activeTuning   int
}

// ActionHandler 动作处理器接口
type ActionHandler interface {
	Execute(ctx context.Context, metrics *PerformanceMetrics) error
	Rollback(ctx context.Context) error
	Name() string
	Impact() map[string]float64 // 预期影响
}

// NewAutoTuner 创建自动调优器
func NewAutoTuner(config *TuningConfig, monitor *PerformanceMonitor, logger *logrus.Logger) *AutoTuner {
	// 设置默认值
	if config.CheckInterval <= 0 {
		config.CheckInterval = 30 * time.Second
	}
	if config.MaxConcurrentTuning <= 0 {
		config.MaxConcurrentTuning = 3
	}
	if config.ImpactThreshold <= 0 {
		config.ImpactThreshold = 0.1 // 10%
	}

	at := &AutoTuner{
		config:         config,
		monitor:        monitor,
		rules:          make(map[string]*TuningRule),
		executions:     make([]*TuningExecution, 0),
		actionHandlers: make(map[TuningAction]ActionHandler),
		logger:         logger,
		stopChan:       make(chan struct{}),
	}

	// 注册默认动作处理器
	at.registerDefaultHandlers()

	// 注册默认调优规则
	at.registerDefaultRules()

	return at
}

// Start 启动自动调优器
func (at *AutoTuner) Start(ctx context.Context) error {
	at.mutex.Lock()
	defer at.mutex.Unlock()

	if at.running {
		return fmt.Errorf("auto tuner is already running")
	}

	if !at.config.Enabled {
		at.logger.Info("Auto tuner is disabled")
		return nil
	}

	at.running = true

	// 启动调优循环
	go at.tuningLoop(ctx)

	at.logger.Info("Auto tuner started")
	return nil
}

// Stop 停止自动调优器
func (at *AutoTuner) Stop() error {
	at.mutex.Lock()
	defer at.mutex.Unlock()

	if !at.running {
		return nil
	}

	close(at.stopChan)
	at.running = false

	at.logger.Info("Auto tuner stopped")
	return nil
}

// RegisterRule 注册调优规则
func (at *AutoTuner) RegisterRule(rule *TuningRule) error {
	at.mutex.Lock()
	defer at.mutex.Unlock()

	if rule.ID == "" {
		return fmt.Errorf("rule ID cannot be empty")
	}

	at.rules[rule.ID] = rule
	at.logger.WithFields(logrus.Fields{
		"rule_id":   rule.ID,
		"rule_name": rule.Name,
	}).Info("Tuning rule registered")

	return nil
}

// RegisterActionHandler 注册动作处理器
func (at *AutoTuner) RegisterActionHandler(action TuningAction, handler ActionHandler) {
	at.mutex.Lock()
	defer at.mutex.Unlock()

	at.actionHandlers[action] = handler
	at.logger.WithFields(logrus.Fields{
		"action":  action,
		"handler": handler.Name(),
	}).Info("Action handler registered")
}

// tuningLoop 调优循环
func (at *AutoTuner) tuningLoop(ctx context.Context) {
	ticker := time.NewTicker(at.config.CheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-at.stopChan:
			return
		case <-ticker.C:
			at.performTuning(ctx)
		}
	}
}

// performTuning 执行调优
func (at *AutoTuner) performTuning(ctx context.Context) {
	// 检查并发限制
	at.mutex.RLock()
	if at.activeTuning >= at.config.MaxConcurrentTuning {
		at.mutex.RUnlock()
		at.logger.Debug("Max concurrent tuning reached, skipping")
		return
	}
	at.mutex.RUnlock()

	// 获取当前指标
	metrics := at.monitor.GetCurrentMetrics()
	if metrics == nil {
		at.logger.Debug("No metrics available for tuning")
		return
	}

	// 检查所有规则
	at.mutex.RLock()
	rules := make([]*TuningRule, 0, len(at.rules))
	for _, rule := range at.rules {
		if rule.Enabled && at.shouldExecuteRule(rule, metrics) {
			rules = append(rules, rule)
		}
	}
	at.mutex.RUnlock()

	if len(rules) == 0 {
		return
	}

	// 按优先级排序
	at.sortRulesByPriority(rules)

	// 执行第一个匹配的规则
	rule := rules[0]
	go at.executeRule(ctx, rule, metrics)
}

// shouldExecuteRule 检查是否应该执行规则
func (at *AutoTuner) shouldExecuteRule(rule *TuningRule, metrics *PerformanceMetrics) bool {
	// 检查冷却期
	if at.isInCooldown(rule) {
		return false
	}

	// 检查最大执行次数
	if rule.MaxExecutions > 0 && at.getExecutionCount(rule.ID) >= rule.MaxExecutions {
		return false
	}

	// 检查条件
	for _, condition := range rule.Conditions {
		if !at.evaluateCondition(condition, metrics) {
			return false
		}
	}

	return true
}

// evaluateCondition 评估条件
func (at *AutoTuner) evaluateCondition(condition TuningCondition, metrics *PerformanceMetrics) bool {
	var value float64

	switch condition.Metric {
	case "cpu_usage":
		value = metrics.CPUUsage
	case "memory_usage":
		value = metrics.MemoryUsage
	case "goroutine_count":
		value = float64(metrics.GoroutineCount)
	case "heap_size":
		value = float64(metrics.HeapSize)
	case "gc_pause_time":
		value = metrics.GCPauseTime
	case "response_time":
		value = metrics.AvgResponseTime
	case "error_rate":
		if metrics.RequestCount > 0 {
			value = float64(metrics.ErrorCount) / float64(metrics.RequestCount) * 100
		}
	default:
		return false
	}

	switch condition.Operator {
	case ">":
		return value > condition.Value
	case "<":
		return value < condition.Value
	case ">=":
		return value >= condition.Value
	case "<=":
		return value <= condition.Value
	case "==":
		return value == condition.Value
	default:
		return false
	}
}

// executeRule 执行规则
func (at *AutoTuner) executeRule(ctx context.Context, rule *TuningRule, beforeMetrics *PerformanceMetrics) {
	at.mutex.Lock()
	at.activeTuning++
	at.mutex.Unlock()

	defer func() {
		at.mutex.Lock()
		at.activeTuning--
		at.mutex.Unlock()
	}()

	startTime := time.Now()
	execution := &TuningExecution{
		RuleID:        rule.ID,
		Actions:       rule.Actions,
		Timestamp:     startTime,
		BeforeMetrics: beforeMetrics,
		Impact:        make(map[string]float64),
	}

	at.logger.WithFields(logrus.Fields{
		"rule_id":   rule.ID,
		"rule_name": rule.Name,
		"actions":   rule.Actions,
	}).Info("Executing tuning rule")

	var lastErr error

	// 执行所有动作
	for _, action := range rule.Actions {
		if err := at.executeAction(ctx, action, beforeMetrics); err != nil {
			lastErr = err
			at.logger.WithFields(logrus.Fields{
				"rule_id": rule.ID,
				"action":  action,
				"error":   err.Error(),
			}).Error("Tuning action failed")
		}
	}

	execution.Duration = time.Since(startTime)
	execution.Success = lastErr == nil

	if lastErr != nil {
		execution.Error = lastErr.Error()
	}

	// 等待一段时间后收集指标以评估影响
	time.Sleep(10 * time.Second)
	afterMetrics := at.monitor.GetCurrentMetrics()
	if afterMetrics != nil {
		execution.AfterMetrics = afterMetrics
		execution.Impact = at.calculateImpact(beforeMetrics, afterMetrics)
	}

	// 记录执行结果
	at.mutex.Lock()
	at.executions = append(at.executions, execution)
	at.mutex.Unlock()

	at.logger.WithFields(logrus.Fields{
		"rule_id":  rule.ID,
		"success":  execution.Success,
		"duration": execution.Duration,
		"impact":   execution.Impact,
	}).Info("Tuning rule execution completed")
}

// executeAction 执行动作
func (at *AutoTuner) executeAction(ctx context.Context, action TuningAction, metrics *PerformanceMetrics) error {
	at.mutex.RLock()
	handler, exists := at.actionHandlers[action]
	at.mutex.RUnlock()

	if !exists {
		return fmt.Errorf("no handler registered for action: %s", action)
	}

	if at.config.DryRun {
		at.logger.WithField("action", action).Info("Dry run: would execute action")
		return nil
	}

	return handler.Execute(ctx, metrics)
}

// calculateImpact 计算影响
func (at *AutoTuner) calculateImpact(before, after *PerformanceMetrics) map[string]float64 {
	impact := make(map[string]float64)

	if before.CPUUsage > 0 {
		impact["cpu_usage"] = (after.CPUUsage - before.CPUUsage) / before.CPUUsage
	}
	if before.MemoryUsage > 0 {
		impact["memory_usage"] = (after.MemoryUsage - before.MemoryUsage) / before.MemoryUsage
	}
	if before.GoroutineCount > 0 {
		impact["goroutine_count"] = (float64(after.GoroutineCount) - float64(before.GoroutineCount)) / float64(before.GoroutineCount)
	}
	if before.AvgResponseTime > 0 {
		impact["response_time"] = (after.AvgResponseTime - before.AvgResponseTime) / before.AvgResponseTime
	}

	return impact
}

// isInCooldown 检查是否在冷却期
func (at *AutoTuner) isInCooldown(rule *TuningRule) bool {
	at.mutex.RLock()
	defer at.mutex.RUnlock()

	now := time.Now()
	for _, execution := range at.executions {
		if execution.RuleID == rule.ID && now.Sub(execution.Timestamp) < rule.Cooldown {
			return true
		}
	}
	return false
}

// getExecutionCount 获取执行次数
func (at *AutoTuner) getExecutionCount(ruleID string) int {
	at.mutex.RLock()
	defer at.mutex.RUnlock()

	count := 0
	for _, execution := range at.executions {
		if execution.RuleID == ruleID {
			count++
		}
	}
	return count
}

// sortRulesByPriority 按优先级排序规则
func (at *AutoTuner) sortRulesByPriority(rules []*TuningRule) {
	// 简单的冒泡排序，按优先级降序
	for i := 0; i < len(rules)-1; i++ {
		for j := 0; j < len(rules)-i-1; j++ {
			if rules[j].Priority < rules[j+1].Priority {
				rules[j], rules[j+1] = rules[j+1], rules[j]
			}
		}
	}
}

// registerDefaultHandlers 注册默认处理器
func (at *AutoTuner) registerDefaultHandlers() {
	at.RegisterActionHandler(ActionTriggerGC, &GCActionHandler{logger: at.logger})
	at.RegisterActionHandler(ActionOptimizeMemory, &MemoryOptimizeHandler{logger: at.logger})
}

// registerDefaultRules 注册默认规则
func (at *AutoTuner) registerDefaultRules() {
	// 高内存使用率触发GC
	highMemoryRule := &TuningRule{
		ID:          "high_memory_gc",
		Name:        "High Memory Usage GC",
		Description: "Trigger GC when memory usage is high",
		Enabled:     true,
		Conditions: []TuningCondition{
			{
				Metric:   "memory_usage",
				Operator: ">",
				Value:    80.0, // 80%
			},
		},
		Actions:       []TuningAction{ActionTriggerGC},
		Cooldown:      5 * time.Minute,
		MaxExecutions: 10,
		Priority:      100,
	}

	// 高Goroutine数量优化
	highGoroutineRule := &TuningRule{
		ID:          "high_goroutine_optimize",
		Name:        "High Goroutine Count Optimization",
		Description: "Optimize when goroutine count is high",
		Enabled:     true,
		Conditions: []TuningCondition{
			{
				Metric:   "goroutine_count",
				Operator: ">",
				Value:    1000,
			},
		},
		Actions:       []TuningAction{ActionOptimizeMemory},
		Cooldown:      10 * time.Minute,
		MaxExecutions: 5,
		Priority:      80,
	}

	at.RegisterRule(highMemoryRule)
	at.RegisterRule(highGoroutineRule)
}

// GetStats 获取统计信息
func (at *AutoTuner) GetStats() map[string]interface{} {
	at.mutex.RLock()
	defer at.mutex.RUnlock()

	totalExecutions := len(at.executions)
	successfulExecutions := 0
	for _, execution := range at.executions {
		if execution.Success {
			successfulExecutions++
		}
	}

	successRate := 0.0
	if totalExecutions > 0 {
		successRate = float64(successfulExecutions) / float64(totalExecutions)
	}

	return map[string]interface{}{
		"enabled":               at.config.Enabled,
		"total_rules":           len(at.rules),
		"total_handlers":        len(at.actionHandlers),
		"total_executions":      totalExecutions,
		"successful_executions": successfulExecutions,
		"success_rate":          successRate,
		"active_tuning":         at.activeTuning,
	}
}

// 默认动作处理器实现

// GCActionHandler GC动作处理器
type GCActionHandler struct {
	logger *logrus.Logger
}

func (gah *GCActionHandler) Name() string {
	return "gc_action_handler"
}

func (gah *GCActionHandler) Execute(ctx context.Context, metrics *PerformanceMetrics) error {
	gah.logger.Info("Triggering garbage collection")
	runtime.GC()
	return nil
}

func (gah *GCActionHandler) Rollback(ctx context.Context) error {
	// GC操作无法回滚
	return nil
}

func (gah *GCActionHandler) Impact() map[string]float64 {
	return map[string]float64{
		"memory_usage": -0.1, // 预期减少10%内存使用
		"gc_pause_time": 0.05, // 可能增加5%的GC暂停时间
	}
}

// MemoryOptimizeHandler 内存优化处理器
type MemoryOptimizeHandler struct {
	logger *logrus.Logger
}

func (moh *MemoryOptimizeHandler) Name() string {
	return "memory_optimize_handler"
}

func (moh *MemoryOptimizeHandler) Execute(ctx context.Context, metrics *PerformanceMetrics) error {
	moh.logger.Info("Optimizing memory usage")

	// 触发GC
	runtime.GC()

	// 调整GC目标
	if metrics.MemoryUsage > 80 {
		// 降低GC目标以更频繁地回收内存
		debug.SetGCPercent(50)
		moh.logger.Info("Reduced GC target to 50%")
	}

	return nil
}

func (moh *MemoryOptimizeHandler) Rollback(ctx context.Context) error {
	// 恢复默认GC目标
	debug.SetGCPercent(100)
	return nil
}

func (moh *MemoryOptimizeHandler) Impact() map[string]float64 {
	return map[string]float64{
		"memory_usage": -0.15, // 预期减少15%内存使用
		"cpu_usage": 0.05,     // 可能增加5%CPU使用
	}
}
