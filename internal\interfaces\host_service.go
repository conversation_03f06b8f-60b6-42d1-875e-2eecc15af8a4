package interfaces

import (
	"context"
	"time"

	"aiops-platform/internal/model"
)

// HostService 主机服务接口
type HostService interface {
	// 基本CRUD操作
	CreateHost(ctx context.Context, host *model.Host) error
	GetHost(ctx context.Context, id int64) (*model.Host, error)
	GetHostByIP(ctx context.Context, ip string) (*model.Host, error)
	UpdateHost(ctx context.Context, host *model.Host) error
	DeleteHost(ctx context.Context, id int64) error
	ListHosts(ctx context.Context, filter *HostFilter) ([]*model.Host, error)
	
	// 连接管理
	TestConnection(ctx context.Context, host *model.Host) error
	ExecuteCommand(ctx context.Context, hostID int64, command string) (*CommandResult, error)
	ExecuteCommandWithTimeout(ctx context.Context, hostID int64, command string, timeout time.Duration) (*CommandResult, error)
	
	// 批量操作
	BatchExecuteCommand(ctx context.Context, hostIDs []int64, command string) (map[int64]*CommandResult, error)
	
	// 监控相关
	GetHostStatus(ctx context.Context, hostID int64) (*HostStatus, error)
	GetHostMetrics(ctx context.Context, hostID int64) (*HostMetrics, error)
	
	// 文件操作
	UploadFile(ctx context.Context, hostID int64, localPath, remotePath string) error
	DownloadFile(ctx context.Context, hostID int64, remotePath, localPath string) error
	
	// 服务管理
	StartService(ctx context.Context, hostID int64, serviceName string) error
	StopService(ctx context.Context, hostID int64, serviceName string) error
	RestartService(ctx context.Context, hostID int64, serviceName string) error
	GetServiceStatus(ctx context.Context, hostID int64, serviceName string) (*ServiceStatus, error)
}

// HostFilter 主机过滤器
type HostFilter struct {
	Name     string
	IP       string
	Status   string
	Category string
	Tags     []string
	Limit    int
	Offset   int
}

// CommandResult 命令执行结果
type CommandResult struct {
	Success    bool      `json:"success"`
	Output     string    `json:"output"`
	Error      string    `json:"error"`
	ExitCode   int       `json:"exit_code"`
	Duration   time.Duration `json:"duration"`
	ExecutedAt time.Time `json:"executed_at"`
}

// HostStatus 主机状态
type HostStatus struct {
	Online      bool      `json:"online"`
	LastSeen    time.Time `json:"last_seen"`
	CPUUsage    float64   `json:"cpu_usage"`
	MemoryUsage float64   `json:"memory_usage"`
	DiskUsage   float64   `json:"disk_usage"`
	LoadAverage float64   `json:"load_average"`
}

// HostMetrics 主机指标
type HostMetrics struct {
	CPU    *CPUMetrics    `json:"cpu"`
	Memory *MemoryMetrics `json:"memory"`
	Disk   *DiskMetrics   `json:"disk"`
	Network *NetworkMetrics `json:"network"`
	Timestamp time.Time    `json:"timestamp"`
}

// CPUMetrics CPU指标
type CPUMetrics struct {
	Usage     float64 `json:"usage"`
	UserTime  float64 `json:"user_time"`
	SystemTime float64 `json:"system_time"`
	IdleTime  float64 `json:"idle_time"`
	Cores     int     `json:"cores"`
}

// MemoryMetrics 内存指标
type MemoryMetrics struct {
	Total     uint64  `json:"total"`
	Used      uint64  `json:"used"`
	Free      uint64  `json:"free"`
	Available uint64  `json:"available"`
	Usage     float64 `json:"usage"`
}

// DiskMetrics 磁盘指标
type DiskMetrics struct {
	Total     uint64  `json:"total"`
	Used      uint64  `json:"used"`
	Free      uint64  `json:"free"`
	Usage     float64 `json:"usage"`
	IOReads   uint64  `json:"io_reads"`
	IOWrites  uint64  `json:"io_writes"`
}

// NetworkMetrics 网络指标
type NetworkMetrics struct {
	BytesReceived uint64 `json:"bytes_received"`
	BytesSent     uint64 `json:"bytes_sent"`
	PacketsReceived uint64 `json:"packets_received"`
	PacketsSent   uint64 `json:"packets_sent"`
	Errors        uint64 `json:"errors"`
	Drops         uint64 `json:"drops"`
}

// ServiceStatus 服务状态
type ServiceStatus struct {
	Name      string    `json:"name"`
	Status    string    `json:"status"`
	PID       int       `json:"pid"`
	StartTime time.Time `json:"start_time"`
	Memory    uint64    `json:"memory"`
	CPU       float64   `json:"cpu"`
}
