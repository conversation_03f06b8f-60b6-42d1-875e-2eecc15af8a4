package agent

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ExecutorManager 执行器管理器
type ExecutorManager struct {
	config    *ExecutorManagerConfig
	logger    *logrus.Logger
	executors map[string]*ManagedExecutor
	pools     map[string]*ExecutorPool
	metrics   *ExecutorMetrics
	mutex     sync.RWMutex
	isRunning bool
	stopChan  chan struct{}
}

// ExecutorManagerConfig 执行器管理器配置
type ExecutorManagerConfig struct {
	MaxExecutors        int           `json:"max_executors"`
	DefaultPoolSize     int           `json:"default_pool_size"`
	HealthCheckInterval time.Duration `json:"health_check_interval"`
	IdleTimeout         time.Duration `json:"idle_timeout"`
	EnableAutoScaling   bool          `json:"enable_auto_scaling"`
	ScaleUpThreshold    float64       `json:"scale_up_threshold"`
	ScaleDownThreshold  float64       `json:"scale_down_threshold"`
	MinPoolSize         int           `json:"min_pool_size"`
	MaxPoolSize         int           `json:"max_pool_size"`
}

// ManagedExecutor 托管执行器
type ManagedExecutor struct {
	*TaskExecutor
	Pool            *ExecutorPool          `json:"pool"`
	CreatedAt       time.Time              `json:"created_at"`
	LastHealthCheck time.Time              `json:"last_health_check"`
	FailureCount    int                    `json:"failure_count"`
	MaxFailures     int                    `json:"max_failures"`
	RestartCount    int                    `json:"restart_count"`
	IsManaged       bool                   `json:"is_managed"`
	Config          map[string]interface{} `json:"config"`
}

// ExecutorPool 执行器池
type ExecutorPool struct {
	ID          string             `json:"id"`
	Name        string             `json:"name"`
	Type        string             `json:"type"` // local, sandbox, remote
	Executors   []*ManagedExecutor `json:"executors"`
	MinSize     int                `json:"min_size"`
	MaxSize     int                `json:"max_size"`
	CurrentSize int                `json:"current_size"`
	TargetSize  int                `json:"target_size"`
	LoadFactor  float64            `json:"load_factor"`
	Status      string             `json:"status"` // active, scaling, draining, stopped
	CreatedAt   time.Time          `json:"created_at"`
	LastScaled  time.Time          `json:"last_scaled"`
	mutex       sync.RWMutex
}

// ExecutorMetrics 执行器指标
type ExecutorMetrics struct {
	TotalExecutors  int                        `json:"total_executors"`
	ActiveExecutors int                        `json:"active_executors"`
	IdleExecutors   int                        `json:"idle_executors"`
	FailedExecutors int                        `json:"failed_executors"`
	TotalPools      int                        `json:"total_pools"`
	AvgLoadFactor   float64                    `json:"avg_load_factor"`
	TotalExecutions int64                      `json:"total_executions"`
	SuccessRate     float64                    `json:"success_rate"`
	AvgResponseTime time.Duration              `json:"avg_response_time"`
	LastUpdateTime  time.Time                  `json:"last_update_time"`
	PoolMetrics     map[string]*PoolMetrics    `json:"pool_metrics"`
	ExecutorMetrics map[string]*ExecutorMetric `json:"executor_metrics"`
}

// PoolMetrics 池指标
type PoolMetrics struct {
	PoolID           string        `json:"pool_id"`
	ExecutorCount    int           `json:"executor_count"`
	ActiveTasks      int           `json:"active_tasks"`
	QueuedTasks      int           `json:"queued_tasks"`
	LoadFactor       float64       `json:"load_factor"`
	AvgResponseTime  time.Duration `json:"avg_response_time"`
	ThroughputPerMin float64       `json:"throughput_per_min"`
	ErrorRate        float64       `json:"error_rate"`
}

// ExecutorMetric 执行器指标
type ExecutorMetric struct {
	ExecutorID    string        `json:"executor_id"`
	Status        string        `json:"status"`
	ActiveTasks   int           `json:"active_tasks"`
	TotalExecuted int64         `json:"total_executed"`
	SuccessRate   float64       `json:"success_rate"`
	AvgDuration   time.Duration `json:"avg_duration"`
	LastActivity  time.Time     `json:"last_activity"`
	HealthStatus  string        `json:"health_status"`
	FailureCount  int           `json:"failure_count"`
	RestartCount  int           `json:"restart_count"`
}

// NewExecutorManager 创建执行器管理器
func NewExecutorManager(config *ExecutorManagerConfig, logger *logrus.Logger) *ExecutorManager {
	if config == nil {
		config = DefaultExecutorManagerConfig()
	}

	return &ExecutorManager{
		config:    config,
		logger:    logger,
		executors: make(map[string]*ManagedExecutor),
		pools:     make(map[string]*ExecutorPool),
		metrics:   NewExecutorMetrics(),
		isRunning: false,
		stopChan:  make(chan struct{}),
	}
}

// DefaultExecutorManagerConfig 默认执行器管理器配置
func DefaultExecutorManagerConfig() *ExecutorManagerConfig {
	return &ExecutorManagerConfig{
		MaxExecutors:        50,
		DefaultPoolSize:     5,
		HealthCheckInterval: 30 * time.Second,
		IdleTimeout:         10 * time.Minute,
		EnableAutoScaling:   true,
		ScaleUpThreshold:    0.8, // 80%负载时扩容
		ScaleDownThreshold:  0.3, // 30%负载时缩容
		MinPoolSize:         1,
		MaxPoolSize:         20,
	}
}

// NewExecutorMetrics 创建执行器指标
func NewExecutorMetrics() *ExecutorMetrics {
	return &ExecutorMetrics{
		LastUpdateTime:  time.Now(),
		PoolMetrics:     make(map[string]*PoolMetrics),
		ExecutorMetrics: make(map[string]*ExecutorMetric),
	}
}

// Start 启动执行器管理器
func (em *ExecutorManager) Start(ctx context.Context) error {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	if em.isRunning {
		return fmt.Errorf("executor manager is already running")
	}

	em.logger.Info("Starting executor manager")

	// 创建默认池
	em.createDefaultPools()

	// 启动管理协程
	go em.managementRoutine(ctx)
	go em.healthCheckRoutine(ctx)
	go em.metricsRoutine(ctx)

	if em.config.EnableAutoScaling {
		go em.autoScalingRoutine(ctx)
	}

	em.isRunning = true
	em.logger.Info("Executor manager started successfully")

	return nil
}

// Stop 停止执行器管理器
func (em *ExecutorManager) Stop(ctx context.Context) error {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	if !em.isRunning {
		return nil
	}

	em.logger.Info("Stopping executor manager")

	// 停止所有执行器
	for _, executor := range em.executors {
		em.stopExecutor(executor)
	}

	close(em.stopChan)
	em.isRunning = false

	em.logger.Info("Executor manager stopped")
	return nil
}

// CreatePool 创建执行器池
func (em *ExecutorManager) CreatePool(poolConfig *PoolConfig) (*ExecutorPool, error) {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	if _, exists := em.pools[poolConfig.ID]; exists {
		return nil, fmt.Errorf("pool already exists: %s", poolConfig.ID)
	}

	pool := &ExecutorPool{
		ID:          poolConfig.ID,
		Name:        poolConfig.Name,
		Type:        poolConfig.Type,
		Executors:   make([]*ManagedExecutor, 0),
		MinSize:     poolConfig.MinSize,
		MaxSize:     poolConfig.MaxSize,
		CurrentSize: 0,
		TargetSize:  poolConfig.MinSize,
		LoadFactor:  0.0,
		Status:      "active",
		CreatedAt:   time.Now(),
		LastScaled:  time.Now(),
	}

	em.pools[pool.ID] = pool

	// 创建初始执行器
	for i := 0; i < pool.MinSize; i++ {
		if err := em.addExecutorToPool(pool); err != nil {
			em.logger.WithError(err).WithField("pool_id", pool.ID).Error("Failed to add executor to pool")
		}
	}

	em.logger.WithFields(logrus.Fields{
		"pool_id":   pool.ID,
		"pool_type": pool.Type,
		"min_size":  pool.MinSize,
		"max_size":  pool.MaxSize,
	}).Info("Executor pool created")

	return pool, nil
}

// GetPool 获取执行器池
func (em *ExecutorManager) GetPool(poolID string) (*ExecutorPool, error) {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	pool, exists := em.pools[poolID]
	if !exists {
		return nil, fmt.Errorf("pool not found: %s", poolID)
	}

	return pool, nil
}

// ScalePool 扩缩容执行器池
func (em *ExecutorManager) ScalePool(poolID string, targetSize int) error {
	em.mutex.Lock()
	defer em.mutex.Unlock()

	pool, exists := em.pools[poolID]
	if !exists {
		return fmt.Errorf("pool not found: %s", poolID)
	}

	if targetSize < pool.MinSize {
		targetSize = pool.MinSize
	}
	if targetSize > pool.MaxSize {
		targetSize = pool.MaxSize
	}

	pool.mutex.Lock()
	defer pool.mutex.Unlock()

	currentSize := len(pool.Executors)

	if targetSize > currentSize {
		// 扩容
		for i := currentSize; i < targetSize; i++ {
			if err := em.addExecutorToPool(pool); err != nil {
				em.logger.WithError(err).WithField("pool_id", poolID).Error("Failed to scale up pool")
				break
			}
		}
	} else if targetSize < currentSize {
		// 缩容
		for i := currentSize; i > targetSize; i-- {
			if err := em.removeExecutorFromPool(pool); err != nil {
				em.logger.WithError(err).WithField("pool_id", poolID).Error("Failed to scale down pool")
				break
			}
		}
	}

	pool.TargetSize = targetSize
	pool.CurrentSize = len(pool.Executors)
	pool.LastScaled = time.Now()

	em.logger.WithFields(logrus.Fields{
		"pool_id":      poolID,
		"target_size":  targetSize,
		"current_size": pool.CurrentSize,
	}).Info("Pool scaled")

	return nil
}

// GetExecutor 获取执行器
func (em *ExecutorManager) GetExecutor(executorID string) (*ManagedExecutor, error) {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	executor, exists := em.executors[executorID]
	if !exists {
		return nil, fmt.Errorf("executor not found: %s", executorID)
	}

	return executor, nil
}

// GetAvailableExecutor 获取可用执行器
func (em *ExecutorManager) GetAvailableExecutor(taskType string) (*ManagedExecutor, error) {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	for _, executor := range em.executors {
		if executor.Status == "idle" &&
			executor.ActiveTasks < executor.MaxConcurrency &&
			em.executorSupportsTaskType(executor, taskType) {
			return executor, nil
		}
	}

	return nil, fmt.Errorf("no available executor for task type: %s", taskType)
}

// GetMetrics 获取执行器指标
func (em *ExecutorManager) GetMetrics() *ExecutorMetrics {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	return em.metrics
}

// GetStatus 获取执行器管理器状态
func (em *ExecutorManager) GetStatus() *ExecutorManagerStatus {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	pools := make([]*ExecutorPool, 0, len(em.pools))
	for _, pool := range em.pools {
		pools = append(pools, pool)
	}

	executors := make([]*ManagedExecutor, 0, len(em.executors))
	for _, executor := range em.executors {
		executors = append(executors, executor)
	}

	return &ExecutorManagerStatus{
		IsRunning:      em.isRunning,
		TotalPools:     len(em.pools),
		TotalExecutors: len(em.executors),
		Pools:          pools,
		Executors:      executors,
		Metrics:        em.metrics,
		Config:         em.config,
	}
}

// PoolConfig 池配置
type PoolConfig struct {
	ID      string `json:"id"`
	Name    string `json:"name"`
	Type    string `json:"type"`
	MinSize int    `json:"min_size"`
	MaxSize int    `json:"max_size"`
}

// ExecutorManagerStatus 执行器管理器状态
type ExecutorManagerStatus struct {
	IsRunning      bool                   `json:"is_running"`
	TotalPools     int                    `json:"total_pools"`
	TotalExecutors int                    `json:"total_executors"`
	Pools          []*ExecutorPool        `json:"pools"`
	Executors      []*ManagedExecutor     `json:"executors"`
	Metrics        *ExecutorMetrics       `json:"metrics"`
	Config         *ExecutorManagerConfig `json:"config"`
}

// 执行器管理器核心方法

// createDefaultPools 创建默认池
func (em *ExecutorManager) createDefaultPools() {
	// 创建本地执行器池
	localPoolConfig := &PoolConfig{
		ID:      "local-pool",
		Name:    "Local Executor Pool",
		Type:    "local",
		MinSize: 2,
		MaxSize: 10,
	}

	if _, err := em.CreatePool(localPoolConfig); err != nil {
		em.logger.WithError(err).Error("Failed to create local pool")
	}

	// 创建沙箱执行器池
	sandboxPoolConfig := &PoolConfig{
		ID:      "sandbox-pool",
		Name:    "Sandbox Executor Pool",
		Type:    "sandbox",
		MinSize: 1,
		MaxSize: 5,
	}

	if _, err := em.CreatePool(sandboxPoolConfig); err != nil {
		em.logger.WithError(err).Error("Failed to create sandbox pool")
	}
}

// addExecutorToPool 向池中添加执行器
func (em *ExecutorManager) addExecutorToPool(pool *ExecutorPool) error {
	executorID := fmt.Sprintf("%s-executor-%d", pool.ID, len(pool.Executors)+1)

	// 创建基础执行器
	baseExecutor := &TaskExecutor{
		ID:             executorID,
		Type:           pool.Type,
		Status:         "idle",
		Capabilities:   em.getExecutorCapabilities(pool.Type),
		MaxConcurrency: 3,
		ActiveTasks:    0,
		TotalExecuted:  0,
		SuccessRate:    100.0,
		LastActivity:   time.Now(),
		HealthStatus:   "healthy",
		Config:         make(map[string]interface{}),
	}

	// 创建托管执行器
	managedExecutor := &ManagedExecutor{
		TaskExecutor:    baseExecutor,
		Pool:            pool,
		CreatedAt:       time.Now(),
		LastHealthCheck: time.Now(),
		FailureCount:    0,
		MaxFailures:     3,
		RestartCount:    0,
		IsManaged:       true,
		Config:          make(map[string]interface{}),
	}

	// 添加到池和全局映射
	pool.Executors = append(pool.Executors, managedExecutor)
	em.executors[executorID] = managedExecutor

	em.logger.WithFields(logrus.Fields{
		"executor_id": executorID,
		"pool_id":     pool.ID,
		"pool_type":   pool.Type,
	}).Info("Executor added to pool")

	return nil
}

// removeExecutorFromPool 从池中移除执行器
func (em *ExecutorManager) removeExecutorFromPool(pool *ExecutorPool) error {
	if len(pool.Executors) == 0 {
		return fmt.Errorf("pool is empty")
	}

	// 找到空闲的执行器
	var targetExecutor *ManagedExecutor
	for i, executor := range pool.Executors {
		if executor.Status == "idle" && executor.ActiveTasks == 0 {
			targetExecutor = executor
			// 从池中移除
			pool.Executors = append(pool.Executors[:i], pool.Executors[i+1:]...)
			break
		}
	}

	if targetExecutor == nil {
		return fmt.Errorf("no idle executor to remove")
	}

	// 停止执行器
	em.stopExecutor(targetExecutor)

	// 从全局映射中移除
	delete(em.executors, targetExecutor.ID)

	em.logger.WithFields(logrus.Fields{
		"executor_id": targetExecutor.ID,
		"pool_id":     pool.ID,
	}).Info("Executor removed from pool")

	return nil
}

// stopExecutor 停止执行器
func (em *ExecutorManager) stopExecutor(executor *ManagedExecutor) {
	executor.mutex.Lock()
	defer executor.mutex.Unlock()

	executor.Status = "stopped"
	executor.HealthStatus = "offline"

	em.logger.WithField("executor_id", executor.ID).Info("Executor stopped")
}

// getExecutorCapabilities 获取执行器能力
func (em *ExecutorManager) getExecutorCapabilities(executorType string) []string {
	switch executorType {
	case "local":
		return []string{"command", "script", "file"}
	case "sandbox":
		return []string{"command", "script"}
	case "remote":
		return []string{"command", "script", "file", "network"}
	default:
		return []string{"command"}
	}
}

// executorSupportsTaskType 检查执行器是否支持任务类型
func (em *ExecutorManager) executorSupportsTaskType(executor *ManagedExecutor, taskType string) bool {
	for _, capability := range executor.Capabilities {
		if capability == taskType {
			return true
		}
	}
	return false
}

// managementRoutine 管理协程
func (em *ExecutorManager) managementRoutine(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-em.stopChan:
			return
		case <-ticker.C:
			em.performManagementTasks()
		}
	}
}

// performManagementTasks 执行管理任务
func (em *ExecutorManager) performManagementTasks() {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	// 清理失败的执行器
	em.cleanupFailedExecutors()

	// 重启需要重启的执行器
	em.restartFailedExecutors()

	// 更新池负载因子
	em.updatePoolLoadFactors()
}

// cleanupFailedExecutors 清理失败的执行器
func (em *ExecutorManager) cleanupFailedExecutors() {
	for _, executor := range em.executors {
		if executor.FailureCount >= executor.MaxFailures {
			em.logger.WithFields(logrus.Fields{
				"executor_id":   executor.ID,
				"failure_count": executor.FailureCount,
			}).Warn("Executor exceeded max failures, marking for cleanup")

			executor.Status = "failed"
			executor.HealthStatus = "unhealthy"
		}
	}
}

// restartFailedExecutors 重启失败的执行器
func (em *ExecutorManager) restartFailedExecutors() {
	for _, executor := range em.executors {
		if executor.Status == "failed" && executor.RestartCount < 3 {
			em.logger.WithField("executor_id", executor.ID).Info("Restarting failed executor")

			executor.mutex.Lock()
			executor.Status = "idle"
			executor.HealthStatus = "healthy"
			executor.FailureCount = 0
			executor.RestartCount++
			executor.LastActivity = time.Now()
			executor.mutex.Unlock()
		}
	}
}

// updatePoolLoadFactors 更新池负载因子
func (em *ExecutorManager) updatePoolLoadFactors() {
	for _, pool := range em.pools {
		pool.mutex.Lock()

		if len(pool.Executors) == 0 {
			pool.LoadFactor = 0.0
		} else {
			totalCapacity := 0
			totalLoad := 0

			for _, executor := range pool.Executors {
				totalCapacity += executor.MaxConcurrency
				totalLoad += executor.ActiveTasks
			}

			if totalCapacity > 0 {
				pool.LoadFactor = float64(totalLoad) / float64(totalCapacity)
			}
		}

		pool.mutex.Unlock()
	}
}

// healthCheckRoutine 健康检查协程
func (em *ExecutorManager) healthCheckRoutine(ctx context.Context) {
	ticker := time.NewTicker(em.config.HealthCheckInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-em.stopChan:
			return
		case <-ticker.C:
			em.performHealthChecks()
		}
	}
}

// performHealthChecks 执行健康检查
func (em *ExecutorManager) performHealthChecks() {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	for _, executor := range em.executors {
		em.checkExecutorHealth(executor)
	}
}

// checkExecutorHealth 检查执行器健康状态
func (em *ExecutorManager) checkExecutorHealth(executor *ManagedExecutor) {
	executor.mutex.Lock()
	defer executor.mutex.Unlock()

	now := time.Now()

	// 检查是否长时间无活动
	if now.Sub(executor.LastActivity) > em.config.IdleTimeout {
		executor.HealthStatus = "degraded"
		em.logger.WithField("executor_id", executor.ID).Warn("Executor has been idle for too long")
	}

	// 检查是否响应正常（简化实现）
	if executor.Status == "error" {
		executor.FailureCount++
		if executor.FailureCount >= executor.MaxFailures {
			executor.HealthStatus = "unhealthy"
		}
	} else {
		executor.HealthStatus = "healthy"
	}

	executor.LastHealthCheck = now
}

// autoScalingRoutine 自动扩缩容协程
func (em *ExecutorManager) autoScalingRoutine(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-em.stopChan:
			return
		case <-ticker.C:
			em.performAutoScaling()
		}
	}
}

// performAutoScaling 执行自动扩缩容
func (em *ExecutorManager) performAutoScaling() {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	for _, pool := range em.pools {
		pool.mutex.RLock()
		loadFactor := pool.LoadFactor
		currentSize := len(pool.Executors)
		pool.mutex.RUnlock()

		// 扩容检查
		if loadFactor > em.config.ScaleUpThreshold && currentSize < pool.MaxSize {
			targetSize := currentSize + 1
			if err := em.ScalePool(pool.ID, targetSize); err != nil {
				em.logger.WithError(err).WithField("pool_id", pool.ID).Error("Failed to scale up pool")
			} else {
				em.logger.WithFields(logrus.Fields{
					"pool_id":     pool.ID,
					"load_factor": loadFactor,
					"new_size":    targetSize,
				}).Info("Pool scaled up automatically")
			}
		}

		// 缩容检查
		if loadFactor < em.config.ScaleDownThreshold && currentSize > pool.MinSize {
			targetSize := currentSize - 1
			if err := em.ScalePool(pool.ID, targetSize); err != nil {
				em.logger.WithError(err).WithField("pool_id", pool.ID).Error("Failed to scale down pool")
			} else {
				em.logger.WithFields(logrus.Fields{
					"pool_id":     pool.ID,
					"load_factor": loadFactor,
					"new_size":    targetSize,
				}).Info("Pool scaled down automatically")
			}
		}
	}
}

// metricsRoutine 指标更新协程
func (em *ExecutorManager) metricsRoutine(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-em.stopChan:
			return
		case <-ticker.C:
			em.updateMetrics()
		}
	}
}

// updateMetrics 更新指标
func (em *ExecutorManager) updateMetrics() {
	em.mutex.RLock()
	defer em.mutex.RUnlock()

	// 更新基础指标
	em.metrics.TotalExecutors = len(em.executors)
	em.metrics.TotalPools = len(em.pools)

	activeCount := 0
	idleCount := 0
	failedCount := 0
	totalLoadFactor := 0.0

	// 统计执行器状态
	for _, executor := range em.executors {
		switch executor.Status {
		case "busy":
			activeCount++
		case "idle":
			idleCount++
		case "failed", "error":
			failedCount++
		}
	}

	// 统计池负载
	for _, pool := range em.pools {
		totalLoadFactor += pool.LoadFactor
	}

	em.metrics.ActiveExecutors = activeCount
	em.metrics.IdleExecutors = idleCount
	em.metrics.FailedExecutors = failedCount

	if len(em.pools) > 0 {
		em.metrics.AvgLoadFactor = totalLoadFactor / float64(len(em.pools))
	}

	em.metrics.LastUpdateTime = time.Now()
}
