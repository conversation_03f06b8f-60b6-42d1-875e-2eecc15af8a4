package service

import (
	"context"
	"fmt"
	"os"
	"time"

	"aiops-platform/internal/config"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedAIIntegration 增强AI集成管理器
type EnhancedAIIntegration struct {
	db                    *gorm.DB
	logger                *logrus.Logger
	config                *config.Config
	
	// 核心服务组件
	enhancedDeepSeek      *EnhancedDeepSeekService
	resultCollector       *ExecutionResultCollector
	resultAnalyzer        *AIResultAnalyzer
	resultRenderer        *AIResultRenderer
	fallbackManager       *IntelligentFallbackManager
	
	// WebSocket组件
	websocketManager      *WebSocketManager
	enhancedHandler       *EnhancedWebSocketHandler
	
	// 执行引擎
	executionEngine       *UnifiedExecutionEngine
	
	// 其他服务
	hostService           HostService
	aiService             AIService
}

// NewEnhancedAIIntegration 创建增强AI集成管理器
func NewEnhancedAIIntegration(
	db *gorm.DB,
	logger *logrus.Logger,
	cfg *config.Config,
	executionEngine *UnifiedExecutionEngine,
	hostService HostService,
	aiService AIService,
) (*EnhancedAIIntegration, error) {
	
	integration := &EnhancedAIIntegration{
		db:              db,
		logger:          logger,
		config:          cfg,
		executionEngine: executionEngine,
		hostService:     hostService,
		aiService:       aiService,
	}
	
	// 初始化所有组件
	if err := integration.initializeComponents(); err != nil {
		return nil, fmt.Errorf("初始化增强AI组件失败: %w", err)
	}
	
	logger.Info("🚀 增强AI集成管理器初始化完成")
	
	return integration, nil
}

// initializeComponents 初始化所有组件
func (eai *EnhancedAIIntegration) initializeComponents() error {
	// 1. 初始化增强DeepSeek服务
	if err := eai.initializeEnhancedDeepSeek(); err != nil {
		return fmt.Errorf("初始化增强DeepSeek服务失败: %w", err)
	}
	
	// 2. 初始化结果处理组件
	eai.initializeResultProcessors()
	
	// 3. 初始化降级管理器
	eai.initializeFallbackManager()
	
	// 4. 初始化WebSocket组件
	if err := eai.initializeWebSocketComponents(); err != nil {
		return fmt.Errorf("初始化WebSocket组件失败: %w", err)
	}
	
	eai.logger.Info("✅ 所有增强AI组件初始化完成")
	
	return nil
}

// initializeEnhancedDeepSeek 初始化增强DeepSeek服务
func (eai *EnhancedAIIntegration) initializeEnhancedDeepSeek() error {
	// 从环境变量或配置获取API密钥
	apiKey := os.Getenv("DEEPSEEK_API_KEY")
	if apiKey == "" {
		apiKey = eai.config.DeepSeek.APIKey
	}
	
	if apiKey == "" {
		return fmt.Errorf("DeepSeek API密钥未配置")
	}
	
	// 创建增强配置
	enhancedConfig := &EnhancedDeepSeekConfig{
		APIKey:            apiKey,
		BaseURL:           eai.config.DeepSeek.APIURL,
		Model:             eai.config.DeepSeek.Model,
		MaxTokens:         eai.config.DeepSeek.MaxContextTokens,
		Temperature:       eai.config.DeepSeek.Temperature,
		TopP:              eai.config.DeepSeek.TopP,
		DefaultTimeout:    eai.config.DeepSeek.Timeout,
		ComplexTimeout:    120 * time.Second, // 复杂操作2分钟超时
		MaxRetries:        eai.config.DeepSeek.MaxRetries,
		RetryDelay:        2 * time.Second,
		BackoffMultiplier: 2.0,
		EnableSmartRetry:  true,
		RetryableErrors: []string{
			"context deadline exceeded",
			"timeout",
			"connection reset",
			"temporary failure",
			"rate limit",
			"service unavailable",
		},
	}
	
	eai.enhancedDeepSeek = NewEnhancedDeepSeekService(enhancedConfig, eai.logger)
	
	eai.logger.WithFields(logrus.Fields{
		"base_url":        enhancedConfig.BaseURL,
		"model":           enhancedConfig.Model,
		"default_timeout": enhancedConfig.DefaultTimeout,
		"complex_timeout": enhancedConfig.ComplexTimeout,
		"max_retries":     enhancedConfig.MaxRetries,
	}).Info("✅ 增强DeepSeek服务初始化完成")
	
	return nil
}

// initializeResultProcessors 初始化结果处理组件
func (eai *EnhancedAIIntegration) initializeResultProcessors() {
	// 创建结果收集器
	eai.resultCollector = NewExecutionResultCollector(eai.db, eai.logger)
	
	// 创建结果分析器
	eai.resultAnalyzer = NewAIResultAnalyzer(eai.enhancedDeepSeek, eai.logger)
	
	// 创建结果渲染器
	eai.resultRenderer = NewAIResultRenderer(eai.enhancedDeepSeek, eai.logger)
	
	eai.logger.Info("✅ 结果处理组件初始化完成")
}

// initializeFallbackManager 初始化降级管理器
func (eai *EnhancedAIIntegration) initializeFallbackManager() {
	eai.fallbackManager = NewIntelligentFallbackManager(eai.logger)
	
	eai.logger.Info("✅ 智能降级管理器初始化完成")
}

// initializeWebSocketComponents 初始化WebSocket组件
func (eai *EnhancedAIIntegration) initializeWebSocketComponents() error {
	// 创建WebSocket管理器
	eai.websocketManager = NewWebSocketManager(eai.logger, eai.db)
	
	// 设置传统AI服务（用于降级）
	if eai.aiService != nil {
		eai.websocketManager.SetAIService(eai.aiService)
	}
	
	// 创建增强WebSocket处理器
	eai.enhancedHandler = NewEnhancedWebSocketHandler(
		eai.db,
		eai.logger,
		eai.enhancedDeepSeek,
		eai.executionEngine,
		eai.hostService,
	)
	
	// 设置增强处理器
	eai.websocketManager.SetEnhancedHandler(eai.enhancedHandler)
	
	eai.logger.Info("✅ WebSocket组件初始化完成")
	
	return nil
}

// GetWebSocketManager 获取WebSocket管理器
func (eai *EnhancedAIIntegration) GetWebSocketManager() *WebSocketManager {
	return eai.websocketManager
}

// GetEnhancedHandler 获取增强处理器
func (eai *EnhancedAIIntegration) GetEnhancedHandler() *EnhancedWebSocketHandler {
	return eai.enhancedHandler
}

// GetFallbackManager 获取降级管理器
func (eai *EnhancedAIIntegration) GetFallbackManager() *IntelligentFallbackManager {
	return eai.fallbackManager
}

// GetEnhancedDeepSeek 获取增强DeepSeek服务
func (eai *EnhancedAIIntegration) GetEnhancedDeepSeek() *EnhancedDeepSeekService {
	return eai.enhancedDeepSeek
}

// ProcessMessage 处理消息（主要入口点）
func (eai *EnhancedAIIntegration) ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	if eai.enhancedHandler == nil {
		return nil, fmt.Errorf("增强处理器未初始化")
	}

	// 使用增强处理器处理消息
	if ctx == nil {
		ctx = context.Background()
	}

	return eai.enhancedHandler.ProcessMessage(ctx, req)
}

// GetHealthStatus 获取健康状态
func (eai *EnhancedAIIntegration) GetHealthStatus() map[string]interface{} {
	status := map[string]interface{}{
		"enhanced_deepseek":  eai.enhancedDeepSeek != nil,
		"result_collector":   eai.resultCollector != nil,
		"result_analyzer":    eai.resultAnalyzer != nil,
		"result_renderer":    eai.resultRenderer != nil,
		"fallback_manager":   eai.fallbackManager != nil,
		"websocket_manager":  eai.websocketManager != nil,
		"enhanced_handler":   eai.enhancedHandler != nil,
		"execution_engine":   eai.executionEngine != nil,
		"host_service":       eai.hostService != nil,
		"ai_service":         eai.aiService != nil,
	}
	
	// 添加降级管理器统计信息
	if eai.fallbackManager != nil {
		fallbackStats := eai.fallbackManager.GetStatistics()
		status["fallback_stats"] = fallbackStats
	}
	
	return status
}

// GetStatistics 获取统计信息
func (eai *EnhancedAIIntegration) GetStatistics() map[string]interface{} {
	stats := map[string]interface{}{
		"timestamp": time.Now(),
		"uptime":    time.Since(time.Now()), // 这里应该记录实际的启动时间
	}
	
	// 添加降级管理器统计
	if eai.fallbackManager != nil {
		stats["fallback"] = eai.fallbackManager.GetStatistics()
	}
	
	// 添加WebSocket连接统计
	if eai.websocketManager != nil {
		// 这里可以添加WebSocket连接统计
		stats["websocket"] = map[string]interface{}{
			"active_connections": "N/A", // 需要在WebSocketManager中实现
		}
	}
	
	return stats
}

// Shutdown 关闭所有组件
func (eai *EnhancedAIIntegration) Shutdown() error {
	eai.logger.Info("🔄 开始关闭增强AI集成组件...")
	
	// 这里可以添加各个组件的清理逻辑
	// 例如：关闭连接、保存状态等
	
	eai.logger.Info("✅ 增强AI集成组件关闭完成")
	
	return nil
}

// ValidateConfiguration 验证配置
func (eai *EnhancedAIIntegration) ValidateConfiguration() error {
	if eai.config == nil {
		return fmt.Errorf("配置为空")
	}
	
	if eai.config.DeepSeek.APIKey == "" && os.Getenv("DEEPSEEK_API_KEY") == "" {
		return fmt.Errorf("DeepSeek API密钥未配置")
	}
	
	if eai.config.DeepSeek.APIURL == "" {
		return fmt.Errorf("DeepSeek API URL未配置")
	}
	
	if eai.config.DeepSeek.Model == "" {
		return fmt.Errorf("DeepSeek模型未配置")
	}
	
	eai.logger.Info("✅ 配置验证通过")
	
	return nil
}

// TestConnection 测试连接
func (eai *EnhancedAIIntegration) TestConnection() error {
	if eai.enhancedDeepSeek == nil {
		return fmt.Errorf("增强DeepSeek服务未初始化")
	}
	
	// 发送测试请求
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()
	
	req := &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "user",
				Content: "测试连接",
			},
		},
		MaxTokens:   100,
		Temperature: 0.1,
	}
	
	_, err := eai.enhancedDeepSeek.CallWithComplexity(ctx, req, ComplexitySimple)
	if err != nil {
		return fmt.Errorf("DeepSeek连接测试失败: %w", err)
	}
	
	eai.logger.Info("✅ DeepSeek连接测试成功")
	
	return nil
}
