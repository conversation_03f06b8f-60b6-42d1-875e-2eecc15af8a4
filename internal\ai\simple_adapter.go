package ai

import (
	"context"
	"time"

	"aiops-platform/internal/config"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 类型定义 - 避免循环导入
type WorkflowIntentAnalysis struct {
	NeedsWorkflow   bool                   `json:"needs_workflow"`
	WorkflowType    string                 `json:"workflow_type"`
	Confidence      float64                `json:"confidence"`
	Intent          string                 `json:"intent"`
	SuggestedAction string                 `json:"suggested_action"`
	Parameters      map[string]interface{} `json:"parameters"`
	Context         string                 `json:"context"`
}

type WorkflowState struct {
	InstanceID    string                 `json:"instance_id"`
	DefinitionID  string                 `json:"definition_id"`
	CurrentStep   string                 `json:"current_step"`
	Status        string                 `json:"status"`
	Variables     map[string]interface{} `json:"variables"`
	CollectedData map[string]interface{} `json:"collected_data"`
	Progress      float64                `json:"progress"`
}

type WorkflowGuidance struct {
	Message           string   `json:"message"`
	Suggestions       []string `json:"suggestions"`
	NextAction        string   `json:"next_action"`
	RequiredInputs    []string `json:"required_inputs"`
	ProgressIndicator string   `json:"progress_indicator"`
	HelpText          string   `json:"help_text"`
}

type AdapterConversationContext struct {
	SessionID string                 `json:"session_id"`
	UserID    int64                  `json:"user_id"`
	Messages  []AdapterMessage       `json:"messages"`
	Metadata  map[string]interface{} `json:"metadata"`
}

type AdapterMessage struct {
	Role      string    `json:"role"`
	Content   string    `json:"content"`
	Timestamp time.Time `json:"timestamp"`
}

type AdapterIntentResult struct {
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Command    string                 `json:"command,omitempty"`
}

type ToolDefinition struct {
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Parameters  map[string]interface{} `json:"parameters"`
}

type ToolCall struct {
	Name      string                 `json:"name"`
	Arguments map[string]interface{} `json:"arguments"`
}

type ToolResult struct {
	Success bool                   `json:"success"`
	Result  string                 `json:"result"`
	Data    map[string]interface{} `json:"data"`
}

type GenerateResponseRequest struct {
	Message   string                 `json:"message"`
	Context   map[string]interface{} `json:"context"`
	SessionID string                 `json:"session_id"`
}

type GenerateResponseResult struct {
	Content   string `json:"content"`
	TokenUsed int    `json:"token_used"`
}

type ConversationSummary struct {
	Summary     string   `json:"summary"`
	KeyPoints   []string `json:"key_points"`
	ActionItems []string `json:"action_items"`
}

type CommandValidation struct {
	IsValid     bool     `json:"is_valid"`
	RiskLevel   string   `json:"risk_level"`
	Suggestions []string `json:"suggestions"`
}

// SimpleAdapter 简单适配器，实现AIService接口
type SimpleAdapter struct {
	simpleService *SimpleAIService
	logger        *logrus.Logger
}

// NewSimpleAdapter 创建简单适配器
func NewSimpleAdapter(db *gorm.DB, cfg *config.Config, logger *logrus.Logger, hostService interface{}) *SimpleAdapter {
	simpleService := NewSimpleAIService(db, hostService, logger)

	return &SimpleAdapter{
		simpleService: simpleService,
		logger:        logger,
	}
}

// ProcessMessage 处理消息 - 实现AIService接口
func (sa *SimpleAdapter) ProcessMessage(ctx context.Context, req interface{}) (interface{}, error) {
	// 提取消息内容
	message := ""
	sessionID := ""
	userID := int64(1)

	// 尝试从interface{}中提取字段
	if reqMap, ok := req.(map[string]interface{}); ok {
		if msg, exists := reqMap["message"]; exists {
			if msgStr, ok := msg.(string); ok {
				message = msgStr
			}
		}
		if sid, exists := reqMap["session_id"]; exists {
			if sidStr, ok := sid.(string); ok {
				sessionID = sidStr
			}
		}
		if uid, exists := reqMap["user_id"]; exists {
			if uidInt, ok := uid.(int64); ok {
				userID = uidInt
			} else if uidFloat, ok := uid.(float64); ok {
				userID = int64(uidFloat)
			}
		}
	}

	// 创建简单请求
	simpleReq := &SimpleMessageRequest{
		Message:   message,
		SessionID: sessionID,
		UserID:    userID,
	}

	// 调用简单服务
	simpleResp, err := sa.simpleService.ProcessMessage(ctx, simpleReq)
	if err != nil {
		return nil, err
	}

	// 返回通用响应
	return map[string]interface{}{
		"content":         simpleResp.Content,
		"intent":          simpleResp.Intent,
		"confidence":      simpleResp.Confidence,
		"parameters":      simpleResp.Parameters,
		"token_count":     simpleResp.TokenCount,
		"processing_time": simpleResp.ProcessingTime,
		"timestamp":       simpleResp.Timestamp,
		"source":          simpleResp.Source,
		"success":         simpleResp.Success,
	}, nil
}

// ProcessMessageWithTools 使用工具处理消息 - 实现AIService接口
func (sa *SimpleAdapter) ProcessMessageWithTools(ctx context.Context, req interface{}) (interface{}, error) {
	return sa.ProcessMessage(ctx, req)
}

// AnalyzeWorkflowIntent 分析工作流意图 - 实现AIService接口
func (sa *SimpleAdapter) AnalyzeWorkflowIntent(ctx context.Context, message string, sessionID string) (interface{}, error) {
	// 返回一个通用的map，让调用方进行类型转换
	return map[string]interface{}{
		"needs_workflow":   false,
		"workflow_type":    "none",
		"confidence":       0.5,
		"intent":           "general",
		"suggested_action": "continue_conversation",
		"parameters":       make(map[string]interface{}),
		"context":          "normal_conversation",
	}, nil
}

// GenerateWorkflowGuidance 生成工作流指导 - 实现AIService接口
func (sa *SimpleAdapter) GenerateWorkflowGuidance(ctx context.Context, workflowState *WorkflowState) (*WorkflowGuidance, error) {
	return &WorkflowGuidance{
		Message:           "继续当前操作",
		Suggestions:       []string{"输入具体命令"},
		NextAction:        "continue",
		RequiredInputs:    []string{"请提供更多信息"},
		ProgressIndicator: "进行中",
		HelpText:          "如需帮助，请输入'帮助'",
	}, nil
}

// CreateContext 创建上下文 - 实现AIService接口
func (sa *SimpleAdapter) CreateContext(sessionID string, userID int64) error {
	sa.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"user_id":    userID,
	}).Info("SimpleAdapter: 创建上下文")
	return nil
}

// GetContext 获取上下文 - 实现AIService接口
func (sa *SimpleAdapter) GetContext(sessionID string) (*AdapterConversationContext, error) {
	return &AdapterConversationContext{
		SessionID: sessionID,
		UserID:    1,
		Messages:  []AdapterMessage{},
		Metadata:  make(map[string]interface{}),
	}, nil
}

// UpdateContext 更新上下文 - 实现AIService接口
func (sa *SimpleAdapter) UpdateContext(sessionID string, updates map[string]interface{}) error {
	sa.logger.WithFields(logrus.Fields{
		"session_id": sessionID,
		"updates":    updates,
	}).Info("SimpleAdapter: 更新上下文")
	return nil
}

// ClearContext 清除上下文 - 实现AIService接口
func (sa *SimpleAdapter) ClearContext(sessionID string) error {
	sa.logger.WithField("session_id", sessionID).Info("SimpleAdapter: 清除上下文")
	return nil
}

// ExtractIntent 提取意图 - 实现AIService接口
func (sa *SimpleAdapter) ExtractIntent(ctx context.Context, message string, context *AdapterConversationContext) (*AdapterIntentResult, error) {
	intent, err := sa.simpleService.intentEngine.RecognizeIntent(ctx, message)
	if err != nil {
		return nil, err
	}

	return &AdapterIntentResult{
		Type:       intent.Type,
		Confidence: intent.Confidence,
		Parameters: intent.Parameters,
		Command:    intent.Command,
	}, nil
}

// GetAvailableTools 获取可用工具 - 实现AIService接口
func (sa *SimpleAdapter) GetAvailableTools(userID int64) ([]ToolDefinition, error) {
	return []ToolDefinition{
		{
			Name:        "host_management",
			Description: "主机管理工具",
			Parameters:  map[string]interface{}{},
		},
		{
			Name:        "database_query",
			Description: "数据库查询工具",
			Parameters:  map[string]interface{}{},
		},
	}, nil
}

// ExecuteTool 执行工具 - 实现AIService接口
func (sa *SimpleAdapter) ExecuteTool(ctx context.Context, toolCall *ToolCall, context *AdapterConversationContext) (*ToolResult, error) {
	return &ToolResult{
		Success: true,
		Result:  "工具执行成功",
		Data:    make(map[string]interface{}),
	}, nil
}

// GenerateResponse 生成响应 - 实现AIService接口
func (sa *SimpleAdapter) GenerateResponse(ctx context.Context, req *GenerateResponseRequest) (*GenerateResponseResult, error) {
	return &GenerateResponseResult{
		Content:   "这是生成的响应",
		TokenUsed: 10,
	}, nil
}

// SummarizeConversation 总结对话 - 实现AIService接口
func (sa *SimpleAdapter) SummarizeConversation(ctx context.Context, sessionID string) (*ConversationSummary, error) {
	return &ConversationSummary{
		Summary:     "对话总结",
		KeyPoints:   []string{"要点1", "要点2"},
		ActionItems: []string{"行动项1", "行动项2"},
	}, nil
}

// ValidateCommand 验证命令 - 实现AIService接口
func (sa *SimpleAdapter) ValidateCommand(ctx context.Context, command string, context *AdapterConversationContext) (*CommandValidation, error) {
	return &CommandValidation{
		IsValid:     true,
		RiskLevel:   "low",
		Suggestions: []string{"命令看起来安全"},
	}, nil
}

// ProcessMessageWithWorkflow 使用工作流处理消息 - 实现AIService接口
func (sa *SimpleAdapter) ProcessMessageWithWorkflow(ctx context.Context, req interface{}) (interface{}, error) {
	return sa.ProcessMessage(ctx, req)
}

// GetSystemStatus 获取系统状态 - 实现AIService接口
func (sa *SimpleAdapter) GetSystemStatus() map[string]interface{} {
	status := sa.simpleService.GetSystemStatus()
	status["adapter_type"] = "simple_adapter"
	return status
}

// HealthCheck 健康检查 - 实现AIService接口
func (sa *SimpleAdapter) HealthCheck() bool {
	return sa.simpleService.HealthCheck()
}

// Close 关闭服务 - 实现AIService接口
func (sa *SimpleAdapter) Close() error {
	return sa.simpleService.Close()
}

// GetMetrics 获取指标 - 实现AIService接口
func (sa *SimpleAdapter) GetMetrics() map[string]interface{} {
	metrics := sa.simpleService.GetMetrics()
	metrics["adapter_type"] = "simple_adapter"
	return metrics
}

// ValidateMessage 验证消息 - 实现AIService接口
func (sa *SimpleAdapter) ValidateMessage(message string) error {
	return sa.simpleService.ValidateMessage(message)
}

// PreprocessMessage 预处理消息 - 实现AIService接口
func (sa *SimpleAdapter) PreprocessMessage(message string) string {
	return sa.simpleService.PreprocessMessage(message)
}

// GetSupportedIntents 获取支持的意图 - 实现AIService接口
func (sa *SimpleAdapter) GetSupportedIntents() []string {
	return sa.simpleService.GetSupportedIntents()
}

// GetSupportedOperations 获取支持的操作 - 实现AIService接口
func (sa *SimpleAdapter) GetSupportedOperations() map[string][]string {
	return sa.simpleService.GetSupportedOperations()
}

// GetServiceInfo 获取服务信息 - 实现AIService接口
func (sa *SimpleAdapter) GetServiceInfo() map[string]interface{} {
	return map[string]interface{}{
		"name":        "simple_adapter",
		"version":     "1.0.0",
		"description": "简单AI服务适配器",
		"features": []string{
			"类型转换",
			"接口适配",
			"主机管理",
			"意图识别",
		},
		"status":    "running",
		"timestamp": time.Now(),
	}
}

// IsReady 检查服务是否就绪 - 实现AIService接口
func (sa *SimpleAdapter) IsReady() bool {
	return sa.HealthCheck()
}

// GetVersion 获取版本信息 - 实现AIService接口
func (sa *SimpleAdapter) GetVersion() string {
	return "1.0.0"
}

// GetCapabilities 获取能力列表 - 实现AIService接口
func (sa *SimpleAdapter) GetCapabilities() []string {
	return []string{
		"interface_adaptation",
		"type_conversion",
		"intent_recognition",
		"database_operations",
		"host_management",
		"general_chat",
	}
}

// SetConfiguration 设置配置 - 实现AIService接口
func (sa *SimpleAdapter) SetConfiguration(config map[string]interface{}) error {
	sa.logger.WithField("config", config).Info("SimpleAdapter: 配置更新")
	return nil
}

// GetConfiguration 获取配置 - 实现AIService接口
func (sa *SimpleAdapter) GetConfiguration() map[string]interface{} {
	return map[string]interface{}{
		"service_type":         "simple_adapter",
		"intent_engine":        "simple_intent_engine",
		"execution_engine":     "simple_execution_engine",
		"type_conversion":      "enabled",
		"interface_adaptation": "enabled",
	}
}

// Reset 重置服务状态 - 实现AIService接口
func (sa *SimpleAdapter) Reset() error {
	sa.logger.Info("SimpleAdapter: 服务重置")
	return nil
}

// GetStatistics 获取统计信息 - 实现AIService接口
func (sa *SimpleAdapter) GetStatistics() map[string]interface{} {
	return map[string]interface{}{
		"total_requests":        0, // 可以添加计数器
		"successful_requests":   0,
		"failed_requests":       0,
		"type_conversions":      0,
		"average_response_time": "0ms",
		"uptime":                time.Now().Format("2006-01-02 15:04:05"),
	}
}
