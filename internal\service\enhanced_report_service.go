package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedReportService 增强报表服务
type EnhancedReportService struct {
	db                *gorm.DB
	logger            *logrus.Logger
	wsManager         *WebSocketManager
	config            *EnhancedReportConfig
	templateManager   *ReportTemplateManager
	chartGenerator    *DynamicChartGenerator
	dataAggregator    *ReportDataAggregator
	exportManager     *EnhancedExportManager
	cacheManager      *ReportCacheManager
	schedulerManager  *ReportSchedulerManager
	running           bool
	stopChan          chan struct{}
	mutex             sync.RWMutex
	metrics           *ReportServiceMetrics
}

// EnhancedReportConfig 增强报表配置
type EnhancedReportConfig struct {
	Enabled                  bool          `json:"enabled"`
	MaxConcurrentReports     int           `json:"max_concurrent_reports"`
	ReportTimeout            time.Duration `json:"report_timeout"`
	CacheEnabled             bool          `json:"cache_enabled"`
	CacheTTL                 time.Duration `json:"cache_ttl"`
	EnableRealTimeUpdates    bool          `json:"enable_real_time_updates"`
	EnableCustomTemplates    bool          `json:"enable_custom_templates"`
	EnableAdvancedCharts     bool          `json:"enable_advanced_charts"`
	ExportFormats            []string      `json:"export_formats"`
	MaxReportHistory         int           `json:"max_report_history"`
	AutoGenerationEnabled    bool          `json:"auto_generation_enabled"`
	AutoGenerationInterval   time.Duration `json:"auto_generation_interval"`
}

// ReportRequest 报表请求
type ReportRequest struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"`
	Title        string                 `json:"title"`
	Description  string                 `json:"description"`
	TimeRange    string                 `json:"time_range"`
	Filters      map[string]interface{} `json:"filters"`
	TemplateID   string                 `json:"template_id,omitempty"`
	ChartTypes   []string               `json:"chart_types"`
	ExportFormat string                 `json:"export_format"`
	UserID       int64                  `json:"user_id"`
	Priority     string                 `json:"priority"` // high, normal, low
	Metadata     map[string]interface{} `json:"metadata"`
}

// ReportResponse 报表响应
type ReportResponse struct {
	ID            string                 `json:"id"`
	Type          string                 `json:"type"`
	Title         string                 `json:"title"`
	Description   string                 `json:"description"`
	Status        string                 `json:"status"` // generating, completed, failed
	Progress      int                    `json:"progress"`
	Data          *ReportData            `json:"data,omitempty"`
	Charts        []*ChartData           `json:"charts,omitempty"`
	Summary       *ReportSummary         `json:"summary,omitempty"`
	Insights      []string               `json:"insights,omitempty"`
	Recommendations []string             `json:"recommendations,omitempty"`
	ExportLinks   map[string]string      `json:"export_links,omitempty"`
	GeneratedAt   time.Time              `json:"generated_at"`
	Duration      time.Duration          `json:"duration"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// ReportData 报表数据
type ReportData struct {
	Sections []ReportSection        `json:"sections"`
	Metrics  map[string]interface{} `json:"metrics"`
	Tables   []TableData            `json:"tables"`
	Raw      map[string]interface{} `json:"raw"`
}

// ReportSection 报表章节
type ReportSection struct {
	ID      string                 `json:"id"`
	Title   string                 `json:"title"`
	Type    string                 `json:"type"` // text, chart, table, metric
	Content interface{}            `json:"content"`
	Order   int                    `json:"order"`
	Config  map[string]interface{} `json:"config"`
}

// ChartData 图表数据
type ChartData struct {
	ID       string                 `json:"id"`
	Type     string                 `json:"type"` // line, bar, pie, area, scatter, heatmap
	Title    string                 `json:"title"`
	Data     interface{}            `json:"data"`
	Options  map[string]interface{} `json:"options"`
	Config   map[string]interface{} `json:"config"`
}

// TableData 表格数据
type TableData struct {
	ID      string                   `json:"id"`
	Title   string                   `json:"title"`
	Headers []string                 `json:"headers"`
	Rows    [][]interface{}          `json:"rows"`
	Config  map[string]interface{}   `json:"config"`
}

// ReportSummary 报表摘要
type ReportSummary struct {
	TotalItems      int                    `json:"total_items"`
	KeyMetrics      map[string]interface{} `json:"key_metrics"`
	Trends          map[string]string      `json:"trends"` // increasing, decreasing, stable
	HealthScore     float64                `json:"health_score"`
	AlertsCount     int                    `json:"alerts_count"`
	Recommendations int                    `json:"recommendations_count"`
}

// ReportServiceMetrics 报表服务指标
type ReportServiceMetrics struct {
	TotalReports      int64     `json:"total_reports"`
	CompletedReports  int64     `json:"completed_reports"`
	FailedReports     int64     `json:"failed_reports"`
	AvgGenerationTime float64   `json:"avg_generation_time_ms"`
	CacheHitRate      float64   `json:"cache_hit_rate"`
	LastGenerated     time.Time `json:"last_generated"`
	ActiveReports     int       `json:"active_reports"`
}

// NewEnhancedReportService 创建增强报表服务
func NewEnhancedReportService(db *gorm.DB, logger *logrus.Logger, wsManager *WebSocketManager) *EnhancedReportService {
	config := &EnhancedReportConfig{
		Enabled:                  true,
		MaxConcurrentReports:     10,
		ReportTimeout:            5 * time.Minute,
		CacheEnabled:             true,
		CacheTTL:                 30 * time.Minute,
		EnableRealTimeUpdates:    true,
		EnableCustomTemplates:    true,
		EnableAdvancedCharts:     true,
		ExportFormats:            []string{"json", "csv", "pdf", "html", "excel"},
		MaxReportHistory:         100,
		AutoGenerationEnabled:    true,
		AutoGenerationInterval:   1 * time.Hour,
	}

	service := &EnhancedReportService{
		db:        db,
		logger:    logger,
		wsManager: wsManager,
		config:    config,
		stopChan:  make(chan struct{}),
		metrics:   &ReportServiceMetrics{},
	}

	// 初始化组件
	service.templateManager = NewReportTemplateManager(db, logger)
	service.chartGenerator = NewDynamicChartGenerator(logger)
	service.dataAggregator = NewReportDataAggregator(db, logger)
	service.exportManager = NewEnhancedExportManager(logger)
	service.cacheManager = NewReportCacheManager(logger, config.CacheTTL)
	service.schedulerManager = NewReportSchedulerManager(logger, service)

	return service
}

// Start 启动增强报表服务
func (ers *EnhancedReportService) Start(ctx context.Context) error {
	ers.mutex.Lock()
	defer ers.mutex.Unlock()

	if ers.running {
		return fmt.Errorf("enhanced report service is already running")
	}

	if !ers.config.Enabled {
		ers.logger.Info("Enhanced report service is disabled")
		return nil
	}

	ers.running = true

	// 启动模板管理器
	if err := ers.templateManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start template manager: %w", err)
	}

	// 启动缓存管理器
	if ers.config.CacheEnabled {
		if err := ers.cacheManager.Start(ctx); err != nil {
			return fmt.Errorf("failed to start cache manager: %w", err)
		}
	}

	// 启动调度管理器
	if ers.config.AutoGenerationEnabled {
		if err := ers.schedulerManager.Start(ctx); err != nil {
			return fmt.Errorf("failed to start scheduler manager: %w", err)
		}
	}

	ers.logger.Info("📊 Enhanced report service started")
	return nil
}

// Stop 停止增强报表服务
func (ers *EnhancedReportService) Stop() error {
	ers.mutex.Lock()
	defer ers.mutex.Unlock()

	if !ers.running {
		return nil
	}

	close(ers.stopChan)
	ers.running = false

	ers.logger.Info("Enhanced report service stopped")
	return nil
}

// GenerateReport 生成报表
func (ers *EnhancedReportService) GenerateReport(ctx context.Context, req *ReportRequest) (*ReportResponse, error) {
	if !ers.running {
		return nil, fmt.Errorf("enhanced report service is not running")
	}

	startTime := time.Now()
	ers.logger.WithFields(logrus.Fields{
		"report_id":   req.ID,
		"report_type": req.Type,
		"time_range":  req.TimeRange,
		"user_id":     req.UserID,
	}).Info("🚀 Starting report generation")

	// 检查缓存
	if ers.config.CacheEnabled {
		cacheKey := ers.buildCacheKey(req)
		if cached := ers.cacheManager.GetCachedReport(cacheKey); cached != nil {
			ers.logger.WithField("report_id", req.ID).Debug("Report found in cache")
			return cached, nil
		}
	}

	// 创建响应对象
	response := &ReportResponse{
		ID:          req.ID,
		Type:        req.Type,
		Title:       req.Title,
		Description: req.Description,
		Status:      "generating",
		Progress:    0,
		GeneratedAt: time.Now(),
		Metadata:    req.Metadata,
	}

	// 发送开始通知
	if ers.config.EnableRealTimeUpdates {
		ers.sendProgressUpdate(response)
	}

	// 聚合数据
	response.Progress = 20
	ers.sendProgressUpdate(response)
	
	data, err := ers.dataAggregator.AggregateData(ctx, req)
	if err != nil {
		response.Status = "failed"
		ers.metrics.FailedReports++
		return response, fmt.Errorf("failed to aggregate data: %w", err)
	}

	// 生成图表
	response.Progress = 50
	ers.sendProgressUpdate(response)
	
	charts, err := ers.chartGenerator.GenerateCharts(ctx, req, data)
	if err != nil {
		ers.logger.WithError(err).Warn("Failed to generate charts, continuing without charts")
		charts = []*ChartData{}
	}

	// 生成摘要和洞察
	response.Progress = 80
	ers.sendProgressUpdate(response)
	
	summary := ers.generateSummary(data)
	insights := ers.generateInsights(data)
	recommendations := ers.generateRecommendations(data)

	// 完成报表
	response.Progress = 100
	response.Status = "completed"
	response.Data = data
	response.Charts = charts
	response.Summary = summary
	response.Insights = insights
	response.Recommendations = recommendations
	response.Duration = time.Since(startTime)

	// 生成导出链接
	if len(req.ExportFormat) > 0 {
		exportLinks := make(map[string]string)
		for _, format := range ers.config.ExportFormats {
			exportLinks[format] = fmt.Sprintf("/api/v1/reports/%s/export/%s", req.ID, format)
		}
		response.ExportLinks = exportLinks
	}

	// 缓存报表
	if ers.config.CacheEnabled {
		cacheKey := ers.buildCacheKey(req)
		ers.cacheManager.CacheReport(cacheKey, response)
	}

	// 更新指标
	ers.updateMetrics(time.Since(startTime))

	// 发送完成通知
	ers.sendProgressUpdate(response)

	ers.logger.WithFields(logrus.Fields{
		"report_id": req.ID,
		"duration":  response.Duration,
		"status":    response.Status,
	}).Info("✅ Report generation completed")

	return response, nil
}

// buildCacheKey 构建缓存键
func (ers *EnhancedReportService) buildCacheKey(req *ReportRequest) string {
	return fmt.Sprintf("report_%s_%s_%s_%d", req.Type, req.TimeRange, req.TemplateID, req.UserID)
}

// sendProgressUpdate 发送进度更新
func (ers *EnhancedReportService) sendProgressUpdate(response *ReportResponse) {
	if !ers.config.EnableRealTimeUpdates || ers.wsManager == nil {
		return
	}

	message := &WSMessage{
		ID:       fmt.Sprintf("report_progress_%s", response.ID),
		Type:     "report_progress",
		Channel:  "report",
		Priority: "normal",
		Data: map[string]interface{}{
			"report_id":   response.ID,
			"status":      response.Status,
			"progress":    response.Progress,
			"title":       response.Title,
			"generated_at": response.GeneratedAt,
		},
		Timestamp: time.Now(),
		Metadata: map[string]interface{}{
			"source": "enhanced_report_service",
			"version": "v1",
		},
	}

	ers.wsManager.BroadcastToAll(message)
}

// updateMetrics 更新指标
func (ers *EnhancedReportService) updateMetrics(duration time.Duration) {
	ers.mutex.Lock()
	defer ers.mutex.Unlock()

	ers.metrics.TotalReports++
	ers.metrics.CompletedReports++
	ers.metrics.LastGenerated = time.Now()

	// 计算平均生成时间
	if ers.metrics.CompletedReports > 0 {
		totalTime := ers.metrics.AvgGenerationTime * float64(ers.metrics.CompletedReports-1)
		ers.metrics.AvgGenerationTime = (totalTime + float64(duration.Nanoseconds())/1e6) / float64(ers.metrics.CompletedReports)
	} else {
		ers.metrics.AvgGenerationTime = float64(duration.Nanoseconds()) / 1e6
	}
}

// generateSummary 生成摘要
func (ers *EnhancedReportService) generateSummary(data *ReportData) *ReportSummary {
	// 简化实现，实际应该根据数据计算
	return &ReportSummary{
		TotalItems:      len(data.Sections),
		KeyMetrics:      data.Metrics,
		Trends:          map[string]string{"cpu": "stable", "memory": "increasing"},
		HealthScore:     85.5,
		AlertsCount:     2,
		Recommendations: 3,
	}
}

// generateInsights 生成洞察
func (ers *EnhancedReportService) generateInsights(data *ReportData) []string {
	return []string{
		"系统整体运行状况良好，CPU使用率保持在正常范围内",
		"内存使用率呈上升趋势，建议关注内存优化",
		"网络流量在高峰时段有明显增长",
	}
}

// generateRecommendations 生成建议
func (ers *EnhancedReportService) generateRecommendations(data *ReportData) []string {
	return []string{
		"建议在高峰时段增加服务器资源配置",
		"优化数据库查询以减少内存使用",
		"考虑实施缓存策略以提高响应速度",
	}
}

// GetMetrics 获取服务指标
func (ers *EnhancedReportService) GetMetrics() *ReportServiceMetrics {
	ers.mutex.RLock()
	defer ers.mutex.RUnlock()

	metrics := *ers.metrics
	return &metrics
}

// 支持组件实现

// NewEnhancedExportManager 创建增强导出管理器
func NewEnhancedExportManager(logger *logrus.Logger) *EnhancedExportManager {
	return &EnhancedExportManager{
		logger:    logger,
		exporters: make(map[string]ReportExporter),
	}
}

// EnhancedExportManager 增强导出管理器
type EnhancedExportManager struct {
	logger    *logrus.Logger
	exporters map[string]ReportExporter
}

// ReportExporter 报表导出器接口
type ReportExporter interface {
	Export(data *ReportResponse) ([]byte, error)
	GetFormat() string
	GetMimeType() string
}

// NewReportCacheManager 创建报表缓存管理器
func NewReportCacheManager(logger *logrus.Logger, ttl time.Duration) *ReportCacheManager {
	return &ReportCacheManager{
		logger: logger,
		cache:  make(map[string]*ReportCacheEntry),
		ttl:    ttl,
	}
}

// ReportCacheManager 报表缓存管理器
type ReportCacheManager struct {
	logger *logrus.Logger
	cache  map[string]*ReportCacheEntry
	ttl    time.Duration
	mutex  sync.RWMutex
}

// ReportCacheEntry 报表缓存条目
type ReportCacheEntry struct {
	Data      *ReportResponse
	ExpiresAt time.Time
}

// Start 启动缓存管理器
func (rcm *ReportCacheManager) Start(ctx context.Context) error {
	// 启动清理协程
	go rcm.cleanupRoutine(ctx)
	rcm.logger.Info("Report cache manager started")
	return nil
}

// GetCachedReport 获取缓存的报表
func (rcm *ReportCacheManager) GetCachedReport(key string) *ReportResponse {
	rcm.mutex.RLock()
	defer rcm.mutex.RUnlock()

	entry, exists := rcm.cache[key]
	if !exists {
		return nil
	}

	if time.Now().After(entry.ExpiresAt) {
		delete(rcm.cache, key)
		return nil
	}

	return entry.Data
}

// CacheReport 缓存报表
func (rcm *ReportCacheManager) CacheReport(key string, report *ReportResponse) {
	rcm.mutex.Lock()
	defer rcm.mutex.Unlock()

	now := time.Now()
	rcm.cache[key] = &ReportCacheEntry{
		Data:      report,
		ExpiresAt: now.Add(rcm.ttl),
	}
}

// cleanupRoutine 清理协程
func (rcm *ReportCacheManager) cleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(rcm.ttl / 2)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			rcm.cleanup()
		}
	}
}

// cleanup 清理过期缓存
func (rcm *ReportCacheManager) cleanup() {
	rcm.mutex.Lock()
	defer rcm.mutex.Unlock()

	now := time.Now()
	for key, entry := range rcm.cache {
		if now.After(entry.ExpiresAt) {
			delete(rcm.cache, key)
		}
	}
}

// NewReportSchedulerManager 创建报表调度管理器
func NewReportSchedulerManager(logger *logrus.Logger, reportService *EnhancedReportService) *ReportSchedulerManager {
	return &ReportSchedulerManager{
		logger:        logger,
		reportService: reportService,
		schedules:     make(map[string]*ScheduleEntry),
	}
}

// ReportSchedulerManager 报表调度管理器
type ReportSchedulerManager struct {
	logger        *logrus.Logger
	reportService *EnhancedReportService
	schedules     map[string]*ScheduleEntry
	mutex         sync.RWMutex
}

// ScheduleEntry 调度条目
type ScheduleEntry struct {
	ID       string
	Request  *ReportRequest
	Interval time.Duration
	NextRun  time.Time
	Enabled  bool
}

// Start 启动调度管理器
func (rsm *ReportSchedulerManager) Start(ctx context.Context) error {
	// 添加默认调度任务
	rsm.addDefaultSchedules()

	// 启动调度协程
	go rsm.schedulerRoutine(ctx)

	rsm.logger.Info("Report scheduler manager started")
	return nil
}

// addDefaultSchedules 添加默认调度
func (rsm *ReportSchedulerManager) addDefaultSchedules() {
	defaultSchedules := []*ScheduleEntry{
		{
			ID: "daily_operation_report",
			Request: &ReportRequest{
				ID:        "daily_operation_" + time.Now().Format("20060102"),
				Type:      "operation",
				Title:     "每日运维操作报表",
				TimeRange: "24h",
				UserID:    0, // 系统生成
			},
			Interval: 24 * time.Hour,
			NextRun:  time.Now().Add(24 * time.Hour),
			Enabled:  true,
		},
		{
			ID: "hourly_health_check",
			Request: &ReportRequest{
				ID:        "hourly_health_" + time.Now().Format("**********"),
				Type:      "system_health",
				Title:     "每小时系统健康检查",
				TimeRange: "1h",
				UserID:    0,
			},
			Interval: 1 * time.Hour,
			NextRun:  time.Now().Add(1 * time.Hour),
			Enabled:  true,
		},
	}

	rsm.mutex.Lock()
	defer rsm.mutex.Unlock()

	for _, schedule := range defaultSchedules {
		rsm.schedules[schedule.ID] = schedule
	}
}

// schedulerRoutine 调度协程
func (rsm *ReportSchedulerManager) schedulerRoutine(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟检查一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			rsm.checkAndExecuteSchedules(ctx)
		}
	}
}

// checkAndExecuteSchedules 检查并执行调度
func (rsm *ReportSchedulerManager) checkAndExecuteSchedules(ctx context.Context) {
	rsm.mutex.RLock()
	schedules := make([]*ScheduleEntry, 0, len(rsm.schedules))
	for _, schedule := range rsm.schedules {
		schedules = append(schedules, schedule)
	}
	rsm.mutex.RUnlock()

	now := time.Now()
	for _, schedule := range schedules {
		if schedule.Enabled && now.After(schedule.NextRun) {
			go rsm.executeScheduledReport(ctx, schedule)
		}
	}
}

// executeScheduledReport 执行调度报表
func (rsm *ReportSchedulerManager) executeScheduledReport(ctx context.Context, schedule *ScheduleEntry) {
	rsm.logger.WithField("schedule_id", schedule.ID).Info("Executing scheduled report")

	// 更新请求ID和时间戳
	schedule.Request.ID = fmt.Sprintf("%s_%s", schedule.Request.Type, time.Now().Format("20060102_150405"))

	// 生成报表
	_, err := rsm.reportService.GenerateReport(ctx, schedule.Request)
	if err != nil {
		rsm.logger.WithError(err).WithField("schedule_id", schedule.ID).Error("Failed to execute scheduled report")
	}

	// 更新下次执行时间
	rsm.mutex.Lock()
	schedule.NextRun = time.Now().Add(schedule.Interval)
	rsm.mutex.Unlock()
}
