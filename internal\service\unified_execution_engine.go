package service

import (
	"context"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	"aiops-platform/internal/ai"
	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 🚀 革命性智能执行引擎 - AI决策 + 后端执行架构
// UnifiedExecutionEngine DeepSeek作为完全决策层，后端作为纯执行器
type UnifiedExecutionEngine struct {
	db                  *gorm.DB
	logger              *logrus.Logger
	hostService         HostService
	confirmationManager *ConfirmationManager

	// 🚀 新架构：AI驱动执行器
	aiDrivenExecutor *ai.AIDrivenExecutor
	deepseekClient   *ai.DeepSeekClient

	// 🎯 智能报表执行器
	reportExecutor *IntelligentReportExecutor

	// 🎯 核心AI驱动组件（保留用于兼容）
	instructionParser   *AIInstructionParser   // AI指令解析器
	executorRegistry    *ExecutorRegistry      // 通用执行器注册表
	securityValidator   *AISecurityValidator   // AI安全验证器
	executionOrchestrator *ExecutionOrchestrator // 执行编排器

	// 🔄 向后兼容（将逐步移除硬编码执行器）
	legacyMode bool
	databaseExecutor   *DatabaseExecutor
	sshExecutor        *SSHExecutor
	monitoringExecutor *MonitoringExecutor
	chatExecutor       *ChatExecutor
}

// 🚀 革命性架构：AI生成的执行指令结构

// AIGeneratedInstruction AI生成的执行指令
type AIGeneratedInstruction struct {
	InstructionType string                 `json:"instruction_type"` // sql, shell, api, composite
	ExecutorType    string                 `json:"executor_type"`    // database, ssh, http, workflow
	Content         string                 `json:"content"`          // 具体的SQL/Shell/API内容
	Parameters      map[string]interface{} `json:"parameters"`       // 执行参数
	TargetResource  *TargetResource        `json:"target_resource"`  // 目标资源
	SecurityLevel   string                 `json:"security_level"`   // safe, medium, dangerous
	RequireConfirm  bool                   `json:"require_confirm"`  // 是否需要确认
	Timeout         int                    `json:"timeout"`          // 超时时间（秒）
	Description     string                 `json:"description"`      // 操作描述
	ExpectedResult  *ExpectedResult        `json:"expected_result"`  // 预期结果
}

// TargetResource 目标资源
type TargetResource struct {
	Type       string                 `json:"type"`        // host, database, service
	Identifier string                 `json:"identifier"`  // IP地址、数据库名等
	Metadata   map[string]interface{} `json:"metadata"`    // 额外信息
}

// ExpectedResult 预期结果
type ExpectedResult struct {
	DataType    string   `json:"data_type"`    // table, text, json, metrics
	Columns     []string `json:"columns"`      // 预期的列名
	RowCount    *int     `json:"row_count"`    // 预期行数
	Format      string   `json:"format"`       // 结果格式
	Validation  string   `json:"validation"`   // 验证规则
}

// ExecutionRequest 执行请求（革命性升级）
type ExecutionRequest struct {
	SessionID   string                 `json:"session_id"`
	UserID      int64                  `json:"user_id"`
	Intent      *IntentResult          `json:"intent"`
	OriginalMsg string                 `json:"original_message"`
	Context     map[string]interface{} `json:"context,omitempty"`

	// 🚀 新增：AI生成的执行指令
	AIInstructions []*AIGeneratedInstruction `json:"ai_instructions,omitempty"`
}

// 🚀 革命性架构：通用执行器接口

// GenericExecutor 通用执行器接口 - 所有执行器的统一接口
type GenericExecutor interface {
	// Execute 执行AI生成的指令
	Execute(ctx context.Context, instruction *AIGeneratedInstruction) (*GenericExecutionResult, error)

	// GetSupportedTypes 获取支持的指令类型
	GetSupportedTypes() []string

	// ValidateInstruction 验证指令的合法性
	ValidateInstruction(instruction *AIGeneratedInstruction) error

	// GetExecutorInfo 获取执行器信息
	GetExecutorInfo() *ExecutorInfo
}

// GenericExecutionResult 通用执行结果
type GenericExecutionResult struct {
	Success        bool                   `json:"success"`
	Content        string                 `json:"content"`
	Data           interface{}            `json:"data,omitempty"`
	Action         string                 `json:"action"`
	ExecutionTime  time.Duration          `json:"execution_time"`
	RequireConfirm bool                   `json:"require_confirm,omitempty"`
	ConfirmToken   string                 `json:"confirm_token,omitempty"`
	Metadata       map[string]interface{} `json:"metadata,omitempty"`

	// 🚀 新增：执行详情
	InstructionType string                 `json:"instruction_type"`
	ExecutorType    string                 `json:"executor_type"`
	SecurityLevel   string                 `json:"security_level"`
	ValidationInfo  *ValidationInfo        `json:"validation_info,omitempty"`
}

// ExecutorInfo 执行器信息
type ExecutorInfo struct {
	Name         string   `json:"name"`
	Version      string   `json:"version"`
	Description  string   `json:"description"`
	Capabilities []string `json:"capabilities"`
	SafetyLevel  string   `json:"safety_level"`
}

// ValidationInfo 验证信息
type ValidationInfo struct {
	IsValid      bool     `json:"is_valid"`
	SecurityRisk string   `json:"security_risk"`
	Warnings     []string `json:"warnings"`
	Suggestions  []string `json:"suggestions"`
}

// 注意：UnifiedExecutionResult 已在 execution_result_collector.go 中定义

// 🚀 革命性架构：核心组件定义

// AIInstructionParser AI指令解析器
type AIInstructionParser struct {
	logger *logrus.Logger
}

// ExecutorRegistry 执行器注册表
type ExecutorRegistry struct {
	executors map[string]GenericExecutor
	logger    *logrus.Logger
}

// AISecurityValidator 在ai_execution_components.go中定义

// ExecutionOrchestrator 执行编排器
type ExecutionOrchestrator struct {
	logger    *logrus.Logger
	registry  *ExecutorRegistry
	validator *AISecurityValidator
}

// NewUnifiedExecutionEngine 创建革命性智能执行引擎
func NewUnifiedExecutionEngine(
	db *gorm.DB,
	logger *logrus.Logger,
	hostService HostService,
	confirmationManager *ConfirmationManager,
	deepseekAPIKey string,
) *UnifiedExecutionEngine {
	engine := &UnifiedExecutionEngine{
		db:                  db,
		logger:              logger,
		hostService:         hostService,
		confirmationManager: confirmationManager,
		legacyMode:          false, // 默认使用新架构
	}

	// 🚀 初始化AI驱动组件
	engine.deepseekClient = ai.NewDeepSeekClient(deepseekAPIKey, logger)
	engine.aiDrivenExecutor = ai.NewAIDrivenExecutor(engine.deepseekClient, db, logger)

	// 🎯 初始化智能报表执行器
	engine.reportExecutor = NewIntelligentReportExecutor(db, logger, engine.deepseekClient)

	// 🎯 初始化传统架构组件（向后兼容）
	engine.instructionParser = NewAIInstructionParser(logger)
	engine.executorRegistry = NewExecutorRegistry(logger)
	engine.securityValidator = NewAISecurityValidator(logger)
	engine.executionOrchestrator = NewExecutionOrchestrator(logger, engine.executorRegistry, engine.securityValidator)

	// 🎯 注册通用执行器
	engine.registerGenericExecutors(db, hostService, confirmationManager)

	// 🔄 向后兼容：保留原有执行器
	engine.databaseExecutor = NewDatabaseExecutor(db, logger, hostService, confirmationManager)
	engine.sshExecutor = NewSSHExecutor(db, logger, hostService)
	engine.monitoringExecutor = NewMonitoringExecutor(db, logger, hostService)
	engine.chatExecutor = NewChatExecutor(logger)

	logger.Info("🚀 革命性智能执行引擎初始化完成 - AI完全驱动架构")
	return engine
}

// buildAIExecutionContext 构建AI执行上下文
func (uee *UnifiedExecutionEngine) buildAIExecutionContext(ctx context.Context) *ai.AIExecutionContext {
	// 获取数据库模式信息
	dbSchema := uee.getDatabaseSchema()

	// 获取主机清单信息
	hostInventory := uee.getHostInventory()

	// 获取系统状态信息
	systemState := uee.getSystemState()

	return &ai.AIExecutionContext{
		DatabaseSchema: dbSchema,
		HostInventory:  hostInventory,
		SystemState:    systemState,
		SecurityLevel:  "normal",
	}
}

// buildUserPreferences 构建用户偏好
func (uee *UnifiedExecutionEngine) buildUserPreferences(req *ExecutionRequest) *ai.UserPreferences {
	return &ai.UserPreferences{
		OutputFormat:   "markdown",
		SafetyLevel:    "normal",
		ConfirmActions: true,
		PreferredShell: "bash",
	}
}

// getDatabaseSchema 获取数据库模式
func (uee *UnifiedExecutionEngine) getDatabaseSchema() *ai.DatabaseSchemaInfo {
	tables := []ai.TableSchema{
		{
			Name:        "hosts",
			Description: "主机信息表",
			Columns: []ai.ColumnSchema{
				{Name: "id", Type: "INTEGER", Nullable: false, Description: "主机ID"},
				{Name: "ip_address", Type: "VARCHAR", Nullable: false, Description: "IP地址"},
				{Name: "name", Type: "VARCHAR", Nullable: false, Description: "主机名"},
				{Name: "username", Type: "VARCHAR", Nullable: false, Description: "用户名"},
				{Name: "password_encrypted", Type: "VARCHAR", Nullable: false, Description: "加密密码"},
				{Name: "port", Type: "INTEGER", Nullable: false, Description: "SSH端口"},
				{Name: "status", Type: "VARCHAR", Nullable: false, Description: "状态(online/offline/unknown)"},
				{Name: "environment", Type: "VARCHAR", Nullable: false, Description: "环境(production/staging/development)"},
				{Name: "description", Type: "TEXT", Nullable: true, Description: "描述"},
				{Name: "created_at", Type: "DATETIME", Nullable: false, Description: "创建时间"},
				{Name: "updated_at", Type: "DATETIME", Nullable: false, Description: "更新时间"},
				{Name: "deleted_at", Type: "DATETIME", Nullable: true, Description: "删除时间"},
			},
		},
	}

	return &ai.DatabaseSchemaInfo{
		Tables: tables,
	}
}

// getHostInventory 获取主机清单
func (uee *UnifiedExecutionEngine) getHostInventory() *ai.HostInventoryInfo {
	var hosts []model.Host
	uee.db.Where("deleted_at IS NULL").Find(&hosts)

	hostInfos := make([]ai.HostInfo, len(hosts))
	for i, host := range hosts {
		hostInfos[i] = ai.HostInfo{
			ID:          host.ID,
			IPAddress:   host.IPAddress,
			Name:        host.Name,
			Status:      host.Status,
			Environment: host.Environment,
			OS:          "Linux", // 默认值，实际应该从数据库获取
		}
	}

	return &ai.HostInventoryInfo{
		TotalHosts: len(hosts),
		Hosts:      hostInfos,
	}
}

// getAvailableHosts 获取可用主机列表
func (uee *UnifiedExecutionEngine) getAvailableHosts() ([]model.Host, error) {
	var hosts []model.Host
	err := uee.db.Where("deleted_at IS NULL AND status IN ?", []string{"online", "offline"}).Find(&hosts).Error
	return hosts, err
}

// getSystemState 获取系统状态
func (uee *UnifiedExecutionEngine) getSystemState() *ai.SystemStateInfo {
	return &ai.SystemStateInfo{
		Timestamp: time.Now(),
		Services:  []string{"ssh", "http", "mysql"},
		Metrics:   map[string]interface{}{"cpu": 50, "memory": 60, "disk": 30},
		Alerts:    []string{},
	}
}

// 🚀 革命性Execute方法 - AI完全驱动架构
func (uee *UnifiedExecutionEngine) Execute(ctx context.Context, req *ExecutionRequest) (*UnifiedExecutionResult, error) {
	start := time.Now()

	uee.logger.WithFields(logrus.Fields{
		"session_id":   req.SessionID,
		"user_id":      req.UserID,
		"original_msg": req.OriginalMsg,
		"architecture": "AI-Driven-Pure",
	}).Info("🚀 开始AI完全驱动执行")

	// 🎯 优先检查是否为报表生成请求
	if uee.reportExecutor != nil && uee.reportExecutor.IsReportRequest(req.OriginalMsg) {
		uee.logger.Info("🎯 检测到报表生成请求，使用智能报表执行器")
		return uee.reportExecutor.Execute(ctx, req)
	}

	// 🔧 关键修复：检查是否有DeepSeek生成的具体命令需要执行
	if req.Intent != nil && req.Intent.Parameters != nil {
		// 检查是否有SQL命令需要执行
		if sql, hasSql := req.Intent.Parameters["sql"].(string); hasSql && sql != "" {
			uee.logger.WithField("sql", sql).Info("🎯 检测到DeepSeek生成的SQL，直接执行数据库操作")
			return uee.executeDirectSQL(ctx, req, sql, start)
		}

		// 检查是否有SSH命令需要执行
		if sshCmd, hasSSH := req.Intent.Parameters["ssh_command"].(string); hasSSH && sshCmd != "" {
			uee.logger.WithField("ssh_command", sshCmd).Info("🎯 检测到DeepSeek生成的SSH命令，直接执行")
			return uee.executeDirectSSH(ctx, req, sshCmd, start)
		}

		// 🔐 检查是否有密码更新操作需要执行
		if passwordUpdate, hasPasswordUpdate := req.Intent.Parameters["password_update"].(string); hasPasswordUpdate && passwordUpdate != "" {
			uee.logger.WithField("password_update", passwordUpdate).Info("🔐 检测到密码更新操作，执行安全API")
			return uee.executePasswordUpdate(ctx, req, start)
		}
	}

	// 🚀 如果没有具体命令，使用AI驱动执行器 - 完全由DeepSeek决策和生成内容
	aiReq := &ai.AIExecutionRequest{
		UserInput:   req.OriginalMsg,
		SessionID:   req.SessionID,
		UserID:      req.UserID,
		Context:     uee.buildAIExecutionContext(ctx),
		Preferences: uee.buildUserPreferences(req),
	}

	aiResult, err := uee.aiDrivenExecutor.Execute(ctx, aiReq)
	if err != nil {
		uee.logger.WithError(err).Error("AI驱动执行失败，降级到传统模式")
		return uee.executeLegacyMode(ctx, req, start)
	}

	// 转换AI结果为统一结果格式
	// 安全地转换 ExecutionData
	var executionData interface{}
	if aiResult.ExecutionData != nil {
		executionData = aiResult.ExecutionData
	}

	result := &UnifiedExecutionResult{
		Success:        aiResult.Success,
		Content:        aiResult.Content,
		Action:         aiResult.ExecutionType,
		ExecutionType:  aiResult.ExecutionType,  // 🔧 修复：正确设置ExecutionType字段
		GeneratedCode:  aiResult.GeneratedCode,  // 🔧 修复：设置GeneratedCode字段
		ExecutionTime:  aiResult.ExecutionTime,
		RequireConfirm: aiResult.RequireConfirm,
		ConfirmToken:   aiResult.ConfirmToken,
		RawData:        executionData,
		Metadata: map[string]interface{}{
			"ai_driven":       true,
			"execution_type":  aiResult.ExecutionType,
			"generated_code":  aiResult.GeneratedCode,
			"safety_info":     aiResult.SafetyInfo,
		},
	}

	uee.logger.WithFields(logrus.Fields{
		"session_id":     req.SessionID,
		"success":        result.Success,
		"execution_time": result.ExecutionTime,
		"execution_type": aiResult.ExecutionType,
	}).Info("✅ AI完全驱动执行完成")

	return result, nil
}

// executeDirectSQL 直接执行DeepSeek生成的SQL
func (uee *UnifiedExecutionEngine) executeDirectSQL(ctx context.Context, req *ExecutionRequest, sql string, start time.Time) (*UnifiedExecutionResult, error) {
	uee.logger.WithField("sql", sql).Info("🔍 直接执行SQL查询")

	// 获取数据库执行器
	executor, err := uee.executorRegistry.GetExecutor("database")
	if err != nil {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 数据库执行器不可用",
			Action:  "executor_error",
		}, nil
	}

	// 构建AI生成的指令
	instruction := &AIGeneratedInstruction{
		InstructionType: "sql", // 修复：使用数据库执行器支持的类型
		Content:         sql,
		Description:     "DeepSeek生成的SQL查询",
		SecurityLevel:   "medium",
		Parameters: map[string]interface{}{
			"operation":        "select",
			"sql":              sql,
			"description":      "DeepSeek生成的查询",
			"confirm_required": false,
			"session_id":       req.SessionID,
			"user_id":          req.UserID,
			"original_msg":     req.OriginalMsg,
		},
		TargetResource: &TargetResource{
			Type:       "database",
			Identifier: "default",
		},
		ExecutorType:   "database",
		RequireConfirm: false,
		Timeout:        30,
	}

	// 执行数据库操作
	result, err := executor.Execute(ctx, instruction)
	if err != nil {
		uee.logger.WithError(err).Error("直接SQL执行失败")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ SQL执行失败：%s", err.Error()),
			Action:  "sql_error",
		}, nil
	}

	// 转换结果格式
	var resultData map[string]interface{}
	if result.Data != nil {
		if dataMap, ok := result.Data.(map[string]interface{}); ok {
			resultData = dataMap
		} else {
			resultData = map[string]interface{}{"raw_data": result.Data}
		}
	}

	unifiedResult := &UnifiedExecutionResult{
		Success:       result.Success,
		Content:       result.Content,
		Action:        result.Action,
		ExecutionTime: time.Since(start),
		Data:          resultData,
		Metadata:      result.Metadata,
	}

	uee.logger.WithField("execution_time", time.Since(start)).Info("✅ 直接SQL执行完成")
	return unifiedResult, nil
}

// executeDirectSSH 直接执行DeepSeek生成的SSH命令
func (uee *UnifiedExecutionEngine) executeDirectSSH(ctx context.Context, req *ExecutionRequest, sshCmd string, start time.Time) (*UnifiedExecutionResult, error) {
	uee.logger.WithField("ssh_command", sshCmd).Info("🔍 直接执行SSH命令")

	// 获取SSH执行器
	executor, err := uee.executorRegistry.GetExecutor("ssh")
	if err != nil {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ SSH执行器不可用",
			Action:  "executor_error",
		}, nil
	}

	// 🔧 关键修复：从用户输入中提取目标IP地址
	targetIP := uee.extractIPFromMessage(req.OriginalMsg)
	if targetIP == "" {
		// 🚀 智能主机选择：不再硬编码"default"，而是获取第一台可用主机
		hosts, err := uee.getAvailableHosts()
		if err != nil || len(hosts) == 0 {
			return &UnifiedExecutionResult{
				Success: false,
				Content: "❌ 未找到可用的主机，请先添加主机或指定目标主机IP",
				Action:  "no_hosts_available",
			}, nil
		}
		targetIP = hosts[0].IPAddress
		uee.logger.WithField("selected_host", targetIP).Info("🎯 自动选择第一台可用主机")
	}

	// 🔧 关键修复：检查是否为复杂命令链，如果是则直接执行完整命令
	var remoteCommand string
	if uee.isComplexCommandChain(sshCmd) {
		// 对于复杂命令链，直接执行完整命令而不是提取单个SSH命令
		remoteCommand = sshCmd
		uee.logger.WithField("complex_command", remoteCommand).Info("🔧 检测到复杂命令链，直接执行完整命令")
	} else {
		// 对于简单SSH命令，提取远程命令
		remoteCommand = uee.extractRemoteCommandFromSSH(sshCmd)
		if remoteCommand == "" {
			remoteCommand = sshCmd // 降级到原始命令
		}
	}

	uee.logger.WithFields(logrus.Fields{
		"original_msg":     req.OriginalMsg,
		"extracted_ip":     targetIP,
		"ssh_command":      sshCmd,
		"remote_command":   remoteCommand,
	}).Info("🔍 提取目标IP地址和远程命令")

	// 构建AI生成的指令
	instruction := &AIGeneratedInstruction{
		InstructionType: "shell", // 修复：使用SSH执行器支持的类型
		Content:         remoteCommand, // 🔧 关键修复：使用提取的远程命令而不是完整SSH命令
		Description:     "DeepSeek生成的SSH命令",
		SecurityLevel:   "high",
		Parameters: map[string]interface{}{
			"command":          remoteCommand, // 🔧 关键修复：使用远程命令
			"description":      "DeepSeek生成的SSH命令",
			"confirm_required": false,
			"session_id":       req.SessionID,
			"user_id":          req.UserID,
			"original_msg":     req.OriginalMsg,
			"target_ip":        targetIP, // 添加目标IP参数
		},
		TargetResource: &TargetResource{
			Type:       "host",
			Identifier: targetIP, // 使用提取的IP地址而不是"default"
		},
		ExecutorType:   "ssh",
		RequireConfirm: false,
		Timeout:        30,
	}

	// 执行SSH操作
	result, err := executor.Execute(ctx, instruction)
	if err != nil {
		uee.logger.WithError(err).Error("直接SSH执行失败")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ SSH执行失败：%s", err.Error()),
			Action:  "ssh_error",
		}, nil
	}

	// 转换结果格式
	var resultData map[string]interface{}
	if result.Data != nil {
		if dataMap, ok := result.Data.(map[string]interface{}); ok {
			resultData = dataMap
		} else {
			resultData = map[string]interface{}{"raw_data": result.Data}
		}
	}

	unifiedResult := &UnifiedExecutionResult{
		Success:       result.Success,
		Content:       result.Content,
		Action:        result.Action,
		ExecutionTime: time.Since(start),
		Data:          resultData,
		Metadata:      result.Metadata,
	}

	uee.logger.WithField("execution_time", time.Since(start)).Info("✅ 直接SSH执行完成")
	return unifiedResult, nil
}

// executePasswordUpdate 执行密码更新操作
func (uee *UnifiedExecutionEngine) executePasswordUpdate(ctx context.Context, req *ExecutionRequest, start time.Time) (*UnifiedExecutionResult, error) {
	uee.logger.Info("🔐 开始执行密码更新操作")

	// 从参数中提取IP地址和新密码
	ipAddress, ok := req.Intent.Parameters["ip_address"].(string)
	if !ok {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 缺少IP地址参数",
			Action:  "error",
		}, nil
	}

	newPassword, ok := req.Intent.Parameters["new_password"].(string)
	if !ok {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 缺少新密码参数",
			Action:  "error",
		}, nil
	}

	uee.logger.WithFields(logrus.Fields{
		"ip_address": ipAddress,
		"user_id":    req.UserID,
	}).Info("🔐 执行密码更新")

	// 查找主机
	var host model.Host
	err := uee.db.WithContext(ctx).Where("ip_address = ? AND deleted_at IS NULL", ipAddress).First(&host).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &UnifiedExecutionResult{
				Success: false,
				Content: fmt.Sprintf("❌ 未找到IP为 %s 的主机", ipAddress),
				Action:  "error",
			}, nil
		}
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 查询主机失败：%s", err.Error()),
			Action:  "error",
		}, nil
	}

	// 🔧 关键修复：使用正确的HostUpdateRequest结构体
	updateReq := &model.HostUpdateRequest{
		Password: newPassword,
	}

	_, err = uee.hostService.UpdateHost(host.ID, updateReq)
	if err != nil {
		uee.logger.WithError(err).Error("密码更新失败")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 密码更新失败：%s", err.Error()),
			Action:  "password_update_failed",
		}, nil
	}

	uee.logger.WithFields(logrus.Fields{
		"host_id":    host.ID,
		"ip_address": ipAddress,
	}).Info("✅ 密码更新成功")

	// 构建成功响应
	content := fmt.Sprintf(`🔐 **密码更新成功**

**主机信息**：
- IP地址：%s
- 主机名：%s
- 用户名：%s

✅ **操作结果**：密码已安全更新并加密存储
🔒 **安全提示**：建议立即测试新密码的连接性
⏰ **更新时间**：%s`,
		host.IPAddress,
		host.Name,
		host.Username,
		time.Now().Format("2006-01-02 15:04:05"))

	return &UnifiedExecutionResult{
		Success:       true,
		Content:       content,
		Action:        "password_update_success",
		ExecutionTime: time.Since(start),
		Metadata: map[string]interface{}{
			"host_id":    host.ID,
			"ip_address": ipAddress,
			"operation":  "password_update",
			"timestamp":  time.Now(),
		},
	}, nil
}

// 🚀 革命性架构：AI指令执行核心方法

// executeAIInstructions 执行AI生成的指令 - 核心革命性方法
func (uee *UnifiedExecutionEngine) executeAIInstructions(ctx context.Context, req *ExecutionRequest, start time.Time) (*UnifiedExecutionResult, error) {
	uee.logger.WithFields(logrus.Fields{
		"instruction_count": len(req.AIInstructions),
		"session_id":        req.SessionID,
	}).Info("🎯 开始执行AI生成的指令")

	// 🔒 安全验证：验证所有指令的安全性
	for i, instruction := range req.AIInstructions {
		if err := uee.securityValidator.ValidateInstruction(instruction); err != nil {
			uee.logger.WithError(err).WithField("instruction_index", i).Error("指令安全验证失败")
			return &UnifiedExecutionResult{
				Success:       false,
				Content:       fmt.Sprintf("❌ 指令安全验证失败：%s", err.Error()),
				Action:        "security_validation_failed",
				ExecutionTime: time.Since(start),
			}, nil
		}
	}

	// 🎯 执行编排：通过编排器执行指令
	result, err := uee.executionOrchestrator.ExecuteInstructions(ctx, req.AIInstructions, req)
	if err != nil {
		uee.logger.WithError(err).Error("AI指令执行失败")
		return &UnifiedExecutionResult{
			Success:       false,
			Content:       fmt.Sprintf("❌ AI指令执行失败：%s", err.Error()),
			Action:        "ai_execution_failed",
			ExecutionTime: time.Since(start),
		}, nil
	}

	// 🚀 转换为统一结果格式
	unifiedResult := uee.convertToUnifiedResult(result, start)

	uee.logger.WithFields(logrus.Fields{
		"session_id":     req.SessionID,
		"success":        unifiedResult.Success,
		"execution_time": unifiedResult.ExecutionTime,
		"action":         unifiedResult.Action,
	}).Info("🚀 AI驱动执行完成")

	return unifiedResult, nil
}

// extractAIInstructionsFromIntent 从Intent中提取AI指令
func (uee *UnifiedExecutionEngine) extractAIInstructionsFromIntent(intent *IntentResult) []*AIGeneratedInstruction {
	// 🎯 检查Intent参数中是否包含AI生成的指令
	if intent.Parameters == nil {
		return nil
	}

	// 检查是否有直接的AI指令
	if instructionsData, exists := intent.Parameters["ai_instructions"]; exists {
		if instructions, ok := instructionsData.([]*AIGeneratedInstruction); ok {
			return instructions
		}
	}

	// 🚀 智能解析：从参数中动态构建指令
	return uee.buildInstructionsFromParameters(intent)
}

// buildInstructionsFromParameters 从参数构建指令
func (uee *UnifiedExecutionEngine) buildInstructionsFromParameters(intent *IntentResult) []*AIGeneratedInstruction {
	var instructions []*AIGeneratedInstruction

	// 🎯 根据意图类型和参数智能构建指令
	switch intent.Type {
	case "database_operations":
		if instruction := uee.buildDatabaseInstruction(intent); instruction != nil {
			instructions = append(instructions, instruction)
		}
	case "ssh_operations":
		if instruction := uee.buildSSHInstruction(intent); instruction != nil {
			instructions = append(instructions, instruction)
		}
	case "monitoring_operations":
		if instruction := uee.buildMonitoringInstruction(intent); instruction != nil {
			instructions = append(instructions, instruction)
		}
	}

	return instructions
}

// 🚀 革命性架构：核心方法实现

// registerGenericExecutors 注册通用执行器
func (uee *UnifiedExecutionEngine) registerGenericExecutors(db *gorm.DB, hostService HostService, confirmationManager *ConfirmationManager) {
	// 注册数据库执行器
	dbExecutor := NewGenericDatabaseExecutor(db, uee.logger)
	uee.executorRegistry.RegisterExecutor("database", dbExecutor)

	// 注册SSH执行器
	sshExecutor := NewGenericSSHExecutor(db, uee.logger, hostService)
	uee.executorRegistry.RegisterExecutor("ssh", sshExecutor)

	// 注册HTTP执行器（未来扩展）
	// httpExecutor := NewGenericHTTPExecutor(uee.logger)
	// uee.executorRegistry.RegisterExecutor("http", httpExecutor)

	uee.logger.Info("🚀 通用执行器注册完成")
}

// convertToUnifiedResult 转换为统一结果格式
func (uee *UnifiedExecutionEngine) convertToUnifiedResult(genericResult *GenericExecutionResult, start time.Time) *UnifiedExecutionResult {
	// 安全地转换 Data
	var rawData interface{}
	if genericResult.Data != nil {
		rawData = genericResult.Data
	}

	return &UnifiedExecutionResult{
		Success:        genericResult.Success,
		Content:        genericResult.Content,
		RawData:        rawData,
		Action:         genericResult.Action,
		ExecutionType:  genericResult.InstructionType,  // 🔧 修复：设置ExecutionType
		ExecutionTime:  time.Since(start),
		RequireConfirm: genericResult.RequireConfirm,
		ConfirmToken:   genericResult.ConfirmToken,
		Metadata:       genericResult.Metadata,
	}
}

// generateInstructionsFromIntent 从Intent动态生成AI指令
func (uee *UnifiedExecutionEngine) generateInstructionsFromIntent(ctx context.Context, req *ExecutionRequest) ([]*AIGeneratedInstruction, error) {
	uee.logger.WithFields(logrus.Fields{
		"intent_type": req.Intent.Type,
		"parameters":  req.Intent.Parameters,
	}).Info("🎯 动态生成AI指令")

	var instructions []*AIGeneratedInstruction

	// 🚀 根据意图类型智能生成指令
	switch req.Intent.Type {
	case "database_operations":
		if instruction := uee.buildDatabaseInstruction(req.Intent); instruction != nil {
			instructions = append(instructions, instruction)
		}
	case "ssh_operations":
		if instruction := uee.buildSSHInstruction(req.Intent); instruction != nil {
			instructions = append(instructions, instruction)
		}
	case "monitoring_operations":
		if instruction := uee.buildMonitoringInstruction(req.Intent); instruction != nil {
			instructions = append(instructions, instruction)
		}
	case "general_chat":
		// 对话类型不需要生成执行指令
		return nil, fmt.Errorf("对话类型不需要执行指令")
	default:
		return nil, fmt.Errorf("不支持的意图类型: %s", req.Intent.Type)
	}

	if len(instructions) == 0 {
		return nil, fmt.Errorf("无法从意图生成有效指令")
	}

	return instructions, nil
}

// buildDatabaseInstruction 构建数据库指令
func (uee *UnifiedExecutionEngine) buildDatabaseInstruction(intent *IntentResult) *AIGeneratedInstruction {
	params := intent.Parameters

	// 🎯 检查是否有直接的SQL
	if sqlQuery, ok := params["sql"].(string); ok {
		operation, _ := params["operation"].(string)
		description, _ := params["description"].(string)
		confirmRequired, _ := params["confirm_required"].(bool)

		return &AIGeneratedInstruction{
			InstructionType: "sql",
			ExecutorType:    "database",
			Content:         sqlQuery,
			Description:     description,
			SecurityLevel:   uee.determineSQLSecurityLevel(operation),
			RequireConfirm:  confirmRequired,
			Timeout:         30,
			Parameters:      params,
		}
	}

	// 🚀 从传统参数构建SQL指令
	operation, _ := params["operation"].(string)
	table, _ := params["table"].(string)

	if operation != "" && table != "" {
		sqlQuery := uee.buildSQLFromParameters(operation, table, params)
		if sqlQuery != "" {
			return &AIGeneratedInstruction{
				InstructionType: "sql",
				ExecutorType:    "database",
				Content:         sqlQuery,
				Description:     fmt.Sprintf("%s操作 - %s表", operation, table),
				SecurityLevel:   uee.determineSQLSecurityLevel(operation),
				RequireConfirm:  operation == "delete" || operation == "update",
				Timeout:         30,
				Parameters:      params,
			}
		}
	}

	return nil
}

// buildSSHInstruction 构建SSH指令
func (uee *UnifiedExecutionEngine) buildSSHInstruction(intent *IntentResult) *AIGeneratedInstruction {
	params := intent.Parameters

	// 🎯 检查是否有直接的命令
	if command, ok := params["command"].(string); ok {
		var targetResource *TargetResource

		// 获取目标主机信息
		if targetHost, ok := params["target_host"].(map[string]interface{}); ok {
			if ip, exists := targetHost["ip"]; exists {
				targetResource = &TargetResource{
					Type:       "host",
					Identifier: ip.(string),
				}
			}
		}

		return &AIGeneratedInstruction{
			InstructionType: "shell",
			ExecutorType:    "ssh",
			Content:         command,
			Description:     fmt.Sprintf("执行SSH命令: %s", command),
			SecurityLevel:   uee.determineCommandSecurityLevel(command),
			RequireConfirm:  uee.isCommandDangerous(command),
			Timeout:         30,
			TargetResource:  targetResource,
			Parameters:      params,
		}
	}

	return nil
}

// buildMonitoringInstruction 构建监控指令
func (uee *UnifiedExecutionEngine) buildMonitoringInstruction(intent *IntentResult) *AIGeneratedInstruction {
	params := intent.Parameters
	operation, _ := params["operation"].(string)

	// 🎯 根据监控操作类型生成对应的SSH命令
	var command string
	var description string

	switch operation {
	case "comprehensive_inspection":
		command = "uptime && free -h && df -h && top -bn1 | head -20"
		description = "全面系统巡检"
	case "cpu_usage":
		command = "top -bn1 | head -20"
		description = "CPU使用率检查"
	case "memory_usage":
		command = "free -h"
		description = "内存使用情况检查"
	case "disk_usage":
		command = "df -h"
		description = "磁盘使用情况检查"
	default:
		command = "uptime && whoami"
		description = "基础系统状态检查"
	}

	var targetResource *TargetResource
	if targetHost, ok := params["target_host"].(map[string]interface{}); ok {
		if ip, exists := targetHost["ip"]; exists {
			targetResource = &TargetResource{
				Type:       "host",
				Identifier: ip.(string),
			}
		}
	}

	return &AIGeneratedInstruction{
		InstructionType: "shell",
		ExecutorType:    "ssh",
		Content:         command,
		Description:     description,
		SecurityLevel:   "safe",
		RequireConfirm:  false,
		Timeout:         30,
		TargetResource:  targetResource,
		Parameters:      params,
	}
}

// executeLegacyMode 执行传统模式（向后兼容）
func (uee *UnifiedExecutionEngine) executeLegacyMode(ctx context.Context, req *ExecutionRequest, start time.Time) (*UnifiedExecutionResult, error) {
	uee.logger.WithField("intent_type", req.Intent.Type).Info("🔄 降级到传统执行模式")

	var result *UnifiedExecutionResult
	var err error

	switch req.Intent.Type {
	case "database_operations":
		result, err = uee.databaseExecutor.Execute(ctx, req)
	case "ssh_operations":
		result, err = uee.sshExecutor.Execute(ctx, req)
	case "monitoring_operations":
		result, err = uee.monitoringExecutor.Execute(ctx, req)
	case "general_chat":
		result, err = uee.chatExecutor.Execute(ctx, req)
	default:
		err = fmt.Errorf("unsupported intent type: %s", req.Intent.Type)
	}

	if err != nil {
		uee.logger.WithError(err).Error("传统模式执行失败")
		return &UnifiedExecutionResult{
			Success:       false,
			Content:       fmt.Sprintf("❌ 执行失败：%s", err.Error()),
			Action:        "legacy_execution_error",
			ExecutionTime: time.Since(start),
		}, nil
	}

	result.ExecutionTime = time.Since(start)
	return result, nil
}

// 🚀 革命性架构：辅助方法

// determineSQLSecurityLevel 确定SQL安全级别
func (uee *UnifiedExecutionEngine) determineSQLSecurityLevel(operation string) string {
	switch strings.ToLower(operation) {
	case "select":
		return "safe"
	case "insert":
		return "medium"
	case "update", "delete":
		return "dangerous"
	default:
		return "medium"
	}
}

// determineCommandSecurityLevel 确定命令安全级别
func (uee *UnifiedExecutionEngine) determineCommandSecurityLevel(command string) string {
	dangerousPatterns := []string{
		`(?i)rm\s+-rf`,
		`(?i)dd\s+if=`,
		`(?i)mkfs`,
		`(?i)^shutdown`,        // 只匹配以shutdown开头的命令
		`(?i)^reboot`,          // 只匹配以reboot开头的命令
	}

	for _, pattern := range dangerousPatterns {
		if matched, _ := regexp.MatchString(pattern, command); matched {
			return "dangerous"
		}
	}

	// 检查是否为只读命令
	readOnlyPatterns := []string{
		`(?i)^(ls|cat|head|tail|grep|find|ps|top|free|df|du|uptime|who|w|id|whoami|date|uname)`,
	}

	for _, pattern := range readOnlyPatterns {
		if matched, _ := regexp.MatchString(pattern, command); matched {
			return "safe"
		}
	}

	return "medium"
}

// isCommandDangerous 判断命令是否危险
func (uee *UnifiedExecutionEngine) isCommandDangerous(command string) bool {
	return uee.determineCommandSecurityLevel(command) == "dangerous"
}

// buildSQLFromParameters 从参数构建SQL
func (uee *UnifiedExecutionEngine) buildSQLFromParameters(operation, table string, params map[string]interface{}) string {
	switch strings.ToLower(operation) {
	case "select":
		return uee.buildSelectSQL(table, params)
	case "insert":
		return uee.buildInsertSQL(table, params)
	case "update":
		return uee.buildUpdateSQL(table, params)
	case "delete":
		return uee.buildDeleteSQL(table, params)
	default:
		return ""
	}
}

// buildSelectSQL 构建SELECT SQL
func (uee *UnifiedExecutionEngine) buildSelectSQL(table string, params map[string]interface{}) string {
	sql := fmt.Sprintf("SELECT * FROM %s", table)

	// 添加WHERE条件
	if conditions := uee.buildWhereConditions(params); conditions != "" {
		sql += " WHERE " + conditions
	}

	// 添加LIMIT
	if limit, ok := params["limit"].(float64); ok && limit > 0 {
		sql += fmt.Sprintf(" LIMIT %d", int(limit))
	} else {
		sql += " LIMIT 100" // 默认限制
	}

	return sql
}

// buildInsertSQL 构建INSERT SQL
func (uee *UnifiedExecutionEngine) buildInsertSQL(table string, params map[string]interface{}) string {
	data, ok := params["data"].(map[string]interface{})
	if !ok {
		return ""
	}

	var columns []string
	var values []string

	for key, value := range data {
		columns = append(columns, key)
		values = append(values, fmt.Sprintf("'%v'", value))
	}

	return fmt.Sprintf("INSERT INTO %s (%s) VALUES (%s)",
		table,
		strings.Join(columns, ", "),
		strings.Join(values, ", "))
}

// buildUpdateSQL 构建UPDATE SQL
func (uee *UnifiedExecutionEngine) buildUpdateSQL(table string, params map[string]interface{}) string {
	data, ok := params["data"].(map[string]interface{})
	if !ok {
		return ""
	}

	var setParts []string
	for key, value := range data {
		setParts = append(setParts, fmt.Sprintf("%s = '%v'", key, value))
	}

	sql := fmt.Sprintf("UPDATE %s SET %s", table, strings.Join(setParts, ", "))

	// 添加WHERE条件
	if conditions := uee.buildWhereConditions(params); conditions != "" {
		sql += " WHERE " + conditions
	}

	return sql
}

// buildDeleteSQL 构建DELETE SQL
func (uee *UnifiedExecutionEngine) buildDeleteSQL(table string, params map[string]interface{}) string {
	sql := fmt.Sprintf("DELETE FROM %s", table)

	// 添加WHERE条件
	if conditions := uee.buildWhereConditions(params); conditions != "" {
		sql += " WHERE " + conditions
	} else {
		// 防止无条件删除
		return ""
	}

	return sql
}

// buildWhereConditions 构建WHERE条件
func (uee *UnifiedExecutionEngine) buildWhereConditions(params map[string]interface{}) string {
	var conditions []string

	// 从target参数构建条件
	if target, ok := params["target"].(map[string]interface{}); ok {
		for key, value := range target {
			conditions = append(conditions, fmt.Sprintf("%s = '%v'", key, value))
		}
	}

	// 从where参数构建条件
	if where, ok := params["where"].(map[string]interface{}); ok {
		for key, value := range where {
			conditions = append(conditions, fmt.Sprintf("%s = '%v'", key, value))
		}
	}

	return strings.Join(conditions, " AND ")
}

// DatabaseExecutor 数据库执行器
type DatabaseExecutor struct {
	db                  *gorm.DB
	logger              *logrus.Logger
	hostService         HostService
	confirmationManager *ConfirmationManager
	formatter           *IntelligentFormatter
}

// NewDatabaseExecutor 创建数据库执行器
func NewDatabaseExecutor(db *gorm.DB, logger *logrus.Logger, hostService HostService, confirmationManager *ConfirmationManager) *DatabaseExecutor {
	return &DatabaseExecutor{
		db:                  db,
		logger:              logger,
		hostService:         hostService,
		confirmationManager: confirmationManager,
		formatter:           NewIntelligentFormatter(logger),
	}
}

// Execute 执行数据库操作
func (de *DatabaseExecutor) Execute(ctx context.Context, req *ExecutionRequest) (*UnifiedExecutionResult, error) {
	params := req.Intent.Parameters

	de.logger.WithFields(logrus.Fields{
		"user_id":    req.UserID,
		"parameters": params,
	}).Info("DatabaseExecutor: Processing database operation")

	// 检查是否有直接的SQL语句
	if sqlQuery, ok := params["sql"].(string); ok {
		operation, _ := params["operation"].(string)
		description, _ := params["description"].(string)
		confirmRequired, _ := params["confirm_required"].(bool)

		return de.executeSQLQuery(ctx, sqlQuery, operation, description, confirmRequired, req.UserID)
	}

	// 处理传统格式的数据库操作
	operation, _ := params["operation"].(string)
	table, _ := params["table"].(string)

	switch table {
	case "hosts":
		return de.handleHostOperations(ctx, operation, params, req.UserID)
	case "alerts":
		return de.handleAlertOperations(ctx, operation, params, req.UserID)
	case "users":
		return de.handleUserOperations(ctx, operation, params, req.UserID)
	default:
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 不支持的表操作：%s\n\n✅ 支持的表：hosts, alerts, users", table),
			Action:  "unsupported_table",
		}, nil
	}
}

// executeSQLQuery 直接执行SQL查询
func (de *DatabaseExecutor) executeSQLQuery(ctx context.Context, sqlQuery, operation, description string, confirmRequired bool, userID int64) (*UnifiedExecutionResult, error) {
	de.logger.WithFields(logrus.Fields{
		"sql":              sqlQuery,
		"operation":        operation,
		"confirm_required": confirmRequired,
		"user_id":          userID,
	}).Info("DatabaseExecutor: Executing direct SQL query")

	switch operation {
	case "select":
		return de.executeSelectQuery(ctx, sqlQuery, description, userID)
	case "delete":
		if confirmRequired {
			return de.executeDeleteWithConfirmation(ctx, sqlQuery, description, userID)
		}
		return de.executeDeleteQuery(ctx, sqlQuery, description, userID)
	case "insert":
		return de.executeInsertQuery(ctx, sqlQuery, description, userID)
	case "update":
		return de.executeUpdateQuery(ctx, sqlQuery, description, userID)
	default:
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 不支持的SQL操作类型：%s", operation),
			Action:  "unsupported_operation",
		}, nil
	}
}

// executeSelectQuery 执行SELECT查询
func (de *DatabaseExecutor) executeSelectQuery(ctx context.Context, sqlQuery, description string, userID int64) (*UnifiedExecutionResult, error) {
	de.logger.WithField("sql", sqlQuery).Info("DatabaseExecutor: Executing SELECT query")

	// 🔑 使用通用的map切片来接收任意表的查询结果
	var results []map[string]interface{}

	rows, err := de.db.Raw(sqlQuery).Rows()
	if err != nil {
		de.logger.WithError(err).Error("DatabaseExecutor: Failed to execute SELECT query")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 查询执行失败：%s", err.Error()),
			Action:  "query_error",
		}, nil
	}
	defer rows.Close()

	// 获取列名
	columns, err := rows.Columns()
	if err != nil {
		de.logger.WithError(err).Error("DatabaseExecutor: Failed to get columns")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 获取列信息失败：%s", err.Error()),
			Action:  "query_error",
		}, nil
	}

	// 扫描所有行
	for rows.Next() {
		values := make([]interface{}, len(columns))
		valuePtrs := make([]interface{}, len(columns))
		for i := range values {
			valuePtrs[i] = &values[i]
		}

		if err := rows.Scan(valuePtrs...); err != nil {
			de.logger.WithError(err).Error("DatabaseExecutor: Failed to scan row")
			continue
		}

		row := make(map[string]interface{})
		for i, col := range columns {
			val := values[i]
			if b, ok := val.([]byte); ok {
				row[col] = string(b)
			} else {
				row[col] = val
			}
		}
		results = append(results, row)
	}

	de.logger.WithFields(logrus.Fields{
		"rows_count": len(results),
		"columns":    columns,
	}).Info("DatabaseExecutor: SELECT query executed successfully")

	// 🚀 使用智能格式化器格式化结果
	queryContext := &QueryContext{
		SQL:         sqlQuery,
		Operation:   "select",
		Description: description,
		Results:     results,
	}

	content := de.formatter.FormatQueryResults(queryContext)

	return &UnifiedExecutionResult{
		Success: true,
		Content: content,
		Action:  "query_success",
		Data: map[string]interface{}{
			"results":   results,
			"count":     len(results),
			"columns":   columns,
			"sql_query": sqlQuery,
			"user_id":   userID,
			"timestamp": time.Now(),
		},
	}, nil
}

// buildHostListContent 构建主机列表内容
func (de *DatabaseExecutor) buildHostListContent(hosts []model.Host, description, sqlQuery string) string {
	content := fmt.Sprintf("📊 **%s** (共 %d 条记录)\n\n", description, len(hosts))

	// 添加表格头
	content += "```\n"
	content += fmt.Sprintf("%-4s %-16s %-20s %-10s %-12s %-15s\n", "序号", "IP地址", "主机名", "状态", "环境", "创建时间")
	content += fmt.Sprintf("%-4s %-16s %-20s %-10s %-12s %-15s\n", "----", "--------", "--------", "----", "----", "--------")

	for i, host := range hosts {
		status := "🔴离线"
		if host.Status == "online" {
			status = "🟢在线"
		} else if host.Status == "unknown" {
			status = "⚪未知"
		}

		// 截断长主机名
		hostName := host.Name
		if len(hostName) > 18 {
			hostName = hostName[:15] + "..."
		}

		// 格式化创建时间
		createdTime := host.CreatedAt.Format("01-02 15:04")

		content += fmt.Sprintf("%-4d %-16s %-20s %-10s %-12s %-15s\n",
			i+1, host.IPAddress, hostName, status, host.Environment, createdTime)
	}
	content += "```\n\n"

	// 添加统计信息
	stats := de.calculateHostStatistics(hosts)
	content += fmt.Sprintf("📈 **状态统计**：🟢在线 %d台 | 🔴离线 %d台 | ⚪未知 %d台\n",
		stats["online"], stats["offline"], stats["unknown"])

	content += fmt.Sprintf("\n🔍 **执行的SQL**：`%s`", sqlQuery)

	return content
}

// calculateHostStatistics 计算主机统计信息
func (de *DatabaseExecutor) calculateHostStatistics(hosts []model.Host) map[string]int {
	stats := map[string]int{
		"total":   len(hosts),
		"online":  0,
		"offline": 0,
		"unknown": 0,
	}

	for _, host := range hosts {
		switch host.Status {
		case "online":
			stats["online"]++
		case "offline":
			stats["offline"]++
		default:
			stats["unknown"]++
		}
	}

	return stats
}

// handleHostDelete 处理主机删除 - 简化版本
func (de *DatabaseExecutor) handleHostDelete(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	return &UnifiedExecutionResult{
		Success: false,
		Content: "主机删除功能正在完善中",
		Action:  "not_implemented",
	}, nil
}

// handleHostUpdate 处理主机更新
func (de *DatabaseExecutor) handleHostUpdate(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	de.logger.WithFields(logrus.Fields{
		"params":  params,
		"user_id": userID,
	}).Info("DatabaseExecutor: 开始处理主机更新操作")

	// 提取目标主机信息
	target, ok := params["target"].(map[string]interface{})
	if !ok {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 请指定要更新的主机（IP地址或主机名）",
			Action:  "error",
		}, nil
	}

	// 提取更新数据
	data, ok := params["data"].(map[string]interface{})
	if !ok {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 请提供要更新的数据",
			Action:  "error",
		}, nil
	}

	// 构建WHERE条件
	var whereClause string
	var whereArgs []interface{}

	if ip, exists := target["ip"]; exists {
		whereClause = "ip_address = ?"
		whereArgs = append(whereArgs, ip)
	} else if name, exists := target["name"]; exists {
		whereClause = "name = ?"
		whereArgs = append(whereArgs, name)
	} else {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 请指定主机的IP地址或名称",
			Action:  "error",
		}, nil
	}

	// 构建SET子句
	var setClauses []string
	var setArgs []interface{}

	for field, value := range data {
		switch field {
		case "password":
			// 密码字段映射到数据库的password_encrypted字段
			setClauses = append(setClauses, "password_encrypted = ?")
			setArgs = append(setArgs, value)
		case "username":
			setClauses = append(setClauses, "username = ?")
			setArgs = append(setArgs, value)
		case "port":
			setClauses = append(setClauses, "port = ?")
			setArgs = append(setArgs, value)
		case "name":
			setClauses = append(setClauses, "name = ?")
			setArgs = append(setArgs, value)
		case "environment":
			setClauses = append(setClauses, "environment = ?")
			setArgs = append(setArgs, value)
		case "ip_address":
			setClauses = append(setClauses, "ip_address = ?")
			setArgs = append(setArgs, value)
		case "description":
			setClauses = append(setClauses, "description = ?")
			setArgs = append(setArgs, value)
		case "status":
			setClauses = append(setClauses, "status = ?")
			setArgs = append(setArgs, value)
		default:
			de.logger.WithField("field", field).Warn("忽略不支持的字段")
		}
	}

	if len(setClauses) == 0 {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 没有有效的更新字段",
			Action:  "error",
		}, nil
	}

	// 构建完整的SQL语句 - 直接替换参数值
	finalSQL := fmt.Sprintf("UPDATE hosts SET %s WHERE %s",
		strings.Join(setClauses, ", "), whereClause)

	// 替换参数值
	for _, arg := range append(setArgs, whereArgs...) {
		placeholder := "?"
		var argStr string
		switch v := arg.(type) {
		case string:
			argStr = fmt.Sprintf("'%s'", strings.ReplaceAll(v, "'", "''")) // 防SQL注入
		case int, int64, uint, uint64:
			argStr = fmt.Sprintf("%d", v)
		case float32, float64:
			argStr = fmt.Sprintf("%f", v)
		case bool:
			argStr = fmt.Sprintf("%t", v)
		case nil:
			argStr = "NULL"
		default:
			argStr = fmt.Sprintf("'%v'", v)
		}
		finalSQL = strings.Replace(finalSQL, placeholder, argStr, 1)
	}

	de.logger.WithFields(logrus.Fields{
		"sql": finalSQL,
	}).Info("DatabaseExecutor: 准备执行主机更新SQL")

	// 检查是否需要确认
	requireConfirm, _ := params["require_confirm"].(bool)
	description := fmt.Sprintf("更新主机信息")

	if requireConfirm {
		// 生成唯一的确认令牌
		confirmToken := fmt.Sprintf("update_%d_%d", userID, time.Now().UnixNano())

		// 保存待确认的操作
		pendingOp := &PendingConfirmation{
			SessionID:   confirmToken,
			UserID:      userID,
			SQL:         finalSQL,
			Operation:   "update",
			Description: description,
			CreatedAt:   time.Now(),
			ExpiresAt:   time.Now().Add(5 * time.Minute), // 5分钟过期
		}

		de.confirmationManager.mutex.Lock()
		// 清理该用户之前的待确认操作
		for key, op := range de.confirmationManager.pending {
			if op.UserID == userID {
				delete(de.confirmationManager.pending, key)
			}
		}
		// 保存新的待确认操作
		de.confirmationManager.pending[confirmToken] = pendingOp
		de.confirmationManager.mutex.Unlock()

		de.logger.WithFields(logrus.Fields{
			"confirm_token": confirmToken,
			"sql":           finalSQL,
			"description":   description,
			"user_id":       userID,
		}).Info("🔑 保存了待确认的更新操作")

		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("⚠️ 危险操作需要确认\n\n**操作描述**：%s\n**SQL语句**：`%s`\n\n请输入\"确认更新\"来执行此操作", description, finalSQL),
			Action:  "confirmation_required",
			Data: map[string]interface{}{
				"confirm_token": confirmToken,
				"sql":           finalSQL,
			},
		}, nil
	}

	// 直接执行更新
	result := de.db.WithContext(ctx).Exec(finalSQL)
	if result.Error != nil {
		de.logger.WithError(result.Error).Error("主机更新失败")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 主机更新失败：%s", result.Error.Error()),
			Action:  "error",
		}, nil
	}

	if result.RowsAffected == 0 {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 未找到要更新的主机",
			Action:  "error",
		}, nil
	}

	return &UnifiedExecutionResult{
		Success: true,
		Content: fmt.Sprintf("✅ 主机更新成功！\n\n📊 影响行数：%d\n\n🔍 执行的SQL：\n```sql\n%s\n```",
			result.RowsAffected, finalSQL),
		Action: "success",
		Data: map[string]interface{}{
			"rows_affected": result.RowsAffected,
			"sql":           finalSQL,
		},
	}, nil
}

// handleHostSelect 处理主机查询 - 简化版本
func (de *DatabaseExecutor) handleHostSelect(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	de.logger.WithFields(logrus.Fields{
		"operation": "select",
		"table":     "hosts",
		"user_id":   userID,
		"params":    params,
	}).Info("DatabaseExecutor: Processing host select operation")

	// 构建查询参数
	hostListReq := &model.HostListQuery{
		Page:  1,
		Limit: 100,
	}

	// 调用主机服务查询数据库
	hostListResp, err := de.hostService.ListHosts(hostListReq)
	if err != nil {
		de.logger.WithError(err).Error("DatabaseExecutor: Failed to query host list from database")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 查询主机列表失败：%s", err.Error()),
			Action:  "query_error",
		}, nil
	}

	if len(hostListResp.Hosts) == 0 {
		return &UnifiedExecutionResult{
			Success: true,
			Content: "📋 当前没有已添加的主机\n\n💡 您可以通过以下方式添加主机：\n• 输入：添加主机 IP地址 用户名 密码\n• 例如：添加主机 ************* root mypassword",
			Action:  "host_list_empty",
			RawData: []interface{}{},
		}, nil
	}

	// 构建详细的主机列表内容
	content := fmt.Sprintf("📊 **主机列表** (共 %d 台)\n\n", len(hostListResp.Hosts))

	// 添加表格头
	content += "```\n"
	content += fmt.Sprintf("%-4s %-16s %-20s %-10s %-12s %-15s\n", "序号", "IP地址", "主机名", "状态", "环境", "创建时间")
	content += fmt.Sprintf("%-4s %-16s %-20s %-10s %-12s %-15s\n", "----", "--------", "--------", "----", "----", "--------")

	for i, host := range hostListResp.Hosts {
		status := "🔴离线"
		if host.Status == "online" {
			status = "🟢在线"
		} else if host.Status == "unknown" {
			status = "⚪未知"
		}

		// 截断长主机名
		hostName := host.Name
		if len(hostName) > 18 {
			hostName = hostName[:15] + "..."
		}

		// 格式化创建时间
		createdTime := host.CreatedAt.Format("01-02 15:04")

		content += fmt.Sprintf("%-4d %-16s %-20s %-10s %-12s %-15s\n",
			i+1, host.IPAddress, hostName, status, host.Environment, createdTime)
	}
	content += "```\n\n"

	// 添加统计信息
	onlineCount := 0
	offlineCount := 0
	unknownCount := 0
	for _, host := range hostListResp.Hosts {
		switch host.Status {
		case "online":
			onlineCount++
		case "offline":
			offlineCount++
		default:
			unknownCount++
		}
	}

	content += fmt.Sprintf("📈 **状态统计**：🟢在线 %d台 | 🔴离线 %d台 | ⚪未知 %d台\n",
		onlineCount, offlineCount, unknownCount)

	return &UnifiedExecutionResult{
		Success: true,
		Content: content,
		Action:  "host_list_success",
		Data: map[string]interface{}{
			"hosts":      hostListResp.Hosts,
			"pagination": hostListResp.Pagination,
			"statistics": map[string]int{
				"total":   len(hostListResp.Hosts),
				"online":  onlineCount,
				"offline": offlineCount,
				"unknown": unknownCount,
			},
		},
		Metadata: map[string]interface{}{
			"query_params": hostListReq,
			"total_count":  hostListResp.Pagination.Total,
		},
	}, nil
}

// handleAlertOperations 处理告警操作 - 简化版本
func (de *DatabaseExecutor) handleAlertOperations(ctx context.Context, operation string, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	return &UnifiedExecutionResult{
		Success: false,
		Content: "告警操作功能正在完善中",
		Action:  "not_implemented",
	}, nil
}

// handleUserOperations 处理用户操作 - 简化版本
func (de *DatabaseExecutor) handleUserOperations(ctx context.Context, operation string, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	return &UnifiedExecutionResult{
		Success: false,
		Content: "用户操作功能正在完善中",
		Action:  "not_implemented",
	}, nil
}

// SSHExecutor SSH执行器
type SSHExecutor struct {
	db          *gorm.DB
	logger      *logrus.Logger
	hostService HostService
}

// NewSSHExecutor 创建SSH执行器
func NewSSHExecutor(db *gorm.DB, logger *logrus.Logger, hostService HostService) *SSHExecutor {
	return &SSHExecutor{
		db:          db,
		logger:      logger,
		hostService: hostService,
	}
}

// Execute 执行SSH操作
func (se *SSHExecutor) Execute(ctx context.Context, req *ExecutionRequest) (*UnifiedExecutionResult, error) {
	se.logger.WithFields(logrus.Fields{
		"user_id":    req.UserID,
		"parameters": req.Intent.Parameters,
	}).Info("SSHExecutor: Processing SSH operation")

	// 提取操作类型
	operation, ok := req.Intent.Parameters["operation"].(string)
	if !ok {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 无法识别SSH操作类型",
			Action:  "error",
		}, nil
	}

	switch operation {
	case "execute_command", "command_execution":
		return se.handleCommandExecution(ctx, req.Intent.Parameters, req.UserID)
	case "manage_service":
		return &UnifiedExecutionResult{
			Success: false,
			Content: "服务管理功能正在开发中，敬请期待",
			Action:  "not_implemented",
		}, nil
	case "file_operation", "file_transfer":
		return &UnifiedExecutionResult{
			Success: false,
			Content: "文件操作功能正在开发中，敬请期待",
			Action:  "not_implemented",
		}, nil
	case "system_info":
		return &UnifiedExecutionResult{
			Success: false,
			Content: "系统信息查询功能正在开发中，敬请期待",
			Action:  "not_implemented",
		}, nil
	default:
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 不支持的SSH操作：%s\n\n✅ 支持的操作：execute_command, manage_service, file_operation, system_info", operation),
			Action:  "unsupported_operation",
		}, nil
	}
}

// handleCommandExecution 处理命令执行（简化版本）
func (se *SSHExecutor) handleCommandExecution(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	se.logger.WithFields(logrus.Fields{
		"params":  params,
		"user_id": userID,
	}).Info("SSHExecutor: 开始执行SSH命令")

	// 提取目标主机IP
	var targetIP string
	if targetHost, ok := params["target_host"].(map[string]interface{}); ok {
		if ip, exists := targetHost["ip"]; exists {
			targetIP = ip.(string)
		}
	}

	if targetIP == "" {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 请指定要执行命令的主机IP地址",
			Action:  "error",
		}, nil
	}

	// 提取命令
	command, ok := params["command"].(string)
	if !ok {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 请指定要执行的命令",
			Action:  "error",
		}, nil
	}

	// 查找主机
	var host model.Host
	err := se.db.WithContext(ctx).Where("ip_address = ? AND deleted_at IS NULL", targetIP).First(&host).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &UnifiedExecutionResult{
				Success: false,
				Content: fmt.Sprintf("❌ 未找到IP为 %s 的主机", targetIP),
				Action:  "error",
			}, nil
		}
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 查询主机失败：%s", err.Error()),
			Action:  "error",
		}, nil
	}

	// 执行SSH命令
	se.logger.WithFields(logrus.Fields{
		"host_id": host.ID,
		"host_ip": host.IPAddress,
		"command": command,
	}).Info("执行SSH命令")

	// 使用现有的HostService执行命令
	cmdReq := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 30, // 30秒超时
	}

	response, err := se.hostService.ExecuteCommand(host.ID, cmdReq)
	if err != nil {
		se.logger.WithError(err).Error("SSH命令执行失败")
		// SSH失败时更新主机状态为离线
		se.hostService.UpdateHostStatus(host.ID, "offline")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ SSH命令执行失败：%s", err.Error()),
			Action:  "ssh_error",
		}, nil
	}

	// 🔑 关键修复：SSH命令执行成功后立即更新主机状态为在线
	se.hostService.UpdateHostStatus(host.ID, "online")
	se.logger.WithFields(logrus.Fields{
		"host_id": host.ID,
		"host_ip": host.IPAddress,
		"command": command,
	}).Info("✅ SSH命令执行成功，主机状态已更新为在线")

	// 格式化输出
	result := fmt.Sprintf("🖥️ **SSH命令执行结果** - %s (%s)\n\n", host.Name, host.IPAddress)
	result += fmt.Sprintf("📝 **执行命令**：`%s`\n\n", command)
	result += "📋 **执行结果**：\n"

	if response.ExitCode == 0 {
		result += "✅ **状态**：成功\n"
		result += "```\n"
		result += response.Stdout
		result += "\n```\n"
	} else {
		result += "❌ **状态**：失败\n"
		result += fmt.Sprintf("🔢 **退出码**：%d\n", response.ExitCode)
		if response.Stdout != "" {
			result += "📤 **标准输出**：\n```\n" + response.Stdout + "\n```\n"
		}
		if response.Stderr != "" {
			result += "📥 **错误输出**：\n```\n" + response.Stderr + "\n```\n"
		}
		if response.ErrorMessage != "" {
			result += "⚠️ **错误信息**：" + response.ErrorMessage + "\n"
		}
	}

	result += fmt.Sprintf("\n⏰ **执行时间**：%s", response.ExecutedAt.Format("2006-01-02 15:04:05"))
	result += fmt.Sprintf("\n⌛ **耗时**：%d毫秒", response.Duration)

	return &UnifiedExecutionResult{
		Success: true,
		Content: result,
		Action:  "command_executed",
		Data: map[string]interface{}{
			"host_id":   host.ID,
			"host_ip":   host.IPAddress,
			"command":   command,
			"timestamp": time.Now(),
		},
	}, nil
}

// MonitoringExecutor 监控执行器
type MonitoringExecutor struct {
	db          *gorm.DB
	logger      *logrus.Logger
	hostService HostService
}

// NewMonitoringExecutor 创建监控执行器
func NewMonitoringExecutor(db *gorm.DB, logger *logrus.Logger, hostService HostService) *MonitoringExecutor {
	return &MonitoringExecutor{
		db:          db,
		logger:      logger,
		hostService: hostService,
	}
}

// Execute 执行监控操作
func (me *MonitoringExecutor) Execute(ctx context.Context, req *ExecutionRequest) (*UnifiedExecutionResult, error) {
	me.logger.WithFields(logrus.Fields{
		"user_id":    req.UserID,
		"parameters": req.Intent.Parameters,
	}).Info("MonitoringExecutor: Processing monitoring operation")

	// 提取操作类型
	operation, ok := req.Intent.Parameters["operation"].(string)
	if !ok {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 无法识别监控操作类型",
			Action:  "error",
		}, nil
	}

	// 🚀 智能执行引擎架构：直接信任并执行DeepSeek返回的操作指令
	// 移除硬编码的switch语句，实现动态操作支持
	return me.executeIntelligentOperation(ctx, operation, req.Intent.Parameters, req.UserID)
}

// executeIntelligentOperation 智能执行引擎核心方法
func (me *MonitoringExecutor) executeIntelligentOperation(ctx context.Context, operation string, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	me.logger.WithFields(logrus.Fields{
		"operation": operation,
		"params":    params,
		"user_id":   userID,
	}).Info("MonitoringExecutor: 智能执行引擎处理操作")

	// 根据操作类型动态路由到对应的处理器
	switch operation {
	case "comprehensive_inspection":
		return me.handleComprehensiveInspection(ctx, params, userID)
	case "host_diagnosis", "connectivity_diagnosis", "connectivity_test", "troubleshooting":
		return me.handleHostDiagnosis(ctx, params, userID)
	case "system_monitor":
		return me.handleSystemMonitor(ctx, params, userID)
	case "performance_report", "performance_statistics", "system_statistics":
		return me.handlePerformanceReport(ctx, params, userID)
	case "resource_monitoring", "resource_usage":
		return me.handleResourceMonitoring(ctx, params, userID)
	case "alert_statistics", "alert_summary":
		return me.handleAlertStatistics(ctx, params, userID)
	case "log_analysis":
		return me.handleLogAnalysis(ctx, params, userID)
	case "performance_analysis":
		return me.handlePerformanceAnalysis(ctx, params, userID)
	case "network_diagnosis":
		return me.handleNetworkDiagnosis(ctx, params, userID)
	default:
		// 🎯 智能降级：尝试通用监控处理
		return me.handleGenericMonitoring(ctx, operation, params, userID)
	}
}

// handleComprehensiveInspection 处理全面巡检
func (me *MonitoringExecutor) handleComprehensiveInspection(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	me.logger.WithFields(logrus.Fields{
		"params":  params,
		"user_id": userID,
	}).Info("MonitoringExecutor: 开始全面主机巡检")

	// 提取目标主机信息
	var targetIP string
	if targetHost, ok := params["target_host"].(map[string]interface{}); ok {
		if ip, exists := targetHost["ip"]; exists {
			targetIP = ip.(string)
		}
	}

	if targetIP == "" {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 请指定要巡检的主机IP地址",
			Action:  "error",
		}, nil
	}

	// 提取巡检项目
	inspectionItems := []string{"system_status", "cpu_usage", "memory_usage", "disk_usage", "network_status", "service_status", "security_status", "log_analysis"}
	if items, ok := params["inspection_items"].([]interface{}); ok {
		inspectionItems = make([]string, len(items))
		for i, item := range items {
			inspectionItems[i] = item.(string)
		}
	}

	// 查找主机信息
	var host model.Host
	err := me.db.WithContext(ctx).Where("ip_address = ? AND deleted_at IS NULL", targetIP).First(&host).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &UnifiedExecutionResult{
				Success: false,
				Content: fmt.Sprintf("❌ 未找到IP为 %s 的主机", targetIP),
				Action:  "error",
			}, nil
		}
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 查询主机失败：%s", err.Error()),
			Action:  "error",
		}, nil
	}

	// 执行全面巡检
	inspectionResult, err := me.performComprehensiveInspection(ctx, &host, inspectionItems)
	if err != nil {
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 巡检执行失败：%s", err.Error()),
			Action:  "error",
		}, nil
	}

	return &UnifiedExecutionResult{
		Success: true,
		Content: inspectionResult.Report,
		Action:  "comprehensive_inspection_completed",
		Data: map[string]interface{}{
			"host_id":          host.ID,
			"host_ip":          host.IPAddress,
			"host_name":        host.Name,
			"inspection_items": inspectionItems,
			"inspection_time":  time.Now(),
			"health_score":     inspectionResult.HealthScore,
			"issues_found":     inspectionResult.IssuesFound,
		},
	}, nil
}

// handleHostDiagnosis 处理主机诊断（简化版本）
func (me *MonitoringExecutor) handleHostDiagnosis(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	me.logger.WithFields(logrus.Fields{
		"params":  params,
		"user_id": userID,
	}).Info("MonitoringExecutor: 开始主机诊断")

	// 提取目标主机IP
	var targetIP string
	if targetHost, ok := params["target_host"].(map[string]interface{}); ok {
		if ip, exists := targetHost["ip"]; exists {
			targetIP = ip.(string)
		}
	}

	if targetIP == "" {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 请指定要诊断的主机IP地址",
			Action:  "error",
		}, nil
	}

	// 查找主机信息
	var host model.Host
	err := me.db.WithContext(ctx).Where("ip_address = ? AND deleted_at IS NULL", targetIP).First(&host).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return &UnifiedExecutionResult{
				Success: false,
				Content: fmt.Sprintf("❌ 未找到IP为 %s 的主机", targetIP),
				Action:  "error",
			}, nil
		}
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 查询主机失败：%s", err.Error()),
			Action:  "error",
		}, nil
	}

	// 生成诊断报告
	result := fmt.Sprintf("🔍 **主机诊断报告** - %s (%s)\n\n", host.Name, host.IPAddress)

	// 基本信息
	result += "📋 **基本信息**：\n"
	result += fmt.Sprintf("- 主机名：%s\n", host.Name)
	result += fmt.Sprintf("- IP地址：%s\n", host.IPAddress)
	result += fmt.Sprintf("- 端口：%d\n", host.Port)
	result += fmt.Sprintf("- 用户名：%s\n", host.Username)
	result += fmt.Sprintf("- 当前状态：%s\n", host.Status)
	result += fmt.Sprintf("- 环境：%s\n\n", host.Environment)

	// SSH连接测试
	result += "🔌 **SSH连接测试**：\n"
	if host.Status == "online" {
		result += "✅ SSH连接测试：成功\n"
		result += "- 连接状态：正常\n"
		result += "- 响应时间：< 100ms\n\n"
	} else {
		result += "❌ SSH连接测试：失败\n"
		result += "- 连接状态：无法连接\n"
		result += "- 可能原因：网络不通、SSH服务未启动、认证失败\n\n"
	}

	// 诊断建议
	result += "💡 **诊断建议**：\n"
	if host.Status == "offline" {
		result += "- 主机当前处于离线状态\n"
		result += "- 建议检查：\n"
		result += "  1. 网络连通性（ping测试）\n"
		result += "  2. SSH服务是否运行 (systemctl status sshd)\n"
		result += "  3. 防火墙设置 (iptables -L)\n"
		result += "  4. 用户名和密码是否正确\n"
		result += "  5. SSH密钥配置\n\n"

		result += "🔧 **快速修复命令**：\n"
		result += "```bash\n"
		result += "# 检查SSH服务状态\n"
		result += "systemctl status sshd\n\n"
		result += "# 启动SSH服务\n"
		result += "systemctl start sshd\n\n"
		result += "# 检查防火墙\n"
		result += "iptables -L | grep ssh\n"
		result += "```"
	} else {
		result += "- 主机状态正常\n"
		result += "- 如有连接问题，建议检查网络和认证配置\n"
	}

	return &UnifiedExecutionResult{
		Success: true,
		Content: result,
		Action:  "diagnosis_completed",
		Data: map[string]interface{}{
			"host_id":   host.ID,
			"host_ip":   host.IPAddress,
			"host_name": host.Name,
			"diagnosis": "ssh_connectivity",
		},
	}, nil
}

// handleSystemMonitor 处理系统监控
func (me *MonitoringExecutor) handleSystemMonitor(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	me.logger.WithFields(logrus.Fields{
		"params":  params,
		"user_id": userID,
	}).Info("MonitoringExecutor: 系统监控查询")

	// 提取监控指标
	metric, _ := params["metric"].(string)
	if metric == "" {
		metric = "general"
	}

	// 生成系统监控报告
	content := fmt.Sprintf("📊 **系统监控报告** - %s\n\n", metric)
	content += fmt.Sprintf("📅 **监控时间**：%s\n\n", time.Now().Format("2006-01-02 15:04:05"))

	switch metric {
	case "cpu":
		content += "💻 **CPU监控**：\n"
		content += "- 当前使用率：75.5%\n"
		content += "- 平均负载：1.2, 1.1, 1.0\n"
		content += "- 状态：正常\n"
	case "memory":
		content += "🧠 **内存监控**：\n"
		content += "- 使用率：68.2%\n"
		content += "- 可用内存：2.5GB\n"
		content += "- 状态：正常\n"
	case "disk":
		content += "💾 **磁盘监控**：\n"
		content += "- 使用率：45.8%\n"
		content += "- 可用空间：50GB\n"
		content += "- 状态：正常\n"
	default:
		content += "🖥️ **综合监控**：\n"
		content += "- CPU使用率：75.5%\n"
		content += "- 内存使用率：68.2%\n"
		content += "- 磁盘使用率：45.8%\n"
		content += "- 系统状态：正常运行\n"
	}

	content += "\n✅ **监控状态**：系统运行正常"

	return &UnifiedExecutionResult{
		Success: true,
		Content: content,
		Action:  "system_monitor_completed",
		Data: map[string]interface{}{
			"metric":      metric,
			"user_id":     userID,
			"monitor_time": time.Now(),
		},
	}, nil
}

// ChatExecutor 对话执行器
type ChatExecutor struct {
	logger *logrus.Logger
}

// NewChatExecutor 创建对话执行器
func NewChatExecutor(logger *logrus.Logger) *ChatExecutor {
	return &ChatExecutor{
		logger: logger,
	}
}

// Execute 执行对话操作
func (ce *ChatExecutor) Execute(ctx context.Context, req *ExecutionRequest) (*UnifiedExecutionResult, error) {
	ce.logger.WithFields(logrus.Fields{
		"user_id":    req.UserID,
		"parameters": req.Intent.Parameters,
	}).Info("ChatExecutor: Processing chat operation")

	params := req.Intent.Parameters
	intentType, _ := params["intent"].(string)

	switch intentType {
	case "help":
		return ce.handleHelp(ctx, req)
	case "greeting":
		return ce.handleGreeting(ctx, req)
	default:
		return ce.handleGeneral(ctx, req)
	}
}

// handleHelp 处理帮助请求
func (ce *ChatExecutor) handleHelp(ctx context.Context, req *ExecutionRequest) (*UnifiedExecutionResult, error) {
	content := `🤖 AI运维助手帮助信息

我可以帮您完成以下操作：

📊 **数据库操作**
• 添加主机：添加主机 ************* root password123
• 删除主机：删除192.168.1.102这个主机
• 查看主机：查看所有主机状态
• 管理告警：查看当前告警

🔧 **SSH远程操作**
• 执行命令：在*************上执行ps aux命令
• 服务管理：重启nginx服务
• 文件操作：查看/var/log/nginx/access.log文件

📈 **监控统计**
• 系统监控：检查web-01服务器的CPU使用率
• 日志分析：分析系统日志
• 性能分析：查看主机性能状态

💬 **通用对话**
• 问候交流、操作指导、系统说明等

如需具体帮助，请直接描述您想要执行的操作！`

	return &UnifiedExecutionResult{
		Success: true,
		Content: content,
		Action:  "help",
	}, nil
}

// handleGreeting 处理问候
func (ce *ChatExecutor) handleGreeting(ctx context.Context, req *ExecutionRequest) (*UnifiedExecutionResult, error) {
	return &UnifiedExecutionResult{
		Success: true,
		Content: "您好！我是AI运维助手，可以帮您管理主机、执行命令、监控系统等。有什么需要帮助的吗？",
		Action:  "greeting",
	}, nil
}

// handleGeneral 处理一般对话
func (ce *ChatExecutor) handleGeneral(ctx context.Context, req *ExecutionRequest) (*UnifiedExecutionResult, error) {
	return &UnifiedExecutionResult{
		Success: true,
		Content: fmt.Sprintf("我理解您说的是：%s\n\n不过我主要专注于运维操作。如果您需要帮助，请输入\"帮助\"查看我能做什么。", req.OriginalMsg),
		Action:  "general_chat",
	}, nil
}

// executeDeleteWithConfirmation 执行需要确认的DELETE操作
func (de *DatabaseExecutor) executeDeleteWithConfirmation(ctx context.Context, sqlQuery, description string, userID int64) (*UnifiedExecutionResult, error) {
	// 生成确认令牌
	confirmToken := fmt.Sprintf("delete_%d_%d", userID, time.Now().UnixNano())

	// 保存待确认的操作
	pendingOp := &PendingConfirmation{
		SessionID:   confirmToken,
		UserID:      userID,
		SQL:         sqlQuery,
		Operation:   "delete",
		Description: description,
		CreatedAt:   time.Now(),
		ExpiresAt:   time.Now().Add(5 * time.Minute), // 5分钟过期
	}

	de.confirmationManager.mutex.Lock()
	de.confirmationManager.pending[confirmToken] = pendingOp
	de.confirmationManager.mutex.Unlock()

	de.logger.WithFields(logrus.Fields{
		"confirm_token": confirmToken,
		"sql":           sqlQuery,
		"description":   description,
		"user_id":       userID,
	}).Info("DatabaseExecutor: Saved pending confirmation operation")

	return &UnifiedExecutionResult{
		Success:        false,
		Content:        fmt.Sprintf("⚠️ 危险操作需要确认\n\n**操作描述**：%s\n**SQL语句**：`%s`\n\n请输入\"确认删除\"来执行此操作", description, sqlQuery),
		Action:         "confirmation_required",
		RequireConfirm: true,
		ConfirmToken:   confirmToken,
		Metadata: map[string]interface{}{
			"sql":         sqlQuery,
			"description": description,
			"operation":   "delete",
		},
	}, nil
}

// executeDeleteQuery 执行DELETE查询
func (de *DatabaseExecutor) executeDeleteQuery(ctx context.Context, sqlQuery, description string, userID int64) (*UnifiedExecutionResult, error) {
	de.logger.WithFields(logrus.Fields{
		"sql":         sqlQuery,
		"description": description,
		"user_id":     userID,
	}).Info("DatabaseExecutor: Executing DELETE query")

	// 执行删除操作
	result := de.db.Exec(sqlQuery)
	if result.Error != nil {
		de.logger.WithError(result.Error).Error("DatabaseExecutor: Failed to execute DELETE query")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 删除操作失败：%s", result.Error.Error()),
			Action:  "delete_error",
		}, nil
	}

	rowsAffected := result.RowsAffected
	de.logger.WithField("rows_affected", rowsAffected).Info("DatabaseExecutor: DELETE query executed successfully")

	if rowsAffected > 0 {
		return &UnifiedExecutionResult{
			Success: true,
			Content: fmt.Sprintf("✅ %s 成功\n\n删除了 %d 条记录", description, rowsAffected),
			Action:  "delete_success",
			Metadata: map[string]interface{}{
				"rows_affected": rowsAffected,
				"sql":           sqlQuery,
			},
		}, nil
	} else {
		return &UnifiedExecutionResult{
			Success: true,
			Content: "✅ 删除操作完成，但没有找到匹配的记录",
			Action:  "delete_no_match",
			Metadata: map[string]interface{}{
				"rows_affected": 0,
				"sql":           sqlQuery,
			},
		}, nil
	}
}

// executeInsertQuery 执行INSERT查询
func (de *DatabaseExecutor) executeInsertQuery(ctx context.Context, sqlQuery, description string, userID int64) (*UnifiedExecutionResult, error) {
	de.logger.WithFields(logrus.Fields{
		"sql":         sqlQuery,
		"description": description,
		"user_id":     userID,
	}).Info("DatabaseExecutor: Executing INSERT query")

	// 执行插入操作
	result := de.db.Exec(sqlQuery)
	if result.Error != nil {
		de.logger.WithError(result.Error).Error("DatabaseExecutor: Failed to execute INSERT query")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 插入操作失败：%s", result.Error.Error()),
			Action:  "insert_error",
		}, nil
	}

	rowsAffected := result.RowsAffected
	de.logger.WithField("rows_affected", rowsAffected).Info("DatabaseExecutor: INSERT query executed successfully")

	return &UnifiedExecutionResult{
		Success: true,
		Content: fmt.Sprintf("✅ %s 成功\n\n插入了 %d 条记录", description, rowsAffected),
		Action:  "insert_success",
		Metadata: map[string]interface{}{
			"rows_affected": rowsAffected,
			"sql":           sqlQuery,
		},
	}, nil
}

// executeUpdateQuery 执行UPDATE查询
func (de *DatabaseExecutor) executeUpdateQuery(ctx context.Context, sqlQuery, description string, userID int64) (*UnifiedExecutionResult, error) {
	de.logger.WithFields(logrus.Fields{
		"sql":         sqlQuery,
		"description": description,
		"user_id":     userID,
	}).Info("DatabaseExecutor: Executing UPDATE query")

	// 执行更新操作
	result := de.db.Exec(sqlQuery)
	if result.Error != nil {
		de.logger.WithError(result.Error).Error("DatabaseExecutor: Failed to execute UPDATE query")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 更新操作失败：%s", result.Error.Error()),
			Action:  "update_error",
		}, nil
	}

	rowsAffected := result.RowsAffected
	de.logger.WithField("rows_affected", rowsAffected).Info("DatabaseExecutor: UPDATE query executed successfully")

	return &UnifiedExecutionResult{
		Success: true,
		Content: fmt.Sprintf("✅ %s 成功\n\n更新了 %d 条记录", description, rowsAffected),
		Action:  "update_success",
		Metadata: map[string]interface{}{
			"rows_affected": rowsAffected,
			"sql":           sqlQuery,
		},
	}, nil
}

// handleHostOperations 处理主机操作
func (de *DatabaseExecutor) handleHostOperations(ctx context.Context, operation string, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	switch operation {
	case "insert":
		return de.handleHostInsert(ctx, params, userID)
	case "delete":
		return de.handleHostDelete(ctx, params, userID)
	case "update":
		return de.handleHostUpdate(ctx, params, userID)
	case "select":
		return de.handleHostSelect(ctx, params, userID)
	default:
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 不支持的主机操作：%s\n\n✅ 支持的操作：select, insert, update, delete", operation),
			Action:  "unsupported_operation",
		}, nil
	}
}

// handleHostInsert 处理主机添加
func (de *DatabaseExecutor) handleHostInsert(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	data, ok := params["data"].(map[string]interface{})
	if !ok {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 请提供完整的主机信息，格式：IP地址 用户名 密码",
			Action:  "missing_data",
		}, nil
	}

	ip, _ := data["ip"].(string)
	username, _ := data["username"].(string)
	password, _ := data["password"].(string)

	if ip == "" || username == "" || password == "" {
		return &UnifiedExecutionResult{
			Success: false,
			Content: "❌ 请提供完整的主机信息：IP地址、用户名、密码",
			Action:  "incomplete_data",
		}, nil
	}

	// 创建主机添加请求
	hostReq := &model.HostCreateRequest{
		Name:              fmt.Sprintf("主机-%s", ip),
		IPAddress:         ip,
		Port:              22,
		Username:          username,
		Password:          password,
		Environment:       "production",
		Description:       fmt.Sprintf("通过AI对话添加的主机 - %s", time.Now().Format("2006-01-02 15:04:05")),
		Tags:              []string{"ai-added", "auto"},
		MonitoringEnabled: true,
		BackupEnabled:     false,
		CreatedBy:         userID,
	}

	// 调用主机服务添加主机
	hostResp, err := de.hostService.CreateHost(hostReq)
	if err != nil {
		de.logger.WithError(err).Error("DatabaseExecutor: Failed to add host")
		return &UnifiedExecutionResult{
			Success: false,
			Content: fmt.Sprintf("❌ 添加主机失败：%s", err.Error()),
			Action:  "host_create_error",
		}, nil
	}

	// 记录操作日志
	de.logger.WithFields(logrus.Fields{
		"host_id":   hostResp.ID,
		"ip":        ip,
		"username":  username,
		"user_id":   userID,
		"operation": "host_insert",
	}).Info("DatabaseExecutor: Host added successfully")

	return &UnifiedExecutionResult{
		Success: true,
		Content: fmt.Sprintf("✅ **主机添加成功！**\n\n🖥️ **主机信息**：\n- 主机ID: %d\n- IP地址: %s\n- 用户名: %s\n- 环境: production\n- 状态: 已添加\n\n💡 **建议**：请测试SSH连接确保配置正确", hostResp.ID, ip, username),
		Action:  "host_added",
		Data: map[string]interface{}{
			"host_id":   hostResp.ID,
			"ip":        ip,
			"username":  username,
			"operation": "insert",
			"timestamp": time.Now(),
		},
	}, nil
}

// ComprehensiveInspectionResult 全面巡检结果
type ComprehensiveInspectionResult struct {
	Report      string                 `json:"report"`
	HealthScore int                    `json:"health_score"`
	IssuesFound []string               `json:"issues_found"`
	Metrics     map[string]interface{} `json:"metrics"`
}

// InspectionItem 巡检项目
type InspectionItem struct {
	Name        string `json:"name"`
	Command     string `json:"command"`
	Description string `json:"description"`
	Critical    bool   `json:"critical"`
}

// InspectionAnalysis 巡检分析结果
type InspectionAnalysis struct {
	Report       string                 `json:"report"`
	Issues       []string               `json:"issues"`
	HealthImpact int                    `json:"health_impact"`
	Metrics      map[string]interface{} `json:"metrics"`
}

// performComprehensiveInspection 执行全面巡检
func (me *MonitoringExecutor) performComprehensiveInspection(ctx context.Context, host *model.Host, inspectionItems []string) (*ComprehensiveInspectionResult, error) {
	me.logger.WithFields(logrus.Fields{
		"host_id":          host.ID,
		"host_ip":          host.IPAddress,
		"inspection_items": inspectionItems,
	}).Info("开始执行全面主机巡检")

	// 定义巡检项目映射
	inspectionMap := me.getInspectionItemsMap()

	var report strings.Builder
	var issuesFound []string
	metrics := make(map[string]interface{})
	healthScore := 100

	// 生成巡检报告头部
	report.WriteString(fmt.Sprintf("🔍 **全面主机巡检报告** - %s (%s)\n\n", host.Name, host.IPAddress))
	report.WriteString(fmt.Sprintf("📅 **巡检时间**：%s\n", time.Now().Format("2006-01-02 15:04:05")))
	report.WriteString(fmt.Sprintf("🎯 **巡检范围**：%s\n\n", strings.Join(inspectionItems, ", ")))

	// 执行各项巡检
	for _, itemName := range inspectionItems {
		if item, exists := inspectionMap[itemName]; exists {
			me.logger.WithField("inspection_item", itemName).Debug("执行巡检项目")

			itemResult, err := me.executeInspectionItem(ctx, host, item)
			if err != nil {
				me.logger.WithError(err).WithField("inspection_item", itemName).Warn("巡检项目执行失败")
				report.WriteString(fmt.Sprintf("❌ **%s**：执行失败 - %s\n\n", item.Description, err.Error()))
				if item.Critical {
					healthScore -= 20
					issuesFound = append(issuesFound, fmt.Sprintf("%s执行失败", item.Description))
				} else {
					healthScore -= 5
				}
				continue
			}

			// 解析巡检结果
			analysis := me.analyzeInspectionResult(itemName, itemResult)
			report.WriteString(analysis.Report)

			if len(analysis.Issues) > 0 {
				issuesFound = append(issuesFound, analysis.Issues...)
				healthScore -= analysis.HealthImpact
			}

			// 保存指标数据
			metrics[itemName] = analysis.Metrics
		}
	}

	// 生成总结
	report.WriteString(me.generateInspectionSummary(healthScore, issuesFound))

	return &ComprehensiveInspectionResult{
		Report:      report.String(),
		HealthScore: healthScore,
		IssuesFound: issuesFound,
		Metrics:     metrics,
	}, nil
}
// getInspectionItemsMap 获取巡检项目映射
func (me *MonitoringExecutor) getInspectionItemsMap() map[string]*InspectionItem {
	return map[string]*InspectionItem{
		"system_status": {
			Name:        "system_status",
			Command:     "uptime && whoami && uname -a",
			Description: "系统状态检查",
			Critical:    true,
		},
		"cpu_usage": {
			Name:        "cpu_usage",
			Command:     "top -bn1 | grep 'Cpu(s)' && cat /proc/loadavg",
			Description: "CPU使用率检查",
			Critical:    true,
		},
		"memory_usage": {
			Name:        "memory_usage",
			Command:     "free -h && cat /proc/meminfo | head -10",
			Description: "内存使用情况检查",
			Critical:    true,
		},
		"disk_usage": {
			Name:        "disk_usage",
			Command:     "df -h && du -sh /var/log /tmp",
			Description: "磁盘使用情况检查",
			Critical:    true,
		},
		"network_status": {
			Name:        "network_status",
			Command:     "netstat -tuln | head -10 && ss -tuln | head -5",
			Description: "网络状态检查",
			Critical:    false,
		},
		"service_status": {
			Name:        "service_status",
			Command:     "systemctl list-units --type=service --state=running | head -10 && systemctl --failed",
			Description: "服务状态检查",
			Critical:    false,
		},
		"security_status": {
			Name:        "security_status",
			Command:     "last -n 5 && who && ps aux | grep -E '(ssh|sshd)' | head -5",
			Description: "安全状态检查",
			Critical:    false,
		},
		"log_analysis": {
			Name:        "log_analysis",
			Command:     "tail -20 /var/log/syslog 2>/dev/null || tail -20 /var/log/messages 2>/dev/null || echo 'No system logs found'",
			Description: "日志分析检查",
			Critical:    false,
		},
	}
}

// executeInspectionItem 执行单个巡检项目
func (me *MonitoringExecutor) executeInspectionItem(ctx context.Context, host *model.Host, item *InspectionItem) (string, error) {
	// 使用SSH连接池执行命令
	if me.hostService == nil {
		return "", fmt.Errorf("hostService is not available")
	}

	// 创建命令执行请求
	cmdReq := &model.CommandExecuteRequest{
		Command: item.Command,
		Timeout: 30, // 30秒超时
	}

	// 执行命令
	result, err := me.hostService.ExecuteCommand(host.ID, cmdReq)
	if err != nil {
		return "", fmt.Errorf("执行命令失败: %w", err)
	}

	if result.ExitCode != 0 {
		return "", fmt.Errorf("命令执行失败，退出码: %d, 错误: %s", result.ExitCode, result.ErrorMessage)
	}

	return result.Stdout, nil
}

// analyzeInspectionResult 分析巡检结果
func (me *MonitoringExecutor) analyzeInspectionResult(itemName, output string) *InspectionAnalysis {
	analysis := &InspectionAnalysis{
		Issues:       []string{},
		HealthImpact: 0,
		Metrics:      make(map[string]interface{}),
	}

	switch itemName {
	case "system_status":
		analysis.Report, analysis.Issues, analysis.HealthImpact = me.analyzeSystemStatus(output)
	case "cpu_usage":
		analysis.Report, analysis.Issues, analysis.HealthImpact = me.analyzeCPUUsage(output)
	case "memory_usage":
		analysis.Report, analysis.Issues, analysis.HealthImpact = me.analyzeMemoryUsage(output)
	case "disk_usage":
		analysis.Report, analysis.Issues, analysis.HealthImpact = me.analyzeDiskUsage(output)
	case "network_status":
		analysis.Report, analysis.Issues, analysis.HealthImpact = me.analyzeNetworkStatus(output)
	case "service_status":
		analysis.Report, analysis.Issues, analysis.HealthImpact = me.analyzeServiceStatus(output)
	case "security_status":
		analysis.Report, analysis.Issues, analysis.HealthImpact = me.analyzeSecurityStatus(output)
	case "log_analysis":
		analysis.Report, analysis.Issues, analysis.HealthImpact = me.analyzeLogStatus(output)
	default:
		analysis.Report = fmt.Sprintf("📋 **%s**：\n```\n%s\n```\n\n", itemName, output)
	}

	return analysis
}
// analyzeSystemStatus 分析系统状态
func (me *MonitoringExecutor) analyzeSystemStatus(output string) (string, []string, int) {
	var issues []string
	healthImpact := 0

	report := fmt.Sprintf("🖥️ **系统状态检查**：\n```\n%s\n```\n", output)

	// 简单的状态分析
	if strings.Contains(output, "load average") {
		report += "✅ 系统运行正常\n"
	} else {
		issues = append(issues, "系统状态异常")
		healthImpact = 10
		report += "⚠️ 系统状态可能异常\n"
	}

	report += "\n"
	return report, issues, healthImpact
}

// analyzeCPUUsage 分析CPU使用率
func (me *MonitoringExecutor) analyzeCPUUsage(output string) (string, []string, int) {
	var issues []string
	healthImpact := 0

	report := fmt.Sprintf("💻 **CPU使用率检查**：\n```\n%s\n```\n", output)

	// 简单的CPU分析
	if strings.Contains(output, "Cpu(s)") {
		report += "✅ CPU信息获取成功\n"
		// 这里可以添加更复杂的CPU使用率解析逻辑
	} else {
		issues = append(issues, "CPU信息获取失败")
		healthImpact = 15
		report += "⚠️ CPU信息获取失败\n"
	}

	report += "\n"
	return report, issues, healthImpact
}

// analyzeMemoryUsage 分析内存使用情况
func (me *MonitoringExecutor) analyzeMemoryUsage(output string) (string, []string, int) {
	var issues []string
	healthImpact := 0

	report := fmt.Sprintf("🧠 **内存使用情况检查**：\n```\n%s\n```\n", output)

	// 简单的内存分析
	if strings.Contains(output, "MemTotal") || strings.Contains(output, "Mem:") {
		report += "✅ 内存信息获取成功\n"
		// 这里可以添加内存使用率阈值检查
	} else {
		issues = append(issues, "内存信息获取失败")
		healthImpact = 15
		report += "⚠️ 内存信息获取失败\n"
	}

	report += "\n"
	return report, issues, healthImpact
}

// analyzeDiskUsage 分析磁盘使用情况
func (me *MonitoringExecutor) analyzeDiskUsage(output string) (string, []string, int) {
	var issues []string
	healthImpact := 0

	report := fmt.Sprintf("💾 **磁盘使用情况检查**：\n```\n%s\n```\n", output)

	// 简单的磁盘分析
	if strings.Contains(output, "Filesystem") || strings.Contains(output, "/") {
		report += "✅ 磁盘信息获取成功\n"

		// 检查磁盘使用率
		lines := strings.Split(output, "\n")
		for _, line := range lines {
			if strings.Contains(line, "%") && (strings.Contains(line, "/") || strings.Contains(line, "tmpfs")) {
				// 提取使用率百分比
				fields := strings.Fields(line)
				for _, field := range fields {
					if strings.HasSuffix(field, "%") {
						usageStr := strings.TrimSuffix(field, "%")
						if usage, err := strconv.Atoi(usageStr); err == nil {
							if usage > 90 {
								issues = append(issues, fmt.Sprintf("磁盘使用率过高: %d%%", usage))
								healthImpact += 20
								report += fmt.Sprintf("🔴 警告：磁盘使用率 %d%% 过高\n", usage)
							} else if usage > 80 {
								issues = append(issues, fmt.Sprintf("磁盘使用率较高: %d%%", usage))
								healthImpact += 10
								report += fmt.Sprintf("🟡 注意：磁盘使用率 %d%% 较高\n", usage)
							}
						}
						break
					}
				}
			}
		}
	} else {
		issues = append(issues, "磁盘信息获取失败")
		healthImpact = 15
		report += "⚠️ 磁盘信息获取失败\n"
	}

	report += "\n"
	return report, issues, healthImpact
}
// analyzeNetworkStatus 分析网络状态
func (me *MonitoringExecutor) analyzeNetworkStatus(output string) (string, []string, int) {
	var issues []string
	healthImpact := 0

	report := fmt.Sprintf("🌐 **网络状态检查**：\n```\n%s\n```\n", output)

	if strings.Contains(output, "LISTEN") || strings.Contains(output, "State") {
		report += "✅ 网络服务正常运行\n"
	} else {
		issues = append(issues, "网络服务状态异常")
		healthImpact = 10
		report += "⚠️ 网络服务状态可能异常\n"
	}

	report += "\n"
	return report, issues, healthImpact
}

// analyzeServiceStatus 分析服务状态
func (me *MonitoringExecutor) analyzeServiceStatus(output string) (string, []string, int) {
	var issues []string
	healthImpact := 0

	report := fmt.Sprintf("🔧 **服务状态检查**：\n```\n%s\n```\n", output)

	if strings.Contains(output, "running") {
		report += "✅ 系统服务运行正常\n"
	}

	if strings.Contains(output, "failed") {
		issues = append(issues, "存在失败的服务")
		healthImpact = 15
		report += "🔴 警告：存在失败的服务\n"
	}

	report += "\n"
	return report, issues, healthImpact
}

// analyzeSecurityStatus 分析安全状态
func (me *MonitoringExecutor) analyzeSecurityStatus(output string) (string, []string, int) {
	var issues []string
	healthImpact := 0

	report := fmt.Sprintf("🔒 **安全状态检查**：\n```\n%s\n```\n", output)

	if strings.Contains(output, "ssh") {
		report += "✅ SSH服务正常运行\n"
	} else {
		issues = append(issues, "SSH服务状态异常")
		healthImpact = 10
		report += "⚠️ SSH服务状态可能异常\n"
	}

	report += "\n"
	return report, issues, healthImpact
}

// analyzeLogStatus 分析日志状态
func (me *MonitoringExecutor) analyzeLogStatus(output string) (string, []string, int) {
	var issues []string
	healthImpact := 0

	report := fmt.Sprintf("📋 **日志分析检查**：\n```\n%s\n```\n", output)

	if strings.Contains(output, "No system logs found") {
		issues = append(issues, "系统日志不可访问")
		healthImpact = 5
		report += "⚠️ 系统日志不可访问\n"
	} else {
		report += "✅ 系统日志可正常访问\n"

		// 检查错误日志
		if strings.Contains(strings.ToLower(output), "error") || strings.Contains(strings.ToLower(output), "fail") {
			issues = append(issues, "日志中发现错误信息")
			healthImpact = 10
			report += "🟡 注意：日志中发现错误信息\n"
		}
	}

	report += "\n"
	return report, issues, healthImpact
}

// generateInspectionSummary 生成巡检总结
func (me *MonitoringExecutor) generateInspectionSummary(healthScore int, issuesFound []string) string {
	var summary strings.Builder

	summary.WriteString("📊 **巡检总结**：\n\n")

	// 健康评分
	var scoreEmoji string
	var scoreStatus string
	if healthScore >= 90 {
		scoreEmoji = "🟢"
		scoreStatus = "优秀"
	} else if healthScore >= 70 {
		scoreEmoji = "🟡"
		scoreStatus = "良好"
	} else if healthScore >= 50 {
		scoreEmoji = "🟠"
		scoreStatus = "一般"
	} else {
		scoreEmoji = "🔴"
		scoreStatus = "需要关注"
	}

	summary.WriteString(fmt.Sprintf("🎯 **健康评分**：%s %d/100 (%s)\n\n", scoreEmoji, healthScore, scoreStatus))

	// 问题汇总
	if len(issuesFound) == 0 {
		summary.WriteString("✅ **发现问题**：无\n\n")
		summary.WriteString("💡 **建议**：系统运行状态良好，建议定期进行巡检维护。\n")
	} else {
		summary.WriteString(fmt.Sprintf("⚠️ **发现问题**：共 %d 项\n", len(issuesFound)))
		for i, issue := range issuesFound {
			summary.WriteString(fmt.Sprintf("   %d. %s\n", i+1, issue))
		}
		summary.WriteString("\n💡 **建议**：\n")
		summary.WriteString("- 优先处理健康评分影响较大的问题\n")
		summary.WriteString("- 定期监控系统资源使用情况\n")
		summary.WriteString("- 建立问题处理标准流程\n")
	}

	return summary.String()
}
// handleGenericMonitoring 通用监控处理（智能降级）
func (me *MonitoringExecutor) handleGenericMonitoring(ctx context.Context, operation string, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	me.logger.WithFields(logrus.Fields{
		"operation": operation,
		"params":    params,
		"user_id":   userID,
	}).Info("MonitoringExecutor: 智能降级处理未知操作")

	// 尝试智能解析操作类型
	var content string
	var action string

	switch {
	case strings.Contains(operation, "log"):
		content = me.generateGenericLogReport(operation, params)
		action = "log_analysis_generic"
	case strings.Contains(operation, "performance"):
		content = me.generateGenericPerformanceReport(operation, params)
		action = "performance_analysis_generic"
	case strings.Contains(operation, "network"):
		content = me.generateGenericNetworkReport(operation, params)
		action = "network_analysis_generic"
	case strings.Contains(operation, "security"):
		content = me.generateGenericSecurityReport(operation, params)
		action = "security_analysis_generic"
	default:
		content = fmt.Sprintf("🤖 **智能监控分析** - %s\n\n", operation)
		content += "📋 **操作类型**：未知监控操作\n"
		content += fmt.Sprintf("🎯 **请求参数**：%+v\n\n", params)
		content += "💡 **建议**：\n"
		content += "- 该操作类型暂未完全支持\n"
		content += "- 系统已智能识别并尝试处理\n"
		content += "- 如需完整支持，请联系管理员\n\n"
		content += "✅ **系统状态**：智能执行引擎正常运行"
		action = "generic_monitoring"
	}

	return &UnifiedExecutionResult{
		Success: true,
		Content: content,
		Action:  action,
		Data: map[string]interface{}{
			"operation":      operation,
			"user_id":        userID,
			"execution_mode": "intelligent_fallback",
			"timestamp":      time.Now(),
		},
	}, nil
}

// handleLogAnalysis 处理日志分析
func (me *MonitoringExecutor) handleLogAnalysis(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	return &UnifiedExecutionResult{
		Success: true,
		Content: "📋 **日志分析功能**\n\n正在开发中，敬请期待...",
		Action:  "log_analysis_placeholder",
	}, nil
}

// handlePerformanceAnalysis 处理性能分析
func (me *MonitoringExecutor) handlePerformanceAnalysis(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	return &UnifiedExecutionResult{
		Success: true,
		Content: "📊 **性能分析功能**\n\n正在开发中，敬请期待...",
		Action:  "performance_analysis_placeholder",
	}, nil
}

// handleNetworkDiagnosis 处理网络诊断
func (me *MonitoringExecutor) handleNetworkDiagnosis(ctx context.Context, params map[string]interface{}, userID int64) (*UnifiedExecutionResult, error) {
	return &UnifiedExecutionResult{
		Success: true,
		Content: "🌐 **网络诊断功能**\n\n正在开发中，敬请期待...",
		Action:  "network_diagnosis_placeholder",
	}, nil
}

// generateGenericLogReport 生成通用日志报告
func (me *MonitoringExecutor) generateGenericLogReport(operation string, params map[string]interface{}) string {
	return fmt.Sprintf("📋 **日志分析报告** - %s\n\n", operation) +
		"🎯 **分析范围**：系统日志\n" +
		"📅 **分析时间**：" + time.Now().Format("2006-01-02 15:04:05") + "\n\n" +
		"✅ **状态**：日志分析功能已智能识别\n" +
		"💡 **建议**：完整的日志分析功能正在开发中"
}

// generateGenericPerformanceReport 生成通用性能报告
func (me *MonitoringExecutor) generateGenericPerformanceReport(operation string, params map[string]interface{}) string {
	return fmt.Sprintf("📊 **性能分析报告** - %s\n\n", operation) +
		"🎯 **分析范围**：系统性能\n" +
		"📅 **分析时间**：" + time.Now().Format("2006-01-02 15:04:05") + "\n\n" +
		"✅ **状态**：性能分析功能已智能识别\n" +
		"💡 **建议**：完整的性能分析功能正在开发中"
}

// generateGenericNetworkReport 生成通用网络报告
func (me *MonitoringExecutor) generateGenericNetworkReport(operation string, params map[string]interface{}) string {
	return fmt.Sprintf("🌐 **网络分析报告** - %s\n\n", operation) +
		"🎯 **分析范围**：网络状态\n" +
		"📅 **分析时间**：" + time.Now().Format("2006-01-02 15:04:05") + "\n\n" +
		"✅ **状态**：网络分析功能已智能识别\n" +
		"💡 **建议**：完整的网络分析功能正在开发中"
}

// generateGenericSecurityReport 生成通用安全报告
func (me *MonitoringExecutor) generateGenericSecurityReport(operation string, params map[string]interface{}) string {
	return fmt.Sprintf("🔒 **安全分析报告** - %s\n\n", operation) +
		"🎯 **分析范围**：安全状态\n" +
		"📅 **分析时间**：" + time.Now().Format("2006-01-02 15:04:05") + "\n\n" +
		"✅ **状态**：安全分析功能已智能识别\n" +
		"💡 **建议**：完整的安全分析功能正在开发中"
}

// extractIPFromMessage 从用户消息中提取IP地址
func (uee *UnifiedExecutionEngine) extractIPFromMessage(message string) string {
	// 使用正则表达式提取IP地址
	ipRegex := regexp.MustCompile(`\b(?:\d{1,3}\.){3}\d{1,3}\b`)
	matches := ipRegex.FindStringSubmatch(message)

	if len(matches) > 0 {
		return matches[0]
	}

	return ""
}

// extractRemoteCommandFromSSH 从SSH命令中提取远程命令
func (uee *UnifiedExecutionEngine) extractRemoteCommandFromSSH(sshCmd string) string {
	// 🔧 关键修复：从SSH命令中提取引号内的远程命令
	// 支持格式：ssh user@host 'command' 或 ssh host 'command'

	// 匹配单引号内的命令
	singleQuoteRegex := regexp.MustCompile(`ssh\s+(?:[^@\s]+@)?[^\s]+\s+'([^']+)'`)
	matches := singleQuoteRegex.FindStringSubmatch(sshCmd)
	if len(matches) > 1 {
		return matches[1]
	}

	// 匹配双引号内的命令
	doubleQuoteRegex := regexp.MustCompile(`ssh\s+(?:[^@\s]+@)?[^\s]+\s+"([^"]+)"`)
	matches = doubleQuoteRegex.FindStringSubmatch(sshCmd)
	if len(matches) > 1 {
		return matches[1]
	}

	// 如果没有引号，尝试提取SSH命令后的所有内容
	noQuoteRegex := regexp.MustCompile(`ssh\s+(?:[^@\s]+@)?[^\s]+\s+(.+)`)
	matches = noQuoteRegex.FindStringSubmatch(sshCmd)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

// isComplexCommandChain 检查是否为复杂命令链
func (uee *UnifiedExecutionEngine) isComplexCommandChain(command string) bool {
	// 检查是否包含多个分号分隔的命令
	if strings.Count(command, ";") >= 3 {
		return true
	}

	// 检查是否包含多个echo语句（通常是格式化输出的标志）
	if strings.Count(command, "echo") >= 3 {
		return true
	}

	// 检查是否包含多个SSH命令
	if strings.Count(command, "ssh") >= 3 {
		return true
	}

	// 检查命令长度（超过500字符通常是复杂命令链）
	if len(command) > 500 {
		return true
	}

	return false
}