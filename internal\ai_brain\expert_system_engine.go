package ai_brain

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ExpertSystemEngine 专家系统引擎
type ExpertSystemEngine struct {
	config *ExpertSystemConfig
	logger *logrus.Logger

	// 规则库
	rules map[string]*ExpertRule
	ruleGroups map[string][]*ExpertRule
	mutex sync.RWMutex

	// 推理引擎
	inferenceEngine *InferenceEngine
	
	// 统计信息
	rulesCount int64
	executionCount int64
	lastUpdate time.Time
}

// ExpertRule 专家规则
type ExpertRule struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	Priority    int                    `json:"priority"`
	Conditions  []*RuleCondition       `json:"conditions"`
	Actions     []*RuleAction          `json:"actions"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Enabled     bool                   `json:"enabled"`
	Weight      float64                `json:"weight"`
	Confidence  float64                `json:"confidence"`
}

// RuleCondition 规则条件
type RuleCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"`
	Value    interface{} `json:"value"`
	Type     string      `json:"type"`
	Weight   float64     `json:"weight"`
}

// RuleAction 规则动作
type RuleAction struct {
	Type       string                 `json:"type"`
	Target     string                 `json:"target"`
	Parameters map[string]interface{} `json:"parameters"`
	Priority   int                    `json:"priority"`
}

// InferenceEngine 推理引擎
type InferenceEngine struct {
	strategy string // forward, backward, hybrid
	logger   *logrus.Logger
}

// ExpertAnalysis 专家分析结果
type ExpertAnalysis struct {
	Summary         string                 `json:"summary"`
	MatchedRules    []*MatchedRule         `json:"matched_rules"`
	Recommendations []*ExpertRecommendation `json:"recommendations"`
	RiskFactors     []*RiskFactor          `json:"risk_factors"`
	Confidence      float64                `json:"confidence"`
	ProcessingTime  time.Duration          `json:"processing_time"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// MatchedRule 匹配的规则
type MatchedRule struct {
	Rule       *ExpertRule `json:"rule"`
	MatchScore float64     `json:"match_score"`
	Confidence float64     `json:"confidence"`
	Triggered  bool        `json:"triggered"`
	Reason     string      `json:"reason"`
}

// ExpertRecommendation 专家推荐
type ExpertRecommendation struct {
	Type        string                 `json:"type"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Priority    int                    `json:"priority"`
	Confidence  float64                `json:"confidence"`
	Actions     []string               `json:"actions"`
	Benefits    []string               `json:"benefits"`
	Risks       []string               `json:"risks"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// NewExpertSystemEngine 创建专家系统引擎
func NewExpertSystemEngine(config *ExpertSystemConfig, logger *logrus.Logger) (*ExpertSystemEngine, error) {
	if config == nil {
		config = &ExpertSystemConfig{
			Enabled:   true,
			RuleCount: 1000,
		}
	}

	engine := &ExpertSystemEngine{
		config:     config,
		logger:     logger,
		rules:      make(map[string]*ExpertRule),
		ruleGroups: make(map[string][]*ExpertRule),
		inferenceEngine: &InferenceEngine{
			strategy: "hybrid",
			logger:   logger,
		},
		rulesCount:     0,
		executionCount: 0,
		lastUpdate:     time.Now(),
	}

	// 初始化基础规则库
	if err := engine.initializeBaseRules(); err != nil {
		return nil, fmt.Errorf("failed to initialize base rules: %w", err)
	}

	logger.WithFields(logrus.Fields{
		"rule_count": engine.rulesCount,
		"strategy":   engine.inferenceEngine.strategy,
	}).Info("Expert System Engine initialized successfully")

	return engine, nil
}

// Analyze 专家分析
func (es *ExpertSystemEngine) Analyze(ctx context.Context, req *OperationRequest, enhancedContext *OperationContext) (*ExpertAnalysis, error) {
	start := time.Now()
	
	es.logger.WithFields(logrus.Fields{
		"operation_id":   req.ID,
		"operation_type": req.Type,
		"intent":         req.Intent,
	}).Debug("Expert system analysis started")

	// 1. 规则匹配
	matchedRules, err := es.matchRules(req, enhancedContext)
	if err != nil {
		return nil, fmt.Errorf("rule matching failed: %w", err)
	}

	// 2. 推理执行
	inferenceResult, err := es.inferenceEngine.Execute(matchedRules, req, enhancedContext)
	if err != nil {
		es.logger.WithError(err).Warn("Inference execution failed")
		inferenceResult = &InferenceResult{
			Conclusions: []string{},
			Confidence:  0.5,
		}
	}

	// 3. 生成推荐
	recommendations := es.generateRecommendations(matchedRules, inferenceResult)

	// 4. 识别风险因素
	riskFactors := es.identifyRiskFactors(matchedRules, req)

	// 5. 计算整体置信度
	overallConfidence := es.calculateOverallConfidence(matchedRules, inferenceResult)

	// 6. 生成分析摘要
	summary := es.generateAnalysisSummary(matchedRules, recommendations, riskFactors)

	analysis := &ExpertAnalysis{
		Summary:         summary,
		MatchedRules:    matchedRules,
		Recommendations: recommendations,
		RiskFactors:     riskFactors,
		Confidence:      overallConfidence,
		ProcessingTime:  time.Since(start),
		Metadata: map[string]interface{}{
			"rules_evaluated": len(es.rules),
			"rules_matched":   len(matchedRules),
			"inference_strategy": es.inferenceEngine.strategy,
		},
	}

	// 更新统计信息
	es.updateStats()

	es.logger.WithFields(logrus.Fields{
		"operation_id":      req.ID,
		"matched_rules":     len(matchedRules),
		"recommendations":   len(recommendations),
		"risk_factors":      len(riskFactors),
		"confidence":        overallConfidence,
		"processing_time":   analysis.ProcessingTime,
	}).Debug("Expert system analysis completed")

	return analysis, nil
}

// AddRule 添加专家规则
func (es *ExpertSystemEngine) AddRule(rule *ExpertRule) error {
	es.mutex.Lock()
	defer es.mutex.Unlock()

	rule.CreatedAt = time.Now()
	rule.UpdatedAt = time.Now()
	
	es.rules[rule.ID] = rule
	
	// 添加到分组
	es.ruleGroups[rule.Category] = append(es.ruleGroups[rule.Category], rule)
	
	es.rulesCount++
	es.lastUpdate = time.Now()

	es.logger.WithFields(logrus.Fields{
		"rule_id":   rule.ID,
		"rule_name": rule.Name,
		"category":  rule.Category,
		"priority":  rule.Priority,
	}).Debug("Expert rule added")

	return nil
}

// matchRules 匹配规则
func (es *ExpertSystemEngine) matchRules(req *OperationRequest, context *OperationContext) ([]*MatchedRule, error) {
	es.mutex.RLock()
	defer es.mutex.RUnlock()

	var matchedRules []*MatchedRule

	for _, rule := range es.rules {
		if !rule.Enabled {
			continue
		}

		matchScore, confidence, reason := es.evaluateRule(rule, req, context)
		if matchScore > 0.3 { // 匹配阈值
			matchedRule := &MatchedRule{
				Rule:       rule,
				MatchScore: matchScore,
				Confidence: confidence,
				Triggered:  matchScore > 0.7,
				Reason:     reason,
			}
			matchedRules = append(matchedRules, matchedRule)
		}
	}

	// 按匹配分数排序
	for i := 0; i < len(matchedRules)-1; i++ {
		for j := i + 1; j < len(matchedRules); j++ {
			if matchedRules[i].MatchScore < matchedRules[j].MatchScore {
				matchedRules[i], matchedRules[j] = matchedRules[j], matchedRules[i]
			}
		}
	}

	return matchedRules, nil
}

// evaluateRule 评估规则
func (es *ExpertSystemEngine) evaluateRule(rule *ExpertRule, req *OperationRequest, context *OperationContext) (float64, float64, string) {
	totalScore := 0.0
	totalWeight := 0.0
	var reasons []string

	for _, condition := range rule.Conditions {
		score, reason := es.evaluateCondition(condition, req, context)
		totalScore += score * condition.Weight
		totalWeight += condition.Weight
		
		if score > 0.5 {
			reasons = append(reasons, reason)
		}
	}

	if totalWeight == 0 {
		return 0.0, 0.0, "No valid conditions"
	}

	matchScore := totalScore / totalWeight
	confidence := matchScore * rule.Confidence
	reasonText := strings.Join(reasons, "; ")

	return matchScore, confidence, reasonText
}

// evaluateCondition 评估条件
func (es *ExpertSystemEngine) evaluateCondition(condition *RuleCondition, req *OperationRequest, context *OperationContext) (float64, string) {
	var fieldValue interface{}
	var reason string

	// 获取字段值
	switch condition.Field {
	case "intent":
		fieldValue = req.Intent
	case "type":
		fieldValue = req.Type
	case "priority":
		fieldValue = req.Priority
	case "environment":
		fieldValue = context.Environment
	case "security_level":
		fieldValue = context.SecurityLevel
	default:
		if val, exists := req.Parameters[condition.Field]; exists {
			fieldValue = val
		} else {
			return 0.0, fmt.Sprintf("Field %s not found", condition.Field)
		}
	}

	// 评估条件
	switch condition.Operator {
	case "equals":
		if fieldValue == condition.Value {
			reason = fmt.Sprintf("%s equals %v", condition.Field, condition.Value)
			return 1.0, reason
		}
		return 0.0, fmt.Sprintf("%s does not equal %v", condition.Field, condition.Value)
		
	case "contains":
		if str, ok := fieldValue.(string); ok {
			if valueStr, ok := condition.Value.(string); ok {
				if strings.Contains(strings.ToLower(str), strings.ToLower(valueStr)) {
					reason = fmt.Sprintf("%s contains %s", condition.Field, valueStr)
					return 1.0, reason
				}
			}
		}
		return 0.0, fmt.Sprintf("%s does not contain %v", condition.Field, condition.Value)
		
	case "greater_than":
		if num, ok := fieldValue.(float64); ok {
			if threshold, ok := condition.Value.(float64); ok {
				if num > threshold {
					reason = fmt.Sprintf("%s (%f) > %f", condition.Field, num, threshold)
					return 1.0, reason
				}
			}
		}
		return 0.0, fmt.Sprintf("%s not greater than %v", condition.Field, condition.Value)
		
	case "in_list":
		if list, ok := condition.Value.([]interface{}); ok {
			for _, item := range list {
				if fieldValue == item {
					reason = fmt.Sprintf("%s in list", condition.Field)
					return 1.0, reason
				}
			}
		}
		return 0.0, fmt.Sprintf("%s not in list", condition.Field)
		
	default:
		return 0.0, fmt.Sprintf("Unknown operator: %s", condition.Operator)
	}
}

// generateRecommendations 生成推荐
func (es *ExpertSystemEngine) generateRecommendations(matchedRules []*MatchedRule, inferenceResult *InferenceResult) []*ExpertRecommendation {
	var recommendations []*ExpertRecommendation

	for _, matchedRule := range matchedRules {
		if !matchedRule.Triggered {
			continue
		}

		for _, action := range matchedRule.Rule.Actions {
			if action.Type == "recommend" {
				rec := &ExpertRecommendation{
					Type:        action.Type,
					Title:       fmt.Sprintf("基于规则 %s 的建议", matchedRule.Rule.Name),
					Description: fmt.Sprintf("%s", action.Parameters["description"]),
					Priority:    action.Priority,
					Confidence:  matchedRule.Confidence,
					Actions:     []string{action.Target},
					Benefits:    []string{"提高操作成功率", "降低风险"},
					Risks:       []string{},
					Metadata: map[string]interface{}{
						"rule_id":    matchedRule.Rule.ID,
						"match_score": matchedRule.MatchScore,
					},
				}
				recommendations = append(recommendations, rec)
			}
		}
	}

	return recommendations
}

// identifyRiskFactors 识别风险因素
func (es *ExpertSystemEngine) identifyRiskFactors(matchedRules []*MatchedRule, req *OperationRequest) []*RiskFactor {
	var riskFactors []*RiskFactor

	for _, matchedRule := range matchedRules {
		if matchedRule.Rule.Category == "risk" && matchedRule.Triggered {
			riskFactor := &RiskFactor{
				Name:        matchedRule.Rule.Name,
				Description: matchedRule.Rule.Description,
				Severity:    "medium",
				Probability: matchedRule.MatchScore,
				Impact:      "operational",
			}
			
			// 根据规则优先级调整严重程度
			if matchedRule.Rule.Priority > 8 {
				riskFactor.Severity = "high"
			} else if matchedRule.Rule.Priority < 3 {
				riskFactor.Severity = "low"
			}
			
			riskFactors = append(riskFactors, riskFactor)
		}
	}

	return riskFactors
}

// calculateOverallConfidence 计算整体置信度
func (es *ExpertSystemEngine) calculateOverallConfidence(matchedRules []*MatchedRule, inferenceResult *InferenceResult) float64 {
	if len(matchedRules) == 0 {
		return 0.5 // 默认置信度
	}

	totalConfidence := 0.0
	totalWeight := 0.0

	for _, matchedRule := range matchedRules {
		weight := matchedRule.MatchScore
		totalConfidence += matchedRule.Confidence * weight
		totalWeight += weight
	}

	if totalWeight == 0 {
		return 0.5
	}

	baseConfidence := totalConfidence / totalWeight
	
	// 结合推理结果调整置信度
	adjustedConfidence := (baseConfidence + inferenceResult.Confidence) / 2

	return adjustedConfidence
}

// generateAnalysisSummary 生成分析摘要
func (es *ExpertSystemEngine) generateAnalysisSummary(matchedRules []*MatchedRule, recommendations []*ExpertRecommendation, riskFactors []*RiskFactor) string {
	triggeredRules := 0
	for _, rule := range matchedRules {
		if rule.Triggered {
			triggeredRules++
		}
	}

	summary := fmt.Sprintf("专家系统分析完成：匹配 %d 条规则，触发 %d 条规则，生成 %d 项建议，识别 %d 个风险因素。",
		len(matchedRules), triggeredRules, len(recommendations), len(riskFactors))

	if len(riskFactors) > 0 {
		summary += " 建议仔细评估风险后执行操作。"
	}

	return summary
}

// updateStats 更新统计信息
func (es *ExpertSystemEngine) updateStats() {
	es.mutex.Lock()
	defer es.mutex.Unlock()
	
	es.executionCount++
	es.lastUpdate = time.Now()
}

// initializeBaseRules 初始化基础规则库
func (es *ExpertSystemEngine) initializeBaseRules() error {
	baseRules := []*ExpertRule{
		{
			ID: "rule_host_management_safety", Name: "主机管理安全规则", Category: "safety",
			Description: "主机管理操作的安全检查", Priority: 9, Enabled: true, Weight: 1.0, Confidence: 0.9,
			Conditions: []*RuleCondition{
				{Field: "intent", Operator: "equals", Value: "host_management", Weight: 1.0},
			},
			Actions: []*RuleAction{
				{Type: "recommend", Target: "backup_before_change", Priority: 1,
					Parameters: map[string]interface{}{"description": "建议在修改主机配置前进行备份"}},
			},
		},
		{
			ID: "rule_high_risk_operation", Name: "高风险操作警告", Category: "risk",
			Description: "识别高风险操作并发出警告", Priority: 10, Enabled: true, Weight: 1.0, Confidence: 0.95,
			Conditions: []*RuleCondition{
				{Field: "security_level", Operator: "equals", Value: "high", Weight: 1.0},
			},
			Actions: []*RuleAction{
				{Type: "recommend", Target: "require_approval", Priority: 1,
					Parameters: map[string]interface{}{"description": "高风险操作需要额外审批"}},
			},
		},
		{
			ID: "rule_production_environment", Name: "生产环境保护", Category: "safety",
			Description: "生产环境操作的额外保护措施", Priority: 8, Enabled: true, Weight: 1.0, Confidence: 0.9,
			Conditions: []*RuleCondition{
				{Field: "environment", Operator: "equals", Value: "production", Weight: 1.0},
			},
			Actions: []*RuleAction{
				{Type: "recommend", Target: "extra_validation", Priority: 1,
					Parameters: map[string]interface{}{"description": "生产环境操作需要额外验证"}},
			},
		},
	}

	for _, rule := range baseRules {
		if err := es.AddRule(rule); err != nil {
			return fmt.Errorf("failed to add base rule %s: %w", rule.ID, err)
		}
	}

	return nil
}

// InferenceResult 推理结果
type InferenceResult struct {
	Conclusions []string `json:"conclusions"`
	Confidence  float64  `json:"confidence"`
}

// Execute 执行推理
func (ie *InferenceEngine) Execute(matchedRules []*MatchedRule, req *OperationRequest, context *OperationContext) (*InferenceResult, error) {
	// 简化实现：基于规则生成结论
	var conclusions []string
	totalConfidence := 0.0
	count := 0

	for _, matchedRule := range matchedRules {
		if matchedRule.Triggered {
			conclusion := fmt.Sprintf("根据规则 %s，建议 %s", matchedRule.Rule.Name, matchedRule.Rule.Description)
			conclusions = append(conclusions, conclusion)
			totalConfidence += matchedRule.Confidence
			count++
		}
	}

	confidence := 0.5
	if count > 0 {
		confidence = totalConfidence / float64(count)
	}

	return &InferenceResult{
		Conclusions: conclusions,
		Confidence:  confidence,
	}, nil
}
