package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/service"
	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockHostService 模拟主机服务
type MockHostService struct{}

func (m *MockHostService) CreateHost(req *model.HostCreateRequest) (*model.HostResponse, error) {
	return nil, nil
}

func (m *MockHostService) GetHostByID(id int64) (*model.HostResponse, error) {
	return nil, nil
}

func (m *MockHostService) GetHostByName(name string) (*model.HostResponse, error) {
	return nil, nil
}

func (m *MockHostService) UpdateHost(id int64, req *model.HostUpdateRequest) (*model.HostResponse, error) {
	return nil, nil
}

func (m *MockHostService) DeleteHost(id int64) error {
	return nil
}

func (m *MockHostService) ListHosts(req *model.HostListQuery) (*model.HostListResponse, error) {
	return nil, nil
}

func (m *MockHostService) TestConnection(id int64) (*model.HostTestResponse, error) {
	return &model.HostTestResponse{
		Success:  true,
		Message:  "Connection successful",
		Duration: 100,
		TestedAt: time.Now(),
	}, nil
}

func (m *MockHostService) ExecuteCommand(id int64, req *model.CommandExecuteRequest) (*model.CommandExecuteResponse, error) {
	// 模拟不同命令的输出
	var stdout string
	switch {
	case req.Command == "uptime && whoami && uname -a":
		stdout = " 10:30:01 up 5 days,  2:15,  1 user,  load average: 0.15, 0.05, 0.01\nroot\nLinux test-server 5.4.0-74-generic #83-Ubuntu SMP Sat May 8 02:35:39 UTC 2021 x86_64 x86_64 x86_64 GNU/Linux"
	case req.Command == "top -bn1 | grep 'Cpu(s)' && cat /proc/loadavg":
		stdout = "%Cpu(s):  2.3 us,  1.0 sy,  0.0 ni, 96.7 id,  0.0 wa,  0.0 hi,  0.0 si,  0.0 st\n0.15 0.05 0.01 1/123 12345"
	case req.Command == "free -h && cat /proc/meminfo | head -10":
		stdout = "              total        used        free      shared  buff/cache   available\nMem:           7.8G        2.1G        3.2G        123M        2.5G        5.4G\nSwap:          2.0G          0B        2.0G\nMemTotal:        8000000 kB\nMemFree:         3200000 kB\nMemAvailable:    5400000 kB"
	case req.Command == "df -h && du -sh /var/log /tmp":
		stdout = "Filesystem      Size  Used Avail Use% Mounted on\n/dev/sda1        20G   12G  7.2G  63% /\n/dev/sda2       100G   45G   50G  48% /home\n1.2G\t/var/log\n256M\t/tmp"
	case req.Command == "netstat -tuln | head -10 && ss -tuln | head -5":
		stdout = "Active Internet connections (only servers)\nProto Recv-Q Send-Q Local Address           Foreign Address         State\ntcp        0      0 0.0.0.0:22              0.0.0.0:*               LISTEN\ntcp        0      0 127.0.0.1:3306          0.0.0.0:*               LISTEN\nState      Recv-Q Send-Q Local Address:Port               Peer Address:Port\nLISTEN     0      128          0.0.0.0:22                     0.0.0.0:*"
	case req.Command == "systemctl list-units --type=service --state=running | head -10 && systemctl --failed":
		stdout = "UNIT                     LOAD   ACTIVE SUB     DESCRIPTION\nsshd.service             loaded active running OpenBSD Secure Shell server\nnginx.service            loaded active running A high performance web server\nmysql.service            loaded active running MySQL Community Server\n0 loaded units listed."
	case req.Command == "last -n 5 && who && ps aux | grep -E '(ssh|sshd)' | head -5":
		stdout = "root     pts/0        *************    Wed Oct 25 10:30   still logged in\nroot     console                       Wed Oct 25 09:00 - 10:29  (01:29)\nroot     pts/0        ************* Wed Oct 25 10:30:01 (pts/0)\nroot      1234  0.0  0.1  12345  6789 ?        Ss   09:00   0:00 /usr/sbin/sshd -D"
	case req.Command == "tail -20 /var/log/syslog 2>/dev/null || tail -20 /var/log/messages 2>/dev/null || echo 'No system logs found'":
		stdout = "Oct 25 10:30:01 test-server systemd[1]: Started Session 123 of user root.\nOct 25 10:29:45 test-server sshd[1234]: Accepted publickey for root from ************* port 12345 ssh2\nOct 25 10:25:01 test-server CRON[5678]: (root) CMD (test -x /usr/sbin/anacron || ( cd / && run-parts --report /etc/cron.hourly ))"
	default:
		stdout = "Command executed successfully"
	}

	return &model.CommandExecuteResponse{
		Command:    req.Command,
		ExitCode:   0,
		Stdout:     stdout,
		Stderr:     "",
		Duration:   100,
		ExecutedAt: time.Now(),
	}, nil
}

func (m *MockHostService) GetHostStatus(id int64) (*model.HostResponse, error) {
	return &model.HostResponse{
		ID:     id,
		Status: "online",
	}, nil
}

func (m *MockHostService) UpdateHostStatus(id int64, status string) error {
	fmt.Printf("MockHostService: Updating host %d status to %s\n", id, status)
	return nil
}

func main() {
	// 设置日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 自动迁移
	err = db.AutoMigrate(&model.Host{})
	if err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// 创建测试主机
	testHost := &model.Host{
		Name:      "test-server",
		IPAddress: "**************",
		Port:      22,
		Username:  "root",
		Status:    "online",
	}
	db.Create(testHost)

	// 创建模拟主机服务
	mockHostService := &MockHostService{}

	// 创建监控执行器
	monitoringExecutor := service.NewMonitoringExecutor(db, logger, mockHostService)

	// 测试comprehensive_inspection
	fmt.Println("🔍 测试comprehensive_inspection功能...")
	fmt.Println("============================================================")

	// 构建测试参数
	params := map[string]interface{}{
		"operation": "comprehensive_inspection",
		"target_host": map[string]interface{}{
			"ip": "**************",
		},
		"inspection_items": []interface{}{
			"system_status",
			"cpu_usage",
			"memory_usage",
			"disk_usage",
			"network_status",
			"service_status",
			"security_status",
			"log_analysis",
		},
		"inspection_level": "full",
	}

	// 创建执行请求
	req := &service.ExecutionRequest{
		UserID: 1,
		Intent: &service.IntentResult{
			Type:       "monitoring_operations",
			Parameters: params,
		},
		OriginalMsg: "对**************做一个全面的巡检，检查系统各个方面的健康情况",
		SessionID:   "test_session_123",
	}

	// 执行巡检
	ctx := context.Background()
	result, err := monitoringExecutor.Execute(ctx, req)
	if err != nil {
		log.Fatal("执行巡检失败:", err)
	}

	// 输出结果
	fmt.Printf("✅ 执行成功: %v\n", result.Success)
	fmt.Printf("📋 操作类型: %s\n", result.Action)
	fmt.Println("\n📊 巡检报告:")
	fmt.Println(result.Content)

	if result.Data != nil {
		fmt.Println("\n📈 执行数据:")
		data, _ := json.MarshalIndent(result.Data, "", "  ")
		fmt.Println(string(data))
	}

	fmt.Println("\n🎉 comprehensive_inspection功能测试完成！")
}
