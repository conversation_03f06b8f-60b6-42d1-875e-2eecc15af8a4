package ai

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// 简化的3大核心意图类型
const (
	INTENT_QUERY   = "QUERY"   // 查询类：获取信息，不修改系统状态
	INTENT_EXECUTE = "EXECUTE" // 执行类：修改系统状态，需要确认
	INTENT_CHAT    = "CHAT"    // 对话类：一般交流，无需系统操作
)

// RevolutionaryIntentClassifier 革命性简化意图分类器
type RevolutionaryIntentClassifier struct {
	logger              *logrus.Logger
	classificationRules map[string]*IntentClassificationRule
	deepseekService     DeepSeekService
	config              *ClassifierConfig
}

// IntentClassificationRule 意图分类规则
type IntentClassificationRule struct {
	Keywords   []string `json:"keywords"`   // 关键词匹配
	Patterns   []string `json:"patterns"`   // 正则模式
	Examples   []string `json:"examples"`   // 示例语句
	Confidence float64  `json:"confidence"` // 置信度阈值
	Priority   int      `json:"priority"`   // 优先级（数字越小优先级越高）
}

// ClassifierConfig 分类器配置
type ClassifierConfig struct {
	EnableDeepSeekFallback bool          `json:"enable_deepseek_fallback"`
	DefaultConfidence      float64       `json:"default_confidence"`
	MinConfidenceThreshold float64       `json:"min_confidence_threshold"`
	Timeout                time.Duration `json:"timeout"`
}

// SimplifiedIntent 简化意图结果
type SimplifiedIntent struct {
	Type        string                 `json:"type"`        // QUERY, EXECUTE, CHAT
	Confidence  float64                `json:"confidence"`  // 置信度
	Keywords    []string               `json:"keywords"`    // 匹配的关键词
	Category    string                 `json:"category"`    // 细分类别
	Description string                 `json:"description"` // 意图描述
	Metadata    map[string]interface{} `json:"metadata"`    // 额外元数据
	Timestamp   time.Time              `json:"timestamp"`   // 分析时间
}

// DeepSeekService DeepSeek服务接口
type DeepSeekService interface {
	ClassifyIntent(ctx context.Context, message string) (*SimplifiedIntent, error)
}

// NewRevolutionaryIntentClassifier 创建革命性意图分类器
func NewRevolutionaryIntentClassifier(logger *logrus.Logger, deepseekService DeepSeekService) *RevolutionaryIntentClassifier {
	classifier := &RevolutionaryIntentClassifier{
		logger:              logger,
		classificationRules: make(map[string]*IntentClassificationRule),
		deepseekService:     deepseekService,
		config: &ClassifierConfig{
			EnableDeepSeekFallback: true,
			DefaultConfidence:      0.7,
			MinConfidenceThreshold: 0.6,
			Timeout:                10 * time.Second,
		},
	}

	// 初始化分类规则
	classifier.initializeClassificationRules()

	return classifier
}

// initializeClassificationRules 初始化分类规则
func (ric *RevolutionaryIntentClassifier) initializeClassificationRules() {
	// QUERY类规则 - 查询类操作
	ric.classificationRules[INTENT_QUERY] = &IntentClassificationRule{
		Keywords: []string{
			"查看", "显示", "列出", "获取", "检查", "查询", "搜索", "找到", "展示",
			"状态", "信息", "详情", "列表", "统计", "报告", "监控", "分析",
			"show", "list", "get", "check", "query", "search", "find", "display",
			"status", "info", "details", "statistics", "report", "monitor", "analyze",
		},
		Patterns: []string{
			`^(查看|显示|列出|获取|检查).*`,
			`.*状态.*`,
			`.*信息.*`,
			`.*列表.*`,
			`.*报告.*`,
			`^(show|list|get|check).*`,
		},
		Examples: []string{
			"查看主机列表",
			"显示主机状态",
			"列出所有主机",
			"获取主机信息",
			"检查连接状态",
			"查询主机详情",
			"show hosts",
			"list servers",
			"get host status",
		},
		Confidence: 0.9,
		Priority:   1,
	}

	// EXECUTE类规则 - 执行类操作
	ric.classificationRules[INTENT_EXECUTE] = &IntentClassificationRule{
		Keywords: []string{
			"添加", "创建", "新建", "删除", "移除", "修改", "更新", "编辑", "执行",
			"重启", "启动", "停止", "安装", "卸载", "配置", "设置", "部署",
			"add", "create", "new", "delete", "remove", "modify", "update", "edit", "execute",
			"restart", "start", "stop", "install", "uninstall", "configure", "set", "deploy",
		},
		Patterns: []string{
			`^(添加|创建|新建|删除|移除|修改|更新|编辑|执行).*`,
			`^(add|create|new|delete|remove|modify|update|edit|execute).*`,
			`.*重启.*`,
			`.*启动.*`,
			`.*停止.*`,
		},
		Examples: []string{
			"添加主机",
			"创建新主机",
			"删除主机",
			"修改主机密码",
			"更新主机信息",
			"执行命令",
			"重启服务",
			"add host",
			"create server",
			"delete host",
			"update password",
		},
		Confidence: 0.9,
		Priority:   1,
	}

	// CHAT类规则 - 对话类
	ric.classificationRules[INTENT_CHAT] = &IntentClassificationRule{
		Keywords: []string{
			"你好", "谢谢", "感谢", "再见", "帮助", "什么", "怎么", "为什么", "如何",
			"介绍", "说明", "解释", "告诉我", "我想知道", "请问",
			"hello", "hi", "thanks", "thank you", "bye", "help", "what", "how", "why",
			"introduce", "explain", "tell me", "i want to know", "please",
		},
		Patterns: []string{
			`^(你好|谢谢|感谢|再见).*`,
			`^(hello|hi|thanks|bye).*`,
			`.*帮助.*`,
			`.*什么.*`,
			`.*怎么.*`,
			`.*为什么.*`,
			`.*如何.*`,
		},
		Examples: []string{
			"你好",
			"谢谢你的帮助",
			"再见",
			"这是什么",
			"怎么使用",
			"为什么会这样",
			"如何操作",
			"hello",
			"thank you",
			"what is this",
			"how to use",
		},
		Confidence: 0.8,
		Priority:   2,
	}
}

// Classify 分类用户意图
func (ric *RevolutionaryIntentClassifier) Classify(ctx context.Context, message string) (*SimplifiedIntent, error) {
	start := time.Now()

	ric.logger.WithFields(logrus.Fields{
		"message": message,
	}).Info("开始革命性意图分类")

	// 预处理消息
	normalizedMessage := ric.normalizeMessage(message)

	// 1. 首先尝试基于规则的快速分类
	ruleBasedIntent := ric.classifyByRules(normalizedMessage)
	if ruleBasedIntent != nil && ruleBasedIntent.Confidence >= ric.config.MinConfidenceThreshold {
		ruleBasedIntent.Timestamp = time.Now()
		ric.logger.WithFields(logrus.Fields{
			"intent_type": ruleBasedIntent.Type,
			"confidence":  ruleBasedIntent.Confidence,
			"duration":    time.Since(start),
			"method":      "rule_based",
		}).Info("规则分类成功")
		return ruleBasedIntent, nil
	}

	// 2. 如果规则分类置信度不够，使用DeepSeek增强
	if ric.config.EnableDeepSeekFallback && ric.deepseekService != nil {
		deepseekIntent, err := ric.classifyWithDeepSeek(ctx, message)
		if err != nil {
			ric.logger.WithError(err).Warn("DeepSeek分类失败，使用规则分类结果")
			if ruleBasedIntent != nil {
				ruleBasedIntent.Timestamp = time.Now()
				return ruleBasedIntent, nil
			}
		} else {
			deepseekIntent.Timestamp = time.Now()
			ric.logger.WithFields(logrus.Fields{
				"intent_type": deepseekIntent.Type,
				"confidence":  deepseekIntent.Confidence,
				"duration":    time.Since(start),
				"method":      "deepseek_enhanced",
			}).Info("DeepSeek增强分类成功")
			return deepseekIntent, nil
		}
	}

	// 3. 降级到默认CHAT类型
	fallbackIntent := &SimplifiedIntent{
		Type:        INTENT_CHAT,
		Confidence:  ric.config.DefaultConfidence,
		Keywords:    []string{},
		Category:    "general_chat",
		Description: "一般对话",
		Metadata: map[string]interface{}{
			"fallback": true,
			"reason":   "无法确定具体意图类型",
		},
		Timestamp: time.Now(),
	}

	ric.logger.WithFields(logrus.Fields{
		"intent_type": fallbackIntent.Type,
		"confidence":  fallbackIntent.Confidence,
		"duration":    time.Since(start),
		"method":      "fallback",
	}).Info("使用降级分类")

	return fallbackIntent, nil
}

// normalizeMessage 标准化消息
func (ric *RevolutionaryIntentClassifier) normalizeMessage(message string) string {
	// 转换为小写
	normalized := strings.ToLower(strings.TrimSpace(message))

	// 移除多余的空格
	normalized = regexp.MustCompile(`\s+`).ReplaceAllString(normalized, " ")

	return normalized
}

// classifyByRules 基于规则的分类
func (ric *RevolutionaryIntentClassifier) classifyByRules(message string) *SimplifiedIntent {
	bestMatch := &SimplifiedIntent{
		Type:       INTENT_CHAT,
		Confidence: 0.0,
		Keywords:   []string{},
	}

	// 按优先级遍历规则
	for intentType, rule := range ric.classificationRules {
		score, matchedKeywords := ric.calculateRuleScore(message, rule)

		if score > bestMatch.Confidence {
			bestMatch = &SimplifiedIntent{
				Type:        intentType,
				Confidence:  score,
				Keywords:    matchedKeywords,
				Category:    ric.getCategoryByType(intentType),
				Description: ric.getDescriptionByType(intentType),
				Metadata: map[string]interface{}{
					"classification_method": "rule_based",
					"matched_patterns":      ric.getMatchedPatterns(message, rule),
				},
			}
		}
	}

	return bestMatch
}

// calculateRuleScore 计算规则匹配分数
func (ric *RevolutionaryIntentClassifier) calculateRuleScore(message string, rule *IntentClassificationRule) (float64, []string) {
	var matchedKeywords []string
	keywordScore := 0.0
	patternScore := 0.0

	// 关键词匹配
	for _, keyword := range rule.Keywords {
		if strings.Contains(message, strings.ToLower(keyword)) {
			matchedKeywords = append(matchedKeywords, keyword)
			keywordScore += 1.0
		}
	}

	// 模式匹配
	for _, pattern := range rule.Patterns {
		if matched, _ := regexp.MatchString(pattern, message); matched {
			patternScore += 2.0 // 模式匹配权重更高
		}
	}

	// 计算总分
	totalScore := (keywordScore + patternScore) / float64(len(rule.Keywords)+len(rule.Patterns))

	// 应用基础置信度
	finalScore := totalScore * rule.Confidence

	return finalScore, matchedKeywords
}

// getMatchedPatterns 获取匹配的模式
func (ric *RevolutionaryIntentClassifier) getMatchedPatterns(message string, rule *IntentClassificationRule) []string {
	var matchedPatterns []string
	for _, pattern := range rule.Patterns {
		if matched, _ := regexp.MatchString(pattern, message); matched {
			matchedPatterns = append(matchedPatterns, pattern)
		}
	}
	return matchedPatterns
}

// getCategoryByType 根据类型获取类别
func (ric *RevolutionaryIntentClassifier) getCategoryByType(intentType string) string {
	switch intentType {
	case INTENT_QUERY:
		return "information_retrieval"
	case INTENT_EXECUTE:
		return "system_operation"
	case INTENT_CHAT:
		return "general_conversation"
	default:
		return "unknown"
	}
}

// getDescriptionByType 根据类型获取描述
func (ric *RevolutionaryIntentClassifier) getDescriptionByType(intentType string) string {
	switch intentType {
	case INTENT_QUERY:
		return "查询信息，获取系统状态或数据"
	case INTENT_EXECUTE:
		return "执行操作，修改系统状态或配置"
	case INTENT_CHAT:
		return "一般对话，无需系统操作"
	default:
		return "未知意图类型"
	}
}

// classifyWithDeepSeek 使用DeepSeek进行意图分类
func (ric *RevolutionaryIntentClassifier) classifyWithDeepSeek(ctx context.Context, message string) (*SimplifiedIntent, error) {
	ric.logger.WithField("message", message).Debug("使用DeepSeek进行意图分类")

	// 调用DeepSeek服务
	intent, err := ric.deepseekService.ClassifyIntent(ctx, message)
	if err != nil {
		return nil, fmt.Errorf("DeepSeek分类失败: %w", err)
	}

	// 验证和标准化结果
	if intent == nil {
		return nil, fmt.Errorf("DeepSeek返回空结果")
	}

	// 确保意图类型有效
	if !ric.isValidIntentType(intent.Type) {
		ric.logger.WithField("invalid_type", intent.Type).Warn("DeepSeek返回无效意图类型，降级为CHAT")
		intent.Type = INTENT_CHAT
		intent.Confidence = ric.config.DefaultConfidence
	}

	// 设置分类方法标记
	if intent.Metadata == nil {
		intent.Metadata = make(map[string]interface{})
	}
	intent.Metadata["classification_method"] = "deepseek_enhanced"

	return intent, nil
}

// isValidIntentType 检查意图类型是否有效
func (ric *RevolutionaryIntentClassifier) isValidIntentType(intentType string) bool {
	validTypes := []string{INTENT_QUERY, INTENT_EXECUTE, INTENT_CHAT}
	for _, validType := range validTypes {
		if intentType == validType {
			return true
		}
	}
	return false
}

// GetSupportedIntentTypes 获取支持的意图类型
func (ric *RevolutionaryIntentClassifier) GetSupportedIntentTypes() []string {
	return []string{INTENT_QUERY, INTENT_EXECUTE, INTENT_CHAT}
}

// GetClassificationRules 获取分类规则（用于调试和监控）
func (ric *RevolutionaryIntentClassifier) GetClassificationRules() map[string]*IntentClassificationRule {
	return ric.classificationRules
}

// UpdateConfig 更新分类器配置
func (ric *RevolutionaryIntentClassifier) UpdateConfig(config *ClassifierConfig) {
	ric.config = config
	ric.logger.WithFields(logrus.Fields{
		"enable_deepseek_fallback": config.EnableDeepSeekFallback,
		"default_confidence":       config.DefaultConfidence,
		"min_confidence_threshold": config.MinConfidenceThreshold,
		"timeout":                  config.Timeout,
	}).Info("分类器配置已更新")
}
