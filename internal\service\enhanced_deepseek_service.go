package service

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// EnhancedDeepSeekConfig 增强型DeepSeek配置
type EnhancedDeepSeekConfig struct {
	APIKey           string        `mapstructure:"api_key"`
	BaseURL          string        `mapstructure:"base_url"`
	Model            string        `mapstructure:"model"`
	MaxTokens        int           `mapstructure:"max_tokens"`
	Temperature      float64       `mapstructure:"temperature"`
	TopP             float64       `mapstructure:"top_p"`
	
	// 增强配置
	DefaultTimeout   time.Duration `mapstructure:"default_timeout"`   // 默认超时时间
	ComplexTimeout   time.Duration `mapstructure:"complex_timeout"`   // 复杂操作超时时间
	MaxRetries       int           `mapstructure:"max_retries"`       // 最大重试次数
	RetryDelay       time.Duration `mapstructure:"retry_delay"`       // 重试延迟
	BackoffMultiplier float64      `mapstructure:"backoff_multiplier"` // 退避倍数
	
	// 智能重试配置
	EnableSmartRetry bool          `mapstructure:"enable_smart_retry"` // 启用智能重试
	RetryableErrors  []string      `mapstructure:"retryable_errors"`   // 可重试的错误类型
}

// DefaultEnhancedDeepSeekConfig 默认增强配置
func DefaultEnhancedDeepSeekConfig() *EnhancedDeepSeekConfig {
	return &EnhancedDeepSeekConfig{
		BaseURL:           "https://api.deepseek.com",
		Model:             "deepseek-chat",
		MaxTokens:         4000,
		Temperature:       0.7,
		TopP:              0.9,
		DefaultTimeout:    30 * time.Second,
		ComplexTimeout:    120 * time.Second, // 复杂操作2分钟超时
		MaxRetries:        3,
		RetryDelay:        2 * time.Second,
		BackoffMultiplier: 2.0,
		EnableSmartRetry:  true,
		RetryableErrors: []string{
			"context deadline exceeded",
			"timeout",
			"connection reset",
			"temporary failure",
			"rate limit",
		},
	}
}

// EnhancedDeepSeekService 增强型DeepSeek服务
type EnhancedDeepSeekService struct {
	config     *EnhancedDeepSeekConfig
	httpClient *http.Client
	logger     *logrus.Logger
}

// NewEnhancedDeepSeekService 创建增强型DeepSeek服务
func NewEnhancedDeepSeekService(config *EnhancedDeepSeekConfig, logger *logrus.Logger) *EnhancedDeepSeekService {
	if config == nil {
		config = DefaultEnhancedDeepSeekConfig()
	}
	
	return &EnhancedDeepSeekService{
		config: config,
		httpClient: &http.Client{
			Timeout: config.ComplexTimeout, // 使用最长的超时时间
		},
		logger: logger,
	}
}

// DeepSeekRequest 请求结构
type DeepSeekRequest struct {
	Model       string            `json:"model"`
	Messages    []DeepSeekMessage `json:"messages"`
	MaxTokens   int               `json:"max_tokens"`
	Temperature float64           `json:"temperature"`
	TopP        float64           `json:"top_p"`
	Stream      bool              `json:"stream"`
}

// DeepSeekMessage 消息结构
type DeepSeekMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// DeepSeekResponse 响应结构
type DeepSeekResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// OperationComplexity 操作复杂度
type OperationComplexity string

const (
	ComplexitySimple  OperationComplexity = "simple"   // 简单操作：意图识别、简单查询
	ComplexityMedium  OperationComplexity = "medium"   // 中等操作：命令生成、数据分析
	ComplexityComplex OperationComplexity = "complex"  // 复杂操作：全面巡检、结果渲染
)

// CallWithComplexity 根据操作复杂度调用API
func (eds *EnhancedDeepSeekService) CallWithComplexity(
	ctx context.Context, 
	req *DeepSeekRequest, 
	complexity OperationComplexity,
) (*DeepSeekResponse, error) {
	// 根据复杂度选择超时时间
	timeout := eds.getTimeoutForComplexity(complexity)
	
	// 创建带超时的上下文
	ctxWithTimeout, cancel := context.WithTimeout(ctx, timeout)
	defer cancel()
	
	eds.logger.WithFields(logrus.Fields{
		"complexity": complexity,
		"timeout":    timeout,
		"model":      req.Model,
		"max_tokens": req.MaxTokens,
	}).Info("EnhancedDeepSeek: 开始API调用")
	
	// 执行带重试的API调用
	return eds.callWithRetry(ctxWithTimeout, req, complexity)
}

// getTimeoutForComplexity 根据复杂度获取超时时间
func (eds *EnhancedDeepSeekService) getTimeoutForComplexity(complexity OperationComplexity) time.Duration {
	switch complexity {
	case ComplexitySimple:
		return eds.config.DefaultTimeout
	case ComplexityMedium:
		return eds.config.DefaultTimeout + 30*time.Second
	case ComplexityComplex:
		return eds.config.ComplexTimeout
	default:
		return eds.config.DefaultTimeout
	}
}

// callWithRetry 带重试的API调用
func (eds *EnhancedDeepSeekService) callWithRetry(
	ctx context.Context, 
	req *DeepSeekRequest, 
	complexity OperationComplexity,
) (*DeepSeekResponse, error) {
	var lastErr error
	retryDelay := eds.config.RetryDelay
	
	for attempt := 0; attempt <= eds.config.MaxRetries; attempt++ {
		eds.logger.WithFields(logrus.Fields{
			"attempt":    attempt + 1,
			"max_retries": eds.config.MaxRetries + 1,
			"complexity": complexity,
		}).Info("EnhancedDeepSeek: 尝试API调用")
		
		response, err := eds.makeAPICall(ctx, req)
		if err == nil {
			eds.logger.WithFields(logrus.Fields{
				"attempt":     attempt + 1,
				"success":     true,
				"total_tokens": response.Usage.TotalTokens,
			}).Info("EnhancedDeepSeek: API调用成功")
			return response, nil
		}
		
		lastErr = err
		
		// 检查是否应该重试
		if !eds.shouldRetry(err, attempt) {
			break
		}
		
		// 如果不是最后一次尝试，等待后重试
		if attempt < eds.config.MaxRetries {
			eds.logger.WithFields(logrus.Fields{
				"attempt":     attempt + 1,
				"error":       err.Error(),
				"retry_delay": retryDelay,
			}).Warn("EnhancedDeepSeek: API调用失败，准备重试")
			
			select {
			case <-ctx.Done():
				return nil, ctx.Err()
			case <-time.After(retryDelay):
				// 指数退避
				retryDelay = time.Duration(float64(retryDelay) * eds.config.BackoffMultiplier)
			}
		}
	}
	
	eds.logger.WithFields(logrus.Fields{
		"max_retries": eds.config.MaxRetries + 1,
		"final_error": lastErr.Error(),
		"complexity":  complexity,
	}).Error("EnhancedDeepSeek: API调用最终失败")
	
	return nil, fmt.Errorf("API调用失败，已重试%d次: %w", eds.config.MaxRetries, lastErr)
}

// makeAPICall 执行实际的API调用
func (eds *EnhancedDeepSeekService) makeAPICall(ctx context.Context, req *DeepSeekRequest) (*DeepSeekResponse, error) {
	// 设置默认值
	if req.Model == "" {
		req.Model = eds.config.Model
	}
	if req.MaxTokens == 0 {
		req.MaxTokens = eds.config.MaxTokens
	}
	if req.Temperature == 0 {
		req.Temperature = eds.config.Temperature
	}
	if req.TopP == 0 {
		req.TopP = eds.config.TopP
	}

	// 序列化请求
	jsonData, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", eds.config.BaseURL+"/chat/completions", bytes.NewBuffer(jsonData))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+eds.config.APIKey)

	// 发送请求
	resp, err := eds.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API返回错误状态码 %d: %s", resp.StatusCode, string(body))
	}

	// 解析响应
	var response DeepSeekResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 验证响应
	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("API响应中没有选择项")
	}

	return &response, nil
}

// shouldRetry 判断是否应该重试
func (eds *EnhancedDeepSeekService) shouldRetry(err error, attempt int) bool {
	// 如果已达到最大重试次数，不再重试
	if attempt >= eds.config.MaxRetries {
		return false
	}

	// 如果未启用智能重试，只重试网络相关错误
	if !eds.config.EnableSmartRetry {
		errStr := strings.ToLower(err.Error())
		return strings.Contains(errStr, "timeout") ||
			   strings.Contains(errStr, "connection") ||
			   strings.Contains(errStr, "deadline exceeded")
	}

	// 智能重试：检查错误类型
	errStr := strings.ToLower(err.Error())
	for _, retryableErr := range eds.config.RetryableErrors {
		if strings.Contains(errStr, strings.ToLower(retryableErr)) {
			return true
		}
	}

	return false
}

// 便捷方法：意图识别
func (eds *EnhancedDeepSeekService) RecognizeIntent(ctx context.Context, userInput string) (*DeepSeekResponse, error) {
	req := &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role: "system",
				Content: `你是一个专业的AI运维意图识别专家。请分析用户输入并返回JSON格式的意图信息。

返回格式：
{
  "intent_type": "操作类型",
  "confidence": 0.95,
  "parameters": {
    "key": "value"
  },
  "operation": "具体操作名称",
  "requires_confirmation": false
}

支持的操作类型：
- database_operations: 数据库查询操作（包括：查看主机、查看主机状态、主机列表、查询主机信息等）
- ssh_operations: SSH命令执行
- monitoring_operations: 监控和巡检操作
- general_chat: 通用对话

重要识别规则：
- "查看主机状态"、"查看主机"、"主机列表"、"主机状态" -> database_operations
- "添加主机"、"删除主机" -> database_operations
- "执行命令"、"运行命令" -> ssh_operations
- "监控"、"巡检"、"检查" -> monitoring_operations`,
			},
			{
				Role:    "user",
				Content: userInput,
			},
		},
		MaxTokens:   1500,
		Temperature: 0.1,
	}

	return eds.CallWithComplexity(ctx, req, ComplexitySimple)
}

// 便捷方法：生成执行命令（带表结构信息）
func (eds *EnhancedDeepSeekService) GenerateExecutionCommand(ctx context.Context, intent map[string]interface{}, userInput string) (*DeepSeekResponse, error) {
	return eds.GenerateExecutionCommandWithSchema(ctx, intent, userInput, "")
}

// 生成执行命令（带数据库表结构）
func (eds *EnhancedDeepSeekService) GenerateExecutionCommandWithSchema(ctx context.Context, intent map[string]interface{}, userInput string, dbSchema string) (*DeepSeekResponse, error) {
	intentJSON, _ := json.Marshal(intent)

	// 构建包含数据库表结构的系统Prompt
	systemPrompt := `你是一个专业的运维命令生成专家。根据用户意图生成可执行的命令或SQL语句。

🚨 **关键要求**：
1. 生成SQL时，必须严格按照提供的数据库表结构中的确切字段名
2. 绝对不要猜测或假设字段名
3. 仔细检查表结构，使用正确的字段名

请返回JSON格式：
{
  "execution_type": "sql|shell|config",
  "generated_code": "具体的SQL语句或Shell命令",
  "description": "操作描述",
  "estimated_risk": "low|medium|high",
  "require_confirm": false
}

特殊处理规则：
- 对于"查看主机状态"、"查看主机"、"主机列表"等请求，execution_type应为"sql"
- 对于主机查询操作，generated_code应为"SELECT * FROM hosts WHERE deleted_at IS NULL"
- 对于SSH命令执行，execution_type应为"shell"

🔧 **重要：Shell命令连接规则**：
- 必须使用分号(;)连接多个命令，不要使用&&连接符
- 对于可能失败的命令，使用 || true 确保不影响后续执行
- 示例：echo "=== CPU ===" ; top -bn1 | head -5 ; echo "=== Memory ===" ; free -h
- 这样即使某个工具不存在（如iostat），其他检查仍能正常进行`

	// 🔧 关键优化：添加数据库表结构信息
	if dbSchema != "" {
		systemPrompt += "\n\n## 📋 数据库表结构信息\n" + dbSchema + "\n\n⚠️ **重要**：生成SQL时必须使用上述表结构中的确切字段名，不要猜测字段名！"
	}

	req := &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: systemPrompt,
			},
			{
				Role:    "user",
				Content: fmt.Sprintf("用户输入: %s\n意图信息: %s", userInput, string(intentJSON)),
			},
		},
		MaxTokens:   2000,
		Temperature: 0.2,
	}

	return eds.CallWithComplexity(ctx, req, ComplexityMedium)
}

// 便捷方法：分析和渲染执行结果
func (eds *EnhancedDeepSeekService) AnalyzeAndRenderResult(ctx context.Context, executionResult interface{}, userInput string, operation string) (*DeepSeekResponse, error) {
	resultJSON, _ := json.Marshal(executionResult)

	req := &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role: "system",
				Content: `你是一个专业的AI运维结果分析和渲染专家。请分析执行结果并生成用户友好的响应。

要求：
1. 使用Markdown格式
2. 包含操作摘要和关键信息
3. 如果有数据，用表格展示
4. 添加适当的emoji图标
5. 提供专业的分析和建议
6. 语言风格专业但友好

示例格式：
## 📊 操作结果

**操作摘要**: [操作描述]

### 🔍 执行详情
[执行的命令或SQL]

### 📈 结果分析
[数据表格或分析内容]

### 💡 专业建议
[基于结果的建议]`,
			},
			{
				Role:    "user",
				Content: fmt.Sprintf("用户请求: %s\n执行操作: %s\n执行结果: %s\n\n请分析结果并生成友好的响应", userInput, operation, string(resultJSON)),
			},
		},
		MaxTokens:   3000,
		Temperature: 0.4,
	}

	return eds.CallWithComplexity(ctx, req, ComplexityComplex)
}
