// AI对话运维管理平台 - 主JavaScript文件

// 全局配置
const CONFIG = {
    API_BASE_URL: '/api/v1',
    WS_BASE_URL: window.location.protocol === 'https:' ? 'wss://' : 'ws://' + window.location.host,
    TOKEN_KEY: 'access_token',
    REFRESH_TOKEN_KEY: 'refresh_token'
};

// API客户端类
class APIClient {
    constructor() {
        this.baseURL = CONFIG.API_BASE_URL;
    }

    // 获取认证头
    getAuthHeaders() {
        const token = localStorage.getItem(CONFIG.TOKEN_KEY);
        return token ? { 'Authorization': `Bearer ${token}` } : {};
    }

    // 通用请求方法
    async request(method, url, data = null, headers = {}) {
        const config = {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...this.getAuthHeaders(),
                ...headers
            }
        };

        if (data) {
            config.body = JSON.stringify(data);
        }

        try {
            const response = await fetch(this.baseURL + url, config);
            
            // 处理401错误（token过期）
            if (response.status === 401) {
                await this.refreshToken();
                // 重试请求
                config.headers = {
                    ...config.headers,
                    ...this.getAuthHeaders()
                };
                return await fetch(this.baseURL + url, config);
            }

            return response;
        } catch (error) {
            console.error('API请求失败:', error);
            throw error;
        }
    }

    // 刷新Token
    async refreshToken() {
        const refreshToken = localStorage.getItem(CONFIG.REFRESH_TOKEN_KEY);
        if (!refreshToken) {
            this.redirectToLogin();
            return;
        }

        try {
            const response = await fetch(this.baseURL + '/auth/refresh', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ refresh_token: refreshToken })
            });

            if (response.ok) {
                const data = await response.json();
                localStorage.setItem(CONFIG.TOKEN_KEY, data.data.access_token);
                localStorage.setItem(CONFIG.REFRESH_TOKEN_KEY, data.data.refresh_token);
            } else {
                this.redirectToLogin();
            }
        } catch (error) {
            console.error('刷新Token失败:', error);
            this.redirectToLogin();
        }
    }

    // 重定向到登录页
    redirectToLogin() {
        localStorage.removeItem(CONFIG.TOKEN_KEY);
        localStorage.removeItem(CONFIG.REFRESH_TOKEN_KEY);
        window.location.href = '/login';
    }

    // GET请求
    async get(url, headers = {}) {
        return this.request('GET', url, null, headers);
    }

    // POST请求
    async post(url, data, headers = {}) {
        return this.request('POST', url, data, headers);
    }

    // PUT请求
    async put(url, data, headers = {}) {
        return this.request('PUT', url, data, headers);
    }

    // DELETE请求
    async delete(url, headers = {}) {
        return this.request('DELETE', url, null, headers);
    }
}

// 全局API客户端实例
const api = new APIClient();

// 工具函数
const Utils = {
    // 格式化日期
    formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
        const d = new Date(date);
        const year = d.getFullYear();
        const month = String(d.getMonth() + 1).padStart(2, '0');
        const day = String(d.getDate()).padStart(2, '0');
        const hours = String(d.getHours()).padStart(2, '0');
        const minutes = String(d.getMinutes()).padStart(2, '0');
        const seconds = String(d.getSeconds()).padStart(2, '0');

        return format
            .replace('YYYY', year)
            .replace('MM', month)
            .replace('DD', day)
            .replace('HH', hours)
            .replace('mm', minutes)
            .replace('ss', seconds);
    },

    // 格式化文件大小
    formatFileSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 显示通知
    showNotification(message, type = 'info', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // 自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, duration);
    },

    // 显示加载状态
    showLoading(element, text = '加载中...') {
        element.innerHTML = `
            <div class="text-center">
                <div class="loading"></div>
                <div class="mt-2">${text}</div>
            </div>
        `;
    },

    // 确认对话框
    confirm(message, callback) {
        if (window.confirm(message)) {
            callback();
        }
    },

    // 复制到剪贴板
    async copyToClipboard(text) {
        try {
            await navigator.clipboard.writeText(text);
            this.showNotification('已复制到剪贴板', 'success');
        } catch (err) {
            console.error('复制失败:', err);
            this.showNotification('复制失败', 'danger');
        }
    }
};

// WebSocket管理器
class WebSocketManager {
    constructor() {
        this.connections = new Map();
    }

    // 创建WebSocket连接
    connect(endpoint, onMessage, onError = null, onClose = null) {
        const token = localStorage.getItem(CONFIG.TOKEN_KEY);
        const url = `${CONFIG.WS_BASE_URL}/ws/${endpoint}?token=${token}`;
        
        const ws = new WebSocket(url);
        
        ws.onopen = () => {
            console.log(`WebSocket连接已建立: ${endpoint}`);
        };
        
        ws.onmessage = (event) => {
            try {
                const data = JSON.parse(event.data);
                onMessage(data);
            } catch (error) {
                console.error('WebSocket消息解析失败:', error);
            }
        };
        
        ws.onerror = (error) => {
            console.error(`WebSocket错误 (${endpoint}):`, error);
            if (onError) onError(error);
        };
        
        ws.onclose = () => {
            console.log(`WebSocket连接已关闭: ${endpoint}`);
            this.connections.delete(endpoint);
            if (onClose) onClose();
        };
        
        this.connections.set(endpoint, ws);
        return ws;
    }

    // 发送消息
    send(endpoint, message) {
        const ws = this.connections.get(endpoint);
        if (ws && ws.readyState === WebSocket.OPEN) {
            ws.send(JSON.stringify(message));
        } else {
            console.error(`WebSocket连接不可用: ${endpoint}`);
        }
    }

    // 关闭连接
    close(endpoint) {
        const ws = this.connections.get(endpoint);
        if (ws) {
            ws.close();
            this.connections.delete(endpoint);
        }
    }

    // 关闭所有连接
    closeAll() {
        this.connections.forEach((ws, endpoint) => {
            ws.close();
        });
        this.connections.clear();
    }
}

// 全局WebSocket管理器实例
const wsManager = new WebSocketManager();

// 页面通用初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化Bootstrap组件
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 检查登录状态
    checkAuthStatus();

    // 设置全局错误处理
    window.addEventListener('error', function(event) {
        console.error('全局错误:', event.error);
    });

    // 设置全局未处理的Promise拒绝处理
    window.addEventListener('unhandledrejection', function(event) {
        console.error('未处理的Promise拒绝:', event.reason);
    });
});

// 检查认证状态
function checkAuthStatus() {
    const token = localStorage.getItem(CONFIG.TOKEN_KEY);
    const currentPath = window.location.pathname;
    
    // 公开页面不需要检查认证
    const publicPaths = ['/login', '/register', '/'];
    
    if (!token && !publicPaths.includes(currentPath)) {
        window.location.href = '/login';
    }
}

// 页面卸载时清理资源
window.addEventListener('beforeunload', function() {
    wsManager.closeAll();
});

// 导出全局对象
window.API = api;
window.Utils = Utils;
window.WSManager = wsManager;
