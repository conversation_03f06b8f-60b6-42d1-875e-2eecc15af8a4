package agent

import (
	"fmt"
	"path/filepath"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
)

// CommandFilter 命令过滤器
type CommandFilter struct {
	config      *AgentConfig
	logger      *logrus.Logger
	whitelist   *CommandRuleSet
	blacklist   *CommandRuleSet
	pathFilters []PathFilter
}

// CommandRuleSet 命令规则集
type CommandRuleSet struct {
	Commands []CommandRule `json:"commands"`
	Patterns []PatternRule `json:"patterns"`
}

// CommandRule 命令规则
type CommandRule struct {
	Command     string   `json:"command"`
	AllowedArgs []string `json:"allowed_args,omitempty"`
	DeniedArgs  []string `json:"denied_args,omitempty"`
	Description string   `json:"description"`
	RiskLevel   string   `json:"risk_level"` // low, medium, high, critical
}

// PatternRule 模式规则
type PatternRule struct {
	Pattern     string `json:"pattern"`
	Type        string `json:"type"` // regex, glob, exact
	Description string `json:"description"`
	RiskLevel   string `json:"risk_level"`
}

// PathFilter 路径过滤器
type PathFilter struct {
	Path        string `json:"path"`
	Type        string `json:"type"`   // exact, prefix, suffix, glob
	Action      string `json:"action"` // allow, deny
	Description string `json:"description"`
}

// NewCommandFilter 创建命令过滤器
func NewCommandFilter(config *AgentConfig, logger *logrus.Logger) *CommandFilter {
	filter := &CommandFilter{
		config: config,
		logger: logger,
	}

	// 初始化默认规则
	filter.initializeDefaultRules()

	return filter
}

// initializeDefaultRules 初始化默认规则
func (cf *CommandFilter) initializeDefaultRules() {
	// 白名单 - 允许的安全命令
	cf.whitelist = &CommandRuleSet{
		Commands: []CommandRule{
			// 系统信息查询
			{Command: "ps", Description: "进程列表", RiskLevel: "low"},
			{Command: "top", Description: "系统监控", RiskLevel: "low"},
			{Command: "htop", Description: "系统监控", RiskLevel: "low"},
			{Command: "free", Description: "内存信息", RiskLevel: "low"},
			{Command: "df", Description: "磁盘使用", RiskLevel: "low"},
			{Command: "du", Description: "目录大小", RiskLevel: "low"},
			{Command: "uptime", Description: "系统运行时间", RiskLevel: "low"},
			{Command: "whoami", Description: "当前用户", RiskLevel: "low"},
			{Command: "id", Description: "用户ID信息", RiskLevel: "low"},
			{Command: "uname", Description: "系统信息", RiskLevel: "low"},

			// 网络相关
			{Command: "ping", AllowedArgs: []string{"-c", "-i", "-W"}, Description: "网络连通性测试", RiskLevel: "low"},
			{Command: "netstat", Description: "网络连接状态", RiskLevel: "low"},
			{Command: "ss", Description: "套接字统计", RiskLevel: "low"},
			{Command: "ip", AllowedArgs: []string{"addr", "route", "link"}, Description: "网络配置查看", RiskLevel: "medium"},

			// 文件操作（只读）
			{Command: "ls", Description: "文件列表", RiskLevel: "low"},
			{Command: "cat", Description: "文件内容查看", RiskLevel: "medium"},
			{Command: "head", Description: "文件头部", RiskLevel: "low"},
			{Command: "tail", Description: "文件尾部", RiskLevel: "low"},
			{Command: "grep", Description: "文本搜索", RiskLevel: "low"},
			{Command: "find", AllowedArgs: []string{"-name", "-type", "-size", "-mtime"}, Description: "文件查找", RiskLevel: "medium"},
			{Command: "wc", Description: "文件统计", RiskLevel: "low"},

			// 系统服务
			{Command: "systemctl", AllowedArgs: []string{"status", "is-active", "is-enabled", "list-units"}, Description: "服务状态查询", RiskLevel: "medium"},
			{Command: "service", AllowedArgs: []string{"status"}, Description: "服务状态", RiskLevel: "medium"},

			// 日志查看
			{Command: "journalctl", AllowedArgs: []string{"-u", "-f", "-n", "--since", "--until"}, Description: "系统日志", RiskLevel: "medium"},

			// 性能分析
			{Command: "vmstat", Description: "虚拟内存统计", RiskLevel: "low"},
			{Command: "iostat", Description: "IO统计", RiskLevel: "low"},
			{Command: "sar", Description: "系统活动报告", RiskLevel: "low"},
		},
		Patterns: []PatternRule{
			{Pattern: "^(ls|cat|head|tail|grep|wc)\\s+.*", Type: "regex", Description: "基础文件操作", RiskLevel: "low"},
			{Pattern: "^(ps|top|free|df|uptime)\\s*.*", Type: "regex", Description: "系统信息查询", RiskLevel: "low"},
		},
	}

	// 黑名单 - 危险命令
	cf.blacklist = &CommandRuleSet{
		Commands: []CommandRule{
			// 系统管理危险命令
			{Command: "rm", Description: "文件删除", RiskLevel: "critical"},
			{Command: "rmdir", Description: "目录删除", RiskLevel: "high"},
			{Command: "mv", Description: "文件移动", RiskLevel: "high"},
			{Command: "cp", DeniedArgs: []string{"/etc/", "/boot/", "/sys/"}, Description: "文件复制到系统目录", RiskLevel: "high"},
			{Command: "dd", Description: "磁盘操作", RiskLevel: "critical"},
			{Command: "fdisk", Description: "磁盘分区", RiskLevel: "critical"},
			{Command: "mkfs", Description: "文件系统创建", RiskLevel: "critical"},
			{Command: "mount", Description: "文件系统挂载", RiskLevel: "high"},
			{Command: "umount", Description: "文件系统卸载", RiskLevel: "high"},

			// 用户和权限
			{Command: "sudo", Description: "提权执行", RiskLevel: "critical"},
			{Command: "su", Description: "用户切换", RiskLevel: "critical"},
			{Command: "passwd", Description: "密码修改", RiskLevel: "critical"},
			{Command: "useradd", Description: "用户添加", RiskLevel: "critical"},
			{Command: "userdel", Description: "用户删除", RiskLevel: "critical"},
			{Command: "usermod", Description: "用户修改", RiskLevel: "critical"},
			{Command: "chmod", Description: "权限修改", RiskLevel: "high"},
			{Command: "chown", Description: "所有者修改", RiskLevel: "high"},

			// 网络和安全
			{Command: "iptables", Description: "防火墙规则", RiskLevel: "critical"},
			{Command: "ufw", Description: "防火墙管理", RiskLevel: "high"},
			{Command: "ssh", Description: "远程连接", RiskLevel: "high"},
			{Command: "scp", Description: "远程文件传输", RiskLevel: "high"},
			{Command: "rsync", Description: "文件同步", RiskLevel: "medium"},

			// 系统服务控制
			{Command: "systemctl", DeniedArgs: []string{"start", "stop", "restart", "reload", "enable", "disable"}, Description: "服务控制", RiskLevel: "high"},
			{Command: "service", DeniedArgs: []string{"start", "stop", "restart", "reload"}, Description: "服务控制", RiskLevel: "high"},

			// 包管理
			{Command: "apt", Description: "包管理", RiskLevel: "high"},
			{Command: "yum", Description: "包管理", RiskLevel: "high"},
			{Command: "dnf", Description: "包管理", RiskLevel: "high"},
			{Command: "pip", Description: "Python包管理", RiskLevel: "medium"},
			{Command: "npm", Description: "Node.js包管理", RiskLevel: "medium"},

			// 编译和执行
			{Command: "gcc", Description: "编译器", RiskLevel: "high"},
			{Command: "make", Description: "构建工具", RiskLevel: "high"},
			{Command: "python", Description: "Python解释器", RiskLevel: "medium"},
			{Command: "bash", Description: "Shell执行", RiskLevel: "high"},
			{Command: "sh", Description: "Shell执行", RiskLevel: "high"},
		},
		Patterns: []PatternRule{
			{Pattern: ".*\\|\\s*sh\\s*$", Type: "regex", Description: "管道到shell执行", RiskLevel: "critical"},
			{Pattern: ".*\\|\\s*bash\\s*$", Type: "regex", Description: "管道到bash执行", RiskLevel: "critical"},
			{Pattern: ".*>\\s*/etc/.*", Type: "regex", Description: "重定向到系统配置", RiskLevel: "critical"},
			{Pattern: ".*>\\s*/boot/.*", Type: "regex", Description: "重定向到启动目录", RiskLevel: "critical"},
			{Pattern: ".*rm\\s+.*-rf.*", Type: "regex", Description: "强制递归删除", RiskLevel: "critical"},
			{Pattern: ".*\\$\\(.*\\).*", Type: "regex", Description: "命令替换", RiskLevel: "high"},
			{Pattern: ".*`.*`.*", Type: "regex", Description: "命令替换", RiskLevel: "high"},
		},
	}

	// 路径过滤器
	cf.pathFilters = []PathFilter{
		{Path: "/etc", Type: "prefix", Action: "deny", Description: "系统配置目录"},
		{Path: "/boot", Type: "prefix", Action: "deny", Description: "启动目录"},
		{Path: "/sys", Type: "prefix", Action: "deny", Description: "系统文件系统"},
		{Path: "/proc", Type: "prefix", Action: "allow", Description: "进程文件系统（只读）"},
		{Path: "/dev", Type: "prefix", Action: "deny", Description: "设备文件"},
		{Path: "/root", Type: "prefix", Action: "deny", Description: "root用户目录"},
		{Path: "/home", Type: "prefix", Action: "allow", Description: "用户目录"},
		{Path: "/tmp", Type: "prefix", Action: "allow", Description: "临时目录"},
		{Path: "/var/log", Type: "prefix", Action: "allow", Description: "日志目录"},
	}
}

// ValidateCommand 验证命令
func (cf *CommandFilter) ValidateCommand(command string, args []string) error {
	// 检查黑名单
	if err := cf.checkBlacklist(command, args); err != nil {
		return fmt.Errorf("command blocked by blacklist: %w", err)
	}

	// 检查白名单
	if !cf.checkWhitelist(command, args) {
		return fmt.Errorf("command not in whitelist: %s", command)
	}

	// 检查路径过滤
	if err := cf.checkPathFilters(command, args); err != nil {
		return fmt.Errorf("path access denied: %w", err)
	}

	return nil
}

// checkBlacklist 检查黑名单
func (cf *CommandFilter) checkBlacklist(command string, args []string) error {
	// 检查命令黑名单
	for _, rule := range cf.blacklist.Commands {
		if rule.Command == command {
			// 检查拒绝的参数
			if len(rule.DeniedArgs) > 0 {
				for _, arg := range args {
					for _, deniedArg := range rule.DeniedArgs {
						if strings.Contains(arg, deniedArg) {
							return fmt.Errorf("denied argument '%s' found in command '%s'", deniedArg, command)
						}
					}
				}
			} else {
				// 整个命令被禁止
				return fmt.Errorf("command '%s' is blacklisted: %s", command, rule.Description)
			}
		}
	}

	// 检查模式黑名单
	fullCommand := command + " " + strings.Join(args, " ")
	for _, pattern := range cf.blacklist.Patterns {
		if cf.matchPattern(fullCommand, pattern) {
			return fmt.Errorf("command matches blacklisted pattern: %s", pattern.Description)
		}
	}

	return nil
}

// checkWhitelist 检查白名单
func (cf *CommandFilter) checkWhitelist(command string, args []string) bool {
	// 检查命令白名单
	for _, rule := range cf.whitelist.Commands {
		if rule.Command == command {
			// 如果有允许的参数限制，检查参数
			if len(rule.AllowedArgs) > 0 {
				return cf.validateAllowedArgs(args, rule.AllowedArgs)
			}
			return true
		}
	}

	// 检查模式白名单
	fullCommand := command + " " + strings.Join(args, " ")
	for _, pattern := range cf.whitelist.Patterns {
		if cf.matchPattern(fullCommand, pattern) {
			return true
		}
	}

	return false
}

// checkPathFilters 检查路径过滤
func (cf *CommandFilter) checkPathFilters(command string, args []string) error {
	for _, arg := range args {
		// 检查是否是路径参数
		if strings.HasPrefix(arg, "/") || strings.HasPrefix(arg, "./") || strings.HasPrefix(arg, "../") {
			absPath, err := filepath.Abs(arg)
			if err != nil {
				continue
			}

			for _, filter := range cf.pathFilters {
				if cf.matchPath(absPath, filter) {
					if filter.Action == "deny" {
						return fmt.Errorf("access to path '%s' is denied: %s", absPath, filter.Description)
					}
				}
			}
		}
	}

	return nil
}

// validateAllowedArgs 验证允许的参数
func (cf *CommandFilter) validateAllowedArgs(args []string, allowedArgs []string) bool {
	for _, arg := range args {
		if strings.HasPrefix(arg, "-") {
			found := false
			for _, allowed := range allowedArgs {
				if arg == allowed || strings.HasPrefix(arg, allowed+"=") {
					found = true
					break
				}
			}
			if !found {
				return false
			}
		}
	}
	return true
}

// matchPattern 匹配模式
func (cf *CommandFilter) matchPattern(text string, pattern PatternRule) bool {
	switch pattern.Type {
	case "regex":
		if matched, err := regexp.MatchString(pattern.Pattern, text); err == nil {
			return matched
		}
	case "glob":
		if matched, err := filepath.Match(pattern.Pattern, text); err == nil {
			return matched
		}
	case "exact":
		return text == pattern.Pattern
	}
	return false
}

// matchPath 匹配路径
func (cf *CommandFilter) matchPath(path string, filter PathFilter) bool {
	switch filter.Type {
	case "exact":
		return path == filter.Path
	case "prefix":
		return strings.HasPrefix(path, filter.Path)
	case "suffix":
		return strings.HasSuffix(path, filter.Path)
	case "glob":
		if matched, err := filepath.Match(filter.Path, path); err == nil {
			return matched
		}
	}
	return false
}

// GetRiskLevel 获取命令风险等级
func (cf *CommandFilter) GetRiskLevel(command string, args []string) string {
	// 检查黑名单风险等级
	for _, rule := range cf.blacklist.Commands {
		if rule.Command == command {
			return rule.RiskLevel
		}
	}

	// 检查白名单风险等级
	for _, rule := range cf.whitelist.Commands {
		if rule.Command == command {
			return rule.RiskLevel
		}
	}

	// 检查模式风险等级
	fullCommand := command + " " + strings.Join(args, " ")
	for _, pattern := range cf.blacklist.Patterns {
		if cf.matchPattern(fullCommand, pattern) {
			return pattern.RiskLevel
		}
	}

	for _, pattern := range cf.whitelist.Patterns {
		if cf.matchPattern(fullCommand, pattern) {
			return pattern.RiskLevel
		}
	}

	return "unknown"
}

// AddWhitelistRule 添加白名单规则
func (cf *CommandFilter) AddWhitelistRule(rule CommandRule) {
	cf.whitelist.Commands = append(cf.whitelist.Commands, rule)
}

// AddBlacklistRule 添加黑名单规则
func (cf *CommandFilter) AddBlacklistRule(rule CommandRule) {
	cf.blacklist.Commands = append(cf.blacklist.Commands, rule)
}

// GetWhitelist 获取白名单
func (cf *CommandFilter) GetWhitelist() *CommandRuleSet {
	return cf.whitelist
}

// GetBlacklist 获取黑名单
func (cf *CommandFilter) GetBlacklist() *CommandRuleSet {
	return cf.blacklist
}
