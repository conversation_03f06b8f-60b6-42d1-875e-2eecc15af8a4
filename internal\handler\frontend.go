package handler

import (
	"net/http"

	"aiops-platform/internal/middleware"

	"github.com/gin-gonic/gin"
)

// Index 首页 - 现代化AI对话界面
func (h *FrontendHandler) Index(c *gin.Context) {
	// TODO: 暂时注释掉认证检查，用于开发测试
	// 检查是否已登录
	// user, exists := middleware.GetUserFromContext(c)
	// if !exists || user == nil {
	// 	// 未登录，重定向到登录页
	// 	c.Redirect(http.StatusFound, "/login")
	// 	return
	// }

	// 显示AI对话界面（暂时使用模拟用户数据）
	c.HTML(http.StatusOK, "index.html", gin.H{
		"title":       "AI运维助手 - 智能对话管理平台",
		"description": "通过AI对话简化IT基础设施管理",
		"user":        gin.H{"name": "管理员", "id": 1}, // 临时用户数据
	})
}

// Login 登录页面
func (h *FrontendHandler) Login(c *gin.Context) {
	// TODO: 暂时注释掉认证检查，用于开发测试
	// 检查是否已登录
	// user, exists := middleware.GetUserFromContext(c)
	// if exists && user != nil {
	// 	// 已登录，重定向到首页
	// 	c.Redirect(http.StatusFound, "/")
	// 	return
	// }

	c.HTML(http.StatusOK, "login.html", gin.H{
		"title": "用户登录 - AI运维平台",
	})
}

// Dashboard 仪表板
func (h *FrontendHandler) Dashboard(c *gin.Context) {
	// TODO: 暂时注释掉认证检查，用于开发测试
	// user, exists := middleware.GetUserFromContext(c)
	// if !exists || user == nil {
	// 	c.Redirect(http.StatusFound, "/login")
	// 	return
	// }

	c.HTML(http.StatusOK, "dashboard.html", gin.H{
		"title": "仪表板 - AI运维平台",
		"user":  gin.H{"name": "管理员", "id": 1}, // 临时用户数据
	})
}

// Hosts 主机管理页面
func (h *FrontendHandler) Hosts(c *gin.Context) {
	// TODO: 暂时注释掉认证检查，用于开发测试
	// user, exists := middleware.GetUserFromContext(c)
	// if !exists || user == nil {
	// 	c.Redirect(http.StatusFound, "/login")
	// 	return
	// }

	c.HTML(http.StatusOK, "hosts.html", gin.H{
		"title": "主机管理 - AI运维平台",
		"user":  gin.H{"name": "管理员", "id": 1}, // 临时用户数据
	})
}

// Alerts 告警管理页面
func (h *FrontendHandler) Alerts(c *gin.Context) {
	user, exists := middleware.GetUserFromContext(c)
	if !exists || user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	c.HTML(http.StatusOK, "alerts.html", gin.H{
		"title": "告警管理 - AI运维平台",
		"user":  user,
	})
}

// Chat AI对话页面
func (h *FrontendHandler) Chat(c *gin.Context) {
	user, exists := middleware.GetUserFromContext(c)
	if !exists || user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	c.HTML(http.StatusOK, "chat.html", gin.H{
		"title": "AI对话 - AI运维平台",
		"user":  user,
	})
}

// Stats 统计报表页面
func (h *FrontendHandler) Stats(c *gin.Context) {
	user, exists := middleware.GetUserFromContext(c)
	if !exists || user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	c.HTML(http.StatusOK, "stats.html", gin.H{
		"title": "统计报表 - AI运维平台",
		"user":  user,
	})
}

// Users 用户管理页面
func (h *FrontendHandler) Users(c *gin.Context) {
	user, exists := middleware.GetUserFromContext(c)
	if !exists || user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	// 检查管理员权限
	if !user.IsAdmin() {
		c.HTML(http.StatusForbidden, "error.html", gin.H{
			"title":   "访问被拒绝",
			"message": "您没有权限访问此页面",
			"code":    403,
		})
		return
	}

	c.HTML(http.StatusOK, "users.html", gin.H{
		"title": "用户管理 - AI运维平台",
		"user":  user,
	})
}

// Config 系统配置页面
func (h *FrontendHandler) Config(c *gin.Context) {
	user, exists := middleware.GetUserFromContext(c)
	if !exists || user == nil {
		c.Redirect(http.StatusFound, "/login")
		return
	}

	// 检查超级管理员权限
	if !user.IsSuperAdmin() {
		c.HTML(http.StatusForbidden, "error.html", gin.H{
			"title":   "访问被拒绝",
			"message": "您没有权限访问此页面",
			"code":    403,
		})
		return
	}

	c.HTML(http.StatusOK, "config.html", gin.H{
		"title": "系统配置 - AI运维平台",
		"user":  user,
	})
}

// RevolutionaryDemo 革命性AI演示页面
func (h *FrontendHandler) RevolutionaryDemo(c *gin.Context) {
	h.logger.Info("Serving Revolutionary AI Demo page")

	c.HTML(http.StatusOK, "revolutionary_demo.html", gin.H{
		"title":       "🚀 革命性AI运维管理平台 - 下一代意图识别演示",
		"description": "体验下一代AI运维管理平台的革命性意图识别技术",
	})
}
