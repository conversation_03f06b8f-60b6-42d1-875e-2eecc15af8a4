package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// 🚀 智能建议引擎 - AI驱动的个性化建议系统

// IntelligentSuggestionEngine 智能建议引擎
type IntelligentSuggestionEngine struct {
	logger          *logrus.Logger
	deepseekService *DeepSeekService
	suggestionCache map[string][]*CachedSuggestion
	knowledgeBase   *OperationalKnowledgeBase
}

// CachedSuggestion 缓存的建议
type CachedSuggestion struct {
	Suggestion  *SuggestionItem `json:"suggestion"`
	CreatedAt   time.Time       `json:"created_at"`
	ExpiresAt   time.Time       `json:"expires_at"`
	UsageCount  int             `json:"usage_count"`
	SuccessRate float64         `json:"success_rate"`
}

// 使用enhanced_conversation_engine.go中定义的SuggestionItem和ConversationTurn，避免重复定义

// OperationalKnowledgeBase 运维知识库
type OperationalKnowledgeBase struct {
	Commands        map[string]*CommandKnowledge        `json:"commands"`
	Workflows       map[string]*WorkflowKnowledge       `json:"workflows"`
	BestPractices   map[string]*BestPracticeKnowledge   `json:"best_practices"`
	TroubleShooting map[string]*TroubleShootingKnowledge `json:"trouble_shooting"`
	LearningPaths   map[string]*LearningPathKnowledge   `json:"learning_paths"`
}

// CommandKnowledge 命令知识
type CommandKnowledge struct {
	Command     string            `json:"command"`
	Description string            `json:"description"`
	Usage       string            `json:"usage"`
	Examples    []string          `json:"examples"`
	Risks       []string          `json:"risks"`
	Alternatives []string         `json:"alternatives"`
	Prerequisites []string        `json:"prerequisites"`
	Tags        []string          `json:"tags"`
	Difficulty  string            `json:"difficulty"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// WorkflowKnowledge 工作流知识
type WorkflowKnowledge struct {
	WorkflowID  string                 `json:"workflow_id"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Steps       []WorkflowStepKnowledge `json:"steps"`
	UseCase     string                 `json:"use_case"`
	Benefits    []string               `json:"benefits"`
	Complexity  string                 `json:"complexity"`
	EstimatedTime string               `json:"estimated_time"`
	Tags        []string               `json:"tags"`
}

// WorkflowStepKnowledge 工作流步骤知识
type WorkflowStepKnowledge struct {
	StepID      string   `json:"step_id"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Commands    []string `json:"commands"`
	Validation  string   `json:"validation"`
	Tips        []string `json:"tips"`
}

// BestPracticeKnowledge 最佳实践知识
type BestPracticeKnowledge struct {
	PracticeID  string   `json:"practice_id"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Category    string   `json:"category"`
	Benefits    []string `json:"benefits"`
	Implementation []string `json:"implementation"`
	Examples    []string `json:"examples"`
	Pitfalls    []string `json:"pitfalls"`
	References  []string `json:"references"`
}

// TroubleShootingKnowledge 故障排除知识
type TroubleShootingKnowledge struct {
	ProblemID   string                 `json:"problem_id"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Symptoms    []string               `json:"symptoms"`
	Causes      []string               `json:"causes"`
	Solutions   []TroubleshootingSolution `json:"solutions"`
	Prevention  []string               `json:"prevention"`
	Severity    string                 `json:"severity"`
	Tags        []string               `json:"tags"`
}

// TroubleshootingSolution 故障排除解决方案
type TroubleshootingSolution struct {
	SolutionID  string   `json:"solution_id"`
	Title       string   `json:"title"`
	Steps       []string `json:"steps"`
	Commands    []string `json:"commands"`
	Verification string  `json:"verification"`
	Risk        string   `json:"risk"`
	SuccessRate float64  `json:"success_rate"`
}

// LearningPathKnowledge 学习路径知识
type LearningPathKnowledge struct {
	PathID      string                `json:"path_id"`
	Title       string                `json:"title"`
	Description string                `json:"description"`
	Level       string                `json:"level"`
	Duration    string                `json:"duration"`
	Modules     []LearningModuleKnowledge `json:"modules"`
	Prerequisites []string            `json:"prerequisites"`
	Outcomes    []string              `json:"outcomes"`
	Tags        []string              `json:"tags"`
}

// LearningModuleKnowledge 学习模块知识
type LearningModuleKnowledge struct {
	ModuleID    string   `json:"module_id"`
	Title       string   `json:"title"`
	Description string   `json:"description"`
	Topics      []string `json:"topics"`
	Exercises   []string `json:"exercises"`
	Resources   []string `json:"resources"`
	Duration    string   `json:"duration"`
}

// SuggestionContext 建议上下文
type SuggestionContext struct {
	UserMessage     string                 `json:"user_message"`
	Intent          string                 `json:"intent"`
	UserProfile     *UserProfile           `json:"user_profile"`
	ConversationHistory []*ConversationTurn `json:"conversation_history"`
	SystemState     map[string]interface{} `json:"system_state"`
	RecentActions   []string               `json:"recent_actions"`
	CurrentTask     string                 `json:"current_task"`
}

// NewIntelligentSuggestionEngine 创建智能建议引擎
func NewIntelligentSuggestionEngine(logger *logrus.Logger, deepseekService *DeepSeekService) *IntelligentSuggestionEngine {
	engine := &IntelligentSuggestionEngine{
		logger:          logger,
		deepseekService: deepseekService,
		suggestionCache: make(map[string][]*CachedSuggestion),
		knowledgeBase:   initializeKnowledgeBase(),
	}

	logger.Info("🚀 智能建议引擎初始化完成")
	return engine
}

// GenerateSuggestions 生成智能建议
func (ise *IntelligentSuggestionEngine) GenerateSuggestions(ctx context.Context, req *ConversationRequest, response *ConversationResponse) ([]SuggestionItem, error) {
	ise.logger.WithFields(logrus.Fields{
		"session_id": req.SessionID,
		"user_id":    req.UserID,
	}).Info("🎯 开始生成智能建议")

	// 构建建议上下文
	suggestionContext := &SuggestionContext{
		UserMessage:         req.Message,
		UserProfile:         req.UserProfile,
		ConversationHistory: req.ConversationHistory,
		SystemState:         req.Context,
		CurrentTask:         ise.extractCurrentTask(req),
	}

	// 1. 基于知识库的建议
	knowledgeBasedSuggestions := ise.generateKnowledgeBasedSuggestions(suggestionContext)

	// 2. AI生成的智能建议
	aiGeneratedSuggestions, err := ise.generateAISuggestions(ctx, suggestionContext, response)
	if err != nil {
		ise.logger.WithError(err).Warn("AI建议生成失败")
		aiGeneratedSuggestions = []SuggestionItem{}
	}

	// 3. 个性化建议
	personalizedSuggestions := ise.generatePersonalizedSuggestions(suggestionContext)

	// 4. 预测性建议
	predictiveSuggestions := ise.generatePredictiveSuggestions(suggestionContext)

	// 5. 合并和排序建议
	allSuggestions := append(knowledgeBasedSuggestions, aiGeneratedSuggestions...)
	allSuggestions = append(allSuggestions, personalizedSuggestions...)
	allSuggestions = append(allSuggestions, predictiveSuggestions...)

	// 6. 去重和优化
	optimizedSuggestions := ise.optimizeSuggestions(allSuggestions, suggestionContext)

	ise.logger.WithFields(logrus.Fields{
		"session_id":        req.SessionID,
		"suggestions_count": len(optimizedSuggestions),
	}).Info("🚀 智能建议生成完成")

	return optimizedSuggestions, nil
}

// generateKnowledgeBasedSuggestions 生成基于知识库的建议
func (ise *IntelligentSuggestionEngine) generateKnowledgeBasedSuggestions(context *SuggestionContext) []SuggestionItem {
	var suggestions []SuggestionItem

	// 基于用户消息匹配相关命令
	for _, command := range ise.knowledgeBase.Commands {
		if ise.isCommandRelevant(command, context.UserMessage) {
			suggestion := SuggestionItem{
				Type:       "command",
				Content:    fmt.Sprintf("建议使用命令: %s - %s", command.Command, command.Description),
				Confidence: 0.8,
				Priority:   2,
				Category:   "command_suggestion",
				ActionType: "execute",
				Metadata: map[string]interface{}{
					"command":     command.Command,
					"usage":       command.Usage,
					"examples":    command.Examples,
					"difficulty":  command.Difficulty,
				},
			}
			suggestions = append(suggestions, suggestion)
		}
	}

	// 基于当前任务推荐工作流
	for _, workflow := range ise.knowledgeBase.Workflows {
		if ise.isWorkflowRelevant(workflow, context) {
			suggestion := SuggestionItem{
				Type:       "workflow",
				Content:    fmt.Sprintf("推荐工作流: %s - %s", workflow.Title, workflow.Description),
				Confidence: 0.75,
				Priority:   1,
				Category:   "workflow_suggestion",
				ActionType: "explore",
				Metadata: map[string]interface{}{
					"workflow_id":    workflow.WorkflowID,
					"steps":          workflow.Steps,
					"complexity":     workflow.Complexity,
					"estimated_time": workflow.EstimatedTime,
				},
			}
			suggestions = append(suggestions, suggestion)
		}
	}

	return suggestions
}

// generateAISuggestions 生成AI智能建议
func (ise *IntelligentSuggestionEngine) generateAISuggestions(ctx context.Context, suggestionContext *SuggestionContext, response *ConversationResponse) ([]SuggestionItem, error) {
	// 构建AI建议生成提示
	systemPrompt := `你是一个智能运维建议生成器。基于用户的对话和上下文，生成3-5个有价值的建议。

请返回JSON格式的建议列表：
{
  "suggestions": [
    {
      "type": "command|tip|workflow|learning",
      "content": "建议内容",
      "confidence": 0.0-1.0,
      "priority": 1-5,
      "category": "建议分类",
      "action_type": "execute|learn|explore",
      "metadata": {}
    }
  ]
}`

	userPrompt := fmt.Sprintf(`用户消息: %s
用户专业水平: %s
当前任务: %s
对话历史: %s

请生成相关的智能建议。`,
		suggestionContext.UserMessage,
		ise.getUserExpertiseLevel(suggestionContext.UserProfile),
		suggestionContext.CurrentTask,
		ise.formatConversationHistory(suggestionContext.ConversationHistory))

	messages := []Message{
		{Role: "system", Content: systemPrompt},
		{Role: "user", Content: userPrompt},
	}

	aiResponse, err := ise.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("AI建议生成失败: %w", err)
	}

	if len(aiResponse.Choices) == 0 {
		return nil, fmt.Errorf("AI返回空响应")
	}

	// 解析AI响应
	var aiSuggestionResponse struct {
		Suggestions []SuggestionItem `json:"suggestions"`
	}

	content := aiResponse.Choices[0].Message.Content
	if err := json.Unmarshal([]byte(content), &aiSuggestionResponse); err != nil {
		ise.logger.WithError(err).Warn("AI建议响应解析失败")
		return []SuggestionItem{}, nil
	}

	return aiSuggestionResponse.Suggestions, nil
}

// generatePersonalizedSuggestions 生成个性化建议
func (ise *IntelligentSuggestionEngine) generatePersonalizedSuggestions(context *SuggestionContext) []SuggestionItem {
	var suggestions []SuggestionItem

	if context.UserProfile == nil {
		return suggestions
	}

	// 基于用户专业水平的建议
	switch context.UserProfile.ExpertiseLevel {
	case "beginner":
		suggestions = append(suggestions, SuggestionItem{
			Type:       "learning",
			Content:    "建议从基础教程开始学习",
			Confidence: 0.9,
			Priority:   1,
			Category:   "learning_path",
			ActionType: "learn",
		})
	case "intermediate":
		suggestions = append(suggestions, SuggestionItem{
			Type:       "tip",
			Content:    "尝试使用高级功能提升效率",
			Confidence: 0.8,
			Priority:   2,
			Category:   "efficiency_tip",
			ActionType: "explore",
		})
	case "expert":
		suggestions = append(suggestions, SuggestionItem{
			Type:       "workflow",
			Content:    "考虑分享经验或创建自定义工作流",
			Confidence: 0.7,
			Priority:   3,
			Category:   "contribution",
			ActionType: "explore",
		})
	}

	// 基于常见任务的建议
	for _, task := range context.UserProfile.CommonTasks {
		if strings.Contains(context.UserMessage, task) {
			suggestions = append(suggestions, SuggestionItem{
				Type:       "tip",
				Content:    fmt.Sprintf("基于您的%s经验，建议使用自动化脚本", task),
				Confidence: 0.85,
				Priority:   2,
				Category:   "automation_tip",
				ActionType: "execute",
			})
		}
	}

	return suggestions
}

// generatePredictiveSuggestions 生成预测性建议
func (ise *IntelligentSuggestionEngine) generatePredictiveSuggestions(context *SuggestionContext) []SuggestionItem {
	var suggestions []SuggestionItem

	// 基于对话历史预测下一步操作
	if len(context.ConversationHistory) > 0 {
		lastTurn := context.ConversationHistory[len(context.ConversationHistory)-1]
		
		// 如果最后一次是查询操作，建议进行相关的管理操作
		if strings.Contains(lastTurn.Intent, "query") || strings.Contains(lastTurn.Intent, "list") {
			suggestions = append(suggestions, SuggestionItem{
				Type:       "tip",
				Content:    "查询完成后，您可能需要进行配置或优化操作",
				Confidence: 0.7,
				Priority:   3,
				Category:   "next_step_prediction",
				ActionType: "explore",
			})
		}
	}

	return suggestions
}

// 辅助方法
func (ise *IntelligentSuggestionEngine) extractCurrentTask(req *ConversationRequest) string {
	// 从用户消息中提取当前任务
	if strings.Contains(req.Message, "监控") {
		return "system_monitoring"
	} else if strings.Contains(req.Message, "部署") {
		return "deployment"
	} else if strings.Contains(req.Message, "配置") {
		return "configuration"
	}
	return "general_operations"
}

func (ise *IntelligentSuggestionEngine) isCommandRelevant(command *CommandKnowledge, message string) bool {
	// 简单的关键词匹配
	for _, tag := range command.Tags {
		if strings.Contains(strings.ToLower(message), strings.ToLower(tag)) {
			return true
		}
	}
	return false
}

func (ise *IntelligentSuggestionEngine) isWorkflowRelevant(workflow *WorkflowKnowledge, context *SuggestionContext) bool {
	// 基于用户消息和当前任务判断工作流相关性
	return strings.Contains(context.CurrentTask, workflow.UseCase)
}

func (ise *IntelligentSuggestionEngine) getUserExpertiseLevel(profile *UserProfile) string {
	if profile == nil {
		return "intermediate"
	}
	return profile.ExpertiseLevel
}

func (ise *IntelligentSuggestionEngine) formatConversationHistory(history []*ConversationTurn) string {
	if len(history) == 0 {
		return "无对话历史"
	}
	
	var formatted strings.Builder
	maxHistory := 3 // 只显示最近3轮对话
	start := len(history) - maxHistory
	if start < 0 {
		start = 0
	}
	
	for i := start; i < len(history); i++ {
		turn := history[i]
		formatted.WriteString(fmt.Sprintf("用户: %s\nAI: %s\n", turn.UserMessage, turn.AIResponse))
	}
	
	return formatted.String()
}

func (ise *IntelligentSuggestionEngine) optimizeSuggestions(suggestions []SuggestionItem, context *SuggestionContext) []SuggestionItem {
	// 去重、排序和限制数量
	uniqueSuggestions := make(map[string]SuggestionItem)

	for _, suggestion := range suggestions {
		key := fmt.Sprintf("%s_%s", suggestion.Type, suggestion.Category)
		if existing, exists := uniqueSuggestions[key]; !exists || suggestion.Confidence > existing.Confidence {
			uniqueSuggestions[key] = suggestion
		}
	}

	var result []SuggestionItem
	for _, suggestion := range uniqueSuggestions {
		result = append(result, suggestion)
	}
	
	// 按优先级和置信度排序
	// 这里可以实现更复杂的排序逻辑
	
	// 限制返回数量
	maxSuggestions := 5
	if len(result) > maxSuggestions {
		result = result[:maxSuggestions]
	}
	
	return result
}

// initializeKnowledgeBase 初始化知识库
func initializeKnowledgeBase() *OperationalKnowledgeBase {
	return &OperationalKnowledgeBase{
		Commands:        make(map[string]*CommandKnowledge),
		Workflows:       make(map[string]*WorkflowKnowledge),
		BestPractices:   make(map[string]*BestPracticeKnowledge),
		TroubleShooting: make(map[string]*TroubleShootingKnowledge),
		LearningPaths:   make(map[string]*LearningPathKnowledge),
	}
}
