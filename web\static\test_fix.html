<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI运维管理平台 - 修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .success { border-color: #4CAF50; background-color: #f8fff8; }
        .error { border-color: #f44336; background-color: #fff8f8; }
        .info { border-color: #2196F3; background-color: #f8f9ff; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 AI运维管理平台修复验证</h1>
        
        <div class="test-section info">
            <h3>📋 修复内容</h3>
            <ul>
                <li>✅ 修复了 loadAvailableModels 函数未定义错误</li>
                <li>✅ 添加了 metric_update 消息类型处理</li>
                <li>✅ 优化了 WebSocket 消息处理流程</li>
                <li>✅ 修复了 AI 模型配置加载问题</li>
            </ul>
        </div>

        <div class="test-section">
            <h3>🧪 测试1: API端点测试</h3>
            <button onclick="testModelsAPI()">测试模型API</button>
            <div id="api-result" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧪 测试2: JavaScript函数测试</h3>
            <button onclick="testJavaScriptFunctions()">测试JS函数</button>
            <div id="js-result" class="log"></div>
        </div>

        <div class="test-section">
            <h3>🧪 测试3: WebSocket连接测试</h3>
            <button onclick="testWebSocket()">测试WebSocket</button>
            <button onclick="sendTestMessage()">发送测试消息</button>
            <div id="ws-result" class="log"></div>
        </div>

        <div class="test-section">
            <h3>📊 实时日志</h3>
            <div id="console-log" class="log"></div>
        </div>
    </div>

    <script>
        // 重写console.log来显示日志
        const originalLog = console.log;
        const originalError = console.error;
        const logDiv = document.getElementById('console-log');
        
        function addLog(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logDiv.textContent += logEntry;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            addLog(args.join(' '), 'info');
        };
        
        console.error = function(...args) {
            originalError.apply(console, args);
            addLog(args.join(' '), 'error');
        };

        let testWs = null;

        // 测试模型API
        async function testModelsAPI() {
            const resultDiv = document.getElementById('api-result');
            resultDiv.textContent = '正在测试API...\n';
            
            try {
                const response = await fetch('/api/v1/models/available');
                const result = await response.json();
                
                if (result.success && result.data.models.length > 0) {
                    resultDiv.textContent = `✅ API测试成功!\n模型数量: ${result.data.models.length}\n当前模型: ${result.data.current_model}\n\n模型列表:\n`;
                    result.data.models.forEach(model => {
                        resultDiv.textContent += `- ${model.display_name} (${model.name})\n`;
                    });
                    resultDiv.parentElement.className = 'test-section success';
                } else {
                    resultDiv.textContent = '❌ API返回数据异常: ' + JSON.stringify(result, null, 2);
                    resultDiv.parentElement.className = 'test-section error';
                }
            } catch (error) {
                resultDiv.textContent = '❌ API测试失败: ' + error.message;
                resultDiv.parentElement.className = 'test-section error';
            }
        }

        // 测试JavaScript函数
        function testJavaScriptFunctions() {
            const resultDiv = document.getElementById('js-result');
            resultDiv.textContent = '正在测试JavaScript函数...\n';
            
            const tests = [
                { name: 'loadAvailableModels', func: () => typeof loadAvailableModels === 'function' },
                { name: 'handleWebSocketMessage', func: () => typeof handleWebSocketMessage === 'function' },
                { name: 'handleMetricUpdate', func: () => typeof handleMetricUpdate === 'function' },
                { name: 'updateModelSelector', func: () => typeof updateModelSelector === 'function' }
            ];
            
            let allPassed = true;
            tests.forEach(test => {
                try {
                    const result = test.func();
                    const status = result ? '✅' : '❌';
                    resultDiv.textContent += `${status} ${test.name}: ${result ? '已定义' : '未定义'}\n`;
                    if (!result) allPassed = false;
                } catch (error) {
                    resultDiv.textContent += `❌ ${test.name}: 错误 - ${error.message}\n`;
                    allPassed = false;
                }
            });
            
            resultDiv.parentElement.className = allPassed ? 'test-section success' : 'test-section error';
        }

        // 测试WebSocket连接
        function testWebSocket() {
            const resultDiv = document.getElementById('ws-result');
            resultDiv.textContent = '正在测试WebSocket连接...\n';
            
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws/chat?session_id=test_session_${Date.now()}`;
                
                testWs = new WebSocket(wsUrl);
                
                testWs.onopen = function() {
                    resultDiv.textContent += '✅ WebSocket连接成功!\n';
                    resultDiv.parentElement.className = 'test-section success';
                };
                
                testWs.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        resultDiv.textContent += `📨 收到消息: ${data.type}\n`;
                        if (data.type === 'metric_update') {
                            resultDiv.textContent += '✅ metric_update消息处理正常!\n';
                        }
                    } catch (error) {
                        resultDiv.textContent += `❌ 消息解析错误: ${error.message}\n`;
                    }
                };
                
                testWs.onerror = function(error) {
                    resultDiv.textContent += '❌ WebSocket错误: ' + error.message + '\n';
                    resultDiv.parentElement.className = 'test-section error';
                };
                
                testWs.onclose = function() {
                    resultDiv.textContent += '🔌 WebSocket连接已关闭\n';
                };
                
            } catch (error) {
                resultDiv.textContent += '❌ WebSocket测试失败: ' + error.message + '\n';
                resultDiv.parentElement.className = 'test-section error';
            }
        }

        // 发送测试消息
        function sendTestMessage() {
            if (!testWs || testWs.readyState !== WebSocket.OPEN) {
                alert('请先建立WebSocket连接');
                return;
            }
            
            const testMessage = {
                type: 'message',
                content: '测试消息：列出主机'
            };
            
            testWs.send(JSON.stringify(testMessage));
            document.getElementById('ws-result').textContent += '📤 已发送测试消息\n';
        }

        // 页面加载完成后自动运行测试
        window.addEventListener('load', function() {
            console.log('🚀 AI运维管理平台修复验证页面已加载');
            console.log('💡 可以点击按钮进行各项测试');
        });
    </script>
</body>
</html>
