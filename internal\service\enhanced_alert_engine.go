package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// EnhancedAlertEngine 增强告警引擎
type EnhancedAlertEngine struct {
	db               *gorm.DB
	logger           *logrus.Logger
	wsManager        *WebSocketManager
	config           *EnhancedAlertConfig
	ruleManager      *EnhancedAlertRuleManager
	thresholdManager *DynamicThresholdManager
	notificationMgr  *NotificationManager
	alertHistory     *AlertHistoryManager
	running          bool
	stopChan         chan struct{}
	mutex            sync.RWMutex
	alertQueue       chan *AlertEvent
	metrics          *AlertEngineMetrics
}

// EnhancedAlertConfig 增强告警配置
type EnhancedAlertConfig struct {
	Enabled                bool          `json:"enabled"`
	MaxConcurrentAlerts    int           `json:"max_concurrent_alerts"`
	AlertQueueSize         int           `json:"alert_queue_size"`
	ProcessingTimeout      time.Duration `json:"processing_timeout"`
	EnableDynamicThreshold bool          `json:"enable_dynamic_threshold"`
	EnableNoiseReduction   bool          `json:"enable_noise_reduction"`
	EnableEscalation       bool          `json:"enable_escalation"`
	RetentionDays          int           `json:"retention_days"`
	NotificationChannels   []string      `json:"notification_channels"`
}

// AlertEvent 告警事件
type AlertEvent struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"`
	Severity    string                 `json:"severity"`
	Source      string                 `json:"source"`
	HostID      int64                  `json:"host_id"`
	HostName    string                 `json:"host_name"`
	MetricType  string                 `json:"metric_type"`
	Value       float64                `json:"value"`
	Threshold   float64                `json:"threshold"`
	Condition   string                 `json:"condition"`
	Message     string                 `json:"message"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata"`
	Status      string                 `json:"status"` // new, processing, resolved, escalated
	ProcessedAt *time.Time             `json:"processed_at,omitempty"`
	ResolvedAt  *time.Time             `json:"resolved_at,omitempty"`
}

// EnhancedAlertRuleManager 增强告警规则管理器
type EnhancedAlertRuleManager struct {
	db     *gorm.DB
	logger *logrus.Logger
	rules  map[string]*EnhancedAlertRule
	mutex  sync.RWMutex
}

// EnhancedAlertRule 增强告警规则
type EnhancedAlertRule struct {
	ID              string                 `json:"id"`
	Name            string                 `json:"name"`
	Description     string                 `json:"description"`
	MetricType      string                 `json:"metric_type"`
	Condition       string                 `json:"condition"`
	Threshold       float64                `json:"threshold"`
	Severity        string                 `json:"severity"`
	Enabled         bool                   `json:"enabled"`
	HostFilter      string                 `json:"host_filter"`
	TimeWindow      time.Duration          `json:"time_window"`
	EvaluationCount int                    `json:"evaluation_count"`
	CooldownPeriod  time.Duration          `json:"cooldown_period"`
	Actions         []AlertAction          `json:"actions"`
	Metadata        map[string]interface{} `json:"metadata"`
	CreatedAt       time.Time              `json:"created_at"`
	UpdatedAt       time.Time              `json:"updated_at"`
}

// AlertAction 告警动作
type AlertAction struct {
	Type       string                 `json:"type"`
	Target     string                 `json:"target"`
	Parameters map[string]interface{} `json:"parameters"`
	Enabled    bool                   `json:"enabled"`
}

// DynamicThresholdManager 动态阈值管理器
type DynamicThresholdManager struct {
	logger     *logrus.Logger
	thresholds map[string]*DynamicThreshold
	mutex      sync.RWMutex
}

// DynamicThreshold 动态阈值
type DynamicThreshold struct {
	MetricType    string    `json:"metric_type"`
	HostID        int64     `json:"host_id"`
	BaseThreshold float64   `json:"base_threshold"`
	CurrentValue  float64   `json:"current_value"`
	Trend         string    `json:"trend"` // increasing, decreasing, stable
	Confidence    float64   `json:"confidence"`
	LastUpdated   time.Time `json:"last_updated"`
	History       []float64 `json:"history"`
}

// NotificationManager 通知管理器
type NotificationManager struct {
	logger   *logrus.Logger
	channels map[string]NotificationChannel
	mutex    sync.RWMutex
}

// NotificationChannel 通知渠道接口
type NotificationChannel interface {
	Send(ctx context.Context, alert *AlertEvent) error
	GetName() string
	IsEnabled() bool
}

// AlertHistoryManager 告警历史管理器
type AlertHistoryManager struct {
	db     *gorm.DB
	logger *logrus.Logger
	mutex  sync.RWMutex
}

// AlertEngineMetrics 告警引擎指标
type AlertEngineMetrics struct {
	TotalAlerts     int64     `json:"total_alerts"`
	ProcessedAlerts int64     `json:"processed_alerts"`
	ResolvedAlerts  int64     `json:"resolved_alerts"`
	EscalatedAlerts int64     `json:"escalated_alerts"`
	FailedAlerts    int64     `json:"failed_alerts"`
	AvgProcessTime  float64   `json:"avg_process_time_ms"`
	LastProcessed   time.Time `json:"last_processed"`
	QueueSize       int       `json:"queue_size"`
	ActiveRules     int       `json:"active_rules"`
}

// NewEnhancedAlertEngine 创建增强告警引擎
func NewEnhancedAlertEngine(db *gorm.DB, logger *logrus.Logger, wsManager *WebSocketManager) *EnhancedAlertEngine {
	config := &EnhancedAlertConfig{
		Enabled:                true,
		MaxConcurrentAlerts:    20,
		AlertQueueSize:         1000,
		ProcessingTimeout:      30 * time.Second,
		EnableDynamicThreshold: true,
		EnableNoiseReduction:   true,
		EnableEscalation:       true,
		RetentionDays:          30,
		NotificationChannels:   []string{"websocket", "email", "webhook"},
	}

	engine := &EnhancedAlertEngine{
		db:               db,
		logger:           logger,
		wsManager:        wsManager,
		config:           config,
		stopChan:         make(chan struct{}),
		alertQueue:       make(chan *AlertEvent, config.AlertQueueSize),
		metrics:          &AlertEngineMetrics{},
		ruleManager:      NewEnhancedAlertRuleManager(db, logger),
		thresholdManager: NewDynamicThresholdManager(logger),
		notificationMgr:  NewNotificationManager(logger, wsManager),
		alertHistory:     NewAlertHistoryManager(db, logger),
	}

	return engine
}

// Start 启动增强告警引擎
func (eae *EnhancedAlertEngine) Start(ctx context.Context) error {
	eae.mutex.Lock()
	defer eae.mutex.Unlock()

	if eae.running {
		return fmt.Errorf("enhanced alert engine is already running")
	}

	if !eae.config.Enabled {
		eae.logger.Info("Enhanced alert engine is disabled")
		return nil
	}

	eae.running = true

	// 启动告警处理协程
	for i := 0; i < eae.config.MaxConcurrentAlerts; i++ {
		go eae.alertProcessor(ctx, i)
	}

	// 启动规则管理器
	if err := eae.ruleManager.Start(ctx); err != nil {
		return fmt.Errorf("failed to start rule manager: %w", err)
	}

	// 启动通知管理器
	if err := eae.notificationMgr.Start(ctx); err != nil {
		return fmt.Errorf("failed to start notification manager: %w", err)
	}

	// 启动清理协程
	go eae.cleanupRoutine(ctx)

	eae.logger.Info("🚨 Enhanced alert engine started")
	return nil
}

// Stop 停止增强告警引擎
func (eae *EnhancedAlertEngine) Stop() error {
	eae.mutex.Lock()
	defer eae.mutex.Unlock()

	if !eae.running {
		return nil
	}

	close(eae.stopChan)
	close(eae.alertQueue)
	eae.running = false

	eae.logger.Info("Enhanced alert engine stopped")
	return nil
}

// ProcessMetric 处理指标数据
func (eae *EnhancedAlertEngine) ProcessMetric(ctx context.Context, metric *MetricData) error {
	if !eae.running {
		return fmt.Errorf("enhanced alert engine is not running")
	}

	// 更新动态阈值
	if eae.config.EnableDynamicThreshold {
		eae.thresholdManager.UpdateMetric(metric)
	}

	// 评估告警规则
	alerts := eae.ruleManager.EvaluateMetric(metric)

	// 将告警事件加入队列
	for _, alert := range alerts {
		select {
		case eae.alertQueue <- alert:
			eae.metrics.TotalAlerts++
		default:
			eae.logger.Warn("Alert queue is full, dropping alert")
			eae.metrics.FailedAlerts++
		}
	}

	return nil
}

// alertProcessor 告警处理器
func (eae *EnhancedAlertEngine) alertProcessor(ctx context.Context, workerID int) {
	eae.logger.WithField("worker_id", workerID).Debug("Alert processor started")

	for {
		select {
		case <-ctx.Done():
			return
		case <-eae.stopChan:
			return
		case alert, ok := <-eae.alertQueue:
			if !ok {
				return
			}
			eae.processAlert(ctx, alert, workerID)
		}
	}
}

// processAlert 处理单个告警
func (eae *EnhancedAlertEngine) processAlert(ctx context.Context, alert *AlertEvent, workerID int) {
	startTime := time.Now()
	defer func() {
		duration := time.Since(startTime)
		eae.updateProcessingMetrics(duration)
	}()

	eae.logger.WithFields(logrus.Fields{
		"worker_id":  workerID,
		"alert_id":   alert.ID,
		"alert_type": alert.Type,
		"severity":   alert.Severity,
	}).Debug("Processing alert")

	alert.Status = "processing"
	now := time.Now()
	alert.ProcessedAt = &now

	// 保存到历史记录
	if err := eae.alertHistory.SaveAlert(alert); err != nil {
		eae.logger.WithError(err).Error("Failed to save alert to history")
	}

	// 发送通知
	if err := eae.notificationMgr.SendNotification(ctx, alert); err != nil {
		eae.logger.WithError(err).Error("Failed to send notification")
		eae.metrics.FailedAlerts++
		return
	}

	eae.metrics.ProcessedAlerts++
	eae.logger.WithField("alert_id", alert.ID).Info("✅ Alert processed successfully")
}

// updateProcessingMetrics 更新处理指标
func (eae *EnhancedAlertEngine) updateProcessingMetrics(duration time.Duration) {
	eae.mutex.Lock()
	defer eae.mutex.Unlock()

	// 计算平均处理时间
	if eae.metrics.ProcessedAlerts > 0 {
		totalTime := eae.metrics.AvgProcessTime * float64(eae.metrics.ProcessedAlerts-1)
		eae.metrics.AvgProcessTime = (totalTime + float64(duration.Nanoseconds())/1e6) / float64(eae.metrics.ProcessedAlerts)
	} else {
		eae.metrics.AvgProcessTime = float64(duration.Nanoseconds()) / 1e6
	}

	eae.metrics.LastProcessed = time.Now()
	eae.metrics.QueueSize = len(eae.alertQueue)
}

// cleanupRoutine 清理协程
func (eae *EnhancedAlertEngine) cleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(24 * time.Hour) // 每天清理一次
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-eae.stopChan:
			return
		case <-ticker.C:
			eae.performCleanup()
		}
	}
}

// performCleanup 执行清理
func (eae *EnhancedAlertEngine) performCleanup() {
	cutoffDate := time.Now().AddDate(0, 0, -eae.config.RetentionDays)
	
	if err := eae.alertHistory.CleanupOldAlerts(cutoffDate); err != nil {
		eae.logger.WithError(err).Error("Failed to cleanup old alerts")
	} else {
		eae.logger.WithField("cutoff_date", cutoffDate).Info("Old alerts cleaned up")
	}
}

// GetMetrics 获取引擎指标
func (eae *EnhancedAlertEngine) GetMetrics() *AlertEngineMetrics {
	eae.mutex.RLock()
	defer eae.mutex.RUnlock()

	metrics := *eae.metrics
	metrics.QueueSize = len(eae.alertQueue)
	metrics.ActiveRules = eae.ruleManager.GetActiveRuleCount()
	
	return &metrics
}

// NewEnhancedAlertRuleManager 创建增强告警规则管理器
func NewEnhancedAlertRuleManager(db *gorm.DB, logger *logrus.Logger) *EnhancedAlertRuleManager {
	return &EnhancedAlertRuleManager{
		db:     db,
		logger: logger,
		rules:  make(map[string]*EnhancedAlertRule),
	}
}

// Start 启动规则管理器
func (earm *EnhancedAlertRuleManager) Start(ctx context.Context) error {
	// 加载默认规则
	earm.loadDefaultRules()

	// 从数据库加载自定义规则
	if err := earm.loadCustomRules(); err != nil {
		earm.logger.WithError(err).Warn("Failed to load custom rules")
	}

	earm.logger.WithField("rule_count", len(earm.rules)).Info("Enhanced alert rule manager started")
	return nil
}

// loadDefaultRules 加载默认规则
func (earm *EnhancedAlertRuleManager) loadDefaultRules() {
	defaultRules := []*EnhancedAlertRule{
		{
			ID:              "cpu_high_usage",
			Name:            "CPU使用率过高",
			Description:     "当CPU使用率超过80%时触发告警",
			MetricType:      "cpu_usage",
			Condition:       ">",
			Threshold:       80.0,
			Severity:        "warning",
			Enabled:         true,
			TimeWindow:      5 * time.Minute,
			EvaluationCount: 3,
			CooldownPeriod:  10 * time.Minute,
			Actions: []AlertAction{
				{Type: "websocket", Target: "all", Enabled: true},
				{Type: "log", Target: "system", Enabled: true},
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:              "memory_high_usage",
			Name:            "内存使用率过高",
			Description:     "当内存使用率超过85%时触发告警",
			MetricType:      "memory_usage",
			Condition:       ">",
			Threshold:       85.0,
			Severity:        "warning",
			Enabled:         true,
			TimeWindow:      5 * time.Minute,
			EvaluationCount: 2,
			CooldownPeriod:  15 * time.Minute,
			Actions: []AlertAction{
				{Type: "websocket", Target: "all", Enabled: true},
				{Type: "log", Target: "system", Enabled: true},
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:              "disk_high_usage",
			Name:            "磁盘使用率过高",
			Description:     "当磁盘使用率超过90%时触发告警",
			MetricType:      "disk_usage",
			Condition:       ">",
			Threshold:       90.0,
			Severity:        "critical",
			Enabled:         true,
			TimeWindow:      10 * time.Minute,
			EvaluationCount: 1,
			CooldownPeriod:  30 * time.Minute,
			Actions: []AlertAction{
				{Type: "websocket", Target: "all", Enabled: true},
				{Type: "log", Target: "system", Enabled: true},
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
		{
			ID:              "host_offline",
			Name:            "主机离线",
			Description:     "当主机状态变为离线时触发告警",
			MetricType:      "host_status",
			Condition:       "==",
			Threshold:       0, // 0表示离线
			Severity:        "critical",
			Enabled:         true,
			TimeWindow:      1 * time.Minute,
			EvaluationCount: 1,
			CooldownPeriod:  5 * time.Minute,
			Actions: []AlertAction{
				{Type: "websocket", Target: "all", Enabled: true},
				{Type: "log", Target: "system", Enabled: true},
			},
			CreatedAt: time.Now(),
			UpdatedAt: time.Now(),
		},
	}

	earm.mutex.Lock()
	defer earm.mutex.Unlock()

	for _, rule := range defaultRules {
		earm.rules[rule.ID] = rule
	}

	earm.logger.WithField("default_rules", len(defaultRules)).Info("Default alert rules loaded")
}

// loadCustomRules 加载自定义规则
func (earm *EnhancedAlertRuleManager) loadCustomRules() error {
	// TODO: 从数据库加载自定义规则
	// 这里可以扩展为从数据库加载用户自定义的告警规则
	return nil
}

// EvaluateMetric 评估指标
func (earm *EnhancedAlertRuleManager) EvaluateMetric(metric *MetricData) []*AlertEvent {
	earm.mutex.RLock()
	defer earm.mutex.RUnlock()

	var alerts []*AlertEvent

	for _, rule := range earm.rules {
		if !rule.Enabled {
			continue
		}

		// 检查指标类型是否匹配
		if rule.MetricType != metric.MetricType {
			continue
		}

		// 评估条件
		if earm.evaluateCondition(rule, metric) {
			alert := &AlertEvent{
				ID:         fmt.Sprintf("alert_%s_%d_%d", rule.ID, metric.HostID, time.Now().Unix()),
				Type:       "metric_threshold",
				Severity:   rule.Severity,
				Source:     "enhanced_alert_engine",
				HostID:     metric.HostID,
				HostName:   metric.HostName,
				MetricType: metric.MetricType,
				Value:      metric.Value,
				Threshold:  rule.Threshold,
				Condition:  rule.Condition,
				Message:    fmt.Sprintf("%s: %s %s %.2f (阈值: %.2f)", rule.Name, metric.MetricType, rule.Condition, metric.Value, rule.Threshold),
				Timestamp:  time.Now(),
				Status:     "new",
				Metadata: map[string]interface{}{
					"rule_id":     rule.ID,
					"rule_name":   rule.Name,
					"metric_unit": metric.Unit,
				},
			}
			alerts = append(alerts, alert)
		}
	}

	return alerts
}

// evaluateCondition 评估条件
func (earm *EnhancedAlertRuleManager) evaluateCondition(rule *EnhancedAlertRule, metric *MetricData) bool {
	value := metric.Value
	threshold := rule.Threshold

	switch rule.Condition {
	case ">":
		return value > threshold
	case ">=":
		return value >= threshold
	case "<":
		return value < threshold
	case "<=":
		return value <= threshold
	case "==":
		return value == threshold
	case "!=":
		return value != threshold
	default:
		earm.logger.WithField("condition", rule.Condition).Warn("Unknown condition")
		return false
	}
}

// GetActiveRuleCount 获取活跃规则数量
func (earm *EnhancedAlertRuleManager) GetActiveRuleCount() int {
	earm.mutex.RLock()
	defer earm.mutex.RUnlock()

	count := 0
	for _, rule := range earm.rules {
		if rule.Enabled {
			count++
		}
	}
	return count
}

// NewDynamicThresholdManager 创建动态阈值管理器
func NewDynamicThresholdManager(logger *logrus.Logger) *DynamicThresholdManager {
	return &DynamicThresholdManager{
		logger:     logger,
		thresholds: make(map[string]*DynamicThreshold),
	}
}

// UpdateMetric 更新指标
func (dtm *DynamicThresholdManager) UpdateMetric(metric *MetricData) {
	dtm.mutex.Lock()
	defer dtm.mutex.Unlock()

	key := fmt.Sprintf("%s_%d", metric.MetricType, metric.HostID)

	threshold, exists := dtm.thresholds[key]
	if !exists {
		threshold = &DynamicThreshold{
			MetricType:    metric.MetricType,
			HostID:        metric.HostID,
			BaseThreshold: dtm.getDefaultThreshold(metric.MetricType),
			History:       make([]float64, 0, 100), // 保留最近100个值
		}
		dtm.thresholds[key] = threshold
	}

	// 更新历史数据
	threshold.History = append(threshold.History, metric.Value)
	if len(threshold.History) > 100 {
		threshold.History = threshold.History[1:] // 保持最近100个值
	}

	// 更新当前值和趋势
	threshold.CurrentValue = metric.Value
	threshold.LastUpdated = time.Now()

	// 计算趋势
	if len(threshold.History) >= 5 {
		threshold.Trend = dtm.calculateTrend(threshold.History)
		threshold.Confidence = dtm.calculateConfidence(threshold.History)
	}
}

// getDefaultThreshold 获取默认阈值
func (dtm *DynamicThresholdManager) getDefaultThreshold(metricType string) float64 {
	defaults := map[string]float64{
		"cpu_usage":    80.0,
		"memory_usage": 85.0,
		"disk_usage":   90.0,
		"response_time": 1000.0, // ms
	}

	if threshold, exists := defaults[metricType]; exists {
		return threshold
	}
	return 100.0 // 默认阈值
}

// calculateTrend 计算趋势
func (dtm *DynamicThresholdManager) calculateTrend(history []float64) string {
	if len(history) < 5 {
		return "stable"
	}

	recent := history[len(history)-5:]
	sum := 0.0
	for i := 1; i < len(recent); i++ {
		sum += recent[i] - recent[i-1]
	}

	avgChange := sum / float64(len(recent)-1)

	if avgChange > 2.0 {
		return "increasing"
	} else if avgChange < -2.0 {
		return "decreasing"
	}
	return "stable"
}

// calculateConfidence 计算置信度
func (dtm *DynamicThresholdManager) calculateConfidence(history []float64) float64 {
	if len(history) < 10 {
		return 0.5 // 低置信度
	}

	// 计算标准差
	sum := 0.0
	for _, v := range history {
		sum += v
	}
	mean := sum / float64(len(history))

	variance := 0.0
	for _, v := range history {
		variance += (v - mean) * (v - mean)
	}
	variance /= float64(len(history))

	stdDev := variance // 简化计算

	// 标准差越小，置信度越高
	if stdDev < 5.0 {
		return 0.9
	} else if stdDev < 10.0 {
		return 0.7
	} else if stdDev < 20.0 {
		return 0.5
	}
	return 0.3
}

// NewNotificationManager 创建通知管理器
func NewNotificationManager(logger *logrus.Logger, wsManager *WebSocketManager) *NotificationManager {
	nm := &NotificationManager{
		logger:   logger,
		channels: make(map[string]NotificationChannel),
	}

	// 注册WebSocket通知渠道
	if wsManager != nil {
		nm.channels["websocket"] = &WebSocketNotificationChannel{
			wsManager: wsManager,
			logger:    logger,
		}
	}

	// 注册日志通知渠道
	nm.channels["log"] = &LogNotificationChannel{
		logger: logger,
	}

	return nm
}

// Start 启动通知管理器
func (nm *NotificationManager) Start(ctx context.Context) error {
	nm.logger.WithField("channels", len(nm.channels)).Info("Notification manager started")
	return nil
}

// SendNotification 发送通知
func (nm *NotificationManager) SendNotification(ctx context.Context, alert *AlertEvent) error {
	nm.mutex.RLock()
	defer nm.mutex.RUnlock()

	var errors []error

	for _, channel := range nm.channels {
		if !channel.IsEnabled() {
			continue
		}

		if err := channel.Send(ctx, alert); err != nil {
			nm.logger.WithError(err).WithField("channel", channel.GetName()).Error("Failed to send notification")
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("failed to send notifications: %v", errors)
	}

	return nil
}

// WebSocketNotificationChannel WebSocket通知渠道
type WebSocketNotificationChannel struct {
	wsManager *WebSocketManager
	logger    *logrus.Logger
}

func (wsnc *WebSocketNotificationChannel) Send(ctx context.Context, alert *AlertEvent) error {
	message := &WSMessage{
		Type: "alert_notification",
		Data: map[string]interface{}{
			"alert_id":    alert.ID,
			"type":        alert.Type,
			"severity":    alert.Severity,
			"host_id":     alert.HostID,
			"host_name":   alert.HostName,
			"metric_type": alert.MetricType,
			"value":       alert.Value,
			"threshold":   alert.Threshold,
			"message":     alert.Message,
			"timestamp":   alert.Timestamp,
			"status":      alert.Status,
		},
		Timestamp: time.Now(),
	}

	return wsnc.wsManager.BroadcastToAll(message)
}

func (wsnc *WebSocketNotificationChannel) GetName() string {
	return "websocket"
}

func (wsnc *WebSocketNotificationChannel) IsEnabled() bool {
	return wsnc.wsManager != nil
}

// LogNotificationChannel 日志通知渠道
type LogNotificationChannel struct {
	logger *logrus.Logger
}

func (lnc *LogNotificationChannel) Send(ctx context.Context, alert *AlertEvent) error {
	lnc.logger.WithFields(logrus.Fields{
		"alert_id":    alert.ID,
		"type":        alert.Type,
		"severity":    alert.Severity,
		"host_id":     alert.HostID,
		"host_name":   alert.HostName,
		"metric_type": alert.MetricType,
		"value":       alert.Value,
		"threshold":   alert.Threshold,
		"message":     alert.Message,
	}).Warn("🚨 ALERT")

	return nil
}

func (lnc *LogNotificationChannel) GetName() string {
	return "log"
}

func (lnc *LogNotificationChannel) IsEnabled() bool {
	return true
}

// NewAlertHistoryManager 创建告警历史管理器
func NewAlertHistoryManager(db *gorm.DB, logger *logrus.Logger) *AlertHistoryManager {
	return &AlertHistoryManager{
		db:     db,
		logger: logger,
	}
}

// SaveAlert 保存告警
func (ahm *AlertHistoryManager) SaveAlert(alert *AlertEvent) error {
	// TODO: 实现告警历史保存到数据库
	// 这里可以扩展为将告警保存到数据库表中
	ahm.logger.WithField("alert_id", alert.ID).Debug("Alert saved to history")
	return nil
}

// CleanupOldAlerts 清理旧告警
func (ahm *AlertHistoryManager) CleanupOldAlerts(cutoffDate time.Time) error {
	// TODO: 实现清理旧告警记录
	ahm.logger.WithField("cutoff_date", cutoffDate).Debug("Old alerts cleaned up")
	return nil
}
