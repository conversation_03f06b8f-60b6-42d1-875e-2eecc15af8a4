package auth

import (
	"testing"
	"time"

	"aiops-platform/internal/config"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestJWTManager(t *testing.T) {
	// 创建测试配置
	cfg := config.JWTConfig{
		Secret:          "test-secret-key-for-jwt-testing",
		AccessTokenTTL:  15 * time.Minute,
		RefreshTokenTTL: 7 * 24 * time.Hour,
		Issuer:          "aiops-platform-test",
	}

	// 创建黑名单
	blacklist := NewMemoryTokenBlacklist()

	// 创建JWT管理器
	jwtManager := NewJWTManager(cfg, blacklist)

	t.Run("GenerateTokenPair", func(t *testing.T) {
		userID := int64(1)
		username := "testuser"
		role := "admin"
		sessionID := "test-session-123"

		tokenPair, err := jwtManager.GenerateTokenPair(userID, username, role, sessionID)
		require.NoError(t, err)
		require.NotNil(t, tokenPair)

		assert.NotEmpty(t, tokenPair.AccessToken)
		assert.NotEmpty(t, tokenPair.RefreshToken)
		assert.Equal(t, "Bearer", tokenPair.TokenType)
		assert.Equal(t, int64(cfg.AccessTokenTTL.Seconds()), tokenPair.ExpiresIn)
	})

	t.Run("ValidateToken", func(t *testing.T) {
		userID := int64(1)
		username := "testuser"
		role := "admin"
		sessionID := "test-session-123"

		// 生成Token
		tokenPair, err := jwtManager.GenerateTokenPair(userID, username, role, sessionID)
		require.NoError(t, err)

		// 验证Access Token
		claims, err := jwtManager.ValidateToken(tokenPair.AccessToken)
		require.NoError(t, err)
		require.NotNil(t, claims)

		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, username, claims.Username)
		assert.Equal(t, role, claims.Role)
		assert.Equal(t, sessionID, claims.SessionID)
		assert.Equal(t, "access", claims.TokenType)

		// 验证Refresh Token
		refreshClaims, err := jwtManager.ValidateToken(tokenPair.RefreshToken)
		require.NoError(t, err)
		require.NotNil(t, refreshClaims)

		assert.Equal(t, userID, refreshClaims.UserID)
		assert.Equal(t, "refresh", refreshClaims.TokenType)
	})

	t.Run("RefreshToken", func(t *testing.T) {
		userID := int64(1)
		username := "testuser"
		role := "admin"
		sessionID := "test-session-123"

		// 生成初始Token
		tokenPair, err := jwtManager.GenerateTokenPair(userID, username, role, sessionID)
		require.NoError(t, err)

		// 刷新Token
		newTokenPair, err := jwtManager.RefreshToken(tokenPair.RefreshToken)
		require.NoError(t, err)
		require.NotNil(t, newTokenPair)

		assert.NotEmpty(t, newTokenPair.AccessToken)
		assert.NotEmpty(t, newTokenPair.RefreshToken)
		assert.NotEqual(t, tokenPair.AccessToken, newTokenPair.AccessToken)
		assert.NotEqual(t, tokenPair.RefreshToken, newTokenPair.RefreshToken)

		// 验证新Token
		claims, err := jwtManager.ValidateToken(newTokenPair.AccessToken)
		require.NoError(t, err)
		assert.Equal(t, userID, claims.UserID)
		assert.Equal(t, username, claims.Username)
	})

	t.Run("RevokeToken", func(t *testing.T) {
		userID := int64(1)
		username := "testuser"
		role := "admin"
		sessionID := "test-session-123"

		// 生成Token
		tokenPair, err := jwtManager.GenerateTokenPair(userID, username, role, sessionID)
		require.NoError(t, err)

		// 验证Token有效
		_, err = jwtManager.ValidateToken(tokenPair.AccessToken)
		require.NoError(t, err)

		// 撤销Token
		err = jwtManager.RevokeToken(tokenPair.AccessToken)
		require.NoError(t, err)

		// 验证Token已被撤销
		_, err = jwtManager.ValidateToken(tokenPair.AccessToken)
		require.Error(t, err)
		assert.Contains(t, err.Error(), "blacklisted")
	})

	t.Run("InvalidToken", func(t *testing.T) {
		// 测试无效Token
		_, err := jwtManager.ValidateToken("invalid-token")
		require.Error(t, err)

		// 测试空Token
		_, err = jwtManager.ValidateToken("")
		require.Error(t, err)

		// 测试错误格式的Token
		_, err = jwtManager.ValidateToken("Bearer invalid-token")
		require.Error(t, err)
	})
}

func TestMemoryTokenBlacklist(t *testing.T) {
	blacklist := NewMemoryTokenBlacklist()

	t.Run("AddAndCheck", func(t *testing.T) {
		tokenID := "test-token-123"
		expiry := time.Now().Add(1 * time.Hour)

		// 添加到黑名单
		err := blacklist.Add(tokenID, expiry)
		require.NoError(t, err)

		// 检查是否在黑名单中
		assert.True(t, blacklist.IsBlacklisted(tokenID))

		// 检查不存在的Token
		assert.False(t, blacklist.IsBlacklisted("non-existent-token"))
	})

	t.Run("ExpiredToken", func(t *testing.T) {
		tokenID := "expired-token-123"
		expiry := time.Now().Add(-1 * time.Hour) // 已过期

		// 添加过期Token
		err := blacklist.Add(tokenID, expiry)
		require.NoError(t, err)

		// 过期Token应该不在黑名单中
		assert.False(t, blacklist.IsBlacklisted(tokenID))
	})

	t.Run("Cleanup", func(t *testing.T) {
		// 添加一些Token
		blacklist.Add("token1", time.Now().Add(1*time.Hour))
		blacklist.Add("token2", time.Now().Add(-1*time.Hour)) // 过期
		blacklist.Add("token3", time.Now().Add(2*time.Hour))

		// 执行清理
		err := blacklist.Cleanup()
		require.NoError(t, err)

		// 验证有效Token仍在黑名单中
		assert.True(t, blacklist.IsBlacklisted("token1"))
		assert.True(t, blacklist.IsBlacklisted("token3"))

		// 过期Token应该被清理
		assert.False(t, blacklist.IsBlacklisted("token2"))
	})
}

func TestExtractTokenFromHeader(t *testing.T) {
	t.Run("ValidHeader", func(t *testing.T) {
		header := "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9"
		token, err := ExtractTokenFromHeader(header)
		require.NoError(t, err)
		assert.Equal(t, "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9", token)
	})

	t.Run("EmptyHeader", func(t *testing.T) {
		_, err := ExtractTokenFromHeader("")
		require.Error(t, err)
		assert.Contains(t, err.Error(), "empty")
	})

	t.Run("InvalidFormat", func(t *testing.T) {
		_, err := ExtractTokenFromHeader("InvalidFormat token")
		require.Error(t, err)
		assert.Contains(t, err.Error(), "invalid")

		_, err = ExtractTokenFromHeader("Bearer")
		require.Error(t, err)
	})
}

func TestUserContext(t *testing.T) {
	userCtx := &UserContext{
		UserID:    1,
		Username:  "testuser",
		Role:      "admin",
		SessionID: "session-123",
	}

	t.Run("HasRole", func(t *testing.T) {
		assert.True(t, userCtx.HasRole("admin"))
		assert.False(t, userCtx.HasRole("viewer"))
	})

	t.Run("HasAnyRole", func(t *testing.T) {
		assert.True(t, userCtx.HasAnyRole("admin", "operator"))
		assert.True(t, userCtx.HasAnyRole("viewer", "admin"))
		assert.False(t, userCtx.HasAnyRole("viewer", "operator"))
	})

	t.Run("IsAdmin", func(t *testing.T) {
		assert.True(t, userCtx.IsAdmin())

		userCtx.Role = "viewer"
		assert.False(t, userCtx.IsAdmin())
	})

	t.Run("IsSuperAdmin", func(t *testing.T) {
		userCtx.Role = "super_admin"
		assert.True(t, userCtx.IsSuperAdmin())

		userCtx.Role = "admin"
		assert.False(t, userCtx.IsSuperAdmin())
	})

	t.Run("CanManageUser", func(t *testing.T) {
		userCtx.Role = "super_admin"
		assert.True(t, userCtx.CanManageUser("admin"))
		assert.True(t, userCtx.CanManageUser("viewer"))

		userCtx.Role = "admin"
		assert.False(t, userCtx.CanManageUser("super_admin"))
		assert.False(t, userCtx.CanManageUser("admin"))
		assert.True(t, userCtx.CanManageUser("viewer"))

		userCtx.Role = "viewer"
		assert.False(t, userCtx.CanManageUser("admin"))
	})

	t.Run("GetRoleLevel", func(t *testing.T) {
		userCtx.Role = "super_admin"
		assert.Equal(t, 4, userCtx.GetRoleLevel())

		userCtx.Role = "admin"
		assert.Equal(t, 3, userCtx.GetRoleLevel())

		userCtx.Role = "operator"
		assert.Equal(t, 2, userCtx.GetRoleLevel())

		userCtx.Role = "viewer"
		assert.Equal(t, 1, userCtx.GetRoleLevel())

		userCtx.Role = "unknown"
		assert.Equal(t, 0, userCtx.GetRoleLevel())
	})
}
