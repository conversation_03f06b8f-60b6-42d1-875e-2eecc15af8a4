package ai

import (
	"encoding/json"
	"time"
)

// UserProfileRecord 用户画像数据库记录
type UserProfileRecord struct {
	ID                    int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID                int64     `json:"user_id" gorm:"uniqueIndex"`
	InteractionCount      int64     `json:"interaction_count"`
	LearningProgress      float64   `json:"learning_progress"`
	TechnicalLevel        float64   `json:"technical_level"`
	LearningSpeed         float64   `json:"learning_speed"`
	LanguagePreference    string    `json:"language_preference" gorm:"size:10"`
	
	// JSON字段
	PreferredModes        string    `json:"preferred_modes" gorm:"type:text"`
	ResponseStyle         string    `json:"response_style" gorm:"type:text"`
	CommunicationStyle    string    `json:"communication_style" gorm:"type:text"`
	BehaviorPatterns      string    `json:"behavior_patterns" gorm:"type:text"`
	UsagePatterns         string    `json:"usage_patterns" gorm:"type:text"`
	TaskPreferences       string    `json:"task_preferences" gorm:"type:text"`
	DomainExpertise       string    `json:"domain_expertise" gorm:"type:text"`
	CustomSettings        string    `json:"custom_settings" gorm:"type:text"`
	AccessibilityNeeds    string    `json:"accessibility_needs" gorm:"type:text"`
	LearningState         string    `json:"learning_state" gorm:"type:text"`
	AdaptationHistory     string    `json:"adaptation_history" gorm:"type:text"`
	PrivacySettings       string    `json:"privacy_settings" gorm:"type:text"`
	
	CreatedAt             time.Time `json:"created_at"`
	UpdatedAt             time.Time `json:"updated_at"`
}

// TableName 指定表名
func (UserProfileRecord) TableName() string {
	return "user_profiles"
}

// ToUserProfile 转换为用户画像对象
func (record *UserProfileRecord) ToUserProfile() (*UserProfile, error) {
	profile := &UserProfile{
		UserID:           record.UserID,
		CreatedAt:        record.CreatedAt,
		LastUpdated:      record.UpdatedAt,
		InteractionCount: record.InteractionCount,
		LearningProgress: record.LearningProgress,
		TechnicalLevel:   record.TechnicalLevel,
		LearningSpeed:    record.LearningSpeed,
		LanguagePreference: record.LanguagePreference,
	}

	// 解析JSON字段
	if err := json.Unmarshal([]byte(record.PreferredModes), &profile.PreferredModes); err != nil {
		profile.PreferredModes = make(map[InteractionMode]float64)
	}

	if err := json.Unmarshal([]byte(record.ResponseStyle), &profile.ResponseStyle); err != nil {
		profile.ResponseStyle = ResponseStylePreference{}
	}

	if err := json.Unmarshal([]byte(record.CommunicationStyle), &profile.CommunicationStyle); err != nil {
		profile.CommunicationStyle = CommunicationStylePreference{}
	}

	if err := json.Unmarshal([]byte(record.BehaviorPatterns), &profile.BehaviorPatterns); err != nil {
		profile.BehaviorPatterns = make(map[string]*BehaviorPattern)
	}

	if err := json.Unmarshal([]byte(record.UsagePatterns), &profile.UsagePatterns); err != nil {
		profile.UsagePatterns = &UsagePattern{}
	}

	if err := json.Unmarshal([]byte(record.TaskPreferences), &profile.TaskPreferences); err != nil {
		profile.TaskPreferences = make(map[string]*TaskPreference)
	}

	if err := json.Unmarshal([]byte(record.DomainExpertise), &profile.DomainExpertise); err != nil {
		profile.DomainExpertise = make(map[string]float64)
	}

	if err := json.Unmarshal([]byte(record.CustomSettings), &profile.CustomSettings); err != nil {
		profile.CustomSettings = make(map[string]interface{})
	}

	if err := json.Unmarshal([]byte(record.AccessibilityNeeds), &profile.AccessibilityNeeds); err != nil {
		profile.AccessibilityNeeds = []string{}
	}

	if err := json.Unmarshal([]byte(record.LearningState), &profile.LearningState); err != nil {
		profile.LearningState = &LearningState{}
	}

	if err := json.Unmarshal([]byte(record.AdaptationHistory), &profile.AdaptationHistory); err != nil {
		profile.AdaptationHistory = []AdaptationRecord{}
	}

	if err := json.Unmarshal([]byte(record.PrivacySettings), &profile.PrivacySettings); err != nil {
		profile.PrivacySettings = &PrivacySettings{}
	}

	return profile, nil
}

// FromUserProfile 从用户画像对象创建数据库记录
func (record *UserProfileRecord) FromUserProfile(profile *UserProfile) error {
	record.UserID = profile.UserID
	record.InteractionCount = profile.InteractionCount
	record.LearningProgress = profile.LearningProgress
	record.TechnicalLevel = profile.TechnicalLevel
	record.LearningSpeed = profile.LearningSpeed
	record.LanguagePreference = profile.LanguagePreference
	record.CreatedAt = profile.CreatedAt
	record.UpdatedAt = profile.LastUpdated

	// 序列化JSON字段
	if data, err := json.Marshal(profile.PreferredModes); err == nil {
		record.PreferredModes = string(data)
	}

	if data, err := json.Marshal(profile.ResponseStyle); err == nil {
		record.ResponseStyle = string(data)
	}

	if data, err := json.Marshal(profile.CommunicationStyle); err == nil {
		record.CommunicationStyle = string(data)
	}

	if data, err := json.Marshal(profile.BehaviorPatterns); err == nil {
		record.BehaviorPatterns = string(data)
	}

	if data, err := json.Marshal(profile.UsagePatterns); err == nil {
		record.UsagePatterns = string(data)
	}

	if data, err := json.Marshal(profile.TaskPreferences); err == nil {
		record.TaskPreferences = string(data)
	}

	if data, err := json.Marshal(profile.DomainExpertise); err == nil {
		record.DomainExpertise = string(data)
	}

	if data, err := json.Marshal(profile.CustomSettings); err == nil {
		record.CustomSettings = string(data)
	}

	if data, err := json.Marshal(profile.AccessibilityNeeds); err == nil {
		record.AccessibilityNeeds = string(data)
	}

	if data, err := json.Marshal(profile.LearningState); err == nil {
		record.LearningState = string(data)
	}

	if data, err := json.Marshal(profile.AdaptationHistory); err == nil {
		record.AdaptationHistory = string(data)
	}

	if data, err := json.Marshal(profile.PrivacySettings); err == nil {
		record.PrivacySettings = string(data)
	}

	return nil
}

// BehaviorRecord 行为记录
type BehaviorRecord struct {
	ID              int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID          int64     `json:"user_id" gorm:"index"`
	InteractionID   string    `json:"interaction_id" gorm:"size:100"`
	BehaviorType    string    `json:"behavior_type" gorm:"size:50"`
	PatternName     string    `json:"pattern_name" gorm:"size:100"`
	Frequency       float64   `json:"frequency"`
	Confidence      float64   `json:"confidence"`
	Context         string    `json:"context" gorm:"type:text"`
	Triggers        string    `json:"triggers" gorm:"type:text"`
	Outcomes        string    `json:"outcomes" gorm:"type:text"`
	ObservedAt      time.Time `json:"observed_at"`
	CreatedAt       time.Time `json:"created_at"`
}

// TableName 指定表名
func (BehaviorRecord) TableName() string {
	return "behavior_records"
}

// LearningRecord 学习记录
type LearningRecord struct {
	ID                int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID            int64     `json:"user_id" gorm:"index"`
	LearningPhase     string    `json:"learning_phase" gorm:"size:20"`
	Progress          float64   `json:"progress"`
	LearningGoals     string    `json:"learning_goals" gorm:"type:text"`
	Achievements      string    `json:"achievements" gorm:"type:text"`
	Challenges        string    `json:"challenges" gorm:"type:text"`
	LearningSpeed     float64   `json:"learning_speed"`
	EfficiencyScore   float64   `json:"efficiency_score"`
	LastUpdate        time.Time `json:"last_update"`
	CreatedAt         time.Time `json:"created_at"`
}

// TableName 指定表名
func (LearningRecord) TableName() string {
	return "learning_records"
}

// AdaptationRecord 适应记录（数据库版本）
type AdaptationRecordDB struct {
	ID             int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID         int64     `json:"user_id" gorm:"index"`
	AdaptationID   string    `json:"adaptation_id" gorm:"size:100"`
	AdaptationType string    `json:"adaptation_type" gorm:"size:50"`
	BeforeState    string    `json:"before_state" gorm:"type:text"`
	AfterState     string    `json:"after_state" gorm:"type:text"`
	Reason         string    `json:"reason" gorm:"type:text"`
	Confidence     float64   `json:"confidence"`
	Success        bool      `json:"success"`
	AppliedAt      time.Time `json:"applied_at"`
	CreatedAt      time.Time `json:"created_at"`
}

// TableName 指定表名
func (AdaptationRecordDB) TableName() string {
	return "adaptation_records"
}

// PersonalizationMetricsRecord 个性化指标记录
type PersonalizationMetricsRecord struct {
	ID                    int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	Date                  time.Time `json:"date" gorm:"index"`
	TotalUsers            int64     `json:"total_users"`
	ActiveLearners        int64     `json:"active_learners"`
	AverageLearningRate   float64   `json:"average_learning_rate"`
	AdaptationSuccess     float64   `json:"adaptation_success"`
	UserSatisfaction      float64   `json:"user_satisfaction"`
	LearningEfficiency    float64   `json:"learning_efficiency"`
	PhaseDistribution     string    `json:"phase_distribution" gorm:"type:text"`
	BehaviorPatternCount  int64     `json:"behavior_pattern_count"`
	AdaptationCount       int64     `json:"adaptation_count"`
	PrivacyCompliance     float64   `json:"privacy_compliance"`
	CreatedAt             time.Time `json:"created_at"`
}

// TableName 指定表名
func (PersonalizationMetricsRecord) TableName() string {
	return "personalization_metrics"
}

// ToLearningMetrics 转换为学习指标
func (record *PersonalizationMetricsRecord) ToLearningMetrics() (*LearningMetrics, error) {
	var phaseDistribution map[LearningPhase]int64
	if err := json.Unmarshal([]byte(record.PhaseDistribution), &phaseDistribution); err != nil {
		phaseDistribution = make(map[LearningPhase]int64)
	}

	return &LearningMetrics{
		TotalUsers:          record.TotalUsers,
		ActiveLearners:      record.ActiveLearners,
		AverageLearningRate: record.AverageLearningRate,
		AdaptationSuccess:   record.AdaptationSuccess,
		UserSatisfaction:    record.UserSatisfaction,
		LearningEfficiency:  record.LearningEfficiency,
		PhaseDistribution:   phaseDistribution,
		LastUpdated:         record.CreatedAt,
	}, nil
}

// FromLearningMetrics 从学习指标创建数据库记录
func (record *PersonalizationMetricsRecord) FromLearningMetrics(metrics *LearningMetrics) error {
	record.Date = time.Now().Truncate(24 * time.Hour)
	record.TotalUsers = metrics.TotalUsers
	record.ActiveLearners = metrics.ActiveLearners
	record.AverageLearningRate = metrics.AverageLearningRate
	record.AdaptationSuccess = metrics.AdaptationSuccess
	record.UserSatisfaction = metrics.UserSatisfaction
	record.LearningEfficiency = metrics.LearningEfficiency
	record.CreatedAt = time.Now()

	if data, err := json.Marshal(metrics.PhaseDistribution); err == nil {
		record.PhaseDistribution = string(data)
	}

	return nil
}

// UserInteractionRecord 用户交互记录
type UserInteractionRecord struct {
	ID               int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID           int64     `json:"user_id" gorm:"index"`
	SessionID        string    `json:"session_id" gorm:"size:100"`
	InteractionID    string    `json:"interaction_id" gorm:"uniqueIndex;size:100"`
	InteractionType  string    `json:"interaction_type" gorm:"size:50"`
	InteractionMode  string    `json:"interaction_mode" gorm:"size:20"`
	Content          string    `json:"content" gorm:"type:text"`
	Success          bool      `json:"success"`
	Duration         int64     `json:"duration"` // 毫秒
	Context          string    `json:"context" gorm:"type:text"`
	UserFeedback     string    `json:"user_feedback" gorm:"type:text"`
	LearningValue    float64   `json:"learning_value"`
	CreatedAt        time.Time `json:"created_at"`
}

// TableName 指定表名
func (UserInteractionRecord) TableName() string {
	return "user_interactions"
}

// ToInteractionData 转换为交互数据
func (record *UserInteractionRecord) ToInteractionData() (*InteractionData, error) {
	var context map[string]interface{}
	if err := json.Unmarshal([]byte(record.Context), &context); err != nil {
		context = make(map[string]interface{})
	}

	var feedback *UserFeedback
	if record.UserFeedback != "" {
		if err := json.Unmarshal([]byte(record.UserFeedback), &feedback); err != nil {
			feedback = nil
		}
	}

	return &InteractionData{
		ID:        record.InteractionID,
		Type:      record.InteractionType,
		Content:   record.Content,
		Mode:      InteractionMode(record.InteractionMode),
		Timestamp: record.CreatedAt,
		Success:   record.Success,
		Duration:  time.Duration(record.Duration) * time.Millisecond,
		Context:   context,
		Feedback:  feedback,
	}, nil
}

// FromInteractionData 从交互数据创建数据库记录
func (record *UserInteractionRecord) FromInteractionData(userID int64, sessionID string, data *InteractionData) error {
	record.UserID = userID
	record.SessionID = sessionID
	record.InteractionID = data.ID
	record.InteractionType = data.Type
	record.InteractionMode = string(data.Mode)
	record.Content = data.Content
	record.Success = data.Success
	record.Duration = int64(data.Duration / time.Millisecond)
	record.CreatedAt = data.Timestamp

	if contextData, err := json.Marshal(data.Context); err == nil {
		record.Context = string(contextData)
	}

	if data.Feedback != nil {
		if feedbackData, err := json.Marshal(data.Feedback); err == nil {
			record.UserFeedback = string(feedbackData)
		}
	}

	return nil
}

// PreferenceChangeRecord 偏好变化记录
type PreferenceChangeRecord struct {
	ID              int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID          int64     `json:"user_id" gorm:"index"`
	ChangeType      string    `json:"change_type" gorm:"size:50"`
	PreferenceKey   string    `json:"preference_key" gorm:"size:100"`
	OldValue        string    `json:"old_value" gorm:"type:text"`
	NewValue        string    `json:"new_value" gorm:"type:text"`
	ChangeReason    string    `json:"change_reason" gorm:"type:text"`
	Confidence      float64   `json:"confidence"`
	AutoGenerated   bool      `json:"auto_generated"`
	UserConfirmed   bool      `json:"user_confirmed"`
	CreatedAt       time.Time `json:"created_at"`
}

// TableName 指定表名
func (PreferenceChangeRecord) TableName() string {
	return "preference_changes"
}

// LearningGoalRecord 学习目标记录
type LearningGoalRecord struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID      int64     `json:"user_id" gorm:"index"`
	GoalID      string    `json:"goal_id" gorm:"size:100"`
	Description string    `json:"description" gorm:"type:text"`
	Progress    float64   `json:"progress"`
	Priority    int       `json:"priority"`
	Status      string    `json:"status" gorm:"size:20"`
	Deadline    time.Time `json:"deadline"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
}

// TableName 指定表名
func (LearningGoalRecord) TableName() string {
	return "learning_goals"
}

// AchievementRecord 成就记录
type AchievementRecord struct {
	ID          int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID      int64     `json:"user_id" gorm:"index"`
	AchievementID string  `json:"achievement_id" gorm:"size:100"`
	Name        string    `json:"name" gorm:"size:200"`
	Description string    `json:"description" gorm:"type:text"`
	Category    string    `json:"category" gorm:"size:50"`
	Points      int       `json:"points"`
	Rarity      string    `json:"rarity" gorm:"size:20"`
	UnlockedAt  time.Time `json:"unlocked_at"`
	CreatedAt   time.Time `json:"created_at"`
}

// TableName 指定表名
func (AchievementRecord) TableName() string {
	return "achievements"
}

// PrivacyAuditRecord 隐私审计记录
type PrivacyAuditRecord struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID        int64     `json:"user_id" gorm:"index"`
	ActionType    string    `json:"action_type" gorm:"size:50"`
	DataType      string    `json:"data_type" gorm:"size:50"`
	DataAccessed  string    `json:"data_accessed" gorm:"type:text"`
	Purpose       string    `json:"purpose" gorm:"type:text"`
	Authorized    bool      `json:"authorized"`
	ConsentGiven  bool      `json:"consent_given"`
	IPAddress     string    `json:"ip_address" gorm:"size:45"`
	UserAgent     string    `json:"user_agent" gorm:"type:text"`
	CreatedAt     time.Time `json:"created_at"`
}

// TableName 指定表名
func (PrivacyAuditRecord) TableName() string {
	return "privacy_audits"
}

// PersonalizationConfigRecord 个性化配置记录
type PersonalizationConfigRecord struct {
	ID                         int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID                     int64     `json:"user_id" gorm:"uniqueIndex"`
	EnablePersonalization      bool      `json:"enable_personalization"`
	LearningRate              float64   `json:"learning_rate"`
	AdaptationThreshold       float64   `json:"adaptation_threshold"`
	MinInteractionsForLearning int       `json:"min_interactions_for_learning"`
	PrivacyLevel              string    `json:"privacy_level" gorm:"size:20"`
	EnableBehaviorTracking    bool      `json:"enable_behavior_tracking"`
	EnablePreferenceSync      bool      `json:"enable_preference_sync"`
	DataRetentionDays         int       `json:"data_retention_days"`
	CustomSettings            string    `json:"custom_settings" gorm:"type:text"`
	CreatedAt                 time.Time `json:"created_at"`
	UpdatedAt                 time.Time `json:"updated_at"`
}

// TableName 指定表名
func (PersonalizationConfigRecord) TableName() string {
	return "personalization_configs"
}

// UserSegmentRecord 用户分群记录
type UserSegmentRecord struct {
	ID              int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID          int64     `json:"user_id" gorm:"index"`
	SegmentName     string    `json:"segment_name" gorm:"size:100"`
	SegmentType     string    `json:"segment_type" gorm:"size:50"`
	Characteristics string    `json:"characteristics" gorm:"type:text"`
	Confidence      float64   `json:"confidence"`
	AssignedAt      time.Time `json:"assigned_at"`
	ValidUntil      time.Time `json:"valid_until"`
	CreatedAt       time.Time `json:"created_at"`
}

// TableName 指定表名
func (UserSegmentRecord) TableName() string {
	return "user_segments"
}

// LearningPathRecord 学习路径记录
type LearningPathRecord struct {
	ID            int64     `json:"id" gorm:"primaryKey;autoIncrement"`
	UserID        int64     `json:"user_id" gorm:"index"`
	PathID        string    `json:"path_id" gorm:"size:100"`
	PathName      string    `json:"path_name" gorm:"size:200"`
	Description   string    `json:"description" gorm:"type:text"`
	Steps         string    `json:"steps" gorm:"type:text"`
	CurrentStep   int       `json:"current_step"`
	Progress      float64   `json:"progress"`
	Difficulty    string    `json:"difficulty" gorm:"size:20"`
	EstimatedTime int       `json:"estimated_time"` // 分钟
	StartedAt     time.Time `json:"started_at"`
	CompletedAt   *time.Time `json:"completed_at"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
}

// TableName 指定表名
func (LearningPathRecord) TableName() string {
	return "learning_paths"
}
