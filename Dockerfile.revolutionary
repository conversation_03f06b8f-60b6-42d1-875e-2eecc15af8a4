# 革命性AI运维平台 Dockerfile
# 多阶段构建，优化镜像大小和安全性

# 第一阶段：构建阶段
FROM golang:1.21-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装必要的构建工具
RUN apk add --no-cache git ca-certificates tzdata

# 复制go mod文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build \
    -ldflags='-w -s -extldflags "-static"' \
    -a -installsuffix cgo \
    -o revolutionary-aiops \
    ./cmd/revolutionary_aiops

# 第二阶段：运行阶段
FROM alpine:3.18

# 安装运行时依赖
RUN apk --no-cache add \
    ca-certificates \
    tzdata \
    curl \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S aiops && \
    adduser -u 1001 -S aiops -G aiops

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/revolutionary-aiops .

# 复制配置文件和静态资源
COPY --chown=aiops:aiops config/ ./config/
COPY --chown=aiops:aiops web/ ./web/
COPY --chown=aiops:aiops docs/ ./docs/

# 创建日志目录
RUN mkdir -p /app/logs && chown -R aiops:aiops /app/logs

# 设置权限
RUN chmod +x revolutionary-aiops && \
    chown -R aiops:aiops /app

# 切换到非root用户
USER aiops

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# 设置环境变量
ENV GIN_MODE=release
ENV LOG_LEVEL=info

# 启动命令
ENTRYPOINT ["./revolutionary-aiops"]

# 元数据标签
LABEL maintainer="AI Ops Team <<EMAIL>>"
LABEL version="2.0.0"
LABEL description="Revolutionary AI Operations Management Platform"
LABEL org.opencontainers.image.title="Revolutionary AI Ops"
LABEL org.opencontainers.image.description="Next-generation AI-driven operations management platform"
LABEL org.opencontainers.image.version="2.0.0"
LABEL org.opencontainers.image.vendor="AI Ops Company"
LABEL org.opencontainers.image.licenses="MIT"
LABEL org.opencontainers.image.source="https://github.com/company/revolutionary-aiops"
