# AI对话运维管理平台

一个基于Go语言开发的智能运维管理平台，通过AI对话的方式简化IT基础设施管理。

## 🚀 项目特性

### 核心功能
- **🤖 AI智能对话**: 通过自然语言与DeepSeek AI交互，执行运维操作
- **🖥️ 主机管理**: 统一管理多台服务器，支持SSH连接和命令执行
- **⚠️ 监控告警**: 实时监控系统状态，智能告警通知
- **📊 统计报表**: 丰富的数据可视化和统计分析
- **👥 用户权限**: 基于角色的访问控制(RBAC)
- **🔒 安全防护**: 企业级安全架构，数据加密存储

### 技术特性
- **高性能**: Go语言开发，支持高并发处理
- **轻量级**: SQLite数据库，部署简单
- **可扩展**: 模块化架构，易于扩展
- **云原生**: Docker容器化部署
- **实时通信**: WebSocket支持实时消息推送

## 🏗️ 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端界面      │    │   API网关       │    │   DeepSeek AI   │
│   (HTML/JS)     │◄──►│   (Gin)         │◄──►│   (对话引擎)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   业务服务层    │
                       │   (Services)    │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   数据访问层    │
                       │   (GORM)        │
                       └─────────────────┘
                                │
                       ┌─────────────────┐
                       │   SQLite数据库  │
                       │   (持久化存储)  │
                       └─────────────────┘
```

## 📦 项目结构

```
aiops-platform/
├── cmd/                    # 应用入口
│   ├── server/            # 主服务器
│   └── migrate/           # 数据库迁移工具
├── internal/              # 内部包
│   ├── auth/             # 认证模块
│   ├── config/           # 配置管理
│   ├── database/         # 数据库操作
│   ├── handler/          # HTTP处理器
│   ├── logger/           # 日志模块
│   ├── middleware/       # 中间件
│   ├── model/            # 数据模型
│   ├── router/           # 路由配置
│   └── service/          # 业务服务
├── web/                   # 前端资源
│   ├── static/           # 静态文件
│   └── templates/        # HTML模板
├── configs/               # 配置文件
├── docs/                  # 项目文档
├── scripts/               # 部署脚本
├── docker-compose.yml     # Docker编排
├── Dockerfile            # Docker镜像
├── Makefile              # 构建脚本
└── go.mod                # Go模块
```

## 🚀 快速开始

### 环境要求
- Go 1.21+
- Docker & Docker Compose (可选)
- DeepSeek API密钥

### 本地开发

1. **克隆项目**
```bash
git clone <repository-url>
cd aiops-platform
```

2. **安装依赖**
```bash
make deps
```

3. **配置环境**
```bash
make init-env
# 编辑 .env 文件，配置必要参数
```

4. **初始化数据库**
```bash
make migrate-up
```

5. **启动开发服务器**
```bash
make dev
```

6. **访问应用**
- 主应用: http://localhost:8080
- API文档: http://localhost:8080/swagger/index.html

### Docker部署

1. **配置环境变量**
```bash
cp .env.example .env
# 编辑 .env 文件
```

2. **启动服务**
```bash
make docker-compose-up
```

3. **查看日志**
```bash
make docker-compose-logs
```

## 🔧 配置说明

### 必要配置
```bash
# DeepSeek API配置
DEEPSEEK_API_KEY=sk-your-api-key-here

# 安全配置
JWT_SECRET=your-jwt-secret
ENCRYPTION_KEY=your-32-byte-encryption-key

# 数据库配置
DB_PATH=./data/aiops.db
```

### 可选配置
```bash
# Redis缓存
REDIS_ENABLED=true
REDIS_HOST=localhost
REDIS_PORT=6379

# 监控配置
METRICS_ENABLED=true
METRICS_PORT=9090

# 日志配置
LOG_LEVEL=info
LOG_FILE=./logs/aiops.log
```

## 📖 使用指南

### 基础操作

1. **用户管理**
   - 创建用户账号
   - 分配角色权限
   - 管理用户状态

2. **主机管理**
   - 添加服务器
   - 配置SSH连接
   - 执行远程命令

3. **AI对话**
   - 自然语言交互
   - 智能命令解析
   - 自动化操作执行

4. **监控告警**
   - 设置监控规则
   - 接收告警通知
   - 处理告警事件

### API接口

详细的API文档请访问: `/swagger/index.html`

主要接口包括:
- `/api/v1/auth/*` - 认证相关
- `/api/v1/hosts/*` - 主机管理
- `/api/v1/alerts/*` - 告警管理
- `/api/v1/chat/*` - 对话管理
- `/api/v1/stats/*` - 统计数据

## 🛠️ 开发指南

### 代码规范
```bash
# 代码格式化
make fmt

# 代码检查
make lint

# 运行测试
make test
```

### 构建部署
```bash
# 构建应用
make build

# 构建Docker镜像
make docker-build

# 打包发布
make package
```

## 📊 监控运维

### 健康检查
```bash
# 应用健康检查
curl http://localhost:8080/health

# 系统状态检查
make health-check
```

### 日志管理
- 应用日志: `./logs/aiops.log`
- 访问日志: 通过中间件记录
- 错误日志: 自动记录到日志文件

### 性能监控
- Prometheus指标: `http://localhost:9090/metrics`
- 系统统计: `/api/v1/stats/overview`

## 🔒 安全特性

- **认证授权**: JWT Token + RBAC权限控制
- **数据加密**: 敏感数据AES加密存储
- **传输安全**: HTTPS + WSS加密传输
- **访问控制**: IP白名单 + 限流保护
- **审计日志**: 完整的操作审计记录

## 🤝 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持与反馈

- 📧 邮箱: <EMAIL>
- 🐛 问题反馈: [GitHub Issues](https://github.com/your-org/aiops-platform/issues)
- 📖 文档: [项目文档](./docs/)

## 🎯 路线图

- [x] 基础框架搭建
- [x] 用户认证系统
- [x] 主机管理功能
- [x] AI对话集成
- [ ] 监控告警系统
- [ ] 统计报表功能
- [ ] 移动端适配
- [ ] 插件系统
- [ ] 集群部署支持

---

**AI对话运维管理平台** - 让运维更智能，让管理更简单！
