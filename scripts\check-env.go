package main

import (
	"fmt"
	"os"
	"strings"
)

func main() {
	fmt.Println("=== AI运维管理平台环境变量检查 ===")
	fmt.Println()

	// 检查必需的环境变量
	requiredVars := map[string]string{
		"AIOPS_ENCRYPTION_KEY": "32字节加密密钥",
		"AIOPS_JWT_SECRET":     "JWT签名密钥",
		"AIOPS_DEEPSEEK_API_KEY": "DeepSeek API密钥",
	}

	allSet := true
	
	for varName, description := range requiredVars {
		value := os.Getenv(varName)
		if value == "" {
			fmt.Printf("❌ %s: 未设置 (%s)\n", varName, description)
			allSet = false
		} else {
			// 检查特定要求
			switch varName {
			case "AIOPS_ENCRYPTION_KEY":
				if len(value) != 32 {
					fmt.Printf("❌ %s: 长度错误 (当前: %d字节, 需要: 32字节)\n", varName, len(value))
					allSet = false
				} else {
					fmt.Printf("✅ %s: 已正确设置 (32字节)\n", varName)
				}
			case "AIOPS_JWT_SECRET":
				if len(value) < 32 {
					fmt.Printf("⚠️  %s: 长度较短 (当前: %d字符, 建议: ≥32字符)\n", varName, len(value))
				} else {
					fmt.Printf("✅ %s: 已正确设置 (%d字符)\n", varName, len(value))
				}
			case "AIOPS_DEEPSEEK_API_KEY":
				if strings.Contains(value, "your-") || strings.Contains(value, "here") {
					fmt.Printf("❌ %s: 使用默认值，请设置真实的API密钥\n", varName)
					allSet = false
				} else {
					fmt.Printf("✅ %s: 已设置\n", varName)
				}
			}
		}
	}

	fmt.Println()
	
	if allSet {
		fmt.Println("🎉 所有环境变量都已正确设置！")
		fmt.Println("现在可以运行: go run cmd/server/main.go")
	} else {
		fmt.Println("❌ 部分环境变量未正确设置")
		fmt.Println()
		fmt.Println("设置方法:")
		fmt.Println("Windows: 运行 scripts/setup-env.ps1")
		fmt.Println("Linux/Mac: 运行 bash scripts/setup-env.sh")
		fmt.Println()
		fmt.Println("或手动设置:")
		fmt.Println("Windows PowerShell:")
		fmt.Println(`[Environment]::SetEnvironmentVariable("AIOPS_ENCRYPTION_KEY", "your-32-byte-key-here", "User")`)
		fmt.Println()
		fmt.Println("Linux/Mac Bash:")
		fmt.Println(`export AIOPS_ENCRYPTION_KEY="your-32-byte-key-here"`)
	}
}
