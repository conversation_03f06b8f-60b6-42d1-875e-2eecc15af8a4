package visualization

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// DashboardEngine 仪表盘引擎
type DashboardEngine struct {
	config        *DashboardConfig
	logger        *logrus.Logger
	chartEngine   *ChartEngine
	dashboards    map[string]*Dashboard
	widgets       map[string]*Widget
	layouts       map[string]*Layout
	dataProviders map[string]DataProvider
	eventBus      *EventBus
	mutex         sync.RWMutex
	isRunning     bool
}

// DashboardConfig 仪表盘配置
type DashboardConfig struct {
	EnableRealtime    bool          `json:"enable_realtime"`
	RefreshInterval   time.Duration `json:"refresh_interval"`
	MaxDashboards     int           `json:"max_dashboards"`
	MaxWidgetsPerDash int           `json:"max_widgets_per_dash"`
	EnableInteraction bool          `json:"enable_interaction"`
	EnableExport      bool          `json:"enable_export"`
	CacheEnabled      bool          `json:"cache_enabled"`
	CacheExpiration   time.Duration `json:"cache_expiration"`
}

// Dashboard 仪表盘
type Dashboard struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Type        string                 `json:"type"`
	Layout      *Layout                `json:"layout"`
	Widgets     []*Widget              `json:"widgets"`
	Filters     []*Filter              `json:"filters"`
	Settings    *DashboardSettings     `json:"settings"`
	Metadata    map[string]interface{} `json:"metadata"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	CreatedBy   string                 `json:"created_by"`
	IsPublic    bool                   `json:"is_public"`
	Tags        []string               `json:"tags"`
}

// Layout 布局
type Layout struct {
	Type        string       `json:"type"` // grid, flex, absolute, masonry
	Columns     int          `json:"columns"`
	Rows        int          `json:"rows"`
	Gap         int          `json:"gap"`
	Padding     *Padding     `json:"padding"`
	Responsive  bool         `json:"responsive"`
	Breakpoints *Breakpoints `json:"breakpoints"`
}

// Widget 小部件
type Widget struct {
	ID           string                 `json:"id"`
	Type         string                 `json:"type"` // chart, metric, table, text, image, iframe
	Title        string                 `json:"title"`
	Description  string                 `json:"description"`
	Position     *Position              `json:"position"`
	Size         *Size                  `json:"size"`
	Data         interface{}            `json:"data"`
	Config       *WidgetConfig          `json:"config"`
	Style        *WidgetStyle           `json:"style"`
	Interactions []*Interaction         `json:"interactions"`
	DataSource   *DataSource            `json:"data_source"`
	RefreshRate  time.Duration          `json:"refresh_rate"`
	Metadata     map[string]interface{} `json:"metadata"`
	CreatedAt    time.Time              `json:"created_at"`
	UpdatedAt    time.Time              `json:"updated_at"`
	IsVisible    bool                   `json:"is_visible"`
}

// Position 位置
type Position struct {
	X      int `json:"x"`
	Y      int `json:"y"`
	ZIndex int `json:"z_index"`
}

// Size 大小
type Size struct {
	Width     int `json:"width"`
	Height    int `json:"height"`
	MinWidth  int `json:"min_width"`
	MinHeight int `json:"min_height"`
	MaxWidth  int `json:"max_width"`
	MaxHeight int `json:"max_height"`
}

// WidgetConfig 小部件配置
type WidgetConfig struct {
	ChartType     string                 `json:"chart_type,omitempty"`
	ChartOptions  *ChartOptions          `json:"chart_options,omitempty"`
	TableOptions  *TableOptions          `json:"table_options,omitempty"`
	MetricOptions *MetricOptions         `json:"metric_options,omitempty"`
	TextOptions   *TextOptions           `json:"text_options,omitempty"`
	CustomConfig  map[string]interface{} `json:"custom_config,omitempty"`
}

// WidgetStyle 小部件样式
type WidgetStyle struct {
	BackgroundColor string                 `json:"background_color,omitempty"`
	BorderColor     string                 `json:"border_color,omitempty"`
	BorderWidth     int                    `json:"border_width,omitempty"`
	BorderRadius    int                    `json:"border_radius,omitempty"`
	Shadow          string                 `json:"shadow,omitempty"`
	Opacity         float64                `json:"opacity,omitempty"`
	CustomCSS       map[string]interface{} `json:"custom_css,omitempty"`
}

// Interaction 交互
type Interaction struct {
	Type    string                 `json:"type"` // click, hover, select, filter
	Target  string                 `json:"target"`
	Action  string                 `json:"action"`
	Params  map[string]interface{} `json:"params"`
	Enabled bool                   `json:"enabled"`
}

// DataSource 数据源
type DataSource struct {
	Type      string                 `json:"type"`
	URL       string                 `json:"url,omitempty"`
	Query     string                 `json:"query,omitempty"`
	Params    map[string]interface{} `json:"params,omitempty"`
	Headers   map[string]string      `json:"headers,omitempty"`
	Method    string                 `json:"method,omitempty"`
	Interval  time.Duration          `json:"interval,omitempty"`
	Cache     bool                   `json:"cache,omitempty"`
	Transform string                 `json:"transform,omitempty"`
}

// Filter 过滤器
type Filter struct {
	ID       string      `json:"id"`
	Name     string      `json:"name"`
	Type     string      `json:"type"` // date, select, input, range
	Value    interface{} `json:"value"`
	Options  []string    `json:"options,omitempty"`
	Required bool        `json:"required"`
	Global   bool        `json:"global"`
}

// DashboardSettings 仪表盘设置
type DashboardSettings struct {
	Theme           string        `json:"theme"`
	RefreshInterval time.Duration `json:"refresh_interval"`
	AutoRefresh     bool          `json:"auto_refresh"`
	ShowTitle       bool          `json:"show_title"`
	ShowFilters     bool          `json:"show_filters"`
	ShowExport      bool          `json:"show_export"`
	ShowFullscreen  bool          `json:"show_fullscreen"`
	AllowEdit       bool          `json:"allow_edit"`
	AllowShare      bool          `json:"allow_share"`
}

// Breakpoints 断点
type Breakpoints struct {
	XS int `json:"xs"` // < 576px
	SM int `json:"sm"` // >= 576px
	MD int `json:"md"` // >= 768px
	LG int `json:"lg"` // >= 992px
	XL int `json:"xl"` // >= 1200px
}

// TableOptions 表格选项
type TableOptions struct {
	Columns    []*TableColumn `json:"columns"`
	Pagination bool           `json:"pagination"`
	PageSize   int            `json:"page_size"`
	Sortable   bool           `json:"sortable"`
	Filterable bool           `json:"filterable"`
	Searchable bool           `json:"searchable"`
	Striped    bool           `json:"striped"`
	Bordered   bool           `json:"bordered"`
	Hover      bool           `json:"hover"`
	Compact    bool           `json:"compact"`
}

// TableColumn 表格列
type TableColumn struct {
	Key        string `json:"key"`
	Title      string `json:"title"`
	Width      int    `json:"width,omitempty"`
	Sortable   bool   `json:"sortable"`
	Filterable bool   `json:"filterable"`
	Align      string `json:"align,omitempty"`
	Format     string `json:"format,omitempty"`
	Render     string `json:"render,omitempty"`
}

// MetricOptions 指标选项
type MetricOptions struct {
	Value     interface{} `json:"value"`
	Label     string      `json:"label"`
	Unit      string      `json:"unit,omitempty"`
	Format    string      `json:"format,omitempty"`
	Trend     *Trend      `json:"trend,omitempty"`
	Target    interface{} `json:"target,omitempty"`
	Threshold *Threshold  `json:"threshold,omitempty"`
	Icon      string      `json:"icon,omitempty"`
	Color     string      `json:"color,omitempty"`
}

// Trend 趋势
type Trend struct {
	Direction  string  `json:"direction"` // up, down, stable
	Value      float64 `json:"value"`
	Percentage bool    `json:"percentage"`
}

// Threshold 阈值
type Threshold struct {
	Warning  interface{} `json:"warning"`
	Critical interface{} `json:"critical"`
	Good     interface{} `json:"good"`
}

// TextOptions 文本选项
type TextOptions struct {
	Content    string `json:"content"`
	Format     string `json:"format"` // plain, markdown, html
	FontSize   int    `json:"font_size,omitempty"`
	FontWeight string `json:"font_weight,omitempty"`
	Color      string `json:"color,omitempty"`
	Align      string `json:"align,omitempty"`
}

// DataProvider 数据提供者接口
type DataProvider interface {
	GetData(source *DataSource) (interface{}, error)
	GetType() string
	ValidateSource(source *DataSource) error
}

// EventBus 事件总线
type EventBus struct {
	logger      *logrus.Logger
	subscribers map[string][]EventHandler
	mutex       sync.RWMutex
}

// EventHandler 事件处理器
type EventHandler func(event *Event) error

// Event 事件
type Event struct {
	Type      string                 `json:"type"`
	Source    string                 `json:"source"`
	Target    string                 `json:"target,omitempty"`
	Data      map[string]interface{} `json:"data"`
	Timestamp time.Time              `json:"timestamp"`
}

// NewDashboardEngine 创建仪表盘引擎
func NewDashboardEngine(config *DashboardConfig, chartEngine *ChartEngine, logger *logrus.Logger) *DashboardEngine {
	if config == nil {
		config = DefaultDashboardConfig()
	}

	engine := &DashboardEngine{
		config:        config,
		logger:        logger,
		chartEngine:   chartEngine,
		dashboards:    make(map[string]*Dashboard),
		widgets:       make(map[string]*Widget),
		layouts:       make(map[string]*Layout),
		dataProviders: make(map[string]DataProvider),
		eventBus:      NewEventBus(logger),
		isRunning:     false,
	}

	// 注册默认数据提供者
	engine.registerDefaultDataProviders()

	return engine
}

// DefaultDashboardConfig 默认仪表盘配置
func DefaultDashboardConfig() *DashboardConfig {
	return &DashboardConfig{
		EnableRealtime:    true,
		RefreshInterval:   30 * time.Second,
		MaxDashboards:     100,
		MaxWidgetsPerDash: 50,
		EnableInteraction: true,
		EnableExport:      true,
		CacheEnabled:      true,
		CacheExpiration:   5 * time.Minute,
	}
}

// Start 启动仪表盘引擎
func (de *DashboardEngine) Start(ctx context.Context) error {
	de.mutex.Lock()
	defer de.mutex.Unlock()

	if de.isRunning {
		return fmt.Errorf("dashboard engine is already running")
	}

	de.logger.Info("Starting dashboard engine")

	// 启动事件总线
	if err := de.eventBus.Start(ctx); err != nil {
		return fmt.Errorf("failed to start event bus: %w", err)
	}

	// 启动实时更新协程
	if de.config.EnableRealtime {
		go de.realtimeUpdateRoutine(ctx)
	}

	de.isRunning = true
	de.logger.Info("Dashboard engine started successfully")

	return nil
}

// Stop 停止仪表盘引擎
func (de *DashboardEngine) Stop(ctx context.Context) error {
	de.mutex.Lock()
	defer de.mutex.Unlock()

	if !de.isRunning {
		return nil
	}

	de.logger.Info("Stopping dashboard engine")

	// 停止事件总线
	if err := de.eventBus.Stop(ctx); err != nil {
		de.logger.WithError(err).Error("Failed to stop event bus")
	}

	de.isRunning = false
	de.logger.Info("Dashboard engine stopped")

	return nil
}

// CreateDashboard 创建仪表盘
func (de *DashboardEngine) CreateDashboard(dashboard *Dashboard) error {
	de.mutex.Lock()
	defer de.mutex.Unlock()

	if len(de.dashboards) >= de.config.MaxDashboards {
		return fmt.Errorf("maximum number of dashboards reached: %d", de.config.MaxDashboards)
	}

	// 设置默认值
	if dashboard.ID == "" {
		dashboard.ID = de.generateDashboardID()
	}

	dashboard.CreatedAt = time.Now()
	dashboard.UpdatedAt = time.Now()

	// 验证仪表盘
	if err := de.validateDashboard(dashboard); err != nil {
		return fmt.Errorf("dashboard validation failed: %w", err)
	}

	de.dashboards[dashboard.ID] = dashboard

	de.logger.WithFields(logrus.Fields{
		"dashboard_id":   dashboard.ID,
		"dashboard_name": dashboard.Name,
	}).Info("Dashboard created")

	// 发送事件
	de.eventBus.Publish(&Event{
		Type:      "dashboard.created",
		Source:    "dashboard_engine",
		Data:      map[string]interface{}{"dashboard_id": dashboard.ID},
		Timestamp: time.Now(),
	})

	return nil
}

// GetDashboard 获取仪表盘
func (de *DashboardEngine) GetDashboard(id string) (*Dashboard, error) {
	de.mutex.RLock()
	defer de.mutex.RUnlock()

	dashboard, exists := de.dashboards[id]
	if !exists {
		return nil, fmt.Errorf("dashboard not found: %s", id)
	}

	return dashboard, nil
}

// UpdateDashboard 更新仪表盘
func (de *DashboardEngine) UpdateDashboard(dashboard *Dashboard) error {
	de.mutex.Lock()
	defer de.mutex.Unlock()

	existing, exists := de.dashboards[dashboard.ID]
	if !exists {
		return fmt.Errorf("dashboard not found: %s", dashboard.ID)
	}

	// 保留创建信息
	dashboard.CreatedAt = existing.CreatedAt
	dashboard.CreatedBy = existing.CreatedBy
	dashboard.UpdatedAt = time.Now()

	// 验证仪表盘
	if err := de.validateDashboard(dashboard); err != nil {
		return fmt.Errorf("dashboard validation failed: %w", err)
	}

	de.dashboards[dashboard.ID] = dashboard

	de.logger.WithField("dashboard_id", dashboard.ID).Info("Dashboard updated")

	// 发送事件
	de.eventBus.Publish(&Event{
		Type:      "dashboard.updated",
		Source:    "dashboard_engine",
		Data:      map[string]interface{}{"dashboard_id": dashboard.ID},
		Timestamp: time.Now(),
	})

	return nil
}

// DeleteDashboard 删除仪表盘
func (de *DashboardEngine) DeleteDashboard(id string) error {
	de.mutex.Lock()
	defer de.mutex.Unlock()

	_, exists := de.dashboards[id]
	if !exists {
		return fmt.Errorf("dashboard not found: %s", id)
	}

	delete(de.dashboards, id)

	de.logger.WithField("dashboard_id", id).Info("Dashboard deleted")

	// 发送事件
	de.eventBus.Publish(&Event{
		Type:      "dashboard.deleted",
		Source:    "dashboard_engine",
		Data:      map[string]interface{}{"dashboard_id": id},
		Timestamp: time.Now(),
	})

	return nil
}

// ListDashboards 列出仪表盘
func (de *DashboardEngine) ListDashboards() []*Dashboard {
	de.mutex.RLock()
	defer de.mutex.RUnlock()

	var dashboards []*Dashboard
	for _, dashboard := range de.dashboards {
		dashboards = append(dashboards, dashboard)
	}

	return dashboards
}

// RenderDashboard 渲染仪表盘
func (de *DashboardEngine) RenderDashboard(ctx context.Context, id string) (*RenderedDashboard, error) {
	dashboard, err := de.GetDashboard(id)
	if err != nil {
		return nil, err
	}

	rendered := &RenderedDashboard{
		Dashboard:       dashboard,
		RenderedWidgets: make([]*RenderedWidget, 0),
		RenderTime:      time.Now(),
	}

	// 渲染所有小部件
	for _, widget := range dashboard.Widgets {
		renderedWidget, err := de.renderWidget(ctx, widget)
		if err != nil {
			de.logger.WithError(err).WithField("widget_id", widget.ID).Error("Failed to render widget")
			continue
		}
		rendered.RenderedWidgets = append(rendered.RenderedWidgets, renderedWidget)
	}

	return rendered, nil
}

// renderWidget 渲染小部件
func (de *DashboardEngine) renderWidget(ctx context.Context, widget *Widget) (*RenderedWidget, error) {
	// 获取数据
	data, err := de.getWidgetData(widget)
	if err != nil {
		return nil, fmt.Errorf("failed to get widget data: %w", err)
	}

	rendered := &RenderedWidget{
		Widget:     widget,
		Data:       data,
		RenderTime: time.Now(),
	}

	// 根据小部件类型进行渲染
	switch widget.Type {
	case "chart":
		chart, err := de.renderChartWidget(ctx, widget, data)
		if err != nil {
			return nil, fmt.Errorf("failed to render chart widget: %w", err)
		}
		rendered.Content = chart
	case "metric":
		metric := de.renderMetricWidget(widget, data)
		rendered.Content = metric
	case "table":
		table := de.renderTableWidget(widget, data)
		rendered.Content = table
	case "text":
		text := de.renderTextWidget(widget, data)
		rendered.Content = text
	default:
		return nil, fmt.Errorf("unsupported widget type: %s", widget.Type)
	}

	return rendered, nil
}

// getWidgetData 获取小部件数据
func (de *DashboardEngine) getWidgetData(widget *Widget) (interface{}, error) {
	if widget.DataSource == nil {
		return widget.Data, nil
	}

	provider, exists := de.dataProviders[widget.DataSource.Type]
	if !exists {
		return nil, fmt.Errorf("unsupported data source type: %s", widget.DataSource.Type)
	}

	return provider.GetData(widget.DataSource)
}

// renderChartWidget 渲染图表小部件
func (de *DashboardEngine) renderChartWidget(ctx context.Context, widget *Widget, data interface{}) (*RenderedChart, error) {
	if widget.Config.ChartType == "" {
		return nil, fmt.Errorf("chart type not specified")
	}

	// 处理数据
	chartData, err := de.chartEngine.ProcessData(ctx, data, "generic")
	if err != nil {
		return nil, fmt.Errorf("failed to process chart data: %w", err)
	}

	// 设置图表类型
	chartData.Type = widget.Config.ChartType

	// 渲染图表
	return de.chartEngine.RenderChart(ctx, chartData, widget.Config.ChartOptions)
}

// renderMetricWidget 渲染指标小部件
func (de *DashboardEngine) renderMetricWidget(widget *Widget, data interface{}) *RenderedMetric {
	options := widget.Config.MetricOptions
	if options == nil {
		options = &MetricOptions{}
	}

	return &RenderedMetric{
		Value:  options.Value,
		Label:  options.Label,
		Unit:   options.Unit,
		Format: options.Format,
		Trend:  options.Trend,
		Color:  options.Color,
		Icon:   options.Icon,
	}
}

// renderTableWidget 渲染表格小部件
func (de *DashboardEngine) renderTableWidget(widget *Widget, data interface{}) *RenderedTable {
	options := widget.Config.TableOptions
	if options == nil {
		options = &TableOptions{}
	}

	return &RenderedTable{
		Columns: options.Columns,
		Data:    data,
		Options: options,
	}
}

// renderTextWidget 渲染文本小部件
func (de *DashboardEngine) renderTextWidget(widget *Widget, data interface{}) *RenderedText {
	options := widget.Config.TextOptions
	if options == nil {
		options = &TextOptions{}
	}

	return &RenderedText{
		Content:    options.Content,
		Format:     options.Format,
		FontSize:   options.FontSize,
		FontWeight: options.FontWeight,
		Color:      options.Color,
		Align:      options.Align,
	}
}

// 私有方法

// generateDashboardID 生成仪表盘ID
func (de *DashboardEngine) generateDashboardID() string {
	return fmt.Sprintf("dashboard_%d", time.Now().UnixNano())
}

// validateDashboard 验证仪表盘
func (de *DashboardEngine) validateDashboard(dashboard *Dashboard) error {
	if dashboard.Name == "" {
		return fmt.Errorf("dashboard name cannot be empty")
	}

	if len(dashboard.Widgets) > de.config.MaxWidgetsPerDash {
		return fmt.Errorf("too many widgets: %d (max: %d)", len(dashboard.Widgets), de.config.MaxWidgetsPerDash)
	}

	// 验证小部件
	for i, widget := range dashboard.Widgets {
		if err := de.validateWidget(widget); err != nil {
			return fmt.Errorf("widget %d validation failed: %w", i, err)
		}
	}

	return nil
}

// validateWidget 验证小部件
func (de *DashboardEngine) validateWidget(widget *Widget) error {
	if widget.Type == "" {
		return fmt.Errorf("widget type cannot be empty")
	}

	if widget.Title == "" {
		return fmt.Errorf("widget title cannot be empty")
	}

	return nil
}

// realtimeUpdateRoutine 实时更新协程
func (de *DashboardEngine) realtimeUpdateRoutine(ctx context.Context) {
	ticker := time.NewTicker(de.config.RefreshInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			de.updateDashboards()
		}
	}
}

// updateDashboards 更新仪表盘
func (de *DashboardEngine) updateDashboards() {
	de.mutex.RLock()
	dashboards := make([]*Dashboard, 0, len(de.dashboards))
	for _, dashboard := range de.dashboards {
		if dashboard.Settings != nil && dashboard.Settings.AutoRefresh {
			dashboards = append(dashboards, dashboard)
		}
	}
	de.mutex.RUnlock()

	for _, dashboard := range dashboards {
		// 发送更新事件
		de.eventBus.Publish(&Event{
			Type:      "dashboard.refresh",
			Source:    "dashboard_engine",
			Data:      map[string]interface{}{"dashboard_id": dashboard.ID},
			Timestamp: time.Now(),
		})
	}
}

// registerDefaultDataProviders 注册默认数据提供者
func (de *DashboardEngine) registerDefaultDataProviders() {
	// 注册静态数据提供者
	de.dataProviders["static"] = &StaticDataProvider{logger: de.logger}

	// 注册API数据提供者
	de.dataProviders["api"] = &APIDataProvider{logger: de.logger}

	// 注册数据库数据提供者
	de.dataProviders["database"] = &DatabaseDataProvider{logger: de.logger}

	// 注册监控数据提供者
	de.dataProviders["monitoring"] = &MonitoringDataProvider{logger: de.logger}

	// 注册日志数据提供者
	de.dataProviders["logs"] = &LogDataProvider{logger: de.logger}

	// 注册性能数据提供者
	de.dataProviders["performance"] = &PerformanceDataProvider{logger: de.logger}

	// 注册实时数据提供者
	de.dataProviders["realtime"] = &RealtimeDataProvider{logger: de.logger}

	// 注册JSON数据提供者
	de.dataProviders["json"] = &JSONDataProvider{logger: de.logger}
}

// GetDashboardEngineStatus 获取仪表盘引擎状态
func (de *DashboardEngine) GetDashboardEngineStatus() *DashboardEngineStatus {
	de.mutex.RLock()
	defer de.mutex.RUnlock()

	return &DashboardEngineStatus{
		IsRunning:           de.isRunning,
		Config:              de.config,
		TotalDashboards:     len(de.dashboards),
		TotalWidgets:        de.getTotalWidgets(),
		RegisteredProviders: len(de.dataProviders),
		SupportedProviders:  de.getSupportedProviders(),
	}
}

// getTotalWidgets 获取总小部件数
func (de *DashboardEngine) getTotalWidgets() int {
	total := 0
	for _, dashboard := range de.dashboards {
		total += len(dashboard.Widgets)
	}
	return total
}

// getSupportedProviders 获取支持的数据提供者
func (de *DashboardEngine) getSupportedProviders() []string {
	var providers []string
	for providerType := range de.dataProviders {
		providers = append(providers, providerType)
	}
	return providers
}

// RegisterDataProvider 注册数据提供者
func (de *DashboardEngine) RegisterDataProvider(provider DataProvider) error {
	de.mutex.Lock()
	defer de.mutex.Unlock()

	providerType := provider.GetType()
	if providerType == "" {
		return fmt.Errorf("provider type cannot be empty")
	}

	de.dataProviders[providerType] = provider
	de.logger.WithField("provider_type", providerType).Info("Data provider registered")

	return nil
}

// DashboardEngineStatus 仪表盘引擎状态
type DashboardEngineStatus struct {
	IsRunning           bool             `json:"is_running"`
	Config              *DashboardConfig `json:"config"`
	TotalDashboards     int              `json:"total_dashboards"`
	TotalWidgets        int              `json:"total_widgets"`
	RegisteredProviders int              `json:"registered_providers"`
	SupportedProviders  []string         `json:"supported_providers"`
}

// 事件总线实现

// NewEventBus 创建事件总线
func NewEventBus(logger *logrus.Logger) *EventBus {
	return &EventBus{
		logger:      logger,
		subscribers: make(map[string][]EventHandler),
	}
}

// Start 启动事件总线
func (eb *EventBus) Start(ctx context.Context) error {
	eb.logger.Info("Starting event bus")
	return nil
}

// Stop 停止事件总线
func (eb *EventBus) Stop(ctx context.Context) error {
	eb.logger.Info("Stopping event bus")
	return nil
}

// Subscribe 订阅事件
func (eb *EventBus) Subscribe(eventType string, handler EventHandler) {
	eb.mutex.Lock()
	defer eb.mutex.Unlock()

	eb.subscribers[eventType] = append(eb.subscribers[eventType], handler)
}

// Publish 发布事件
func (eb *EventBus) Publish(event *Event) {
	eb.mutex.RLock()
	handlers := eb.subscribers[event.Type]
	eb.mutex.RUnlock()

	for _, handler := range handlers {
		go func(h EventHandler) {
			if err := h(event); err != nil {
				eb.logger.WithError(err).WithField("event_type", event.Type).Error("Event handler failed")
			}
		}(handler)
	}
}
