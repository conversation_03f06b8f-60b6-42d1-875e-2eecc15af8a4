package handler

import (
	"net/http"

	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// AIHandler AI处理器
type AIHandler struct {
	services *service.Services
	logger   *logrus.Logger
}

// NewAIHandler 创建AI处理器
func NewAIHandler(services *service.Services, logger *logrus.Logger) *AIHandler {
	return &AIHandler{
		services: services,
		logger:   logger,
	}
}

// ProcessMessage 处理消息
func (h *AIHandler) ProcessMessage(c *gin.Context) {
	var req service.ProcessMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind request")
		c.J<PERSON>(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"message": err.Error(),
		})
		return
	}

	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.<PERSON>(http.StatusUnauthorized, gin.H{
			"error":   "Unauthorized",
			"message": "User ID not found in token",
		})
		return
	}

	req.UserID = userID.(int64)

	h.logger.WithFields(logrus.Fields{
		"user_id":    req.UserID,
		"session_id": req.SessionID,
		"message":    req.Message,
	}).Info("Processing AI message")

	response, err := h.services.AI.ProcessMessageWithTools(c, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process message")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to process message",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// ProcessBasicMessage 处理基础消息（不使用工具）
func (h *AIHandler) ProcessBasicMessage(c *gin.Context) {
	var req service.ProcessMessageRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"message": err.Error(),
		})
		return
	}

	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Unauthorized",
			"message": "User ID not found in token",
		})
		return
	}

	req.UserID = userID.(int64)

	response, err := h.services.AI.ProcessMessage(c, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to process basic message")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to process message",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}

// GetContext 获取对话上下文
func (h *AIHandler) GetContext(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid session ID",
			"message": "Session ID is required",
		})
		return
	}

	context, err := h.services.AI.GetContext(sessionID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to get context")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get context",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, context)
}

// ClearContext 清除对话上下文
func (h *AIHandler) ClearContext(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid session ID",
			"message": "Session ID is required",
		})
		return
	}

	err := h.services.AI.ClearContext(sessionID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to clear context")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to clear context",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Context cleared successfully",
	})
}

// GetAvailableTools 获取可用工具
func (h *AIHandler) GetAvailableTools(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"error":   "Unauthorized",
			"message": "User ID not found in token",
		})
		return
	}

	tools, err := h.services.AI.GetAvailableTools(userID.(int64))
	if err != nil {
		h.logger.WithError(err).Error("Failed to get available tools")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get tools",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, tools)
}

// SummarizeConversation 总结对话
func (h *AIHandler) SummarizeConversation(c *gin.Context) {
	sessionID := c.Param("session_id")
	if sessionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid session ID",
			"message": "Session ID is required",
		})
		return
	}

	summary, err := h.services.AI.SummarizeConversation(c, sessionID)
	if err != nil {
		h.logger.WithError(err).Error("Failed to summarize conversation")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to summarize conversation",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, summary)
}

// ValidateCommand 验证命令
func (h *AIHandler) ValidateCommand(c *gin.Context) {
	var req struct {
		Command   string `json:"command" binding:"required"`
		SessionID string `json:"session_id,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"message": err.Error(),
		})
		return
	}

	// 获取用户上下文（如果有会话ID）
	var context *service.ConversationContext
	if req.SessionID != "" {
		var err error
		context, err = h.services.AI.GetContext(req.SessionID)
		if err != nil {
			h.logger.WithError(err).Warn("Failed to get context for command validation")
		}
	}

	validation, err := h.services.AI.ValidateCommand(c, req.Command, context)
	if err != nil {
		h.logger.WithError(err).Error("Failed to validate command")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to validate command",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, validation)
}

// GenerateResponse 生成响应
func (h *AIHandler) GenerateResponse(c *gin.Context) {
	var req service.GenerateResponseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.logger.WithError(err).Error("Failed to bind request")
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request format",
			"message": err.Error(),
		})
		return
	}

	response, err := h.services.AI.GenerateResponse(c, &req)
	if err != nil {
		h.logger.WithError(err).Error("Failed to generate response")
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to generate response",
			"message": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, response)
}
