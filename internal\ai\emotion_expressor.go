package ai

import (
	"math/rand"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// EmotionExpressor 情感表达器
type EmotionExpressor struct {
	logger            *logrus.Logger
	config            *EmotionalIntelligenceConfig
	responseTemplates map[EmotionType]*ResponseTemplate
	empatheticPhrases map[EmotionType][]string
	toneAdjustments   map[EmotionType]*ToneAdjustment
	emojiMappings     map[EmotionType][]string
}

// ResponseTemplate 响应模板
type ResponseTemplate struct {
	Greetings     []string `json:"greetings"`
	Transitions   []string `json:"transitions"`
	Conclusions   []string `json:"conclusions"`
	Empathy       []string `json:"empathy"`
	Encouragement []string `json:"encouragement"`
	Solutions     []string `json:"solutions"`
}

// ToneAdjustment 语调调整
type ToneAdjustment struct {
	Formality      string  `json:"formality"`      // formal, casual, friendly
	Warmth         float64 `json:"warmth"`         // 0.0-1.0
	Urgency        float64 `json:"urgency"`        // 0.0-1.0
	Supportiveness float64 `json:"supportiveness"` // 0.0-1.0
	Confidence     float64 `json:"confidence"`     // 0.0-1.0
}

// ResponseStrategy 响应策略
type ResponseStrategy struct {
	Tone           string                 `json:"tone"`
	Style          string                 `json:"style"`
	EmpatheticCues []string               `json:"empathetic_cues"`
	Adaptations    map[string]interface{} `json:"adaptations"`
	Template       *ResponseTemplate      `json:"template"`
	ToneAdjustment *ToneAdjustment        `json:"tone_adjustment"`
}

// UserEmotionalProfile 用户情感画像
type UserEmotionalProfile struct {
	UserID              int64                  `json:"user_id"`
	DominantEmotions    []EmotionType          `json:"dominant_emotions"`
	EmotionalPatterns   map[string]float64     `json:"emotional_patterns"`
	PreferredTone       string                 `json:"preferred_tone"`
	ResponsePreferences map[string]interface{} `json:"response_preferences"`
	LastUpdated         time.Time              `json:"last_updated"`
	InteractionHistory  []EmotionInteraction   `json:"interaction_history"`
}

// EmotionInteraction 情感交互记录
type EmotionInteraction struct {
	Timestamp     time.Time   `json:"timestamp"`
	UserEmotion   EmotionType `json:"user_emotion"`
	ResponseStyle string      `json:"response_style"`
	UserFeedback  string      `json:"user_feedback"`
	Effectiveness float64     `json:"effectiveness"`
}

// NewEmotionExpressor 创建情感表达器
func NewEmotionExpressor(
	logger *logrus.Logger,
	config *EmotionalIntelligenceConfig,
) *EmotionExpressor {
	expressor := &EmotionExpressor{
		logger: logger,
		config: config,
	}

	// 初始化响应模板和映射
	expressor.initializeResponseTemplates()
	expressor.initializeEmpatheticPhrases()
	expressor.initializeToneAdjustments()
	expressor.initializeEmojiMappings()

	return expressor
}

// GenerateResponseStrategy 生成响应策略
func (ee *EmotionExpressor) GenerateResponseStrategy(
	userEmotion *EmotionState,
	emotionHistory []*EmotionMemoryRecord,
) *ResponseStrategy {
	ee.logger.WithFields(logrus.Fields{
		"user_emotion": userEmotion.PrimaryEmotion,
		"intensity":    userEmotion.Intensity,
		"confidence":   userEmotion.Confidence,
	}).Debug("生成响应策略")

	// 1. 确定基础响应风格
	baseStyle := ee.determineBaseStyle(userEmotion)

	// 2. 选择合适的语调
	tone := ee.selectTone(userEmotion, emotionHistory)

	// 3. 生成共情线索
	empatheticCues := ee.generateEmpatheticCues(userEmotion)

	// 4. 创建适应性调整
	adaptations := ee.createAdaptations(userEmotion, emotionHistory)

	// 5. 选择响应模板
	template := ee.responseTemplates[userEmotion.PrimaryEmotion]

	// 6. 配置语调调整
	toneAdjustment := ee.toneAdjustments[userEmotion.PrimaryEmotion]

	return &ResponseStrategy{
		Tone:           tone,
		Style:          baseStyle,
		EmpatheticCues: empatheticCues,
		Adaptations:    adaptations,
		Template:       template,
		ToneAdjustment: toneAdjustment,
	}
}

// EnhanceResponse 增强响应的情感表达
func (ee *EmotionExpressor) EnhanceResponse(
	originalResponse string,
	emotionAnalysis *EmotionAnalysisResult,
) string {
	if !ee.config.EnableEmotionExpression {
		return originalResponse
	}

	userEmotion := emotionAnalysis.UserEmotion

	ee.logger.WithFields(logrus.Fields{
		"original_length": len(originalResponse),
		"user_emotion":    userEmotion.PrimaryEmotion,
		"suggested_tone":  emotionAnalysis.SuggestedTone,
	}).Debug("开始增强响应")

	// 1. 添加情感化的开场白
	enhancedResponse := ee.addEmotionalOpening(originalResponse, userEmotion)

	// 2. 调整语言风格
	enhancedResponse = ee.adjustLanguageStyle(enhancedResponse, emotionAnalysis)

	// 3. 添加共情表达
	enhancedResponse = ee.addEmpatheticExpressions(enhancedResponse, userEmotion)

	// 4. 添加合适的表情符号
	enhancedResponse = ee.addEmojis(enhancedResponse, userEmotion)

	// 5. 调整结尾语调
	enhancedResponse = ee.adjustClosing(enhancedResponse, userEmotion)

	ee.logger.WithFields(logrus.Fields{
		"enhanced_length":  len(enhancedResponse),
		"enhancement_rate": float64(len(enhancedResponse)) / float64(len(originalResponse)),
	}).Debug("响应增强完成")

	return enhancedResponse
}

// determineBaseStyle 确定基础响应风格
func (ee *EmotionExpressor) determineBaseStyle(emotion *EmotionState) string {
	switch emotion.PrimaryEmotion {
	case EmotionJoy, EmotionExcitement:
		return "enthusiastic"
	case EmotionSadness, EmotionFrustration:
		return "supportive"
	case EmotionAnger:
		return "calming"
	case EmotionAnxiety, EmotionFear:
		return "reassuring"
	case EmotionSurprise:
		return "explanatory"
	case EmotionRelief:
		return "affirming"
	case EmotionConfidence:
		return "collaborative"
	default:
		return "professional"
	}
}

// selectTone 选择合适的语调
func (ee *EmotionExpressor) selectTone(
	emotion *EmotionState,
	history []*EmotionMemoryRecord,
) string {
	// 基于当前情感选择基础语调
	baseTone := ee.getBaseToneForEmotion(emotion.PrimaryEmotion)

	// 根据历史情感调整语调
	if len(history) > 0 {
		recentEmotion := history[0].EmotionType
		if recentEmotion == string(EmotionFrustration) && emotion.PrimaryEmotion == EmotionFrustration {
			// 连续挫折，使用更加耐心的语调
			return "patient_supportive"
		}
	}

	return baseTone
}

// generateEmpatheticCues 生成共情线索
func (ee *EmotionExpressor) generateEmpatheticCues(emotion *EmotionState) []string {
	phrases, exists := ee.empatheticPhrases[emotion.PrimaryEmotion]
	if !exists {
		return []string{"我理解您的情况"}
	}

	// 根据情感强度选择合适数量的共情短语
	numPhrases := 1
	if emotion.Intensity > 0.7 {
		numPhrases = 2
	}

	selectedPhrases := make([]string, 0, numPhrases)
	for i := 0; i < numPhrases && i < len(phrases); i++ {
		selectedPhrases = append(selectedPhrases, phrases[rand.Intn(len(phrases))])
	}

	return selectedPhrases
}

// createAdaptations 创建适应性调整
func (ee *EmotionExpressor) createAdaptations(
	emotion *EmotionState,
	history []*EmotionMemoryRecord,
) map[string]interface{} {
	adaptations := make(map[string]interface{})

	// 基于情感强度调整
	adaptations["response_length"] = ee.calculateResponseLength(emotion.Intensity)
	adaptations["detail_level"] = ee.calculateDetailLevel(emotion.PrimaryEmotion)
	adaptations["urgency_level"] = ee.calculateUrgencyLevel(emotion)

	// 基于历史模式调整
	if len(history) > 0 {
		adaptations["historical_context"] = ee.analyzeHistoricalPattern(history)
	}

	return adaptations
}

// addEmotionalOpening 添加情感化的开场白
func (ee *EmotionExpressor) addEmotionalOpening(response string, emotion *EmotionState) string {
	template := ee.responseTemplates[emotion.PrimaryEmotion]
	if template == nil || len(template.Greetings) == 0 {
		return response
	}

	greeting := template.Greetings[rand.Intn(len(template.Greetings))]
	return greeting + " " + response
}

// adjustLanguageStyle 调整语言风格
func (ee *EmotionExpressor) adjustLanguageStyle(response string, analysis *EmotionAnalysisResult) string {
	toneAdjustment := ee.toneAdjustments[analysis.UserEmotion.PrimaryEmotion]
	if toneAdjustment == nil {
		return response
	}

	// 根据正式程度调整语言
	if toneAdjustment.Formality == "casual" {
		response = ee.makeCasual(response)
	} else if toneAdjustment.Formality == "formal" {
		response = ee.makeFormal(response)
	}

	// 根据温暖程度调整
	if toneAdjustment.Warmth > 0.7 {
		response = ee.addWarmth(response)
	}

	return response
}

// addEmpatheticExpressions 添加共情表达
func (ee *EmotionExpressor) addEmpatheticExpressions(response string, emotion *EmotionState) string {
	template := ee.responseTemplates[emotion.PrimaryEmotion]
	if template == nil || len(template.Empathy) == 0 {
		return response
	}

	// 在适当位置插入共情表达
	empathy := template.Empathy[rand.Intn(len(template.Empathy))]

	// 如果响应较长，在中间插入；否则在开头添加
	if len(response) > 100 {
		sentences := strings.Split(response, "。")
		if len(sentences) > 1 {
			midPoint := len(sentences) / 2
			sentences[midPoint] = empathy + "。" + sentences[midPoint]
			return strings.Join(sentences, "。")
		}
	}

	return empathy + "。" + response
}

// addEmojis 添加合适的表情符号
func (ee *EmotionExpressor) addEmojis(response string, emotion *EmotionState) string {
	emojis, exists := ee.emojiMappings[emotion.PrimaryEmotion]
	if !exists || len(emojis) == 0 {
		return response
	}

	// 根据情感强度决定是否添加表情符号
	if emotion.Intensity > 0.6 {
		emoji := emojis[rand.Intn(len(emojis))]
		return response + " " + emoji
	}

	return response
}

// adjustClosing 调整结尾语调
func (ee *EmotionExpressor) adjustClosing(response string, emotion *EmotionState) string {
	template := ee.responseTemplates[emotion.PrimaryEmotion]
	if template == nil || len(template.Conclusions) == 0 {
		return response
	}

	conclusion := template.Conclusions[rand.Intn(len(template.Conclusions))]
	return response + " " + conclusion
}

// 初始化方法
func (ee *EmotionExpressor) initializeResponseTemplates() {
	ee.responseTemplates = map[EmotionType]*ResponseTemplate{
		EmotionJoy: {
			Greetings:     []string{"太好了！", "很高兴听到这个消息！", "真是令人开心！"},
			Transitions:   []string{"继续保持", "让我们", "接下来"},
			Conclusions:   []string{"希望能继续帮助您！", "祝您工作顺利！", "有什么需要随时联系我！"},
			Empathy:       []string{"我能感受到您的喜悦", "看到您成功我也很开心"},
			Encouragement: []string{"您做得很棒", "继续保持这个势头"},
		},
		EmotionFrustration: {
			Greetings:     []string{"我理解您的困扰", "遇到问题确实令人沮丧", "让我来帮您解决这个问题"},
			Transitions:   []string{"让我们一步步来", "不要担心", "我们可以"},
			Conclusions:   []string{"相信问题很快就能解决", "我会一直协助您", "有任何问题都可以找我"},
			Empathy:       []string{"我完全理解您的感受", "这种情况确实让人头疼"},
			Encouragement: []string{"不要放弃，我们一起解决", "您已经做得很好了"},
		},
		EmotionAnxiety: {
			Greetings:     []string{"请不要担心", "我来帮您处理", "让我们冷静地解决这个问题"},
			Transitions:   []string{"首先", "我们慢慢来", "一步一步"},
			Conclusions:   []string{"一切都会好起来的", "我会确保问题得到解决", "请放心"},
			Empathy:       []string{"我理解您的担心", "这种紧急情况确实让人焦虑"},
			Encouragement: []string{"深呼吸，我们一起处理", "您不是一个人在战斗"},
		},
	}
}

func (ee *EmotionExpressor) initializeEmpatheticPhrases() {
	ee.empatheticPhrases = map[EmotionType][]string{
		EmotionJoy:         {"我为您感到高兴", "真是太棒了", "您的成功让我也很开心"},
		EmotionFrustration: {"我理解您的困扰", "这确实很令人沮丧", "我能感受到您的不易"},
		EmotionAnxiety:     {"我理解您的担心", "这种情况确实让人焦虑", "请不要过于担心"},
		EmotionAnger:       {"我理解您的愤怒", "这种情况确实让人生气", "让我们冷静地处理"},
		EmotionSadness:     {"我能感受到您的失落", "这确实让人难过", "我会陪伴您度过难关"},
	}
}

func (ee *EmotionExpressor) initializeToneAdjustments() {
	ee.toneAdjustments = map[EmotionType]*ToneAdjustment{
		EmotionJoy: {
			Formality:      "friendly",
			Warmth:         0.9,
			Urgency:        0.3,
			Supportiveness: 0.7,
			Confidence:     0.8,
		},
		EmotionFrustration: {
			Formality:      "professional",
			Warmth:         0.8,
			Urgency:        0.7,
			Supportiveness: 0.9,
			Confidence:     0.9,
		},
		EmotionAnxiety: {
			Formality:      "reassuring",
			Warmth:         0.9,
			Urgency:        0.8,
			Supportiveness: 1.0,
			Confidence:     0.9,
		},
	}
}

func (ee *EmotionExpressor) initializeEmojiMappings() {
	ee.emojiMappings = map[EmotionType][]string{
		EmotionJoy:         {"😊", "🎉", "👍", "✨"},
		EmotionFrustration: {"💪", "🔧", "⚡"},
		EmotionAnxiety:     {"🤝", "💙", "🛡️"},
		EmotionRelief:      {"😌", "✅", "🎯"},
		EmotionConfidence:  {"💪", "🚀", "⭐"},
	}
}

// 辅助方法
func (ee *EmotionExpressor) getBaseToneForEmotion(emotion EmotionType) string {
	switch emotion {
	case EmotionJoy, EmotionExcitement:
		return "enthusiastic"
	case EmotionSadness, EmotionFrustration:
		return "supportive"
	case EmotionAnger:
		return "calming"
	case EmotionAnxiety, EmotionFear:
		return "reassuring"
	default:
		return "professional"
	}
}

func (ee *EmotionExpressor) calculateResponseLength(intensity float64) string {
	if intensity > 0.8 {
		return "detailed"
	} else if intensity > 0.5 {
		return "moderate"
	}
	return "concise"
}

func (ee *EmotionExpressor) calculateDetailLevel(emotion EmotionType) string {
	switch emotion {
	case EmotionAnxiety, EmotionFear:
		return "high" // 焦虑时需要详细解释
	case EmotionJoy, EmotionRelief:
		return "low" // 开心时简洁即可
	default:
		return "moderate"
	}
}

func (ee *EmotionExpressor) calculateUrgencyLevel(emotion *EmotionState) string {
	if emotion.PrimaryEmotion == EmotionAnxiety && emotion.Intensity > 0.7 {
		return "high"
	} else if emotion.PrimaryEmotion == EmotionAnger {
		return "moderate"
	}
	return "low"
}

func (ee *EmotionExpressor) analyzeHistoricalPattern(history []*EmotionMemoryRecord) map[string]interface{} {
	pattern := make(map[string]interface{})

	if len(history) > 0 {
		recentEmotion := history[0].EmotionType
		pattern["recent_emotion"] = recentEmotion
		pattern["pattern_detected"] = len(history) > 2
	}

	return pattern
}

func (ee *EmotionExpressor) makeCasual(response string) string {
	// 简单的口语化处理
	response = strings.ReplaceAll(response, "您", "你")
	response = strings.ReplaceAll(response, "请", "")
	return response
}

func (ee *EmotionExpressor) makeFormal(response string) string {
	// 正式化处理
	response = strings.ReplaceAll(response, "你", "您")
	return response
}

func (ee *EmotionExpressor) addWarmth(response string) string {
	// 添加温暖的表达
	warmPhrases := []string{"亲爱的用户", "我的朋友", ""}
	if rand.Float64() > 0.7 {
		phrase := warmPhrases[rand.Intn(len(warmPhrases))]
		if phrase != "" {
			response = phrase + "，" + response
		}
	}
	return response
}
