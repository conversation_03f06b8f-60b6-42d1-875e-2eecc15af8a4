package config

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"io"
	"os"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"
	"github.com/spf13/viper"
)

// EnhancedConfigManager 增强的配置管理器
type EnhancedConfigManager struct {
	mu            sync.RWMutex
	config        *Config
	viper         *viper.Viper
	watchers      []ConfigWatcher
	encryptionKey []byte
	validators    map[string]ConfigValidator
	lastModified  time.Time
}

// ConfigWatcher 配置变更监听器
type ConfigWatcher interface {
	OnConfigChanged(oldConfig, newConfig *Config) error
}

// ConfigValidator 配置验证器
type ConfigValidator interface {
	Validate(config *Config) error
}

// NewEnhancedConfigManager 创建增强的配置管理器
func NewEnhancedConfigManager() *EnhancedConfigManager {
	return &EnhancedConfigManager{
		viper:      viper.New(),
		watchers:   make([]ConfigWatcher, 0),
		validators: make(map[string]ConfigValidator),
	}
}

// LoadWithEncryption 加载加密配置
func (m *EnhancedConfigManager) LoadWithEncryption(configPath, keyPath string) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	// 加载加密密钥
	if keyPath != "" {
		key, err := os.ReadFile(keyPath)
		if err != nil {
			return fmt.Errorf("failed to read encryption key: %w", err)
		}
		m.encryptionKey = key
	}

	// 设置配置文件路径
	m.viper.SetConfigFile(configPath)

	// 设置环境变量前缀
	m.viper.SetEnvPrefix("AIOPS")
	m.viper.AutomaticEnv()
	m.viper.SetEnvKeyReplacer(strings.NewReplacer(".", "_"))

	// 设置默认值
	m.setDefaults()

	// 读取配置文件
	if err := m.viper.ReadInConfig(); err != nil {
		return fmt.Errorf("failed to read config file: %w", err)
	}

	// 解析配置
	config := &Config{}
	if err := m.viper.Unmarshal(config); err != nil {
		return fmt.Errorf("failed to unmarshal config: %w", err)
	}

	// 解密敏感配置
	if err := m.decryptSensitiveFields(config); err != nil {
		return fmt.Errorf("failed to decrypt sensitive fields: %w", err)
	}

	// 验证配置
	if err := m.validateConfig(config); err != nil {
		return fmt.Errorf("config validation failed: %w", err)
	}

	m.config = config
	m.lastModified = time.Now()

	return nil
}

// WatchConfig 监听配置文件变化
func (m *EnhancedConfigManager) WatchConfig() error {
	m.viper.WatchConfig()
	m.viper.OnConfigChange(func(e fsnotify.Event) {
		m.handleConfigChange(e)
	})
	return nil
}

// AddWatcher 添加配置变更监听器
func (m *EnhancedConfigManager) AddWatcher(watcher ConfigWatcher) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.watchers = append(m.watchers, watcher)
}

// AddValidator 添加配置验证器
func (m *EnhancedConfigManager) AddValidator(name string, validator ConfigValidator) {
	m.mu.Lock()
	defer m.mu.Unlock()
	m.validators[name] = validator
}

// GetConfig 获取配置
func (m *EnhancedConfigManager) GetConfig() *Config {
	m.mu.RLock()
	defer m.mu.RUnlock()
	return m.config
}

// UpdateConfig 更新配置
func (m *EnhancedConfigManager) UpdateConfig(key string, value interface{}) error {
	m.mu.Lock()
	defer m.mu.Unlock()

	oldConfig := *m.config
	m.viper.Set(key, value)

	// 重新解析配置
	newConfig := &Config{}
	if err := m.viper.Unmarshal(newConfig); err != nil {
		return fmt.Errorf("failed to unmarshal updated config: %w", err)
	}

	// 验证新配置
	if err := m.validateConfig(newConfig); err != nil {
		return fmt.Errorf("updated config validation failed: %w", err)
	}

	m.config = newConfig

	// 通知监听器
	for _, watcher := range m.watchers {
		if err := watcher.OnConfigChanged(&oldConfig, newConfig); err != nil {
			// 记录错误但不中断流程
			fmt.Printf("Config watcher error: %v\n", err)
		}
	}

	return nil
}

// EncryptSensitiveValue 加密敏感值
func (m *EnhancedConfigManager) EncryptSensitiveValue(value string) (string, error) {
	if m.encryptionKey == nil {
		return value, nil // 如果没有加密密钥，返回原值
	}

	block, err := aes.NewCipher(m.encryptionKey)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	nonce := make([]byte, gcm.NonceSize())
	if _, err := io.ReadFull(rand.Reader, nonce); err != nil {
		return "", fmt.Errorf("failed to generate nonce: %w", err)
	}

	ciphertext := gcm.Seal(nonce, nonce, []byte(value), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// DecryptSensitiveValue 解密敏感值
func (m *EnhancedConfigManager) DecryptSensitiveValue(encryptedValue string) (string, error) {
	if m.encryptionKey == nil {
		return encryptedValue, nil // 如果没有加密密钥，返回原值
	}

	// 检查是否为加密值（以特定前缀标识）
	if !strings.HasPrefix(encryptedValue, "enc:") {
		return encryptedValue, nil
	}

	// 移除前缀
	encryptedValue = strings.TrimPrefix(encryptedValue, "enc:")

	data, err := base64.StdEncoding.DecodeString(encryptedValue)
	if err != nil {
		return "", fmt.Errorf("failed to decode encrypted value: %w", err)
	}

	block, err := aes.NewCipher(m.encryptionKey)
	if err != nil {
		return "", fmt.Errorf("failed to create cipher: %w", err)
	}

	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("failed to create GCM: %w", err)
	}

	nonceSize := gcm.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("encrypted data too short")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("failed to decrypt: %w", err)
	}

	return string(plaintext), nil
}

// setDefaults 设置默认配置值
func (m *EnhancedConfigManager) setDefaults() {
	// 应用默认配置
	m.viper.SetDefault("app.name", "AI Ops Platform")
	m.viper.SetDefault("app.env", "development")
	m.viper.SetDefault("app.debug", false)
	m.viper.SetDefault("app.port", 8080)
	m.viper.SetDefault("app.version", "1.0.0")

	// 数据库默认配置
	m.viper.SetDefault("database.path", "./data/aiops.db")
	m.viper.SetDefault("database.max_open_conns", 25)
	m.viper.SetDefault("database.max_idle_conns", 5)
	m.viper.SetDefault("database.conn_max_lifetime", "5m")
	m.viper.SetDefault("database.conn_max_idle_time", "5m")

	// JWT默认配置
	m.viper.SetDefault("jwt.access_token_ttl", "15m")
	m.viper.SetDefault("jwt.refresh_token_ttl", "7d")
	m.viper.SetDefault("jwt.issuer", "aiops-platform")
	m.viper.SetDefault("jwt.max_concurrent_sessions", 5)

	// 安全默认配置
	m.viper.SetDefault("security.password_hash_cost", 12)
	m.viper.SetDefault("security.session_timeout", "30m")
	m.viper.SetDefault("security.max_login_attempts", 5)
	m.viper.SetDefault("security.lockout_duration", "15m")

	// 日志默认配置
	m.viper.SetDefault("log.level", "info")
	m.viper.SetDefault("log.format", "json")
	m.viper.SetDefault("log.file", "./logs/aiops.log")
	m.viper.SetDefault("log.max_size", 100)
	m.viper.SetDefault("log.retention_days", 30)
}

// handleConfigChange 处理配置文件变化
func (m *EnhancedConfigManager) handleConfigChange(e fsnotify.Event) {
	m.mu.Lock()
	defer m.mu.Unlock()

	oldConfig := *m.config

	// 重新读取配置
	if err := m.viper.ReadInConfig(); err != nil {
		fmt.Printf("Failed to reload config: %v\n", err)
		return
	}

	// 解析新配置
	newConfig := &Config{}
	if err := m.viper.Unmarshal(newConfig); err != nil {
		fmt.Printf("Failed to unmarshal reloaded config: %v\n", err)
		return
	}

	// 解密敏感配置
	if err := m.decryptSensitiveFields(newConfig); err != nil {
		fmt.Printf("Failed to decrypt sensitive fields: %v\n", err)
		return
	}

	// 验证新配置
	if err := m.validateConfig(newConfig); err != nil {
		fmt.Printf("Reloaded config validation failed: %v\n", err)
		return
	}

	m.config = newConfig
	m.lastModified = time.Now()

	// 通知监听器
	for _, watcher := range m.watchers {
		if err := watcher.OnConfigChanged(&oldConfig, newConfig); err != nil {
			fmt.Printf("Config watcher error: %v\n", err)
		}
	}
}

// decryptSensitiveFields 解密敏感字段
func (m *EnhancedConfigManager) decryptSensitiveFields(config *Config) error {
	// 解密JWT密钥
	if decrypted, err := m.DecryptSensitiveValue(config.JWT.Secret); err != nil {
		return fmt.Errorf("failed to decrypt JWT secret: %w", err)
	} else {
		config.JWT.Secret = decrypted
	}

	// 解密DeepSeek API密钥
	if decrypted, err := m.DecryptSensitiveValue(config.DeepSeek.APIKey); err != nil {
		return fmt.Errorf("failed to decrypt DeepSeek API key: %w", err)
	} else {
		config.DeepSeek.APIKey = decrypted
	}

	// 解密Redis密码
	if decrypted, err := m.DecryptSensitiveValue(config.Redis.Password); err != nil {
		return fmt.Errorf("failed to decrypt Redis password: %w", err)
	} else {
		config.Redis.Password = decrypted
	}

	return nil
}

// validateConfig 验证配置
func (m *EnhancedConfigManager) validateConfig(config *Config) error {
	for name, validator := range m.validators {
		if err := validator.Validate(config); err != nil {
			return fmt.Errorf("validator %s failed: %w", name, err)
		}
	}
	return nil
}
