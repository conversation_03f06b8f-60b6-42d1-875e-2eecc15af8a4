package handler

import (
	"net/http"
	"strconv"

	"aiops-platform/internal/middleware"
	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// ActionHandler 操作处理器
type ActionHandler struct {
	logger           *logrus.Logger
	actionExecutor   *service.ActionExecutor
	actionRecognizer *service.ActionRecognizer
}

// NewActionHandler 创建操作处理器
func NewActionHandler(logger *logrus.Logger, actionExecutor *service.ActionExecutor, actionRecognizer *service.ActionRecognizer) *ActionHandler {
	return &ActionHandler{
		logger:           logger,
		actionExecutor:   actionExecutor,
		actionRecognizer: actionRecognizer,
	}
}

// ExecuteAction 执行操作
func (h *ActionHandler) ExecuteAction(c *gin.Context) {
	var req service.ExecuteActionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 获取用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		userID = 1 // 默认用户ID，实际应该从认证中获取
	}
	req.UserID = userID

	// 执行操作
	result, err := h.actionExecutor.ExecuteAction(c.Request.Context(), &req)
	if err != nil {
		h.logger.WithFields(logrus.Fields{
			"action_id":   req.ActionID,
			"action_type": req.ActionType,
			"user_id":     userID,
			"error":       err.Error(),
		}).Error("Failed to execute action")

		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to execute action",
			"error":   err.Error(),
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"action_id": req.ActionID,
		"status":    result.Status,
		"user_id":   userID,
	}).Info("Action executed successfully")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Action executed successfully",
		"data":    result,
	})
}

// RecognizeActions 识别文本中的操作
func (h *ActionHandler) RecognizeActions(c *gin.Context) {
	var req struct {
		Text string `json:"text" binding:"required"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 识别操作
	result, err := h.actionRecognizer.RecognizeActions(c.Request.Context(), req.Text)
	if err != nil {
		h.logger.WithError(err).Error("Failed to recognize actions")
		c.JSON(http.StatusInternalServerError, gin.H{
			"code":    500,
			"message": "Failed to recognize actions",
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Actions recognized successfully",
		"data":    result,
	})
}

// GetActionHistory 获取操作历史
func (h *ActionHandler) GetActionHistory(c *gin.Context) {
	// 获取查询参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))
	actionType := c.Query("action_type")
	status := c.Query("status")

	// 获取用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		userID = 1 // 默认用户ID
	}

	// 这里应该调用服务层获取操作历史
	// 暂时返回模拟数据
	mockHistory := []map[string]interface{}{
		{
			"id":          1,
			"action_id":   "restart_service_123",
			"action_type": "service_management",
			"command":     "systemctl restart nginx",
			"status":      "success",
			"executed_at": "2024-01-15T10:30:00Z",
			"duration":    "2.3s",
			"user_id":     userID,
		},
		{
			"id":          2,
			"action_id":   "check_status_456",
			"action_type": "monitoring",
			"command":     "systemctl status mysql",
			"status":      "success",
			"executed_at": "2024-01-15T10:25:00Z",
			"duration":    "1.1s",
			"user_id":     userID,
		},
	}

	// 应用过滤条件
	filteredHistory := mockHistory
	if actionType != "" {
		var filtered []map[string]interface{}
		for _, item := range filteredHistory {
			if item["action_type"] == actionType {
				filtered = append(filtered, item)
			}
		}
		filteredHistory = filtered
	}

	if status != "" {
		var filtered []map[string]interface{}
		for _, item := range filteredHistory {
			if item["status"] == status {
				filtered = append(filtered, item)
			}
		}
		filteredHistory = filtered
	}

	// 分页处理
	total := len(filteredHistory)
	start := (page - 1) * limit
	end := start + limit
	if start > total {
		start = total
	}
	if end > total {
		end = total
	}

	pagedHistory := filteredHistory[start:end]

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Action history retrieved successfully",
		"data": gin.H{
			"actions": pagedHistory,
			"pagination": gin.H{
				"page":     page,
				"limit":    limit,
				"total":    total,
				"pages":    (total + limit - 1) / limit,
				"has_next": end < total,
				"has_prev": page > 1,
			},
		},
	})
}

// ConfirmAction 确认高风险操作
func (h *ActionHandler) ConfirmAction(c *gin.Context) {
	actionID := c.Param("action_id")
	if actionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Action ID is required",
		})
		return
	}

	var req struct {
		Confirmed bool   `json:"confirmed" binding:"required"`
		Password  string `json:"password,omitempty"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Invalid request parameters",
			"error":   err.Error(),
		})
		return
	}

	// 获取用户ID
	userID, exists := middleware.GetUserIDFromContext(c)
	if !exists {
		c.JSON(http.StatusUnauthorized, gin.H{
			"code":    401,
			"message": "User authentication required",
		})
		return
	}

	// 这里应该验证用户权限和密码
	// 暂时简单处理
	if !req.Confirmed {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Action confirmation is required",
		})
		return
	}

	h.logger.WithFields(logrus.Fields{
		"action_id": actionID,
		"user_id":   userID,
		"confirmed": req.Confirmed,
	}).Info("Action confirmed by user")

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Action confirmed successfully",
		"data": gin.H{
			"action_id":    actionID,
			"confirmed":    true,
			"confirmed_by": userID,
			"confirmed_at": "now",
		},
	})
}

// GetActionStatus 获取操作状态
func (h *ActionHandler) GetActionStatus(c *gin.Context) {
	actionID := c.Param("action_id")
	if actionID == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"code":    400,
			"message": "Action ID is required",
		})
		return
	}

	// 这里应该从数据库查询操作状态
	// 暂时返回模拟数据
	mockStatus := gin.H{
		"action_id":    actionID,
		"status":       "success",
		"progress":     100,
		"started_at":   "2024-01-15T10:30:00Z",
		"completed_at": "2024-01-15T10:30:02Z",
		"duration":     "2.3s",
		"output":       "Service nginx restarted successfully",
		"error":        "",
	}

	c.JSON(http.StatusOK, gin.H{
		"code":    200,
		"message": "Action status retrieved successfully",
		"data":    mockStatus,
	})
}
