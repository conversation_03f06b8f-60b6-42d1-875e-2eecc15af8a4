/* AI运维管理平台 - 布局修复 */
/* 确保核心布局正常工作 */

/* ========================================
   核心布局修复
   ======================================== */

/* 确保主容器正确 */
.app-container {
  display: flex !important;
  flex-direction: column !important;
  height: 100vh !important;
  width: 100vw !important;
  overflow: hidden !important;
}

/* 确保导航栏正确 */
.app-navbar {
  display: flex !important;
  align-items: center !important;
  justify-content: space-between !important;
  height: 60px !important;
  flex-shrink: 0 !important;
  z-index: 50 !important;
}

/* 确保主布局区域正确 */
.app-layout {
  display: flex !important;
  flex: 1 !important;
  overflow: hidden !important;
}

/* 确保侧边栏正确 */
.sidebar {
  width: 240px !important;
  flex-shrink: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  background-color: var(--bg-secondary) !important;
  border-right: 1px solid var(--border-primary) !important;
}

.sidebar.collapsed {
  width: 60px !important;
}

/* 确保主对话区域正确 */
.chat-main {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  background-color: var(--bg-primary) !important;
  overflow: hidden !important;
}

/* 确保对话容器正确 */
.chat-container {
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

/* 确保消息区域正确 */
.chat-messages {
  flex: 1 !important;
  overflow-y: auto !important;
  padding: var(--space-4) 0 !important;
}

/* 确保右侧面板正确 */
.assistant-panel {
  width: 280px !important;
  flex-shrink: 0 !important;
  display: flex !important;
  flex-direction: column !important;
  background-color: var(--bg-secondary) !important;
  border-left: 1px solid var(--border-primary) !important;
}

.assistant-panel.collapsed {
  width: 60px !important;
}

/* ========================================
   输入区域修复
   ======================================== */

/* 确保输入区域正确 */
.chat-input-container {
  flex-shrink: 0 !important;
  padding: var(--space-4) !important;
  background-color: var(--bg-primary) !important;
  border-top: 1px solid var(--border-primary) !important;
}

.input-wrapper {
  display: flex !important;
  align-items: flex-end !important;
  gap: var(--space-3) !important;
  max-width: 100% !important;
}

.smart-input-container {
  flex: 1 !important;
  position: relative !important;
}

/* ========================================
   响应式修复
   ======================================== */

/* 移动端布局修复 */
@media (max-width: 768px) {
  .app-layout {
    position: relative !important;
  }
  
  .sidebar {
    position: absolute !important;
    top: 0 !important;
    left: 0 !important;
    height: 100% !important;
    z-index: 100 !important;
    transform: translateX(-100%) !important;
    transition: transform 0.3s ease !important;
  }
  
  .sidebar.show {
    transform: translateX(0) !important;
  }
  
  .assistant-panel {
    display: none !important;
  }
  
  .chat-main {
    width: 100% !important;
  }
}

/* 平板端布局修复 */
@media (min-width: 769px) and (max-width: 1024px) {
  .sidebar {
    width: 200px !important;
  }
  
  .assistant-panel {
    width: 240px !important;
  }
}

/* ========================================
   Z-index 层级修复
   ======================================== */

.app-navbar {
  z-index: 50 !important;
}

.sidebar {
  z-index: 40 !important;
}

.assistant-panel {
  z-index: 30 !important;
}

.settings-panel {
  z-index: 1000 !important;
}

.notification-toast {
  z-index: 1100 !important;
}

/* ========================================
   滚动条修复
   ======================================== */

.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: transparent;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--border-primary);
}

/* ========================================
   防止样式冲突
   ======================================== */

/* 重置可能冲突的样式 */
.app-container * {
  box-sizing: border-box;
}

/* 确保flex容器正常工作 */
.app-container,
.app-layout,
.sidebar,
.chat-main,
.chat-container,
.assistant-panel {
  min-height: 0;
  min-width: 0;
}

/* ========================================
   调试辅助（开发时使用）
   ======================================== */

/* 取消注释以显示布局边界 */
/*
.app-container { border: 2px solid red !important; }
.app-navbar { border: 2px solid blue !important; }
.app-layout { border: 2px solid green !important; }
.sidebar { border: 2px solid orange !important; }
.chat-main { border: 2px solid purple !important; }
.assistant-panel { border: 2px solid pink !important; }
*/
