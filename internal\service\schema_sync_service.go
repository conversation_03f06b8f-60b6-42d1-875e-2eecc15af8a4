package service

import (
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// SchemaSyncService 数据库模式同步服务
type SchemaSyncService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewSchemaSyncService 创建数据库模式同步服务
func NewSchemaSyncService(db *gorm.DB, logger *logrus.Logger) *SchemaSyncService {
	return &SchemaSyncService{
		db:     db,
		logger: logger,
	}
}

// GenerateAccurateSchemaPrompt 生成准确的数据库模式提示词
func (sss *SchemaSyncService) GenerateAccurateSchemaPrompt() string {
	var prompt strings.Builder

	prompt.WriteString("📊 **数据库表结构信息**（生成SQL时必须使用这些确切的表名和字段名）：\n\n")

	// 生成hosts表结构
	prompt.WriteString(sss.generateHostsTableSchema())
	prompt.WriteString("\n")

	// 生成alerts表结构
	prompt.WriteString(sss.generateAlertsTableSchema())
	prompt.WriteString("\n")

	// 生成users表结构
	prompt.WriteString(sss.generateUsersTableSchema())
	prompt.WriteString("\n")

	// 生成字段映射规则
	prompt.WriteString(sss.generateFieldMappingRules())

	return prompt.String()
}

// generateHostsTableSchema 生成hosts表结构
func (sss *SchemaSyncService) generateHostsTableSchema() string {
	return `### hosts表结构（主机管理表）：
CREATE TABLE hosts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    name VARCHAR(100) UNIQUE NOT NULL,  -- 🏷️ 主机名称
    ip_address VARCHAR(45) NOT NULL,  -- 🌐 IP地址
    port INTEGER NOT NULL DEFAULT 22,  -- 🔌 SSH端口
    username VARCHAR(50) NOT NULL,  -- 👤 登录用户名
    password_encrypted TEXT,  -- 🔑 加密后的密码
    ssh_key_path VARCHAR(255),  -- 🗝️ SSH密钥路径
    ssh_key_passphrase_encrypted TEXT,  -- 🔐 SSH密钥密码
    description TEXT,  -- 📝 主机描述
    status VARCHAR(20) NOT NULL DEFAULT 'unknown',  -- 📊 主机状态：online, offline, unknown
    os_type VARCHAR(20),  -- 💻 操作系统类型
    os_version VARCHAR(50),  -- 📋 操作系统版本
    cpu_cores INTEGER,  -- ⚙️ CPU核心数
    memory_gb INTEGER,  -- 💾 内存大小(GB)
    disk_gb INTEGER,  -- 💿 磁盘大小(GB)
    tags TEXT,  -- 🏷️ 标签
    environment VARCHAR(20) DEFAULT 'production',  -- 🌍 环境：production, testing, development
    group_name VARCHAR(50),  -- 👥 主机组
    monitoring_enabled BOOLEAN DEFAULT true,  -- 📡 是否启用监控
    backup_enabled BOOLEAN DEFAULT false,  -- 💾 是否启用备份
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    created_by INTEGER NOT NULL,
    last_connected DATETIME,  -- ⏰ 最后连接时间
    connection_count INTEGER DEFAULT 0,  -- 🔢 连接次数
    deleted_at DATETIME
);`
}

// generateAlertsTableSchema 生成alerts表结构
func (sss *SchemaSyncService) generateAlertsTableSchema() string {
	return `### alerts表结构（告警管理表）：
CREATE TABLE alerts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title VARCHAR(200) NOT NULL,  -- 📋 告警标题
    message TEXT NOT NULL,  -- 📝 告警详细信息
    level VARCHAR(20) NOT NULL,  -- 🚨 告警级别：critical, warning, info
    source VARCHAR(50) NOT NULL,  -- 📍 告警来源：system, host, application, monitoring, manual
    status VARCHAR(20) NOT NULL DEFAULT 'open',  -- 📊 告警状态：open, acknowledged, resolved, closed
    alert_time DATETIME NOT NULL,  -- ⏰ 告警时间
    acknowledged_time DATETIME,  -- ✅ 确认时间
    resolved_time DATETIME,  -- ✔️ 解决时间
    closed_time DATETIME,  -- 🔒 关闭时间
    host_id INTEGER,  -- 🖥️ 关联主机ID
    assigned_to INTEGER,  -- 👤 分配给用户ID
    created_by INTEGER,  -- 👤 创建者ID
    rule_id VARCHAR(50),  -- 📏 规则ID
    fingerprint VARCHAR(64),  -- 🔍 告警指纹
    notification_sent BOOLEAN DEFAULT false,  -- 📧 是否已发送通知
    escalation_level INTEGER DEFAULT 0,  -- 📈 升级级别
    parent_alert_id INTEGER,  -- 👨‍👩‍👧‍👦 父告警ID
    metadata TEXT,  -- 📊 元数据(JSON格式)
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME
);`
}

// generateUsersTableSchema 生成users表结构
func (sss *SchemaSyncService) generateUsersTableSchema() string {
	return `### users表结构（用户管理表）：
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,  -- 👤 用户名
    email VARCHAR(100) UNIQUE NOT NULL,  -- 📧 邮箱地址
    password_hash VARCHAR(255) NOT NULL,  -- 🔑 密码哈希
    full_name VARCHAR(100),  -- 📝 全名
    role VARCHAR(20) NOT NULL DEFAULT 'user',  -- 👥 角色：admin, user, viewer
    status VARCHAR(20) NOT NULL DEFAULT 'active',  -- 📊 状态：active, inactive, suspended
    last_login DATETIME,  -- ⏰ 最后登录时间
    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
    deleted_at DATETIME
);`
}

// generateFieldMappingRules 生成字段映射规则
func (sss *SchemaSyncService) generateFieldMappingRules() string {
	return `🚨 **重要字段映射规则**：

**hosts表字段映射**：
- 用户说"密码" → 数据库字段是 password_encrypted
- 用户说"IP地址/IP" → 数据库字段是 ip_address  
- 用户说"主机名/名称" → 数据库字段是 name
- 用户说"用户名" → 数据库字段是 username
- 用户说"端口" → 数据库字段是 port
- 用户说"环境" → 数据库字段是 environment
- 用户说"状态" → 数据库字段是 status
- 用户说"描述" → 数据库字段是 description
- 用户说"最后连接时间" → 数据库字段是 last_connected

**alerts表字段映射**：
- 用户说"告警标题/标题" → 数据库字段是 title
- 用户说"告警内容/描述/详情/消息" → 数据库字段是 message
- 用户说"告警级别/严重程度/优先级/级别" → 数据库字段是 level
- 用户说"告警来源/来源" → 数据库字段是 source
- 用户说"告警状态/状态" → 数据库字段是 status
- 用户说"告警时间/时间" → 数据库字段是 alert_time
- 用户说"主机ID/主机" → 数据库字段是 host_id
- 用户说"确认时间" → 数据库字段是 acknowledged_time
- 用户说"解决时间" → 数据库字段是 resolved_time

**users表字段映射**：
- 用户说"用户名" → 数据库字段是 username
- 用户说"邮箱/邮件" → 数据库字段是 email
- 用户说"全名/姓名" → 数据库字段是 full_name
- 用户说"角色" → 数据库字段是 role
- 用户说"状态" → 数据库字段是 status
- 用户说"最后登录" → 数据库字段是 last_login

**查询类型识别规则**：
- 告警相关：检查告警、查看告警、告警列表、告警状态、告警信息 → 查询alerts表
- 主机相关：主机列表、主机状态、主机信息、服务器列表 → 查询hosts表
- 用户相关：用户列表、用户信息、用户管理 → 查询users表

`
}

// GetTableColumns 获取表的实际列信息
func (sss *SchemaSyncService) GetTableColumns(tableName string) ([]string, error) {
	var columns []string

	rows, err := sss.db.Raw(fmt.Sprintf("PRAGMA table_info(%s)", tableName)).Rows()
	if err != nil {
		return nil, fmt.Errorf("failed to get table info: %w", err)
	}
	defer rows.Close()

	for rows.Next() {
		var cid int
		var name, dataType string
		var notNull, pk int
		var defaultValue interface{}

		err := rows.Scan(&cid, &name, &dataType, &notNull, &defaultValue, &pk)
		if err != nil {
			continue
		}

		columns = append(columns, name)
	}

	return columns, nil
}

// ValidateTableSchema 验证表结构
func (sss *SchemaSyncService) ValidateTableSchema(tableName string, expectedColumns []string) error {
	actualColumns, err := sss.GetTableColumns(tableName)
	if err != nil {
		return fmt.Errorf("failed to get actual columns for table %s: %w", tableName, err)
	}

	// 检查缺失的列
	actualColumnMap := make(map[string]bool)
	for _, col := range actualColumns {
		actualColumnMap[col] = true
	}

	var missingColumns []string
	for _, expectedCol := range expectedColumns {
		if !actualColumnMap[expectedCol] {
			missingColumns = append(missingColumns, expectedCol)
		}
	}

	if len(missingColumns) > 0 {
		sss.logger.WithFields(logrus.Fields{
			"table":           tableName,
			"missing_columns": missingColumns,
			"actual_columns":  actualColumns,
		}).Warn("Table schema validation failed - missing columns")

		return fmt.Errorf("table %s is missing columns: %v", tableName, missingColumns)
	}

	sss.logger.WithField("table", tableName).Info("Table schema validation passed")
	return nil
}

// SyncAllSchemas 同步所有表结构
func (sss *SchemaSyncService) SyncAllSchemas() error {
	// 验证hosts表
	hostsColumns := []string{"id", "name", "ip_address", "port", "username", "password_encrypted", "status", "environment", "last_connected"}
	if err := sss.ValidateTableSchema("hosts", hostsColumns); err != nil {
		sss.logger.WithError(err).Error("Hosts table validation failed")
	}

	// 验证alerts表
	alertsColumns := []string{"id", "title", "message", "level", "source", "status", "alert_time", "host_id"}
	if err := sss.ValidateTableSchema("alerts", alertsColumns); err != nil {
		sss.logger.WithError(err).Error("Alerts table validation failed")
	}

	// 验证users表
	usersColumns := []string{"id", "username", "email", "full_name", "role", "status", "last_login"}
	if err := sss.ValidateTableSchema("users", usersColumns); err != nil {
		sss.logger.WithError(err).Error("Users table validation failed")
	}

	return nil
}
