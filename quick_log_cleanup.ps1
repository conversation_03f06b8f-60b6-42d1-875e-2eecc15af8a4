# Quick Log Cleanup Script
Write-Host "AI Ops Platform Log Cleanup" -ForegroundColor Green

# Check current log file
$currentLog = "logs\aiops.log"
if (Test-Path $currentLog) {
    $logFile = Get-Item $currentLog
    $sizeMB = [math]::Round($logFile.Length / 1MB, 2)
    Write-Host "Current log file size: $sizeMB MB" -ForegroundColor Yellow

    # Rotate if log file is larger than 5MB
    if ($logFile.Length -gt 5MB) {
        Write-Host "Log file too large, starting rotation..." -ForegroundColor Yellow

        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupName = "logs\aiops_backup_$timestamp.log"

        # Backup current log
        Copy-Item $currentLog $backupName
        Write-Host "Log backed up to: $backupName" -ForegroundColor Green

        # Clear current log file
        Clear-Content $currentLog
        Write-Host "Current log file cleared" -ForegroundColor Green

        $newSizeMB = [math]::Round((Get-Item $currentLog).Length / 1MB, 2)
        Write-Host "After cleanup log size: $newSizeMB MB" -ForegroundColor Green
    } else {
        Write-Host "Log file size is normal, no rotation needed" -ForegroundColor Green
    }
}

# Clean old backup files (keep 3 days)
$cutoffDate = (Get-Date).AddDays(-3)
$oldBackups = Get-ChildItem "logs\aiops_backup_*.log" -ErrorAction SilentlyContinue | Where-Object { $_.LastWriteTime -lt $cutoffDate }

if ($oldBackups) {
    $totalSize = ($oldBackups | Measure-Object -Property Length -Sum).Sum
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)

    Write-Host "Found $($oldBackups.Count) expired backup files, total size: $totalSizeMB MB" -ForegroundColor Yellow

    foreach ($backup in $oldBackups) {
        $backupSizeMB = [math]::Round($backup.Length / 1MB, 2)
        Remove-Item $backup.FullName -Force
        Write-Host "Deleted: $($backup.Name) ($backupSizeMB MB)" -ForegroundColor Red
    }

    Write-Host "Freed disk space: $totalSizeMB MB" -ForegroundColor Green
} else {
    Write-Host "No expired backup files found" -ForegroundColor Green
}

# Show final status
Write-Host ""
Write-Host "Final Status:" -ForegroundColor Cyan
$allLogs = Get-ChildItem "logs\*.log" -ErrorAction SilentlyContinue
if ($allLogs) {
    $totalSize = ($allLogs | Measure-Object -Property Length -Sum).Sum
    $totalSizeMB = [math]::Round($totalSize / 1MB, 2)
    Write-Host "   - Log files count: $($allLogs.Count)" -ForegroundColor White
    Write-Host "   - Total space used: $totalSizeMB MB" -ForegroundColor White
} else {
    Write-Host "   - No log files" -ForegroundColor White
}

Write-Host ""
Write-Host "Log cleanup completed!" -ForegroundColor Green
