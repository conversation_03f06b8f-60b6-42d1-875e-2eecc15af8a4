package auth

import (
	"errors"
	"fmt"
	"time"

	"aiops-platform/internal/config"

	"github.com/golang-jwt/jwt/v5"
)

// JWTManager JWT管理器
type JWTManager struct {
	secretKey  []byte
	accessTTL  time.Duration
	refreshTTL time.Duration
	issuer     string
	blacklist  TokenBlacklist
}

// TokenClaims JWT声明
type TokenClaims struct {
	UserID    int64  `json:"user_id"`
	Username  string `json:"username"`
	Role      string `json:"role"`
	SessionID string `json:"session_id"`
	TokenType string `json:"token_type"` // access, refresh
	jwt.RegisteredClaims
}

// TokenPair Token对
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	ExpiresIn    int64  `json:"expires_in"`
	TokenType    string `json:"token_type"`
}

// TokenBlacklist Token黑名单接口
type TokenBlacklist interface {
	Add(tokenID string, expiry time.Time) error
	IsBlacklisted(tokenID string) bool
	Cleanup() error
}

// NewJWTManager 创建JWT管理器
func NewJWTManager(cfg config.JWTConfig, blacklist TokenBlacklist) *JWTManager {
	return &JWTManager{
		secretKey:  []byte(cfg.Secret),
		accessTTL:  cfg.AccessTokenTTL,
		refreshTTL: cfg.RefreshTokenTTL,
		issuer:     cfg.Issuer,
		blacklist:  blacklist,
	}
}

// GenerateTokenPair 生成Token对
func (jm *JWTManager) GenerateTokenPair(userID int64, username, role, sessionID string) (*TokenPair, error) {
	now := time.Now()

	// 生成Access Token
	accessClaims := &TokenClaims{
		UserID:    userID,
		Username:  username,
		Role:      role,
		SessionID: sessionID,
		TokenType: "access",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        generateTokenID(),
			Issuer:    jm.issuer,
			Subject:   fmt.Sprintf("%d", userID),
			Audience:  []string{"aiops-platform"},
			ExpiresAt: jwt.NewNumericDate(now.Add(jm.accessTTL)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}

	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString(jm.secretKey)
	if err != nil {
		return nil, fmt.Errorf("failed to sign access token: %w", err)
	}

	// 生成Refresh Token
	refreshClaims := &TokenClaims{
		UserID:    userID,
		Username:  username,
		Role:      role,
		SessionID: sessionID,
		TokenType: "refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			ID:        generateTokenID(),
			Issuer:    jm.issuer,
			Subject:   fmt.Sprintf("%d", userID),
			Audience:  []string{"aiops-platform"},
			ExpiresAt: jwt.NewNumericDate(now.Add(jm.refreshTTL)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
		},
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString(jm.secretKey)
	if err != nil {
		return nil, fmt.Errorf("failed to sign refresh token: %w", err)
	}

	return &TokenPair{
		AccessToken:  accessTokenString,
		RefreshToken: refreshTokenString,
		ExpiresIn:    int64(jm.accessTTL.Seconds()),
		TokenType:    "Bearer",
	}, nil
}

// ValidateToken 验证Token
func (jm *JWTManager) ValidateToken(tokenString string) (*TokenClaims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &TokenClaims{}, func(token *jwt.Token) (interface{}, error) {
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return jm.secretKey, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to parse token: %w", err)
	}

	claims, ok := token.Claims.(*TokenClaims)
	if !ok || !token.Valid {
		return nil, errors.New("invalid token")
	}

	// 检查Token是否在黑名单中
	if jm.blacklist.IsBlacklisted(claims.ID) {
		return nil, errors.New("token is blacklisted")
	}

	// 检查Token是否过期
	if claims.ExpiresAt != nil && claims.ExpiresAt.Before(time.Now()) {
		return nil, errors.New("token is expired")
	}

	return claims, nil
}

// RefreshToken 刷新Token
func (jm *JWTManager) RefreshToken(refreshTokenString string) (*TokenPair, error) {
	claims, err := jm.ValidateToken(refreshTokenString)
	if err != nil {
		return nil, fmt.Errorf("invalid refresh token: %w", err)
	}

	if claims.TokenType != "refresh" {
		return nil, errors.New("not a refresh token")
	}

	// 将旧的refresh token加入黑名单
	if err := jm.blacklist.Add(claims.ID, claims.ExpiresAt.Time); err != nil {
		return nil, fmt.Errorf("failed to blacklist old token: %w", err)
	}

	// 生成新的Token对
	return jm.GenerateTokenPair(claims.UserID, claims.Username, claims.Role, claims.SessionID)
}

// RevokeToken 撤销Token
func (jm *JWTManager) RevokeToken(tokenString string) error {
	claims, err := jm.ValidateToken(tokenString)
	if err != nil {
		return fmt.Errorf("invalid token: %w", err)
	}

	// 将Token加入黑名单
	return jm.blacklist.Add(claims.ID, claims.ExpiresAt.Time)
}

// ExtractTokenFromHeader 从Header中提取Token
func ExtractTokenFromHeader(authHeader string) (string, error) {
	if authHeader == "" {
		return "", errors.New("authorization header is empty")
	}

	const bearerPrefix = "Bearer "
	if len(authHeader) < len(bearerPrefix) || authHeader[:len(bearerPrefix)] != bearerPrefix {
		return "", errors.New("invalid authorization header format")
	}

	return authHeader[len(bearerPrefix):], nil
}

// generateTokenID 生成Token ID
func generateTokenID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// MemoryTokenBlacklist 内存Token黑名单实现
type MemoryTokenBlacklist struct {
	tokens map[string]time.Time
}

// NewMemoryTokenBlacklist 创建内存Token黑名单
func NewMemoryTokenBlacklist() *MemoryTokenBlacklist {
	return &MemoryTokenBlacklist{
		tokens: make(map[string]time.Time),
	}
}

// Add 添加Token到黑名单
func (mtb *MemoryTokenBlacklist) Add(tokenID string, expiry time.Time) error {
	mtb.tokens[tokenID] = expiry
	return nil
}

// IsBlacklisted 检查Token是否在黑名单中
func (mtb *MemoryTokenBlacklist) IsBlacklisted(tokenID string) bool {
	expiry, exists := mtb.tokens[tokenID]
	if !exists {
		return false
	}

	// 如果Token已过期，从黑名单中移除
	if time.Now().After(expiry) {
		delete(mtb.tokens, tokenID)
		return false
	}

	return true
}

// Cleanup 清理过期的Token
func (mtb *MemoryTokenBlacklist) Cleanup() error {
	now := time.Now()
	for tokenID, expiry := range mtb.tokens {
		if now.After(expiry) {
			delete(mtb.tokens, tokenID)
		}
	}
	return nil
}

// UserContext 用户上下文
type UserContext struct {
	UserID    int64  `json:"user_id"`
	Username  string `json:"username"`
	Role      string `json:"role"`
	SessionID string `json:"session_id"`
}

// HasRole 检查用户是否有指定角色
func (uc *UserContext) HasRole(role string) bool {
	return uc.Role == role
}

// HasAnyRole 检查用户是否有任意指定角色
func (uc *UserContext) HasAnyRole(roles ...string) bool {
	for _, role := range roles {
		if uc.Role == role {
			return true
		}
	}
	return false
}

// IsAdmin 检查用户是否为管理员
func (uc *UserContext) IsAdmin() bool {
	return uc.HasAnyRole("admin", "super_admin")
}

// IsSuperAdmin 检查用户是否为超级管理员
func (uc *UserContext) IsSuperAdmin() bool {
	return uc.HasRole("super_admin")
}

// CanManageUser 检查是否可以管理指定用户
func (uc *UserContext) CanManageUser(targetUserRole string) bool {
	if uc.IsSuperAdmin() {
		return true
	}

	if uc.HasRole("admin") {
		// 管理员不能管理超级管理员和其他管理员
		return targetUserRole != "super_admin" && targetUserRole != "admin"
	}

	return false
}

// GetRoleLevel 获取角色级别
func (uc *UserContext) GetRoleLevel() int {
	switch uc.Role {
	case "super_admin":
		return 4
	case "admin":
		return 3
	case "operator":
		return 2
	case "viewer":
		return 1
	default:
		return 0
	}
}
