package service

import (
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// IntelligentFormatter 智能结果格式化器
type IntelligentFormatter struct {
	logger *logrus.Logger
}

// NewIntelligentFormatter 创建智能结果格式化器
func NewIntelligentFormatter(logger *logrus.Logger) *IntelligentFormatter {
	return &IntelligentFormatter{
		logger: logger,
	}
}

// QueryContext 查询上下文
type QueryContext struct {
	SQL         string                   `json:"sql"`
	Operation   string                   `json:"operation"`
	Description string                   `json:"description"`
	TableName   string                   `json:"table_name"`
	QueryType   string                   `json:"query_type"`
	Results     []map[string]interface{} `json:"results"`
}

// FormatQueryResults 智能格式化查询结果
func (inf *IntelligentFormatter) FormatQueryResults(ctx *QueryContext) string {
	if len(ctx.Results) == 0 {
		return inf.formatEmptyResults(ctx)
	}

	// 根据查询类型选择格式化器
	switch ctx.QueryType {
	case "alerts", "alert":
		return inf.formatAlertsResults(ctx)
	case "hosts", "host":
		return inf.formatHostsResults(ctx)
	case "users", "user":
		return inf.formatUsersResults(ctx)
	default:
		// 自动检测查询类型
		detectedType := inf.detectQueryType(ctx.SQL, ctx.Results)
		ctx.QueryType = detectedType
		return inf.FormatQueryResults(ctx)
	}
}

// detectQueryType 自动检测查询类型
func (inf *IntelligentFormatter) detectQueryType(sql string, results []map[string]interface{}) string {
	sqlLower := strings.ToLower(sql)

	// 检查SQL中的表名
	if strings.Contains(sqlLower, "from alerts") || strings.Contains(sqlLower, "alerts.") {
		return "alerts"
	}
	if strings.Contains(sqlLower, "from hosts") || strings.Contains(sqlLower, "hosts.") {
		return "hosts"
	}
	if strings.Contains(sqlLower, "from users") || strings.Contains(sqlLower, "users.") {
		return "users"
	}

	// 检查结果字段
	if len(results) > 0 {
		firstRow := results[0]

		// 告警表特征字段
		if _, hasLevel := firstRow["level"]; hasLevel {
			if _, hasAlertTime := firstRow["alert_time"]; hasAlertTime {
				return "alerts"
			}
		}

		// 主机表特征字段
		if _, hasIPAddress := firstRow["ip_address"]; hasIPAddress {
			return "hosts"
		}

		// 用户表特征字段
		if _, hasUsername := firstRow["username"]; hasUsername {
			if _, hasEmail := firstRow["email"]; hasEmail {
				return "users"
			}
		}
	}

	return "generic"
}

// formatAlertsResults 格式化告警结果
func (inf *IntelligentFormatter) formatAlertsResults(ctx *QueryContext) string {
	var result strings.Builder

	result.WriteString(fmt.Sprintf("📊 %s (共 %d 条记录)\n\n", ctx.Description, len(ctx.Results)))

	// 表头
	result.WriteString("```\n")
	result.WriteString("序号 告警标题                级别     状态       告警时间        主机\n")
	result.WriteString("---- -------------------- -------- ---------- --------------- --------\n")

	// 数据行
	for i, row := range ctx.Results {
		title := inf.truncateString(inf.getStringValue(row, "title"), 20)
		level := inf.formatAlertLevel(inf.getStringValue(row, "level"))
		status := inf.formatAlertStatus(inf.getStringValue(row, "status"))
		alertTime := inf.formatDateTime(row, "alert_time")
		hostInfo := inf.getHostInfo(row)

		result.WriteString(fmt.Sprintf("%-4d %-20s %-8s %-10s %-15s %s\n",
			i+1, title, level, status, alertTime, hostInfo))
	}

	result.WriteString("```\n\n")

	// 统计信息
	result.WriteString(inf.generateAlertStatistics(ctx.Results))

	// SQL信息
	result.WriteString(fmt.Sprintf("\n🔍 执行的SQL：%s", ctx.SQL))

	return result.String()
}

// formatHostsResults 格式化主机结果
func (inf *IntelligentFormatter) formatHostsResults(ctx *QueryContext) string {
	var result strings.Builder

	result.WriteString(fmt.Sprintf("📊 %s (共 %d 条记录)\n\n", ctx.Description, len(ctx.Results)))

	// 表头
	result.WriteString("```\n")
	result.WriteString("序号 IP地址           主机名               状态   环境        创建时间\n")
	result.WriteString("---- --------------- -------------------- ------ ----------- --------\n")

	// 数据行
	for i, row := range ctx.Results {
		ipAddress := inf.getStringValue(row, "ip_address")
		name := inf.truncateString(inf.getStringValue(row, "name"), 20)
		status := inf.formatHostStatus(inf.getStringValue(row, "status"))
		environment := inf.getStringValue(row, "environment")
		createdAt := inf.formatDate(row, "created_at")

		result.WriteString(fmt.Sprintf("%-4d %-15s %-20s %-6s %-11s %s\n",
			i+1, ipAddress, name, status, environment, createdAt))
	}

	result.WriteString("```\n\n")

	// 统计信息
	result.WriteString(inf.generateHostStatistics(ctx.Results))

	// SQL信息
	result.WriteString(fmt.Sprintf("\n🔍 执行的SQL：%s", ctx.SQL))

	return result.String()
}

// formatUsersResults 格式化用户结果
func (inf *IntelligentFormatter) formatUsersResults(ctx *QueryContext) string {
	var result strings.Builder

	result.WriteString(fmt.Sprintf("📊 %s (共 %d 条记录)\n\n", ctx.Description, len(ctx.Results)))

	// 表头
	result.WriteString("```\n")
	result.WriteString("序号 用户名           全名                 角色     状态       最后登录\n")
	result.WriteString("---- --------------- -------------------- -------- ---------- --------\n")

	// 数据行
	for i, row := range ctx.Results {
		username := inf.getStringValue(row, "username")
		fullName := inf.truncateString(inf.getStringValue(row, "full_name"), 20)
		role := inf.formatUserRole(inf.getStringValue(row, "role"))
		status := inf.formatUserStatus(inf.getStringValue(row, "status"))
		lastLogin := inf.formatDateTime(row, "last_login")

		result.WriteString(fmt.Sprintf("%-4d %-15s %-20s %-8s %-10s %s\n",
			i+1, username, fullName, role, status, lastLogin))
	}

	result.WriteString("```\n\n")

	// 统计信息
	result.WriteString(inf.generateUserStatistics(ctx.Results))

	// SQL信息
	result.WriteString(fmt.Sprintf("\n🔍 执行的SQL：%s", ctx.SQL))

	return result.String()
}

// formatEmptyResults 格式化空结果
func (inf *IntelligentFormatter) formatEmptyResults(ctx *QueryContext) string {
	var result strings.Builder

	result.WriteString(fmt.Sprintf("📊 %s (共 0 条记录)\n\n", ctx.Description))
	result.WriteString("🔍 没有找到符合条件的记录\n\n")
	result.WriteString(fmt.Sprintf("🔍 执行的SQL：%s", ctx.SQL))

	return result.String()
}

// 辅助方法
func (inf *IntelligentFormatter) getStringValue(row map[string]interface{}, key string) string {
	if val, exists := row[key]; exists && val != nil {
		return fmt.Sprintf("%v", val)
	}
	return ""
}

func (inf *IntelligentFormatter) truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen-3] + "..."
}

func (inf *IntelligentFormatter) formatAlertLevel(level string) string {
	switch strings.ToLower(level) {
	case "critical":
		return "🔴严重"
	case "warning":
		return "🟡警告"
	case "info":
		return "🔵信息"
	default:
		return level
	}
}

func (inf *IntelligentFormatter) formatAlertStatus(status string) string {
	switch strings.ToLower(status) {
	case "open":
		return "🔓开放"
	case "acknowledged":
		return "✅已确认"
	case "resolved":
		return "✔️已解决"
	case "closed":
		return "🔒已关闭"
	default:
		return status
	}
}

func (inf *IntelligentFormatter) formatHostStatus(status string) string {
	switch strings.ToLower(status) {
	case "online":
		return "🟢在线"
	case "offline":
		return "🔴离线"
	case "unknown":
		return "⚪未知"
	default:
		return status
	}
}

func (inf *IntelligentFormatter) formatUserRole(role string) string {
	switch strings.ToLower(role) {
	case "admin":
		return "👑管理员"
	case "user":
		return "👤用户"
	case "viewer":
		return "👁️观察者"
	default:
		return role
	}
}

func (inf *IntelligentFormatter) formatUserStatus(status string) string {
	switch strings.ToLower(status) {
	case "active":
		return "🟢活跃"
	case "inactive":
		return "🔴非活跃"
	case "suspended":
		return "⏸️暂停"
	default:
		return status
	}
}

func (inf *IntelligentFormatter) formatDateTime(row map[string]interface{}, key string) string {
	if val, exists := row[key]; exists && val != nil {
		if timeStr, ok := val.(string); ok && timeStr != "" {
			if t, err := time.Parse("2006-01-02 15:04:05", timeStr); err == nil {
				return t.Format("01-02 15:04")
			}
			if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
				return t.Format("01-02 15:04")
			}
		}
	}
	return "未知"
}

func (inf *IntelligentFormatter) formatDate(row map[string]interface{}, key string) string {
	if val, exists := row[key]; exists && val != nil {
		if timeStr, ok := val.(string); ok && timeStr != "" {
			if t, err := time.Parse("2006-01-02 15:04:05", timeStr); err == nil {
				return t.Format("01-02 15:04")
			}
			if t, err := time.Parse(time.RFC3339, timeStr); err == nil {
				return t.Format("01-02 15:04")
			}
		}
	}
	return "未知"
}

func (inf *IntelligentFormatter) getHostInfo(row map[string]interface{}) string {
	if hostID := inf.getStringValue(row, "host_id"); hostID != "" && hostID != "0" {
		return fmt.Sprintf("主机%s", hostID)
	}
	return "无关联"
}

func (inf *IntelligentFormatter) generateAlertStatistics(results []map[string]interface{}) string {
	var critical, warning, info, open, acknowledged, resolved int

	for _, row := range results {
		level := strings.ToLower(inf.getStringValue(row, "level"))
		status := strings.ToLower(inf.getStringValue(row, "status"))

		switch level {
		case "critical":
			critical++
		case "warning":
			warning++
		case "info":
			info++
		}

		switch status {
		case "open":
			open++
		case "acknowledged":
			acknowledged++
		case "resolved":
			resolved++
		}
	}

	return fmt.Sprintf("📈 级别统计：🔴严重 %d条 | 🟡警告 %d条 | 🔵信息 %d条\n📊 状态统计：🔓开放 %d条 | ✅已确认 %d条 | ✔️已解决 %d条",
		critical, warning, info, open, acknowledged, resolved)
}

func (inf *IntelligentFormatter) generateHostStatistics(results []map[string]interface{}) string {
	var online, offline, unknown int

	for _, row := range results {
		status := strings.ToLower(inf.getStringValue(row, "status"))
		switch status {
		case "online":
			online++
		case "offline":
			offline++
		case "unknown":
			unknown++
		}
	}

	return fmt.Sprintf("📈 状态统计：🟢在线 %d台 | 🔴离线 %d台 | ⚪未知 %d台", online, offline, unknown)
}

func (inf *IntelligentFormatter) generateUserStatistics(results []map[string]interface{}) string {
	var admin, user, viewer, active, inactive int

	for _, row := range results {
		role := strings.ToLower(inf.getStringValue(row, "role"))
		status := strings.ToLower(inf.getStringValue(row, "status"))

		switch role {
		case "admin":
			admin++
		case "user":
			user++
		case "viewer":
			viewer++
		}

		switch status {
		case "active":
			active++
		case "inactive":
			inactive++
		}
	}

	return fmt.Sprintf("📈 角色统计：👑管理员 %d人 | 👤用户 %d人 | 👁️观察者 %d人\n📊 状态统计：🟢活跃 %d人 | 🔴非活跃 %d人",
		admin, user, viewer, active, inactive)
}
