# API接口设计

## 🔌 API概述

基于RESTful设计原则，提供完整的API接口，支持前端应用和第三方集成。

### 设计原则
- **RESTful风格**：资源导向的URL设计
- **统一响应格式**：标准化的JSON响应
- **版本控制**：API版本管理
- **安全认证**：JWT Token认证
- **错误处理**：统一的错误码和消息

### 基础信息
- **Base URL**: `https://api.aiops.example.com/api/v1`
- **认证方式**: Bearer Token (JWT)
- **内容类型**: `application/json`
- **字符编码**: `UTF-8`

## 🔐 认证相关接口

### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
    "username": "admin",
    "password": "password123",
    "remember_me": true,
    "captcha": "abc123"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "登录成功",
    "data": {
        "access_token": "eyJhbGciOiJIUzI1NiIs...",
        "refresh_token": "eyJhbGciOiJIUzI1NiIs...",
        "expires_in": 900,
        "user": {
            "id": 1,
            "username": "admin",
            "full_name": "系统管理员",
            "role": "super_admin",
            "avatar_url": "/avatars/admin.jpg"
        }
    }
}
```

### 刷新Token
```http
POST /api/v1/auth/refresh
Authorization: Bearer <refresh_token>
```

### 用户登出
```http
POST /api/v1/auth/logout
Authorization: Bearer <access_token>
```

### 获取用户信息
```http
GET /api/v1/auth/profile
Authorization: Bearer <access_token>
```

## 🤖 AI对话接口

### 创建对话会话
```http
POST /api/v1/chat/sessions
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "title": "运维咨询会话"
}
```

### 发送消息
```http
POST /api/v1/chat/message
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "content": "帮我查看服务器web-01的CPU使用率",
    "stream": true
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "处理成功",
    "data": {
        "message_id": "msg_123456",
        "response": "我来帮您查看web-01服务器的CPU使用率...",
        "intent": "monitoring_query",
        "tool_calls": [
            {
                "function": "get_host_metrics",
                "parameters": {
                    "host_name": "web-01",
                    "metric": "cpu_usage"
                }
            }
        ],
        "processing_time": 1250
    }
}
```

### 获取会话列表
```http
GET /api/v1/chat/sessions?page=1&limit=20&status=active
Authorization: Bearer <access_token>
```

### 获取会话详情
```http
GET /api/v1/chat/sessions/{session_id}
Authorization: Bearer <access_token>
```

## 🖥️ 主机管理接口

### 获取主机列表
```http
GET /api/v1/hosts?page=1&limit=20&status=online&group=web
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "hosts": [
            {
                "id": 1,
                "name": "web-01",
                "ip_address": "************",
                "port": 22,
                "status": "online",
                "os_type": "linux",
                "group_name": "web",
                "last_connected": "2024-01-15T10:30:00Z",
                "created_at": "2024-01-01T00:00:00Z"
            }
        ],
        "pagination": {
            "page": 1,
            "limit": 20,
            "total": 50,
            "pages": 3
        }
    }
}
```

### 添加主机
```http
POST /api/v1/hosts
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "name": "web-02",
    "ip_address": "************",
    "port": 22,
    "username": "root",
    "password": "password123",
    "description": "Web服务器2",
    "group_name": "web",
    "tags": ["production", "nginx"]
}
```

### 更新主机信息
```http
PUT /api/v1/hosts/{host_id}
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "description": "更新后的描述",
    "tags": ["production", "nginx", "updated"]
}
```

### 删除主机
```http
DELETE /api/v1/hosts/{host_id}
Authorization: Bearer <access_token>
```

### 测试主机连接
```http
POST /api/v1/hosts/{host_id}/test
Authorization: Bearer <access_token>
```

### 执行命令
```http
POST /api/v1/hosts/{host_id}/execute
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "command": "ps aux | grep nginx",
    "timeout": 30,
    "working_directory": "/var/log"
}
```

**响应示例**:
```json
{
    "code": 200,
    "message": "命令执行成功",
    "data": {
        "command": "ps aux | grep nginx",
        "exit_code": 0,
        "stdout": "root      1234  0.0  0.1  12345  6789 ?        S    10:30   0:00 nginx: master process\n",
        "stderr": "",
        "duration": 125,
        "executed_at": "2024-01-15T10:30:00Z"
    }
}
```

## 🚨 监控告警接口

### 获取告警列表
```http
GET /api/v1/alerts?page=1&limit=20&level=critical&status=open
Authorization: Bearer <access_token>
```

### 创建告警
```http
POST /api/v1/alerts
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "title": "CPU使用率过高",
    "message": "服务器web-01的CPU使用率达到95%",
    "level": "critical",
    "source": "monitoring",
    "host_id": 1,
    "metadata": {
        "metric": "cpu_usage",
        "value": 95.5,
        "threshold": 90
    }
}
```

### 确认告警
```http
PUT /api/v1/alerts/{alert_id}/acknowledge
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "comment": "已确认，正在处理"
}
```

### 解决告警
```http
PUT /api/v1/alerts/{alert_id}/resolve
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "resolution": "已重启服务，CPU使用率恢复正常"
}
```

## 📊 统计报表接口

### 系统概览统计
```http
GET /api/v1/stats/overview
Authorization: Bearer <access_token>
```

**响应示例**:
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "hosts": {
            "total": 50,
            "online": 45,
            "offline": 5
        },
        "alerts": {
            "total": 120,
            "open": 15,
            "critical": 3
        },
        "operations": {
            "today": 256,
            "success_rate": 98.5
        },
        "chat_sessions": {
            "active": 8,
            "today": 25
        }
    }
}
```

### 主机统计
```http
GET /api/v1/stats/hosts?period=7d&group_by=status
Authorization: Bearer <access_token>
```

### 告警统计
```http
GET /api/v1/stats/alerts?period=30d&group_by=level
Authorization: Bearer <access_token>
```

### 操作统计
```http
GET /api/v1/stats/operations?period=24h&host_id=1
Authorization: Bearer <access_token>
```

## 👥 用户管理接口

### 获取用户列表
```http
GET /api/v1/users?page=1&limit=20&role=operator&active=true
Authorization: Bearer <access_token>
```

### 创建用户
```http
POST /api/v1/users
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "username": "operator01",
    "password": "password123",
    "email": "<EMAIL>",
    "full_name": "运维工程师01",
    "role": "operator",
    "department": "运维部"
}
```

### 更新用户信息
```http
PUT /api/v1/users/{user_id}
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "full_name": "高级运维工程师01",
    "department": "技术部",
    "phone": "13800138000"
}
```

### 重置用户密码
```http
POST /api/v1/users/{user_id}/reset-password
Authorization: Bearer <access_token>
Content-Type: application/json

{
    "new_password": "newpassword123",
    "force_change": true
}
```

## 📝 响应格式规范

### 成功响应
```json
{
    "code": 200,
    "message": "操作成功",
    "data": {
        // 具体数据
    },
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### 错误响应
```json
{
    "code": 400,
    "message": "请求参数错误",
    "error": {
        "type": "validation_error",
        "details": [
            {
                "field": "username",
                "message": "用户名不能为空"
            }
        ]
    },
    "timestamp": "2024-01-15T10:30:00Z"
}
```

### 分页响应
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "items": [...],
        "pagination": {
            "page": 1,
            "limit": 20,
            "total": 100,
            "pages": 5,
            "has_next": true,
            "has_prev": false
        }
    }
}
```

## 🔍 错误码定义

| 错误码 | 说明 | 示例 |
|--------|------|------|
| 200 | 成功 | 操作成功 |
| 400 | 请求错误 | 参数格式错误 |
| 401 | 未认证 | Token无效或过期 |
| 403 | 无权限 | 没有访问权限 |
| 404 | 资源不存在 | 主机不存在 |
| 409 | 资源冲突 | 主机名已存在 |
| 422 | 验证失败 | 数据验证不通过 |
| 429 | 请求过频 | 超出API调用限制 |
| 500 | 服务器错误 | 内部服务器错误 |
| 502 | 网关错误 | 上游服务不可用 |
| 503 | 服务不可用 | 系统维护中 |

## 🔒 安全考虑

### 认证授权
- **JWT Token**：所有API需要有效Token
- **Token刷新**：自动刷新机制
- **权限验证**：基于RBAC的权限控制

### 输入验证
- **参数验证**：严格的输入参数验证
- **SQL注入防护**：使用参数化查询
- **XSS防护**：输出内容编码

### 限流保护
- **API限流**：防止API滥用
- **IP限制**：可疑IP自动封禁
- **并发控制**：限制并发请求数
