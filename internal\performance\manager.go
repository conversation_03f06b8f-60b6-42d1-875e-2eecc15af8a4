package performance

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// PerformanceManagerConfig 性能管理器配置
type PerformanceManagerConfig struct {
	ServiceName       string                    `json:"service_name"`
	Enabled           bool                      `json:"enabled"`
	MonitorConfig     *PerformanceConfig        `json:"monitor_config"`
	TunerConfig       *TuningConfig             `json:"tuner_config"`
	AlertWebhookURL   string                    `json:"alert_webhook_url"`
	MetricsRetention  time.Duration             `json:"metrics_retention"`
	ReportInterval    time.Duration             `json:"report_interval"`
	CustomCollectors  []MetricCollector         `json:"-"`
	CustomHandlers    []AlertHandler            `json:"-"`
}

// PerformanceManager 性能管理器
type PerformanceManager struct {
	config    *PerformanceManagerConfig
	monitor   *PerformanceMonitor
	tuner     *AutoTuner
	logger    *logrus.Logger
	mutex     sync.RWMutex
	running   bool
	stopChan  chan struct{}
	
	// 性能历史数据
	metricsHistory []*PerformanceMetrics
	alertsHistory  []*PerformanceAlert
	
	// 性能报告
	lastReport     *PerformanceReport
	reportChan     chan *PerformanceReport
}

// PerformanceReport 性能报告
type PerformanceReport struct {
	ServiceName     string                 `json:"service_name"`
	ReportTime      time.Time              `json:"report_time"`
	Period          time.Duration          `json:"period"`
	Summary         *PerformanceSummary    `json:"summary"`
	Trends          *PerformanceTrends     `json:"trends"`
	Alerts          []*PerformanceAlert    `json:"alerts"`
	TuningActions   []*TuningExecution     `json:"tuning_actions"`
	Recommendations []string               `json:"recommendations"`
	HealthScore     float64                `json:"health_score"`
}

// PerformanceSummary 性能摘要
type PerformanceSummary struct {
	AvgCPUUsage        float64 `json:"avg_cpu_usage"`
	MaxCPUUsage        float64 `json:"max_cpu_usage"`
	AvgMemoryUsage     float64 `json:"avg_memory_usage"`
	MaxMemoryUsage     float64 `json:"max_memory_usage"`
	AvgResponseTime    float64 `json:"avg_response_time"`
	MaxResponseTime    float64 `json:"max_response_time"`
	TotalRequests      int64   `json:"total_requests"`
	TotalErrors        int64   `json:"total_errors"`
	ErrorRate          float64 `json:"error_rate"`
	Uptime             float64 `json:"uptime"`
	AvgGoroutines      float64 `json:"avg_goroutines"`
	MaxGoroutines      int     `json:"max_goroutines"`
}

// PerformanceTrends 性能趋势
type PerformanceTrends struct {
	CPUTrend        string  `json:"cpu_trend"`        // improving, stable, degrading
	MemoryTrend     string  `json:"memory_trend"`
	ResponseTrend   string  `json:"response_trend"`
	ErrorTrend      string  `json:"error_trend"`
	ThroughputTrend string  `json:"throughput_trend"`
	OverallTrend    string  `json:"overall_trend"`
	TrendScore      float64 `json:"trend_score"`      // -1 to 1
}

// NewPerformanceManager 创建性能管理器
func NewPerformanceManager(config *PerformanceManagerConfig, logger *logrus.Logger) *PerformanceManager {
	// 设置默认值
	if config.MetricsRetention <= 0 {
		config.MetricsRetention = 24 * time.Hour
	}
	if config.ReportInterval <= 0 {
		config.ReportInterval = 1 * time.Hour
	}

	// 创建监控器
	monitor := NewPerformanceMonitor(config.MonitorConfig, logger)

	// 创建调优器
	tuner := NewAutoTuner(config.TunerConfig, monitor, logger)

	pm := &PerformanceManager{
		config:         config,
		monitor:        monitor,
		tuner:          tuner,
		logger:         logger,
		stopChan:       make(chan struct{}),
		metricsHistory: make([]*PerformanceMetrics, 0),
		alertsHistory:  make([]*PerformanceAlert, 0),
		reportChan:     make(chan *PerformanceReport, 10),
	}

	// 注册自定义收集器
	for _, collector := range config.CustomCollectors {
		monitor.RegisterCollector(collector)
	}

	// 注册自定义告警处理器
	for _, handler := range config.CustomHandlers {
		monitor.RegisterAlertHandler(handler)
	}

	return pm
}

// Start 启动性能管理器
func (pm *PerformanceManager) Start(ctx context.Context) error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if pm.running {
		return fmt.Errorf("performance manager is already running")
	}

	if !pm.config.Enabled {
		pm.logger.Info("Performance manager is disabled")
		return nil
	}

	pm.running = true

	// 启动监控器
	if err := pm.monitor.Start(ctx); err != nil {
		return fmt.Errorf("failed to start performance monitor: %w", err)
	}

	// 启动调优器
	if err := pm.tuner.Start(ctx); err != nil {
		return fmt.Errorf("failed to start auto tuner: %w", err)
	}

	// 启动数据收集协程
	go pm.dataCollectionLoop(ctx)

	// 启动报告生成协程
	go pm.reportGenerationLoop(ctx)

	// 启动清理协程
	go pm.cleanupLoop(ctx)

	pm.logger.WithField("service", pm.config.ServiceName).Info("Performance manager started")
	return nil
}

// Stop 停止性能管理器
func (pm *PerformanceManager) Stop() error {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	if !pm.running {
		return nil
	}

	close(pm.stopChan)
	pm.running = false

	// 停止组件
	if pm.monitor != nil {
		pm.monitor.Stop()
	}

	if pm.tuner != nil {
		pm.tuner.Stop()
	}

	pm.logger.WithField("service", pm.config.ServiceName).Info("Performance manager stopped")
	return nil
}

// dataCollectionLoop 数据收集循环
func (pm *PerformanceManager) dataCollectionLoop(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Minute) // 每分钟收集一次历史数据
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-pm.stopChan:
			return
		case <-ticker.C:
			pm.collectHistoricalData()
		}
	}
}

// collectHistoricalData 收集历史数据
func (pm *PerformanceManager) collectHistoricalData() {
	// 收集当前指标
	if metrics := pm.monitor.GetCurrentMetrics(); metrics != nil {
		pm.mutex.Lock()
		pm.metricsHistory = append(pm.metricsHistory, metrics)
		pm.mutex.Unlock()
	}

	// 收集最新告警
	alerts := pm.monitor.GetAlerts(10)
	if len(alerts) > 0 {
		pm.mutex.Lock()
		pm.alertsHistory = append(pm.alertsHistory, alerts...)
		pm.mutex.Unlock()
	}
}

// reportGenerationLoop 报告生成循环
func (pm *PerformanceManager) reportGenerationLoop(ctx context.Context) {
	ticker := time.NewTicker(pm.config.ReportInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-pm.stopChan:
			return
		case <-ticker.C:
			pm.generateReport()
		}
	}
}

// generateReport 生成性能报告
func (pm *PerformanceManager) generateReport() {
	pm.mutex.RLock()
	metricsCount := len(pm.metricsHistory)
	alertsCount := len(pm.alertsHistory)
	pm.mutex.RUnlock()

	if metricsCount == 0 {
		pm.logger.Debug("No metrics available for report generation")
		return
	}

	report := &PerformanceReport{
		ServiceName: pm.config.ServiceName,
		ReportTime:  time.Now(),
		Period:      pm.config.ReportInterval,
		Summary:     pm.calculateSummary(),
		Trends:      pm.calculateTrends(),
		Alerts:      pm.getRecentAlerts(),
		TuningActions: pm.getRecentTuningActions(),
		Recommendations: pm.generateRecommendations(),
	}

	// 计算健康分数
	report.HealthScore = pm.calculateHealthScore(report)

	pm.mutex.Lock()
	pm.lastReport = report
	pm.mutex.Unlock()

	// 发送报告
	select {
	case pm.reportChan <- report:
	default:
		pm.logger.Warn("Report channel is full, dropping report")
	}

	pm.logger.WithFields(logrus.Fields{
		"service":      pm.config.ServiceName,
		"health_score": report.HealthScore,
		"alerts":       len(report.Alerts),
		"tuning_actions": len(report.TuningActions),
	}).Info("Performance report generated")
}

// calculateSummary 计算性能摘要
func (pm *PerformanceManager) calculateSummary() *PerformanceSummary {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	if len(pm.metricsHistory) == 0 {
		return &PerformanceSummary{}
	}

	summary := &PerformanceSummary{}
	var totalCPU, totalMemory, totalResponse, totalGoroutines float64
	var maxCPU, maxMemory, maxResponse float64
	var maxGoroutines int
	var totalRequests, totalErrors int64

	for _, metrics := range pm.metricsHistory {
		totalCPU += metrics.CPUUsage
		totalMemory += metrics.MemoryUsage
		totalResponse += metrics.AvgResponseTime
		totalGoroutines += float64(metrics.GoroutineCount)
		totalRequests += metrics.RequestCount
		totalErrors += metrics.ErrorCount

		if metrics.CPUUsage > maxCPU {
			maxCPU = metrics.CPUUsage
		}
		if metrics.MemoryUsage > maxMemory {
			maxMemory = metrics.MemoryUsage
		}
		if metrics.AvgResponseTime > maxResponse {
			maxResponse = metrics.AvgResponseTime
		}
		if metrics.GoroutineCount > maxGoroutines {
			maxGoroutines = metrics.GoroutineCount
		}
	}

	count := float64(len(pm.metricsHistory))
	summary.AvgCPUUsage = totalCPU / count
	summary.MaxCPUUsage = maxCPU
	summary.AvgMemoryUsage = totalMemory / count
	summary.MaxMemoryUsage = maxMemory
	summary.AvgResponseTime = totalResponse / count
	summary.MaxResponseTime = maxResponse
	summary.AvgGoroutines = totalGoroutines / count
	summary.MaxGoroutines = maxGoroutines
	summary.TotalRequests = totalRequests
	summary.TotalErrors = totalErrors

	if totalRequests > 0 {
		summary.ErrorRate = float64(totalErrors) / float64(totalRequests) * 100
	}

	// 计算正常运行时间（简化实现）
	summary.Uptime = 99.9

	return summary
}

// calculateTrends 计算性能趋势
func (pm *PerformanceManager) calculateTrends() *PerformanceTrends {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	trends := &PerformanceTrends{
		CPUTrend:        "stable",
		MemoryTrend:     "stable",
		ResponseTrend:   "stable",
		ErrorTrend:      "stable",
		ThroughputTrend: "stable",
		OverallTrend:    "stable",
		TrendScore:      0.0,
	}

	// 简化的趋势计算
	if len(pm.metricsHistory) >= 2 {
		recent := pm.metricsHistory[len(pm.metricsHistory)-1]
		previous := pm.metricsHistory[len(pm.metricsHistory)-2]

		// CPU趋势
		if recent.CPUUsage > previous.CPUUsage*1.1 {
			trends.CPUTrend = "degrading"
		} else if recent.CPUUsage < previous.CPUUsage*0.9 {
			trends.CPUTrend = "improving"
		}

		// 内存趋势
		if recent.MemoryUsage > previous.MemoryUsage*1.1 {
			trends.MemoryTrend = "degrading"
		} else if recent.MemoryUsage < previous.MemoryUsage*0.9 {
			trends.MemoryTrend = "improving"
		}

		// 响应时间趋势
		if recent.AvgResponseTime > previous.AvgResponseTime*1.1 {
			trends.ResponseTrend = "degrading"
		} else if recent.AvgResponseTime < previous.AvgResponseTime*0.9 {
			trends.ResponseTrend = "improving"
		}
	}

	return trends
}

// getRecentAlerts 获取最近的告警
func (pm *PerformanceManager) getRecentAlerts() []*PerformanceAlert {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()

	// 返回最近1小时的告警
	cutoff := time.Now().Add(-1 * time.Hour)
	alerts := make([]*PerformanceAlert, 0)

	for _, alert := range pm.alertsHistory {
		if alert.Timestamp.After(cutoff) {
			alerts = append(alerts, alert)
		}
	}

	return alerts
}

// getRecentTuningActions 获取最近的调优动作
func (pm *PerformanceManager) getRecentTuningActions() []*TuningExecution {
	if pm.tuner == nil {
		return []*TuningExecution{}
	}

	// 简化实现：返回空列表
	return []*TuningExecution{}
}

// generateRecommendations 生成建议
func (pm *PerformanceManager) generateRecommendations() []string {
	recommendations := make([]string, 0)

	summary := pm.calculateSummary()

	if summary.AvgCPUUsage > 80 {
		recommendations = append(recommendations, "CPU使用率较高，建议优化计算密集型操作或增加实例")
	}

	if summary.AvgMemoryUsage > 85 {
		recommendations = append(recommendations, "内存使用率较高，建议检查内存泄漏或增加内存")
	}

	if summary.AvgResponseTime > 1000 {
		recommendations = append(recommendations, "响应时间较长，建议优化数据库查询或增加缓存")
	}

	if summary.ErrorRate > 5 {
		recommendations = append(recommendations, "错误率较高，建议检查错误日志并修复问题")
	}

	if summary.AvgGoroutines > 1000 {
		recommendations = append(recommendations, "Goroutine数量较多，建议检查是否存在Goroutine泄漏")
	}

	if len(recommendations) == 0 {
		recommendations = append(recommendations, "系统运行良好，继续保持")
	}

	return recommendations
}

// calculateHealthScore 计算健康分数
func (pm *PerformanceManager) calculateHealthScore(report *PerformanceReport) float64 {
	score := 100.0

	// CPU使用率影响
	if report.Summary.AvgCPUUsage > 90 {
		score -= 20
	} else if report.Summary.AvgCPUUsage > 80 {
		score -= 10
	} else if report.Summary.AvgCPUUsage > 70 {
		score -= 5
	}

	// 内存使用率影响
	if report.Summary.AvgMemoryUsage > 95 {
		score -= 20
	} else if report.Summary.AvgMemoryUsage > 85 {
		score -= 10
	} else if report.Summary.AvgMemoryUsage > 75 {
		score -= 5
	}

	// 响应时间影响
	if report.Summary.AvgResponseTime > 5000 {
		score -= 20
	} else if report.Summary.AvgResponseTime > 2000 {
		score -= 10
	} else if report.Summary.AvgResponseTime > 1000 {
		score -= 5
	}

	// 错误率影响
	if report.Summary.ErrorRate > 10 {
		score -= 20
	} else if report.Summary.ErrorRate > 5 {
		score -= 10
	} else if report.Summary.ErrorRate > 1 {
		score -= 5
	}

	// 告警影响
	criticalAlerts := 0
	for _, alert := range report.Alerts {
		if alert.Type == "critical" {
			criticalAlerts++
		}
	}
	score -= float64(criticalAlerts) * 5

	if score < 0 {
		score = 0
	}

	return score
}

// cleanupLoop 清理循环
func (pm *PerformanceManager) cleanupLoop(ctx context.Context) {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-pm.stopChan:
			return
		case <-ticker.C:
			pm.cleanup()
		}
	}
}

// cleanup 清理过期数据
func (pm *PerformanceManager) cleanup() {
	pm.mutex.Lock()
	defer pm.mutex.Unlock()

	cutoff := time.Now().Add(-pm.config.MetricsRetention)

	// 清理过期指标
	filtered := make([]*PerformanceMetrics, 0)
	for _, metrics := range pm.metricsHistory {
		if metrics.Timestamp.After(cutoff) {
			filtered = append(filtered, metrics)
		}
	}
	removed := len(pm.metricsHistory) - len(filtered)
	pm.metricsHistory = filtered

	// 清理过期告警
	filteredAlerts := make([]*PerformanceAlert, 0)
	for _, alert := range pm.alertsHistory {
		if alert.Timestamp.After(cutoff) {
			filteredAlerts = append(filteredAlerts, alert)
		}
	}
	pm.alertsHistory = filteredAlerts

	if removed > 0 {
		pm.logger.WithField("removed_metrics", removed).Debug("Cleaned up old performance data")
	}
}

// GetCurrentMetrics 获取当前指标
func (pm *PerformanceManager) GetCurrentMetrics() *PerformanceMetrics {
	if pm.monitor == nil {
		return nil
	}
	return pm.monitor.GetCurrentMetrics()
}

// GetLastReport 获取最新报告
func (pm *PerformanceManager) GetLastReport() *PerformanceReport {
	pm.mutex.RLock()
	defer pm.mutex.RUnlock()
	return pm.lastReport
}

// GetStatus 获取状态
func (pm *PerformanceManager) GetStatus() map[string]interface{} {
	status := map[string]interface{}{
		"service": pm.config.ServiceName,
		"enabled": pm.config.Enabled,
		"running": pm.running,
	}

	if pm.monitor != nil {
		status["monitor"] = map[string]interface{}{
			"current_metrics": pm.monitor.GetCurrentMetrics(),
			"recent_alerts":   pm.monitor.GetAlerts(5),
		}
	}

	if pm.tuner != nil {
		status["tuner"] = pm.tuner.GetStats()
	}

	pm.mutex.RLock()
	status["metrics_history_count"] = len(pm.metricsHistory)
	status["alerts_history_count"] = len(pm.alertsHistory)
	status["last_report"] = pm.lastReport
	pm.mutex.RUnlock()

	return status
}
