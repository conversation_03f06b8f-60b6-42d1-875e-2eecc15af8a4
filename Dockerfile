# AI对话运维管理平台 - 多阶段构建Dockerfile

# =============================================================================
# 构建阶段
# =============================================================================
FROM golang:1.21-alpine AS builder

# 设置构建参数
ARG GO_VERSION=1.21
ARG BUILD_TIME
ARG GIT_COMMIT
ARG VERSION

# 安装构建依赖
RUN apk add --no-cache \
    git \
    ca-certificates \
    tzdata \
    sqlite \
    gcc \
    musl-dev

# 设置工作目录
WORKDIR /app

# 复制go模块文件
COPY go.mod go.sum ./

# 下载依赖
RUN go mod download && go mod verify

# 复制源代码
COPY . .

# 构建应用
RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-w -s \
    -X main.Version=${VERSION:-dev} \
    -X main.BuildTime=${BUILD_TIME:-unknown} \
    -X main.GitCommit=${GIT_COMMIT:-unknown}" \
    -a -installsuffix cgo \
    -o aiops-platform \
    ./cmd/server

# 构建数据库迁移工具
RUN CGO_ENABLED=1 GOOS=linux GOARCH=amd64 go build \
    -ldflags="-w -s" \
    -a -installsuffix cgo \
    -o migrate \
    ./cmd/migrate

# =============================================================================
# 运行阶段
# =============================================================================
FROM alpine:latest

# 安装运行时依赖
RUN apk add --no-cache \
    ca-certificates \
    tzdata \
    sqlite \
    curl \
    bash \
    && rm -rf /var/cache/apk/*

# 创建非root用户
RUN addgroup -g 1001 -S aiops && \
    adduser -u 1001 -S aiops -G aiops

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建必要的目录
RUN mkdir -p /app/data /app/logs /app/backups /app/configs && \
    chown -R aiops:aiops /app

# 设置工作目录
WORKDIR /app

# 从构建阶段复制二进制文件
COPY --from=builder /app/aiops-platform /app/aiops-platform
COPY --from=builder /app/migrate /app/migrate

# 复制静态资源和配置文件
COPY --chown=aiops:aiops web/ /app/web/
COPY --chown=aiops:aiops configs/ /app/configs/
COPY --chown=aiops:aiops scripts/ /app/scripts/

# 设置执行权限
RUN chmod +x /app/aiops-platform /app/migrate /app/scripts/*.sh

# 创建健康检查脚本
RUN echo '#!/bin/bash\ncurl -f http://localhost:8080/health || exit 1' > /app/healthcheck.sh && \
    chmod +x /app/healthcheck.sh

# 切换到非root用户
USER aiops

# 暴露端口
EXPOSE 8080 9090

# 设置环境变量
ENV ENV=production
ENV LOG_LEVEL=info
ENV DB_PATH=/app/data/aiops.db
ENV LOG_FILE=/app/logs/aiops.log

# 健康检查
HEALTHCHECK --interval=30s --timeout=10s --start-period=60s --retries=3 \
    CMD /app/healthcheck.sh

# 设置入口点
ENTRYPOINT ["/app/scripts/entrypoint.sh"]

# 默认命令
CMD ["/app/aiops-platform"]

# 添加标签
LABEL maintainer="AI运维平台开发团队" \
      version="${VERSION:-dev}" \
      description="AI对话运维管理平台" \
      build-time="${BUILD_TIME:-unknown}" \
      git-commit="${GIT_COMMIT:-unknown}"
