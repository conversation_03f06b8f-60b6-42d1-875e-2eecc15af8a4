// 工作流管理JavaScript模块
class WorkflowManager {
    constructor() {
        this.activeWorkflows = new Map();
        this.currentWorkflowId = null;
        this.sessionId = this.generateSessionId();
        this.init();
    }

    // 初始化工作流管理器
    init() {
        this.bindEvents();
        this.loadActiveWorkflows();
        this.setupWorkflowIndicator();
    }

    // 绑定事件
    bindEvents() {
        // 监听消息发送事件
        document.addEventListener('messageSent', (event) => {
            this.handleMessageSent(event.detail);
        });

        // 监听AI响应事件
        document.addEventListener('aiResponse', (event) => {
            this.handleAIResponse(event.detail);
        });

        // 工作流控制按钮
        document.addEventListener('click', (event) => {
            if (event.target.matches('[data-workflow-action]')) {
                this.handleWorkflowAction(event.target);
            }
        });
    }

    // 生成会话ID
    generateSessionId() {
        return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 处理消息发送
    async handleMessageSent(messageData) {
        try {
            // 分析用户意图
            const intentResult = await this.analyzeIntent(messageData.message);
            
            if (intentResult.needs_workflow) {
                console.log('检测到工作流需求:', intentResult);
                await this.triggerWorkflow(intentResult);
            }
        } catch (error) {
            console.error('处理消息时出错:', error);
        }
    }

    // 处理AI响应
    handleAIResponse(responseData) {
        // 检查响应中是否包含工作流建议
        if (responseData.workflow_suggestion) {
            this.showWorkflowSuggestion(responseData.workflow_suggestion);
        }

        // 更新工作流状态
        if (this.currentWorkflowId) {
            this.updateWorkflowStatus();
        }
    }

    // 分析用户意图
    async analyzeIntent(message) {
        try {
            const response = await fetch('/api/v1/workflow/analyze-intent', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    session_id: this.sessionId
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            return data.result;
        } catch (error) {
            console.error('分析意图失败:', error);
            return { needs_workflow: false };
        }
    }

    // 触发工作流
    async triggerWorkflow(intentResult) {
        try {
            const response = await fetch('/api/v1/workflow/trigger', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    trigger_type: 'intent',
                    intent: intentResult.intent,
                    workflow_type: intentResult.workflow_type,
                    session_id: this.sessionId,
                    user_id: 1, // 临时用户ID
                    parameters: intentResult.parameters,
                    priority: 1
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            if (data.success) {
                this.currentWorkflowId = data.instance_id;
                this.activeWorkflows.set(data.instance_id, {
                    id: data.instance_id,
                    status: data.status,
                    type: intentResult.workflow_type,
                    startTime: new Date()
                });
                
                this.showWorkflowStarted(data.instance_id, intentResult.workflow_type);
                this.updateWorkflowIndicator();
            }
        } catch (error) {
            console.error('触发工作流失败:', error);
            this.showError('启动工作流失败，请稍后重试');
        }
    }

    // 处理用户输入（工作流中）
    async processWorkflowInput(instanceId, userInput) {
        try {
            const response = await fetch(`/api/v1/workflow/${instanceId}/input`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    input: userInput
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            const data = await response.json();
            this.handleWorkflowResponse(data);
        } catch (error) {
            console.error('处理工作流输入失败:', error);
            this.showError('处理输入失败，请稍后重试');
        }
    }

    // 处理工作流响应
    handleWorkflowResponse(response) {
        if (response.status === 'completed') {
            this.showWorkflowCompleted(response.instance_id);
            this.activeWorkflows.delete(response.instance_id);
            if (this.currentWorkflowId === response.instance_id) {
                this.currentWorkflowId = null;
            }
        } else if (response.status === 'waiting') {
            this.showWorkflowWaiting(response);
        }
        
        this.updateWorkflowIndicator();
    }

    // 加载活跃工作流
    async loadActiveWorkflows() {
        try {
            const response = await fetch(`/api/v1/workflow/active?session_id=${this.sessionId}`);
            if (response.ok) {
                const data = await response.json();
                if (data.success && data.workflows) {
                    data.workflows.forEach(workflow => {
                        this.activeWorkflows.set(workflow.id, workflow);
                        if (workflow.status === 'running' || workflow.status === 'waiting') {
                            this.currentWorkflowId = workflow.id;
                        }
                    });
                    this.updateWorkflowIndicator();
                }
            }
        } catch (error) {
            console.error('加载活跃工作流失败:', error);
        }
    }

    // 设置工作流指示器
    setupWorkflowIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'workflow-indicator';
        indicator.className = 'workflow-indicator hidden';
        indicator.innerHTML = `
            <div class="workflow-status">
                <i class="bi bi-gear-fill workflow-icon"></i>
                <span class="workflow-text">工作流进行中...</span>
                <button class="workflow-cancel" onclick="workflowManager.cancelCurrentWorkflow()">
                    <i class="bi bi-x"></i>
                </button>
            </div>
        `;
        
        document.body.appendChild(indicator);
    }

    // 更新工作流指示器
    updateWorkflowIndicator() {
        const indicator = document.getElementById('workflow-indicator');
        if (!indicator) return;

        if (this.activeWorkflows.size > 0) {
            indicator.classList.remove('hidden');
            const text = indicator.querySelector('.workflow-text');
            if (this.activeWorkflows.size === 1) {
                const workflow = Array.from(this.activeWorkflows.values())[0];
                text.textContent = `${this.getWorkflowTypeName(workflow.type)} 进行中...`;
            } else {
                text.textContent = `${this.activeWorkflows.size} 个工作流进行中...`;
            }
        } else {
            indicator.classList.add('hidden');
        }
    }

    // 获取工作流类型名称
    getWorkflowTypeName(type) {
        const typeNames = {
            'host_management': '主机管理',
            'command_execution': '命令执行',
            'system_monitoring': '系统监控',
            'alert_management': '告警管理',
            'report_generation': '报表生成',
            'backup_restore': '备份恢复',
            'security_audit': '安全审计'
        };
        return typeNames[type] || type;
    }

    // 显示工作流建议
    showWorkflowSuggestion(suggestion) {
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;

        const suggestionElement = document.createElement('div');
        suggestionElement.className = 'workflow-suggestion';
        suggestionElement.innerHTML = `
            <div class="suggestion-content">
                <i class="bi bi-lightbulb text-warning"></i>
                <span>检测到您可能需要启动 <strong>${this.getWorkflowTypeName(suggestion.workflow_type)}</strong> 工作流</span>
                <button class="btn btn-sm btn-primary ms-2" onclick="workflowManager.acceptSuggestion('${suggestion.workflow_type}')">
                    启动工作流
                </button>
                <button class="btn btn-sm btn-outline-secondary ms-1" onclick="this.parentElement.parentElement.remove()">
                    忽略
                </button>
            </div>
        `;
        
        chatMessages.appendChild(suggestionElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;
    }

    // 接受工作流建议
    async acceptSuggestion(workflowType) {
        await this.triggerWorkflow({
            workflow_type: workflowType,
            intent: workflowType,
            parameters: {}
        });
    }

    // 显示工作流已启动
    showWorkflowStarted(instanceId, workflowType) {
        this.showSystemMessage(`🚀 ${this.getWorkflowTypeName(workflowType)} 工作流已启动`, 'success');
    }

    // 显示工作流已完成
    showWorkflowCompleted(instanceId) {
        this.showSystemMessage('✅ 工作流已完成', 'success');
    }

    // 显示工作流等待输入
    showWorkflowWaiting(response) {
        if (response.required_inputs && response.required_inputs.length > 0) {
            this.showSystemMessage(`⏳ 工作流等待输入: ${response.required_inputs.join(', ')}`, 'info');
        }
    }

    // 显示系统消息
    showSystemMessage(message, type = 'info') {
        const chatMessages = document.getElementById('chat-messages');
        if (!chatMessages) return;

        const messageElement = document.createElement('div');
        messageElement.className = `system-message alert alert-${type}`;
        messageElement.innerHTML = `
            <i class="bi bi-info-circle"></i>
            <span>${message}</span>
        `;
        
        chatMessages.appendChild(messageElement);
        chatMessages.scrollTop = chatMessages.scrollHeight;

        // 3秒后自动移除
        setTimeout(() => {
            if (messageElement.parentNode) {
                messageElement.remove();
            }
        }, 3000);
    }

    // 显示错误消息
    showError(message) {
        this.showSystemMessage(`❌ ${message}`, 'danger');
    }

    // 取消当前工作流
    async cancelCurrentWorkflow() {
        if (!this.currentWorkflowId) return;

        try {
            const response = await fetch(`/api/v1/workflow/${this.currentWorkflowId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.activeWorkflows.delete(this.currentWorkflowId);
                this.currentWorkflowId = null;
                this.updateWorkflowIndicator();
                this.showSystemMessage('工作流已取消', 'warning');
            }
        } catch (error) {
            console.error('取消工作流失败:', error);
            this.showError('取消工作流失败');
        }
    }

    // 处理工作流操作
    handleWorkflowAction(button) {
        const action = button.dataset.workflowAction;
        const instanceId = button.dataset.instanceId;

        switch (action) {
            case 'cancel':
                this.cancelWorkflow(instanceId);
                break;
            case 'retry':
                this.retryWorkflow(instanceId);
                break;
            default:
                console.warn('未知的工作流操作:', action);
        }
    }

    // 取消指定工作流
    async cancelWorkflow(instanceId) {
        try {
            const response = await fetch(`/api/v1/workflow/${instanceId}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                this.activeWorkflows.delete(instanceId);
                if (this.currentWorkflowId === instanceId) {
                    this.currentWorkflowId = null;
                }
                this.updateWorkflowIndicator();
                this.showSystemMessage('工作流已取消', 'warning');
            }
        } catch (error) {
            console.error('取消工作流失败:', error);
            this.showError('取消工作流失败');
        }
    }
}

// 全局工作流管理器实例
let workflowManager;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
    workflowManager = new WorkflowManager();
    console.log('工作流管理器已初始化');
});
