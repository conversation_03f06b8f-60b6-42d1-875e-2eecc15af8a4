package service

import (
	"errors"
	"fmt"
	"time"

	"aiops-platform/internal/auth"
	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// userService 用户服务实现
type userService struct {
	db              *gorm.DB
	passwordManager *auth.PasswordManager
	logger          *logrus.Logger
}

// NewUserService 创建用户服务
func NewUserService(db *gorm.DB, passwordManager *auth.PasswordManager, logger *logrus.Logger) UserService {
	return &userService{
		db:              db,
		passwordManager: passwordManager,
		logger:          logger,
	}
}

// CreateUser 创建用户
func (s *userService) CreateUser(req *model.UserCreateRequest) (*model.UserResponse, error) {
	// 检查用户名是否已存在
	var existingUser model.User
	if err := s.db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		return nil, errors.New("username already exists")
	}

	// 检查邮箱是否已存在
	if err := s.db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		return nil, errors.New("email already exists")
	}

	// 哈希密码
	hashedPassword, err := s.passwordManager.HashPassword(req.Password)
	if err != nil {
		return nil, fmt.Errorf("failed to hash password: %w", err)
	}

	// 创建用户
	user := &model.User{
		Username:     req.Username,
		PasswordHash: hashedPassword,
		Email:        req.Email,
		FullName:     req.FullName,
		Role:         req.Role,
		Phone:        req.Phone,
		Department:   req.Department,
		IsActive:     true,
	}

	if err := s.db.Create(user).Error; err != nil {
		return nil, fmt.Errorf("failed to create user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
		"role":     user.Role,
	}).Info("User created successfully")

	return user.ToResponse(), nil
}

// GetUserByID 根据ID获取用户
func (s *userService) GetUserByID(id int64) (*model.UserResponse, error) {
	var user model.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user.ToResponse(), nil
}

// GetUserByUsername 根据用户名获取用户
func (s *userService) GetUserByUsername(username string) (*model.UserResponse, error) {
	var user model.User
	if err := s.db.Where("username = ?", username).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user.ToResponse(), nil
}

// GetUserByEmail 根据邮箱获取用户
func (s *userService) GetUserByEmail(email string) (*model.UserResponse, error) {
	var user model.User
	if err := s.db.Where("email = ?", email).First(&user).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	return user.ToResponse(), nil
}

// UpdateUser 更新用户
func (s *userService) UpdateUser(id int64, req *model.UserUpdateRequest) (*model.UserResponse, error) {
	var user model.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("user not found")
		}
		return nil, fmt.Errorf("failed to get user: %w", err)
	}

	// 更新字段
	if req.Email != "" && req.Email != user.Email {
		// 检查邮箱是否已被其他用户使用
		var existingUser model.User
		if err := s.db.Where("email = ? AND id != ?", req.Email, id).First(&existingUser).Error; err == nil {
			return nil, errors.New("email already exists")
		}
		user.Email = req.Email
	}

	if req.FullName != "" {
		user.FullName = req.FullName
	}

	if req.Role != "" {
		user.Role = req.Role
	}

	if req.IsActive != nil {
		user.IsActive = *req.IsActive
	}

	if req.Phone != "" {
		user.Phone = req.Phone
	}

	if req.Department != "" {
		user.Department = req.Department
	}

	if err := s.db.Save(&user).Error; err != nil {
		return nil, fmt.Errorf("failed to update user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	}).Info("User updated successfully")

	return user.ToResponse(), nil
}

// DeleteUser 删除用户
func (s *userService) DeleteUser(id int64) error {
	var user model.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	if err := s.db.Delete(&user).Error; err != nil {
		return fmt.Errorf("failed to delete user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	}).Info("User deleted successfully")

	return nil
}

// ListUsers 获取用户列表
func (s *userService) ListUsers(req *model.UserListQuery) (*model.UserListResponse, error) {
	query := s.db.Model(&model.User{})

	// 应用过滤条件
	if req.Role != "" {
		query = query.Where("role = ?", req.Role)
	}

	if req.IsActive != nil {
		query = query.Where("is_active = ?", *req.IsActive)
	}

	if req.Department != "" {
		query = query.Where("department = ?", req.Department)
	}

	if req.Search != "" {
		searchPattern := "%" + req.Search + "%"
		query = query.Where("username LIKE ? OR full_name LIKE ? OR email LIKE ?",
			searchPattern, searchPattern, searchPattern)
	}

	// 获取总数
	var total int64
	if err := query.Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count users: %w", err)
	}

	// 分页
	offset := (req.Page - 1) * req.Limit
	var users []model.User
	if err := query.Offset(offset).Limit(req.Limit).Order("created_at DESC").Find(&users).Error; err != nil {
		return nil, fmt.Errorf("failed to get users: %w", err)
	}

	// 转换为响应格式
	userResponses := make([]*model.UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = user.ToResponse()
	}

	// 计算分页信息
	pagination := model.NewPagination(total, req.Page, req.Limit)

	return &model.UserListResponse{
		Users:      userResponses,
		Pagination: pagination,
	}, nil
}

// ChangePassword 修改密码
func (s *userService) ChangePassword(id int64, req *model.PasswordChangeRequest) error {
	var user model.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 验证旧密码
	if err := s.passwordManager.VerifyPassword(user.PasswordHash, req.OldPassword); err != nil {
		return errors.New("invalid old password")
	}

	// 哈希新密码
	hashedPassword, err := s.passwordManager.HashPassword(req.NewPassword)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// 更新密码
	now := time.Now()
	user.PasswordHash = hashedPassword
	user.PasswordChangedAt = &now

	if err := s.db.Save(&user).Error; err != nil {
		return fmt.Errorf("failed to update password: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	}).Info("Password changed successfully")

	return nil
}

// ResetPassword 重置密码
func (s *userService) ResetPassword(id int64, newPassword string) error {
	var user model.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 哈希新密码
	hashedPassword, err := s.passwordManager.HashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("failed to hash password: %w", err)
	}

	// 更新密码
	now := time.Now()
	user.PasswordHash = hashedPassword
	user.PasswordChangedAt = &now

	if err := s.db.Save(&user).Error; err != nil {
		return fmt.Errorf("failed to reset password: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	}).Info("Password reset successfully")

	return nil
}

// LockUser 锁定用户
func (s *userService) LockUser(id int64, duration time.Duration) error {
	var user model.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	lockUntil := time.Now().Add(duration)
	user.LockedUntil = &lockUntil

	if err := s.db.Save(&user).Error; err != nil {
		return fmt.Errorf("failed to lock user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":    user.ID,
		"username":   user.Username,
		"lock_until": lockUntil,
	}).Info("User locked successfully")

	return nil
}

// UnlockUser 解锁用户
func (s *userService) UnlockUser(id int64) error {
	var user model.User
	if err := s.db.First(&user, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	user.LockedUntil = nil
	user.FailedLoginCount = 0

	if err := s.db.Save(&user).Error; err != nil {
		return fmt.Errorf("failed to unlock user: %w", err)
	}

	s.logger.WithFields(logrus.Fields{
		"user_id":  user.ID,
		"username": user.Username,
	}).Info("User unlocked successfully")

	return nil
}
