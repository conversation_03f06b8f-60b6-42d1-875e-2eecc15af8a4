package workflow

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// WorkflowEngine 智能工作流引擎
type WorkflowEngine struct {
	db               *gorm.DB
	logger           *logrus.Logger
	definitions      map[string]*WorkflowDefinition
	instances        map[string]*WorkflowInstance
	stateManager     *StateManager
	stepExecutor     *StepExecutor
	contextManager   *WorkflowContextManager
	aiIntegration    *AIIntegration
	scheduler        *WorkflowScheduler
	mutex            sync.RWMutex
	eventBus         *EventBus
	metricsCollector *MetricsCollector
}

// WorkflowDefinition 工作流定义
type WorkflowDefinition struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Version     string                 `json:"version"`
	Category    string                 `json:"category"` // host_management, monitoring, reporting
	Triggers    []WorkflowTrigger      `json:"triggers"`
	Steps       []WorkflowStep         `json:"steps"`
	Variables   map[string]interface{} `json:"variables"`
	Conditions  []WorkflowCondition    `json:"conditions"`
	Timeout     time.Duration          `json:"timeout"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Enabled     bool                   `json:"enabled"`
}

// WorkflowInstance 工作流实例
type WorkflowInstance struct {
	ID            string                 `json:"id"`
	DefinitionID  string                 `json:"definition_id"`
	SessionID     string                 `json:"session_id"`
	UserID        int64                  `json:"user_id"`
	Status        WorkflowStatus         `json:"status"`
	CurrentStep   string                 `json:"current_step"`
	Variables     map[string]interface{} `json:"variables"`
	CollectedData map[string]interface{} `json:"collected_data"`
	ExecutionPath []string               `json:"execution_path"`
	StartTime     time.Time              `json:"start_time"`
	EndTime       *time.Time             `json:"end_time,omitempty"`
	LastActivity  time.Time              `json:"last_activity"`
	ErrorMessage  string                 `json:"error_message,omitempty"`
	RetryCount    int                    `json:"retry_count"`
	Priority      int                    `json:"priority"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// WorkflowStatus 工作流状态
type WorkflowStatus string

const (
	StatusPending   WorkflowStatus = "pending"
	StatusRunning   WorkflowStatus = "running"
	StatusWaiting   WorkflowStatus = "waiting" // 等待用户输入
	StatusCompleted WorkflowStatus = "completed"
	StatusFailed    WorkflowStatus = "failed"
	StatusCancelled WorkflowStatus = "cancelled"
	StatusSuspended WorkflowStatus = "suspended"
)

// WorkflowTrigger 工作流触发器
type WorkflowTrigger struct {
	Type       string                 `json:"type"` // intent, event, schedule
	Conditions map[string]interface{} `json:"conditions"`
	Priority   int                    `json:"priority"`
}

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	ID            string                 `json:"id"`
	Name          string                 `json:"name"`
	Type          StepType               `json:"type"`
	Description   string                 `json:"description"`
	Action        string                 `json:"action"`
	Parameters    map[string]interface{} `json:"parameters"`
	Conditions    []StepCondition        `json:"conditions"`
	NextSteps     []string               `json:"next_steps"`
	ErrorHandling *ErrorHandling         `json:"error_handling,omitempty"`
	Timeout       time.Duration          `json:"timeout"`
	RetryPolicy   *RetryPolicy           `json:"retry_policy,omitempty"`
	AIGuidance    *AIGuidance            `json:"ai_guidance,omitempty"`
}

// StepType 步骤类型
type StepType string

const (
	StepTypeMessage     StepType = "message"      // AI消息
	StepTypeInput       StepType = "input"        // 用户输入
	StepTypeValidation  StepType = "validation"   // 数据验证
	StepTypeExecution   StepType = "execution"    // 业务执行
	StepTypeCondition   StepType = "condition"    // 条件判断
	StepTypeParallel    StepType = "parallel"     // 并行执行
	StepTypeSubWorkflow StepType = "sub_workflow" // 子工作流
	StepTypeAIAnalysis  StepType = "ai_analysis"  // AI分析
)

// StepCondition 步骤条件
type StepCondition struct {
	Field    string      `json:"field"`
	Operator string      `json:"operator"` // eq, ne, gt, lt, contains, exists
	Value    interface{} `json:"value"`
	Logic    string      `json:"logic"` // and, or
}

// WorkflowCondition 工作流条件
type WorkflowCondition struct {
	ID         string            `json:"id"`
	Expression string            `json:"expression"`
	Variables  map[string]string `json:"variables"`
}

// ErrorHandling 错误处理
type ErrorHandling struct {
	Strategy     string        `json:"strategy"` // retry, skip, fail, custom
	MaxRetries   int           `json:"max_retries"`
	RetryDelay   time.Duration `json:"retry_delay"`
	FallbackStep string        `json:"fallback_step,omitempty"`
}

// RetryPolicy 重试策略
type RetryPolicy struct {
	MaxAttempts int           `json:"max_attempts"`
	Delay       time.Duration `json:"delay"`
	Backoff     string        `json:"backoff"` // linear, exponential
	MaxDelay    time.Duration `json:"max_delay"`
}

// AIGuidance AI引导配置
type AIGuidance struct {
	Enabled     bool     `json:"enabled"`
	Prompt      string   `json:"prompt"`
	Context     string   `json:"context"`
	Suggestions []string `json:"suggestions"`
}

// NewWorkflowEngine 创建工作流引擎
func NewWorkflowEngine(db *gorm.DB, logger *logrus.Logger) *WorkflowEngine {
	engine := &WorkflowEngine{
		db:          db,
		logger:      logger,
		definitions: make(map[string]*WorkflowDefinition),
		instances:   make(map[string]*WorkflowInstance),
	}

	// 初始化组件
	engine.stateManager = NewStateManager(logger)
	engine.stepExecutor = NewStepExecutor(db, logger)
	engine.contextManager = NewWorkflowContextManager(db, logger)
	engine.aiIntegration = NewAIIntegration(logger)
	engine.scheduler = NewWorkflowScheduler(logger)
	engine.eventBus = NewEventBus(logger)
	engine.metricsCollector = NewMetricsCollector(logger)

	return engine
}

// SetServices 设置服务依赖
func (we *WorkflowEngine) SetServices(hostService HostServiceInterface, aiService AIServiceInterface) {
	we.stepExecutor.SetServices(hostService, aiService)
}

// Start 启动工作流引擎
func (we *WorkflowEngine) Start(ctx context.Context) error {
	we.logger.Info("Starting workflow engine")

	// 启动调度器
	if err := we.scheduler.Start(ctx); err != nil {
		return fmt.Errorf("failed to start scheduler: %w", err)
	}

	// 启动事件总线
	if err := we.eventBus.Start(ctx); err != nil {
		return fmt.Errorf("failed to start event bus: %w", err)
	}

	// 加载工作流定义
	if err := we.loadWorkflowDefinitions(); err != nil {
		return fmt.Errorf("failed to load workflow definitions: %w", err)
	}

	// 恢复未完成的工作流实例
	if err := we.recoverInstances(ctx); err != nil {
		we.logger.WithError(err).Warn("Failed to recover some workflow instances")
	}

	we.logger.Info("Workflow engine started successfully")
	return nil
}

// Stop 停止工作流引擎
func (we *WorkflowEngine) Stop(ctx context.Context) error {
	we.logger.Info("Stopping workflow engine")

	// 停止调度器
	if err := we.scheduler.Stop(ctx); err != nil {
		we.logger.WithError(err).Error("Failed to stop scheduler")
	}

	// 停止事件总线
	if err := we.eventBus.Stop(ctx); err != nil {
		we.logger.WithError(err).Error("Failed to stop event bus")
	}

	// 保存所有活跃实例状态
	if err := we.saveActiveInstances(); err != nil {
		we.logger.WithError(err).Error("Failed to save active instances")
	}

	we.logger.Info("Workflow engine stopped")
	return nil
}

// RegisterDefinition 注册工作流定义
func (we *WorkflowEngine) RegisterDefinition(definition *WorkflowDefinition) error {
	we.mutex.Lock()
	defer we.mutex.Unlock()

	// 验证定义
	if err := we.validateDefinition(definition); err != nil {
		return fmt.Errorf("invalid workflow definition: %w", err)
	}

	definition.CreatedAt = time.Now()
	definition.UpdatedAt = time.Now()
	we.definitions[definition.ID] = definition

	we.logger.WithFields(logrus.Fields{
		"workflow_id": definition.ID,
		"name":        definition.Name,
		"category":    definition.Category,
	}).Info("Workflow definition registered")

	return nil
}

// TriggerWorkflow 触发工作流
func (we *WorkflowEngine) TriggerWorkflow(ctx context.Context, req *TriggerWorkflowRequest) (*WorkflowInstance, error) {
	// 查找匹配的工作流定义
	definition := we.findMatchingWorkflow(req)
	if definition == nil {
		return nil, fmt.Errorf("no matching workflow found for request")
	}

	// 创建工作流实例
	instance := &WorkflowInstance{
		ID:            generateInstanceID(),
		DefinitionID:  definition.ID,
		SessionID:     req.SessionID,
		UserID:        req.UserID,
		Status:        StatusPending,
		Variables:     make(map[string]interface{}),
		CollectedData: make(map[string]interface{}),
		ExecutionPath: make([]string, 0),
		StartTime:     time.Now(),
		LastActivity:  time.Now(),
		Priority:      req.Priority,
		Metadata:      req.Metadata,
	}

	// 复制定义变量
	for k, v := range definition.Variables {
		instance.Variables[k] = v
	}

	// 复制请求变量
	for k, v := range req.Variables {
		instance.Variables[k] = v
	}

	we.mutex.Lock()
	we.instances[instance.ID] = instance
	we.mutex.Unlock()

	// 保存到数据库
	if err := we.saveInstance(instance); err != nil {
		return nil, fmt.Errorf("failed to save instance: %w", err)
	}

	// 开始执行
	go we.executeWorkflow(ctx, instance)

	we.logger.WithFields(logrus.Fields{
		"instance_id":   instance.ID,
		"definition_id": definition.ID,
		"session_id":    req.SessionID,
		"user_id":       req.UserID,
	}).Info("Workflow triggered")

	return instance, nil
}

// ProcessUserInput 处理用户输入
func (we *WorkflowEngine) ProcessUserInput(ctx context.Context, instanceID, userInput string) (*WorkflowResponse, error) {
	we.mutex.RLock()
	instance, exists := we.instances[instanceID]
	we.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("workflow instance not found: %s", instanceID)
	}

	if instance.Status != StatusWaiting {
		return nil, fmt.Errorf("workflow instance is not waiting for input")
	}

	// 获取当前步骤
	definition := we.definitions[instance.DefinitionID]
	currentStep := we.findStep(definition, instance.CurrentStep)
	if currentStep == nil {
		return nil, fmt.Errorf("current step not found: %s", instance.CurrentStep)
	}

	// 处理用户输入
	if err := we.processStepInput(ctx, instance, currentStep, userInput); err != nil {
		return nil, fmt.Errorf("failed to process input: %w", err)
	}

	// 继续执行工作流
	go we.executeWorkflow(ctx, instance)

	return &WorkflowResponse{
		InstanceID: instance.ID,
		Status:     instance.Status,
		Message:    "Input processed successfully",
	}, nil
}

// GetInstance 获取工作流实例
func (we *WorkflowEngine) GetInstance(instanceID string) (*WorkflowInstance, error) {
	we.mutex.RLock()
	defer we.mutex.RUnlock()

	instance, exists := we.instances[instanceID]
	if !exists {
		return nil, fmt.Errorf("workflow instance not found: %s", instanceID)
	}

	return instance, nil
}

// GetActiveInstances 获取活跃的工作流实例
func (we *WorkflowEngine) GetActiveInstances(sessionID string) ([]*WorkflowInstance, error) {
	we.mutex.RLock()
	defer we.mutex.RUnlock()

	var instances []*WorkflowInstance
	for _, instance := range we.instances {
		if instance.SessionID == sessionID &&
			(instance.Status == StatusRunning || instance.Status == StatusWaiting) {
			instances = append(instances, instance)
		}
	}

	return instances, nil
}

// executeWorkflow 执行工作流
func (we *WorkflowEngine) executeWorkflow(ctx context.Context, instance *WorkflowInstance) {
	defer func() {
		if r := recover(); r != nil {
			we.logger.WithFields(logrus.Fields{
				"instance_id": instance.ID,
				"panic":       r,
			}).Error("Workflow execution panicked")

			instance.Status = StatusFailed
			instance.ErrorMessage = fmt.Sprintf("Execution panicked: %v", r)
			we.saveInstance(instance)
		}
	}()

	definition := we.definitions[instance.DefinitionID]
	if definition == nil {
		we.logger.WithField("instance_id", instance.ID).Error("Workflow definition not found")
		return
	}

	instance.Status = StatusRunning
	instance.LastActivity = time.Now()

	// 如果没有当前步骤，从第一个步骤开始
	if instance.CurrentStep == "" && len(definition.Steps) > 0 {
		instance.CurrentStep = definition.Steps[0].ID
	}

	for instance.Status == StatusRunning {
		currentStep := we.findStep(definition, instance.CurrentStep)
		if currentStep == nil {
			instance.Status = StatusFailed
			instance.ErrorMessage = fmt.Sprintf("Step not found: %s", instance.CurrentStep)
			break
		}

		// 执行步骤
		result, err := we.executeStep(ctx, instance, currentStep)
		if err != nil {
			we.handleStepError(instance, currentStep, err)
			break
		}

		// 处理步骤结果
		if err := we.processStepResult(instance, currentStep, result); err != nil {
			instance.Status = StatusFailed
			instance.ErrorMessage = err.Error()
			break
		}

		// 检查是否完成
		if instance.CurrentStep == "" {
			instance.Status = StatusCompleted
			instance.EndTime = &[]time.Time{time.Now()}[0]
			break
		}

		// 更新活动时间
		instance.LastActivity = time.Now()

		// 保存状态
		if err := we.saveInstance(instance); err != nil {
			we.logger.WithError(err).Error("Failed to save instance state")
		}

		// 检查超时
		if time.Since(instance.StartTime) > definition.Timeout {
			instance.Status = StatusFailed
			instance.ErrorMessage = "Workflow timeout"
			break
		}
	}

	// 最终保存状态
	we.saveInstance(instance)

	// 发送完成事件
	we.eventBus.Publish(&WorkflowEvent{
		Type:       "workflow_completed",
		InstanceID: instance.ID,
		Status:     string(instance.Status),
		Timestamp:  time.Now(),
	})
}

// executeStep 执行步骤
func (we *WorkflowEngine) executeStep(ctx context.Context, instance *WorkflowInstance, step *WorkflowStep) (*StepResult, error) {
	we.logger.WithFields(logrus.Fields{
		"instance_id": instance.ID,
		"step_id":     step.ID,
		"step_type":   step.Type,
	}).Info("Executing workflow step")

	// 添加到执行路径
	instance.ExecutionPath = append(instance.ExecutionPath, step.ID)

	// 根据步骤类型执行
	switch step.Type {
	case StepTypeMessage:
		return we.executeMessageStep(ctx, instance, step)
	case StepTypeInput:
		return we.executeInputStep(ctx, instance, step)
	case StepTypeValidation:
		return we.executeValidationStep(ctx, instance, step)
	case StepTypeExecution:
		return we.executeBusinessStep(ctx, instance, step)
	case StepTypeCondition:
		return we.executeConditionStep(ctx, instance, step)
	case StepTypeAIAnalysis:
		return we.executeAIAnalysisStep(ctx, instance, step)
	default:
		return nil, fmt.Errorf("unsupported step type: %s", step.Type)
	}
}

// findMatchingWorkflow 查找匹配的工作流
func (we *WorkflowEngine) findMatchingWorkflow(req *TriggerWorkflowRequest) *WorkflowDefinition {
	var bestMatch *WorkflowDefinition
	var bestScore int

	for _, definition := range we.definitions {
		if !definition.Enabled {
			continue
		}

		score := we.calculateMatchScore(definition, req)
		if score > bestScore {
			bestScore = score
			bestMatch = definition
		}
	}

	return bestMatch
}

// calculateMatchScore 计算匹配分数
func (we *WorkflowEngine) calculateMatchScore(definition *WorkflowDefinition, req *TriggerWorkflowRequest) int {
	score := 0

	// 检查触发器匹配
	for _, trigger := range definition.Triggers {
		if trigger.Type == req.TriggerType {
			score += 10

			// 检查意图匹配
			if req.Intent != "" && trigger.Conditions["intent"] == req.Intent {
				score += 20
			}

			// 检查类别匹配
			if req.Category != "" && definition.Category == req.Category {
				score += 15
			}

			// 添加优先级分数
			score += trigger.Priority
		}
	}

	return score
}

// findStep 查找步骤
func (we *WorkflowEngine) findStep(definition *WorkflowDefinition, stepID string) *WorkflowStep {
	for _, step := range definition.Steps {
		if step.ID == stepID {
			return &step
		}
	}
	return nil
}

// validateDefinition 验证工作流定义
func (we *WorkflowEngine) validateDefinition(definition *WorkflowDefinition) error {
	if definition.ID == "" {
		return fmt.Errorf("workflow ID is required")
	}

	if definition.Name == "" {
		return fmt.Errorf("workflow name is required")
	}

	if len(definition.Steps) == 0 {
		return fmt.Errorf("workflow must have at least one step")
	}

	// 验证步骤引用
	stepIDs := make(map[string]bool)
	for _, step := range definition.Steps {
		stepIDs[step.ID] = true
	}

	for _, step := range definition.Steps {
		for _, nextStep := range step.NextSteps {
			if !stepIDs[nextStep] {
				return fmt.Errorf("step %s references non-existent step %s", step.ID, nextStep)
			}
		}
	}

	return nil
}

// generateInstanceID 生成实例ID
func generateInstanceID() string {
	return fmt.Sprintf("wf_%d_%d", time.Now().UnixNano(), time.Now().Unix())
}
