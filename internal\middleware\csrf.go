package middleware

import (
	"crypto/rand"
	"crypto/subtle"
	"encoding/base64"
	"fmt"
	"net/http"
	"strings"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/gin-gonic/gin"
)

// CSRFConfig CSRF配置
type CSRFConfig struct {
	TokenLength    int           // 令牌长度
	TokenLifetime  time.Duration // 令牌生命周期
	CookieName     string        // Cookie名称
	HeaderName     string        // 请求头名称
	FormFieldName  string        // 表单字段名称
	SecureCookie   bool          // 安全Cookie
	SameSite       http.SameSite // SameSite属性
	IgnoreMethods  []string      // 忽略的HTTP方法
	TrustedOrigins []string      // 信任的来源
}

// DefaultCSRFConfig 默认CSRF配置
func DefaultCSRFConfig() CSRFConfig {
	return CSRFConfig{
		TokenLength:    32,
		TokenLifetime:  24 * time.Hour,
		CookieName:     "_csrf_token",
		HeaderName:     "X-CSRF-Token",
		FormFieldName:  "_csrf_token",
		SecureCookie:   true,
		SameSite:       http.SameSiteStrictMode,
		IgnoreMethods:  []string{"GET", "HEAD", "OPTIONS", "TRACE"},
		TrustedOrigins: []string{},
	}
}

// CSRFTokenStore CSRF令牌存储
type CSRFTokenStore struct {
	tokens map[string]*CSRFToken
	mu     sync.RWMutex
}

// CSRFToken CSRF令牌
type CSRFToken struct {
	Value     string
	CreatedAt time.Time
	ExpiresAt time.Time
	UserID    int64
	SessionID string
}

// NewCSRFTokenStore 创建CSRF令牌存储
func NewCSRFTokenStore() *CSRFTokenStore {
	store := &CSRFTokenStore{
		tokens: make(map[string]*CSRFToken),
	}

	// 启动清理goroutine
	go store.cleanup()

	return store
}

// GenerateToken 生成CSRF令牌
func (s *CSRFTokenStore) GenerateToken(userID int64, sessionID string, lifetime time.Duration) (string, error) {
	s.mu.Lock()
	defer s.mu.Unlock()

	// 生成随机令牌
	tokenBytes := make([]byte, 32)
	if _, err := rand.Read(tokenBytes); err != nil {
		return "", fmt.Errorf("failed to generate CSRF token: %w", err)
	}

	tokenValue := base64.URLEncoding.EncodeToString(tokenBytes)

	// 创建令牌对象
	token := &CSRFToken{
		Value:     tokenValue,
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(lifetime),
		UserID:    userID,
		SessionID: sessionID,
	}

	// 存储令牌
	s.tokens[tokenValue] = token

	return tokenValue, nil
}

// ValidateToken 验证CSRF令牌
func (s *CSRFTokenStore) ValidateToken(tokenValue string, userID int64, sessionID string) bool {
	s.mu.RLock()
	defer s.mu.RUnlock()

	token, exists := s.tokens[tokenValue]
	if !exists {
		return false
	}

	// 检查是否过期
	if time.Now().After(token.ExpiresAt) {
		return false
	}

	// 检查用户ID和会话ID
	if token.UserID != userID || token.SessionID != sessionID {
		return false
	}

	return true
}

// DeleteToken 删除CSRF令牌
func (s *CSRFTokenStore) DeleteToken(tokenValue string) {
	s.mu.Lock()
	defer s.mu.Unlock()
	delete(s.tokens, tokenValue)
}

// cleanup 清理过期令牌
func (s *CSRFTokenStore) cleanup() {
	ticker := time.NewTicker(1 * time.Hour)
	defer ticker.Stop()

	for range ticker.C {
		s.mu.Lock()
		now := time.Now()
		for tokenValue, token := range s.tokens {
			if now.After(token.ExpiresAt) {
				delete(s.tokens, tokenValue)
			}
		}
		s.mu.Unlock()
	}
}

// CSRFMiddleware CSRF保护中间件
func CSRFMiddleware(config CSRFConfig, store *CSRFTokenStore) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否需要CSRF保护
		if shouldIgnoreMethod(c.Request.Method, config.IgnoreMethods) {
			c.Next()
			return
		}

		// 检查是否为信任的来源
		if isTrustedOrigin(c.Request, config.TrustedOrigins) {
			c.Next()
			return
		}

		// 获取用户信息
		userID, exists := GetUserIDFromContext(c)
		if !exists {
			// 未认证用户，跳过CSRF检查
			c.Next()
			return
		}

		sessionID := c.GetString("session_id")
		if sessionID == "" {
			sessionID = c.GetString("request_id") // 使用请求ID作为备选
		}

		// 验证CSRF令牌
		if !validateCSRFToken(c, config, store, userID, sessionID) {
			AbortWithAppError(c, model.ErrCodeForbidden, "CSRF token validation failed", nil)
			return
		}

		c.Next()
	}
}

// GenerateCSRFToken 生成CSRF令牌的处理器
func GenerateCSRFToken(config CSRFConfig, store *CSRFTokenStore) gin.HandlerFunc {
	return func(c *gin.Context) {
		requestID := GetRequestID(c)

		// 获取用户信息
		userID, exists := GetUserIDFromContext(c)
		if !exists {
			AbortWithAppError(c, model.ErrCodeUnauthorized, "User not authenticated", nil)
			return
		}

		sessionID := c.GetString("session_id")
		if sessionID == "" {
			sessionID = requestID
		}

		// 生成CSRF令牌
		token, err := store.GenerateToken(userID, sessionID, config.TokenLifetime)
		if err != nil {
			AbortWithAppError(c, model.ErrCodeInternalError, "Failed to generate CSRF token", err)
			return
		}

		// 设置Cookie
		c.SetCookie(
			config.CookieName,
			token,
			int(config.TokenLifetime.Seconds()),
			"/",
			"",
			config.SecureCookie,
			true, // HttpOnly
		)

		// 返回令牌
		response := model.SuccessResponse(gin.H{
			"csrf_token": token,
			"expires_in": int(config.TokenLifetime.Seconds()),
		}, requestID)

		c.JSON(http.StatusOK, response)
	}
}

// validateCSRFToken 验证CSRF令牌
func validateCSRFToken(c *gin.Context, config CSRFConfig, store *CSRFTokenStore, userID int64, sessionID string) bool {
	// 从多个来源获取令牌
	token := getCSRFTokenFromRequest(c, config)
	if token == "" {
		return false
	}

	// 验证令牌
	return store.ValidateToken(token, userID, sessionID)
}

// getCSRFTokenFromRequest 从请求中获取CSRF令牌
func getCSRFTokenFromRequest(c *gin.Context, config CSRFConfig) string {
	// 1. 从请求头获取
	if token := c.GetHeader(config.HeaderName); token != "" {
		return token
	}

	// 2. 从表单字段获取
	if token := c.PostForm(config.FormFieldName); token != "" {
		return token
	}

	// 3. 从Cookie获取
	if token, err := c.Cookie(config.CookieName); err == nil && token != "" {
		return token
	}

	return ""
}

// shouldIgnoreMethod 检查是否应该忽略该HTTP方法
func shouldIgnoreMethod(method string, ignoreMethods []string) bool {
	for _, ignoreMethod := range ignoreMethods {
		if strings.EqualFold(method, ignoreMethod) {
			return true
		}
	}
	return false
}

// isTrustedOrigin 检查是否为信任的来源
func isTrustedOrigin(req *http.Request, trustedOrigins []string) bool {
	origin := req.Header.Get("Origin")
	if origin == "" {
		return false
	}

	for _, trusted := range trustedOrigins {
		if origin == trusted {
			return true
		}
	}

	return false
}

// DoubleSubmitCookieMiddleware 双重提交Cookie CSRF保护
func DoubleSubmitCookieMiddleware(config CSRFConfig) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否需要CSRF保护
		if shouldIgnoreMethod(c.Request.Method, config.IgnoreMethods) {
			c.Next()
			return
		}

		// 获取Cookie中的令牌
		cookieToken, err := c.Cookie(config.CookieName)
		if err != nil || cookieToken == "" {
			AbortWithAppError(c, model.ErrCodeForbidden, "CSRF cookie not found", nil)
			return
		}

		// 获取请求中的令牌
		requestToken := c.GetHeader(config.HeaderName)
		if requestToken == "" {
			requestToken = c.PostForm(config.FormFieldName)
		}

		if requestToken == "" {
			AbortWithAppError(c, model.ErrCodeForbidden, "CSRF token not found in request", nil)
			return
		}

		// 比较令牌
		if subtle.ConstantTimeCompare([]byte(cookieToken), []byte(requestToken)) != 1 {
			AbortWithAppError(c, model.ErrCodeForbidden, "CSRF token mismatch", nil)
			return
		}

		c.Next()
	}
}

// SetCSRFCookie 设置CSRF Cookie
func SetCSRFCookie(c *gin.Context, config CSRFConfig) error {
	// 生成随机令牌
	tokenBytes := make([]byte, config.TokenLength)
	if _, err := rand.Read(tokenBytes); err != nil {
		return fmt.Errorf("failed to generate CSRF token: %w", err)
	}

	token := base64.URLEncoding.EncodeToString(tokenBytes)

	// 设置Cookie
	c.SetCookie(
		config.CookieName,
		token,
		int(config.TokenLifetime.Seconds()),
		"/",
		"",
		config.SecureCookie,
		true, // HttpOnly
	)

	// 将令牌存储到上下文中，供模板使用
	c.Set("csrf_token", token)

	return nil
}

// GetCSRFToken 从上下文获取CSRF令牌
func GetCSRFToken(c *gin.Context) string {
	if token, exists := c.Get("csrf_token"); exists {
		if tokenStr, ok := token.(string); ok {
			return tokenStr
		}
	}
	return ""
}
