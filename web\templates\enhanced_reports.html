<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>增强报表系统 - AI运维管理平台</title>
    <link rel="stylesheet" href="/static/css/design-system.css">
    <script src="https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.0/dist/echarts.min.js"></script>
    <style>
        .reports-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
            background: var(--bg-primary);
            min-height: 100vh;
        }

        .reports-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            padding: 20px;
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
        }

        .reports-title {
            font-size: 28px;
            font-weight: 600;
            color: var(--text-primary);
            margin: 0;
        }

        .reports-actions {
            display: flex;
            gap: 12px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-secondary {
            background: var(--bg-secondary);
            color: var(--text-primary);
            border: 1px solid var(--border-color);
            padding: 12px 24px;
            border-radius: 8px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-secondary:hover {
            background: var(--bg-hover);
        }

        .reports-grid {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 20px;
            height: calc(100vh - 200px);
        }

        .reports-sidebar {
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 20px;
            overflow-y: auto;
        }

        .reports-main {
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 1px solid var(--border-color);
            padding: 20px;
            overflow-y: auto;
        }

        .sidebar-section {
            margin-bottom: 30px;
        }

        .sidebar-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid var(--border-color);
        }

        .template-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .template-item {
            padding: 12px;
            margin-bottom: 8px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .template-item:hover {
            background: var(--bg-hover);
            transform: translateX(4px);
        }

        .template-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .template-name {
            font-weight: 500;
            margin-bottom: 4px;
        }

        .template-desc {
            font-size: 12px;
            opacity: 0.7;
        }

        .report-builder {
            display: none;
        }

        .report-builder.active {
            display: block;
        }

        .builder-section {
            margin-bottom: 25px;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .builder-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            font-weight: 500;
            color: var(--text-primary);
            margin-bottom: 5px;
        }

        .form-input {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .form-select {
            width: 100%;
            padding: 10px 12px;
            border: 1px solid var(--border-color);
            border-radius: 6px;
            background: var(--bg-secondary);
            color: var(--text-primary);
            font-size: 14px;
        }

        .chart-types {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
            gap: 10px;
        }

        .chart-type {
            padding: 12px;
            text-align: center;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .chart-type:hover {
            background: var(--bg-hover);
        }

        .chart-type.selected {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .chart-icon {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .chart-name {
            font-size: 12px;
            font-weight: 500;
        }

        .report-preview {
            display: none;
            padding: 20px;
            background: var(--bg-primary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
        }

        .report-preview.active {
            display: block;
        }

        .preview-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid var(--border-color);
        }

        .preview-title {
            font-size: 24px;
            font-weight: 600;
            color: var(--text-primary);
        }

        .preview-meta {
            font-size: 14px;
            color: var(--text-secondary);
        }

        .preview-charts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .preview-chart {
            background: var(--bg-secondary);
            border-radius: 8px;
            border: 1px solid var(--border-color);
            padding: 20px;
        }

        .chart-title {
            font-size: 16px;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 15px;
        }

        .chart-container {
            height: 300px;
            position: relative;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: var(--bg-primary);
            border-radius: 3px;
            overflow: hidden;
            margin: 20px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .status-indicator {
            display: inline-flex;
            align-items: center;
            gap: 8px;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-generating {
            background: rgba(255, 193, 7, 0.1);
            color: #ffc107;
            border: 1px solid rgba(255, 193, 7, 0.3);
        }

        .status-completed {
            background: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .status-failed {
            background: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }

        .loading-spinner {
            display: inline-block;
            width: 12px;
            height: 12px;
            border: 2px solid transparent;
            border-top: 2px solid currentColor;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .export-options {
            display: flex;
            gap: 10px;
            margin-top: 20px;
        }

        .export-btn {
            padding: 8px 16px;
            background: var(--bg-secondary);
            border: 1px solid var(--border-color);
            border-radius: 6px;
            color: var(--text-primary);
            text-decoration: none;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
        }

        .export-btn:hover {
            background: var(--bg-hover);
            transform: translateY(-1px);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1000;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: linear-gradient(135deg, #28a745, #20c997);
        }

        .notification.error {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
        }

        .notification.info {
            background: linear-gradient(135deg, #17a2b8, #007bff);
        }
    </style>
</head>
<body>
    <div class="reports-container">
        <!-- 报表头部 -->
        <div class="reports-header">
            <h1 class="reports-title">📊 增强报表系统</h1>
            <div class="reports-actions">
                <button class="btn-secondary" onclick="showTemplates()">📋 模板管理</button>
                <button class="btn-primary" onclick="showReportBuilder()">✨ 创建报表</button>
            </div>
        </div>

        <!-- 报表主体 -->
        <div class="reports-grid">
            <!-- 侧边栏 -->
            <div class="reports-sidebar">
                <div class="sidebar-section">
                    <h3 class="sidebar-title">📋 报表模板</h3>
                    <ul class="template-list" id="templateList">
                        <!-- 动态加载模板列表 -->
                    </ul>
                </div>

                <div class="sidebar-section">
                    <h3 class="sidebar-title">📈 图表类型</h3>
                    <div class="chart-types" id="chartTypes">
                        <!-- 动态加载图表类型 -->
                    </div>
                </div>
            </div>

            <!-- 主内容区 -->
            <div class="reports-main">
                <!-- 报表构建器 -->
                <div class="report-builder active" id="reportBuilder">
                    <div class="builder-section">
                        <h3 class="builder-title">📝 基本信息</h3>
                        <div class="form-group">
                            <label class="form-label">报表标题</label>
                            <input type="text" class="form-input" id="reportTitle" placeholder="请输入报表标题">
                        </div>
                        <div class="form-group">
                            <label class="form-label">报表类型</label>
                            <select class="form-select" id="reportType">
                                <option value="operation">运维操作报表</option>
                                <option value="system_health">系统健康报表</option>
                                <option value="ai_usage">AI使用报表</option>
                                <option value="performance">性能分析报表</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">时间范围</label>
                            <select class="form-select" id="timeRange">
                                <option value="1h">最近1小时</option>
                                <option value="24h" selected>最近24小时</option>
                                <option value="7d">最近7天</option>
                                <option value="30d">最近30天</option>
                            </select>
                        </div>
                    </div>

                    <div class="builder-section">
                        <h3 class="builder-title">📊 图表配置</h3>
                        <div class="chart-types" id="selectedChartTypes">
                            <!-- 选中的图表类型 -->
                        </div>
                    </div>

                    <div class="builder-section">
                        <h3 class="builder-title">⚙️ 高级选项</h3>
                        <div class="form-group">
                            <label class="form-label">模板</label>
                            <select class="form-select" id="templateSelect">
                                <option value="">使用默认模板</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label class="form-label">导出格式</label>
                            <select class="form-select" id="exportFormat">
                                <option value="json">JSON</option>
                                <option value="csv">CSV</option>
                                <option value="pdf">PDF</option>
                                <option value="html">HTML</option>
                            </select>
                        </div>
                    </div>

                    <div class="builder-section">
                        <button class="btn-primary" onclick="generateReport()" style="width: 100%; padding: 15px;">
                            🚀 生成报表
                        </button>
                    </div>
                </div>

                <!-- 报表预览 -->
                <div class="report-preview" id="reportPreview">
                    <div class="preview-header">
                        <div>
                            <h2 class="preview-title" id="previewTitle">报表标题</h2>
                            <div class="preview-meta" id="previewMeta">生成时间：2024-01-01 12:00:00</div>
                        </div>
                        <div>
                            <span class="status-indicator status-generating" id="reportStatus">
                                <span class="loading-spinner"></span>
                                生成中...
                            </span>
                        </div>
                    </div>

                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>

                    <div class="preview-charts" id="previewCharts">
                        <!-- 动态生成图表 -->
                    </div>

                    <div class="export-options" id="exportOptions" style="display: none;">
                        <a href="#" class="export-btn" onclick="exportReport('json')">📄 导出JSON</a>
                        <a href="#" class="export-btn" onclick="exportReport('csv')">📊 导出CSV</a>
                        <a href="#" class="export-btn" onclick="exportReport('pdf')">📋 导出PDF</a>
                        <a href="#" class="export-btn" onclick="exportReport('html')">🌐 导出HTML</a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 通知组件 -->
    <div class="notification" id="notification"></div>

    <script>
        // 全局变量
        let currentReportId = null;
        let selectedChartTypes = [];
        let availableTemplates = [];
        let availableChartTypes = [];

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadTemplates();
            loadChartTypes();
            initializeEventListeners();
        });

        // 加载模板列表
        async function loadTemplates() {
            try {
                const response = await fetch('/api/v1/enhanced-reports/templates');
                const result = await response.json();
                
                if (result.success) {
                    availableTemplates = result.data;
                    renderTemplateList();
                    renderTemplateSelect();
                }
            } catch (error) {
                console.error('Failed to load templates:', error);
                showNotification('加载模板失败', 'error');
            }
        }

        // 加载图表类型
        async function loadChartTypes() {
            try {
                const response = await fetch('/api/v1/enhanced-reports/chart-types');
                const result = await response.json();
                
                if (result.success) {
                    availableChartTypes = result.data;
                    renderChartTypes();
                }
            } catch (error) {
                console.error('Failed to load chart types:', error);
                showNotification('加载图表类型失败', 'error');
            }
        }

        // 渲染模板列表
        function renderTemplateList() {
            const templateList = document.getElementById('templateList');
            templateList.innerHTML = '';

            availableTemplates.forEach(template => {
                const li = document.createElement('li');
                li.className = 'template-item';
                li.onclick = () => selectTemplate(template);
                
                li.innerHTML = `
                    <div class="template-name">${template.name}</div>
                    <div class="template-desc">${template.description}</div>
                `;
                
                templateList.appendChild(li);
            });
        }

        // 渲染模板选择器
        function renderTemplateSelect() {
            const templateSelect = document.getElementById('templateSelect');
            templateSelect.innerHTML = '<option value="">使用默认模板</option>';

            availableTemplates.forEach(template => {
                const option = document.createElement('option');
                option.value = template.id;
                option.textContent = template.name;
                templateSelect.appendChild(option);
            });
        }

        // 渲染图表类型
        function renderChartTypes() {
            const chartTypes = document.getElementById('chartTypes');
            chartTypes.innerHTML = '';

            availableChartTypes.forEach(chartType => {
                const div = document.createElement('div');
                div.className = 'chart-type';
                div.onclick = () => toggleChartType(chartType.type);
                
                div.innerHTML = `
                    <div class="chart-icon">${getChartIcon(chartType.type)}</div>
                    <div class="chart-name">${chartType.name}</div>
                `;
                
                chartTypes.appendChild(div);
            });
        }

        // 获取图表图标
        function getChartIcon(type) {
            const icons = {
                'line': '📈',
                'bar': '📊',
                'pie': '🥧',
                'area': '📉',
                'scatter': '🔸',
                'heatmap': '🔥',
                'gauge': '⏱️',
                'radar': '🎯'
            };
            return icons[type] || '📊';
        }

        // 切换图表类型选择
        function toggleChartType(type) {
            const index = selectedChartTypes.indexOf(type);
            if (index > -1) {
                selectedChartTypes.splice(index, 1);
            } else {
                selectedChartTypes.push(type);
            }
            
            updateChartTypeSelection();
            updateSelectedChartTypes();
        }

        // 更新图表类型选择状态
        function updateChartTypeSelection() {
            const chartTypes = document.querySelectorAll('#chartTypes .chart-type');
            chartTypes.forEach((element, index) => {
                const type = availableChartTypes[index].type;
                if (selectedChartTypes.includes(type)) {
                    element.classList.add('selected');
                } else {
                    element.classList.remove('selected');
                }
            });
        }

        // 更新选中的图表类型显示
        function updateSelectedChartTypes() {
            const container = document.getElementById('selectedChartTypes');
            container.innerHTML = '';

            if (selectedChartTypes.length === 0) {
                container.innerHTML = '<p style="color: var(--text-secondary); text-align: center; padding: 20px;">请选择图表类型</p>';
                return;
            }

            selectedChartTypes.forEach(type => {
                const chartType = availableChartTypes.find(ct => ct.type === type);
                if (chartType) {
                    const div = document.createElement('div');
                    div.className = 'chart-type selected';
                    div.innerHTML = `
                        <div class="chart-icon">${getChartIcon(type)}</div>
                        <div class="chart-name">${chartType.name}</div>
                    `;
                    container.appendChild(div);
                }
            });
        }

        // 选择模板
        function selectTemplate(template) {
            // 移除之前的选中状态
            document.querySelectorAll('.template-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加选中状态
            event.currentTarget.classList.add('active');
            
            // 更新表单
            document.getElementById('reportTitle').value = template.name;
            document.getElementById('reportType').value = template.type;
            document.getElementById('templateSelect').value = template.id;
            
            showNotification(`已选择模板：${template.name}`, 'info');
        }

        // 显示报表构建器
        function showReportBuilder() {
            document.getElementById('reportBuilder').classList.add('active');
            document.getElementById('reportPreview').classList.remove('active');
        }

        // 显示模板管理
        function showTemplates() {
            showNotification('模板管理功能开发中...', 'info');
        }

        // 生成报表
        async function generateReport() {
            const title = document.getElementById('reportTitle').value || '自动生成报表';
            const type = document.getElementById('reportType').value;
            const timeRange = document.getElementById('timeRange').value;
            const templateId = document.getElementById('templateSelect').value;
            const exportFormat = document.getElementById('exportFormat').value;

            const requestData = {
                title: title,
                type: type,
                time_range: timeRange,
                template_id: templateId,
                chart_types: selectedChartTypes,
                export_format: exportFormat,
                priority: 'normal'
            };

            try {
                // 显示预览界面
                showReportPreview(title);
                
                // 发送生成请求
                const response = await fetch('/api/v1/enhanced-reports/generate', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestData)
                });

                const result = await response.json();
                
                if (result.success) {
                    currentReportId = result.data.id;
                    updateReportProgress(result.data);
                    showNotification('报表生成成功！', 'success');
                } else {
                    throw new Error(result.error);
                }
            } catch (error) {
                console.error('Failed to generate report:', error);
                showNotification('报表生成失败：' + error.message, 'error');
                updateReportStatus('failed');
            }
        }

        // 显示报表预览
        function showReportPreview(title) {
            document.getElementById('reportBuilder').classList.remove('active');
            document.getElementById('reportPreview').classList.add('active');
            
            document.getElementById('previewTitle').textContent = title;
            document.getElementById('previewMeta').textContent = `生成时间：${new Date().toLocaleString()}`;
            
            updateReportStatus('generating');
            updateProgress(0);
        }

        // 更新报表进度
        function updateReportProgress(reportData) {
            updateProgress(reportData.progress);
            
            if (reportData.status === 'completed') {
                updateReportStatus('completed');
                renderReportCharts(reportData);
                showExportOptions();
            } else if (reportData.status === 'failed') {
                updateReportStatus('failed');
            }
        }

        // 更新进度条
        function updateProgress(progress) {
            const progressFill = document.getElementById('progressFill');
            progressFill.style.width = progress + '%';
        }

        // 更新报表状态
        function updateReportStatus(status) {
            const statusElement = document.getElementById('reportStatus');
            statusElement.className = `status-indicator status-${status}`;
            
            const statusText = {
                'generating': '<span class="loading-spinner"></span> 生成中...',
                'completed': '✅ 生成完成',
                'failed': '❌ 生成失败'
            };
            
            statusElement.innerHTML = statusText[status] || status;
        }

        // 渲染报表图表
        function renderReportCharts(reportData) {
            const chartsContainer = document.getElementById('previewCharts');
            chartsContainer.innerHTML = '';

            // 模拟图表数据
            const mockCharts = [
                { id: 'chart1', title: '操作趋势', type: 'line' },
                { id: 'chart2', title: '类型分布', type: 'pie' },
                { id: 'chart3', title: '成功率', type: 'gauge' },
                { id: 'chart4', title: '资源使用', type: 'area' }
            ];

            mockCharts.forEach(chart => {
                const chartDiv = document.createElement('div');
                chartDiv.className = 'preview-chart';
                chartDiv.innerHTML = `
                    <h4 class="chart-title">${chart.title}</h4>
                    <div class="chart-container">
                        <canvas id="${chart.id}"></canvas>
                    </div>
                `;
                chartsContainer.appendChild(chartDiv);

                // 渲染图表
                setTimeout(() => renderChart(chart.id, chart.type), 100);
            });
        }

        // 渲染单个图表
        function renderChart(canvasId, type) {
            const ctx = document.getElementById(canvasId);
            if (!ctx) return;

            const config = getChartConfig(type);
            new Chart(ctx, config);
        }

        // 获取图表配置
        function getChartConfig(type) {
            const baseConfig = {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: true,
                        position: 'top'
                    }
                }
            };

            switch (type) {
                case 'line':
                    return {
                        type: 'line',
                        data: {
                            labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                            datasets: [{
                                label: '操作数量',
                                data: [12, 19, 3, 5, 2, 3],
                                borderColor: '#667eea',
                                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                                tension: 0.4
                            }]
                        },
                        options: baseConfig
                    };
                case 'pie':
                    return {
                        type: 'pie',
                        data: {
                            labels: ['SSH操作', '数据库查询', '文件操作', '系统监控'],
                            datasets: [{
                                data: [45, 32, 28, 15],
                                backgroundColor: ['#667eea', '#764ba2', '#f093fb', '#f5576c']
                            }]
                        },
                        options: baseConfig
                    };
                case 'gauge':
                    return {
                        type: 'doughnut',
                        data: {
                            labels: ['成功', '失败'],
                            datasets: [{
                                data: [94.8, 5.2],
                                backgroundColor: ['#28a745', '#dc3545'],
                                circumference: 180,
                                rotation: 270
                            }]
                        },
                        options: {
                            ...baseConfig,
                            cutout: '70%'
                        }
                    };
                case 'area':
                    return {
                        type: 'line',
                        data: {
                            labels: ['00:00', '04:00', '08:00', '12:00', '16:00', '20:00'],
                            datasets: [{
                                label: 'CPU使用率',
                                data: [45, 52, 38, 65, 72, 48],
                                borderColor: '#667eea',
                                backgroundColor: 'rgba(102, 126, 234, 0.3)',
                                fill: true
                            }]
                        },
                        options: baseConfig
                    };
                default:
                    return {
                        type: 'bar',
                        data: {
                            labels: ['A', 'B', 'C', 'D'],
                            datasets: [{
                                label: '数据',
                                data: [12, 19, 3, 5],
                                backgroundColor: '#667eea'
                            }]
                        },
                        options: baseConfig
                    };
            }
        }

        // 显示导出选项
        function showExportOptions() {
            document.getElementById('exportOptions').style.display = 'flex';
        }

        // 导出报表
        function exportReport(format) {
            if (!currentReportId) {
                showNotification('没有可导出的报表', 'error');
                return;
            }

            const url = `/api/v1/enhanced-reports/${currentReportId}/export/${format}`;
            window.open(url, '_blank');
            showNotification(`正在导出${format.toUpperCase()}格式...`, 'info');
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            notification.textContent = message;
            notification.className = `notification ${type}`;
            notification.classList.add('show');

            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }

        // 初始化事件监听器
        function initializeEventListeners() {
            // 表单变化监听
            document.getElementById('reportType').addEventListener('change', function() {
                // 根据报表类型自动选择合适的图表类型
                const type = this.value;
                selectedChartTypes = getDefaultChartTypes(type);
                updateChartTypeSelection();
                updateSelectedChartTypes();
            });
        }

        // 获取默认图表类型
        function getDefaultChartTypes(reportType) {
            const defaults = {
                'operation': ['line', 'pie', 'gauge'],
                'system_health': ['area', 'bar', 'heatmap'],
                'ai_usage': ['line', 'scatter', 'radar'],
                'performance': ['line', 'bar']
            };
            return defaults[reportType] || ['line'];
        }

        // 初始化默认图表类型
        selectedChartTypes = getDefaultChartTypes('operation');
        setTimeout(() => {
            updateChartTypeSelection();
            updateSelectedChartTypes();
        }, 100);
    </script>
</body>
</html>
