package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/ai"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// MockSmartDeepSeekService 模拟智能DeepSeek服务
type MockSmartDeepSeekService struct {
	logger *logrus.Logger
}

func NewMockSmartDeepSeekService(logger *logrus.Logger) ai.SmartDeepSeekService {
	return &MockSmartDeepSeekService{logger: logger}
}

func (m *MockSmartDeepSeekService) UnderstandIntent(ctx context.Context, req *ai.IntentUnderstandingRequest) (*ai.IntentUnderstandingResponse, error) {
	m.logger.Info("模拟意图理解: " + req.UserMessage)
	
	// 简单的关键词匹配逻辑
	message := req.UserMessage
	
	if contains(message, []string{"查看", "显示", "列出", "主机", "服务器"}) {
		return &ai.IntentUnderstandingResponse{
			IntentUnderstanding: "用户想要查看主机列表信息",
			OperationType:       "database",
			Confidence:          0.9,
			Parameters: map[string]interface{}{
				"action": "select",
				"table":  "hosts",
			},
			RequiredSteps:   []string{"查询数据库", "格式化结果", "返回给用户"},
			ExpectedOutcome: "显示主机列表",
			Metadata:        make(map[string]interface{}),
		}, nil
	}
	
	if contains(message, []string{"添加", "新增", "创建", "主机"}) {
		return &ai.IntentUnderstandingResponse{
			IntentUnderstanding: "用户想要添加新的主机",
			OperationType:       "database",
			Confidence:          0.85,
			Parameters: map[string]interface{}{
				"action": "insert",
				"table":  "hosts",
			},
			RequiredSteps:   []string{"验证参数", "插入数据库", "返回结果"},
			ExpectedOutcome: "成功添加主机",
			Metadata:        make(map[string]interface{}),
		}, nil
	}
	
	if contains(message, []string{"删除", "移除", "清除"}) {
		return &ai.IntentUnderstandingResponse{
			IntentUnderstanding: "用户想要删除数据",
			OperationType:       "database",
			Confidence:          0.8,
			Parameters: map[string]interface{}{
				"action": "delete",
				"table":  "hosts",
			},
			RequiredSteps:   []string{"安全验证", "执行删除", "记录日志"},
			ExpectedOutcome: "删除指定数据",
			Metadata:        make(map[string]interface{}),
		}, nil
	}
	
	if contains(message, []string{"CPU", "内存", "磁盘", "系统", "监控"}) {
		return &ai.IntentUnderstandingResponse{
			IntentUnderstanding: "用户想要查看系统监控信息",
			OperationType:       "system",
			Confidence:          0.88,
			Parameters: map[string]interface{}{
				"action": "monitor",
				"type":   "system_status",
			},
			RequiredSteps:   []string{"连接目标主机", "执行监控命令", "收集数据", "格式化输出"},
			ExpectedOutcome: "显示系统状态信息",
			Metadata:        make(map[string]interface{}),
		}, nil
	}
	
	// 默认为对话类型
	return &ai.IntentUnderstandingResponse{
		IntentUnderstanding: "用户进行一般对话交流",
		OperationType:       "conversation",
		Confidence:          0.7,
		Parameters:          make(map[string]interface{}),
		RequiredSteps:       []string{"理解用户意图", "生成友好回复"},
		ExpectedOutcome:     "提供有帮助的回复",
		Metadata:            make(map[string]interface{}),
	}, nil
}

func (m *MockSmartDeepSeekService) GenerateCode(ctx context.Context, req *ai.CodeGenerationRequest) (*ai.GeneratedCode, error) {
	m.logger.Info("模拟代码生成: " + req.OperationType)
	
	code := &ai.GeneratedCode{
		SQL:      []string{},
		Shell:    []string{},
		APICalls: []ai.APICall{},
		Scripts:  make(map[string]string),
		Metadata: make(map[string]interface{}),
	}
	
	switch req.OperationType {
	case "database":
		if req.Parameters["action"] == "select" {
			code.SQL = []string{"SELECT * FROM hosts WHERE deleted_at IS NULL"}
		} else if req.Parameters["action"] == "insert" {
			code.SQL = []string{"INSERT INTO hosts (name, ip, username, status) VALUES ('new-host', '************0', 'admin', 'offline')"}
		} else if req.Parameters["action"] == "delete" {
			code.SQL = []string{"DELETE FROM hosts WHERE id = ?"}
		}
	case "system":
		code.Shell = []string{
			"top -bn1 | grep 'Cpu(s)'",
			"free -h",
			"df -h",
		}
	}
	
	return code, nil
}

func (m *MockSmartDeepSeekService) AssessSecurityRisk(ctx context.Context, req *ai.SecurityRiskRequest) (*ai.RiskAssessment, error) {
	m.logger.Info("模拟安全风险评估: " + req.OperationType)
	
	risk := &ai.RiskAssessment{
		Level:       "low",
		Score:       0.2,
		Factors:     []string{"模拟评估"},
		Warnings:    []string{},
		Mitigations: []string{},
	}
	
	// 检查是否包含删除操作
	if req.Code != nil {
		for _, sql := range req.Code.SQL {
			if contains(sql, []string{"DELETE", "DROP", "TRUNCATE"}) {
				risk.Level = "high"
				risk.Score = 0.8
				risk.Factors = append(risk.Factors, "包含删除操作")
				risk.Warnings = append(risk.Warnings, "删除操作不可逆")
				risk.Mitigations = append(risk.Mitigations, "建议先备份数据")
				break
			}
		}
	}
	
	return risk, nil
}

func (m *MockSmartDeepSeekService) GenerateResponse(ctx context.Context, req *ai.ResponseGenerationRequest) (*ai.ResponseGenerationResult, error) {
	return &ai.ResponseGenerationResult{
		Message:        "操作已完成",
		Suggestions:    []string{"查看结果", "执行其他操作"},
		NextActions:    []string{"继续操作"},
		AdditionalInfo: "模拟响应生成",
		Metadata:       make(map[string]interface{}),
	}, nil
}

func (m *MockSmartDeepSeekService) LearnFromInteraction(ctx context.Context, req *ai.LearningRequest) error {
	m.logger.Info("模拟学习交互数据")
	return nil
}

func (m *MockSmartDeepSeekService) GetServiceStatus() *ai.ServiceStatus {
	return &ai.ServiceStatus{
		IsHealthy:         true,
		LastHealthCheck:   time.Now(),
		APIResponseTime:   100 * time.Millisecond,
		SuccessRate:       0.95,
		RequestsPerMinute: 10,
		ErrorRate:         0.05,
		ModelVersion:      "mock-1.0",
		AvailableFeatures: []string{"intent_understanding", "code_generation", "risk_assessment"},
		Metrics:           make(map[string]interface{}),
	}
}

// contains 检查字符串是否包含任何关键词
func contains(text string, keywords []string) bool {
	for _, keyword := range keywords {
		if len(text) >= len(keyword) {
			for i := 0; i <= len(text)-len(keyword); i++ {
				if text[i:i+len(keyword)] == keyword {
					return true
				}
			}
		}
	}
	return false
}

func main() {
	fmt.Println("🧪 AI运维管理平台 - 简化测试系统")
	fmt.Println("=" * 60)

	// 创建日志器
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 创建测试表
	if err := createTestTables(db); err != nil {
		log.Fatal("Failed to create test tables:", err)
	}

	// 创建模拟DeepSeek服务
	mockDeepSeek := NewMockSmartDeepSeekService(logger)

	// 创建安全验证器
	securityValidator := ai.NewIntelligentSecurityValidator(mockDeepSeek, logger)

	// 创建执行引擎
	executionEngine := ai.NewDynamicExecutionEngine(&ai.ExecutionEngineConfig{
		EnableSandbox:       true,
		DefaultTimeout:      30 * time.Second,
		MaxConcurrentJobs:   5,
		EnableAuditLog:      true,
		SecurityLevel:       "normal",
		AllowedOperations:   []string{"database", "system"},
		ForbiddenOperations: []string{"format", "shutdown"},
	}, logger)

	// 注册SQL执行器
	sqlExecutor := ai.NewSQLCodeExecutor(db, logger)
	if err := executionEngine.RegisterCodeExecutor("sql", sqlExecutor); err != nil {
		log.Fatal("Failed to register SQL executor:", err)
	}

	// 创建统一意图引擎
	intentEngine := ai.NewUnifiedIntelligentIntentEngine(
		mockDeepSeek,
		securityValidator,
		executionEngine,
		logger,
	)

	fmt.Println("✅ 系统组件初始化完成")

	// 测试场景
	testCases := []struct {
		name    string
		message string
	}{
		{"查询测试", "查看所有主机"},
		{"添加测试", "添加新主机"},
		{"删除测试", "删除主机数据"},
		{"监控测试", "检查系统CPU使用情况"},
		{"对话测试", "你好，请介绍功能"},
	}

	fmt.Println("\n🧪 开始执行测试...")

	for i, test := range testCases {
		fmt.Printf("\n--- 测试 %d: %s ---\n", i+1, test.name)
		fmt.Printf("输入: %s\n", test.message)

		// 创建测试请求
		req := &ai.SmartIntentRequest{
			ID:              fmt.Sprintf("test_%d", i),
			UserID:          1,
			SessionID:       "test_session",
			Message:         test.message,
			Context:         make(map[string]interface{}),
			UserPermissions: []string{"basic", "database_access"},
			Timestamp:       time.Now(),
		}

		// 执行意图理解
		start := time.Now()
		response, err := intentEngine.UnderstandIntent(context.Background(), req)
		duration := time.Since(start)

		if err != nil {
			fmt.Printf("❌ 错误: %s\n", err.Error())
			continue
		}

		fmt.Printf("✅ 意图理解: %s\n", response.IntentUnderstanding)
		fmt.Printf("🔧 操作类型: %s\n", response.OperationType)
		fmt.Printf("📊 置信度: %.2f\n", response.Confidence)
		fmt.Printf("🛡️ 风险等级: %s\n", response.RiskAssessment.Level)
		fmt.Printf("⚠️ 需要确认: %t\n", response.RequiresConfirmation)
		fmt.Printf("⏱️ 处理时间: %v\n", duration)

		// 如果有执行计划，显示代码
		if response.ExecutionPlan != nil && response.ExecutionPlan.Code != nil {
			if len(response.ExecutionPlan.Code.SQL) > 0 {
				fmt.Printf("💾 生成SQL: %v\n", response.ExecutionPlan.Code.SQL)
			}
			if len(response.ExecutionPlan.Code.Shell) > 0 {
				fmt.Printf("🖥️ 生成Shell: %v\n", response.ExecutionPlan.Code.Shell)
			}
		}
	}

	// 健康检查测试
	fmt.Println("\n🏥 系统健康检查...")
	status := intentEngine.GetEngineStatus()
	fmt.Printf("系统健康: %t\n", status.IsHealthy)
	fmt.Printf("处理请求数: %d\n", status.ProcessedRequests)
	fmt.Printf("成功率: %.2f%%\n", status.SuccessRate*100)
	fmt.Printf("平均延迟: %v\n", status.AverageLatency)

	fmt.Println("\n🎉 简化测试完成！系统基本功能正常。")
	fmt.Println("\n💡 提示：要使用完整功能，请配置真实的DeepSeek API密钥。")
}

// createTestTables 创建测试表
func createTestTables(db *gorm.DB) error {
	err := db.Exec(`
		CREATE TABLE IF NOT EXISTS hosts (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name VARCHAR(255) NOT NULL,
			ip VARCHAR(45) NOT NULL,
			port INTEGER DEFAULT 22,
			username VARCHAR(255),
			status VARCHAR(50) DEFAULT 'offline',
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			deleted_at DATETIME
		)
	`).Error
	if err != nil {
		return err
	}

	// 插入测试数据
	testData := []string{
		"INSERT INTO hosts (name, ip, username, status) VALUES ('web-01', '************', 'admin', 'online')",
		"INSERT INTO hosts (name, ip, username, status) VALUES ('db-01', '************', 'root', 'online')",
		"INSERT INTO hosts (name, ip, username, status) VALUES ('app-01', '************', 'deploy', 'offline')",
	}

	for _, sql := range testData {
		db.Exec(sql)
	}

	fmt.Println("✅ 测试数据创建完成")
	return nil
}
