package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// AIResultAnalyzer AI结果分析器
type AIResultAnalyzer struct {
	deepseekService *EnhancedDeepSeekService
	logger          *logrus.Logger
}

// NewAIResultAnalyzer 创建AI结果分析器
func NewAIResultAnalyzer(deepseekService *EnhancedDeepSeekService, logger *logrus.Logger) *AIResultAnalyzer {
	return &AIResultAnalyzer{
		deepseekService: deepseekService,
		logger:          logger,
	}
}

// AnalysisRequest 分析请求
type AnalysisRequest struct {
	UserInput       string                 `json:"user_input"`
	Operation       string                 `json:"operation"`
	ExecutionResult *UnifiedExecutionResult `json:"execution_result"`
	Context         map[string]interface{} `json:"context"`
}

// AnalysisResult 分析结果
type AnalysisResult struct {
	Success         bool                   `json:"success"`
	Summary         string                 `json:"summary"`         // 操作摘要
	KeyFindings     []string               `json:"key_findings"`    // 关键发现
	Insights        []string               `json:"insights"`        // 深度洞察
	Recommendations []string               `json:"recommendations"` // 专业建议
	RiskAssessment  string                 `json:"risk_assessment"` // 风险评估
	NextSteps       []string               `json:"next_steps"`      // 后续步骤
	Metadata        map[string]interface{} `json:"metadata"`
}

// AnalyzeExecutionResult 分析执行结果
func (ara *AIResultAnalyzer) AnalyzeExecutionResult(ctx context.Context, req *AnalysisRequest) (*AnalysisResult, error) {
	start := time.Now()
	
	ara.logger.WithFields(logrus.Fields{
		"user_input": req.UserInput,
		"operation":  req.Operation,
		"success":    req.ExecutionResult.Success,
	}).Info("AIResultAnalyzer: 开始分析执行结果")
	
	// 构建分析提示词
	prompt := ara.buildAnalysisPrompt(req)
	
	// 调用DeepSeek进行分析
	// 🔧 优化：使用Medium复杂度减少分析时间
	deepseekReq := &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: ara.buildSystemPrompt(),
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   2000,  // 🔧 减少token数量
		Temperature: 0.2,   // 🔧 降低温度提高响应速度
	}

	response, err := ara.deepseekService.CallWithComplexity(ctx, deepseekReq, ComplexityMedium)
	if err != nil {
		ara.logger.WithError(err).Error("AIResultAnalyzer: DeepSeek分析失败")
		return ara.generateFallbackAnalysis(req), nil
	}
	
	// 解析AI分析结果
	analysisResult, err := ara.parseAnalysisResponse(response.Choices[0].Message.Content)
	if err != nil {
		ara.logger.WithError(err).Warn("AIResultAnalyzer: 解析AI响应失败，使用降级分析")
		return ara.generateFallbackAnalysis(req), nil
	}
	
	// 添加元数据
	analysisResult.Metadata = map[string]interface{}{
		"analysis_time":  time.Since(start),
		"ai_model":       response.Model,
		"total_tokens":   response.Usage.TotalTokens,
		"execution_type": req.ExecutionResult.ExecutionType,
	}
	
	ara.logger.WithFields(logrus.Fields{
		"analysis_time": time.Since(start),
		"key_findings":  len(analysisResult.KeyFindings),
		"insights":      len(analysisResult.Insights),
	}).Info("AIResultAnalyzer: 分析完成")
	
	return analysisResult, nil
}

// buildSystemPrompt 构建系统提示词
func (ara *AIResultAnalyzer) buildSystemPrompt() string {
	return `你是一个世界级的AI运维专家和数据分析师，专门负责深度分析运维操作的执行结果。

你的任务是：
1. 深度分析执行结果，提取关键信息和洞察
2. 识别潜在的问题、风险和优化机会
3. 提供专业的运维建议和后续步骤
4. 评估操作的影响和风险
5. 🔧 特别重要：准确识别操作的真实执行状态（成功/失败）

请返回JSON格式的分析结果：
{
  "success": true/false,
  "summary": "操作摘要，准确描述执行了什么操作以及实际结果",
  "key_findings": ["关键发现1", "关键发现2"],
  "insights": ["深度洞察1", "深度洞察2"],
  "recommendations": ["专业建议1", "专业建议2"],
  "risk_assessment": "风险评估：low/medium/high，并说明原因",
  "next_steps": ["建议的后续步骤1", "建议的后续步骤2"]
}

🔧 关键分析要点：
- 仔细检查执行状态字段，如果为false则操作失败
- 对于SSH命令，检查退出码：0表示成功，非0表示失败
- 对于失败的操作，重点分析失败原因和解决方案
- 对于成功的操作，关注数据的异常模式和趋势
- 识别性能瓶颈和安全风险
- 提供可操作的改进建议
- 考虑业务影响和运维最佳实践

🚨 失败操作处理：
- 如果操作失败，summary应明确说明"操作失败"
- key_findings应包含失败的具体原因
- recommendations应提供故障排除步骤
- risk_assessment应考虑失败对系统的影响`
}

// buildAnalysisPrompt 构建分析提示词
func (ara *AIResultAnalyzer) buildAnalysisPrompt(req *AnalysisRequest) string {
	var prompt strings.Builder

	prompt.WriteString(fmt.Sprintf("用户请求: %s\n", req.UserInput))
	prompt.WriteString(fmt.Sprintf("执行操作: %s\n", req.Operation))
	prompt.WriteString(fmt.Sprintf("操作类型: %s\n", req.ExecutionResult.ExecutionType))
	prompt.WriteString(fmt.Sprintf("🔧 执行状态: %v\n", req.ExecutionResult.Success))
	prompt.WriteString(fmt.Sprintf("执行时间: %v\n", req.ExecutionResult.ExecutionTime))

	// 🔧 添加详细的执行结果数据
	if req.ExecutionResult.Data != nil {
		// 特别处理SSH命令的退出码
		if exitCode, exists := req.ExecutionResult.Data["exit_code"]; exists {
			prompt.WriteString(fmt.Sprintf("🔧 命令退出码: %v (0=成功, 非0=失败)\n", exitCode))
		}
		if stderr, exists := req.ExecutionResult.Data["stderr"]; exists && stderr != "" {
			prompt.WriteString(fmt.Sprintf("🔧 错误输出: %v\n", stderr))
		}
		if stdout, exists := req.ExecutionResult.Data["stdout"]; exists && stdout != "" {
			prompt.WriteString(fmt.Sprintf("🔧 标准输出: %v\n", stdout))
		}

		dataJSON, _ := json.MarshalIndent(req.ExecutionResult.Data, "", "  ")
		prompt.WriteString(fmt.Sprintf("执行结果数据:\n%s\n", string(dataJSON)))
	}

	// 添加执行结果数据
	if req.ExecutionResult.RawData != nil {
		rawDataJSON, _ := json.MarshalIndent(req.ExecutionResult.RawData, "", "  ")
		prompt.WriteString(fmt.Sprintf("原始执行数据:\n%s\n", string(rawDataJSON)))
	}

	// 添加生成的代码
	if req.ExecutionResult.GeneratedCode != "" {
		prompt.WriteString(fmt.Sprintf("生成的代码:\n%s\n", req.ExecutionResult.GeneratedCode))
	}

	// 添加错误信息
	if req.ExecutionResult.Error != "" {
		prompt.WriteString(fmt.Sprintf("🚨 错误信息: %s\n", req.ExecutionResult.Error))
	}

	// 添加上下文信息
	if req.Context != nil && len(req.Context) > 0 {
		contextJSON, _ := json.MarshalIndent(req.Context, "", "  ")
		prompt.WriteString(fmt.Sprintf("上下文信息:\n%s\n", string(contextJSON)))
	}

	prompt.WriteString("\n🔧 请深度分析以上执行结果，特别注意执行状态和退出码，提供准确的运维洞察和建议。")

	return prompt.String()
}

// parseAnalysisResponse 解析分析响应
func (ara *AIResultAnalyzer) parseAnalysisResponse(content string) (*AnalysisResult, error) {
	// 提取JSON内容
	jsonContent := ara.extractJSONFromResponse(content)
	if jsonContent == "" {
		return nil, fmt.Errorf("未找到有效的JSON内容")
	}
	
	var result AnalysisResult
	if err := json.Unmarshal([]byte(jsonContent), &result); err != nil {
		return nil, fmt.Errorf("解析JSON失败: %w", err)
	}
	
	return &result, nil
}

// extractJSONFromResponse 从响应中提取JSON
func (ara *AIResultAnalyzer) extractJSONFromResponse(content string) string {
	// 查找JSON代码块
	if start := strings.Index(content, "```json"); start != -1 {
		start += 7
		if end := strings.Index(content[start:], "```"); end != -1 {
			return strings.TrimSpace(content[start : start+end])
		}
	}
	
	// 查找普通代码块
	if start := strings.Index(content, "```"); start != -1 {
		start += 3
		if end := strings.Index(content[start:], "```"); end != -1 {
			return strings.TrimSpace(content[start : start+end])
		}
	}
	
	// 查找JSON对象
	if start := strings.Index(content, "{"); start != -1 {
		braceCount := 0
		for i := start; i < len(content); i++ {
			if content[i] == '{' {
				braceCount++
			} else if content[i] == '}' {
				braceCount--
				if braceCount == 0 {
					return strings.TrimSpace(content[start : i+1])
				}
			}
		}
	}
	
	return ""
}

// generateFallbackAnalysis 生成降级分析
func (ara *AIResultAnalyzer) generateFallbackAnalysis(req *AnalysisRequest) *AnalysisResult {
	result := &AnalysisResult{
		Success: req.ExecutionResult.Success,
		Summary: fmt.Sprintf("执行了%s操作", req.Operation),
		Metadata: map[string]interface{}{
			"fallback": true,
			"reason":   "AI分析失败，使用降级分析",
		},
	}
	
	// 基于执行结果生成基础分析
	if req.ExecutionResult.Success {
		result.KeyFindings = []string{
			"操作执行成功",
			fmt.Sprintf("执行时间: %v", req.ExecutionResult.ExecutionTime),
		}
		result.Insights = []string{
			"系统响应正常",
		}
		result.Recommendations = []string{
			"继续监控系统状态",
		}
		result.RiskAssessment = "low - 操作成功完成"
		result.NextSteps = []string{
			"查看详细结果",
			"根据需要执行后续操作",
		}
	} else {
		result.KeyFindings = []string{
			"操作执行失败",
			fmt.Sprintf("错误: %s", req.ExecutionResult.Error),
		}
		result.Insights = []string{
			"需要检查错误原因",
		}
		result.Recommendations = []string{
			"检查系统日志",
			"验证操作参数",
		}
		result.RiskAssessment = "medium - 操作失败需要关注"
		result.NextSteps = []string{
			"分析错误原因",
			"修复问题后重试",
		}
	}
	
	// 根据执行类型添加特定分析
	switch req.ExecutionResult.ExecutionType {
	case "sql":
		result.Insights = append(result.Insights, "数据库操作完成")
		if req.ExecutionResult.Success {
			result.Recommendations = append(result.Recommendations, "检查数据一致性")
		}
	case "shell":
		result.Insights = append(result.Insights, "系统命令执行完成")
		if req.ExecutionResult.Success {
			result.Recommendations = append(result.Recommendations, "验证系统状态")
		}
	case "monitoring":
		result.Insights = append(result.Insights, "监控操作完成")
		if req.ExecutionResult.Success {
			result.Recommendations = append(result.Recommendations, "关注监控指标变化")
		}
	}
	
	return result
}
