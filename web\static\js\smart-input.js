/* ========================================
   智能输入组件JavaScript
   提供类似ChatGPT的高级输入体验
   ======================================== */

class SmartInput {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            maxLength: 4000,
            placeholder: '输入您的消息...',
            autoResize: true,
            showCharCounter: true,
            showShortcutHint: true,
            enableEmoji: true,
            enableAutocomplete: true,
            enableFileUpload: false,
            sendShortcut: 'Enter',
            ...options
        };
        
        this.textarea = null;
        this.sendButton = null;
        this.charCounter = null;
        this.autocompleteList = null;
        this.emojiPicker = null;
        
        this.isComposing = false;
        this.selectedSuggestionIndex = -1;
        
        this.init();
    }
    
    init() {
        this.createHTML();
        this.bindEvents();
        this.loadAutocompleteData();
    }
    
    createHTML() {
        this.container.innerHTML = `
            <div class="smart-input-container">
                <div class="smart-input-wrapper" id="smart-input-wrapper">
                    <div class="smart-input-area">
                        <textarea 
                            class="smart-textarea" 
                            id="smart-textarea"
                            placeholder="${this.options.placeholder}"
                            maxlength="${this.options.maxLength}"
                            rows="1"
                        ></textarea>
                        ${this.options.showCharCounter || this.options.enableEmoji ? `
                        <div class="input-toolbar">
                            ${this.options.enableEmoji ? `
                            <button class="toolbar-btn" id="emoji-btn" title="表情符号">
                                <i class="bi bi-emoji-smile"></i>
                            </button>
                            ` : ''}
                            ${this.options.enableFileUpload ? `
                            <button class="toolbar-btn" id="file-btn" title="上传文件">
                                <i class="bi bi-paperclip"></i>
                            </button>
                            ` : ''}
                            ${this.options.showCharCounter ? `
                            <span class="char-counter" id="char-counter">0/${this.options.maxLength}</span>
                            ` : ''}
                        </div>
                        ` : ''}
                    </div>
                    <div class="send-button-area">
                        <button class="smart-send-button" id="smart-send-button" disabled title="发送消息">
                            <i class="bi bi-send"></i>
                        </button>
                    </div>
                    ${this.options.showShortcutHint ? `
                    <div class="shortcut-hint">
                        ${this.options.sendShortcut === 'Enter' ? 'Enter 发送' : 'Ctrl+Enter 发送'}
                    </div>
                    ` : ''}
                </div>
                
                ${this.options.enableAutocomplete ? `
                <div class="autocomplete-suggestions" id="autocomplete-suggestions"></div>
                ` : ''}
                
                ${this.options.enableEmoji ? `
                <div class="emoji-picker" id="emoji-picker">
                    <div class="emoji-categories">
                        <button class="emoji-category active" data-category="recent">最近</button>
                        <button class="emoji-category" data-category="smileys">😀</button>
                        <button class="emoji-category" data-category="people">👥</button>
                        <button class="emoji-category" data-category="nature">🌿</button>
                        <button class="emoji-category" data-category="objects">📱</button>
                    </div>
                    <div class="emoji-grid" id="emoji-grid"></div>
                </div>
                ` : ''}
                
                ${this.options.enableFileUpload ? `
                <div class="file-upload-area" id="file-upload-area">
                    <div class="file-upload-content">
                        <div class="file-upload-icon">📁</div>
                        <div class="file-upload-text">拖拽文件到这里上传</div>
                    </div>
                </div>
                ` : ''}
            </div>
        `;
        
        this.textarea = document.getElementById('smart-textarea');
        this.sendButton = document.getElementById('smart-send-button');
        this.charCounter = document.getElementById('char-counter');
        this.autocompleteList = document.getElementById('autocomplete-suggestions');
        this.emojiPicker = document.getElementById('emoji-picker');
        this.wrapper = document.getElementById('smart-input-wrapper');
    }
    
    bindEvents() {
        // 文本域事件
        this.textarea.addEventListener('input', (e) => this.handleInput(e));
        this.textarea.addEventListener('keydown', (e) => this.handleKeyDown(e));
        this.textarea.addEventListener('compositionstart', () => this.isComposing = true);
        this.textarea.addEventListener('compositionend', () => this.isComposing = false);
        this.textarea.addEventListener('focus', () => this.handleFocus());
        this.textarea.addEventListener('blur', () => this.handleBlur());
        
        // 发送按钮事件
        this.sendButton.addEventListener('click', () => this.handleSend());
        
        // 表情符号按钮
        const emojiBtn = document.getElementById('emoji-btn');
        if (emojiBtn) {
            emojiBtn.addEventListener('click', () => this.toggleEmojiPicker());
        }
        
        // 文件上传按钮
        const fileBtn = document.getElementById('file-btn');
        if (fileBtn) {
            fileBtn.addEventListener('click', () => this.handleFileUpload());
        }
        
        // 点击外部关闭弹窗
        document.addEventListener('click', (e) => this.handleOutsideClick(e));
        
        // 拖拽文件上传
        if (this.options.enableFileUpload) {
            this.bindFileUploadEvents();
        }
    }
    
    handleInput(e) {
        const value = e.target.value;
        
        // 自动调整高度
        if (this.options.autoResize) {
            this.autoResize();
        }
        
        // 更新字符计数
        if (this.options.showCharCounter) {
            this.updateCharCounter(value.length);
        }
        
        // 更新发送按钮状态
        this.updateSendButton(value.trim().length > 0);
        
        // 更新包装器状态
        this.wrapper.classList.toggle('has-content', value.trim().length > 0);
        
        // 自动完成
        if (this.options.enableAutocomplete && !this.isComposing) {
            this.handleAutocomplete(value);
        }
        
        // 触发输入事件
        this.triggerEvent('input', { value });
    }
    
    handleKeyDown(e) {
        // 处理发送快捷键
        if (this.shouldSend(e)) {
            e.preventDefault();
            this.handleSend();
            return;
        }
        
        // 处理自动完成导航
        if (this.autocompleteList && this.autocompleteList.classList.contains('show')) {
            if (e.key === 'ArrowDown') {
                e.preventDefault();
                this.navigateAutocomplete(1);
            } else if (e.key === 'ArrowUp') {
                e.preventDefault();
                this.navigateAutocomplete(-1);
            } else if (e.key === 'Tab' || e.key === 'Enter') {
                e.preventDefault();
                this.selectAutocomplete();
            } else if (e.key === 'Escape') {
                this.hideAutocomplete();
            }
        }
        
        // 处理Tab键插入空格
        if (e.key === 'Tab' && !e.shiftKey) {
            e.preventDefault();
            this.insertText('    '); // 插入4个空格
        }
    }
    
    shouldSend(e) {
        if (this.options.sendShortcut === 'Enter') {
            return e.key === 'Enter' && !e.shiftKey && !e.ctrlKey && !e.altKey;
        } else if (this.options.sendShortcut === 'Ctrl+Enter') {
            return e.key === 'Enter' && e.ctrlKey;
        } else if (this.options.sendShortcut === 'Shift+Enter') {
            return e.key === 'Enter' && e.shiftKey;
        }
        return false;
    }
    
    handleSend() {
        const value = this.textarea.value.trim();
        if (value && !this.sendButton.disabled) {
            this.triggerEvent('send', { message: value });
            this.clear();
        }
    }
    
    autoResize() {
        this.textarea.style.height = 'auto';
        const scrollHeight = this.textarea.scrollHeight;
        const maxHeight = 200; // 最大高度
        this.textarea.style.height = Math.min(scrollHeight, maxHeight) + 'px';
    }
    
    updateCharCounter(length) {
        if (!this.charCounter) return;
        
        this.charCounter.textContent = `${length}/${this.options.maxLength}`;
        
        this.charCounter.classList.remove('warning', 'error');
        if (length > this.options.maxLength * 0.9) {
            this.charCounter.classList.add('warning');
        }
        if (length >= this.options.maxLength) {
            this.charCounter.classList.add('error');
        }
    }
    
    updateSendButton(enabled) {
        this.sendButton.disabled = !enabled;
    }
    
    clear() {
        this.textarea.value = '';
        this.textarea.style.height = 'auto';
        this.updateCharCounter(0);
        this.updateSendButton(false);
        this.wrapper.classList.remove('has-content');
        this.hideAutocomplete();
        this.hideEmojiPicker();
    }
    
    insertText(text) {
        const start = this.textarea.selectionStart;
        const end = this.textarea.selectionEnd;
        const value = this.textarea.value;
        
        this.textarea.value = value.substring(0, start) + text + value.substring(end);
        this.textarea.selectionStart = this.textarea.selectionEnd = start + text.length;
        
        this.handleInput({ target: this.textarea });
        this.textarea.focus();
    }
    
    focus() {
        this.textarea.focus();
    }
    
    getValue() {
        return this.textarea.value;
    }
    
    setValue(value) {
        this.textarea.value = value;
        this.handleInput({ target: this.textarea });
    }
    
    triggerEvent(eventName, data) {
        const event = new CustomEvent(`smartInput:${eventName}`, {
            detail: data
        });
        this.container.dispatchEvent(event);
    }
    
    // 自动完成功能
    loadAutocompleteData() {
        this.autocompleteData = [
            { text: '查看所有主机状态', description: '显示所有服务器的运行状态' },
            { text: '显示最近的告警信息', description: '查看最新的系统告警' },
            { text: '生成今天的运维统计报表', description: '生成当日运维数据报告' },
            { text: '重启nginx服务', description: '重新启动nginx web服务器' },
            { text: '检查磁盘使用情况', description: '查看各分区磁盘占用率' },
            { text: '查看系统负载', description: '显示CPU、内存、网络负载' },
            { text: '备份数据库', description: '执行数据库备份操作' },
            { text: '清理日志文件', description: '清理过期的系统日志' }
        ];
    }

    handleAutocomplete(value) {
        if (value.length < 2) {
            this.hideAutocomplete();
            return;
        }

        const matches = this.autocompleteData.filter(item =>
            item.text.toLowerCase().includes(value.toLowerCase()) ||
            item.description.toLowerCase().includes(value.toLowerCase())
        );

        if (matches.length > 0) {
            this.showAutocomplete(matches);
        } else {
            this.hideAutocomplete();
        }
    }

    showAutocomplete(matches) {
        if (!this.autocompleteList) return;

        const html = matches.map((item, index) => `
            <div class="suggestion-item" data-index="${index}" data-text="${item.text}">
                <div class="suggestion-text">${item.text}</div>
                <div class="suggestion-description">${item.description}</div>
            </div>
        `).join('');

        this.autocompleteList.innerHTML = html;
        this.autocompleteList.classList.add('show');
        this.selectedSuggestionIndex = -1;

        // 绑定点击事件
        this.autocompleteList.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', () => {
                this.textarea.value = item.dataset.text;
                this.hideAutocomplete();
                this.handleInput({ target: this.textarea });
                this.textarea.focus();
            });
        });
    }

    hideAutocomplete() {
        if (this.autocompleteList) {
            this.autocompleteList.classList.remove('show');
            this.selectedSuggestionIndex = -1;
        }
    }

    navigateAutocomplete(direction) {
        const items = this.autocompleteList.querySelectorAll('.suggestion-item');
        if (items.length === 0) return;

        // 移除当前选中状态
        if (this.selectedSuggestionIndex >= 0) {
            items[this.selectedSuggestionIndex].classList.remove('selected');
        }

        // 计算新的索引
        this.selectedSuggestionIndex += direction;
        if (this.selectedSuggestionIndex < 0) {
            this.selectedSuggestionIndex = items.length - 1;
        } else if (this.selectedSuggestionIndex >= items.length) {
            this.selectedSuggestionIndex = 0;
        }

        // 添加新的选中状态
        items[this.selectedSuggestionIndex].classList.add('selected');
        items[this.selectedSuggestionIndex].scrollIntoView({ block: 'nearest' });
    }

    selectAutocomplete() {
        const items = this.autocompleteList.querySelectorAll('.suggestion-item');
        if (this.selectedSuggestionIndex >= 0 && this.selectedSuggestionIndex < items.length) {
            const selectedItem = items[this.selectedSuggestionIndex];
            this.textarea.value = selectedItem.dataset.text;
            this.hideAutocomplete();
            this.handleInput({ target: this.textarea });
            this.textarea.focus();
        }
    }

    // 表情符号功能
    toggleEmojiPicker() {
        if (!this.emojiPicker) return;

        if (this.emojiPicker.classList.contains('show')) {
            this.hideEmojiPicker();
        } else {
            this.showEmojiPicker();
        }
    }

    showEmojiPicker() {
        if (!this.emojiPicker) return;

        this.loadEmojis('smileys');
        this.emojiPicker.classList.add('show');

        // 绑定分类切换事件
        this.emojiPicker.querySelectorAll('.emoji-category').forEach(btn => {
            btn.addEventListener('click', () => {
                this.emojiPicker.querySelectorAll('.emoji-category').forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                this.loadEmojis(btn.dataset.category);
            });
        });
    }

    hideEmojiPicker() {
        if (this.emojiPicker) {
            this.emojiPicker.classList.remove('show');
        }
    }

    loadEmojis(category) {
        const emojiData = {
            recent: ['😀', '😊', '👍', '❤️', '😂', '🎉', '👏', '🔥'],
            smileys: ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳'],
            people: ['👋', '🤚', '🖐️', '✋', '🖖', '👌', '🤏', '✌️', '🤞', '🤟', '🤘', '🤙', '👈', '👉', '👆', '🖕', '👇', '☝️', '👍', '👎', '👊', '✊', '🤛', '🤜', '👏', '🙌', '👐', '🤲', '🤝', '🙏'],
            nature: ['🌱', '🌿', '🍀', '🌾', '🌵', '🌲', '🌳', '🌴', '🌸', '🌺', '🌻', '🌹', '🥀', '🌷', '💐', '🌼', '🌙', '⭐', '🌟', '✨', '⚡', '🔥', '💧', '🌈', '☀️', '⛅', '☁️', '🌤️', '⛈️', '🌦️'],
            objects: ['📱', '💻', '⌨️', '🖥️', '🖨️', '🖱️', '🖲️', '💽', '💾', '💿', '📀', '📼', '📷', '📸', '📹', '🎥', '📞', '☎️', '📟', '📠', '📺', '📻', '🎙️', '🎚️', '🎛️', '⏰', '⏲️', '⏱️', '🕰️', '📡']
        };

        const grid = this.emojiPicker.querySelector('#emoji-grid');
        const emojis = emojiData[category] || emojiData.smileys;

        grid.innerHTML = emojis.map(emoji => `
            <button class="emoji-item" data-emoji="${emoji}">${emoji}</button>
        `).join('');

        // 绑定点击事件
        grid.querySelectorAll('.emoji-item').forEach(btn => {
            btn.addEventListener('click', () => {
                this.insertText(btn.dataset.emoji);
                this.hideEmojiPicker();
            });
        });
    }

    // 文件上传功能
    bindFileUploadEvents() {
        const uploadArea = document.getElementById('file-upload-area');

        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            this.container.addEventListener(eventName, (e) => {
                e.preventDefault();
                e.stopPropagation();
            });
        });

        ['dragenter', 'dragover'].forEach(eventName => {
            this.container.addEventListener(eventName, () => {
                uploadArea.classList.add('show');
            });
        });

        ['dragleave', 'drop'].forEach(eventName => {
            this.container.addEventListener(eventName, () => {
                uploadArea.classList.remove('show');
            });
        });

        this.container.addEventListener('drop', (e) => {
            const files = Array.from(e.dataTransfer.files);
            this.handleFiles(files);
        });
    }

    handleFileUpload() {
        const input = document.createElement('input');
        input.type = 'file';
        input.multiple = true;
        input.accept = '.txt,.md,.json,.csv,.log';

        input.addEventListener('change', (e) => {
            const files = Array.from(e.target.files);
            this.handleFiles(files);
        });

        input.click();
    }

    handleFiles(files) {
        this.triggerEvent('filesSelected', { files });
    }

    // 处理外部点击
    handleOutsideClick(e) {
        if (!this.container.contains(e.target)) {
            this.hideAutocomplete();
            this.hideEmojiPicker();
        }
    }

    handleFocus() {
        this.triggerEvent('focus');
    }

    handleBlur() {
        // 延迟隐藏，避免点击建议时立即隐藏
        setTimeout(() => {
            if (document.activeElement !== this.textarea) {
                this.triggerEvent('blur');
            }
        }, 100);
    }
}

// 导出类
window.SmartInput = SmartInput;
