package workflow

import (
	"sync"
	"time"
)

// TriggerWorkflowRequest 触发工作流请求
type TriggerWorkflowRequest struct {
	TriggerType string                 `json:"trigger_type"` // intent, event, manual
	Intent      string                 `json:"intent"`
	Category    string                 `json:"category"`
	SessionID   string                 `json:"session_id"`
	UserID      int64                  `json:"user_id"`
	Variables   map[string]interface{} `json:"variables"`
	Priority    int                    `json:"priority"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// WorkflowResponse 工作流响应
type WorkflowResponse struct {
	InstanceID     string                 `json:"instance_id"`
	Status         WorkflowStatus         `json:"status"`
	Message        string                 `json:"message"`
	CurrentStep    string                 `json:"current_step,omitempty"`
	NextSteps      []string               `json:"next_steps,omitempty"`
	RequiredInputs []InputRequirement     `json:"required_inputs,omitempty"`
	Suggestions    []string               `json:"suggestions,omitempty"`
	Data           map[string]interface{} `json:"data,omitempty"`
	AIGuidance     *AIGuidanceResponse    `json:"ai_guidance,omitempty"`
	Progress       *WorkflowProgress      `json:"progress,omitempty"`
}

// InputRequirement 输入要求
type InputRequirement struct {
	Name        string      `json:"name"`
	Type        string      `json:"type"` // string, number, boolean, array, object
	Description string      `json:"description"`
	Required    bool        `json:"required"`
	Default     interface{} `json:"default,omitempty"`
	Validation  *Validation `json:"validation,omitempty"`
	Options     []Option    `json:"options,omitempty"`
}

// Validation 验证规则
type Validation struct {
	Pattern string      `json:"pattern,omitempty"` // 正则表达式
	Min     interface{} `json:"min,omitempty"`     // 最小值
	Max     interface{} `json:"max,omitempty"`     // 最大值
	MinLen  int         `json:"min_len,omitempty"` // 最小长度
	MaxLen  int         `json:"max_len,omitempty"` // 最大长度
	Custom  string      `json:"custom,omitempty"`  // 自定义验证函数
}

// Option 选项
type Option struct {
	Value       interface{} `json:"value"`
	Label       string      `json:"label"`
	Description string      `json:"description,omitempty"`
}

// AIGuidanceResponse AI引导响应
type AIGuidanceResponse struct {
	Message     string   `json:"message"`
	Suggestions []string `json:"suggestions"`
	Context     string   `json:"context"`
	NextAction  string   `json:"next_action"`
}

// WorkflowProgress 工作流进度
type WorkflowProgress struct {
	CurrentStep   int     `json:"current_step"`
	TotalSteps    int     `json:"total_steps"`
	Percentage    float64 `json:"percentage"`
	EstimatedTime string  `json:"estimated_time,omitempty"`
}

// StepResult 步骤执行结果
type StepResult struct {
	Success     bool                   `json:"success"`
	Data        map[string]interface{} `json:"data"`
	NextStep    string                 `json:"next_step"`
	Message     string                 `json:"message"`
	WaitForUser bool                   `json:"wait_for_user"`
	Error       string                 `json:"error,omitempty"`
	Retry       bool                   `json:"retry"`
}

// WorkflowEvent 工作流事件
type WorkflowEvent struct {
	Type       string                 `json:"type"`
	InstanceID string                 `json:"instance_id"`
	StepID     string                 `json:"step_id,omitempty"`
	Status     string                 `json:"status"`
	Data       map[string]interface{} `json:"data,omitempty"`
	Timestamp  time.Time              `json:"timestamp"`
	UserID     int64                  `json:"user_id,omitempty"`
	SessionID  string                 `json:"session_id,omitempty"`
}

// WorkflowMetrics 工作流指标
type WorkflowMetrics struct {
	TotalInstances   int64         `json:"total_instances"`
	RunningInstances int64         `json:"running_instances"`
	CompletedToday   int64         `json:"completed_today"`
	FailedToday      int64         `json:"failed_today"`
	AverageExecution time.Duration `json:"average_execution"`
	SuccessRate      float64       `json:"success_rate"`
	PopularWorkflows []string      `json:"popular_workflows"`
}

// HostManagementData 主机管理数据
type HostManagementData struct {
	HostID      int64  `json:"host_id,omitempty"`
	Name        string `json:"name"`
	IPAddress   string `json:"ip_address"`
	Port        int    `json:"port"`
	Username    string `json:"username"`
	Password    string `json:"password,omitempty"`
	SSHKeyPath  string `json:"ssh_key_path,omitempty"`
	Description string `json:"description,omitempty"`
	Environment string `json:"environment"`
	GroupName   string `json:"group_name,omitempty"`
}

// CommandExecutionData 命令执行数据
type CommandExecutionData struct {
	HostID  int64             `json:"host_id"`
	Command string            `json:"command"`
	Timeout int               `json:"timeout"`
	WorkDir string            `json:"work_dir,omitempty"`
	Env     map[string]string `json:"env,omitempty"`
}

// MonitoringData 监控数据
type MonitoringData struct {
	HostID  int64    `json:"host_id"`
	Metrics []string `json:"metrics"` // cpu, memory, disk, network
	Period  string   `json:"period"`  // 1m, 5m, 1h, 1d
}

// AlertData 告警数据
type AlertData struct {
	HostID     int64                  `json:"host_id,omitempty"`
	Type       string                 `json:"type"`
	Severity   string                 `json:"severity"`
	Message    string                 `json:"message"`
	Conditions map[string]interface{} `json:"conditions"`
	Actions    []string               `json:"actions"`
}

// ReportData 报表数据
type ReportData struct {
	Type      string    `json:"type"` // daily, weekly, monthly
	StartDate time.Time `json:"start_date"`
	EndDate   time.Time `json:"end_date"`
	HostIDs   []int64   `json:"host_ids,omitempty"`
	Metrics   []string  `json:"metrics"`
	Format    string    `json:"format"` // json, csv, pdf
}

// WorkflowTemplate 工作流模板
type WorkflowTemplate struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	Icon        string                 `json:"icon"`
	Tags        []string               `json:"tags"`
	Variables   map[string]interface{} `json:"variables"`
	Definition  *WorkflowDefinition    `json:"definition"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	UsageCount  int64                  `json:"usage_count"`
	Rating      float64                `json:"rating"`
}

// WorkflowLibrary 工作流库
type WorkflowLibrary struct {
	Templates map[string]*WorkflowTemplate `json:"templates"`
	mutex     sync.RWMutex
}

// ExecutionContext 执行上下文
type ExecutionContext struct {
	InstanceID  string                 `json:"instance_id"`
	SessionID   string                 `json:"session_id"`
	UserID      int64                  `json:"user_id"`
	Variables   map[string]interface{} `json:"variables"`
	StepData    map[string]interface{} `json:"step_data"`
	StartTime   time.Time              `json:"start_time"`
	CurrentStep string                 `json:"current_step"`
	Permissions []string               `json:"permissions"`
	Environment string                 `json:"environment"`
}

// StepExecutionResult 步骤执行结果详情
type StepExecutionResult struct {
	StepID      string                 `json:"step_id"`
	Success     bool                   `json:"success"`
	StartTime   time.Time              `json:"start_time"`
	EndTime     time.Time              `json:"end_time"`
	Duration    time.Duration          `json:"duration"`
	Data        map[string]interface{} `json:"data"`
	Error       string                 `json:"error,omitempty"`
	RetryCount  int                    `json:"retry_count"`
	NextStep    string                 `json:"next_step"`
	WaitForUser bool                   `json:"wait_for_user"`
	UserMessage string                 `json:"user_message,omitempty"`
}

// WorkflowState 工作流状态快照
type WorkflowState struct {
	InstanceID    string                 `json:"instance_id"`
	Status        WorkflowStatus         `json:"status"`
	CurrentStep   string                 `json:"current_step"`
	Variables     map[string]interface{} `json:"variables"`
	CollectedData map[string]interface{} `json:"collected_data"`
	ExecutionPath []string               `json:"execution_path"`
	Timestamp     time.Time              `json:"timestamp"`
	Checksum      string                 `json:"checksum"`
}

// WorkflowHistory 工作流历史记录
type WorkflowHistory struct {
	InstanceID string                `json:"instance_id"`
	Events     []WorkflowEvent       `json:"events"`
	States     []WorkflowState       `json:"states"`
	Results    []StepExecutionResult `json:"results"`
	CreatedAt  time.Time             `json:"created_at"`
	UpdatedAt  time.Time             `json:"updated_at"`
}

// WorkflowStatistics 工作流统计
type WorkflowStatistics struct {
	DefinitionID    string        `json:"definition_id"`
	TotalExecutions int64         `json:"total_executions"`
	SuccessfulRuns  int64         `json:"successful_runs"`
	FailedRuns      int64         `json:"failed_runs"`
	AverageRuntime  time.Duration `json:"average_runtime"`
	LastExecution   time.Time     `json:"last_execution"`
	PopularSteps    []string      `json:"popular_steps"`
	CommonFailures  []string      `json:"common_failures"`
}
