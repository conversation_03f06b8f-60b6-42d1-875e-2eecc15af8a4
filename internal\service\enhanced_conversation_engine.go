package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// 🚀 增强对话引擎 - AI智能对话运维助手核心优化

// EnhancedConversationEngine 增强对话引擎
type EnhancedConversationEngine struct {
	db                    *gorm.DB
	logger                *logrus.Logger
	emotionalEngine       *EmotionalIntelligenceEngine
	contextEnhancer       *ConversationContextEnhancer
	suggestionEngine      *IntelligentSuggestionEngine
	personalizationEngine *PersonalizationEngine
	feedbackOptimizer     *RealTimeFeedbackOptimizer
	deepseekService       *DeepSeekService
}

// ConversationRequest 增强对话请求
type ConversationRequest struct {
	SessionID       string                 `json:"session_id"`
	UserID          int64                  `json:"user_id"`
	Message         string                 `json:"message"`
	MessageType     string                 `json:"message_type"`     // text, voice, image
	Context         map[string]interface{} `json:"context"`
	UserProfile     *UserProfile           `json:"user_profile"`
	EmotionalState  *EmotionalState        `json:"emotional_state"`
	ConversationHistory []*ConversationTurn `json:"conversation_history"`
}

// ConversationResponse 增强对话响应
type ConversationResponse struct {
	Content            string                 `json:"content"`
	EmotionalTone      string                 `json:"emotional_tone"`
	Suggestions        []SuggestionItem        `json:"suggestions"`
	PersonalizedTips   []string               `json:"personalized_tips"`
	ContextualHelp     *ContextualHelp        `json:"contextual_help"`
	NextStepGuidance   *NextStepGuidance      `json:"next_step_guidance"`
	ConfidenceScore    float64                `json:"confidence_score"`
	ProcessingTime     time.Duration          `json:"processing_time"`
	Metadata           map[string]interface{} `json:"metadata"`
}

// UserProfile 用户画像
type UserProfile struct {
	UserID           int64                  `json:"user_id"`
	ExpertiseLevel   string                 `json:"expertise_level"`   // beginner, intermediate, expert
	PreferredStyle   string                 `json:"preferred_style"`   // detailed, concise, technical
	CommonTasks      []string               `json:"common_tasks"`
	LearningGoals    []string               `json:"learning_goals"`
	InteractionStats map[string]interface{} `json:"interaction_stats"`
	LastUpdated      time.Time              `json:"last_updated"`
}

// EmotionalState 情感状态
type EmotionalState struct {
	PrimaryEmotion   string  `json:"primary_emotion"`   // frustrated, curious, confident, confused
	IntensityLevel   float64 `json:"intensity_level"`   // 0.0 - 1.0
	StressLevel      float64 `json:"stress_level"`      // 0.0 - 1.0
	EngagementLevel  float64 `json:"engagement_level"`  // 0.0 - 1.0
	SatisfactionScore float64 `json:"satisfaction_score"` // 0.0 - 1.0
}

// ConversationTurn 对话轮次（本地定义避免循环导入）
type ConversationTurn struct {
	TurnID      string                 `json:"turn_id"`
	UserMessage string                 `json:"user_message"`
	AIResponse  string                 `json:"ai_response"`
	Intent      string                 `json:"intent"`
	Success     bool                   `json:"success"`
	Timestamp   time.Time              `json:"timestamp"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// SuggestionItem 建议项（本地定义避免循环导入）
type SuggestionItem struct {
	Type        string                 `json:"type"`        // command, tip, workflow, learning
	Content     string                 `json:"content"`
	Confidence  float64                `json:"confidence"`
	Priority    int                    `json:"priority"`
	Category    string                 `json:"category"`
	ActionType  string                 `json:"action_type"` // execute, learn, explore
	Metadata    map[string]interface{} `json:"metadata"`
}

// ContextualHelp 上下文帮助
type ContextualHelp struct {
	HelpType     string   `json:"help_type"`     // explanation, tutorial, reference
	Title        string   `json:"title"`
	Description  string   `json:"description"`
	Steps        []string `json:"steps"`
	Examples     []string `json:"examples"`
	RelatedTopics []string `json:"related_topics"`
	Difficulty   string   `json:"difficulty"`
}

// NextStepGuidance 下一步指导
type NextStepGuidance struct {
	RecommendedActions []RecommendedAction `json:"recommended_actions"`
	LearningPath       *LearningPath       `json:"learning_path"`
	WorkflowSuggestion *WorkflowSuggestion `json:"workflow_suggestion"`
	ProgressIndicator  *ProgressIndicator  `json:"progress_indicator"`
}

// RecommendedAction 推荐操作
type RecommendedAction struct {
	ActionID    string                 `json:"action_id"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Command     string                 `json:"command"`
	Risk        string                 `json:"risk"`        // low, medium, high
	Benefit     string                 `json:"benefit"`
	EstimatedTime string               `json:"estimated_time"`
	Prerequisites []string             `json:"prerequisites"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// LearningPath 学习路径
type LearningPath struct {
	PathID      string         `json:"path_id"`
	Title       string         `json:"title"`
	Description string         `json:"description"`
	Steps       []LearningStep `json:"steps"`
	Progress    float64        `json:"progress"`
	EstimatedTime string       `json:"estimated_time"`
}

// LearningStep 学习步骤
type LearningStep struct {
	StepID      string `json:"step_id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Type        string `json:"type"`        // theory, practice, assessment
	Completed   bool   `json:"completed"`
	Resources   []string `json:"resources"`
}

// 使用已存在的WorkflowSuggestion类型，避免重复定义

// WorkflowStep 工作流步骤
type WorkflowStep struct {
	StepID      string `json:"step_id"`
	Title       string `json:"title"`
	Description string `json:"description"`
	Command     string `json:"command"`
	Expected    string `json:"expected"`
	Optional    bool   `json:"optional"`
}

// ProgressIndicator 进度指示器
type ProgressIndicator struct {
	CurrentTask    string  `json:"current_task"`
	Progress       float64 `json:"progress"`       // 0.0 - 1.0
	CompletedSteps int     `json:"completed_steps"`
	TotalSteps     int     `json:"total_steps"`
	EstimatedTime  string  `json:"estimated_time"`
	NextMilestone  string  `json:"next_milestone"`
}

// NewEnhancedConversationEngine 创建增强对话引擎
func NewEnhancedConversationEngine(
	db *gorm.DB,
	logger *logrus.Logger,
	deepseekService *DeepSeekService,
) *EnhancedConversationEngine {
	engine := &EnhancedConversationEngine{
		db:              db,
		logger:          logger,
		deepseekService: deepseekService,
	}

	// 初始化子引擎
	engine.emotionalEngine = NewEmotionalIntelligenceEngine(logger)
	engine.contextEnhancer = NewConversationContextEnhancer(db, logger)
	engine.suggestionEngine = NewIntelligentSuggestionEngine(logger, deepseekService)
	engine.personalizationEngine = NewPersonalizationEngine(db, logger)
	engine.feedbackOptimizer = NewRealTimeFeedbackOptimizer(logger)

	logger.Info("🚀 增强对话引擎初始化完成")
	return engine
}

// ProcessConversation 处理增强对话
func (ece *EnhancedConversationEngine) ProcessConversation(ctx context.Context, req *ConversationRequest) (*ConversationResponse, error) {
	start := time.Now()

	ece.logger.WithFields(logrus.Fields{
		"session_id":    req.SessionID,
		"user_id":       req.UserID,
		"message_type":  req.MessageType,
		"message_length": len(req.Message),
	}).Info("🎯 开始处理增强对话")

	// 1. 情感分析
	emotionalState, err := ece.emotionalEngine.AnalyzeEmotion(ctx, req.Message, req.EmotionalState)
	if err != nil {
		ece.logger.WithError(err).Warn("情感分析失败")
		emotionalState = &EmotionalState{
			PrimaryEmotion:    "neutral",
			IntensityLevel:    0.5,
			EngagementLevel:   0.7,
			SatisfactionScore: 0.8,
		}
	}

	// 2. 上下文增强
	enhancedContext, err := ece.contextEnhancer.EnhanceContext(ctx, req)
	if err != nil {
		ece.logger.WithError(err).Warn("上下文增强失败")
		enhancedContext = req.Context
	}

	// 3. 个性化处理
	personalizedRequest, err := ece.personalizationEngine.PersonalizeRequest(ctx, req, emotionalState)
	if err != nil {
		ece.logger.WithError(err).Warn("个性化处理失败")
		personalizedRequest = req
	}

	// 4. 生成智能响应
	response, err := ece.generateIntelligentResponse(ctx, personalizedRequest, emotionalState, enhancedContext)
	if err != nil {
		return nil, fmt.Errorf("生成智能响应失败: %w", err)
	}

	// 5. 生成智能建议
	suggestions, err := ece.suggestionEngine.GenerateSuggestions(ctx, personalizedRequest, response)
	if err != nil {
		ece.logger.WithError(err).Warn("生成智能建议失败")
		suggestions = []SuggestionItem{}
	}

	// 6. 生成下一步指导
	nextStepGuidance, err := ece.generateNextStepGuidance(ctx, personalizedRequest, response)
	if err != nil {
		ece.logger.WithError(err).Warn("生成下一步指导失败")
	}

	// 7. 实时反馈优化
	optimizedResponse, err := ece.feedbackOptimizer.OptimizeResponse(ctx, response, emotionalState)
	if err != nil {
		ece.logger.WithError(err).Warn("实时反馈优化失败")
		optimizedResponse = response
	}

	// 8. 构建最终响应
	finalResponse := &ConversationResponse{
		Content:            optimizedResponse.Content,
		EmotionalTone:      ece.determineEmotionalTone(emotionalState),
		Suggestions:        suggestions,
		PersonalizedTips:   ece.generatePersonalizedTips(personalizedRequest, emotionalState),
		ContextualHelp:     ece.generateContextualHelp(personalizedRequest),
		NextStepGuidance:   nextStepGuidance,
		ConfidenceScore:    optimizedResponse.ConfidenceScore,
		ProcessingTime:     time.Since(start),
		Metadata: map[string]interface{}{
			"emotional_state":  emotionalState,
			"processing_steps": []string{"emotion_analysis", "context_enhancement", "personalization", "response_generation", "suggestion_generation", "guidance_generation", "optimization"},
			"engine_version":   "enhanced_v2.0",
		},
	}

	// 9. 保存对话记录和学习数据
	go ece.saveConversationData(req, finalResponse, emotionalState)

	ece.logger.WithFields(logrus.Fields{
		"session_id":       req.SessionID,
		"processing_time":  finalResponse.ProcessingTime,
		"confidence_score": finalResponse.ConfidenceScore,
		"suggestions_count": len(finalResponse.Suggestions),
	}).Info("🚀 增强对话处理完成")

	return finalResponse, nil
}

// generateIntelligentResponse 生成智能响应
func (ece *EnhancedConversationEngine) generateIntelligentResponse(ctx context.Context, req *ConversationRequest, emotionalState *EmotionalState, enhancedContext map[string]interface{}) (*ConversationResponse, error) {
	// 构建增强的系统提示
	systemPrompt := ece.buildEnhancedSystemPrompt(req.UserProfile, emotionalState, enhancedContext)

	// 构建对话历史
	conversationHistory := ece.buildConversationHistory(req.ConversationHistory)

	// 调用DeepSeek生成响应
	messages := []Message{
		{Role: "system", Content: systemPrompt},
	}

	// 添加对话历史
	messages = append(messages, conversationHistory...)

	// 添加当前用户消息
	messages = append(messages, Message{
		Role:    "user",
		Content: req.Message,
	})

	response, err := ece.deepseekService.Chat(ctx, messages)
	if err != nil {
		return nil, fmt.Errorf("DeepSeek API调用失败: %w", err)
	}

	if len(response.Choices) == 0 {
		return nil, fmt.Errorf("DeepSeek返回空响应")
	}

	return &ConversationResponse{
		Content:         response.Choices[0].Message.Content,
		ConfidenceScore: 0.85, // 基础置信度
	}, nil
}

// buildEnhancedSystemPrompt 构建增强系统提示
func (ece *EnhancedConversationEngine) buildEnhancedSystemPrompt(userProfile *UserProfile, emotionalState *EmotionalState, context map[string]interface{}) string {
	var prompt strings.Builder

	prompt.WriteString("你是一个高度智能的AI运维助手，具备情感智能和个性化服务能力。\n\n")

	// 用户画像信息
	if userProfile != nil {
		prompt.WriteString(fmt.Sprintf("用户画像：\n"))
		prompt.WriteString(fmt.Sprintf("- 专业水平：%s\n", userProfile.ExpertiseLevel))
		prompt.WriteString(fmt.Sprintf("- 偏好风格：%s\n", userProfile.PreferredStyle))
		if len(userProfile.CommonTasks) > 0 {
			prompt.WriteString(fmt.Sprintf("- 常见任务：%s\n", strings.Join(userProfile.CommonTasks, ", ")))
		}
		prompt.WriteString("\n")
	}

	// 情感状态信息
	if emotionalState != nil {
		prompt.WriteString(fmt.Sprintf("用户当前情感状态：\n"))
		prompt.WriteString(fmt.Sprintf("- 主要情绪：%s\n", emotionalState.PrimaryEmotion))
		prompt.WriteString(fmt.Sprintf("- 压力水平：%.1f/1.0\n", emotionalState.StressLevel))
		prompt.WriteString(fmt.Sprintf("- 参与度：%.1f/1.0\n", emotionalState.EngagementLevel))
		prompt.WriteString("\n")
	}

	// 响应指导原则
	prompt.WriteString("响应指导原则：\n")
	prompt.WriteString("1. 根据用户的专业水平调整技术深度\n")
	prompt.WriteString("2. 根据情感状态调整语调和支持程度\n")
	prompt.WriteString("3. 提供个性化的建议和解决方案\n")
	prompt.WriteString("4. 主动提供相关的学习资源和最佳实践\n")
	prompt.WriteString("5. 保持专业、友好、有帮助的态度\n")
	prompt.WriteString("6. 在适当时候提供预防性建议\n\n")

	// 上下文信息
	if len(context) > 0 {
		prompt.WriteString("当前上下文：\n")
		for key, value := range context {
			prompt.WriteString(fmt.Sprintf("- %s: %v\n", key, value))
		}
		prompt.WriteString("\n")
	}

	prompt.WriteString("请基于以上信息提供智能、个性化的响应。")

	return prompt.String()
}

// buildConversationHistory 构建对话历史
func (ece *EnhancedConversationEngine) buildConversationHistory(history []*ConversationTurn) []Message {
	var messages []Message

	// 只保留最近的对话轮次（避免上下文过长）
	maxHistory := 10
	startIndex := 0
	if len(history) > maxHistory {
		startIndex = len(history) - maxHistory
	}

	for i := startIndex; i < len(history); i++ {
		turn := history[i]
		messages = append(messages, Message{
			Role:    "user",
			Content: turn.UserMessage,
		})
		messages = append(messages, Message{
			Role:    "assistant",
			Content: turn.AIResponse,
		})
	}

	return messages
}

// generateNextStepGuidance 生成下一步指导
func (ece *EnhancedConversationEngine) generateNextStepGuidance(ctx context.Context, req *ConversationRequest, response *ConversationResponse) (*NextStepGuidance, error) {
	// 基于用户消息和响应生成下一步指导
	guidance := &NextStepGuidance{
		RecommendedActions: []RecommendedAction{
			{
				ActionID:    "explore_more",
				Title:       "深入了解",
				Description: "获取更多相关信息和最佳实践",
				Risk:        "low",
				Benefit:     "提升专业知识",
				EstimatedTime: "5-10分钟",
			},
		},
		ProgressIndicator: &ProgressIndicator{
			CurrentTask:    "学习运维知识",
			Progress:       0.6,
			CompletedSteps: 3,
			TotalSteps:     5,
			EstimatedTime:  "15分钟",
			NextMilestone:  "掌握基础监控",
		},
	}

	return guidance, nil
}

// determineEmotionalTone 确定情感语调
func (ece *EnhancedConversationEngine) determineEmotionalTone(emotionalState *EmotionalState) string {
	if emotionalState == nil {
		return "neutral"
	}

	switch emotionalState.PrimaryEmotion {
	case "frustrated":
		return "supportive"
	case "confused":
		return "patient"
	case "curious":
		return "encouraging"
	case "confident":
		return "collaborative"
	default:
		return "friendly"
	}
}

// generatePersonalizedTips 生成个性化提示
func (ece *EnhancedConversationEngine) generatePersonalizedTips(req *ConversationRequest, emotionalState *EmotionalState) []string {
	var tips []string

	if req.UserProfile != nil {
		switch req.UserProfile.ExpertiseLevel {
		case "beginner":
			tips = append(tips, "💡 建议先从基础命令开始学习")
			tips = append(tips, "📚 可以查看我们的新手指南")
		case "intermediate":
			tips = append(tips, "🚀 尝试使用更高级的功能")
			tips = append(tips, "🔧 考虑自动化常见任务")
		case "expert":
			tips = append(tips, "⚡ 探索最新的运维技术")
			tips = append(tips, "🎯 分享经验帮助其他用户")
		}
	}

	if emotionalState != nil && emotionalState.StressLevel > 0.7 {
		tips = append(tips, "😌 建议分步骤完成，不要急于求成")
	}

	return tips
}

// generateContextualHelp 生成上下文帮助
func (ece *EnhancedConversationEngine) generateContextualHelp(req *ConversationRequest) *ContextualHelp {
	return &ContextualHelp{
		HelpType:    "explanation",
		Title:       "相关帮助",
		Description: "基于当前对话的相关信息和建议",
		Steps: []string{
			"理解当前问题",
			"查看相关文档",
			"尝试建议的解决方案",
			"验证结果",
		},
		Examples: []string{
			"示例命令和配置",
			"常见问题解决方案",
		},
		RelatedTopics: []string{
			"系统监控",
			"性能优化",
			"故障排除",
		},
		Difficulty: "medium",
	}
}

// saveConversationData 保存对话数据
func (ece *EnhancedConversationEngine) saveConversationData(req *ConversationRequest, response *ConversationResponse, emotionalState *EmotionalState) {
	// 异步保存对话数据用于学习和优化
	ece.logger.WithFields(logrus.Fields{
		"session_id":       req.SessionID,
		"user_id":          req.UserID,
		"emotional_state":  emotionalState.PrimaryEmotion,
		"confidence_score": response.ConfidenceScore,
	}).Debug("保存对话数据用于学习优化")

	// 这里可以实现具体的数据保存逻辑
	// 包括用户行为分析、模型优化等
}
