/* AI运维管理平台 - 统一组件库 */
/* 现代化UI组件系统，参考顶级设计规范 */

/* 对话历史侧边栏 */
.chat-history-sidebar {
    position: fixed;
    top: 0;
    right: -400px;
    width: 400px;
    height: 100vh;
    background: var(--surface-color);
    border-left: 1px solid var(--border-color);
    z-index: 1000;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
    box-shadow: -2px 0 10px rgba(0, 0, 0, 0.1);
}

.chat-history-sidebar.show {
    right: 0;
}

.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: var(--surface-elevated-color);
}

.sidebar-header h5 {
    color: var(--text-primary-color);
    font-weight: 600;
    margin: 0;
}

.sidebar-content {
    flex: 1;
    padding: 1rem;
    overflow-y: auto;
}

.search-box {
    margin-bottom: 1rem;
}

.search-box input {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: 0.5rem;
    background: var(--input-background-color);
    color: var(--text-primary-color);
}

.chat-sessions {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.chat-session-item {
    padding: 1rem;
    border: 1px solid var(--border-color);
    border-radius: 0.75rem;
    background: var(--surface-color);
    cursor: pointer;
    transition: all 0.2s ease;
}

.chat-session-item:hover {
    background: var(--surface-elevated-color);
    border-color: var(--primary-color);
    transform: translateY(-1px);
}

.chat-session-title {
    font-weight: 600;
    color: var(--text-primary-color);
    margin-bottom: 0.25rem;
}

.chat-session-preview {
    font-size: 0.875rem;
    color: var(--text-secondary-color);
    margin-bottom: 0.5rem;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.chat-session-time {
    font-size: 0.75rem;
    color: var(--text-tertiary-color);
}

.loading-sessions,
.no-sessions {
    padding: 2rem 1rem;
    text-align: center;
    color: var(--text-secondary-color);
}

.no-sessions i {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--text-tertiary-color);
}

/* ========================================
   按钮组件系统
   ======================================== */

.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2_5) var(--spacing-4);
  font-family: var(--font-family-sans);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-5);
  text-decoration: none;
  border: 1px solid transparent;
  border-radius: var(--radius-lg);
  cursor: pointer;
  transition: all var(--transition-base);
  position: relative;
  overflow: hidden;
  white-space: nowrap;
  user-select: none;
  transform: translateZ(0);
}

.btn:focus-visible {
  outline: 2px solid var(--color-primary-500);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
}

/* 按钮变体 */
.btn-primary {
  background: linear-gradient(135deg, var(--color-primary-600) 0%, var(--color-primary-700) 100%);
  color: white;
  box-shadow: var(--shadow-primary);
}

.btn-primary:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-primary-700) 0%, var(--color-primary-800) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg), var(--shadow-primary);
}

.btn-secondary {
  background: white;
  color: var(--color-gray-700);
  border-color: var(--color-gray-300);
  box-shadow: var(--shadow-sm);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--color-gray-50);
  border-color: var(--color-gray-400);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.btn-accent {
  background: linear-gradient(135deg, var(--color-accent-500) 0%, var(--color-accent-600) 100%);
  color: white;
  box-shadow: var(--shadow-accent);
}

.btn-accent:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-accent-600) 0%, var(--color-accent-700) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg), var(--shadow-accent);
}

.btn-success {
  background: linear-gradient(135deg, var(--color-success-500) 0%, var(--color-success-600) 100%);
  color: white;
  box-shadow: var(--shadow-success);
}

.btn-success:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-success-600) 0%, var(--color-success-700) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg), var(--shadow-success);
}

.btn-warning {
  background: linear-gradient(135deg, var(--color-warning-400) 0%, var(--color-warning-500) 100%);
  color: white;
  box-shadow: var(--shadow-warning);
}

.btn-warning:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-warning-500) 0%, var(--color-warning-600) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg), var(--shadow-warning);
}

.btn-danger {
  background: linear-gradient(135deg, var(--color-error-500) 0%, var(--color-error-600) 100%);
  color: white;
  box-shadow: var(--shadow-error);
}

.btn-danger:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--color-error-600) 0%, var(--color-error-700) 100%);
  transform: translateY(-1px);
  box-shadow: var(--shadow-lg), var(--shadow-error);
}

/* 按钮大小 */
.btn-xs {
  padding: var(--spacing-1) var(--spacing-2);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-md);
}

.btn-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  border-radius: var(--radius-md);
}

.btn-lg {
  padding: var(--spacing-3) var(--spacing-6);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-xl);
}

.btn-xl {
  padding: var(--spacing-4) var(--spacing-8);
  font-size: var(--font-size-xl);
  border-radius: var(--radius-xl);
}

/* 按钮样式变体 */
.btn-outline {
  background: transparent;
  border-width: 1px;
}

.btn-outline.btn-primary {
  color: var(--color-primary-600);
  border-color: var(--color-primary-600);
  background: transparent;
  box-shadow: none;
}

.btn-outline.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-600);
  color: white;
  box-shadow: var(--shadow-primary);
}

.btn-ghost {
  background: transparent;
  border: none;
  box-shadow: none;
}

.btn-ghost.btn-primary {
  color: var(--color-primary-600);
}

.btn-ghost.btn-primary:hover:not(:disabled) {
  background: var(--color-primary-50);
  color: var(--color-primary-700);
}

/* 圆形按钮 */
.btn-circle {
  border-radius: var(--radius-full);
  padding: var(--spacing-2);
  width: var(--spacing-10);
  height: var(--spacing-10);
}

.btn-circle.btn-sm {
  width: var(--spacing-8);
  height: var(--spacing-8);
  padding: var(--spacing-1_5);
}

.btn-circle.btn-lg {
  width: var(--spacing-12);
  height: var(--spacing-12);
  padding: var(--spacing-3);
}

/* ========================================
   卡片组件系统
   ======================================== */

.card {
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-sm);
  overflow: hidden;
  transition: all var(--transition-base);
  position: relative;
}

.card:hover {
  box-shadow: var(--shadow-md);
  transform: translateY(-2px);
  border-color: var(--color-gray-300);
}

.card-header {
  padding: var(--spacing-6);
  border-bottom: 1px solid var(--color-gray-200);
  background: linear-gradient(135deg, var(--color-gray-50) 0%, white 100%);
}

.card-body {
  padding: var(--spacing-6);
}

.card-footer {
  padding: var(--spacing-6);
  border-top: 1px solid var(--color-gray-200);
  background: linear-gradient(135deg, white 0%, var(--color-gray-50) 100%);
}

/* 卡片变体 */
.card-elevated {
  box-shadow: var(--shadow-lg);
  border: none;
}

.card-elevated:hover {
  box-shadow: var(--shadow-xl);
  transform: translateY(-4px);
}

.card-bordered {
  border-width: 2px;
}

.card-primary {
  border-color: var(--color-primary-200);
  background: linear-gradient(135deg, white 0%, var(--color-primary-50) 100%);
}

.card-success {
  border-color: var(--color-success-200);
  background: linear-gradient(135deg, white 0%, var(--color-success-50) 100%);
}

.card-warning {
  border-color: var(--color-warning-200);
  background: linear-gradient(135deg, white 0%, var(--color-warning-50) 100%);
}

.card-danger {
  border-color: var(--color-error-200);
  background: linear-gradient(135deg, white 0%, var(--color-error-50) 100%);
}

/* ========================================
   表单组件系统
   ======================================== */

.form-group {
  margin-bottom: var(--spacing-4);
}

.form-label {
  display: block;
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-700);
  margin-bottom: var(--spacing-2);
  line-height: var(--line-height-5);
}

.form-control {
  display: block;
  width: 100%;
  padding: var(--spacing-3) var(--spacing-4);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-normal);
  line-height: var(--line-height-5);
  color: var(--color-gray-900);
  background: white;
  border: 1px solid var(--color-gray-300);
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  box-shadow: var(--shadow-xs);
}

.form-control:focus {
  outline: none;
  border-color: var(--color-primary-500);
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1), var(--shadow-sm);
  transform: translateY(-1px);
}

.form-control:disabled {
  background: var(--color-gray-100);
  color: var(--color-gray-500);
  cursor: not-allowed;
  opacity: 0.6;
}

.form-control::placeholder {
  color: var(--color-gray-400);
  font-weight: var(--font-weight-normal);
}

/* 表单控件大小 */
.form-control-sm {
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-xs);
  border-radius: var(--radius-md);
}

.form-control-lg {
  padding: var(--spacing-4) var(--spacing-5);
  font-size: var(--font-size-lg);
  border-radius: var(--radius-xl);
}

/* 表单状态 */
.form-control.is-valid {
  border-color: var(--color-success-500);
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.1);
}

.form-control.is-invalid {
  border-color: var(--color-error-500);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-text {
  font-size: var(--font-size-xs);
  color: var(--color-gray-500);
  margin-top: var(--spacing-1);
  line-height: var(--line-height-4);
}

.form-text.text-success {
  color: var(--color-success-600);
}

.form-text.text-danger {
  color: var(--color-error-600);
}

/* ========================================
   导航组件系统
   ======================================== */

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-4) var(--spacing-6);
  background: white;
  border-bottom: 1px solid var(--color-gray-200);
  box-shadow: var(--shadow-sm);
  position: relative;
  z-index: var(--z-sticky);
}

.navbar-brand {
  display: flex;
  align-items: center;
  gap: var(--spacing-3);
  font-size: var(--font-size-xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  text-decoration: none;
  transition: all var(--transition-base);
}

.navbar-brand:hover {
  color: var(--color-primary-600);
  transform: translateY(-1px);
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: var(--spacing-1);
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-link {
  display: flex;
  align-items: center;
  gap: var(--spacing-2);
  padding: var(--spacing-2) var(--spacing-3);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
  color: var(--color-gray-600);
  text-decoration: none;
  border-radius: var(--radius-lg);
  transition: all var(--transition-base);
  position: relative;
}

.nav-link:hover {
  color: var(--color-primary-600);
  background: var(--color-primary-50);
  transform: translateY(-1px);
}

.nav-link.active {
  color: var(--color-primary-700);
  background: var(--color-primary-100);
  font-weight: var(--font-weight-semibold);
}

/* ========================================
   徽章组件系统
   ======================================== */

.badge {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-1);
  padding: var(--spacing-1) var(--spacing-2_5);
  font-size: var(--font-size-xs);
  font-weight: var(--font-weight-medium);
  line-height: var(--line-height-4);
  border-radius: var(--radius-full);
  white-space: nowrap;
  transition: all var(--transition-base);
}

.badge-primary {
  background: var(--color-primary-100);
  color: var(--color-primary-800);
}

.badge-secondary {
  background: var(--color-gray-100);
  color: var(--color-gray-800);
}

.badge-success {
  background: var(--color-success-100);
  color: var(--color-success-800);
}

.badge-warning {
  background: var(--color-warning-100);
  color: var(--color-warning-800);
}

.badge-danger {
  background: var(--color-error-100);
  color: var(--color-error-800);
}

.badge-info {
  background: var(--color-info-100);
  color: var(--color-info-800);
}

/* 徽章大小 */
.badge-sm {
  padding: var(--spacing-0_5) var(--spacing-2);
  font-size: 0.6875rem; /* 11px */
}

.badge-lg {
  padding: var(--spacing-1_5) var(--spacing-3);
  font-size: var(--font-size-sm);
}

/* ========================================
   状态指示器
   ======================================== */

.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: var(--spacing-2);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-medium);
}

.status-indicator::before {
  content: '';
  width: var(--spacing-2);
  height: var(--spacing-2);
  border-radius: var(--radius-full);
  flex-shrink: 0;
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.status-online::before {
  background: var(--color-success-500);
  box-shadow: 0 0 0 2px var(--color-success-100);
}

.status-offline::before {
  background: var(--color-gray-400);
  box-shadow: 0 0 0 2px var(--color-gray-100);
  animation: none;
}

.status-warning::before {
  background: var(--color-warning-500);
  box-shadow: 0 0 0 2px var(--color-warning-100);
}

.status-error::before {
  background: var(--color-error-500);
  box-shadow: 0 0 0 2px var(--color-error-100);
}

.status-maintenance::before {
  background: var(--color-info-500);
  box-shadow: 0 0 0 2px var(--color-info-100);
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* ========================================
   加载状态组件
   ======================================== */

.loading-spinner {
  display: inline-block;
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--color-gray-200);
  border-top: 2px solid var(--color-primary-500);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.loading-spinner-sm {
  width: 0.875rem;
  height: 0.875rem;
  border-width: 1.5px;
}

.loading-spinner-lg {
  width: 1.5rem;
  height: 1.5rem;
  border-width: 3px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-dots {
  display: inline-flex;
  gap: var(--spacing-1);
  align-items: center;
}

.loading-dots .dot {
  width: 0.375rem;
  height: 0.375rem;
  background: var(--color-primary-500);
  border-radius: var(--radius-full);
  animation: dot-bounce 1.4s ease-in-out infinite both;
}

.loading-dots .dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dots .dot:nth-child(2) { animation-delay: -0.16s; }
.loading-dots .dot:nth-child(3) { animation-delay: 0s; }

@keyframes dot-bounce {
  0%, 80%, 100% {
    transform: scale(0);
  }
  40% {
    transform: scale(1);
  }
}
