package ai

import (
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// PredictiveOperationsEngine 预测性运维引擎
type PredictiveOperationsEngine struct {
	db                   *gorm.DB
	logger               *logrus.Logger
	config               *PredictiveConfig
	dataCollector        *OperationsDataCollector
	mlEngine             *MachineLearningEngine
	insightGenerator     *InsightGenerator
	recommendationEngine *RecommendationEngine
	alertManager         *PredictiveAlertManager
	mutex                sync.RWMutex
	isRunning            bool
	lastAnalysis         time.Time
	predictionAccuracy   float64
	totalPredictions     int64
	correctPredictions   int64
}

// PredictiveConfig 预测配置
type PredictiveConfig struct {
	EnablePrediction      bool          `json:"enable_prediction"`
	AnalysisInterval      time.Duration `json:"analysis_interval"`
	PredictionHorizon     time.Duration `json:"prediction_horizon"`
	MinDataPoints         int           `json:"min_data_points"`
	ConfidenceThreshold   float64       `json:"confidence_threshold"`
	AlertThreshold        float64       `json:"alert_threshold"`
	MaxRecommendations    int           `json:"max_recommendations"`
	EnableAutoRemediation bool          `json:"enable_auto_remediation"`
	LearningRate          float64       `json:"learning_rate"`
	ModelUpdateInterval   time.Duration `json:"model_update_interval"`
}

// PredictiveInsight 预测洞察
type PredictiveInsight struct {
	ID              string                    `json:"id"`
	Type            InsightType               `json:"type"`
	Title           string                    `json:"title"`
	Description     string                    `json:"description"`
	Severity        SeverityLevel             `json:"severity"`
	Confidence      float64                   `json:"confidence"`
	PredictedTime   time.Time                 `json:"predicted_time"`
	AffectedSystems []string                  `json:"affected_systems"`
	Indicators      []PredictiveIndicator     `json:"indicators"`
	Recommendations []OperationRecommendation `json:"recommendations"`
	Metadata        map[string]interface{}    `json:"metadata"`
	CreatedAt       time.Time                 `json:"created_at"`
	Status          InsightStatus             `json:"status"`
}

// InsightType 洞察类型
type InsightType string

const (
	InsightTypePerformanceDegradation  InsightType = "performance_degradation"
	InsightTypeResourceExhaustion      InsightType = "resource_exhaustion"
	InsightTypeSystemFailure           InsightType = "system_failure"
	InsightTypeSecurityThreat          InsightType = "security_threat"
	InsightTypeCapacityPlanning        InsightType = "capacity_planning"
	InsightTypeMaintenanceWindow       InsightType = "maintenance_window"
	InsightTypeOptimizationOpportunity InsightType = "optimization_opportunity"
	InsightTypeCostOptimization        InsightType = "cost_optimization"
)

// SeverityLevel 严重程度
type SeverityLevel string

const (
	SeverityLow      SeverityLevel = "low"
	SeverityMedium   SeverityLevel = "medium"
	SeverityHigh     SeverityLevel = "high"
	SeverityCritical SeverityLevel = "critical"
)

// InsightStatus 洞察状态
type InsightStatus string

const (
	InsightStatusActive     InsightStatus = "active"
	InsightStatusResolved   InsightStatus = "resolved"
	InsightStatusIgnored    InsightStatus = "ignored"
	InsightStatusInProgress InsightStatus = "in_progress"
)

// PredictiveIndicator 预测指标
type PredictiveIndicator struct {
	Name           string         `json:"name"`
	CurrentValue   interface{}    `json:"current_value"`
	PredictedValue interface{}    `json:"predicted_value"`
	Trend          TrendDirection `json:"trend"`
	Confidence     float64        `json:"confidence"`
	Threshold      interface{}    `json:"threshold"`
	Unit           string         `json:"unit"`
}

// TrendDirection 趋势方向
type TrendDirection string

const (
	TrendIncreasing TrendDirection = "increasing"
	TrendDecreasing TrendDirection = "decreasing"
	TrendStable     TrendDirection = "stable"
	TrendVolatile   TrendDirection = "volatile"
)

// OperationRecommendation 运维建议
type OperationRecommendation struct {
	ID          string                 `json:"id"`
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Action      RecommendationAction   `json:"action"`
	Priority    PriorityLevel          `json:"priority"`
	Impact      ImpactLevel            `json:"impact"`
	Effort      EffortLevel            `json:"effort"`
	Commands    []string               `json:"commands"`
	Parameters  map[string]interface{} `json:"parameters"`
	Benefits    []string               `json:"benefits"`
	Risks       []string               `json:"risks"`
	Timeline    string                 `json:"timeline"`
}

// RecommendationAction 建议动作
type RecommendationAction string

const (
	ActionScale    RecommendationAction = "scale"
	ActionOptimize RecommendationAction = "optimize"
	ActionMaintain RecommendationAction = "maintain"
	ActionUpgrade  RecommendationAction = "upgrade"
	ActionReplace  RecommendationAction = "replace"
	ActionMonitor  RecommendationAction = "monitor"
	ActionAlert    RecommendationAction = "alert"
	ActionBackup   RecommendationAction = "backup"
	ActionSecure   RecommendationAction = "secure"
	ActionCleanup  RecommendationAction = "cleanup"
)

// PriorityLevel 优先级
type PriorityLevel string

const (
	PriorityLow      PriorityLevel = "low"
	PriorityMedium   PriorityLevel = "medium"
	PriorityHigh     PriorityLevel = "high"
	PriorityCritical PriorityLevel = "critical"
)

// ImpactLevel 影响程度
type ImpactLevel string

const (
	ImpactLow    ImpactLevel = "low"
	ImpactMedium ImpactLevel = "medium"
	ImpactHigh   ImpactLevel = "high"
)

// EffortLevel 工作量
type EffortLevel string

const (
	EffortLow    EffortLevel = "low"
	EffortMedium EffortLevel = "medium"
	EffortHigh   EffortLevel = "high"
)

// PredictiveAnalysisResult 预测分析结果
type PredictiveAnalysisResult struct {
	Insights        []PredictiveInsight       `json:"insights"`
	Recommendations []OperationRecommendation `json:"recommendations"`
	Summary         AnalysisSummary           `json:"summary"`
	Timestamp       time.Time                 `json:"timestamp"`
	NextAnalysis    time.Time                 `json:"next_analysis"`
}

// AnalysisSummary 分析摘要
type AnalysisSummary struct {
	TotalInsights       int     `json:"total_insights"`
	CriticalInsights    int     `json:"critical_insights"`
	HighPriorityActions int     `json:"high_priority_actions"`
	PredictionAccuracy  float64 `json:"prediction_accuracy"`
	SystemHealth        string  `json:"system_health"`
	RiskLevel           string  `json:"risk_level"`
}

// NewPredictiveOperationsEngine 创建预测性运维引擎
func NewPredictiveOperationsEngine(
	db *gorm.DB,
	logger *logrus.Logger,
	config *PredictiveConfig,
) *PredictiveOperationsEngine {
	if config == nil {
		config = &PredictiveConfig{
			EnablePrediction:      true,
			AnalysisInterval:      15 * time.Minute,
			PredictionHorizon:     24 * time.Hour,
			MinDataPoints:         10,
			ConfidenceThreshold:   0.7,
			AlertThreshold:        0.8,
			MaxRecommendations:    10,
			EnableAutoRemediation: false,
			LearningRate:          0.01,
			ModelUpdateInterval:   time.Hour,
		}
	}

	engine := &PredictiveOperationsEngine{
		db:                 db,
		logger:             logger,
		config:             config,
		isRunning:          false,
		lastAnalysis:       time.Now(),
		predictionAccuracy: 0.75, // 初始准确率
	}

	// 初始化子组件
	engine.dataCollector = NewOperationsDataCollector(db, logger)
	engine.mlEngine = NewMachineLearningEngine(logger, config)
	engine.insightGenerator = NewInsightGenerator(logger, config)
	engine.recommendationEngine = NewRecommendationEngine(logger, config)
	engine.alertManager = NewPredictiveAlertManager(logger, config)

	// 确保数据库表存在
	if err := db.AutoMigrate(&PredictiveInsightRecord{}); err != nil {
		logger.WithError(err).Error("Failed to migrate predictive insight table")
	}

	logger.Info("🔮 预测性运维引擎初始化完成")
	return engine
}

// Start 启动预测性运维引擎
func (poe *PredictiveOperationsEngine) Start() error {
	poe.mutex.Lock()
	defer poe.mutex.Unlock()

	if poe.isRunning {
		return fmt.Errorf("predictive operations engine is already running")
	}

	poe.isRunning = true
	poe.logger.Info("🔮 预测性运维引擎启动成功")

	// 启动后台分析任务
	go poe.runAnalysisLoop()

	// 启动模型更新任务
	go poe.runModelUpdateLoop()

	return nil
}

// Stop 停止预测性运维引擎
func (poe *PredictiveOperationsEngine) Stop() error {
	poe.mutex.Lock()
	defer poe.mutex.Unlock()

	if !poe.isRunning {
		return fmt.Errorf("predictive operations engine is not running")
	}

	poe.isRunning = false
	poe.logger.Info("预测性运维引擎已停止")
	return nil
}

// AnalyzeAndPredict 执行预测分析
func (poe *PredictiveOperationsEngine) AnalyzeAndPredict(ctx context.Context) (*PredictiveAnalysisResult, error) {
	start := time.Now()

	poe.logger.Info("🔮 开始预测性运维分析")

	// 1. 收集运维数据
	operationsData, err := poe.dataCollector.CollectData(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to collect operations data: %w", err)
	}

	// 2. 机器学习预测
	predictions, err := poe.mlEngine.Predict(ctx, operationsData)
	if err != nil {
		poe.logger.WithError(err).Warn("ML prediction failed, using fallback analysis")
		predictions = poe.createFallbackPredictions(operationsData)
	}

	// 3. 生成洞察
	insights := poe.insightGenerator.GenerateInsights(predictions, operationsData)

	// 4. 生成建议
	recommendations := poe.recommendationEngine.GenerateRecommendations(insights, operationsData)

	// 5. 创建分析结果
	result := &PredictiveAnalysisResult{
		Insights:        insights,
		Recommendations: recommendations,
		Summary:         poe.createAnalysisSummary(insights, recommendations),
		Timestamp:       time.Now(),
		NextAnalysis:    time.Now().Add(poe.config.AnalysisInterval),
	}

	// 6. 保存洞察记录
	if err := poe.saveInsights(insights); err != nil {
		poe.logger.WithError(err).Error("Failed to save insights")
	}

	// 7. 触发告警
	if err := poe.alertManager.ProcessInsights(insights); err != nil {
		poe.logger.WithError(err).Error("Failed to process alerts")
	}

	poe.lastAnalysis = time.Now()
	processingTime := time.Since(start)

	poe.logger.WithFields(logrus.Fields{
		"insights_count":      len(insights),
		"recommendations":     len(recommendations),
		"processing_time":     processingTime,
		"prediction_accuracy": poe.predictionAccuracy,
	}).Info("🔮 预测性运维分析完成")

	return result, nil
}

// GetActiveInsights 获取活跃的洞察
func (poe *PredictiveOperationsEngine) GetActiveInsights() ([]PredictiveInsight, error) {
	var records []PredictiveInsightRecord

	if err := poe.db.Where("status = ? AND created_at > ?",
		InsightStatusActive,
		time.Now().Add(-24*time.Hour)).
		Order("severity DESC, confidence DESC").
		Find(&records).Error; err != nil {
		return nil, fmt.Errorf("failed to get active insights: %w", err)
	}

	insights := make([]PredictiveInsight, len(records))
	for i, record := range records {
		insight, err := record.ToInsight()
		if err != nil {
			poe.logger.WithError(err).Warn("Failed to convert insight record")
			continue
		}
		insights[i] = *insight
	}

	return insights, nil
}

// GetRecommendations 获取运维建议
func (poe *PredictiveOperationsEngine) GetRecommendations(
	ctx context.Context,
	filters map[string]interface{},
) ([]OperationRecommendation, error) {
	// 获取最新的分析结果
	result, err := poe.AnalyzeAndPredict(ctx)
	if err != nil {
		return nil, err
	}

	recommendations := result.Recommendations

	// 应用过滤器
	if priority, exists := filters["priority"]; exists {
		filtered := make([]OperationRecommendation, 0)
		for _, rec := range recommendations {
			if string(rec.Priority) == priority.(string) {
				filtered = append(filtered, rec)
			}
		}
		recommendations = filtered
	}

	// 按优先级排序
	sort.Slice(recommendations, func(i, j int) bool {
		return poe.getPriorityWeight(recommendations[i].Priority) >
			poe.getPriorityWeight(recommendations[j].Priority)
	})

	// 限制数量
	if len(recommendations) > poe.config.MaxRecommendations {
		recommendations = recommendations[:poe.config.MaxRecommendations]
	}

	return recommendations, nil
}

// GetMetrics 获取引擎指标
func (poe *PredictiveOperationsEngine) GetMetrics() map[string]interface{} {
	poe.mutex.RLock()
	defer poe.mutex.RUnlock()

	accuracy := 0.0
	if poe.totalPredictions > 0 {
		accuracy = float64(poe.correctPredictions) / float64(poe.totalPredictions)
	}

	return map[string]interface{}{
		"is_running":          poe.isRunning,
		"last_analysis":       poe.lastAnalysis,
		"prediction_accuracy": accuracy,
		"total_predictions":   poe.totalPredictions,
		"correct_predictions": poe.correctPredictions,
		"config":              poe.config,
	}
}

// 私有方法

func (poe *PredictiveOperationsEngine) runAnalysisLoop() {
	ticker := time.NewTicker(poe.config.AnalysisInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !poe.isRunning {
				return
			}

			ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
			_, err := poe.AnalyzeAndPredict(ctx)
			if err != nil {
				poe.logger.WithError(err).Error("Scheduled analysis failed")
			}
			cancel()
		}
	}
}

func (poe *PredictiveOperationsEngine) runModelUpdateLoop() {
	ticker := time.NewTicker(poe.config.ModelUpdateInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			if !poe.isRunning {
				return
			}

			if err := poe.mlEngine.UpdateModels(); err != nil {
				poe.logger.WithError(err).Error("Model update failed")
			}
		}
	}
}

func (poe *PredictiveOperationsEngine) createFallbackPredictions(data *OperationsData) *MLPredictions {
	// 简单的基于规则的预测作为降级方案
	return &MLPredictions{
		SystemHealth:       0.8,
		ResourceUsage:      data.CurrentResourceUsage * 1.1, // 假设增长10%
		FailureProbability: 0.1,
		Confidence:         0.6,
		Timestamp:          time.Now(),
	}
}

func (poe *PredictiveOperationsEngine) createAnalysisSummary(
	insights []PredictiveInsight,
	recommendations []OperationRecommendation,
) AnalysisSummary {
	criticalCount := 0
	highPriorityCount := 0

	for _, insight := range insights {
		if insight.Severity == SeverityCritical {
			criticalCount++
		}
	}

	for _, rec := range recommendations {
		if rec.Priority == PriorityHigh || rec.Priority == PriorityCritical {
			highPriorityCount++
		}
	}

	systemHealth := "healthy"
	riskLevel := "low"

	if criticalCount > 0 {
		systemHealth = "critical"
		riskLevel = "high"
	} else if criticalCount > 2 {
		systemHealth = "warning"
		riskLevel = "medium"
	}

	return AnalysisSummary{
		TotalInsights:       len(insights),
		CriticalInsights:    criticalCount,
		HighPriorityActions: highPriorityCount,
		PredictionAccuracy:  poe.predictionAccuracy,
		SystemHealth:        systemHealth,
		RiskLevel:           riskLevel,
	}
}

func (poe *PredictiveOperationsEngine) saveInsights(insights []PredictiveInsight) error {
	for _, insight := range insights {
		record := &PredictiveInsightRecord{
			InsightID:       insight.ID,
			Type:            string(insight.Type),
			Title:           insight.Title,
			Description:     insight.Description,
			Severity:        string(insight.Severity),
			Confidence:      insight.Confidence,
			PredictedTime:   insight.PredictedTime,
			AffectedSystems: poe.serializeStringSlice(insight.AffectedSystems),
			Metadata:        poe.serializeMetadata(insight.Metadata),
			Status:          string(insight.Status),
			CreatedAt:       time.Now(),
		}

		if err := poe.db.Create(record).Error; err != nil {
			return fmt.Errorf("failed to save insight %s: %w", insight.ID, err)
		}
	}
	return nil
}

func (poe *PredictiveOperationsEngine) getPriorityWeight(priority PriorityLevel) int {
	switch priority {
	case PriorityCritical:
		return 4
	case PriorityHigh:
		return 3
	case PriorityMedium:
		return 2
	case PriorityLow:
		return 1
	default:
		return 0
	}
}

func (poe *PredictiveOperationsEngine) serializeStringSlice(slice []string) string {
	data, _ := json.Marshal(slice)
	return string(data)
}

func (poe *PredictiveOperationsEngine) serializeMetadata(metadata map[string]interface{}) string {
	if metadata == nil {
		return "{}"
	}
	data, _ := json.Marshal(metadata)
	return string(data)
}
