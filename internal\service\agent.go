package service

import (
	"context"
	"fmt"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// AgentManager Agent管理器
type AgentManager struct {
	db           *gorm.DB
	logger       *logrus.Logger
	hostService  HostService
	taskQueue    *TaskQueue
	executors    map[string]*TaskExecutor
	mutex        sync.RWMutex
	maxExecutors int
}

// TaskQueue 任务队列
type TaskQueue struct {
	tasks   chan *ExecutionTask
	results map[string]*ExecutionResult
	mutex   sync.RWMutex
}

// ExecutionTask 执行任务
type ExecutionTask struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"`
	HostID     int64                  `json:"host_id"`
	Command    string                 `json:"command"`
	Parameters map[string]interface{} `json:"parameters"`
	Timeout    time.Duration          `json:"timeout"`
	Priority   int                    `json:"priority"`
	CreatedAt  time.Time              `json:"created_at"`
	CreatedBy  int64                  `json:"created_by"`
	SessionID  string                 `json:"session_id"`
	Status     TaskStatus             `json:"status"`
	RetryCount int                    `json:"retry_count"`
	MaxRetries int                    `json:"max_retries"`
}

// ExecutionResult 执行结果
type ExecutionResult struct {
	TaskID      string                 `json:"task_id"`
	Status      TaskStatus             `json:"status"`
	Output      string                 `json:"output"`
	Error       string                 `json:"error"`
	ExitCode    int                    `json:"exit_code"`
	Duration    time.Duration          `json:"duration"`
	StartedAt   time.Time              `json:"started_at"`
	CompletedAt time.Time              `json:"completed_at"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// TaskStatus 任务状态
type TaskStatus string

const (
	TaskStatusPending   TaskStatus = "pending"
	TaskStatusRunning   TaskStatus = "running"
	TaskStatusCompleted TaskStatus = "completed"
	TaskStatusFailed    TaskStatus = "failed"
	TaskStatusCancelled TaskStatus = "cancelled"
	TaskStatusTimeout   TaskStatus = "timeout"
)

// TaskExecutor 任务执行器
type TaskExecutor struct {
	ID           string
	HostID       int64
	Status       ExecutorStatus
	CurrentTask  *ExecutionTask
	StartedAt    time.Time
	LastActivity time.Time
	mutex        sync.RWMutex
}

// ExecutorStatus 执行器状态
type ExecutorStatus string

const (
	ExecutorStatusIdle    ExecutorStatus = "idle"
	ExecutorStatusBusy    ExecutorStatus = "busy"
	ExecutorStatusError   ExecutorStatus = "error"
	ExecutorStatusOffline ExecutorStatus = "offline"
)

// NewAgentManager 创建Agent管理器
func NewAgentManager(db *gorm.DB, logger *logrus.Logger, hostService HostService) *AgentManager {
	return &AgentManager{
		db:           db,
		logger:       logger,
		hostService:  hostService,
		taskQueue:    NewTaskQueue(1000), // 队列容量1000
		executors:    make(map[string]*TaskExecutor),
		maxExecutors: 50, // 最大执行器数量
	}
}

// NewTaskQueue 创建任务队列
func NewTaskQueue(capacity int) *TaskQueue {
	return &TaskQueue{
		tasks:   make(chan *ExecutionTask, capacity),
		results: make(map[string]*ExecutionResult),
	}
}

// SubmitTask 提交任务
func (am *AgentManager) SubmitTask(task *ExecutionTask) error {
	// 生成任务ID
	if task.ID == "" {
		task.ID = uuid.New().String()
	}

	// 设置默认值
	if task.CreatedAt.IsZero() {
		task.CreatedAt = time.Now()
	}
	if task.Status == "" {
		task.Status = TaskStatusPending
	}
	if task.Timeout == 0 {
		task.Timeout = 30 * time.Second
	}
	if task.MaxRetries == 0 {
		task.MaxRetries = 3
	}

	// 验证任务
	if err := am.validateTask(task); err != nil {
		return fmt.Errorf("task validation failed: %w", err)
	}

	// 添加到队列
	select {
	case am.taskQueue.tasks <- task:
		am.logger.WithFields(logrus.Fields{
			"task_id": task.ID,
			"type":    task.Type,
			"host_id": task.HostID,
		}).Info("Task submitted to queue")
		return nil
	default:
		return fmt.Errorf("task queue is full")
	}
}

// validateTask 验证任务
func (am *AgentManager) validateTask(task *ExecutionTask) error {
	if task.HostID <= 0 {
		return fmt.Errorf("invalid host_id: %d", task.HostID)
	}

	if task.Command == "" {
		return fmt.Errorf("command cannot be empty")
	}

	if task.Timeout <= 0 {
		return fmt.Errorf("timeout must be positive")
	}

	// 验证主机是否存在
	_, err := am.hostService.GetHostByID(task.HostID)
	if err != nil {
		return fmt.Errorf("host not found: %w", err)
	}

	return nil
}

// GetTaskResult 获取任务结果
func (am *AgentManager) GetTaskResult(taskID string) (*ExecutionResult, bool) {
	am.taskQueue.mutex.RLock()
	defer am.taskQueue.mutex.RUnlock()

	result, exists := am.taskQueue.results[taskID]
	return result, exists
}

// CancelTask 取消任务
func (am *AgentManager) CancelTask(taskID string) error {
	am.mutex.Lock()
	defer am.mutex.Unlock()

	// 查找正在执行的任务
	for _, executor := range am.executors {
		executor.mutex.RLock()
		if executor.CurrentTask != nil && executor.CurrentTask.ID == taskID {
			executor.mutex.RUnlock()
			// 标记任务为取消状态
			executor.mutex.Lock()
			if executor.CurrentTask != nil {
				executor.CurrentTask.Status = TaskStatusCancelled
			}
			executor.mutex.Unlock()

			am.logger.WithField("task_id", taskID).Info("Task cancelled")
			return nil
		}
		executor.mutex.RUnlock()
	}

	return fmt.Errorf("task not found or not running: %s", taskID)
}

// StartWorkers 启动工作器
func (am *AgentManager) StartWorkers(ctx context.Context, workerCount int) {
	for i := 0; i < workerCount; i++ {
		go am.worker(ctx, fmt.Sprintf("worker-%d", i))
	}

	am.logger.WithField("worker_count", workerCount).Info("Agent workers started")
}

// worker 工作器
func (am *AgentManager) worker(ctx context.Context, workerID string) {
	am.logger.WithField("worker_id", workerID).Info("Worker started")

	for {
		select {
		case <-ctx.Done():
			am.logger.WithField("worker_id", workerID).Info("Worker stopped")
			return
		case task := <-am.taskQueue.tasks:
			am.executeTask(ctx, workerID, task)
		}
	}
}

// executeTask 执行任务
func (am *AgentManager) executeTask(ctx context.Context, workerID string, task *ExecutionTask) {
	// 创建执行器
	executor := &TaskExecutor{
		ID:           workerID,
		HostID:       task.HostID,
		Status:       ExecutorStatusBusy,
		CurrentTask:  task,
		StartedAt:    time.Now(),
		LastActivity: time.Now(),
	}

	// 注册执行器
	am.mutex.Lock()
	am.executors[workerID] = executor
	am.mutex.Unlock()

	// 执行完成后清理
	defer func() {
		am.mutex.Lock()
		delete(am.executors, workerID)
		am.mutex.Unlock()
	}()

	// 更新任务状态
	task.Status = TaskStatusRunning

	// 创建执行结果
	result := &ExecutionResult{
		TaskID:    task.ID,
		Status:    TaskStatusRunning,
		StartedAt: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	// 保存结果到队列
	am.taskQueue.mutex.Lock()
	am.taskQueue.results[task.ID] = result
	am.taskQueue.mutex.Unlock()

	am.logger.WithFields(logrus.Fields{
		"task_id":   task.ID,
		"worker_id": workerID,
		"host_id":   task.HostID,
		"command":   task.Command,
	}).Info("Executing task")

	// 执行命令
	err := am.executeCommand(ctx, task, result)

	// 更新结果
	result.CompletedAt = time.Now()
	result.Duration = result.CompletedAt.Sub(result.StartedAt)

	if err != nil {
		result.Status = TaskStatusFailed
		result.Error = err.Error()
		task.Status = TaskStatusFailed

		am.logger.WithFields(logrus.Fields{
			"task_id": task.ID,
			"error":   err.Error(),
		}).Error("Task execution failed")
	} else {
		result.Status = TaskStatusCompleted
		task.Status = TaskStatusCompleted

		am.logger.WithFields(logrus.Fields{
			"task_id":  task.ID,
			"duration": result.Duration,
		}).Info("Task completed successfully")
	}

	// 更新结果
	am.taskQueue.mutex.Lock()
	am.taskQueue.results[task.ID] = result
	am.taskQueue.mutex.Unlock()
}

// executeCommand 执行命令
func (am *AgentManager) executeCommand(ctx context.Context, task *ExecutionTask, result *ExecutionResult) error {
	// 创建超时上下文
	timeoutCtx, cancel := context.WithTimeout(ctx, task.Timeout)
	defer cancel()

	// 构建命令执行请求
	req := &model.CommandExecuteRequest{
		Command: task.Command,
		Timeout: int(task.Timeout.Seconds()),
	}

	// 如果有工作目录参数
	if workDir, ok := task.Parameters["working_directory"].(string); ok {
		req.WorkingDirectory = workDir
	}

	// 执行命令
	response, err := am.hostService.ExecuteCommand(task.HostID, req)
	if err != nil {
		return fmt.Errorf("failed to execute command: %w", err)
	}

	// 更新结果
	result.Output = response.Stdout
	result.Error = response.Stderr
	result.ExitCode = response.ExitCode

	// 添加元数据
	result.Metadata["command"] = task.Command
	result.Metadata["host_id"] = task.HostID
	result.Metadata["execution_duration"] = response.Duration

	// 检查是否被取消
	select {
	case <-timeoutCtx.Done():
		if timeoutCtx.Err() == context.DeadlineExceeded {
			result.Status = TaskStatusTimeout
			return fmt.Errorf("command execution timeout")
		}
		return timeoutCtx.Err()
	default:
		// 继续执行
	}

	return nil
}

// GetExecutorStatus 获取执行器状态
func (am *AgentManager) GetExecutorStatus() map[string]*TaskExecutor {
	am.mutex.RLock()
	defer am.mutex.RUnlock()

	status := make(map[string]*TaskExecutor)
	for id, executor := range am.executors {
		// 创建副本避免并发问题
		executor.mutex.RLock()
		status[id] = &TaskExecutor{
			ID:           executor.ID,
			HostID:       executor.HostID,
			Status:       executor.Status,
			StartedAt:    executor.StartedAt,
			LastActivity: executor.LastActivity,
		}
		if executor.CurrentTask != nil {
			status[id].CurrentTask = &ExecutionTask{
				ID:        executor.CurrentTask.ID,
				Type:      executor.CurrentTask.Type,
				HostID:    executor.CurrentTask.HostID,
				Command:   executor.CurrentTask.Command,
				Status:    executor.CurrentTask.Status,
				CreatedAt: executor.CurrentTask.CreatedAt,
			}
		}
		executor.mutex.RUnlock()
	}

	return status
}

// GetQueueStats 获取队列统计
func (am *AgentManager) GetQueueStats() map[string]interface{} {
	am.taskQueue.mutex.RLock()
	defer am.taskQueue.mutex.RUnlock()

	stats := map[string]interface{}{
		"pending_tasks":    len(am.taskQueue.tasks),
		"completed_tasks":  len(am.taskQueue.results),
		"active_executors": len(am.executors),
		"max_executors":    am.maxExecutors,
	}

	// 统计任务状态
	statusCount := make(map[TaskStatus]int)
	for _, result := range am.taskQueue.results {
		statusCount[result.Status]++
	}
	stats["status_count"] = statusCount

	return stats
}
