package ai

import (
	"context"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// DirectExecutionHandler 直接执行处理器
// 专门处理"列出主机"等查询类操作，跳过确认机制直接执行
type DirectExecutionHandler struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewDirectExecutionHandler 创建直接执行处理器
func NewDirectExecutionHandler(db *gorm.DB, logger *logrus.Logger) *DirectExecutionHandler {
	return &DirectExecutionHandler{
		db:     db,
		logger: logger,
	}
}

// ShouldHandleDirectly 检查是否应该直接处理
func (deh *DirectExecutionHandler) ShouldHandleDirectly(message string) bool {
	messageLower := strings.ToLower(message)

	// 直接执行的查询类操作关键词
	directExecutionKeywords := []string{
		"列出主机", "查看主机", "显示主机", "主机列表",
		"list hosts", "show hosts", "display hosts",
		"列出服务器", "查看服务器", "服务器列表",
		"查看所有主机", "显示所有主机",
	}

	for _, keyword := range directExecutionKeywords {
		if strings.Contains(messageLower, strings.ToLower(keyword)) {
			return true
		}
	}

	return false
}

// HandleDirectExecution 直接执行处理
func (deh *DirectExecutionHandler) HandleDirectExecution(ctx context.Context, message string, userID int64, sessionID string) (string, error) {
	deh.logger.WithFields(logrus.Fields{
		"message":    message,
		"user_id":    userID,
		"session_id": sessionID,
	}).Info("DirectExecutionHandler: 开始直接执行处理")

	messageLower := strings.ToLower(message)

	// 处理主机列表查询
	if deh.isHostListQuery(messageLower) {
		return deh.handleHostListQuery(ctx, userID)
	}

	// 处理其他查询类操作
	return deh.handleGenericQuery(ctx, message, userID)
}

// isHostListQuery 检查是否为主机列表查询
func (deh *DirectExecutionHandler) isHostListQuery(messageLower string) bool {
	hostListIndicators := []string{
		"列出主机", "查看主机", "显示主机", "主机列表",
		"list hosts", "show hosts", "display hosts",
		"列出服务器", "查看服务器", "服务器列表",
		"所有主机", "全部主机",
	}

	for _, indicator := range hostListIndicators {
		if strings.Contains(messageLower, strings.ToLower(indicator)) {
			return true
		}
	}

	return false
}

// handleHostListQuery 处理主机列表查询
func (deh *DirectExecutionHandler) handleHostListQuery(ctx context.Context, userID int64) (string, error) {
	deh.logger.WithField("user_id", userID).Info("执行主机列表查询")

	// 直接执行SQL查询
	var hosts []model.Host
	result := deh.db.WithContext(ctx).Find(&hosts)
	if result.Error != nil {
		deh.logger.WithError(result.Error).Error("主机列表查询失败")
		return fmt.Sprintf("❌ **查询失败**\n\n数据库查询出错：%s", result.Error.Error()), nil
	}

	deh.logger.WithFields(logrus.Fields{
		"host_count":    len(hosts),
		"rows_affected": result.RowsAffected,
	}).Info("主机列表查询成功")

	// 格式化结果
	return deh.formatHostListResult(hosts), nil
}

// formatHostListResult 格式化主机列表结果
func (deh *DirectExecutionHandler) formatHostListResult(hosts []model.Host) string {
	if len(hosts) == 0 {
		return `📊 **主机列表查询结果**

🔍 **查询状态**: 成功
📈 **主机数量**: 0

暂无主机数据。

💡 **建议操作**:
• 添加新主机：输入 "添加主机 [IP] [用户名] [密码]"
• 导入主机配置
• 检查数据库连接状态`
	}

	content := fmt.Sprintf(`📊 **主机列表查询结果**

🔍 **查询状态**: 成功
📈 **主机数量**: %d

`, len(hosts))

	// 添加表格头
	content += "| 序号 | 主机名称 | IP地址 | 端口 | 状态 | 环境 | 操作系统 |\n"
	content += "|------|----------|--------|------|------|------|----------|\n"

	// 添加主机数据
	for i, host := range hosts {
		status := "🔴 离线"
		if host.Status == "online" {
			status = "🟢 在线"
		} else if host.Status == "connecting" {
			status = "🟡 连接中"
		}

		environment := host.Environment
		if environment == "" {
			environment = "未设置"
		}

		osType := host.OSType
		if osType == "" {
			osType = "未知"
		}

		content += fmt.Sprintf("| %d | %s | %s | %d | %s | %s | %s |\n",
			i+1,
			host.Name,
			host.IPAddress,
			host.Port,
			status,
			environment,
			osType,
		)
	}

	// 添加操作建议
	content += fmt.Sprintf(`
💡 **可用操作**:
• 查看主机详情：输入 "查看主机 [主机名]"
• 连接主机：输入 "连接主机 [主机名]"
• 检查主机状态：输入 "检查主机状态"
• 添加新主机：输入 "添加主机 [IP] [用户名] [密码]"

⏱️ **查询时间**: %s
🔄 **数据实时性**: 当前数据`, time.Now().Format("2006-01-02 15:04:05"))

	return content
}

// handleGenericQuery 处理通用查询
func (deh *DirectExecutionHandler) handleGenericQuery(ctx context.Context, message string, userID int64) (string, error) {
	// 这里可以扩展处理其他类型的直接查询
	return fmt.Sprintf(`🔍 **查询处理**

📝 **您的请求**: %s

⚠️ **处理状态**: 此类查询暂时需要通过标准流程处理

💡 **建议**: 
• 如需查看主机列表，请输入 "列出主机"
• 如需其他操作，请提供更具体的指令`, message), nil
}

// GetSupportedOperations 获取支持的直接执行操作
func (deh *DirectExecutionHandler) GetSupportedOperations() []string {
	return []string{
		"列出主机",
		"查看主机列表",
		"显示所有主机",
		"主机列表",
		"list hosts",
		"show hosts",
		"display hosts",
	}
}

// IsQueryOperation 检查是否为查询操作
func (deh *DirectExecutionHandler) IsQueryOperation(message string) bool {
	messageLower := strings.ToLower(message)

	queryKeywords := []string{
		"列出", "查看", "显示", "获取", "检查",
		"list", "show", "display", "get", "check",
		"查询", "搜索", "找到",
		"query", "search", "find",
	}

	for _, keyword := range queryKeywords {
		if strings.Contains(messageLower, keyword) {
			return true
		}
	}

	return false
}

// GetExecutionMetrics 获取执行指标
func (deh *DirectExecutionHandler) GetExecutionMetrics() map[string]interface{} {
	return map[string]interface{}{
		"handler_type":   "direct_execution",
		"supported_ops":  len(deh.GetSupportedOperations()),
		"last_execution": time.Now(),
		"execution_mode": "direct_sql",
		"bypass_confirm": true,
	}
}
