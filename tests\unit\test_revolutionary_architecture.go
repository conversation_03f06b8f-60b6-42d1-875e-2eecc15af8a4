package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"aiops-platform/internal/ai"
	"aiops-platform/internal/config"
	"aiops-platform/internal/database"
	"github.com/sirupsen/logrus"
)

func main() {
	fmt.Println("🚀 测试革命性AI运维管理平台架构")
	fmt.Println("=" + string(make([]byte, 50)))

	// 初始化日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 初始化数据库
	dbConfig := config.DatabaseConfig{
		Path:            "./data/test_revolutionary.db",
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: time.Hour,
		ConnMaxIdleTime: 10 * time.Minute,
	}

	db, err := database.New(dbConfig)
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 执行数据库迁移
	if err := database.Migrate(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}

	// 创建模拟DeepSeek配置
	deepseekConfig := &ai.EnhancedDeepSeekConfig{
		APIKey:      "test_api_key",
		BaseURL:     "http://mock.deepseek.com",
		Model:       "test-model",
		MaxTokens:   1000,
		Temperature: 0.7,
		Timeout:     10 * time.Second,
		MaxRetries:  1,
	}

	// 创建革命性集成适配器
	adapter := ai.NewRevolutionaryIntegrationAdapter(db, deepseekConfig, logger)

	// 测试场景
	testCases := []struct {
		name     string
		message  string
		expected string
	}{
		{
			name:     "查询主机列表",
			message:  "列出所有主机",
			expected: ai.INTENT_QUERY,
		},
		{
			name:     "添加主机",
			message:  "添加主机 192.168.1.100 root password123",
			expected: ai.INTENT_EXECUTE,
		},
		{
			name:     "一般对话",
			message:  "你好",
			expected: ai.INTENT_CHAT,
		},
	}

	fmt.Printf("\n🧪 开始测试革命性架构...\n")
	fmt.Printf("-" + string(make([]byte, 30)) + "\n")

	ctx := context.Background()
	successCount := 0

	for i, tc := range testCases {
		fmt.Printf("\n📋 测试 %d: %s\n", i+1, tc.name)
		fmt.Printf("💬 用户输入: %s\n", tc.message)

		// 构建请求
		req := &ai.LegacyProcessMessageRequest{
			SessionID: fmt.Sprintf("test_session_%d", i+1),
			UserID:    1,
			Message:   tc.message,
		}

		// 处理消息
		start := time.Now()
		response, err := adapter.ProcessMessage(ctx, req)
		duration := time.Since(start)

		if err != nil {
			fmt.Printf("❌ 处理失败: %v\n", err)
			continue
		}

		// 验证结果
		if response.Intent == tc.expected {
			successCount++
			fmt.Printf("✅ 测试通过 (耗时: %v)\n", duration)
		} else {
			fmt.Printf("⚠️  意图识别不匹配: 期望 %s, 实际 %s\n", tc.expected, response.Intent)
		}

		fmt.Printf("🎯 识别意图: %s (置信度: %.2f)\n", response.Intent, response.Confidence)
		fmt.Printf("📄 AI回复: %s\n", truncateString(response.Content, 80))

		time.Sleep(500 * time.Millisecond) // 避免请求过于频繁
	}

	// 显示测试结果
	fmt.Printf("\n📊 测试结果统计\n")
	fmt.Printf("=" + string(make([]byte, 20)) + "\n")
	fmt.Printf("总测试数: %d\n", len(testCases))
	fmt.Printf("成功数: %d\n", successCount)
	fmt.Printf("成功率: %.1f%%\n", float64(successCount)/float64(len(testCases))*100)

	// 显示引擎状态
	fmt.Printf("\n🔍 系统状态检查\n")
	fmt.Printf("-" + string(make([]byte, 15)) + "\n")
	status := adapter.GetEngineStatus(ctx)
	for key, value := range status {
		fmt.Printf("%s: %v\n", key, value)
	}

	fmt.Println("\n🎉 革命性架构测试完成！")
}

func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}
