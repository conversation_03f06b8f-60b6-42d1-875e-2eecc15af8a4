package service

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ToolManager 工具管理器
type ToolManager struct {
	logger      *logrus.Logger
	hostService HostService
	db          *gorm.DB
	tools       map[string]ToolHandler
}

// ToolHandler 工具处理器接口
type ToolHandler interface {
	Execute(ctx context.Context, args map[string]interface{}) (interface{}, error)
	GetDefinition() Tool
}

// NewToolManager 创建工具管理器
func NewToolManager(logger *logrus.Logger, hostService HostService) *ToolManager {
	tm := &ToolManager{
		logger:      logger,
		hostService: hostService,
		tools:       make(map[string]ToolHandler),
	}

	// 注册内置工具
	tm.registerBuiltinTools()
	return tm
}

// NewToolManagerWithDB 创建带数据库连接的工具管理器
func NewToolManagerWithDB(logger *logrus.Logger, hostService HostService, db *gorm.DB) *ToolManager {
	tm := &ToolManager{
		logger:      logger,
		hostService: hostService,
		db:          db,
		tools:       make(map[string]ToolHandler),
	}

	// 注册内置工具
	tm.registerBuiltinTools()
	return tm
}

// registerBuiltinTools 注册内置工具
func (tm *ToolManager) registerBuiltinTools() {
	// 主机管理工具
	tm.tools["list_hosts"] = &ListHostsTool{hostService: tm.hostService}
	tm.tools["get_host_info"] = &GetHostInfoTool{hostService: tm.hostService}
	tm.tools["create_host"] = &CreateHostTool{hostService: tm.hostService}
	tm.tools["test_host_connection"] = &TestHostConnectionTool{hostService: tm.hostService}
	tm.tools["execute_command"] = &ExecuteCommandTool{hostService: tm.hostService}

	// 数据库操作工具（仅在有数据库连接时注册）
	if tm.db != nil {
		tm.tools["database_operation"] = NewDatabaseOperationTool(tm.db, tm.logger)
	}

	// 系统信息工具
	tm.tools["get_system_info"] = &GetSystemInfoTool{}
	tm.tools["check_disk_usage"] = &CheckDiskUsageTool{hostService: tm.hostService}
	tm.tools["check_memory_usage"] = &CheckMemoryUsageTool{hostService: tm.hostService}
	tm.tools["check_cpu_usage"] = &CheckCPUUsageTool{hostService: tm.hostService}
	tm.tools["check_network_status"] = &CheckNetworkStatusTool{hostService: tm.hostService}
	tm.tools["get_process_list"] = &GetProcessListTool{hostService: tm.hostService}
	tm.tools["check_service_status"] = &CheckServiceStatusTool{hostService: tm.hostService}

	// 日志分析工具
	tm.tools["analyze_system_logs"] = &AnalyzeSystemLogsTool{hostService: tm.hostService}
	tm.tools["search_logs"] = &SearchLogsTool{hostService: tm.hostService}
	tm.tools["get_error_logs"] = &GetErrorLogsTool{hostService: tm.hostService}

	// 性能监控工具
	tm.tools["get_system_metrics"] = &GetSystemMetricsTool{hostService: tm.hostService}
	tm.tools["monitor_performance"] = &MonitorPerformanceTool{hostService: tm.hostService}
	tm.tools["check_system_health"] = &CheckSystemHealthTool{hostService: tm.hostService}
	tm.tools["check_memory_usage"] = &CheckMemoryUsageTool{hostService: tm.hostService}
	tm.tools["check_cpu_usage"] = &CheckCPUUsageTool{hostService: tm.hostService}

	// 批量操作工具
	tm.tools["batch_check_hosts"] = &BatchCheckHostsTool{hostService: tm.hostService}
	tm.tools["batch_execute_command"] = &BatchExecuteCommandTool{hostService: tm.hostService}
	tm.tools["restart_service"] = &RestartServiceTool{hostService: tm.hostService}
	tm.tools["check_service_status"] = &CheckServiceStatusTool{hostService: tm.hostService}

	// 系统管理工具
	tm.tools["manage_firewall"] = &ManageFirewallTool{hostService: tm.hostService}
	tm.tools["manage_users"] = &ManageUsersTool{hostService: tm.hostService}
	tm.tools["backup_files"] = &BackupFilesTool{hostService: tm.hostService}
	tm.tools["update_system"] = &UpdateSystemTool{hostService: tm.hostService}

	// 监控告警工具
	tm.tools["create_alert_rule"] = &CreateAlertRuleTool{hostService: tm.hostService}
	tm.tools["list_alerts"] = &ListAlertsTool{hostService: tm.hostService}
	tm.tools["acknowledge_alert"] = &AcknowledgeAlertTool{hostService: tm.hostService}

	// 报表生成工具
	tm.tools["generate_system_report"] = &GenerateSystemReportTool{hostService: tm.hostService}
	tm.tools["generate_performance_report"] = &GeneratePerformanceReportTool{hostService: tm.hostService}
	tm.tools["export_logs"] = &ExportLogsTool{hostService: tm.hostService}

	// 智能数据查询工具
	tm.tools["query_host_details"] = &QueryHostDetailsTool{hostService: tm.hostService}
	tm.tools["analyze_system_health"] = &AnalyzeSystemHealthTool{hostService: tm.hostService}
	tm.tools["get_alert_analysis"] = &GetAlertAnalysisTool{hostService: tm.hostService}
	tm.tools["query_operation_history"] = &QueryOperationHistoryTool{hostService: tm.hostService}
	tm.tools["get_performance_trends"] = &GetPerformanceTrendsTool{hostService: tm.hostService}
}

// GetAvailableTools 获取可用工具列表
func (tm *ToolManager) GetAvailableTools() []Tool {
	tools := make([]Tool, 0, len(tm.tools))
	for _, handler := range tm.tools {
		tools = append(tools, handler.GetDefinition())
	}
	return tools
}

// ExecuteTool 执行工具
func (tm *ToolManager) ExecuteTool(ctx context.Context, toolCall ToolCall) (interface{}, error) {
	handler, exists := tm.tools[toolCall.Function.Name]
	if !exists {
		return nil, fmt.Errorf("unknown tool: %s", toolCall.Function.Name)
	}

	// 解析参数
	var args map[string]interface{}
	if err := json.Unmarshal([]byte(toolCall.Function.Arguments), &args); err != nil {
		return nil, fmt.Errorf("failed to parse tool arguments: %w", err)
	}

	// 执行工具
	result, err := handler.Execute(ctx, args)
	if err != nil {
		tm.logger.WithFields(logrus.Fields{
			"tool":  toolCall.Function.Name,
			"args":  args,
			"error": err.Error(),
		}).Error("Tool execution failed")
		return nil, fmt.Errorf("tool execution failed: %w", err)
	}

	tm.logger.WithFields(logrus.Fields{
		"tool": toolCall.Function.Name,
		"args": args,
	}).Info("Tool executed successfully")

	return result, nil
}

// ListHostsTool 列出主机工具
type ListHostsTool struct {
	hostService HostService
}

func (t *ListHostsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	req := &model.HostListQuery{
		Page:  1,
		Limit: 100,
	}

	// 解析可选参数
	if status, ok := args["status"].(string); ok {
		req.Status = status
	}
	if group, ok := args["group"].(string); ok {
		req.GroupName = group
	}

	response, err := t.hostService.ListHosts(req)
	if err != nil {
		return nil, err
	}

	// 返回包含账号信息的完整主机数据（不包含密码等敏感信息）
	hosts := make([]map[string]interface{}, len(response.Hosts))
	for i, host := range response.Hosts {
		hosts[i] = map[string]interface{}{
			"id":          host.ID,
			"name":        host.Name,
			"ip_address":  host.IPAddress,
			"port":        host.Port,
			"username":    host.Username,
			"status":      host.Status,
			"os_type":     host.OSType,
			"environment": host.Environment,
			"group":       host.GroupName,
			"description": host.Description,
			"created_at":  host.CreatedAt.Format("2006-01-02 15:04:05"),
			"updated_at":  host.UpdatedAt.Format("2006-01-02 15:04:05"),
			// 注意：密码字段已在模型中设置为json:"-"，不会被返回
		}
	}

	return map[string]interface{}{
		"hosts": hosts,
		"total": response.Pagination.Total,
	}, nil
}

func (t *ListHostsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "list_hosts",
			Description: "获取主机列表和账号信息，包含主机名、IP地址、端口、用户名、状态、环境等信息（不包含密码），支持按状态和分组筛选",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"status": map[string]interface{}{
						"type":        "string",
						"description": "主机状态筛选 (online, offline, unknown)",
						"enum":        []string{"online", "offline", "unknown"},
					},
					"group": map[string]interface{}{
						"type":        "string",
						"description": "主机分组筛选",
					},
				},
			},
		},
	}
}

// GetHostInfoTool 获取主机信息工具
type GetHostInfoTool struct {
	hostService HostService
}

func (t *GetHostInfoTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	host, err := t.hostService.GetHostByID(int64(hostID))
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"id":          host.ID,
		"name":        host.Name,
		"ip":          host.IPAddress,
		"port":        host.Port,
		"status":      host.Status,
		"os_type":     host.OSType,
		"os_version":  host.OSVersion,
		"cpu_cores":   host.CPUCores,
		"memory_gb":   host.MemoryGB,
		"disk_gb":     host.DiskGB,
		"group":       host.GroupName,
		"environment": host.Environment,
		"description": host.Description,
	}, nil
}

func (t *GetHostInfoTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "get_host_info",
			Description: "获取指定主机的详细信息",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// TestHostConnectionTool 测试主机连接工具
type TestHostConnectionTool struct {
	hostService HostService
}

func (t *TestHostConnectionTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	result, err := t.hostService.TestConnection(int64(hostID))
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"success":  result.Success,
		"message":  result.Message,
		"duration": result.Duration,
	}, nil
}

func (t *TestHostConnectionTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "test_host_connection",
			Description: "测试指定主机的连接状态",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// ExecuteCommandTool 执行命令工具
type ExecuteCommandTool struct {
	hostService HostService
}

func (t *ExecuteCommandTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	command, ok := args["command"].(string)
	if !ok {
		return nil, fmt.Errorf("command is required and must be a string")
	}

	// 安全检查
	if !isCommandSafe(command) {
		return nil, fmt.Errorf("command is not safe to execute: %s", command)
	}

	req := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 30, // 默认30秒超时
	}

	if timeout, ok := args["timeout"].(float64); ok {
		req.Timeout = int(timeout)
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"duration":  result.Duration,
	}, nil
}

func (t *ExecuteCommandTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "execute_command",
			Description: "在指定主机上执行命令",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"command": map[string]interface{}{
						"type":        "string",
						"description": "要执行的命令",
					},
					"timeout": map[string]interface{}{
						"type":        "number",
						"description": "超时时间（秒），默认30秒",
					},
				},
				"required": []string{"host_id", "command"},
			},
		},
	}
}

// isCommandSafe 检查命令安全性
func isCommandSafe(command string) bool {
	// 危险命令列表 - 使用更精确的匹配
	dangerousCommands := []string{
		"rm -rf", "dd if=", "mkfs", "fdisk", "parted",
		"^shutdown", "^reboot", "^halt", "poweroff",  // 修改为精确匹配
		"passwd", "userdel", "groupdel",
		"iptables -F", "ufw --force",
		"chmod 777", "chown -R",
		"format", "del /f", "rmdir /s",
	}

	commandLower := strings.ToLower(command)
	for _, dangerous := range dangerousCommands {
		// 对于以^开头的模式，使用HasPrefix检查
		if strings.HasPrefix(dangerous, "^") {
			pattern := strings.TrimPrefix(dangerous, "^")
			if strings.HasPrefix(commandLower, pattern) {
				return false
			}
		} else if strings.Contains(commandLower, dangerous) {
			return false
		}
	}

	return true
}

// GetSystemInfoTool 获取系统信息工具
type GetSystemInfoTool struct{}

func (t *GetSystemInfoTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// 这里可以返回当前系统的基本信息
	return map[string]interface{}{
		"platform":    "AI运维管理平台",
		"version":     "1.0.0",
		"status":      "running",
		"description": "基于Go语言和DeepSeek API的智能运维管理平台",
	}, nil
}

func (t *GetSystemInfoTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "get_system_info",
			Description: "获取AI运维管理平台的系统信息",
			Parameters: map[string]interface{}{
				"type":       "object",
				"properties": map[string]interface{}{},
			},
		},
	}
}

// CheckDiskUsageTool 检查磁盘使用率工具
type CheckDiskUsageTool struct {
	hostService HostService
}

func (t *CheckDiskUsageTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	// 执行磁盘检查命令
	req := &model.CommandExecuteRequest{
		Command: "df -h",
		Timeout: 10,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"success": result.ExitCode == 0,
		"output":  result.Stdout,
		"command": "df -h",
	}, nil
}

func (t *CheckDiskUsageTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "check_disk_usage",
			Description: "检查指定主机的磁盘使用情况",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// CheckMemoryUsageTool 检查内存使用率工具
type CheckMemoryUsageTool struct {
	hostService HostService
}

func (t *CheckMemoryUsageTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	// 执行内存检查命令
	req := &model.CommandExecuteRequest{
		Command: "free -h",
		Timeout: 10,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"success": result.ExitCode == 0,
		"output":  result.Stdout,
		"command": "free -h",
	}, nil
}

func (t *CheckMemoryUsageTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "check_memory_usage",
			Description: "检查指定主机的内存使用情况",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// CheckCPUUsageTool 检查CPU使用率工具
type CheckCPUUsageTool struct {
	hostService HostService
}

func (t *CheckCPUUsageTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	// 执行CPU检查命令
	req := &model.CommandExecuteRequest{
		Command: "top -bn1 | grep 'Cpu(s)'",
		Timeout: 10,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, err
	}

	return map[string]interface{}{
		"success": result.ExitCode == 0,
		"output":  result.Stdout,
		"command": "top -bn1 | grep 'Cpu(s)'",
	}, nil
}

func (t *CheckCPUUsageTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "check_cpu_usage",
			Description: "检查指定主机的CPU使用情况",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// CheckServiceStatusTool 服务状态检查工具
type CheckServiceStatusTool struct {
	hostService HostService
}

func (t *CheckServiceStatusTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	serviceName := "nginx"
	if sn, ok := args["service_name"].(string); ok {
		serviceName = sn
	}

	req := &model.CommandExecuteRequest{
		Command: fmt.Sprintf("systemctl status %s", serviceName),
		Timeout: 10,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute service status command: %w", err)
	}

	return map[string]interface{}{
		"success":      result.ExitCode == 0,
		"output":       result.Stdout,
		"error":        result.Stderr,
		"exit_code":    result.ExitCode,
		"service_name": serviceName,
		"timestamp":    "now",
	}, nil
}

func (t *CheckServiceStatusTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "check_service_status",
			Description: "检查指定主机上服务的运行状态",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"service_name": map[string]interface{}{
						"type":        "string",
						"description": "服务名称，默认为nginx",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// AnalyzeSystemLogsTool 系统日志分析工具
type AnalyzeSystemLogsTool struct {
	hostService HostService
}

func (t *AnalyzeSystemLogsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	lines := 100
	if l, ok := args["lines"].(float64); ok {
		lines = int(l)
	}

	req := &model.CommandExecuteRequest{
		Command: fmt.Sprintf("tail -%d /var/log/syslog | grep -E '(error|warning|critical|fail)' -i", lines),
		Timeout: 15,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute log analysis command: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"lines":     lines,
		"timestamp": "now",
	}, nil
}

func (t *AnalyzeSystemLogsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "analyze_system_logs",
			Description: "分析系统日志，查找错误、警告和关键信息",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"lines": map[string]interface{}{
						"type":        "number",
						"description": "分析的日志行数，默认100行",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// SearchLogsTool 日志搜索工具
type SearchLogsTool struct {
	hostService HostService
}

func (t *SearchLogsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	keyword, ok := args["keyword"].(string)
	if !ok {
		return nil, fmt.Errorf("keyword is required")
	}

	logFile := "/var/log/syslog"
	if lf, ok := args["log_file"].(string); ok {
		logFile = lf
	}

	req := &model.CommandExecuteRequest{
		Command: fmt.Sprintf("grep -i '%s' %s | tail -20", keyword, logFile),
		Timeout: 15,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute log search command: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"keyword":   keyword,
		"log_file":  logFile,
		"timestamp": "now",
	}, nil
}

func (t *SearchLogsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "search_logs",
			Description: "在指定日志文件中搜索关键词",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"keyword": map[string]interface{}{
						"type":        "string",
						"description": "搜索关键词",
					},
					"log_file": map[string]interface{}{
						"type":        "string",
						"description": "日志文件路径，默认为/var/log/syslog",
					},
				},
				"required": []string{"host_id", "keyword"},
			},
		},
	}
}

// CheckNetworkStatusTool 网络状态检查工具
type CheckNetworkStatusTool struct {
	hostService HostService
}

func (t *CheckNetworkStatusTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	req := &model.CommandExecuteRequest{
		Command: "ip addr show && netstat -tuln | head -20 && ping -c 3 *******",
		Timeout: 15,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute network check command: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"command":   req.Command,
		"timestamp": "now",
	}, nil
}

func (t *CheckNetworkStatusTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "check_network_status",
			Description: "检查指定主机的网络状态，包括网络接口、监听端口和网络连通性",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// GetProcessListTool 进程列表工具
type GetProcessListTool struct {
	hostService HostService
}

func (t *GetProcessListTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	limit := 20
	if l, ok := args["limit"].(float64); ok {
		limit = int(l)
	}

	req := &model.CommandExecuteRequest{
		Command: fmt.Sprintf("ps aux --sort=-%scpu | head -%d", "%", limit+1),
		Timeout: 10,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute process list command: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"command":   req.Command,
		"limit":     limit,
		"timestamp": "now",
	}, nil
}

func (t *GetProcessListTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "get_process_list",
			Description: "获取指定主机的进程列表，按CPU使用率排序",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"limit": map[string]interface{}{
						"type":        "number",
						"description": "返回的进程数量限制，默认20",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// GetErrorLogsTool 错误日志获取工具
type GetErrorLogsTool struct {
	hostService HostService
}

func (t *GetErrorLogsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	lines := 50
	if l, ok := args["lines"].(float64); ok {
		lines = int(l)
	}

	req := &model.CommandExecuteRequest{
		Command: fmt.Sprintf("tail -%d /var/log/syslog | grep -i error", lines),
		Timeout: 10,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute error log command: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"lines":     lines,
		"timestamp": "now",
	}, nil
}

func (t *GetErrorLogsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "get_error_logs",
			Description: "获取系统错误日志",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"lines": map[string]interface{}{
						"type":        "number",
						"description": "获取的日志行数，默认50行",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// GetSystemMetricsTool 系统指标获取工具
type GetSystemMetricsTool struct {
	hostService HostService
}

func (t *GetSystemMetricsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	req := &model.CommandExecuteRequest{
		Command: "echo '=== CPU ===' && top -bn1 | head -5 && echo '=== Memory ===' && free -h && echo '=== Disk ===' && df -h && echo '=== Load ===' && uptime",
		Timeout: 15,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute system metrics command: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"timestamp": "now",
	}, nil
}

func (t *GetSystemMetricsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "get_system_metrics",
			Description: "获取系统综合性能指标，包括CPU、内存、磁盘和负载信息",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// MonitorPerformanceTool 性能监控工具
type MonitorPerformanceTool struct {
	hostService HostService
}

func (t *MonitorPerformanceTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	duration := 5
	if d, ok := args["duration"].(float64); ok {
		duration = int(d)
	}

	req := &model.CommandExecuteRequest{
		Command: fmt.Sprintf("vmstat 1 %d", duration),
		Timeout: duration + 5,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute performance monitoring command: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"duration":  duration,
		"timestamp": "now",
	}, nil
}

func (t *MonitorPerformanceTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "monitor_performance",
			Description: "监控系统性能，实时显示CPU、内存、IO等性能指标",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"duration": map[string]interface{}{
						"type":        "number",
						"description": "监控持续时间（秒），默认5秒",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// CheckSystemHealthTool 系统健康检查工具
type CheckSystemHealthTool struct {
	hostService HostService
}

func (t *CheckSystemHealthTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	req := &model.CommandExecuteRequest{
		Command: "echo '=== System Health Check ===' && echo 'Uptime:' && uptime && echo 'Disk Space:' && df -h | grep -E '^/dev' && echo 'Memory:' && free -h && echo 'CPU Load:' && cat /proc/loadavg && echo 'Failed Services:' && systemctl --failed",
		Timeout: 20,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute system health check command: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"timestamp": "now",
	}, nil
}

func (t *CheckSystemHealthTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "check_system_health",
			Description: "执行全面的系统健康检查，包括运行时间、磁盘空间、内存、CPU负载和服务状态",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// BatchCheckHostsTool 批量检查主机工具
type BatchCheckHostsTool struct {
	hostService HostService
}

func (t *BatchCheckHostsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// 获取所有主机列表
	req := &model.HostListQuery{
		Page:  1,
		Limit: 100,
	}

	response, err := t.hostService.ListHosts(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get host list: %w", err)
	}

	results := make([]map[string]interface{}, 0)
	for _, host := range response.Hosts {
		// 测试每个主机的连接
		testResult, err := t.hostService.TestConnection(host.ID)
		if err != nil {
			results = append(results, map[string]interface{}{
				"host_id":   host.ID,
				"host_name": host.Name,
				"ip":        host.IPAddress,
				"status":    "error",
				"message":   err.Error(),
			})
			continue
		}

		results = append(results, map[string]interface{}{
			"host_id":   host.ID,
			"host_name": host.Name,
			"ip":        host.IPAddress,
			"status":    map[string]bool{"online": testResult.Success, "offline": !testResult.Success}[fmt.Sprintf("%t", testResult.Success)],
			"message":   testResult.Message,
			"duration":  testResult.Duration,
		})
	}

	return map[string]interface{}{
		"total_hosts": len(response.Hosts),
		"results":     results,
		"timestamp":   "now",
	}, nil
}

func (t *BatchCheckHostsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "batch_check_hosts",
			Description: "批量检查所有主机的连接状态",
			Parameters: map[string]interface{}{
				"type":       "object",
				"properties": map[string]interface{}{},
			},
		},
	}
}

// BatchExecuteCommandTool 批量执行命令工具
type BatchExecuteCommandTool struct {
	hostService HostService
}

func (t *BatchExecuteCommandTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	command, ok := args["command"].(string)
	if !ok {
		return nil, fmt.Errorf("command is required")
	}

	// 获取主机列表
	req := &model.HostListQuery{
		Page:   1,
		Limit:  100,
		Status: "online", // 只在在线主机上执行
	}

	response, err := t.hostService.ListHosts(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get host list: %w", err)
	}

	results := make([]map[string]interface{}, 0)
	for _, host := range response.Hosts {
		cmdReq := &model.CommandExecuteRequest{
			Command: command,
			Timeout: 30,
		}

		result, err := t.hostService.ExecuteCommand(host.ID, cmdReq)
		if err != nil {
			results = append(results, map[string]interface{}{
				"host_id":   host.ID,
				"host_name": host.Name,
				"success":   false,
				"error":     err.Error(),
			})
			continue
		}

		results = append(results, map[string]interface{}{
			"host_id":   host.ID,
			"host_name": host.Name,
			"success":   result.ExitCode == 0,
			"output":    result.Stdout,
			"error":     result.Stderr,
			"exit_code": result.ExitCode,
		})
	}

	return map[string]interface{}{
		"command":     command,
		"total_hosts": len(response.Hosts),
		"results":     results,
		"timestamp":   "now",
	}, nil
}

func (t *BatchExecuteCommandTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "batch_execute_command",
			Description: "在所有在线主机上批量执行指定命令",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"command": map[string]interface{}{
						"type":        "string",
						"description": "要执行的命令",
					},
				},
				"required": []string{"command"},
			},
		},
	}
}

// RestartServiceTool 重启服务工具
type RestartServiceTool struct {
	hostService HostService
}

func (t *RestartServiceTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	serviceName, ok := args["service_name"].(string)
	if !ok {
		return nil, fmt.Errorf("service_name is required")
	}

	req := &model.CommandExecuteRequest{
		Command: fmt.Sprintf("sudo systemctl restart %s && sudo systemctl status %s", serviceName, serviceName),
		Timeout: 30,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to restart service: %w", err)
	}

	return map[string]interface{}{
		"success":      result.ExitCode == 0,
		"service_name": serviceName,
		"output":       result.Stdout,
		"error":        result.Stderr,
		"exit_code":    result.ExitCode,
		"timestamp":    "now",
	}, nil
}

func (t *RestartServiceTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "restart_service",
			Description: "重启指定主机上的服务",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"service_name": map[string]interface{}{
						"type":        "string",
						"description": "服务名称（如nginx、apache2、mysql等）",
					},
				},
				"required": []string{"host_id", "service_name"},
			},
		},
	}
}

// ManageFirewallTool 防火墙管理工具
type ManageFirewallTool struct {
	hostService HostService
}

func (t *ManageFirewallTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	action, ok := args["action"].(string)
	if !ok {
		return nil, fmt.Errorf("action is required (status, enable, disable, list)")
	}

	var command string
	switch action {
	case "status":
		command = "sudo ufw status verbose"
	case "enable":
		command = "sudo ufw --force enable"
	case "disable":
		command = "sudo ufw disable"
	case "list":
		command = "sudo ufw status numbered"
	default:
		return nil, fmt.Errorf("invalid action: %s", action)
	}

	req := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 15,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to manage firewall: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"action":    action,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"timestamp": "now",
	}, nil
}

func (t *ManageFirewallTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "manage_firewall",
			Description: "管理主机防火墙，支持查看状态、启用、禁用和列出规则",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"action": map[string]interface{}{
						"type":        "string",
						"description": "操作类型：status(查看状态), enable(启用), disable(禁用), list(列出规则)",
					},
				},
				"required": []string{"host_id", "action"},
			},
		},
	}
}

// ManageUsersTool 用户管理工具
type ManageUsersTool struct {
	hostService HostService
}

func (t *ManageUsersTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	action, ok := args["action"].(string)
	if !ok {
		return nil, fmt.Errorf("action is required (list, info)")
	}

	var command string
	switch action {
	case "list":
		command = "cat /etc/passwd | grep -E '/bin/(bash|sh)$' | cut -d: -f1,3,5"
	case "info":
		username, ok := args["username"].(string)
		if !ok {
			return nil, fmt.Errorf("username is required for info action")
		}
		command = fmt.Sprintf("id %s && groups %s && last %s | head -5", username, username, username)
	default:
		return nil, fmt.Errorf("invalid action: %s", action)
	}

	req := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 15,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to manage users: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"action":    action,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"timestamp": "now",
	}, nil
}

func (t *ManageUsersTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "manage_users",
			Description: "管理系统用户，支持列出用户和查看用户信息",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"action": map[string]interface{}{
						"type":        "string",
						"description": "操作类型：list(列出用户), info(查看用户信息)",
					},
					"username": map[string]interface{}{
						"type":        "string",
						"description": "用户名（info操作时必需）",
					},
				},
				"required": []string{"host_id", "action"},
			},
		},
	}
}

// BackupFilesTool 文件备份工具
type BackupFilesTool struct {
	hostService HostService
}

func (t *BackupFilesTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	sourcePath, ok := args["source_path"].(string)
	if !ok {
		return nil, fmt.Errorf("source_path is required")
	}

	backupPath := fmt.Sprintf("/tmp/backup_%d", time.Now().Unix())
	if bp, ok := args["backup_path"].(string); ok {
		backupPath = bp
	}

	req := &model.CommandExecuteRequest{
		Command: fmt.Sprintf("tar -czf %s.tar.gz %s && ls -lh %s.tar.gz", backupPath, sourcePath, backupPath),
		Timeout: 60,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to backup files: %w", err)
	}

	return map[string]interface{}{
		"success":     result.ExitCode == 0,
		"source_path": sourcePath,
		"backup_path": backupPath + ".tar.gz",
		"output":      result.Stdout,
		"error":       result.Stderr,
		"exit_code":   result.ExitCode,
		"timestamp":   time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

func (t *BackupFilesTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "backup_files",
			Description: "备份指定路径的文件或目录",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"source_path": map[string]interface{}{
						"type":        "string",
						"description": "要备份的源路径",
					},
					"backup_path": map[string]interface{}{
						"type":        "string",
						"description": "备份文件路径（可选，默认为/tmp/backup_时间戳）",
					},
				},
				"required": []string{"host_id", "source_path"},
			},
		},
	}
}

// UpdateSystemTool 系统更新工具
type UpdateSystemTool struct {
	hostService HostService
}

func (t *UpdateSystemTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	action, ok := args["action"].(string)
	if !ok {
		return nil, fmt.Errorf("action is required (check, update)")
	}

	var command string
	switch action {
	case "check":
		command = "sudo apt update && apt list --upgradable"
	case "update":
		command = "sudo apt update && sudo apt upgrade -y"
	default:
		return nil, fmt.Errorf("invalid action: %s", action)
	}

	req := &model.CommandExecuteRequest{
		Command: command,
		Timeout: 300, // 5分钟超时
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to update system: %w", err)
	}

	return map[string]interface{}{
		"success":   result.ExitCode == 0,
		"action":    action,
		"output":    result.Stdout,
		"error":     result.Stderr,
		"exit_code": result.ExitCode,
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

func (t *UpdateSystemTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "update_system",
			Description: "系统更新管理，支持检查可更新包和执行系统更新",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"action": map[string]interface{}{
						"type":        "string",
						"description": "操作类型：check(检查更新), update(执行更新)",
					},
				},
				"required": []string{"host_id", "action"},
			},
		},
	}
}

// CreateAlertRuleTool 创建告警规则工具
type CreateAlertRuleTool struct {
	hostService HostService
}

func (t *CreateAlertRuleTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// 这是一个模拟实现，实际应该与告警系统集成
	ruleName, ok := args["rule_name"].(string)
	if !ok {
		return nil, fmt.Errorf("rule_name is required")
	}

	condition, ok := args["condition"].(string)
	if !ok {
		return nil, fmt.Errorf("condition is required")
	}

	return map[string]interface{}{
		"success":   true,
		"rule_name": ruleName,
		"condition": condition,
		"message":   "告警规则创建成功（模拟）",
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

func (t *CreateAlertRuleTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "create_alert_rule",
			Description: "创建系统告警规则",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"rule_name": map[string]interface{}{
						"type":        "string",
						"description": "告警规则名称",
					},
					"condition": map[string]interface{}{
						"type":        "string",
						"description": "告警条件",
					},
				},
				"required": []string{"rule_name", "condition"},
			},
		},
	}
}

// ListAlertsTool 列出告警工具
type ListAlertsTool struct {
	hostService HostService
}

func (t *ListAlertsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// 这是一个模拟实现，实际应该与告警系统集成
	return map[string]interface{}{
		"success": true,
		"alerts": []map[string]interface{}{
			{
				"id":      1,
				"level":   "warning",
				"message": "CPU使用率超过80%",
				"host":    "web-server-01",
				"time":    time.Now().Add(-time.Hour).Format("2006-01-02 15:04:05"),
				"status":  "active",
			},
			{
				"id":      2,
				"level":   "critical",
				"message": "磁盘空间不足",
				"host":    "db-server-01",
				"time":    time.Now().Add(-30 * time.Minute).Format("2006-01-02 15:04:05"),
				"status":  "active",
			},
		},
		"total":     2,
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

func (t *ListAlertsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "list_alerts",
			Description: "列出系统告警信息",
			Parameters: map[string]interface{}{
				"type":       "object",
				"properties": map[string]interface{}{},
			},
		},
	}
}

// AcknowledgeAlertTool 确认告警工具
type AcknowledgeAlertTool struct {
	hostService HostService
}

func (t *AcknowledgeAlertTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	alertID, ok := args["alert_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("alert_id is required and must be a number")
	}

	// 这是一个模拟实现，实际应该与告警系统集成
	return map[string]interface{}{
		"success":   true,
		"alert_id":  int(alertID),
		"message":   "告警已确认",
		"timestamp": time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

func (t *AcknowledgeAlertTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "acknowledge_alert",
			Description: "确认指定的告警",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"alert_id": map[string]interface{}{
						"type":        "number",
						"description": "告警ID",
					},
				},
				"required": []string{"alert_id"},
			},
		},
	}
}

// GenerateSystemReportTool 生成系统报表工具
type GenerateSystemReportTool struct {
	hostService HostService
}

func (t *GenerateSystemReportTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	reportType := "summary"
	if rt, ok := args["report_type"].(string); ok {
		reportType = rt
	}

	// 获取主机列表
	req := &model.HostListQuery{
		Page:  1,
		Limit: 100,
	}

	response, err := t.hostService.ListHosts(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get host list: %w", err)
	}

	report := map[string]interface{}{
		"report_type":  reportType,
		"generated_at": time.Now().Format("2006-01-02 15:04:05"),
		"summary": map[string]interface{}{
			"total_hosts": len(response.Hosts),
			"online_hosts": func() int {
				count := 0
				for _, host := range response.Hosts {
					if host.Status == "online" {
						count++
					}
				}
				return count
			}(),
		},
		"hosts": func() []map[string]interface{} {
			hosts := make([]map[string]interface{}, len(response.Hosts))
			for i, host := range response.Hosts {
				hosts[i] = map[string]interface{}{
					"name":   host.Name,
					"ip":     host.IPAddress,
					"status": host.Status,
					"group":  host.GroupName,
				}
			}
			return hosts
		}(),
	}

	return map[string]interface{}{
		"success": true,
		"report":  report,
		"message": "系统报表生成成功",
	}, nil
}

func (t *GenerateSystemReportTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "generate_system_report",
			Description: "生成系统状态报表",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"report_type": map[string]interface{}{
						"type":        "string",
						"description": "报表类型（summary, detailed），默认为summary",
					},
				},
			},
		},
	}
}

// GeneratePerformanceReportTool 生成性能报表工具
type GeneratePerformanceReportTool struct {
	hostService HostService
}

func (t *GeneratePerformanceReportTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// 这是一个模拟实现，实际应该从监控数据库获取数据
	return map[string]interface{}{
		"success": true,
		"report": map[string]interface{}{
			"generated_at": time.Now().Format("2006-01-02 15:04:05"),
			"period":       "last_24_hours",
			"metrics": map[string]interface{}{
				"avg_cpu_usage":    "45.2%",
				"avg_memory_usage": "62.8%",
				"avg_disk_usage":   "78.5%",
				"network_traffic":  "1.2GB",
			},
			"top_processes": []map[string]interface{}{
				{"name": "nginx", "cpu": "12.5%", "memory": "256MB"},
				{"name": "mysql", "cpu": "8.3%", "memory": "512MB"},
				{"name": "redis", "cpu": "3.2%", "memory": "128MB"},
			},
		},
		"message": "性能报表生成成功",
	}, nil
}

func (t *GeneratePerformanceReportTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "generate_performance_report",
			Description: "生成系统性能报表",
			Parameters: map[string]interface{}{
				"type":       "object",
				"properties": map[string]interface{}{},
			},
		},
	}
}

// ExportLogsTool 导出日志工具
type ExportLogsTool struct {
	hostService HostService
}

func (t *ExportLogsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	hostID, ok := args["host_id"].(float64)
	if !ok {
		return nil, fmt.Errorf("host_id is required and must be a number")
	}

	logType := "syslog"
	if lt, ok := args["log_type"].(string); ok {
		logType = lt
	}

	lines := 1000
	if l, ok := args["lines"].(float64); ok {
		lines = int(l)
	}

	var logFile string
	switch logType {
	case "syslog":
		logFile = "/var/log/syslog"
	case "auth":
		logFile = "/var/log/auth.log"
	case "nginx":
		logFile = "/var/log/nginx/access.log"
	default:
		logFile = "/var/log/syslog"
	}

	exportPath := fmt.Sprintf("/tmp/exported_logs_%d.txt", time.Now().Unix())
	req := &model.CommandExecuteRequest{
		Command: fmt.Sprintf("tail -%d %s > %s && ls -lh %s", lines, logFile, exportPath, exportPath),
		Timeout: 30,
	}

	result, err := t.hostService.ExecuteCommand(int64(hostID), req)
	if err != nil {
		return nil, fmt.Errorf("failed to export logs: %w", err)
	}

	return map[string]interface{}{
		"success":     result.ExitCode == 0,
		"log_type":    logType,
		"lines":       lines,
		"export_path": exportPath,
		"output":      result.Stdout,
		"error":       result.Stderr,
		"timestamp":   time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

func (t *ExportLogsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "export_logs",
			Description: "导出指定主机的日志文件",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"log_type": map[string]interface{}{
						"type":        "string",
						"description": "日志类型（syslog, auth, nginx），默认为syslog",
					},
					"lines": map[string]interface{}{
						"type":        "number",
						"description": "导出的日志行数，默认1000行",
					},
				},
				"required": []string{"host_id"},
			},
		},
	}
}

// QueryHostDetailsTool 查询主机详细信息工具
type QueryHostDetailsTool struct {
	hostService HostService
}

func (t *QueryHostDetailsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// 支持按ID或名称查询
	var hostID int64
	var hostName string

	if id, ok := args["host_id"].(float64); ok {
		hostID = int64(id)
	}

	if name, ok := args["host_name"].(string); ok {
		hostName = name
	}

	if hostID == 0 && hostName == "" {
		return nil, fmt.Errorf("host_id or host_name is required")
	}

	var host *model.HostResponse
	var err error

	if hostID > 0 {
		host, err = t.hostService.GetHostByID(hostID)
	} else {
		host, err = t.hostService.GetHostByName(hostName)
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get host details: %w", err)
	}

	// 获取主机的详细状态信息
	statusInfo, _ := t.hostService.GetHostStatus(host.ID)

	return map[string]interface{}{
		"host_info":     host,
		"status_detail": statusInfo,
		"summary":       fmt.Sprintf("主机 %s (%s) 当前状态: %s", host.Name, host.IPAddress, host.Status),
		"timestamp":     time.Now(),
	}, nil
}

func (t *QueryHostDetailsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "query_host_details",
			Description: "查询指定主机的详细信息，包括配置、状态、连接历史等",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "主机ID",
					},
					"host_name": map[string]interface{}{
						"type":        "string",
						"description": "主机名称",
					},
				},
			},
		},
	}
}

// GetAlertAnalysisTool 告警分析工具
type GetAlertAnalysisTool struct {
	hostService HostService
}

func (t *GetAlertAnalysisTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// 获取告警列表
	alertQuery := &model.AlertListQuery{
		Page:  1,
		Limit: 50, // 获取最近50条告警
	}

	// 如果指定了主机ID，只查询该主机的告警
	if hostID, ok := args["host_id"].(float64); ok {
		hostIDInt := int64(hostID)
		alertQuery.HostID = &hostIDInt
	}

	// 如果指定了时间范围，应用过滤
	if timeRange, ok := args["time_range"].(string); ok {
		alertQuery.TimeRange = timeRange
	}

	// 这里需要实现告警服务的调用
	// 由于当前代码中告警服务可能未完全实现，我们提供模拟数据

	// 模拟告警分析结果
	analysisResult := map[string]interface{}{
		"total_alerts":    25,
		"critical_alerts": 3,
		"warning_alerts":  15,
		"info_alerts":     7,
		"active_alerts":   18,
		"resolved_alerts": 7,
		"alert_trends": map[string]interface{}{
			"last_24h": 8,
			"last_7d":  25,
			"last_30d": 89,
		},
		"top_alert_sources": []map[string]interface{}{
			{"source": "system", "count": 12},
			{"source": "monitoring", "count": 8},
			{"source": "application", "count": 5},
		},
		"affected_hosts": []map[string]interface{}{
			{"host_name": "web-server-01", "alert_count": 5},
			{"host_name": "db-server-01", "alert_count": 3},
			{"host_name": "app-server-01", "alert_count": 2},
		},
		"recommendations": []string{
			"关注web-server-01的频繁告警，可能存在性能问题",
			"建议检查系统监控配置，减少误报",
			"优先处理3个严重级别告警",
		},
		"analysis_time": time.Now(),
		"summary":       "当前有18个活跃告警，其中3个为严重级别，建议优先处理",
	}

	return analysisResult, nil
}

func (t *GetAlertAnalysisTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "get_alert_analysis",
			Description: "分析系统告警情况，包括告警统计、趋势分析和处理建议",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "可选，指定主机ID来分析特定主机的告警",
					},
					"time_range": map[string]interface{}{
						"type":        "string",
						"description": "时间范围：1h, 6h, 24h, 7d, 30d",
					},
				},
			},
		},
	}
}

// QueryOperationHistoryTool 操作历史查询工具
type QueryOperationHistoryTool struct {
	hostService HostService
}

func (t *QueryOperationHistoryTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// 设置默认查询参数
	limit := 20
	if l, ok := args["limit"].(float64); ok {
		limit = int(l)
	}

	var hostID *int64
	if id, ok := args["host_id"].(float64); ok {
		hostIDInt := int64(id)
		hostID = &hostIDInt
	}

	timeRange := "24h"
	if tr, ok := args["time_range"].(string); ok {
		timeRange = tr
	}

	// 计算时间范围
	var startTime time.Time
	switch timeRange {
	case "1h":
		startTime = time.Now().Add(-1 * time.Hour)
	case "6h":
		startTime = time.Now().Add(-6 * time.Hour)
	case "24h":
		startTime = time.Now().Add(-24 * time.Hour)
	case "7d":
		startTime = time.Now().Add(-7 * 24 * time.Hour)
	case "30d":
		startTime = time.Now().Add(-30 * 24 * time.Hour)
	default:
		startTime = time.Now().Add(-24 * time.Hour)
	}

	// 模拟操作历史数据（实际应该从数据库查询）
	// 使用 startTime 来过滤时间范围内的操作
	allOperations := []map[string]interface{}{
		{
			"id":             1,
			"operation_type": "command_execution",
			"command":        "systemctl status nginx",
			"host_name":      "web-server-01",
			"user":           "admin",
			"status":         "success",
			"duration":       "2.3s",
			"executed_at":    time.Now().Add(-2 * time.Hour),
		},
		{
			"id":             2,
			"operation_type": "file_operation",
			"command":        "cat /var/log/nginx/error.log",
			"host_name":      "web-server-01",
			"user":           "admin",
			"status":         "success",
			"duration":       "1.1s",
			"executed_at":    time.Now().Add(-3 * time.Hour),
		},
		{
			"id":             3,
			"operation_type": "system_monitoring",
			"command":        "top -bn1",
			"host_name":      "db-server-01",
			"user":           "admin",
			"status":         "success",
			"duration":       "0.8s",
			"executed_at":    time.Now().Add(-5 * time.Hour),
		},
	}

	// 过滤时间范围内的操作记录
	var operations []map[string]interface{}
	for _, op := range allOperations {
		if execTime, ok := op["executed_at"].(time.Time); ok {
			if execTime.After(startTime) {
				operations = append(operations, op)
			}
		}
	}

	// 如果指定了主机ID，进一步过滤操作记录
	if hostID != nil {
		var filteredOps []map[string]interface{}
		for _, op := range operations {
			// 这里应该根据hostID过滤，暂时保持所有记录
			// 实际实现中应该检查 op["host_id"] == *hostID
			filteredOps = append(filteredOps, op)
		}
		operations = filteredOps
	}

	// 应用 limit 限制
	if len(operations) > limit {
		operations = operations[:limit]
	}

	// 统计信息
	totalOps := len(operations)
	successOps := 0
	failedOps := 0

	for _, op := range operations {
		if op["status"] == "success" {
			successOps++
		} else {
			failedOps++
		}
	}

	return map[string]interface{}{
		"operations":    operations,
		"total_count":   totalOps,
		"success_count": successOps,
		"failed_count":  failedOps,
		"success_rate":  float64(successOps) / float64(totalOps) * 100,
		"time_range":    timeRange,
		"query_time":    time.Now(),
		"summary":       fmt.Sprintf("最近%s内共执行%d次操作，成功率%.1f%%", timeRange, totalOps, float64(successOps)/float64(totalOps)*100),
	}, nil
}

func (t *QueryOperationHistoryTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "query_operation_history",
			Description: "查询系统操作历史记录，包括命令执行、文件操作等",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "可选，指定主机ID来查询特定主机的操作历史",
					},
					"time_range": map[string]interface{}{
						"type":        "string",
						"description": "时间范围：1h, 6h, 24h, 7d, 30d，默认24h",
					},
					"limit": map[string]interface{}{
						"type":        "number",
						"description": "返回记录数量限制，默认20",
					},
				},
			},
		},
	}
}

// GetPerformanceTrendsTool 性能趋势分析工具
type GetPerformanceTrendsTool struct {
	hostService HostService
}

func (t *GetPerformanceTrendsTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	var hostID *int64
	if id, ok := args["host_id"].(float64); ok {
		hostIDInt := int64(id)
		hostID = &hostIDInt
	}

	timeRange := "24h"
	if tr, ok := args["time_range"].(string); ok {
		timeRange = tr
	}

	metricType := "all"
	if mt, ok := args["metric_type"].(string); ok {
		metricType = mt
	}

	// 模拟性能趋势数据（实际应该从监控数据库查询）
	// 如果指定了hostID，应该查询特定主机的数据
	var hostName string
	if hostID != nil {
		hostName = fmt.Sprintf("host-%d", *hostID)
	} else {
		hostName = "all-hosts"
	}

	trends := map[string]interface{}{
		"cpu_usage": map[string]interface{}{
			"current": 45.2,
			"average": 42.8,
			"peak":    78.5,
			"trend":   "stable",
			"data_points": []map[string]interface{}{
				{"time": time.Now().Add(-6 * time.Hour), "value": 38.5},
				{"time": time.Now().Add(-4 * time.Hour), "value": 42.1},
				{"time": time.Now().Add(-2 * time.Hour), "value": 48.3},
				{"time": time.Now(), "value": 45.2},
			},
		},
		"memory_usage": map[string]interface{}{
			"current": 68.7,
			"average": 65.3,
			"peak":    82.1,
			"trend":   "increasing",
			"data_points": []map[string]interface{}{
				{"time": time.Now().Add(-6 * time.Hour), "value": 58.2},
				{"time": time.Now().Add(-4 * time.Hour), "value": 62.8},
				{"time": time.Now().Add(-2 * time.Hour), "value": 66.5},
				{"time": time.Now(), "value": 68.7},
			},
		},
		"disk_usage": map[string]interface{}{
			"current": 34.5,
			"average": 33.8,
			"peak":    36.2,
			"trend":   "stable",
			"data_points": []map[string]interface{}{
				{"time": time.Now().Add(-6 * time.Hour), "value": 33.1},
				{"time": time.Now().Add(-4 * time.Hour), "value": 33.8},
				{"time": time.Now().Add(-2 * time.Hour), "value": 34.2},
				{"time": time.Now(), "value": 34.5},
			},
		},
		"network_io": map[string]interface{}{
			"current": 125.8,
			"average": 118.4,
			"peak":    245.6,
			"trend":   "fluctuating",
			"data_points": []map[string]interface{}{
				{"time": time.Now().Add(-6 * time.Hour), "value": 98.3},
				{"time": time.Now().Add(-4 * time.Hour), "value": 142.7},
				{"time": time.Now().Add(-2 * time.Hour), "value": 108.9},
				{"time": time.Now(), "value": 125.8},
			},
		},
	}

	// 生成分析建议
	recommendations := []string{}

	// 检查CPU趋势
	if cpuData, ok := trends["cpu_usage"].(map[string]interface{}); ok {
		if current, ok := cpuData["current"].(float64); ok && current > 70 {
			recommendations = append(recommendations, "CPU使用率较高，建议检查高负载进程")
		}
	}

	// 检查内存趋势
	if memData, ok := trends["memory_usage"].(map[string]interface{}); ok {
		if trend, ok := memData["trend"].(string); ok && trend == "increasing" {
			recommendations = append(recommendations, "内存使用率呈上升趋势，建议关注内存泄漏")
		}
	}

	// 检查磁盘使用
	if diskData, ok := trends["disk_usage"].(map[string]interface{}); ok {
		if current, ok := diskData["current"].(float64); ok && current > 80 {
			recommendations = append(recommendations, "磁盘使用率较高，建议清理日志文件")
		}
	}

	if len(recommendations) == 0 {
		recommendations = append(recommendations, "系统性能指标正常，继续保持监控")
	}

	// 如果指定了特定指标类型，只返回该指标
	if metricType != "all" {
		if specificTrend, exists := trends[metricType]; exists {
			trends = map[string]interface{}{
				metricType: specificTrend,
			}
		}
	}

	return map[string]interface{}{
		"trends":          trends,
		"time_range":      timeRange,
		"host_name":       hostName,
		"recommendations": recommendations,
		"analysis_time":   time.Now(),
		"summary":         fmt.Sprintf("最近%s的性能趋势分析完成，发现%d项建议", timeRange, len(recommendations)),
	}, nil
}

func (t *GetPerformanceTrendsTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "get_performance_trends",
			Description: "获取系统性能趋势分析，包括CPU、内存、磁盘、网络等指标的历史数据和趋势",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"host_id": map[string]interface{}{
						"type":        "number",
						"description": "可选，指定主机ID来分析特定主机的性能趋势",
					},
					"time_range": map[string]interface{}{
						"type":        "string",
						"description": "时间范围：1h, 6h, 24h, 7d, 30d，默认24h",
					},
					"metric_type": map[string]interface{}{
						"type":        "string",
						"description": "指标类型：cpu_usage, memory_usage, disk_usage, network_io, all，默认all",
					},
				},
			},
		},
	}
}

// CreateHostTool 创建主机工具
type CreateHostTool struct {
	hostService HostService
}

func (t *CreateHostTool) Execute(ctx context.Context, args map[string]interface{}) (interface{}, error) {
	// 解析参数
	name, ok := args["name"].(string)
	if !ok || name == "" {
		return nil, fmt.Errorf("name is required and must be a non-empty string")
	}

	ipAddress, ok := args["ip_address"].(string)
	if !ok || ipAddress == "" {
		return nil, fmt.Errorf("ip_address is required and must be a non-empty string")
	}

	username, ok := args["username"].(string)
	if !ok || username == "" {
		return nil, fmt.Errorf("username is required and must be a non-empty string")
	}

	// 构建创建主机请求
	req := &model.HostCreateRequest{
		Name:      name,
		IPAddress: ipAddress,
		Username:  username,
		Port:      22, // 默认SSH端口
		CreatedBy: 1,  // TODO: 从上下文获取用户ID
	}

	// 可选参数
	if port, ok := args["port"].(float64); ok {
		req.Port = int(port)
	}

	if password, ok := args["password"].(string); ok {
		req.Password = password
	}

	if sshKeyPath, ok := args["ssh_key_path"].(string); ok {
		req.SSHKeyPath = sshKeyPath
	}

	if description, ok := args["description"].(string); ok {
		req.Description = description
	}

	if environment, ok := args["environment"].(string); ok {
		req.Environment = environment
	}

	if groupName, ok := args["group_name"].(string); ok {
		req.GroupName = groupName
	}

	if monitoringEnabled, ok := args["monitoring_enabled"].(bool); ok {
		req.MonitoringEnabled = monitoringEnabled
	} else {
		req.MonitoringEnabled = true // 默认启用监控
	}

	if backupEnabled, ok := args["backup_enabled"].(bool); ok {
		req.BackupEnabled = backupEnabled
	}

	// 处理标签
	if tagsInterface, ok := args["tags"]; ok {
		if tagsSlice, ok := tagsInterface.([]interface{}); ok {
			tags := make([]string, len(tagsSlice))
			for i, tag := range tagsSlice {
				if tagStr, ok := tag.(string); ok {
					tags[i] = tagStr
				}
			}
			req.Tags = tags
		}
	}

	// 创建主机
	host, err := t.hostService.CreateHost(req)
	if err != nil {
		return nil, fmt.Errorf("failed to create host: %w", err)
	}

	return map[string]interface{}{
		"success":     true,
		"message":     fmt.Sprintf("主机 %s (%s) 创建成功", host.Name, host.IPAddress),
		"host":        host,
		"next_action": "test_connection",
		"suggestions": []string{
			"测试SSH连接",
			"配置监控",
			"查看主机详情",
		},
	}, nil
}
func (t *CreateHostTool) GetDefinition() Tool {
	return Tool{
		Type: "function",
		Function: ToolFunction{
			Name:        "create_host",
			Description: "创建新的主机记录，用于添加需要管理的服务器。当用户想要添加主机、添加服务器、注册新主机时使用此工具。",
			Parameters: map[string]interface{}{
				"type": "object",
				"properties": map[string]interface{}{
					"name": map[string]interface{}{
						"type":        "string",
						"description": "主机名称，必须唯一",
					},
					"ip_address": map[string]interface{}{
						"type":        "string",
						"description": "主机IP地址",
					},
					"username": map[string]interface{}{
						"type":        "string",
						"description": "SSH登录用户名",
					},
					"port": map[string]interface{}{
						"type":        "number",
						"description": "SSH端口，默认22",
					},
					"password": map[string]interface{}{
						"type":        "string",
						"description": "SSH登录密码（可选，如果使用密钥认证则不需要）",
					},
					"ssh_key_path": map[string]interface{}{
						"type":        "string",
						"description": "SSH私钥文件路径（可选）",
					},
					"description": map[string]interface{}{
						"type":        "string",
						"description": "主机描述信息",
					},
					"environment": map[string]interface{}{
						"type":        "string",
						"description": "环境类型：production, staging, development, testing",
					},
					"group_name": map[string]interface{}{
						"type":        "string",
						"description": "主机分组名称",
					},
					"tags": map[string]interface{}{
						"type":        "array",
						"description": "主机标签列表",
						"items": map[string]interface{}{
							"type": "string",
						},
					},
					"monitoring_enabled": map[string]interface{}{
						"type":        "boolean",
						"description": "是否启用监控，默认true",
					},
					"backup_enabled": map[string]interface{}{
						"type":        "boolean",
						"description": "是否启用备份，默认false",
					},
				},
				"required": []string{"name", "ip_address", "username"},
			},
		},
	}
}
