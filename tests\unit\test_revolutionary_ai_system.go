package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"aiops-platform/internal/ai"
	"github.com/sirupsen/logrus"
	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// TestScenario 测试场景
type TestScenario struct {
	Name        string
	Description string
	UserMessage string
	ExpectedOp  string
	ShouldPass  bool
}

func main() {
	fmt.Println("🚀 革命性AI运维管理平台 - 智能意图识别和执行系统测试")
	fmt.Println("=" * 80)

	// 创建日志器
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)

	// 创建内存数据库
	db, err := gorm.Open(sqlite.Open(":memory:"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 创建测试表
	if err := createTestTables(db); err != nil {
		log.Fatal("Failed to create test tables:", err)
	}

	// 配置DeepSeek（使用模拟模式）
	deepseekConfig := &ai.DeepSeekAPIConfig{
		APIKey:           "test-key",
		BaseURL:          "https://api.deepseek.com",
		Model:            "deepseek-chat",
		MaxTokens:        4000,
		Temperature:      0.7,
		TopP:             0.9,
		FrequencyPenalty: 0.0,
		PresencePenalty:  0.0,
		Timeout:          30 * time.Second,
		MaxRetries:       3,
		RetryDelay:       1 * time.Second,
	}

	// 创建革命性AI服务
	aiService, err := ai.NewRevolutionaryAIService(deepseekConfig, db, logger)
	if err != nil {
		log.Fatal("Failed to create AI service:", err)
	}

	// 定义测试场景
	testScenarios := []TestScenario{
		{
			Name:        "数据库查询测试",
			Description: "测试自然语言数据库查询能力",
			UserMessage: "查看所有在线的主机",
			ExpectedOp:  "database",
			ShouldPass:  true,
		},
		{
			Name:        "主机管理测试",
			Description: "测试主机添加操作",
			UserMessage: "添加一台新主机，IP是*************，用户名是admin",
			ExpectedOp:  "database",
			ShouldPass:  true,
		},
		{
			Name:        "系统监控测试",
			Description: "测试系统状态查询",
			UserMessage: "检查服务器的CPU和内存使用情况",
			ExpectedOp:  "system",
			ShouldPass:  true,
		},
		{
			Name:        "安全操作测试",
			Description: "测试高风险操作的安全验证",
			UserMessage: "删除所有主机数据",
			ExpectedOp:  "database",
			ShouldPass:  false, // 应该被安全验证拒绝
		},
		{
			Name:        "对话交互测试",
			Description: "测试一般对话能力",
			UserMessage: "你好，请介绍一下你的功能",
			ExpectedOp:  "conversation",
			ShouldPass:  true,
		},
		{
			Name:        "复杂查询测试",
			Description: "测试复杂的统计查询",
			UserMessage: "统计最近7天每天新增的主机数量",
			ExpectedOp:  "database",
			ShouldPass:  true,
		},
		{
			Name:        "网络诊断测试",
			Description: "测试网络相关操作",
			UserMessage: "ping一下百度看看网络是否正常",
			ExpectedOp:  "network",
			ShouldPass:  true,
		},
		{
			Name:        "模糊意图测试",
			Description: "测试模糊表达的意图识别",
			UserMessage: "帮我看看那些机器的情况",
			ExpectedOp:  "database",
			ShouldPass:  true,
		},
	}

	// 执行测试
	fmt.Println("\n📋 开始执行测试场景...")
	fmt.Println("-" * 80)

	passedTests := 0
	totalTests := len(testScenarios)

	for i, scenario := range testScenarios {
		fmt.Printf("\n🧪 测试 %d/%d: %s\n", i+1, totalTests, scenario.Name)
		fmt.Printf("📝 描述: %s\n", scenario.Description)
		fmt.Printf("💬 用户输入: \"%s\"\n", scenario.UserMessage)

		// 执行测试
		passed := runTestScenario(aiService, scenario, logger)
		if passed {
			passedTests++
			fmt.Printf("✅ 测试通过\n")
		} else {
			fmt.Printf("❌ 测试失败\n")
		}

		fmt.Println("-" * 40)
		time.Sleep(1 * time.Second) // 避免请求过快
	}

	// 输出测试结果
	fmt.Printf("\n📊 测试结果汇总:\n")
	fmt.Printf("总测试数: %d\n", totalTests)
	fmt.Printf("通过测试: %d\n", passedTests)
	fmt.Printf("失败测试: %d\n", totalTests-passedTests)
	fmt.Printf("通过率: %.1f%%\n", float64(passedTests)/float64(totalTests)*100)

	if passedTests == totalTests {
		fmt.Println("\n🎉 所有测试通过！革命性AI系统运行正常！")
	} else {
		fmt.Printf("\n⚠️  有 %d 个测试失败，需要进一步检查。\n", totalTests-passedTests)
	}

	// 性能测试
	fmt.Println("\n⚡ 开始性能测试...")
	runPerformanceTest(aiService, logger)

	fmt.Println("\n🏁 测试完成！")
}

// runTestScenario 运行单个测试场景
func runTestScenario(aiService *ai.RevolutionaryAIService, scenario TestScenario, logger *logrus.Logger) bool {
	ctx := context.Background()

	// 创建测试请求
	req := &ai.RevolutionaryProcessRequest{
		ID:              fmt.Sprintf("test_%d", time.Now().UnixNano()),
		UserID:          1,
		SessionID:       "test_session",
		Message:         scenario.UserMessage,
		Context:         make(map[string]interface{}),
		UserPermissions: []string{"basic", "database_access", "system_access"},
		AutoExecute:     false, // 测试时不自动执行
		Timestamp:       time.Now(),
	}

	// 处理消息
	response, err := aiService.ProcessMessage(ctx, req)
	if err != nil {
		fmt.Printf("❌ 处理失败: %s\n", err.Error())
		return false
	}

	// 验证结果
	success := true

	// 检查操作类型
	if response.OperationType != scenario.ExpectedOp {
		fmt.Printf("❌ 操作类型不匹配: 期望 %s, 实际 %s\n", scenario.ExpectedOp, response.OperationType)
		success = false
	}

	// 检查是否应该通过
	if scenario.ShouldPass && !response.Success {
		fmt.Printf("❌ 期望成功但实际失败: %s\n", response.Error)
		success = false
	}

	if !scenario.ShouldPass && response.Success && !response.RequiresConfirmation {
		fmt.Printf("❌ 期望失败或需要确认，但实际直接成功\n")
		success = false
	}

	// 输出详细信息
	if success {
		fmt.Printf("🎯 意图理解: %s\n", response.IntentUnderstanding)
		fmt.Printf("🔧 操作类型: %s\n", response.OperationType)
		fmt.Printf("🛡️  风险等级: %s\n", response.RiskAssessment.Level)
		fmt.Printf("⚠️  需要确认: %t\n", response.RequiresConfirmation)
	}

	return success
}

// runPerformanceTest 运行性能测试
func runPerformanceTest(aiService *ai.RevolutionaryAIService, logger *logrus.Logger) {
	ctx := context.Background()
	testCount := 10
	totalDuration := time.Duration(0)

	fmt.Printf("🏃 执行 %d 次性能测试...\n", testCount)

	for i := 0; i < testCount; i++ {
		start := time.Now()

		req := &ai.RevolutionaryProcessRequest{
			ID:              fmt.Sprintf("perf_test_%d", i),
			UserID:          1,
			SessionID:       "perf_session",
			Message:         "查看主机列表",
			Context:         make(map[string]interface{}),
			UserPermissions: []string{"basic"},
			AutoExecute:     false,
			Timestamp:       time.Now(),
		}

		_, err := aiService.ProcessMessage(ctx, req)
		duration := time.Since(start)
		totalDuration += duration

		if err != nil {
			fmt.Printf("❌ 性能测试 %d 失败: %s\n", i+1, err.Error())
		} else {
			fmt.Printf("⚡ 测试 %d: %v\n", i+1, duration)
		}
	}

	avgDuration := totalDuration / time.Duration(testCount)
	fmt.Printf("\n📈 性能测试结果:\n")
	fmt.Printf("平均响应时间: %v\n", avgDuration)
	fmt.Printf("总执行时间: %v\n", totalDuration)

	if avgDuration < 3*time.Second {
		fmt.Printf("✅ 性能测试通过！平均响应时间在可接受范围内。\n")
	} else {
		fmt.Printf("⚠️  性能测试警告：平均响应时间较长，建议优化。\n")
	}
}

// createTestTables 创建测试表
func createTestTables(db *gorm.DB) error {
	// 创建主机表
	err := db.Exec(`
		CREATE TABLE IF NOT EXISTS hosts (
			id INTEGER PRIMARY KEY AUTOINCREMENT,
			name VARCHAR(255) NOT NULL,
			ip VARCHAR(45) NOT NULL,
			port INTEGER DEFAULT 22,
			username VARCHAR(255),
			status VARCHAR(50) DEFAULT 'offline',
			created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
			deleted_at DATETIME
		)
	`).Error
	if err != nil {
		return err
	}

	// 插入测试数据
	testHosts := []map[string]interface{}{
		{"name": "web-server-01", "ip": "************", "username": "admin", "status": "online"},
		{"name": "db-server-01", "ip": "************", "username": "root", "status": "online"},
		{"name": "app-server-01", "ip": "************", "username": "deploy", "status": "offline"},
		{"name": "monitor-server", "ip": "************", "username": "monitor", "status": "online"},
	}

	for _, host := range testHosts {
		db.Exec(`
			INSERT INTO hosts (name, ip, username, status) 
			VALUES (?, ?, ?, ?)
		`, host["name"], host["ip"], host["username"], host["status"])
	}

	fmt.Println("✅ 测试数据库和数据创建完成")
	return nil
}
