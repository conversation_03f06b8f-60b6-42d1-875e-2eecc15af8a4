# 高可用部署方案

## 🏗️ 高可用架构概述

AI对话运维管理平台采用多层高可用架构，确保系统在生产环境下的稳定性和可靠性。

### 可用性目标
- **系统可用性**：99.9% (年停机时间<8.76小时)
- **数据可靠性**：99.99% (数据丢失率<0.01%)
- **恢复时间**：RTO < 15分钟
- **恢复点**：RPO < 5分钟

## 🔧 架构组件

### 负载均衡层
```yaml
# nginx.conf - 主负载均衡器配置
upstream aiops_backend {
    least_conn;
    server app1:8080 max_fails=3 fail_timeout=30s;
    server app2:8080 max_fails=3 fail_timeout=30s;
    server app3:8080 max_fails=3 fail_timeout=30s backup;
}

server {
    listen 80;
    listen 443 ssl http2;
    server_name aiops.example.com;
    
    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-CHACHA20-POLY1305;
    
    # 健康检查
    location /health {
        access_log off;
        proxy_pass http://aiops_backend/health;
        proxy_set_header Host $host;
    }
    
    # API代理
    location /api/ {
        proxy_pass http://aiops_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时配置
        proxy_connect_timeout 5s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # 重试配置
        proxy_next_upstream error timeout http_500 http_502 http_503;
        proxy_next_upstream_tries 3;
        proxy_next_upstream_timeout 10s;
    }
    
    # WebSocket代理
    location /ws/ {
        proxy_pass http://aiops_backend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_read_timeout 86400;
    }
}
```

### 应用层高可用
```yaml
# docker-compose.yml - 高可用部署配置
version: '3.8'

services:
  # 负载均衡器
  nginx-primary:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/primary.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
      - nginx_logs:/var/log/nginx
    depends_on:
      - app1
      - app2
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "nginx", "-t"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - aiops_network

  nginx-backup:
    image: nginx:alpine
    ports:
      - "8080:80"
      - "8443:443"
    volumes:
      - ./nginx/backup.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app1
      - app2
    restart: unless-stopped
    networks:
      - aiops_network

  # 应用实例 - 主可用区
  app1:
    build: .
    environment:
      - INSTANCE_ID=app1
      - ZONE=zone-a
      - DB_HOST=db-primary
      - REDIS_HOST=redis-primary
      - CONSUL_HOST=consul
    volumes:
      - app_data:/data
      - app_logs:/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    networks:
      - aiops_network

  app2:
    build: .
    environment:
      - INSTANCE_ID=app2
      - ZONE=zone-a
      - DB_HOST=db-primary
      - REDIS_HOST=redis-primary
      - CONSUL_HOST=consul
    volumes:
      - app_data:/data
      - app_logs:/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    networks:
      - aiops_network

  # 应用实例 - 备用可用区
  app3:
    build: .
    environment:
      - INSTANCE_ID=app3
      - ZONE=zone-b
      - DB_HOST=db-primary
      - REDIS_HOST=redis-primary
      - CONSUL_HOST=consul
    volumes:
      - app_data_backup:/data
      - app_logs_backup:/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    networks:
      - aiops_network

networks:
  aiops_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  app_data:
  app_logs:
  app_data_backup:
  app_logs_backup:
  nginx_logs:
  db_primary_data:
  db_replica_data:
  redis_primary_data:
  redis_replica_data:
  consul_data:
```

## 🗄️ 数据层高可用

### SQLite高可用方案
```go
// 数据库高可用管理器
type DatabaseHA struct {
    primary   *gorm.DB
    replica   *gorm.DB
    backup    *BackupManager
    monitor   *DBMonitor
    failover  *FailoverManager
}

type DBConfig struct {
    Primary struct {
        Path     string `yaml:"path"`
        WALMode  bool   `yaml:"wal_mode"`
        Backup   bool   `yaml:"backup"`
    } `yaml:"primary"`
    
    Replica struct {
        Path     string        `yaml:"path"`
        SyncMode string        `yaml:"sync_mode"`
        Interval time.Duration `yaml:"sync_interval"`
    } `yaml:"replica"`
    
    Backup struct {
        Enabled   bool          `yaml:"enabled"`
        Interval  time.Duration `yaml:"interval"`
        Retention int           `yaml:"retention_days"`
        Storage   string        `yaml:"storage"`
    } `yaml:"backup"`
}

// 读写分离
func (dha *DatabaseHA) GetReadDB() *gorm.DB {
    if dha.monitor.IsReplicaHealthy() {
        return dha.replica
    }
    return dha.primary
}

func (dha *DatabaseHA) GetWriteDB() *gorm.DB {
    return dha.primary
}

// 数据同步
func (dha *DatabaseHA) SyncReplica() error {
    // 使用SQLite的备份API进行同步
    return dha.backup.SyncToReplica(dha.primary, dha.replica)
}
```

### Redis缓存高可用
```yaml
# redis-sentinel.conf
sentinel monitor mymaster redis-primary 6379 2
sentinel down-after-milliseconds mymaster 5000
sentinel failover-timeout mymaster 10000
sentinel parallel-syncs mymaster 1

# Redis主从配置
redis-primary:
  image: redis:7-alpine
  command: redis-server --appendonly yes --save 900 1 --save 300 10
  volumes:
    - redis_primary_data:/data
  networks:
    - aiops_network

redis-replica:
  image: redis:7-alpine
  command: redis-server --appendonly yes --replicaof redis-primary 6379
  volumes:
    - redis_replica_data:/data
  depends_on:
    - redis-primary
  networks:
    - aiops_network

redis-sentinel:
  image: redis:7-alpine
  command: redis-sentinel /etc/redis/sentinel.conf
  volumes:
    - ./redis/sentinel.conf:/etc/redis/sentinel.conf
  depends_on:
    - redis-primary
    - redis-replica
  networks:
    - aiops_network
```

## 🔍 健康检查机制

### 应用健康检查
```go
type HealthChecker struct {
    checks   map[string]HealthCheck
    timeout  time.Duration
    interval time.Duration
}

type HealthCheck interface {
    Name() string
    Check(ctx context.Context) HealthResult
    Critical() bool
}

type HealthResult struct {
    Status    HealthStatus          `json:"status"`
    Message   string               `json:"message"`
    Duration  time.Duration        `json:"duration"`
    Details   map[string]interface{} `json:"details"`
    Timestamp time.Time            `json:"timestamp"`
}

// HTTP健康检查端点
func (h *HealthHandler) HealthCheck(c *gin.Context) {
    ctx, cancel := context.WithTimeout(c.Request.Context(), 10*time.Second)
    defer cancel()
    
    results := make(map[string]HealthResult)
    overall := HealthStatusHealthy
    
    for name, check := range h.checker.checks {
        result := check.Check(ctx)
        results[name] = result
        
        if result.Status == HealthStatusUnhealthy && check.Critical() {
            overall = HealthStatusUnhealthy
        }
    }
    
    response := gin.H{
        "status":    overall,
        "timestamp": time.Now(),
        "checks":    results,
    }
    
    if overall == HealthStatusHealthy {
        c.JSON(http.StatusOK, response)
    } else {
        c.JSON(http.StatusServiceUnavailable, response)
    }
}

// 数据库健康检查
type DatabaseHealthCheck struct {
    db      *gorm.DB
    timeout time.Duration
}

func (dhc *DatabaseHealthCheck) Check(ctx context.Context) HealthResult {
    start := time.Now()
    
    var count int64
    err := dhc.db.WithContext(ctx).Raw("SELECT 1").Count(&count).Error
    
    duration := time.Since(start)
    
    if err != nil {
        return HealthResult{
            Status:    HealthStatusUnhealthy,
            Message:   fmt.Sprintf("Database check failed: %v", err),
            Duration:  duration,
            Timestamp: time.Now(),
        }
    }
    
    return HealthResult{
        Status:    HealthStatusHealthy,
        Message:   "Database is healthy",
        Duration:  duration,
        Timestamp: time.Now(),
    }
}
```

### 外部依赖检查
```go
// DeepSeek API健康检查
type DeepSeekHealthCheck struct {
    client  *DeepSeekClient
    timeout time.Duration
}

func (dshc *DeepSeekHealthCheck) Check(ctx context.Context) HealthResult {
    start := time.Now()
    
    // 发送简单的健康检查请求
    req := &ChatRequest{
        Model: "deepseek-chat",
        Messages: []Message{
            {Role: "user", Content: "ping"},
        },
        MaxTokens: 5,
    }
    
    _, err := dshc.client.ChatCompletion(ctx, req)
    duration := time.Since(start)
    
    if err != nil {
        return HealthResult{
            Status:    HealthStatusUnhealthy,
            Message:   fmt.Sprintf("DeepSeek API check failed: %v", err),
            Duration:  duration,
            Timestamp: time.Now(),
        }
    }
    
    return HealthResult{
        Status:    HealthStatusHealthy,
        Message:   "DeepSeek API is healthy",
        Duration:  duration,
        Timestamp: time.Now(),
    }
}
```

## 🔄 故障转移机制

### 自动故障转移
```go
type FailoverManager struct {
    primary     *ServiceInstance
    backup      *ServiceInstance
    monitor     *ServiceMonitor
    switchover  *SwitchoverManager
    notifier    *NotificationManager
}

type ServiceInstance struct {
    ID       string `json:"id"`
    Address  string `json:"address"`
    Port     int    `json:"port"`
    Status   string `json:"status"`
    LastSeen time.Time `json:"last_seen"`
}

func (fm *FailoverManager) MonitorAndFailover() {
    ticker := time.NewTicker(30 * time.Second)
    defer ticker.Stop()
    
    for {
        select {
        case <-ticker.C:
            if !fm.monitor.IsHealthy(fm.primary) {
                log.Warnf("Primary service %s is unhealthy, initiating failover", fm.primary.ID)
                
                if err := fm.performFailover(); err != nil {
                    log.Errorf("Failover failed: %v", err)
                    fm.notifier.SendAlert("Failover failed", err.Error())
                } else {
                    log.Infof("Failover completed successfully")
                    fm.notifier.SendAlert("Failover completed", "Service switched to backup instance")
                }
            }
        }
    }
}

func (fm *FailoverManager) performFailover() error {
    // 1. 验证备用服务健康状态
    if !fm.monitor.IsHealthy(fm.backup) {
        return errors.New("backup service is also unhealthy")
    }
    
    // 2. 更新负载均衡器配置
    if err := fm.switchover.UpdateLoadBalancer(fm.backup); err != nil {
        return fmt.Errorf("failed to update load balancer: %w", err)
    }
    
    // 3. 更新服务发现
    if err := fm.switchover.UpdateServiceDiscovery(fm.backup); err != nil {
        return fmt.Errorf("failed to update service discovery: %w", err)
    }
    
    // 4. 交换主备角色
    fm.primary, fm.backup = fm.backup, fm.primary
    
    return nil
}
```

### 数据库故障转移
```go
type DatabaseFailover struct {
    primary   *gorm.DB
    replica   *gorm.DB
    monitor   *DBMonitor
    backup    *BackupManager
}

func (df *DatabaseFailover) HandlePrimaryFailure() error {
    log.Warn("Primary database failure detected, switching to replica")
    
    // 1. 停止写入操作
    df.setReadOnlyMode(true)
    
    // 2. 验证副本数据完整性
    if err := df.validateReplicaIntegrity(); err != nil {
        return fmt.Errorf("replica validation failed: %w", err)
    }
    
    // 3. 提升副本为主库
    if err := df.promoteReplica(); err != nil {
        return fmt.Errorf("replica promotion failed: %w", err)
    }
    
    // 4. 恢复写入操作
    df.setReadOnlyMode(false)
    
    log.Info("Database failover completed successfully")
    return nil
}
```

## 📊 监控和告警

### Prometheus监控配置
```yaml
# prometheus.yml
global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  - "alert_rules.yml"

scrape_configs:
  - job_name: 'aiops-platform'
    static_configs:
      - targets: ['app1:8080', 'app2:8080', 'app3:8080']
    metrics_path: /metrics
    scrape_interval: 15s

  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-primary:9113']

  - job_name: 'redis'
    static_configs:
      - targets: ['redis-primary:9121']

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093
```

### 告警规则
```yaml
# alert_rules.yml
groups:
  - name: aiops_platform
    rules:
      - alert: HighErrorRate
        expr: rate(http_requests_total{status=~"5.."}[5m]) > 0.1
        for: 2m
        labels:
          severity: critical
        annotations:
          summary: "High error rate detected"
          description: "Error rate is {{ $value }} for 2 minutes"

      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.instance }} has been down for more than 1 minute"

      - alert: HighMemoryUsage
        expr: (node_memory_MemTotal_bytes - node_memory_MemAvailable_bytes) / node_memory_MemTotal_bytes > 0.9
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is above 90% for 5 minutes"

      - alert: DatabaseConnectionFailure
        expr: database_connections_failed_total > 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Database connection failure"
          description: "Database connection failures detected"
```

## 🔧 运维脚本

### 健康检查脚本
```bash
#!/bin/bash
# health_check.sh

SERVICES=("app1:8080" "app2:8080" "nginx-primary:80")
FAILED_SERVICES=()

for service in "${SERVICES[@]}"; do
    if ! curl -f -s "http://$service/health" > /dev/null; then
        FAILED_SERVICES+=("$service")
    fi
done

if [ ${#FAILED_SERVICES[@]} -gt 0 ]; then
    echo "CRITICAL: Failed services: ${FAILED_SERVICES[*]}"
    exit 2
else
    echo "OK: All services are healthy"
    exit 0
fi
```

### 故障转移脚本
```bash
#!/bin/bash
# failover.sh

PRIMARY_SERVICE="app1:8080"
BACKUP_SERVICE="app3:8080"

# 检查主服务状态
if ! curl -f -s "http://$PRIMARY_SERVICE/health" > /dev/null; then
    echo "Primary service is down, initiating failover..."
    
    # 检查备用服务状态
    if curl -f -s "http://$BACKUP_SERVICE/health" > /dev/null; then
        # 更新负载均衡器配置
        docker exec nginx-primary nginx -s reload
        echo "Failover completed successfully"
    else
        echo "ERROR: Backup service is also down!"
        exit 1
    fi
else
    echo "Primary service is healthy"
fi
```

## 📋 部署检查清单

### 部署前检查
- [ ] 所有配置文件已更新
- [ ] SSL证书已配置
- [ ] 数据库备份已完成
- [ ] 监控系统已配置
- [ ] 告警规则已设置
- [ ] 健康检查已配置
- [ ] 故障转移脚本已测试

### 部署后验证
- [ ] 所有服务正常启动
- [ ] 健康检查通过
- [ ] 负载均衡正常工作
- [ ] 数据库连接正常
- [ ] 监控指标正常
- [ ] 告警系统正常
- [ ] 故障转移测试通过

### 性能验证
- [ ] 响应时间符合要求
- [ ] 并发处理能力达标
- [ ] 资源使用率正常
- [ ] 错误率在可接受范围内
