package risk

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// DeepSeekService DeepSeek AI服务
type DeepSeekService struct {
	config     *DeepSeekConfig
	logger     *logrus.Logger
	httpClient *http.Client
}

// DeepSeekConfig DeepSeek配置
type DeepSeekConfig struct {
	APIKey      string        `json:"api_key"`
	BaseURL     string        `json:"base_url"`
	Model       string        `json:"model"`
	MaxTokens   int           `json:"max_tokens"`
	Temperature float64       `json:"temperature"`
	Timeout     time.Duration `json:"timeout"`
}

// DeepSeekRequest DeepSeek请求
type DeepSeekRequest struct {
	Model       string            `json:"model"`
	Messages    []DeepSeekMessage `json:"messages"`
	MaxTokens   int               `json:"max_tokens"`
	Temperature float64           `json:"temperature"`
	Stream      bool              `json:"stream"`
}

// DeepSeekMessage DeepSeek消息
type DeepSeekMessage struct {
	Role    string `json:"role"`
	Content string `json:"content"`
}

// DeepSeekResponse DeepSeek响应
type DeepSeekResponse struct {
	ID      string           `json:"id"`
	Object  string           `json:"object"`
	Created int64            `json:"created"`
	Model   string           `json:"model"`
	Choices []DeepSeekChoice `json:"choices"`
	Usage   DeepSeekUsage    `json:"usage"`
}

// DeepSeekChoice DeepSeek选择
type DeepSeekChoice struct {
	Index        int             `json:"index"`
	Message      DeepSeekMessage `json:"message"`
	FinishReason string          `json:"finish_reason"`
}

// DeepSeekUsage DeepSeek使用情况
type DeepSeekUsage struct {
	PromptTokens     int `json:"prompt_tokens"`
	CompletionTokens int `json:"completion_tokens"`
	TotalTokens      int `json:"total_tokens"`
}

// NewDeepSeekService 创建DeepSeek服务
func NewDeepSeekService(config *DeepSeekConfig, logger *logrus.Logger) *DeepSeekService {
	if config == nil {
		config = DefaultDeepSeekConfig()
	}

	return &DeepSeekService{
		config: config,
		logger: logger,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// DefaultDeepSeekConfig 默认DeepSeek配置
func DefaultDeepSeekConfig() *DeepSeekConfig {
	return &DeepSeekConfig{
		APIKey:      "", // 需要设置
		BaseURL:     "https://api.deepseek.com/v1",
		Model:       "deepseek-chat",
		MaxTokens:   2048,
		Temperature: 0.1,
		Timeout:     30 * time.Second,
	}
}

// AnalyzeRisk 分析风险
func (ds *DeepSeekService) AnalyzeRisk(ctx context.Context, request *RiskAnalysisRequest) (*RiskAnalysisResponse, error) {
	// 构建分析提示
	prompt := ds.buildRiskAnalysisPrompt(request)

	// 调用DeepSeek API
	response, err := ds.callDeepSeekAPI(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to call DeepSeek API: %w", err)
	}

	// 解析AI响应
	riskResponse, err := ds.parseRiskAnalysisResponse(response)
	if err != nil {
		return nil, fmt.Errorf("failed to parse AI response: %w", err)
	}

	return riskResponse, nil
}

// GenerateRecommendations 生成建议
func (ds *DeepSeekService) GenerateRecommendations(ctx context.Context, risk *RiskAssessment) ([]*RiskRecommendation, error) {
	// 构建建议生成提示
	prompt := ds.buildRecommendationPrompt(risk)

	// 调用DeepSeek API
	response, err := ds.callDeepSeekAPI(ctx, prompt)
	if err != nil {
		return nil, fmt.Errorf("failed to call DeepSeek API: %w", err)
	}

	// 解析建议响应
	recommendations, err := ds.parseRecommendationResponse(response)
	if err != nil {
		return nil, fmt.Errorf("failed to parse recommendation response: %w", err)
	}

	return recommendations, nil
}

// LearnFromFeedback 从反馈中学习
func (ds *DeepSeekService) LearnFromFeedback(ctx context.Context, feedback *RiskFeedback) error {
	ds.logger.WithFields(logrus.Fields{
		"analysis_id":    feedback.AnalysisID,
		"predicted_risk": feedback.PredictedRisk,
		"actual_outcome": feedback.ActualOutcome,
		"user_rating":    feedback.UserRating,
	}).Info("Learning from risk analysis feedback")

	// 简化实现：记录反馈用于后续改进
	// 在实际实现中，这里可以用于微调模型或调整分析策略

	return nil
}

// buildRiskAnalysisPrompt 构建风险分析提示
func (ds *DeepSeekService) buildRiskAnalysisPrompt(request *RiskAnalysisRequest) string {
	var prompt strings.Builder

	prompt.WriteString("你是一个专业的系统运维安全专家。请分析以下命令的风险等级，并提供详细的分析结果。\n\n")

	prompt.WriteString("命令: ")
	prompt.WriteString(request.Command)
	prompt.WriteString("\n\n")

	if request.Context != nil {
		prompt.WriteString("执行环境:\n")
		prompt.WriteString(fmt.Sprintf("- 工作目录: %s\n", request.Context.WorkingDirectory))
		prompt.WriteString(fmt.Sprintf("- 用户: %s\n", request.Context.User))
		if len(request.Context.PreviousCommands) > 0 {
			prompt.WriteString("- 前序命令: ")
			prompt.WriteString(strings.Join(request.Context.PreviousCommands, ", "))
			prompt.WriteString("\n")
		}
	}

	if request.Environment != nil {
		prompt.WriteString("\n系统环境:\n")
		prompt.WriteString(fmt.Sprintf("- 操作系统: %s\n", request.Environment.OS))
		prompt.WriteString(fmt.Sprintf("- 主机名: %s\n", request.Environment.Hostname))
		if request.Environment.Resources != nil {
			prompt.WriteString(fmt.Sprintf("- CPU使用率: %.1f%%\n", request.Environment.Resources.CPUUsage))
			prompt.WriteString(fmt.Sprintf("- 内存使用率: %.1f%%\n", request.Environment.Resources.MemoryUsage))
		}
	}

	if request.UserProfile != nil {
		prompt.WriteString("\n用户信息:\n")
		prompt.WriteString(fmt.Sprintf("- 角色: %s\n", request.UserProfile.Role))
		prompt.WriteString(fmt.Sprintf("- 经验水平: %s\n", request.UserProfile.Experience))
		prompt.WriteString(fmt.Sprintf("- 风险容忍度: %s\n", request.UserProfile.RiskTolerance))
	}

	prompt.WriteString("\n请按照以下JSON格式返回分析结果:\n")
	prompt.WriteString(`{
  "risk_level": "low|medium|high|critical",
  "confidence": 0.0-1.0,
  "reasoning": "详细的风险分析推理",
  "factors": [
    {
      "type": "风险因素类型",
      "description": "风险因素描述",
      "severity": "low|medium|high|critical",
      "impact": "影响范围",
      "likelihood": 0.0-1.0,
      "weight": 0.0-1.0,
      "evidence": ["证据1", "证据2"]
    }
  ],
  "mitigations": [
    {
      "type": "缓解措施类型",
      "description": "缓解措施描述",
      "actions": ["行动1", "行动2"],
      "effectiveness": 0.0-1.0,
      "cost": "low|medium|high",
      "time_required": "所需时间"
    }
  ],
  "alternatives": [
    {
      "command": "替代命令",
      "description": "替代方案描述",
      "risk_level": "low|medium|high|critical",
      "similarity": 0.0-1.0,
      "benefits": ["优势1", "优势2"],
      "limitations": ["限制1", "限制2"]
    }
  ]
}`)

	return prompt.String()
}

// buildRecommendationPrompt 构建建议生成提示
func (ds *DeepSeekService) buildRecommendationPrompt(risk *RiskAssessment) string {
	var prompt strings.Builder

	prompt.WriteString("基于以下风险评估结果，请生成具体的安全建议和操作指导。\n\n")

	prompt.WriteString("风险评估结果:\n")
	prompt.WriteString(fmt.Sprintf("- 命令: %s\n", risk.Command))
	prompt.WriteString(fmt.Sprintf("- 风险等级: %s\n", risk.RiskLevel))
	prompt.WriteString(fmt.Sprintf("- 置信度: %.2f\n", risk.Confidence))
	prompt.WriteString(fmt.Sprintf("- 分析结果: %s\n", risk.Analysis))

	prompt.WriteString("\n请按照以下JSON格式返回建议:\n")
	prompt.WriteString(`{
  "recommendations": [
    {
      "type": "prevention|mitigation|alternative",
      "priority": "low|medium|high|critical",
      "title": "建议标题",
      "description": "详细描述",
      "actions": ["具体行动1", "具体行动2"],
      "benefits": ["好处1", "好处2"],
      "risks": ["风险1", "风险2"],
      "confidence": 0.0-1.0
    }
  ]
}`)

	return prompt.String()
}

// callDeepSeekAPI 调用DeepSeek API
func (ds *DeepSeekService) callDeepSeekAPI(ctx context.Context, prompt string) (string, error) {
	// 构建请求
	request := DeepSeekRequest{
		Model:       ds.config.Model,
		MaxTokens:   ds.config.MaxTokens,
		Temperature: ds.config.Temperature,
		Stream:      false,
		Messages: []DeepSeekMessage{
			{
				Role:    "user",
				Content: prompt,
			},
		},
	}

	// 序列化请求
	requestBody, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("failed to marshal request: %w", err)
	}

	// 创建HTTP请求
	url := ds.config.BaseURL + "/chat/completions"
	httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(requestBody))
	if err != nil {
		return "", fmt.Errorf("failed to create HTTP request: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+ds.config.APIKey)

	// 发送请求
	resp, err := ds.httpClient.Do(httpReq)
	if err != nil {
		return "", fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	responseBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("API request failed with status %d: %s", resp.StatusCode, string(responseBody))
	}

	// 解析响应
	var deepSeekResp DeepSeekResponse
	if err := json.Unmarshal(responseBody, &deepSeekResp); err != nil {
		return "", fmt.Errorf("failed to unmarshal response: %w", err)
	}

	// 提取内容
	if len(deepSeekResp.Choices) == 0 {
		return "", fmt.Errorf("no choices in response")
	}

	content := deepSeekResp.Choices[0].Message.Content

	ds.logger.WithFields(logrus.Fields{
		"model":         deepSeekResp.Model,
		"total_tokens":  deepSeekResp.Usage.TotalTokens,
		"finish_reason": deepSeekResp.Choices[0].FinishReason,
	}).Debug("DeepSeek API call completed")

	return content, nil
}

// parseRiskAnalysisResponse 解析风险分析响应
func (ds *DeepSeekService) parseRiskAnalysisResponse(content string) (*RiskAnalysisResponse, error) {
	// 尝试提取JSON部分
	jsonStart := strings.Index(content, "{")
	jsonEnd := strings.LastIndex(content, "}")

	if jsonStart == -1 || jsonEnd == -1 || jsonStart >= jsonEnd {
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	jsonContent := content[jsonStart : jsonEnd+1]

	// 解析JSON
	var response RiskAnalysisResponse
	if err := json.Unmarshal([]byte(jsonContent), &response); err != nil {
		// 如果解析失败，返回基础响应
		ds.logger.WithError(err).Warn("Failed to parse AI response, using fallback")
		return &RiskAnalysisResponse{
			RiskLevel:    "medium",
			Confidence:   0.5,
			Reasoning:    "AI分析解析失败，使用默认评估",
			Factors:      make([]*RiskFactor, 0),
			Mitigations:  make([]*RiskMitigation, 0),
			Alternatives: make([]*CommandAlternative, 0),
			Metadata:     map[string]interface{}{"ai_response": content},
		}, nil
	}

	return &response, nil
}

// parseRecommendationResponse 解析建议响应
func (ds *DeepSeekService) parseRecommendationResponse(content string) ([]*RiskRecommendation, error) {
	// 尝试提取JSON部分
	jsonStart := strings.Index(content, "{")
	jsonEnd := strings.LastIndex(content, "}")

	if jsonStart == -1 || jsonEnd == -1 || jsonStart >= jsonEnd {
		return nil, fmt.Errorf("no valid JSON found in response")
	}

	jsonContent := content[jsonStart : jsonEnd+1]

	// 解析JSON
	var response struct {
		Recommendations []*RiskRecommendation `json:"recommendations"`
	}

	if err := json.Unmarshal([]byte(jsonContent), &response); err != nil {
		ds.logger.WithError(err).Warn("Failed to parse recommendation response")
		return []*RiskRecommendation{}, nil
	}

	// 设置ID和创建时间
	for _, rec := range response.Recommendations {
		if rec.ID == "" {
			rec.ID = fmt.Sprintf("ai_rec_%d", time.Now().UnixNano())
		}
		rec.CreatedAt = time.Now()
	}

	return response.Recommendations, nil
}
