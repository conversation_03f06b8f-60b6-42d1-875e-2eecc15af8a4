package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"aiops-platform/internal/ai"
	"aiops-platform/internal/database"
	"aiops-platform/internal/config"
	"github.com/sirupsen/logrus"
)

// 革命性AI运维管理平台使用示例
func main() {
	// 初始化日志
	logger := logrus.New()
	logger.SetLevel(logrus.InfoLevel)
	
	fmt.Println("🚀 革命性AI运维管理平台 - 使用示例")
	fmt.Println("=" * 50)
	
	// 1. 初始化数据库
	dbConfig := config.DatabaseConfig{
		Path:            "./data/revolutionary_example.db",
		MaxOpenConns:    10,
		MaxIdleConns:    5,
		ConnMaxLifetime: time.Hour,
		ConnMaxIdleTime: 10 * time.Minute,
	}
	
	db, err := database.New(dbConfig)
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}
	
	// 执行数据库迁移
	if err := database.Migrate(db); err != nil {
		log.Fatalf("数据库迁移失败: %v", err)
	}
	
	// 2. 配置DeepSeek服务
	deepseekConfig := &ai.EnhancedDeepSeekConfig{
		APIKey:      os.Getenv("DEEPSEEK_API_KEY"), // 从环境变量获取
		BaseURL:     "https://api.deepseek.com",
		Model:       "deepseek-chat",
		MaxTokens:   4000,
		Temperature: 0.7,
		Timeout:     30 * time.Second,
		MaxRetries:  3,
	}
	
	// 如果没有API Key，使用模拟模式
	if deepseekConfig.APIKey == "" {
		fmt.Println("⚠️  未检测到DEEPSEEK_API_KEY环境变量，将使用模拟模式")
		deepseekConfig = createMockDeepSeekConfig()
	}
	
	// 3. 创建革命性集成适配器
	adapter := ai.NewRevolutionaryIntegrationAdapter(db, deepseekConfig, logger)
	
	// 4. 测试各种运维场景
	testScenarios := []TestScenario{
		{
			Name:        "查询主机列表",
			Message:     "列出所有主机",
			ExpectedIntent: ai.INTENT_QUERY,
			Description: "测试查询类操作的意图识别和SQL生成",
		},
		{
			Name:        "添加新主机",
			Message:     "添加主机 ************* root password123",
			ExpectedIntent: ai.INTENT_EXECUTE,
			Description: "测试执行类操作的意图识别和参数提取",
		},
		{
			Name:        "检查主机状态",
			Message:     "查看主机状态",
			ExpectedIntent: ai.INTENT_QUERY,
			Description: "测试监控类查询操作",
		},
		{
			Name:        "一般对话",
			Message:     "你好，请介绍一下你的功能",
			ExpectedIntent: ai.INTENT_CHAT,
			Description: "测试对话类意图处理",
		},
		{
			Name:        "复杂查询",
			Message:     "显示生产环境中状态为在线的主机信息",
			ExpectedIntent: ai.INTENT_QUERY,
			Description: "测试复杂条件查询的SQL生成",
		},
	}
	
	// 5. 执行测试场景
	fmt.Println("\n🧪 开始测试革命性AI运维能力...")
	fmt.Println("-" * 50)
	
	ctx := context.Background()
	successCount := 0
	
	for i, scenario := range testScenarios {
		fmt.Printf("\n📋 测试 %d: %s\n", i+1, scenario.Name)
		fmt.Printf("💬 用户输入: %s\n", scenario.Message)
		fmt.Printf("📝 测试目标: %s\n", scenario.Description)
		
		// 构建请求
		req := &ai.LegacyProcessMessageRequest{
			SessionID: fmt.Sprintf("test_session_%d", i+1),
			UserID:    1,
			Message:   scenario.Message,
		}
		
		// 处理消息
		start := time.Now()
		response, err := adapter.ProcessMessage(ctx, req)
		duration := time.Since(start)
		
		if err != nil {
			fmt.Printf("❌ 处理失败: %v\n", err)
			continue
		}
		
		// 验证结果
		success := validateResponse(response, scenario)
		if success {
			successCount++
			fmt.Printf("✅ 测试通过 (耗时: %v)\n", duration)
		} else {
			fmt.Printf("⚠️  测试部分通过 (耗时: %v)\n", duration)
		}
		
		// 显示响应详情
		fmt.Printf("🎯 识别意图: %s (置信度: %.2f)\n", response.Intent, response.Confidence)
		fmt.Printf("📄 AI回复: %s\n", truncateString(response.Content, 100))
		
		if len(response.Parameters) > 0 {
			fmt.Printf("🔧 提取参数: %v\n", response.Parameters)
		}
		
		time.Sleep(1 * time.Second) // 避免请求过于频繁
	}
	
	// 6. 显示测试结果
	fmt.Printf("\n📊 测试结果统计\n")
	fmt.Printf("=" * 30 + "\n")
	fmt.Printf("总测试数: %d\n", len(testScenarios))
	fmt.Printf("成功数: %d\n", successCount)
	fmt.Printf("成功率: %.1f%%\n", float64(successCount)/float64(len(testScenarios))*100)
	
	// 7. 显示引擎状态
	fmt.Printf("\n🔍 系统状态检查\n")
	fmt.Printf("-" * 20 + "\n")
	status := adapter.GetEngineStatus(ctx)
	for key, value := range status {
		fmt.Printf("%s: %v\n", key, value)
	}
	
	// 8. 显示性能指标
	fmt.Printf("\n📈 性能指标\n")
	fmt.Printf("-" * 15 + "\n")
	metrics := adapter.GetMetrics()
	for key, value := range metrics {
		fmt.Printf("%s: %v\n", key, value)
	}
	
	fmt.Println("\n🎉 革命性AI运维管理平台测试完成！")
}

// TestScenario 测试场景
type TestScenario struct {
	Name           string
	Message        string
	ExpectedIntent string
	Description    string
}

// validateResponse 验证响应
func validateResponse(response *ai.LegacyProcessMessageResponse, scenario TestScenario) bool {
	// 检查意图识别是否正确
	if response.Intent != scenario.ExpectedIntent {
		fmt.Printf("⚠️  意图识别不匹配: 期望 %s, 实际 %s\n", scenario.ExpectedIntent, response.Intent)
		return false
	}
	
	// 检查置信度是否合理
	if response.Confidence < 0.5 {
		fmt.Printf("⚠️  置信度过低: %.2f\n", response.Confidence)
		return false
	}
	
	// 检查是否有响应内容
	if response.Content == "" {
		fmt.Printf("⚠️  响应内容为空\n")
		return false
	}
	
	return true
}

// truncateString 截断字符串
func truncateString(s string, maxLen int) string {
	if len(s) <= maxLen {
		return s
	}
	return s[:maxLen] + "..."
}

// createMockDeepSeekConfig 创建模拟DeepSeek配置
func createMockDeepSeekConfig() *ai.EnhancedDeepSeekConfig {
	return &ai.EnhancedDeepSeekConfig{
		APIKey:      "mock_api_key",
		BaseURL:     "http://localhost:8080/mock", // 模拟服务地址
		Model:       "mock-model",
		MaxTokens:   1000,
		Temperature: 0.7,
		Timeout:     10 * time.Second,
		MaxRetries:  1,
	}
}

// 使用说明
/*
运行此示例前，请确保：

1. 设置环境变量：
   export DEEPSEEK_API_KEY="your_deepseek_api_key"

2. 安装依赖：
   go mod tidy

3. 运行示例：
   go run examples/revolutionary_usage_example.go

4. 预期输出：
   - 意图识别准确率 > 90%
   - 查询类操作能生成正确的SQL
   - 执行类操作能提取正确的参数
   - 对话类操作能生成友好的回复

5. 测试场景说明：
   - "列出所有主机" → 应识别为QUERY，生成SELECT SQL
   - "添加主机..." → 应识别为EXECUTE，提取IP/用户名/密码参数
   - "查看主机状态" → 应识别为QUERY，生成状态查询SQL
   - "你好..." → 应识别为CHAT，生成友好回复
   - 复杂查询 → 应识别为QUERY，生成带条件的SQL

这个示例展示了革命性AI运维管理平台的核心能力：
✅ 精准的意图识别（3大类型：QUERY/EXECUTE/CHAT）
✅ 智能的SQL/命令生成
✅ 真正的数据库执行能力
✅ 智能的结果渲染和展示
✅ 完整的对话式运维体验
*/
