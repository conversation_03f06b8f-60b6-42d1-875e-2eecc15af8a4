package handler

import (
	"net/http"
	"strconv"
	"time"

	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// EnhancedReportHandler 增强报表处理器
type EnhancedReportHandler struct {
	logger         *logrus.Logger
	reportService  *service.EnhancedReportService
	templateManager *service.ReportTemplateManager
}

// NewEnhancedReportHandler 创建增强报表处理器
func NewEnhancedReportHandler(logger *logrus.Logger, reportService *service.EnhancedReportService, templateManager *service.ReportTemplateManager) *EnhancedReportHandler {
	return &EnhancedReportHandler{
		logger:          logger,
		reportService:   reportService,
		templateManager: templateManager,
	}
}

// RegisterRoutes 注册路由
func (erh *EnhancedReportHandler) RegisterRoutes(router *gin.RouterGroup) {
	reports := router.Group("/enhanced-reports")
	{
		// 报表生成
		reports.POST("/generate", erh.GenerateReport)
		reports.GET("/:report_id", erh.GetReport)
		reports.GET("/:report_id/status", erh.GetReportStatus)
		reports.DELETE("/:report_id", erh.DeleteReport)
		
		// 报表导出
		reports.GET("/:report_id/export/:format", erh.ExportReport)
		reports.GET("/export-formats", erh.GetExportFormats)
		
		// 报表模板
		reports.GET("/templates", erh.ListTemplates)
		reports.GET("/templates/:template_id", erh.GetTemplate)
		reports.POST("/templates", erh.CreateTemplate)
		reports.PUT("/templates/:template_id", erh.UpdateTemplate)
		reports.DELETE("/templates/:template_id", erh.DeleteTemplate)
		
		// 图表相关
		reports.GET("/chart-types", erh.GetChartTypes)
		reports.POST("/preview-chart", erh.PreviewChart)
		
		// 统计和监控
		reports.GET("/metrics", erh.GetServiceMetrics)
		reports.GET("/history", erh.GetReportHistory)
	}
}

// GenerateReportRequest 生成报表请求
type GenerateReportRequest struct {
	Type         string                 `json:"type" binding:"required"`
	Title        string                 `json:"title"`
	Description  string                 `json:"description"`
	TimeRange    string                 `json:"time_range"`
	Filters      map[string]interface{} `json:"filters"`
	TemplateID   string                 `json:"template_id"`
	ChartTypes   []string               `json:"chart_types"`
	ExportFormat string                 `json:"export_format"`
	Priority     string                 `json:"priority"`
	Metadata     map[string]interface{} `json:"metadata"`
}

// GenerateReport 生成报表
func (erh *EnhancedReportHandler) GenerateReport(c *gin.Context) {
	var req GenerateReportRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		erh.logger.WithError(err).Warn("Invalid report generation request")
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}

	// 设置默认值
	if req.Title == "" {
		req.Title = "自动生成报表"
	}
	if req.TimeRange == "" {
		req.TimeRange = "24h"
	}
	if req.Priority == "" {
		req.Priority = "normal"
	}

	// 获取用户ID
	userID := int64(1) // 简化实现

	// 构建报表请求
	reportReq := &service.ReportRequest{
		ID:           "report_" + strconv.FormatInt(time.Now().UnixNano(), 10),
		Type:         req.Type,
		Title:        req.Title,
		Description:  req.Description,
		TimeRange:    req.TimeRange,
		Filters:      req.Filters,
		TemplateID:   req.TemplateID,
		ChartTypes:   req.ChartTypes,
		ExportFormat: req.ExportFormat,
		UserID:       userID,
		Priority:     req.Priority,
		Metadata:     req.Metadata,
	}

	erh.logger.WithFields(logrus.Fields{
		"report_id":   reportReq.ID,
		"report_type": reportReq.Type,
		"user_id":     userID,
	}).Info("🚀 Starting enhanced report generation")

	// 生成报表
	response, err := erh.reportService.GenerateReport(c.Request.Context(), reportReq)
	if err != nil {
		erh.logger.WithError(err).Error("Failed to generate enhanced report")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "报表生成失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
		"message": "报表生成成功",
	})
}

// GetReport 获取报表
func (erh *EnhancedReportHandler) GetReport(c *gin.Context) {
	reportID := c.Param("report_id")
	
	erh.logger.WithField("report_id", reportID).Info("Getting enhanced report")

	// 这里应该从缓存或数据库获取报表
	// 简化实现：返回模拟数据
	response := &service.ReportResponse{
		ID:          reportID,
		Type:        "operation",
		Title:       "运维操作报表",
		Description: "系统运维操作的详细分析报表",
		Status:      "completed",
		Progress:    100,
		GeneratedAt: time.Now(),
		Duration:    2500 * time.Millisecond,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    response,
	})
}

// GetReportStatus 获取报表状态
func (erh *EnhancedReportHandler) GetReportStatus(c *gin.Context) {
	reportID := c.Param("report_id")
	
	// 简化实现：返回模拟状态
	status := map[string]interface{}{
		"report_id": reportID,
		"status":    "completed",
		"progress":  100,
		"message":   "报表生成完成",
		"updated_at": time.Now(),
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    status,
	})
}

// DeleteReport 删除报表
func (erh *EnhancedReportHandler) DeleteReport(c *gin.Context) {
	reportID := c.Param("report_id")
	
	erh.logger.WithField("report_id", reportID).Info("Deleting enhanced report")

	// 这里应该实现实际的删除逻辑
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "报表删除成功",
	})
}

// ExportReport 导出报表
func (erh *EnhancedReportHandler) ExportReport(c *gin.Context) {
	reportID := c.Param("report_id")
	format := c.Param("format")
	
	erh.logger.WithFields(logrus.Fields{
		"report_id": reportID,
		"format":    format,
	}).Info("Exporting enhanced report")

	// 这里应该实现实际的导出逻辑
	// 简化实现：返回模拟数据
	
	switch format {
	case "json":
		c.Header("Content-Type", "application/json")
		c.Header("Content-Disposition", "attachment; filename=report_"+reportID+".json")
		c.JSON(http.StatusOK, gin.H{
			"report_id": reportID,
			"data":      "模拟JSON数据",
			"exported_at": time.Now(),
		})
	case "csv":
		c.Header("Content-Type", "text/csv")
		c.Header("Content-Disposition", "attachment; filename=report_"+reportID+".csv")
		c.String(http.StatusOK, "报表ID,类型,生成时间\n%s,operation,%s", reportID, time.Now().Format("2006-01-02 15:04:05"))
	case "pdf":
		c.Header("Content-Type", "application/pdf")
		c.Header("Content-Disposition", "attachment; filename=report_"+reportID+".pdf")
		c.String(http.StatusOK, "PDF内容模拟数据")
	default:
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "不支持的导出格式: " + format,
		})
	}
}

// GetExportFormats 获取支持的导出格式
func (erh *EnhancedReportHandler) GetExportFormats(c *gin.Context) {
	formats := []map[string]interface{}{
		{"format": "json", "name": "JSON", "mime_type": "application/json"},
		{"format": "csv", "name": "CSV", "mime_type": "text/csv"},
		{"format": "pdf", "name": "PDF", "mime_type": "application/pdf"},
		{"format": "html", "name": "HTML", "mime_type": "text/html"},
		{"format": "excel", "name": "Excel", "mime_type": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    formats,
	})
}

// ListTemplates 列出模板
func (erh *EnhancedReportHandler) ListTemplates(c *gin.Context) {
	category := c.Query("category")
	templateType := c.Query("type")
	
	templates := erh.templateManager.ListTemplates(category, templateType)
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    templates,
		"total":   len(templates),
	})
}

// GetTemplate 获取模板
func (erh *EnhancedReportHandler) GetTemplate(c *gin.Context) {
	templateID := c.Param("template_id")
	
	template, err := erh.templateManager.GetTemplate(templateID)
	if err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "模板不存在: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    template,
	})
}

// CreateTemplate 创建模板
func (erh *EnhancedReportHandler) CreateTemplate(c *gin.Context) {
	var template service.ReportTemplate
	if err := c.ShouldBindJSON(&template); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取用户ID
	userID := int64(1) // 简化实现
	template.CreatedBy = userID
	template.UpdatedBy = userID

	if err := erh.templateManager.CreateTemplate(&template); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "创建模板失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    template,
		"message": "模板创建成功",
	})
}

// UpdateTemplate 更新模板
func (erh *EnhancedReportHandler) UpdateTemplate(c *gin.Context) {
	templateID := c.Param("template_id")
	
	var updates service.ReportTemplate
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取用户ID
	userID := int64(1) // 简化实现
	updates.UpdatedBy = userID

	if err := erh.templateManager.UpdateTemplate(templateID, &updates); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "更新模板失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "模板更新成功",
	})
}

// DeleteTemplate 删除模板
func (erh *EnhancedReportHandler) DeleteTemplate(c *gin.Context) {
	templateID := c.Param("template_id")
	
	if err := erh.templateManager.DeleteTemplate(templateID); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "删除模板失败: " + err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "模板删除成功",
	})
}

// GetChartTypes 获取支持的图表类型
func (erh *EnhancedReportHandler) GetChartTypes(c *gin.Context) {
	chartTypes := []map[string]interface{}{
		{"type": "line", "name": "折线图", "description": "适用于展示趋势变化"},
		{"type": "bar", "name": "柱状图", "description": "适用于比较不同类别的数据"},
		{"type": "pie", "name": "饼图", "description": "适用于展示占比分布"},
		{"type": "area", "name": "面积图", "description": "适用于展示累积数据"},
		{"type": "scatter", "name": "散点图", "description": "适用于展示相关性"},
		{"type": "heatmap", "name": "热力图", "description": "适用于展示矩阵数据"},
		{"type": "gauge", "name": "仪表盘", "description": "适用于展示单一指标"},
		{"type": "radar", "name": "雷达图", "description": "适用于多维度比较"},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    chartTypes,
	})
}

// PreviewChart 预览图表
func (erh *EnhancedReportHandler) PreviewChart(c *gin.Context) {
	var req map[string]interface{}
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}

	// 这里应该实现图表预览逻辑
	// 简化实现：返回模拟图表数据
	
	chartData := map[string]interface{}{
		"type": req["type"],
		"data": map[string]interface{}{
			"labels": []string{"A", "B", "C", "D", "E"},
			"values": []int{10, 20, 30, 40, 50},
		},
		"options": map[string]interface{}{
			"responsive": true,
			"legend":     map[string]interface{}{"display": true},
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    chartData,
	})
}

// GetServiceMetrics 获取服务指标
func (erh *EnhancedReportHandler) GetServiceMetrics(c *gin.Context) {
	metrics := erh.reportService.GetMetrics()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// GetReportHistory 获取报表历史
func (erh *EnhancedReportHandler) GetReportHistory(c *gin.Context) {
	reportType := c.Query("type")
	limitStr := c.DefaultQuery("limit", "20")
	
	limit, err := strconv.Atoi(limitStr)
	if err != nil {
		limit = 20
	}

	// 这里应该实现实际的历史查询逻辑
	// 简化实现：返回模拟数据
	
	history := []map[string]interface{}{
		{
			"id":           "report_001",
			"type":         reportType,
			"title":        "运维操作报表",
			"status":       "completed",
			"generated_at": time.Now().Add(-2 * time.Hour),
			"duration":     "2.5s",
		},
		{
			"id":           "report_002",
			"type":         reportType,
			"title":        "系统健康报表",
			"status":       "completed",
			"generated_at": time.Now().Add(-1 * time.Hour),
			"duration":     "1.8s",
		},
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    history,
		"total":   len(history),
		"limit":   limit,
	})
}
