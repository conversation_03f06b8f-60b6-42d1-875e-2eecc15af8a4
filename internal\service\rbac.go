package service

import (
	"context"
	"fmt"
	"strings"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// RBACService RBAC服务接口
type RBACService interface {
	// 角色管理
	CreateRole(ctx context.Context, req *model.RoleRequest, creatorID int64) (*model.RoleResponse, error)
	GetRole(ctx context.Context, roleID int64) (*model.RoleResponse, error)
	GetRoleByName(ctx context.Context, name string) (*model.RoleResponse, error)
	ListRoles(ctx context.Context, req *model.PaginationQuery) (*model.RoleListResponse, error)
	UpdateRole(ctx context.Context, roleID int64, req *model.RoleRequest, updaterID int64) (*model.RoleResponse, error)
	DeleteRole(ctx context.Context, roleID int64, deleterID int64) error

	// 权限管理
	CreatePermission(ctx context.Context, req *model.PermissionRequest, creatorID int64) (*model.PermissionResponse, error)
	GetPermission(ctx context.Context, permissionID int64) (*model.PermissionResponse, error)
	ListPermissions(ctx context.Context, req *model.PaginationQuery) (*model.PermissionListResponse, error)
	UpdatePermission(ctx context.Context, permissionID int64, req *model.PermissionRequest, updaterID int64) (*model.PermissionResponse, error)
	DeletePermission(ctx context.Context, permissionID int64, deleterID int64) error

	// 角色权限管理
	AssignPermissionToRole(ctx context.Context, req *model.AssignPermissionRequest, assignerID int64) error
	RevokePermissionFromRole(ctx context.Context, roleID, permissionID int64, revokerID int64) error
	GetRolePermissions(ctx context.Context, roleID int64) ([]*model.PermissionResponse, error)

	// 用户角色管理
	AssignRoleToUser(ctx context.Context, req *model.AssignRoleRequest, assignerID int64) error
	RevokeRoleFromUser(ctx context.Context, userID, roleID int64, revokerID int64) error
	GetUserRoles(ctx context.Context, userID int64) ([]*model.RoleResponse, error)
	GetUserPermissions(ctx context.Context, userID int64) (*model.UserPermissionResponse, error)

	// 权限检查
	CheckPermission(ctx context.Context, req *model.PermissionCheckRequest) (*model.PermissionCheckResponse, error)
	HasPermission(ctx context.Context, userID int64, resource, action string) (bool, error)
	HasAnyPermission(ctx context.Context, userID int64, permissions []string) (bool, error)

	// 初始化系统数据
	InitializeSystemData(ctx context.Context) error
}

// rbacService RBAC服务实现
type rbacService struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewRBACService 创建RBAC服务
func NewRBACService(db *gorm.DB, logger *logrus.Logger) RBACService {
	service := &rbacService{
		db:     db,
		logger: logger,
	}

	// 自动迁移数据库表
	if err := service.migrate(); err != nil {
		logger.WithError(err).Error("Failed to migrate RBAC tables")
	}

	return service
}

// migrate 迁移数据库表
func (s *rbacService) migrate() error {
	return s.db.AutoMigrate(
		&model.Role{},
		&model.Permission{},
		&model.UserRole{},
		&model.RolePermission{},
		&model.ResourcePolicy{},
	)
}

// CreateRole 创建角色
func (s *rbacService) CreateRole(ctx context.Context, req *model.RoleRequest, creatorID int64) (*model.RoleResponse, error) {
	// 检查角色名是否已存在
	var existingRole model.Role
	if err := s.db.WithContext(ctx).Where("name = ?", req.Name).First(&existingRole).Error; err == nil {
		return nil, fmt.Errorf("role with name '%s' already exists", req.Name)
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing role: %w", err)
	}

	// 创建角色
	role := &model.Role{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		IsSystem:    false,
		IsActive:    true,
	}

	if req.IsActive != nil {
		role.IsActive = *req.IsActive
	}

	if err := s.db.WithContext(ctx).Create(role).Error; err != nil {
		return nil, fmt.Errorf("failed to create role: %w", err)
	}

	return s.roleToResponse(role), nil
}

// GetRole 获取角色
func (s *rbacService) GetRole(ctx context.Context, roleID int64) (*model.RoleResponse, error) {
	var role model.Role
	if err := s.db.WithContext(ctx).Preload("Permissions").First(&role, roleID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("role not found")
		}
		return nil, fmt.Errorf("failed to get role: %w", err)
	}

	response := s.roleToResponse(&role)

	// 获取权限列表
	permissions := make([]*model.PermissionResponse, len(role.Permissions))
	for i, perm := range role.Permissions {
		permissions[i] = s.permissionToResponse(&perm)
	}
	response.Permissions = permissions

	// 获取用户数量
	var userCount int64
	s.db.WithContext(ctx).Model(&model.UserRole{}).Where("role_id = ? AND is_active = ?", roleID, true).Count(&userCount)
	response.UserCount = userCount

	return response, nil
}

// GetRoleByName 根据名称获取角色
func (s *rbacService) GetRoleByName(ctx context.Context, name string) (*model.RoleResponse, error) {
	var role model.Role
	if err := s.db.WithContext(ctx).Where("name = ?", name).First(&role).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("role not found")
		}
		return nil, fmt.Errorf("failed to get role: %w", err)
	}

	return s.roleToResponse(&role), nil
}

// ListRoles 获取角色列表
func (s *rbacService) ListRoles(ctx context.Context, req *model.PaginationQuery) (*model.RoleListResponse, error) {
	req.DefaultPagination()

	var roles []model.Role
	var total int64

	// 获取总数
	if err := s.db.WithContext(ctx).Model(&model.Role{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count roles: %w", err)
	}

	// 获取角色列表
	if err := s.db.WithContext(ctx).
		Offset(req.Offset()).
		Limit(req.PageSize).
		Order("created_at DESC").
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to list roles: %w", err)
	}

	// 转换为响应格式
	roleResponses := make([]*model.RoleResponse, len(roles))
	for i, role := range roles {
		roleResponses[i] = s.roleToResponse(&role)
	}

	return &model.RoleListResponse{
		Roles:      roleResponses,
		Pagination: model.NewPagination(total, req.Page, req.PageSize),
	}, nil
}

// UpdateRole 更新角色
func (s *rbacService) UpdateRole(ctx context.Context, roleID int64, req *model.RoleRequest, updaterID int64) (*model.RoleResponse, error) {
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, roleID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("role not found")
		}
		return nil, fmt.Errorf("failed to get role: %w", err)
	}

	// 检查是否为系统角色
	if role.IsSystem {
		return nil, fmt.Errorf("cannot update system role")
	}

	// 检查角色名是否已被其他角色使用
	if req.Name != role.Name {
		var existingRole model.Role
		if err := s.db.WithContext(ctx).Where("name = ? AND id != ?", req.Name, roleID).First(&existingRole).Error; err == nil {
			return nil, fmt.Errorf("role with name '%s' already exists", req.Name)
		} else if err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("failed to check existing role: %w", err)
		}
	}

	// 更新角色信息
	role.Name = req.Name
	role.DisplayName = req.DisplayName
	role.Description = req.Description
	if req.IsActive != nil {
		role.IsActive = *req.IsActive
	}

	if err := s.db.WithContext(ctx).Save(&role).Error; err != nil {
		return nil, fmt.Errorf("failed to update role: %w", err)
	}

	return s.roleToResponse(&role), nil
}

// DeleteRole 删除角色
func (s *rbacService) DeleteRole(ctx context.Context, roleID int64, deleterID int64) error {
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, roleID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("role not found")
		}
		return fmt.Errorf("failed to get role: %w", err)
	}

	// 检查是否为系统角色
	if role.IsSystem {
		return fmt.Errorf("cannot delete system role")
	}

	// 检查是否有用户使用该角色
	var userCount int64
	if err := s.db.WithContext(ctx).Model(&model.UserRole{}).Where("role_id = ? AND is_active = ?", roleID, true).Count(&userCount).Error; err != nil {
		return fmt.Errorf("failed to check role usage: %w", err)
	}

	if userCount > 0 {
		return fmt.Errorf("cannot delete role that is assigned to users")
	}

	// 删除角色及其关联关系
	return s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// 删除角色权限关联
		if err := tx.Where("role_id = ?", roleID).Delete(&model.RolePermission{}).Error; err != nil {
			return fmt.Errorf("failed to delete role permissions: %w", err)
		}

		// 删除角色
		if err := tx.Delete(&role).Error; err != nil {
			return fmt.Errorf("failed to delete role: %w", err)
		}

		return nil
	})
}

// roleToResponse 转换角色为响应格式
func (s *rbacService) roleToResponse(role *model.Role) *model.RoleResponse {
	return &model.RoleResponse{
		ID:          role.ID,
		Name:        role.Name,
		DisplayName: role.DisplayName,
		Description: role.Description,
		IsSystem:    role.IsSystem,
		IsActive:    role.IsActive,
		CreatedAt:   role.CreatedAt.Unix(),
		UpdatedAt:   role.UpdatedAt.Unix(),
	}
}

// permissionToResponse 转换权限为响应格式
func (s *rbacService) permissionToResponse(permission *model.Permission) *model.PermissionResponse {
	return &model.PermissionResponse{
		ID:          permission.ID,
		Name:        permission.Name,
		DisplayName: permission.DisplayName,
		Description: permission.Description,
		Resource:    permission.Resource,
		Action:      permission.Action,
		Scope:       permission.Scope,
		IsSystem:    permission.IsSystem,
		CreatedAt:   permission.CreatedAt.Unix(),
		UpdatedAt:   permission.UpdatedAt.Unix(),
	}
}

// CreatePermission 创建权限
func (s *rbacService) CreatePermission(ctx context.Context, req *model.PermissionRequest, creatorID int64) (*model.PermissionResponse, error) {
	// 检查权限名是否已存在
	var existingPerm model.Permission
	if err := s.db.WithContext(ctx).Where("name = ?", req.Name).First(&existingPerm).Error; err == nil {
		return nil, fmt.Errorf("permission with name '%s' already exists", req.Name)
	} else if err != gorm.ErrRecordNotFound {
		return nil, fmt.Errorf("failed to check existing permission: %w", err)
	}

	// 设置默认范围
	scope := req.Scope
	if scope == "" {
		scope = model.ScopeAll
	}

	// 创建权限
	permission := &model.Permission{
		Name:        req.Name,
		DisplayName: req.DisplayName,
		Description: req.Description,
		Resource:    req.Resource,
		Action:      req.Action,
		Scope:       scope,
		IsSystem:    false,
	}

	if err := s.db.WithContext(ctx).Create(permission).Error; err != nil {
		return nil, fmt.Errorf("failed to create permission: %w", err)
	}

	return s.permissionToResponse(permission), nil
}

// GetPermission 获取权限
func (s *rbacService) GetPermission(ctx context.Context, permissionID int64) (*model.PermissionResponse, error) {
	var permission model.Permission
	if err := s.db.WithContext(ctx).First(&permission, permissionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("permission not found")
		}
		return nil, fmt.Errorf("failed to get permission: %w", err)
	}

	return s.permissionToResponse(&permission), nil
}

// ListPermissions 获取权限列表
func (s *rbacService) ListPermissions(ctx context.Context, req *model.PaginationQuery) (*model.PermissionListResponse, error) {
	req.DefaultPagination()

	var permissions []model.Permission
	var total int64

	// 获取总数
	if err := s.db.WithContext(ctx).Model(&model.Permission{}).Count(&total).Error; err != nil {
		return nil, fmt.Errorf("failed to count permissions: %w", err)
	}

	// 获取权限列表
	if err := s.db.WithContext(ctx).
		Offset(req.Offset()).
		Limit(req.PageSize).
		Order("resource, action, scope").
		Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to list permissions: %w", err)
	}

	// 转换为响应格式
	permissionResponses := make([]*model.PermissionResponse, len(permissions))
	for i, permission := range permissions {
		permissionResponses[i] = s.permissionToResponse(&permission)
	}

	return &model.PermissionListResponse{
		Permissions: permissionResponses,
		Pagination:  model.NewPagination(total, req.Page, req.PageSize),
	}, nil
}

// UpdatePermission 更新权限
func (s *rbacService) UpdatePermission(ctx context.Context, permissionID int64, req *model.PermissionRequest, updaterID int64) (*model.PermissionResponse, error) {
	var permission model.Permission
	if err := s.db.WithContext(ctx).First(&permission, permissionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("permission not found")
		}
		return nil, fmt.Errorf("failed to get permission: %w", err)
	}

	// 检查是否为系统权限
	if permission.IsSystem {
		return nil, fmt.Errorf("cannot update system permission")
	}

	// 检查权限名是否已被其他权限使用
	if req.Name != permission.Name {
		var existingPerm model.Permission
		if err := s.db.WithContext(ctx).Where("name = ? AND id != ?", req.Name, permissionID).First(&existingPerm).Error; err == nil {
			return nil, fmt.Errorf("permission with name '%s' already exists", req.Name)
		} else if err != gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("failed to check existing permission: %w", err)
		}
	}

	// 设置默认范围
	scope := req.Scope
	if scope == "" {
		scope = model.ScopeAll
	}

	// 更新权限信息
	permission.Name = req.Name
	permission.DisplayName = req.DisplayName
	permission.Description = req.Description
	permission.Resource = req.Resource
	permission.Action = req.Action
	permission.Scope = scope

	if err := s.db.WithContext(ctx).Save(&permission).Error; err != nil {
		return nil, fmt.Errorf("failed to update permission: %w", err)
	}

	return s.permissionToResponse(&permission), nil
}

// DeletePermission 删除权限
func (s *rbacService) DeletePermission(ctx context.Context, permissionID int64, deleterID int64) error {
	var permission model.Permission
	if err := s.db.WithContext(ctx).First(&permission, permissionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("permission not found")
		}
		return fmt.Errorf("failed to get permission: %w", err)
	}

	// 检查是否为系统权限
	if permission.IsSystem {
		return fmt.Errorf("cannot delete system permission")
	}

	// 检查是否有角色使用该权限
	var roleCount int64
	if err := s.db.WithContext(ctx).Model(&model.RolePermission{}).Where("permission_id = ?", permissionID).Count(&roleCount).Error; err != nil {
		return fmt.Errorf("failed to check permission usage: %w", err)
	}

	if roleCount > 0 {
		return fmt.Errorf("cannot delete permission that is assigned to roles")
	}

	// 删除权限
	if err := s.db.WithContext(ctx).Delete(&permission).Error; err != nil {
		return fmt.Errorf("failed to delete permission: %w", err)
	}

	return nil
}

// CheckPermission 检查权限
func (s *rbacService) CheckPermission(ctx context.Context, req *model.PermissionCheckRequest) (*model.PermissionCheckResponse, error) {
	// 获取用户的所有有效权限
	userPermissions, err := s.GetUserPermissions(ctx, req.UserID)
	if err != nil {
		return &model.PermissionCheckResponse{
			Allowed: false,
			Reason:  "Failed to get user permissions",
		}, nil
	}

	// 构建权限标识符
	permissionKey := fmt.Sprintf("%s:%s", req.Resource, req.Action)
	if req.Scope != "" {
		permissionKey = fmt.Sprintf("%s:%s:%s", req.Resource, req.Action, req.Scope)
	}

	// 检查是否有匹配的权限
	for _, perm := range userPermissions.EffectivePermissions {
		if s.matchPermission(perm, permissionKey) {
			return &model.PermissionCheckResponse{
				Allowed:     true,
				Permissions: []string{perm},
			}, nil
		}
	}

	return &model.PermissionCheckResponse{
		Allowed: false,
		Reason:  "Permission denied",
	}, nil
}

// HasPermission 检查用户是否有指定权限
func (s *rbacService) HasPermission(ctx context.Context, userID int64, resource, action string) (bool, error) {
	req := &model.PermissionCheckRequest{
		UserID:   userID,
		Resource: resource,
		Action:   action,
	}

	response, err := s.CheckPermission(ctx, req)
	if err != nil {
		return false, err
	}

	return response.Allowed, nil
}

// HasAnyPermission 检查用户是否有任意一个权限
func (s *rbacService) HasAnyPermission(ctx context.Context, userID int64, permissions []string) (bool, error) {
	userPermissions, err := s.GetUserPermissions(ctx, userID)
	if err != nil {
		return false, err
	}

	for _, requiredPerm := range permissions {
		for _, userPerm := range userPermissions.EffectivePermissions {
			if s.matchPermission(userPerm, requiredPerm) {
				return true, nil
			}
		}
	}

	return false, nil
}

// matchPermission 匹配权限
func (s *rbacService) matchPermission(userPerm, requiredPerm string) bool {
	// 精确匹配
	if userPerm == requiredPerm {
		return true
	}

	// 通配符匹配（如果用户权限包含*）
	if strings.Contains(userPerm, "*") {
		userParts := strings.Split(userPerm, ":")
		requiredParts := strings.Split(requiredPerm, ":")

		if len(userParts) != len(requiredParts) {
			return false
		}

		for i, userPart := range userParts {
			if userPart != "*" && userPart != requiredParts[i] {
				return false
			}
		}
		return true
	}

	return false
}

// AssignPermissionToRole 为角色分配权限
func (s *rbacService) AssignPermissionToRole(ctx context.Context, req *model.AssignPermissionRequest, assignerID int64) error {
	// 检查角色是否存在
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, req.RoleID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("role not found")
		}
		return fmt.Errorf("failed to get role: %w", err)
	}

	// 检查权限是否存在
	var permission model.Permission
	if err := s.db.WithContext(ctx).First(&permission, req.PermissionID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("permission not found")
		}
		return fmt.Errorf("failed to get permission: %w", err)
	}

	// 检查是否已经分配
	var existing model.RolePermission
	if err := s.db.WithContext(ctx).Where("role_id = ? AND permission_id = ?", req.RoleID, req.PermissionID).First(&existing).Error; err == nil {
		return fmt.Errorf("permission already assigned to role")
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing assignment: %w", err)
	}

	// 创建角色权限关联
	rolePermission := &model.RolePermission{
		RoleID:       req.RoleID,
		PermissionID: req.PermissionID,
		GrantedBy:    assignerID,
		GrantedAt:    time.Now(),
	}

	if err := s.db.WithContext(ctx).Create(rolePermission).Error; err != nil {
		return fmt.Errorf("failed to assign permission to role: %w", err)
	}

	return nil
}

// RevokePermissionFromRole 从角色撤销权限
func (s *rbacService) RevokePermissionFromRole(ctx context.Context, roleID, permissionID int64, revokerID int64) error {
	// 删除角色权限关联
	result := s.db.WithContext(ctx).Where("role_id = ? AND permission_id = ?", roleID, permissionID).Delete(&model.RolePermission{})
	if result.Error != nil {
		return fmt.Errorf("failed to revoke permission from role: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("permission not assigned to role")
	}

	return nil
}

// GetRolePermissions 获取角色的权限列表
func (s *rbacService) GetRolePermissions(ctx context.Context, roleID int64) ([]*model.PermissionResponse, error) {
	var permissions []model.Permission
	if err := s.db.WithContext(ctx).
		Joins("JOIN role_permissions ON permissions.id = role_permissions.permission_id").
		Where("role_permissions.role_id = ?", roleID).
		Find(&permissions).Error; err != nil {
		return nil, fmt.Errorf("failed to get role permissions: %w", err)
	}

	responses := make([]*model.PermissionResponse, len(permissions))
	for i, permission := range permissions {
		responses[i] = s.permissionToResponse(&permission)
	}

	return responses, nil
}

// AssignRoleToUser 为用户分配角色
func (s *rbacService) AssignRoleToUser(ctx context.Context, req *model.AssignRoleRequest, assignerID int64) error {
	// 检查用户是否存在
	var user model.User
	if err := s.db.WithContext(ctx).First(&user, req.UserID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("user not found")
		}
		return fmt.Errorf("failed to get user: %w", err)
	}

	// 检查角色是否存在
	var role model.Role
	if err := s.db.WithContext(ctx).First(&role, req.RoleID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("role not found")
		}
		return fmt.Errorf("failed to get role: %w", err)
	}

	// 检查是否已经分配且仍然有效
	var existing model.UserRole
	if err := s.db.WithContext(ctx).Where("user_id = ? AND role_id = ? AND is_active = ?", req.UserID, req.RoleID, true).First(&existing).Error; err == nil {
		return fmt.Errorf("role already assigned to user")
	} else if err != gorm.ErrRecordNotFound {
		return fmt.Errorf("failed to check existing assignment: %w", err)
	}

	// 创建用户角色关联
	userRole := &model.UserRole{
		UserID:    req.UserID,
		RoleID:    req.RoleID,
		GrantedBy: assignerID,
		GrantedAt: time.Now(),
		ExpiresAt: req.ExpiresAt,
		IsActive:  true,
	}

	if err := s.db.WithContext(ctx).Create(userRole).Error; err != nil {
		return fmt.Errorf("failed to assign role to user: %w", err)
	}

	return nil
}

// RevokeRoleFromUser 从用户撤销角色
func (s *rbacService) RevokeRoleFromUser(ctx context.Context, userID, roleID int64, revokerID int64) error {
	// 更新用户角色关联为非活跃状态
	result := s.db.WithContext(ctx).
		Model(&model.UserRole{}).
		Where("user_id = ? AND role_id = ? AND is_active = ?", userID, roleID, true).
		Update("is_active", false)

	if result.Error != nil {
		return fmt.Errorf("failed to revoke role from user: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("role not assigned to user")
	}

	return nil
}

// GetUserRoles 获取用户的角色列表
func (s *rbacService) GetUserRoles(ctx context.Context, userID int64) ([]*model.RoleResponse, error) {
	var roles []model.Role
	if err := s.db.WithContext(ctx).
		Joins("JOIN user_roles ON roles.id = user_roles.role_id").
		Where("user_roles.user_id = ? AND user_roles.is_active = ? AND (user_roles.expires_at IS NULL OR user_roles.expires_at > ?)",
			userID, true, time.Now()).
		Find(&roles).Error; err != nil {
		return nil, fmt.Errorf("failed to get user roles: %w", err)
	}

	responses := make([]*model.RoleResponse, len(roles))
	for i, role := range roles {
		responses[i] = s.roleToResponse(&role)
	}

	return responses, nil
}

// GetUserPermissions 获取用户的所有权限
func (s *rbacService) GetUserPermissions(ctx context.Context, userID int64) (*model.UserPermissionResponse, error) {
	// 获取用户角色
	roles, err := s.GetUserRoles(ctx, userID)
	if err != nil {
		return nil, err
	}

	// 获取所有权限
	var effectivePermissions []string

	for _, role := range roles {
		permissions, err := s.GetRolePermissions(ctx, role.ID)
		if err != nil {
			continue // 忽略错误，继续处理其他角色
		}

		for _, perm := range permissions {
			// 构建权限标识符
			permKey := fmt.Sprintf("%s:%s:%s", perm.Resource, perm.Action, perm.Scope)
			effectivePermissions = append(effectivePermissions, permKey)
		}
	}

	// 去重
	effectivePermissions = removeDuplicates(effectivePermissions)

	return &model.UserPermissionResponse{
		UserID:               userID,
		Roles:                roles,
		EffectivePermissions: effectivePermissions,
	}, nil
}

// InitializeSystemData 初始化系统数据
func (s *rbacService) InitializeSystemData(ctx context.Context) error {
	// TODO: 实现系统角色和权限的初始化
	return nil
}

// removeDuplicates 去除重复的字符串
func removeDuplicates(slice []string) []string {
	keys := make(map[string]bool)
	var result []string

	for _, item := range slice {
		if !keys[item] {
			keys[item] = true
			result = append(result, item)
		}
	}

	return result
}
