{"app": {"title": "AI Operations Assistant", "subtitle": "Manage your IT infrastructure through natural language conversations", "description": "Intelligent Conversational Management Platform"}, "nav": {"brand": "AI Ops Assistant", "settings": "Settings", "shortcuts": "Shortcuts", "help": "Help", "history": "Chat History", "newChat": "New Chat", "user": "Administrator", "logout": "Logout"}, "chat": {"welcome": {"title": "Hello! I am your AI Operations Assistant", "description": "I can help you manage servers, monitor system status, handle alerts, and more. What can I help you with?"}, "input": {"placeholder": "Enter your question, e.g.: check host status, view alerts...", "send": "Send", "sending": "Sending...", "typing": "AI is thinking"}, "suggestions": {"label": "Suggestions:", "hostStatus": "Check Host Status", "checkAlerts": "<PERSON>s", "performance": "Performance Stats"}, "session": {"current": "Current Chat:", "new": "New Chat", "title": "Chat Title", "messages": "messages", "updated": "Updated"}, "history": {"title": "Chat History", "empty": "No chat history", "search": "Search chats...", "delete": "Delete", "load": "Load", "close": "Close"}}, "features": {"hostManagement": {"title": "Host Management", "description": "View server status, execute commands, manage hosts", "example": "e.g.: \"Check all host status\""}, "monitoring": {"title": "Monitoring & Alerts", "description": "Real-time monitoring, alert handling, status checks", "example": "e.g.: \"Show recent alerts\""}, "reports": {"title": "Reports", "description": "Data analysis, performance statistics, trend reports", "example": "e.g.: \"Generate today's operations report\""}, "commands": {"title": "Command Execution", "description": "Remote command execution, script running, system operations", "example": "e.g.: \"Execute ps aux on web-server-01\""}}, "quickActions": {"viewHosts": "View Hosts", "checkAlerts": "<PERSON>s", "systemStatus": "System Status", "performance": "Performance"}, "settings": {"title": "Personalization Settings", "subtitle": "Customize your AI Operations Assistant experience", "close": "Close", "appearance": {"title": "Appearance", "theme": {"label": "Theme Mode", "description": "Choose light or dark theme", "light": "Light", "dark": "Dark"}, "animations": {"label": "Animations", "description": "Enable or disable interface animations"}, "particles": {"label": "Particle Effects", "description": "Show background particle animations"}, "fontSize": {"label": "Font Size", "description": "Adjust interface font size", "small": "Small", "normal": "Normal", "large": "Large", "extraLarge": "Extra Large"}, "contrast": {"label": "High Contrast", "description": "Enable high contrast mode"}}, "chat": {"title": "<PERSON><PERSON>", "autoSave": {"label": "Auto-save Chats", "description": "Automatically save chat history locally"}, "sendShortcut": {"label": "Send Shortcut", "description": "Choose shortcut key for sending messages", "enter": "Enter", "ctrlEnter": "Ctrl + Enter", "shiftEnter": "Shift + Enter"}, "voiceInput": {"label": "Voice Input", "description": "Enable voice input functionality"}}, "language": {"title": "Language Settings", "current": "Current Language", "select": "Select Language"}, "accessibility": {"title": "Accessibility", "reducedMotion": {"label": "Reduce Motion", "description": "Reduce or disable animation effects"}, "screenReader": {"label": "Screen Reader Support", "description": "Optimize for screen reader experience"}, "keyboardNav": {"label": "Keyboard Navigation", "description": "Enhanced keyboard navigation support"}}}, "shortcuts": {"title": "Keyboard Shortcuts", "close": "Close", "groups": {"chat": {"title": "Chat Operations", "send": "Send Message", "newChat": "New Chat", "history": "Chat History", "clear": "Clear Chat"}, "interface": {"title": "Interface Operations", "toggleTheme": "Toggle Theme", "openSettings": "Open Settings", "showShortcuts": "Show Shortcuts", "focusInput": "Focus Input"}, "navigation": {"title": "Navigation", "nextMessage": "Next Message", "prevMessage": "Previous Message", "scrollTop": "Scroll to Top", "scrollBottom": "Scroll to Bottom"}}}, "time": {"justNow": "Just now", "minutesAgo": "{0} minutes ago", "hoursAgo": "{0} hours ago", "daysAgo": "{0} days ago", "weeksAgo": "{0} weeks ago", "monthsAgo": "{0} months ago", "yearsAgo": "{0} years ago"}, "status": {"online": "Online", "offline": "Offline", "connecting": "Connecting", "error": "Error", "loading": "Loading", "success": "Success", "warning": "Warning", "info": "Info"}, "actions": {"save": "Save", "cancel": "Cancel", "confirm": "Confirm", "delete": "Delete", "edit": "Edit", "copy": "Copy", "share": "Share", "export": "Export", "import": "Import", "refresh": "Refresh", "retry": "Retry", "back": "Back", "next": "Next", "previous": "Previous", "finish": "Finish"}, "messages": {"loading": "Loading...", "saving": "Saving...", "saved": "Saved", "error": "An error occurred", "success": "Operation successful", "noData": "No data available", "networkError": "Network connection error", "serverError": "Server error", "unauthorized": "Unauthorized access", "forbidden": "Access forbidden", "notFound": "Resource not found", "timeout": "Request timeout", "confirmDelete": "Are you sure you want to delete?", "unsavedChanges": "You have unsaved changes", "sessionExpired": "Session expired, please login again"}, "accessibility": {"skipToMain": "Skip to main content", "skipToNav": "Skip to navigation", "openMenu": "Open menu", "closeMenu": "Close menu", "toggleTheme": "Toggle theme", "currentTheme": "Current theme: {0}", "loading": "Loading", "newMessage": "New message", "messageFrom": "Message from {0}", "sendMessage": "Send message", "voiceInput": "Voice input", "imageAlt": "Image: {0}", "buttonPressed": "But<PERSON> pressed", "menuExpanded": "Menu expanded", "menuCollapsed": "<PERSON><PERSON> collapsed"}}