package main

import (
	"crypto/aes"
	"crypto/cipher"
	"encoding/base64"
	"fmt"
)

func main() {
	// 从数据库获取的加密密码
	encryptedPassword := "jbxZw6SjcSSd+/Axop4poKZaNESRjf9V9TCCIjmhPqHWCmI4"
	
	// 配置文件中的加密密钥
	encryptionKey := "aiops-dev-encryption-key-32byte!"
	
	fmt.Printf("加密密钥长度: %d 字节\n", len(encryptionKey))
	fmt.Printf("加密密码: %s\n", encryptedPassword)
	fmt.Printf("加密密码长度: %d 字符\n", len(encryptedPassword))
	
	// 尝试解密
	decryptedPassword, err := decryptData(encryptedPassword, encryptionKey)
	if err != nil {
		fmt.Printf("❌ 解密失败: %v\n", err)
		
		// 尝试其他可能的密钥
		fmt.Println("\n尝试其他可能的密钥...")
		
		// 原始密钥（可能长度不对）
		oldKey := "aiops-dev-encryption-key-32bytes"
		fmt.Printf("尝试原始密钥 (长度: %d): %s\n", len(oldKey), oldKey)
		if decrypted, err := decryptData(encryptedPassword, oldKey); err == nil {
			fmt.Printf("✅ 使用原始密钥解密成功: %s\n", decrypted)
		} else {
			fmt.Printf("❌ 原始密钥解密失败: %v\n", err)
		}
		
		// 截断到32字节
		truncatedKey := oldKey[:32]
		fmt.Printf("尝试截断密钥 (长度: %d): %s\n", len(truncatedKey), truncatedKey)
		if decrypted, err := decryptData(encryptedPassword, truncatedKey); err == nil {
			fmt.Printf("✅ 使用截断密钥解密成功: %s\n", decrypted)
		} else {
			fmt.Printf("❌ 截断密钥解密失败: %v\n", err)
		}
		
	} else {
		fmt.Printf("✅ 解密成功: %s\n", decryptedPassword)
	}
}

// decryptData 解密数据
func decryptData(encryptedData, key string) (string, error) {
	// Base64解码
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", fmt.Errorf("base64解码失败: %w", err)
	}
	
	// 创建AES cipher
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", fmt.Errorf("创建cipher失败: %w", err)
	}
	
	// 创建GCM
	aead, err := cipher.NewGCM(block)
	if err != nil {
		return "", fmt.Errorf("创建GCM失败: %w", err)
	}
	
	nonceSize := aead.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("密文太短")
	}
	
	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := aead.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", fmt.Errorf("解密失败: %w", err)
	}
	
	return string(plaintext), nil
}
