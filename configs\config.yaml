# AI对话运维管理平台 - 统一配置文件
# 环境变量优先级：环境变量 > 配置文件 > 默认值
# 敏感信息请通过环境变量设置，如：AIOPS_DEEPSEEK_API_KEY、AIOPS_JWT_SECRET、AIOPS_ENCRYPTION_KEY

# 应用配置
app:
  name: "aiops-platform"
  env: "development"  # 环境：development/production
  debug: true
  port: 8084
  version: "3.0.0"

# 数据库配置
database:
  path: "./data/aiops.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: "1h"
  conn_max_idle_time: "10m"

# JWT配置
jwt:
  secret: "aiops-development-jwt-secret-key-2024-very-secure"  # 默认JWT密钥，可通过环境变量 AIOPS_JWT_SECRET 覆盖
  access_token_ttl: "15m"
  refresh_token_ttl: "168h"  # 7天 = 168小时
  issuer: "aiops-platform"
  max_concurrent_sessions: 5

# DeepSeek API配置
deepseek:
  api_key: "***********************************"  # 默认API密钥，可通过环境变量 AIOPS_DEEPSEEK_API_KEY 覆盖
  api_url: "https://api.deepseek.com"
  model: "deepseek-chat"
  timeout: "30s"
  max_retries: 3
  max_context_tokens: 4000
  temperature: 0.7
  top_p: 0.9

# 安全配置
security:
  encryption_key: "aiops-dev-encryption-key-32byte!"  # 默认加密密钥，可通过环境变量 AIOPS_ENCRYPTION_KEY 覆盖
  password_hash_cost: 12
  session_timeout: "24h"
  rate_limit:
    enabled: true
    global: "1000/min"
    per_user: "100/min"
    per_ip: "200/min"

# Redis配置
redis:
  enabled: false
  host: "localhost"
  port: 6379
  password: ""
  db: 0
  pool_size: 10

# SSH配置
ssh:
  timeout: "30s"
  max_connections: 10
  idle_timeout: "5m"
  health_check_interval: "1m"

# 日志配置 - 优化为减少日志噪音
log:
  level: "warn"  # 🔧 优化：改为warn级别，减少Info日志噪音
  file: "./logs/aiops.log"
  max_size: 100
  retention_days: 30
  format: "json"

# 监控配置
metrics:
  enabled: true
  port: 9090
  path: "/metrics"

# AI智能Agent配置
ai:
  enable_intelligent_mode: true
  deepseek:
    api_key: "${AIOPS_DEEPSEEK_API_KEY}"
    base_url: "https://api.deepseek.com"
    model: "deepseek-chat"
    max_tokens: 4000
    temperature: 0.7

  # 智能Agent配置
  intelligent_agent:
    enable_auto_execution: true
    confidence_threshold: 0.7
    max_concurrent_tasks: 10
    default_timeout: "5m"
    enable_fallback: true

# Agent平台配置
agent:
  enabled: true
  max_agents: 50
  health_check_interval: "30s"
  registration_ttl: "1h"
  cleanup_interval: "10m"
  max_concurrent_requests: 10
  enable_auto_registration: true

# 缓存配置
cache:
  enabled: true
  l1_size: "100MB"
  l1_ttl: "5m"
  l2_ttl: "1h"



# AI模型配置
ai_models:
  # 默认模型
  default_model: "deepseek-chat"

  # 模型列表
  models:
    - name: "deepseek-chat"
      display_name: "DeepSeek Chat"
      provider: "deepseek"
      api_key: "${AIOPS_DEEPSEEK_API_KEY}"
      api_url: "https://api.deepseek.com"
      timeout: "30s"
      max_retries: 3
      max_context_tokens: 8000
      temperature: 0.7
      top_p: 0.9
      icon: "🤖"
      description: "DeepSeek智能对话模型"
      enabled: true

    - name: "deepseek-coder"
      display_name: "DeepSeek Coder"
      provider: "deepseek"
      api_key: "${AIOPS_DEEPSEEK_API_KEY}"
      api_url: "https://api.deepseek.com"
      timeout: "30s"
      max_retries: 3
      max_context_tokens: 8000
      temperature: 0.3
      top_p: 0.9
      icon: "💻"
      description: "DeepSeek代码专用模型"
      enabled: true


