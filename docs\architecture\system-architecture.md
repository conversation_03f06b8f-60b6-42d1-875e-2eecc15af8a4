# 系统架构设计

## 📐 整体架构

AI对话运维管理平台采用轻量级单体架构，分层设计，便于开发、部署和维护。

### 架构层次

```
┌─────────────────────────────────────────────────────────────┐
│                        用户层                                │
│  运维工程师 │ 系统管理员 │ 业务用户                          │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      前端展示层                              │
│  Web浏览器 │ 对话界面 │ 管理控制台 │ 监控大屏                │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                     应用服务层                               │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │   Gin Web框架   │  │         业务服务层              │   │
│  │ ┌─────────────┐ │  │ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ │   │
│  │ │   路由层    │ │  │ │AI对话│ │主机 │ │监控 │ │统计 │ │   │
│  │ │   中间件    │ │  │ │服务 │ │管理 │ │告警 │ │报表 │ │   │
│  │ │   认证权限  │ │  │ │     │ │服务 │ │服务 │ │服务 │ │   │
│  │ └─────────────┘ │  │ └─────┘ └─────┘ └─────┘ └─────┘ │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
│  ┌─────────────────────────────────────────────────────┐   │
│  │                AI集成层                             │   │
│  │ ┌─────────┐ ┌─────────┐ ┌─────────┐ ┌─────────┐     │   │
│  │ │DeepSeek │ │意图识别 │ │指令解析 │ │响应生成 │     │   │
│  │ │API客户端│ │引擎     │ │器       │ │器       │     │   │
│  │ └─────────┘ └─────────┘ └─────────┘ └─────────┘     │   │
│  └─────────────────────────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                    数据持久层                                │
│  ┌─────────────────┐  ┌─────────────────────────────────┐   │
│  │   SQLite数据库  │  │            数据表               │   │
│  │                 │  │ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ │   │
│  │   ┌─────────┐   │  │ │用户 │ │主机 │ │告警 │ │日志 │ │   │
│  │   │ 连接池  │   │  │ │表   │ │表   │ │表   │ │表   │ │   │
│  │   │ 事务管理│   │  │ └─────┘ └─────┘ └─────┘ └─────┘ │   │
│  │   │ 查询优化│   │  │ ┌─────┐ ┌─────┐ ┌─────┐ ┌─────┐ │   │
│  │   └─────────┘   │  │ │对话 │ │权限 │ │配置 │ │会话 │ │   │
│  │                 │  │ │记录 │ │表   │ │表   │ │表   │ │   │
│  │                 │  │ └─────┘ └─────┘ └─────┘ └─────┘ │   │
│  └─────────────────┘  └─────────────────────────────────┘   │
└─────────────────────────────────────────────────────────────┘
                                │
┌─────────────────────────────────────────────────────────────┐
│                      外部系统                                │
│  DeepSeek API │ 目标主机群 │ 监控系统 │ 通知服务             │
└─────────────────────────────────────────────────────────────┘
```

## 🔧 核心组件

### 1. Web服务层
- **Gin框架**：高性能HTTP服务器
- **路由管理**：RESTful API路由
- **中间件链**：认证、权限、日志、限流
- **静态资源**：HTML模板、CSS、JavaScript

### 2. 业务服务层
- **AI对话服务**：处理用户对话，调用DeepSeek API
- **主机管理服务**：SSH连接、命令执行、状态监控
- **监控告警服务**：告警规则、通知推送、状态管理
- **统计报表服务**：数据聚合、图表生成、报告导出
- **用户管理服务**：用户认证、权限控制、会话管理

### 3. AI集成层
- **DeepSeek API客户端**：API调用、错误处理、重试机制
- **意图识别引擎**：解析用户意图、提取参数
- **指令解析器**：将自然语言转换为系统操作
- **响应生成器**：格式化AI响应、添加上下文

### 4. 数据访问层
- **GORM ORM**：数据库操作抽象
- **连接池管理**：数据库连接复用
- **事务管理**：数据一致性保证
- **查询优化**：索引优化、查询缓存

## 🔄 数据流向

### 用户对话流程
```
用户输入 → 路由层 → 认证中间件 → AI对话服务 → DeepSeek API
    ↓
响应生成 ← 数据库 ← 业务逻辑 ← 意图识别 ← API响应
    ↓
前端展示 ← WebSocket推送 ← 响应格式化
```

### 主机操作流程
```
AI指令 → 参数解析 → 权限验证 → SSH连接池 → 目标主机
    ↓
操作日志 ← 数据库 ← 结果处理 ← 命令执行 ← SSH会话
    ↓
用户反馈 ← 响应生成 ← 状态更新
```

## 🏗️ 模块设计

### 目录结构
```
aiops-platform/
├── cmd/                    # 应用入口
│   ├── server/            # 主服务器
│   └── migrate/           # 数据库迁移
├── internal/              # 内部包
│   ├── api/              # API处理器
│   ├── service/          # 业务服务
│   ├── repository/       # 数据访问
│   ├── model/            # 数据模型
│   ├── middleware/       # 中间件
│   ├── config/           # 配置管理
│   └── utils/            # 工具函数
├── pkg/                   # 公共包
│   ├── ai/               # AI集成
│   ├── ssh/              # SSH客户端
│   ├── crypto/           # 加密工具
│   └── logger/           # 日志工具
├── web/                   # 前端资源
│   ├── templates/        # HTML模板
│   ├── static/           # 静态文件
│   └── assets/           # 资源文件
├── configs/               # 配置文件
├── scripts/               # 脚本文件
├── docs/                  # 文档
└── deployments/           # 部署配置
```

### 核心接口设计

#### AI服务接口
```go
type AIService interface {
    ProcessMessage(ctx context.Context, req *ChatRequest) (*ChatResponse, error)
    ManageContext(sessionID string) (*ConversationContext, error)
    ExtractIntent(message string) (*IntentResult, error)
}
```

#### 主机管理接口
```go
type HostService interface {
    CreateHost(ctx context.Context, host *Host) error
    GetHosts(ctx context.Context, filter *HostFilter) ([]*Host, error)
    ExecuteCommand(ctx context.Context, hostID string, command string) (*CommandResult, error)
    TestConnection(ctx context.Context, hostID string) error
}
```

#### 监控服务接口
```go
type MonitorService interface {
    CreateAlert(ctx context.Context, alert *Alert) error
    GetAlerts(ctx context.Context, filter *AlertFilter) ([]*Alert, error)
    ResolveAlert(ctx context.Context, alertID string) error
    SendNotification(ctx context.Context, notification *Notification) error
}
```

## 🔐 安全边界

### 网络安全
- **HTTPS/TLS 1.3**：所有外部通信加密
- **防火墙规则**：限制端口访问
- **IP白名单**：管理员访问控制

### 应用安全
- **JWT认证**：无状态身份验证
- **RBAC权限**：基于角色的访问控制
- **输入验证**：防止注入攻击
- **输出编码**：防止XSS攻击

### 数据安全
- **字段加密**：敏感数据AES加密
- **传输加密**：端到端加密通信
- **访问审计**：完整操作日志

## 📈 性能考虑

### 缓存策略
- **内存缓存**：热点数据缓存
- **查询缓存**：数据库查询结果缓存
- **静态资源缓存**：CDN加速

### 连接管理
- **数据库连接池**：复用数据库连接
- **SSH连接池**：复用SSH连接
- **HTTP连接池**：复用HTTP连接

### 并发控制
- **工作池模式**：限制并发任务数
- **限流机制**：API调用频率控制
- **熔断器**：防止级联故障

## 🔍 监控点

### 系统指标
- **响应时间**：API响应延迟
- **吞吐量**：每秒请求数
- **错误率**：失败请求比例
- **资源使用率**：CPU、内存、磁盘

### 业务指标
- **对话成功率**：AI对话完成率
- **命令执行成功率**：SSH命令成功率
- **告警处理时间**：告警响应时间
- **用户活跃度**：用户使用频率

### 技术指标
- **数据库性能**：查询时间、连接数
- **缓存命中率**：缓存效果
- **外部API延迟**：DeepSeek API响应时间
- **连接池状态**：连接使用情况
