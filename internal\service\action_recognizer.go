package service

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
)

// ActionRecognizer 操作识别器
type ActionRecognizer struct {
	logger      *logrus.Logger
	hostService HostService
	patterns    map[string]*ActionPattern
}

// ActionPattern 操作模式定义
type ActionPattern struct {
	Name        string   `json:"name"`
	Description string   `json:"description"`
	Keywords    []string `json:"keywords"`
	Regex       string   `json:"regex"`
	ActionType  string   `json:"action_type"`
	Category    string   `json:"category"`
	RiskLevel   string   `json:"risk_level"`
	Parameters  []string `json:"parameters"`
}

// ActionRecognitionResult 操作识别结果
type ActionRecognitionResult struct {
	Actions       []RecognizedAction `json:"actions"`
	TotalCount    int                `json:"total_count"`
	HighRiskCount int                `json:"high_risk_count"`
	Suggestions   []string           `json:"suggestions"`
	ProcessedText string             `json:"processed_text"`
}

// NewActionRecognizer 创建操作识别器
func NewActionRecognizer(logger *logrus.Logger, hostService HostService) *ActionRecognizer {
	ar := &ActionRecognizer{
		logger:      logger,
		hostService: hostService,
		patterns:    make(map[string]*ActionPattern),
	}

	// 初始化内置操作模式
	ar.initializeBuiltinPatterns()
	return ar
}

// RecognizeActions 识别AI回答中的可执行操作
func (ar *ActionRecognizer) RecognizeActions(ctx context.Context, aiResponse string) (*ActionRecognitionResult, error) {
	var recognizedActions []RecognizedAction
	var suggestions []string

	// 预处理文本
	processedText := ar.preprocessText(aiResponse)

	// 遍历所有模式进行匹配
	for _, pattern := range ar.patterns {
		actions := ar.matchPattern(processedText, pattern)
		recognizedActions = append(recognizedActions, actions...)
	}

	// 去重和排序
	recognizedActions = ar.deduplicateActions(recognizedActions)

	// 计算统计信息
	highRiskCount := 0
	for _, action := range recognizedActions {
		if action.RiskLevel == "high" || action.RiskLevel == "critical" {
			highRiskCount++
		}
	}

	// 生成建议
	if len(recognizedActions) > 0 {
		suggestions = append(suggestions, "检测到可执行操作，请仔细确认后再执行")
		if highRiskCount > 0 {
			suggestions = append(suggestions, fmt.Sprintf("发现%d个高风险操作，建议谨慎执行", highRiskCount))
		}
	}

	return &ActionRecognitionResult{
		Actions:       recognizedActions,
		TotalCount:    len(recognizedActions),
		HighRiskCount: highRiskCount,
		Suggestions:   suggestions,
		ProcessedText: processedText,
	}, nil
}

// initializeBuiltinPatterns 初始化内置操作模式
func (ar *ActionRecognizer) initializeBuiltinPatterns() {
	patterns := []*ActionPattern{
		{
			Name:        "restart_service",
			Description: "重启服务",
			Keywords:    []string{"重启", "restart", "重新启动"},
			Regex:       `(?i)(重启|restart|重新启动)\s*(nginx|apache|mysql|redis|docker|服务)`,
			ActionType:  "service_management",
			Category:    "system",
			RiskLevel:   "medium",
			Parameters:  []string{"service_name", "host_id"},
		},
		{
			Name:        "check_status",
			Description: "检查状态",
			Keywords:    []string{"检查", "查看", "状态", "check", "status"},
			Regex:       `(?i)(检查|查看|check)\s*(状态|status|运行|进程)`,
			ActionType:  "monitoring",
			Category:    "info",
			RiskLevel:   "low",
			Parameters:  []string{"target", "host_id"},
		},
		{
			Name:        "view_logs",
			Description: "查看日志",
			Keywords:    []string{"日志", "log", "查看", "tail"},
			Regex:       `(?i)(查看|tail|cat)\s*(日志|log|错误|error)`,
			ActionType:  "log_analysis",
			Category:    "info",
			RiskLevel:   "low",
			Parameters:  []string{"log_path", "host_id"},
		},
		{
			Name:        "execute_command",
			Description: "执行命令",
			Keywords:    []string{"执行", "运行", "命令"},
			Regex:       `(?i)(执行|运行|run)\s*(命令|command)`,
			ActionType:  "command_execution",
			Category:    "system",
			RiskLevel:   "high",
			Parameters:  []string{"command", "host_id"},
		},
		{
			Name:        "stop_service",
			Description: "停止服务",
			Keywords:    []string{"停止", "stop", "关闭"},
			Regex:       `(?i)(停止|stop|关闭)\s*(nginx|apache|mysql|redis|docker|服务)`,
			ActionType:  "service_management",
			Category:    "system",
			RiskLevel:   "high",
			Parameters:  []string{"service_name", "host_id"},
		},
		{
			Name:        "backup_data",
			Description: "备份数据",
			Keywords:    []string{"备份", "backup"},
			Regex:       `(?i)(备份|backup)\s*(数据|data|文件|database)`,
			ActionType:  "backup_restore",
			Category:    "data",
			RiskLevel:   "medium",
			Parameters:  []string{"backup_path", "host_id"},
		},
	}

	for _, pattern := range patterns {
		ar.patterns[pattern.Name] = pattern
	}
}

// preprocessText 预处理文本
func (ar *ActionRecognizer) preprocessText(text string) string {
	// 移除多余的空白字符
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")
	// 移除特殊字符但保留中文
	text = regexp.MustCompile(`[^\w\s\u4e00-\u9fff]`).ReplaceAllString(text, " ")
	return strings.TrimSpace(text)
}

// matchPattern 匹配模式
func (ar *ActionRecognizer) matchPattern(text string, pattern *ActionPattern) []RecognizedAction {
	var actions []RecognizedAction

	// 使用正则表达式匹配
	regex, err := regexp.Compile(pattern.Regex)
	if err != nil {
		ar.logger.WithError(err).WithField("pattern", pattern.Name).Error("Invalid regex pattern")
		return actions
	}

	matches := regex.FindAllStringSubmatch(text, -1)
	for _, match := range matches {
		if len(match) > 0 {
			action := RecognizedAction{
				ID:          fmt.Sprintf("%s_%d", pattern.Name, time.Now().UnixNano()),
				Name:        pattern.Name,
				Description: pattern.Description,
				ActionType:  pattern.ActionType,
				Category:    pattern.Category,
				RiskLevel:   pattern.RiskLevel,
				Context:     match[0],
				Confidence:  ar.calculateConfidence(text, pattern),
				CreatedAt:   time.Now(),
				Parameters:  make(map[string]interface{}),
			}

			// 提取参数
			ar.extractParameters(&action, match, pattern)

			actions = append(actions, action)
		}
	}

	return actions
}

// calculateConfidence 计算置信度
func (ar *ActionRecognizer) calculateConfidence(text string, pattern *ActionPattern) float64 {
	confidence := 0.5 // 基础置信度

	// 关键词匹配加分
	for _, keyword := range pattern.Keywords {
		if strings.Contains(strings.ToLower(text), strings.ToLower(keyword)) {
			confidence += 0.1
		}
	}

	// 限制在0-1范围内
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// extractParameters 提取参数
func (ar *ActionRecognizer) extractParameters(action *RecognizedAction, match []string, pattern *ActionPattern) {
	// 根据匹配结果提取参数
	if len(match) > 1 {
		switch pattern.Name {
		case "restart_service", "stop_service":
			if len(match) > 1 {
				action.Parameters["service_name"] = match[len(match)-1]
				action.Command = fmt.Sprintf("systemctl %s %s",
					strings.ToLower(strings.Split(pattern.Name, "_")[0]),
					match[len(match)-1])
			}
		case "check_status":
			if len(match) > 1 {
				action.Parameters["target"] = match[len(match)-1]
				action.Command = fmt.Sprintf("systemctl status %s", match[len(match)-1])
			}
		case "view_logs":
			action.Parameters["log_path"] = "/var/log/syslog"
			action.Command = "tail -f /var/log/syslog"
		}
	}
}

// deduplicateActions 去重操作
func (ar *ActionRecognizer) deduplicateActions(actions []RecognizedAction) []RecognizedAction {
	seen := make(map[string]bool)
	var result []RecognizedAction

	for _, action := range actions {
		key := fmt.Sprintf("%s_%s", action.Name, action.Context)
		if !seen[key] {
			seen[key] = true
			result = append(result, action)
		}
	}

	return result
}
