/* AI运维管理平台 - 性能优化系统 */
/* CSS优化、懒加载、骨架屏、加载动画优化 */

/* ========================================
   CSS性能优化
   ======================================== */

/* 关键渲染路径优化 */
.critical-above-fold {
  /* 首屏内容优先级 */
  contain: layout style paint;
  will-change: auto;
}

/* 硬件加速优化 */
.gpu-layer {
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
  will-change: transform;
}

/* 复合层优化 */
.composite-layer {
  transform: translate3d(0, 0, 0);
  will-change: transform, opacity;
}

/* 避免重排重绘 */
.no-reflow {
  position: absolute;
  top: 0;
  left: 0;
  transform: translate3d(0, 0, 0);
}

/* ========================================
   懒加载系统
   ======================================== */

/* 图片懒加载 */
.lazy-image {
  opacity: 0;
  transition: opacity var(--duration-500) var(--ease-out);
  background: var(--color-gray-100);
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.lazy-image.loaded {
  opacity: 1;
}

.lazy-image::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: var(--spacing-8);
  height: var(--spacing-8);
  margin: calc(var(--spacing-8) / -2) 0 0 calc(var(--spacing-8) / -2);
  border: 2px solid var(--color-gray-300);
  border-top-color: var(--color-primary-500);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.lazy-image.loaded::before {
  display: none;
}

/* 内容懒加载 */
.lazy-content {
  opacity: 0;
  transform: translateY(var(--spacing-8));
  transition: all var(--duration-700) var(--ease-out);
}

.lazy-content.loaded {
  opacity: 1;
  transform: translateY(0);
}

/* ========================================
   骨架屏系统
   ======================================== */

.skeleton-container {
  animation: skeleton-pulse 1.5s ease-in-out infinite;
}

.skeleton-text {
  background: linear-gradient(
    90deg,
    var(--color-gray-200) 25%,
    var(--color-gray-100) 50%,
    var(--color-gray-200) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: var(--radius-sm);
  height: 1em;
  margin-bottom: var(--spacing-2);
}

.skeleton-text.title {
  height: 1.5em;
  width: 60%;
  margin-bottom: var(--spacing-3);
}

.skeleton-text.subtitle {
  height: 1.2em;
  width: 80%;
  margin-bottom: var(--spacing-4);
}

.skeleton-text.line {
  height: 1em;
  margin-bottom: var(--spacing-2);
}

.skeleton-text.line:nth-child(odd) {
  width: 95%;
}

.skeleton-text.line:nth-child(even) {
  width: 85%;
}

.skeleton-text.short {
  width: 40%;
}

.skeleton-avatar {
  background: linear-gradient(
    90deg,
    var(--color-gray-200) 25%,
    var(--color-gray-100) 50%,
    var(--color-gray-200) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: var(--radius-full);
  width: var(--spacing-12);
  height: var(--spacing-12);
}

.skeleton-button {
  background: linear-gradient(
    90deg,
    var(--color-gray-200) 25%,
    var(--color-gray-100) 50%,
    var(--color-gray-200) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  border-radius: var(--radius-lg);
  height: var(--spacing-10);
  width: var(--spacing-24);
}

.skeleton-card {
  background: white;
  border: 1px solid var(--color-gray-200);
  border-radius: var(--radius-xl);
  padding: var(--spacing-6);
  margin-bottom: var(--spacing-4);
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}

/* ========================================
   加载动画优化
   ======================================== */

/* 页面加载器 */
.page-loader {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, var(--color-primary-50) 0%, var(--color-gray-50) 100%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  opacity: 1;
  transition: opacity var(--duration-500) var(--ease-out);
}

.page-loader.hidden {
  opacity: 0;
  pointer-events: none;
}

.loader-content {
  text-align: center;
  max-width: 400px;
}

.loader-logo {
  width: var(--spacing-20);
  height: var(--spacing-20);
  background: linear-gradient(135deg, var(--color-primary-500) 0%, var(--color-primary-600) 100%);
  border-radius: var(--radius-2xl);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto var(--spacing-8);
  color: white;
  font-size: var(--font-size-4xl);
  animation: loader-bounce 2s infinite;
  box-shadow: var(--shadow-2xl);
}

.loader-title {
  font-size: var(--font-size-2xl);
  font-weight: var(--font-weight-bold);
  color: var(--color-gray-900);
  margin-bottom: var(--spacing-4);
}

.loader-subtitle {
  font-size: var(--font-size-base);
  color: var(--color-gray-600);
  margin-bottom: var(--spacing-8);
}

.loader-progress {
  width: 100%;
  height: 4px;
  background: var(--color-gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
  margin-bottom: var(--spacing-4);
}

.loader-progress-bar {
  height: 100%;
  background: linear-gradient(90deg, var(--color-primary-500), var(--color-primary-600));
  border-radius: var(--radius-full);
  width: 0%;
  animation: loader-progress 3s ease-out forwards;
}

.loader-status {
  font-size: var(--font-size-sm);
  color: var(--color-gray-500);
  font-weight: var(--font-weight-medium);
}

@keyframes loader-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0) scale(1);
  }
  40% {
    transform: translateY(-10px) scale(1.05);
  }
  60% {
    transform: translateY(-5px) scale(1.02);
  }
}

@keyframes loader-progress {
  0% { width: 0%; }
  30% { width: 30%; }
  60% { width: 70%; }
  90% { width: 95%; }
  100% { width: 100%; }
}

/* 内容加载器 */
.content-loader {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-12) var(--spacing-6);
  flex-direction: column;
  gap: var(--spacing-4);
}

.content-spinner {
  width: var(--spacing-8);
  height: var(--spacing-8);
  border: 3px solid var(--color-gray-200);
  border-top: 3px solid var(--color-primary-500);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
}

.content-loader-text {
  font-size: var(--font-size-sm);
  color: var(--color-gray-600);
  font-weight: var(--font-weight-medium);
}

/* ========================================
   渐进式加载
   ======================================== */

/* 关键CSS内联 */
.critical-css {
  /* 首屏关键样式 */
  font-family: var(--font-family-sans);
  line-height: var(--line-height-normal);
  color: var(--color-gray-900);
}

/* 非关键CSS延迟加载 */
.non-critical {
  /* 非首屏内容样式 */
  opacity: 0;
  transition: opacity var(--duration-300) var(--ease-out);
}

.non-critical.loaded {
  opacity: 1;
}

/* 字体加载优化 */
.font-loading {
  font-display: swap;
  font-family: var(--font-family-fallback);
}

.font-loaded {
  font-family: var(--font-family-sans);
}

/* ========================================
   内存优化
   ======================================== */

/* 虚拟滚动容器 */
.virtual-scroll {
  height: 400px;
  overflow-y: auto;
  contain: strict;
}

.virtual-item {
  contain: layout style paint;
  will-change: transform;
}

/* 图片优化 */
.optimized-image {
  max-width: 100%;
  height: auto;
  object-fit: cover;
  loading: lazy;
  decoding: async;
}

/* ========================================
   缓存优化
   ======================================== */

/* 静态资源缓存 */
.cacheable {
  /* 可缓存内容标识 */
  cache-control: public, max-age=31536000;
}

/* 动态内容缓存 */
.dynamic-cache {
  /* 动态内容缓存策略 */
  cache-control: public, max-age=3600;
}

/* ========================================
   性能监控
   ======================================== */

/* 性能标记 */
.perf-mark {
  /* 性能监控标记 */
  contain: layout style paint;
}

/* 渲染性能优化 */
.render-optimized {
  contain: layout style;
  content-visibility: auto;
  contain-intrinsic-size: 0 500px;
}
