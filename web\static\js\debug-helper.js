/* ========================================
   调试助手 - 帮助诊断前端问题
   ======================================== */

class DebugHelper {
    constructor() {
        this.init();
    }
    
    init() {
        // 添加调试信息到控制台
        console.log('🔧 Debug Helper 已启动');
        
        // 检查关键组件
        this.checkComponents();
        
        // 添加全局错误处理
        this.setupErrorHandling();
        
        // 添加调试快捷键
        this.setupDebugShortcuts();
    }
    
    checkComponents() {
        const components = {
            'SmartInput': window.SmartInput,
            'messageInteractions': window.messageInteractions,
            'chatHistoryManager': window.chatHistoryManager,
            'smartInput实例': window.smartInput
        };
        
        console.group('📋 组件检查');
        Object.keys(components).forEach(name => {
            const component = components[name];
            const status = component ? '✅ 已加载' : '❌ 未加载';
            console.log(`${name}: ${status}`);
        });
        console.groupEnd();
        
        // 检查DOM元素
        const elements = {
            'smart-chat-input': document.getElementById('smart-chat-input'),
            'chat-list': document.getElementById('chat-list'),
            'chat-messages': document.getElementById('chat-messages'),
            'sidebar': document.getElementById('sidebar'),
            'assistant-panel': document.getElementById('assistant-panel')
        };
        
        console.group('🎯 DOM元素检查');
        Object.keys(elements).forEach(id => {
            const element = elements[id];
            const status = element ? '✅ 存在' : '❌ 不存在';
            console.log(`#${id}: ${status}`);
        });
        console.groupEnd();
    }
    
    setupErrorHandling() {
        // 捕获未处理的错误
        window.addEventListener('error', (event) => {
            console.error('🚨 JavaScript错误:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        });
        
        // 捕获Promise拒绝
        window.addEventListener('unhandledrejection', (event) => {
            console.error('🚨 未处理的Promise拒绝:', event.reason);
        });
    }
    
    setupDebugShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+Shift+D 显示调试信息
            if (e.ctrlKey && e.shiftKey && e.key === 'D') {
                e.preventDefault();
                this.showDebugInfo();
            }
            
            // Ctrl+Shift+T 测试所有功能
            if (e.ctrlKey && e.shiftKey && e.key === 'T') {
                e.preventDefault();
                this.testAllFeatures();
            }
        });
    }
    
    showDebugInfo() {
        const info = {
            '浏览器': navigator.userAgent,
            '视口大小': `${window.innerWidth}x${window.innerHeight}`,
            '当前主题': document.documentElement.getAttribute('data-theme') || 'light',
            '智能输入状态': window.smartInput ? '已初始化' : '未初始化',
            '对话历史状态': window.chatHistoryManager ? '已初始化' : '未初始化',
            '消息交互状态': window.messageInteractions ? '已初始化' : '未初始化'
        };
        
        console.group('🔍 调试信息');
        Object.keys(info).forEach(key => {
            console.log(`${key}: ${info[key]}`);
        });
        console.groupEnd();
        
        // 显示在页面上
        this.showDebugModal(info);
    }
    
    showDebugModal(info) {
        // 创建调试模态框
        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.3);
            z-index: 10000;
            max-width: 500px;
            max-height: 400px;
            overflow-y: auto;
        `;
        
        const content = Object.keys(info).map(key => 
            `<div><strong>${key}:</strong> ${info[key]}</div>`
        ).join('');
        
        modal.innerHTML = `
            <h3>🔍 调试信息</h3>
            ${content}
            <br>
            <button onclick="this.parentNode.remove()" style="padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">关闭</button>
        `;
        
        document.body.appendChild(modal);
    }
    
    testAllFeatures() {
        console.group('🧪 功能测试');
        
        // 测试智能输入
        if (window.smartInput) {
            console.log('✅ 智能输入组件可用');
            try {
                window.smartInput.setValue('测试消息');
                console.log('✅ 智能输入设置值成功');
                window.smartInput.clear();
                console.log('✅ 智能输入清空成功');
            } catch (e) {
                console.error('❌ 智能输入测试失败:', e);
            }
        } else {
            console.error('❌ 智能输入组件不可用');
        }
        
        // 测试对话历史
        if (window.chatHistoryManager) {
            console.log('✅ 对话历史管理器可用');
            try {
                window.chatHistoryManager.renderChatList();
                console.log('✅ 对话历史渲染成功');
            } catch (e) {
                console.error('❌ 对话历史测试失败:', e);
            }
        } else {
            console.error('❌ 对话历史管理器不可用');
        }
        
        // 测试消息交互
        if (window.messageInteractions) {
            console.log('✅ 消息交互组件可用');
        } else {
            console.error('❌ 消息交互组件不可用');
        }
        
        console.groupEnd();
    }
    
    // 修复常见问题的方法
    fixCommonIssues() {
        console.log('🔧 尝试修复常见问题...');

        // 修复主题管理器问题
        this.fixThemeManagerIssues();

        // 重新初始化智能输入
        if (!window.smartInput && window.SmartInput) {
            try {
                window.smartInput = new SmartInput('smart-chat-input', {
                    maxLength: 4000,
                    placeholder: '输入您的问题...',
                    autoResize: true,
                    showCharCounter: true,
                    enableEmoji: true,
                    enableAutocomplete: true
                });
                console.log('✅ 智能输入组件重新初始化成功');
            } catch (e) {
                console.error('❌ 智能输入组件重新初始化失败:', e);
            }
        }

        // 重新初始化对话历史
        if (!window.chatHistoryManager) {
            try {
                window.chatHistoryManager = new ChatHistoryManager();
                console.log('✅ 对话历史管理器重新初始化成功');
            } catch (e) {
                console.error('❌ 对话历史管理器重新初始化失败:', e);
            }
        }
    }

    // 修复主题管理器问题
    fixThemeManagerIssues() {
        console.log('🎨 检查主题管理器问题...');

        if (window.themeManager) {
            console.log('✅ ThemeManager已加载');

            // 检查设置面板
            const settingsPanel = document.getElementById('settings-panel');
            if (settingsPanel) {
                console.log('✅ 设置面板已创建');

                // 检查字体滑块
                const fontSlider = settingsPanel.querySelector('.font-size-slider');
                const fontValue = settingsPanel.querySelector('.font-size-value');

                if (fontSlider && fontValue) {
                    console.log('✅ 字体滑块元素存在');

                    // 测试事件绑定
                    try {
                        fontSlider.dispatchEvent(new Event('input'));
                        console.log('✅ 字体滑块事件测试成功');
                    } catch (error) {
                        console.log('❌ 字体滑块事件测试失败:', error);
                        // 重新绑定事件
                        if (window.themeManager.bindFontSliderEvents) {
                            window.themeManager.bindFontSliderEvents();
                        }
                    }
                } else {
                    console.log('❌ 字体滑块元素缺失:', {
                        fontSlider: !!fontSlider,
                        fontValue: !!fontValue
                    });

                    // 尝试重新绑定
                    if (window.themeManager.bindFontSliderEvents) {
                        console.log('🔄 尝试重新绑定字体滑块事件');
                        window.themeManager.bindFontSliderEvents();
                    }
                }
            } else {
                console.log('❌ 设置面板未创建');

                // 尝试重新创建
                if (window.themeManager.createSettingsPanel) {
                    console.log('🔄 尝试重新创建设置面板');
                    window.themeManager.createSettingsPanel();
                }
            }
        } else {
            console.log('❌ ThemeManager未加载');
        }
    }
}

// 自动启动调试助手
if (typeof window !== 'undefined') {
    window.debugHelper = new DebugHelper();
    
    // 添加全局修复函数
    window.fixIssues = () => {
        window.debugHelper.fixCommonIssues();
    };
    
    console.log('💡 调试提示: 按 Ctrl+Shift+D 查看调试信息，按 Ctrl+Shift+T 测试所有功能，或调用 fixIssues() 修复问题');
}
