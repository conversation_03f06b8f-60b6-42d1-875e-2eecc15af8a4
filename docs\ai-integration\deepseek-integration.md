# DeepSeek API集成方案

## 🤖 集成概述

AI对话运维管理平台与DeepSeek API深度集成，提供智能对话、意图识别、工具调用等核心AI功能。

### 集成目标
- **自然语言交互**：支持自然语言运维操作
- **智能意图识别**：准确理解用户意图和参数
- **工具调用能力**：通过Function Calling执行系统操作
- **上下文管理**：维护对话上下文和历史记录

## 🔧 技术架构

### DeepSeek API客户端
```go
type DeepSeekClient struct {
    httpClient     *http.Client
    baseURL        string
    apiKey         string
    model          string
    circuitBreaker *gobreaker.CircuitBreaker
    rateLimiter    *rate.Limiter
    tokenCounter   *TokenCounter
    contextManager *ContextManager
    cache          cache.Cache
    metrics        *APIMetrics
}

type ClientConfig struct {
    BaseURL        string        `yaml:"base_url"`
    APIKey         string        `yaml:"api_key"`
    Model          string        `yaml:"model"`
    Timeout        time.Duration `yaml:"timeout"`
    MaxRetries     int           `yaml:"max_retries"`
    RetryDelay     time.Duration `yaml:"retry_delay"`
    MaxTokens      int           `yaml:"max_tokens"`
    Temperature    float64       `yaml:"temperature"`
    TopP           float64       `yaml:"top_p"`
}
```

### API调用流程
```
用户输入 → 预处理 → 上下文构建 → API调用 → 响应解析 → 后处理 → 返回结果
    ↓         ↓         ↓         ↓         ↓         ↓         ↓
  验证输入   添加系统   Token计数  HTTP请求  JSON解析  意图提取  格式化输出
  安全检查   提示词     限流控制  重试机制  错误处理  工具调用  缓存结果
```

## 📝 消息格式设计

### 标准消息结构
```go
type Message struct {
    Role       string                 `json:"role"`       // system, user, assistant, tool
    Content    string                 `json:"content"`    // 消息内容
    Name       string                 `json:"name,omitempty"`       // 工具名称
    ToolCalls  []ToolCall            `json:"tool_calls,omitempty"` // 工具调用
    ToolCallID string                 `json:"tool_call_id,omitempty"` // 工具调用ID
}

type ToolCall struct {
    ID       string   `json:"id"`
    Type     string   `json:"type"`     // function
    Function Function `json:"function"`
}

type Function struct {
    Name      string `json:"name"`
    Arguments string `json:"arguments"` // JSON字符串
}
```

### 系统提示词模板
```go
const SystemPrompt = `你是一个专业的AI运维助手，负责帮助用户管理和监控IT基础设施。

## 你的能力：
1. 主机管理：查看主机列表、添加/删除主机、测试连接
2. 命令执行：在指定主机上执行Shell命令
3. 监控告警：查看告警信息、处理告警事件
4. 统计分析：生成运维统计报表和趋势分析
5. 系统配置：查看和修改系统配置

## 工作原则：
1. 安全第一：执行任何操作前都要确认用户权限
2. 谨慎操作：对于危险命令要明确警告并要求确认
3. 详细记录：所有操作都要记录日志
4. 友好交互：用专业但易懂的语言与用户交流

## 当前用户信息：
- 用户名：{{.Username}}
- 角色：{{.Role}}
- 权限：{{.Permissions}}

请根据用户的问题，选择合适的工具来帮助解决问题。如果需要执行危险操作，请先警告用户并要求确认。`
```

## 🛠️ Function Calling设计

### 工具定义
```json
{
  "tools": [
    {
      "type": "function",
      "function": {
        "name": "list_hosts",
        "description": "获取主机列表，支持按状态、分组等条件过滤",
        "parameters": {
          "type": "object",
          "properties": {
            "status": {
              "type": "string",
              "enum": ["online", "offline", "all"],
              "description": "主机状态过滤"
            },
            "group": {
              "type": "string",
              "description": "主机分组过滤"
            },
            "limit": {
              "type": "integer",
              "minimum": 1,
              "maximum": 100,
              "default": 20,
              "description": "返回结果数量限制"
            }
          }
        }
      }
    },
    {
      "type": "function",
      "function": {
        "name": "execute_command",
        "description": "在指定主机上执行Shell命令",
        "parameters": {
          "type": "object",
          "properties": {
            "host_id": {
              "type": "integer",
              "description": "目标主机ID"
            },
            "host_name": {
              "type": "string",
              "description": "目标主机名称（与host_id二选一）"
            },
            "command": {
              "type": "string",
              "description": "要执行的Shell命令"
            },
            "timeout": {
              "type": "integer",
              "minimum": 1,
              "maximum": 300,
              "default": 30,
              "description": "命令执行超时时间（秒）"
            },
            "working_directory": {
              "type": "string",
              "description": "命令执行的工作目录"
            }
          },
          "required": ["command"],
          "anyOf": [
            {"required": ["host_id"]},
            {"required": ["host_name"]}
          ]
        }
      }
    },
    {
      "type": "function",
      "function": {
        "name": "get_alerts",
        "description": "获取系统告警信息",
        "parameters": {
          "type": "object",
          "properties": {
            "level": {
              "type": "string",
              "enum": ["critical", "warning", "info", "all"],
              "description": "告警级别过滤"
            },
            "status": {
              "type": "string",
              "enum": ["open", "acknowledged", "resolved", "all"],
              "description": "告警状态过滤"
            },
            "host_id": {
              "type": "integer",
              "description": "主机ID过滤"
            },
            "time_range": {
              "type": "string",
              "enum": ["1h", "6h", "24h", "7d", "30d"],
              "default": "24h",
              "description": "时间范围过滤"
            }
          }
        }
      }
    },
    {
      "type": "function",
      "function": {
        "name": "get_system_stats",
        "description": "获取系统统计信息",
        "parameters": {
          "type": "object",
          "properties": {
            "metric_type": {
              "type": "string",
              "enum": ["overview", "hosts", "alerts", "operations"],
              "description": "统计指标类型"
            },
            "time_period": {
              "type": "string",
              "enum": ["1h", "6h", "24h", "7d", "30d"],
              "default": "24h",
              "description": "统计时间周期"
            },
            "group_by": {
              "type": "string",
              "enum": ["status", "level", "host", "user"],
              "description": "分组统计维度"
            }
          }
        }
      }
    }
  ]
}
```

### 工具执行器
```go
type ToolExecutor struct {
    hostService    HostService
    alertService   AlertService
    statsService   StatsService
    authService    AuthService
    logger         *logrus.Logger
}

func (te *ToolExecutor) ExecuteFunction(ctx context.Context, call ToolCall, userID int64) (*ToolResult, error) {
    // 权限检查
    if !te.authService.HasPermission(userID, call.Function.Name) {
        return nil, errors.New("insufficient permissions")
    }
    
    // 参数解析
    var params map[string]interface{}
    if err := json.Unmarshal([]byte(call.Function.Arguments), &params); err != nil {
        return nil, fmt.Errorf("invalid function arguments: %w", err)
    }
    
    // 执行工具函数
    switch call.Function.Name {
    case "list_hosts":
        return te.executeListHosts(ctx, params)
    case "execute_command":
        return te.executeCommand(ctx, params, userID)
    case "get_alerts":
        return te.executeGetAlerts(ctx, params)
    case "get_system_stats":
        return te.executeGetStats(ctx, params)
    default:
        return nil, fmt.Errorf("unknown function: %s", call.Function.Name)
    }
}

type ToolResult struct {
    Success bool                   `json:"success"`
    Data    interface{}           `json:"data,omitempty"`
    Error   string                `json:"error,omitempty"`
    Metadata map[string]interface{} `json:"metadata,omitempty"`
}
```

## 🧠 上下文管理策略

### 智能上下文压缩
```go
type ContextManager struct {
    maxTokens        int
    compressionRatio float64
    summaryModel     string
    keywordExtractor *KeywordExtractor
    entityExtractor  *EntityExtractor
    cache           *ContextCache
}

type ConversationContext struct {
    SessionID    string              `json:"session_id"`
    Messages     []Message           `json:"messages"`
    Summary      string              `json:"summary"`
    Keywords     []string            `json:"keywords"`
    Entities     map[string]string   `json:"entities"`
    LastActivity time.Time           `json:"last_activity"`
    TokenCount   int                 `json:"token_count"`
    UserContext  *UserContext        `json:"user_context"`
}

type UserContext struct {
    UserID      int64    `json:"user_id"`
    Username    string   `json:"username"`
    Role        string   `json:"role"`
    Permissions []string `json:"permissions"`
    Preferences map[string]interface{} `json:"preferences"`
}

// 上下文压缩算法
func (cm *ContextManager) CompressContext(ctx *ConversationContext) (*ConversationContext, error) {
    if ctx.TokenCount <= cm.maxTokens {
        return ctx, nil
    }
    
    // 1. 保留最近的重要消息
    recentMessages := cm.extractRecentMessages(ctx.Messages, 5)
    
    // 2. 生成历史摘要
    historicalMessages := ctx.Messages[:len(ctx.Messages)-5]
    summary, err := cm.generateSummary(historicalMessages)
    if err != nil {
        return nil, err
    }
    
    // 3. 提取关键实体和上下文
    entities := cm.entityExtractor.Extract(ctx.Messages)
    keywords := cm.keywordExtractor.Extract(ctx.Messages)
    
    // 4. 重构上下文
    compressedCtx := &ConversationContext{
        SessionID:    ctx.SessionID,
        Messages:     recentMessages,
        Summary:      summary,
        Keywords:     keywords,
        Entities:     entities,
        LastActivity: time.Now(),
        UserContext:  ctx.UserContext,
    }
    
    compressedCtx.TokenCount = cm.calculateTokens(compressedCtx)
    return compressedCtx, nil
}
```

### 实体提取器
```go
type EntityExtractor struct {
    patterns map[string]*regexp.Regexp
}

func NewEntityExtractor() *EntityExtractor {
    return &EntityExtractor{
        patterns: map[string]*regexp.Regexp{
            "host":     regexp.MustCompile(`(?i)\b(?:主机|服务器|host|server)\s*[:\-]?\s*([a-zA-Z0-9\-\.]+)`),
            "ip":       regexp.MustCompile(`\b(?:[0-9]{1,3}\.){3}[0-9]{1,3}\b`),
            "command":  regexp.MustCompile(`(?i)(?:执行|运行|run|execute)\s*[:\-]?\s*["`']?([^"`'\n]+)["`']?`),
            "file":     regexp.MustCompile(`(?i)(?:文件|目录|file|dir|path)\s*[:\-]?\s*([/\w\-\.]+)`),
            "service":  regexp.MustCompile(`(?i)(?:服务|service)\s*[:\-]?\s*([a-zA-Z0-9\-_]+)`),
            "port":     regexp.MustCompile(`(?i)(?:端口|port)\s*[:\-]?\s*(\d+)`),
        },
    }
}

func (ee *EntityExtractor) Extract(messages []Message) map[string]string {
    entities := make(map[string]string)
    
    for _, msg := range messages {
        if msg.Role != "user" {
            continue
        }
        
        for entityType, pattern := range ee.patterns {
            matches := pattern.FindAllStringSubmatch(msg.Content, -1)
            if len(matches) > 0 {
                var values []string
                for _, match := range matches {
                    if len(match) > 1 {
                        values = append(values, match[1])
                    }
                }
                if len(values) > 0 {
                    entities[entityType] = strings.Join(values, ",")
                }
            }
        }
    }
    
    return entities
}
```

## 🔄 流式响应处理

### 流式API调用
```go
type StreamProcessor struct {
    client   *DeepSeekClient
    buffer   *bytes.Buffer
    decoder  *json.Decoder
    callback StreamCallback
}

type StreamCallback func(chunk *StreamChunk) error

type StreamChunk struct {
    ID      string `json:"id"`
    Object  string `json:"object"`
    Created int64  `json:"created"`
    Model   string `json:"model"`
    Choices []struct {
        Index int `json:"index"`
        Delta struct {
            Role         string     `json:"role,omitempty"`
            Content      string     `json:"content,omitempty"`
            ToolCalls    []ToolCall `json:"tool_calls,omitempty"`
            FinishReason string     `json:"finish_reason,omitempty"`
        } `json:"delta"`
        FinishReason string `json:"finish_reason,omitempty"`
    } `json:"choices"`
}

func (sp *StreamProcessor) ProcessStream(ctx context.Context, req *ChatRequest) error {
    req.Stream = true
    
    resp, err := sp.client.doStreamRequest(ctx, req)
    if err != nil {
        return err
    }
    defer resp.Body.Close()
    
    scanner := bufio.NewScanner(resp.Body)
    for scanner.Scan() {
        line := scanner.Text()
        
        // 跳过空行和注释
        if line == "" || strings.HasPrefix(line, ":") {
            continue
        }
        
        // 处理SSE数据
        if strings.HasPrefix(line, "data: ") {
            data := strings.TrimPrefix(line, "data: ")
            
            // 检查结束标记
            if data == "[DONE]" {
                break
            }
            
            // 解析JSON数据
            var chunk StreamChunk
            if err := json.Unmarshal([]byte(data), &chunk); err != nil {
                continue
            }
            
            // 调用回调函数
            if err := sp.callback(&chunk); err != nil {
                return err
            }
        }
    }
    
    return scanner.Err()
}
```

## 📊 性能优化

### 缓存策略
```go
type APICache struct {
    cache      cache.Cache
    ttl        time.Duration
    keyBuilder *CacheKeyBuilder
}

type CacheKeyBuilder struct {
    includeUser bool
    includeTime bool
    timeWindow  time.Duration
}

func (ck *CacheKeyBuilder) BuildKey(req *ChatRequest, userID int64) string {
    h := sha256.New()
    
    // 基础内容哈希
    h.Write([]byte(req.Model))
    for _, msg := range req.Messages {
        h.Write([]byte(msg.Role + ":" + msg.Content))
    }
    
    // 用户相关
    if ck.includeUser {
        h.Write([]byte(fmt.Sprintf("user:%d", userID)))
    }
    
    // 时间窗口
    if ck.includeTime {
        window := time.Now().Truncate(ck.timeWindow).Unix()
        h.Write([]byte(fmt.Sprintf("time:%d", window)))
    }
    
    return fmt.Sprintf("api_cache:%x", h.Sum(nil))
}
```

### 连接池管理
```go
type HTTPClientPool struct {
    clients []*http.Client
    index   int64
    mutex   sync.Mutex
}

func (pool *HTTPClientPool) GetClient() *http.Client {
    pool.mutex.Lock()
    defer pool.mutex.Unlock()
    
    client := pool.clients[pool.index%int64(len(pool.clients))]
    pool.index++
    
    return client
}

func NewHTTPClientPool(size int) *HTTPClientPool {
    clients := make([]*http.Client, size)
    
    for i := 0; i < size; i++ {
        clients[i] = &http.Client{
            Timeout: 30 * time.Second,
            Transport: &http.Transport{
                MaxIdleConns:        100,
                MaxIdleConnsPerHost: 10,
                IdleConnTimeout:     90 * time.Second,
                DisableCompression:  false,
            },
        }
    }
    
    return &HTTPClientPool{clients: clients}
}
```

## 🔍 监控与指标

### API调用指标
```go
type APIMetrics struct {
    requestsTotal     *prometheus.CounterVec
    requestDuration   *prometheus.HistogramVec
    tokensUsed        *prometheus.CounterVec
    errorRate         *prometheus.CounterVec
    contextLength     *prometheus.HistogramVec
}

func NewAPIMetrics() *APIMetrics {
    return &APIMetrics{
        requestsTotal: prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "deepseek_api_requests_total",
                Help: "Total number of DeepSeek API requests",
            },
            []string{"model", "status"},
        ),
        requestDuration: prometheus.NewHistogramVec(
            prometheus.HistogramOpts{
                Name:    "deepseek_api_request_duration_seconds",
                Help:    "DeepSeek API request duration in seconds",
                Buckets: []float64{0.1, 0.5, 1.0, 2.0, 5.0, 10.0},
            },
            []string{"model"},
        ),
        tokensUsed: prometheus.NewCounterVec(
            prometheus.CounterOpts{
                Name: "deepseek_api_tokens_used_total",
                Help: "Total number of tokens used",
            },
            []string{"model", "type"},
        ),
    }
}
```

## ⚠️ 错误处理

### 错误分类和处理
```go
type APIError struct {
    Type       string `json:"type"`
    Code       string `json:"code"`
    Message    string `json:"message"`
    StatusCode int    `json:"status_code"`
    Retryable  bool   `json:"retryable"`
}

func (e *APIError) Error() string {
    return fmt.Sprintf("DeepSeek API error [%s]: %s", e.Code, e.Message)
}

// 错误处理策略
func (client *DeepSeekClient) handleError(resp *http.Response) error {
    var apiErr APIError
    
    switch resp.StatusCode {
    case 400:
        apiErr = APIError{
            Type:       "invalid_request",
            Code:       "bad_request",
            Message:    "Invalid request parameters",
            StatusCode: 400,
            Retryable:  false,
        }
    case 401:
        apiErr = APIError{
            Type:       "authentication_error",
            Code:       "invalid_api_key",
            Message:    "Invalid API key",
            StatusCode: 401,
            Retryable:  false,
        }
    case 429:
        apiErr = APIError{
            Type:       "rate_limit_error",
            Code:       "rate_limit_exceeded",
            Message:    "Rate limit exceeded",
            StatusCode: 429,
            Retryable:  true,
        }
    case 500, 502, 503, 504:
        apiErr = APIError{
            Type:       "server_error",
            Code:       "internal_error",
            Message:    "Server error",
            StatusCode: resp.StatusCode,
            Retryable:  true,
        }
    }
    
    return &apiErr
}
```
