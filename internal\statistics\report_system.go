package statistics

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// ReportSystem 统计报表系统
type ReportSystem struct {
	config           *ReportSystemConfig
	logger           *logrus.Logger
	dataCollector    *StatisticsDataCollector
	reportGenerator  *ReportGenerator
	chartGenerator   *ChartGenerator
	dashboardManager *DashboardManager
	dataStore        *StatisticsDataStore
	cacheManager     *ReportCacheManager
	scheduler        *ReportScheduler
	exportManager    *ReportExportManager
	mutex            sync.RWMutex
	isRunning        bool
	stopChan         chan struct{}
}

// ReportSystemConfig 报表系统配置
type ReportSystemConfig struct {
	EnableAutoGeneration     bool          `json:"enable_auto_generation"`
	EnableCaching            bool          `json:"enable_caching"`
	EnableScheduling         bool          `json:"enable_scheduling"`
	EnableExport             bool          `json:"enable_export"`
	DataRetentionDays        int           `json:"data_retention_days"`
	CacheExpirationTime      time.Duration `json:"cache_expiration_time"`
	ReportGenerationInterval time.Duration `json:"report_generation_interval"`
	MaxConcurrentReports     int           `json:"max_concurrent_reports"`
	ExportFormats            []string      `json:"export_formats"`
	DefaultTimeRange         string        `json:"default_time_range"`
	EnableRealTimeUpdates    bool          `json:"enable_real_time_updates"`
	CompressionEnabled       bool          `json:"compression_enabled"`
}

// StatisticsDataCollector 统计数据收集器
type StatisticsDataCollector struct {
	logger            *logrus.Logger
	operationStats    *OperationStatistics
	systemHealthStats *SystemHealthStatistics
	aiUsageStats      *AIUsageStatistics
	userActivityStats *UserActivityStatistics
	performanceStats  *PerformanceStatistics
	mutex             sync.RWMutex
}

// ReportGenerator 报表生成器
type ReportGenerator struct {
	logger        *logrus.Logger
	dataCollector *StatisticsDataCollector
	templates     map[string]*ReportTemplate
	mutex         sync.RWMutex
}

// ChartGenerator 图表生成器
type ChartGenerator struct {
	logger     *logrus.Logger
	chartTypes map[string]ChartRenderer
	mutex      sync.RWMutex
}

// DashboardManager 仪表盘管理器
type DashboardManager struct {
	logger     *logrus.Logger
	dashboards map[string]*Dashboard
	widgets    map[string]*Widget
	layouts    map[string]*DashboardLayout
	mutex      sync.RWMutex
}

// StatisticsDataStore 统计数据存储
type StatisticsDataStore struct {
	logger      *logrus.Logger
	data        map[string][]*StatisticsEntry
	aggregated  map[string]*AggregatedData
	retention   time.Duration
	maxSize     int64
	currentSize int64
	mutex       sync.RWMutex
}

// ReportCacheManager 报表缓存管理器
type ReportCacheManager struct {
	logger     *logrus.Logger
	cache      map[string]*CachedReport
	expiration time.Duration
	maxSize    int
	mutex      sync.RWMutex
}

// ReportScheduler 报表调度器
type ReportScheduler struct {
	logger        *logrus.Logger
	scheduledJobs map[string]*ScheduledReportJob
	cronJobs      map[string]*CronJob
	mutex         sync.RWMutex
}

// ReportExportManager 报表导出管理器
type ReportExportManager struct {
	logger    *logrus.Logger
	exporters map[string]ReportExporter
	mutex     sync.RWMutex
}

// OperationStatistics 运维操作统计
type OperationStatistics struct {
	TotalCommands        int64              `json:"total_commands"`
	SuccessfulCommands   int64              `json:"successful_commands"`
	FailedCommands       int64              `json:"failed_commands"`
	CommandsByType       map[string]int64   `json:"commands_by_type"`
	CommandsByUser       map[string]int64   `json:"commands_by_user"`
	CommandsByHour       map[int]int64      `json:"commands_by_hour"`
	AverageExecutionTime time.Duration      `json:"average_execution_time"`
	TopCommands          []CommandFrequency `json:"top_commands"`
	ErrorPatterns        []ErrorPattern     `json:"error_patterns"`
	RiskDistribution     map[string]int64   `json:"risk_distribution"`
	Timestamp            time.Time          `json:"timestamp"`
}

// SystemHealthStatistics 系统健康度统计
type SystemHealthStatistics struct {
	OverallHealthScore float64             `json:"overall_health_score"`
	CPUHealthScore     float64             `json:"cpu_health_score"`
	MemoryHealthScore  float64             `json:"memory_health_score"`
	DiskHealthScore    float64             `json:"disk_health_score"`
	NetworkHealthScore float64             `json:"network_health_score"`
	ServiceHealthScore float64             `json:"service_health_score"`
	HealthTrends       *HealthTrends       `json:"health_trends"`
	AlertStatistics    *AlertStatistics    `json:"alert_statistics"`
	UptimeStatistics   *UptimeStatistics   `json:"uptime_statistics"`
	PerformanceMetrics *PerformanceMetrics `json:"performance_metrics"`
	Timestamp          time.Time           `json:"timestamp"`
}

// AIUsageStatistics AI助手使用统计
type AIUsageStatistics struct {
	TotalConversations   int64                   `json:"total_conversations"`
	TotalMessages        int64                   `json:"total_messages"`
	AverageSessionLength time.Duration           `json:"average_session_length"`
	TopQuestions         []QuestionFrequency     `json:"top_questions"`
	ToolUsageStats       map[string]int64        `json:"tool_usage_stats"`
	UserSatisfaction     *SatisfactionMetrics    `json:"user_satisfaction"`
	ResponseTimeStats    *ResponseTimeStatistics `json:"response_time_stats"`
	ErrorRateStats       *ErrorRateStatistics    `json:"error_rate_stats"`
	UsageByHour          map[int]int64           `json:"usage_by_hour"`
	UsageByUser          map[string]int64        `json:"usage_by_user"`
	Timestamp            time.Time               `json:"timestamp"`
}

// UserActivityStatistics 用户活动统计
type UserActivityStatistics struct {
	TotalUsers         int64             `json:"total_users"`
	ActiveUsers        int64             `json:"active_users"`
	NewUsers           int64             `json:"new_users"`
	UserRetentionRate  float64           `json:"user_retention_rate"`
	AverageSessionTime time.Duration     `json:"average_session_time"`
	TopActiveUsers     []UserActivity    `json:"top_active_users"`
	UserGrowthTrend    []UserGrowthPoint `json:"user_growth_trend"`
	ActivityByTimeZone map[string]int64  `json:"activity_by_timezone"`
	FeatureUsageStats  map[string]int64  `json:"feature_usage_stats"`
	LoginStatistics    *LoginStatistics  `json:"login_statistics"`
	Timestamp          time.Time         `json:"timestamp"`
}

// PerformanceStatistics 性能统计
type PerformanceStatistics struct {
	AverageResponseTime time.Duration        `json:"average_response_time"`
	P95ResponseTime     time.Duration        `json:"p95_response_time"`
	P99ResponseTime     time.Duration        `json:"p99_response_time"`
	ThroughputPerSecond float64              `json:"throughput_per_second"`
	ErrorRate           float64              `json:"error_rate"`
	CacheHitRate        float64              `json:"cache_hit_rate"`
	DatabaseQueryTime   time.Duration        `json:"database_query_time"`
	MemoryUsage         *MemoryUsageStats    `json:"memory_usage"`
	CPUUsage            *CPUUsageStats       `json:"cpu_usage"`
	NetworkLatency      *NetworkLatencyStats `json:"network_latency"`
	Timestamp           time.Time            `json:"timestamp"`
}

// CommandFrequency 命令频率
type CommandFrequency struct {
	Command    string    `json:"command"`
	Count      int64     `json:"count"`
	Percentage float64   `json:"percentage"`
	LastUsed   time.Time `json:"last_used"`
}

// ErrorPattern 错误模式
type ErrorPattern struct {
	Pattern     string    `json:"pattern"`
	Count       int64     `json:"count"`
	FirstSeen   time.Time `json:"first_seen"`
	LastSeen    time.Time `json:"last_seen"`
	Severity    string    `json:"severity"`
	Description string    `json:"description"`
}

// HealthTrends 健康度趋势
type HealthTrends struct {
	HourlyScores   []float64 `json:"hourly_scores"`
	DailyScores    []float64 `json:"daily_scores"`
	WeeklyScores   []float64 `json:"weekly_scores"`
	TrendDirection string    `json:"trend_direction"`
	ChangeRate     float64   `json:"change_rate"`
}

// AlertStatistics 告警统计
type AlertStatistics struct {
	TotalAlerts      int64            `json:"total_alerts"`
	ActiveAlerts     int64            `json:"active_alerts"`
	ResolvedAlerts   int64            `json:"resolved_alerts"`
	AlertsBySeverity map[string]int64 `json:"alerts_by_severity"`
	AlertsByType     map[string]int64 `json:"alerts_by_type"`
	MTTR             time.Duration    `json:"mttr"` // Mean Time To Resolution
	MTTD             time.Duration    `json:"mttd"` // Mean Time To Detection
}

// UptimeStatistics 运行时间统计
type UptimeStatistics struct {
	TotalUptime    time.Duration `json:"total_uptime"`
	UptimePercent  float64       `json:"uptime_percent"`
	DowntimeEvents int64         `json:"downtime_events"`
	MTBF           time.Duration `json:"mtbf"` // Mean Time Between Failures
	LastDowntime   *time.Time    `json:"last_downtime,omitempty"`
}

// QuestionFrequency 问题频率
type QuestionFrequency struct {
	Question  string    `json:"question"`
	Count     int64     `json:"count"`
	Category  string    `json:"category"`
	LastAsked time.Time `json:"last_asked"`
}

// SatisfactionMetrics 满意度指标
type SatisfactionMetrics struct {
	AverageRating      float64       `json:"average_rating"`
	RatingDistribution map[int]int64 `json:"rating_distribution"`
	PositiveFeedback   int64         `json:"positive_feedback"`
	NegativeFeedback   int64         `json:"negative_feedback"`
	TotalFeedback      int64         `json:"total_feedback"`
}

// ResponseTimeStatistics 响应时间统计
type ResponseTimeStatistics struct {
	AverageTime time.Duration `json:"average_time"`
	MedianTime  time.Duration `json:"median_time"`
	P95Time     time.Duration `json:"p95_time"`
	P99Time     time.Duration `json:"p99_time"`
	MinTime     time.Duration `json:"min_time"`
	MaxTime     time.Duration `json:"max_time"`
}

// ErrorRateStatistics 错误率统计
type ErrorRateStatistics struct {
	OverallErrorRate float64           `json:"overall_error_rate"`
	ErrorsByType     map[string]int64  `json:"errors_by_type"`
	ErrorTrend       []ErrorTrendPoint `json:"error_trend"`
}

// UserActivity 用户活动
type UserActivity struct {
	UserID       int64         `json:"user_id"`
	Username     string        `json:"username"`
	SessionCount int64         `json:"session_count"`
	TotalTime    time.Duration `json:"total_time"`
	LastActivity time.Time     `json:"last_activity"`
}

// UserGrowthPoint 用户增长点
type UserGrowthPoint struct {
	Date       time.Time `json:"date"`
	NewUsers   int64     `json:"new_users"`
	TotalUsers int64     `json:"total_users"`
	GrowthRate float64   `json:"growth_rate"`
}

// LoginStatistics 登录统计
type LoginStatistics struct {
	TotalLogins    int64            `json:"total_logins"`
	UniqueLogins   int64            `json:"unique_logins"`
	FailedLogins   int64            `json:"failed_logins"`
	LoginsByHour   map[int]int64    `json:"logins_by_hour"`
	LoginsByDevice map[string]int64 `json:"logins_by_device"`
}

// MemoryUsageStats 内存使用统计
type MemoryUsageStats struct {
	AverageUsage   float64 `json:"average_usage"`
	PeakUsage      float64 `json:"peak_usage"`
	MinUsage       float64 `json:"min_usage"`
	TrendDirection string  `json:"trend_direction"`
}

// CPUUsageStats CPU使用统计
type CPUUsageStats struct {
	AverageUsage   float64 `json:"average_usage"`
	PeakUsage      float64 `json:"peak_usage"`
	MinUsage       float64 `json:"min_usage"`
	TrendDirection string  `json:"trend_direction"`
}

// NetworkLatencyStats 网络延迟统计
type NetworkLatencyStats struct {
	AverageLatency time.Duration `json:"average_latency"`
	P95Latency     time.Duration `json:"p95_latency"`
	P99Latency     time.Duration `json:"p99_latency"`
	MinLatency     time.Duration `json:"min_latency"`
	MaxLatency     time.Duration `json:"max_latency"`
}

// ErrorTrendPoint 错误趋势点
type ErrorTrendPoint struct {
	Timestamp time.Time `json:"timestamp"`
	ErrorRate float64   `json:"error_rate"`
	Count     int64     `json:"count"`
}

// PerformanceMetrics 性能指标
type PerformanceMetrics struct {
	ResponseTime  time.Duration `json:"response_time"`
	Throughput    float64       `json:"throughput"`
	ErrorRate     float64       `json:"error_rate"`
	ResourceUsage float64       `json:"resource_usage"`
	HealthScore   float64       `json:"health_score"`
}

// 报表数据结构

// OperationReport 运维操作报表
type OperationReport struct {
	ID          string               `json:"id"`
	Type        string               `json:"type"`
	TimeRange   string               `json:"time_range"`
	GeneratedAt time.Time            `json:"generated_at"`
	Statistics  *OperationStatistics `json:"statistics"`
	Summary     *ReportSummary       `json:"summary"`
	Charts      []*Chart             `json:"charts"`
	Insights    []*Insight           `json:"insights"`
}

// SystemHealthReport 系统健康度报表
type SystemHealthReport struct {
	ID              string                  `json:"id"`
	Type            string                  `json:"type"`
	TimeRange       string                  `json:"time_range"`
	GeneratedAt     time.Time               `json:"generated_at"`
	Statistics      *SystemHealthStatistics `json:"statistics"`
	Summary         *ReportSummary          `json:"summary"`
	Charts          []*Chart                `json:"charts"`
	Recommendations []*Recommendation       `json:"recommendations"`
}

// AIUsageReport AI使用报表
type AIUsageReport struct {
	ID          string             `json:"id"`
	Type        string             `json:"type"`
	TimeRange   string             `json:"time_range"`
	GeneratedAt time.Time          `json:"generated_at"`
	Statistics  *AIUsageStatistics `json:"statistics"`
	Summary     *ReportSummary     `json:"summary"`
	Charts      []*Chart           `json:"charts"`
	Insights    []*Insight         `json:"insights"`
}

// ReportSummary 报表摘要
type ReportSummary struct {
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	KeyMetrics  map[string]interface{} `json:"key_metrics"`
	Highlights  []string               `json:"highlights"`
	Concerns    []string               `json:"concerns"`
}

// Chart 图表
type Chart struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"` // line, bar, pie, area, scatter
	Title       string                 `json:"title"`
	Description string                 `json:"description"`
	Data        interface{}            `json:"data"`
	Options     map[string]interface{} `json:"options"`
	Width       int                    `json:"width"`
	Height      int                    `json:"height"`
}

// Insight 洞察
type Insight struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"` // trend, anomaly, recommendation
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Severity    string    `json:"severity"`
	Confidence  float64   `json:"confidence"`
	Timestamp   time.Time `json:"timestamp"`
}

// Recommendation 建议
type Recommendation struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Priority    string    `json:"priority"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Action      string    `json:"action"`
	Impact      string    `json:"impact"`
	Effort      string    `json:"effort"`
	Timestamp   time.Time `json:"timestamp"`
}

// Dashboard 仪表盘
type Dashboard struct {
	ID          string           `json:"id"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	Type        string           `json:"type"`
	Layout      *DashboardLayout `json:"layout"`
	Widgets     []*Widget        `json:"widgets"`
	Filters     []*Filter        `json:"filters"`
	RefreshRate time.Duration    `json:"refresh_rate"`
	CreatedAt   time.Time        `json:"created_at"`
	UpdatedAt   time.Time        `json:"updated_at"`
}

// DashboardLayout 仪表盘布局
type DashboardLayout struct {
	Type    string `json:"type"` // grid, flex, absolute
	Columns int    `json:"columns"`
	Rows    int    `json:"rows"`
	Gap     int    `json:"gap"`
}

// Widget 小部件
type Widget struct {
	ID          string                 `json:"id"`
	Type        string                 `json:"type"` // chart, metric, table, text
	Title       string                 `json:"title"`
	Position    *WidgetPosition        `json:"position"`
	Size        *WidgetSize            `json:"size"`
	Data        interface{}            `json:"data"`
	Config      map[string]interface{} `json:"config"`
	RefreshRate time.Duration          `json:"refresh_rate"`
}

// WidgetPosition 小部件位置
type WidgetPosition struct {
	X int `json:"x"`
	Y int `json:"y"`
}

// WidgetSize 小部件大小
type WidgetSize struct {
	Width  int `json:"width"`
	Height int `json:"height"`
}

// Filter 过滤器
type Filter struct {
	ID       string      `json:"id"`
	Name     string      `json:"name"`
	Type     string      `json:"type"`
	Value    interface{} `json:"value"`
	Options  []string    `json:"options"`
	Required bool        `json:"required"`
}

// StatisticsEntry 统计条目
type StatisticsEntry struct {
	ID        string                 `json:"id"`
	Type      string                 `json:"type"`
	Timestamp time.Time              `json:"timestamp"`
	Data      map[string]interface{} `json:"data"`
	Tags      map[string]string      `json:"tags"`
}

// AggregatedData 聚合数据
type AggregatedData struct {
	Type      string                 `json:"type"`
	TimeRange string                 `json:"time_range"`
	Data      map[string]interface{} `json:"data"`
	UpdatedAt time.Time              `json:"updated_at"`
}

// CachedReport 缓存报表
type CachedReport struct {
	Key       string      `json:"key"`
	Data      interface{} `json:"data"`
	CreatedAt time.Time   `json:"created_at"`
	ExpiresAt time.Time   `json:"expires_at"`
}

// ScheduledReportJob 计划报表任务
type ScheduledReportJob struct {
	ID         string     `json:"id"`
	Name       string     `json:"name"`
	Type       string     `json:"type"`
	Schedule   string     `json:"schedule"` // cron expression
	TimeRange  string     `json:"time_range"`
	Recipients []string   `json:"recipients"`
	Enabled    bool       `json:"enabled"`
	LastRun    *time.Time `json:"last_run,omitempty"`
	NextRun    time.Time  `json:"next_run"`
	CreatedAt  time.Time  `json:"created_at"`
}

// CronJob 定时任务
type CronJob struct {
	ID         string     `json:"id"`
	Expression string     `json:"expression"`
	Job        func()     `json:"-"`
	NextRun    time.Time  `json:"next_run"`
	LastRun    *time.Time `json:"last_run,omitempty"`
	Enabled    bool       `json:"enabled"`
}

// ReportExporter 报表导出器接口
type ReportExporter interface {
	ExportReport(reportID string) ([]byte, error)
	GetFormat() string
	GetMimeType() string
}

// ChartRenderer 图表渲染器接口
type ChartRenderer interface {
	RenderChart(data interface{}, options map[string]interface{}) (*Chart, error)
	GetChartType() string
}

// ReportTemplate 报表模板
type ReportTemplate struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Type      string                 `json:"type"`
	Sections  []string               `json:"sections"`
	Layout    map[string]interface{} `json:"layout"`
	Styles    map[string]interface{} `json:"styles"`
	CreatedAt time.Time              `json:"created_at"`
	UpdatedAt time.Time              `json:"updated_at"`
}

// ReportMetadata 报表元数据
type ReportMetadata struct {
	ID          string    `json:"id"`
	Type        string    `json:"type"`
	Title       string    `json:"title"`
	TimeRange   string    `json:"time_range"`
	GeneratedAt time.Time `json:"generated_at"`
	Size        int64     `json:"size"`
	Status      string    `json:"status"`
}

// NewReportSystem 创建统计报表系统
func NewReportSystem(config *ReportSystemConfig, logger *logrus.Logger) *ReportSystem {
	if config == nil {
		config = DefaultReportSystemConfig()
	}

	system := &ReportSystem{
		config:    config,
		logger:    logger,
		dataStore: NewStatisticsDataStore(config.DataRetentionDays, logger),
		stopChan:  make(chan struct{}),
		isRunning: false,
	}

	// 初始化子组件
	system.dataCollector = NewStatisticsDataCollector(logger)
	system.reportGenerator = NewReportGenerator(system.dataCollector, logger)
	system.chartGenerator = NewChartGenerator(logger)
	system.dashboardManager = NewDashboardManager(logger)

	if config.EnableCaching {
		system.cacheManager = NewReportCacheManager(config.CacheExpirationTime, logger)
	}

	if config.EnableScheduling {
		system.scheduler = NewReportScheduler(logger)
	}

	if config.EnableExport {
		system.exportManager = NewReportExportManager(config.ExportFormats, logger)
	}

	return system
}

// DefaultReportSystemConfig 默认报表系统配置
func DefaultReportSystemConfig() *ReportSystemConfig {
	return &ReportSystemConfig{
		EnableAutoGeneration:     true,
		EnableCaching:            true,
		EnableScheduling:         true,
		EnableExport:             true,
		DataRetentionDays:        30,
		CacheExpirationTime:      1 * time.Hour,
		ReportGenerationInterval: 1 * time.Hour,
		MaxConcurrentReports:     5,
		ExportFormats:            []string{"json", "csv", "pdf", "html"},
		DefaultTimeRange:         "24h",
		EnableRealTimeUpdates:    true,
		CompressionEnabled:       true,
	}
}

// Start 启动统计报表系统
func (rs *ReportSystem) Start(ctx context.Context) error {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	if rs.isRunning {
		return fmt.Errorf("report system is already running")
	}

	rs.logger.Info("Starting statistics report system")

	// 启动数据收集
	if err := rs.dataCollector.Start(ctx); err != nil {
		return fmt.Errorf("failed to start data collector: %w", err)
	}

	// 启动数据存储
	if err := rs.dataStore.Start(ctx); err != nil {
		return fmt.Errorf("failed to start data store: %w", err)
	}

	// 启动自动生成协程
	if rs.config.EnableAutoGeneration {
		go rs.autoGenerationRoutine(ctx)
	}

	// 启动调度器
	if rs.scheduler != nil {
		go rs.scheduler.Start(ctx)
	}

	// 启动清理协程
	go rs.cleanupRoutine(ctx)

	rs.isRunning = true
	rs.logger.Info("Statistics report system started successfully")

	return nil
}

// Stop 停止统计报表系统
func (rs *ReportSystem) Stop(ctx context.Context) error {
	rs.mutex.Lock()
	defer rs.mutex.Unlock()

	if !rs.isRunning {
		return nil
	}

	rs.logger.Info("Stopping statistics report system")

	close(rs.stopChan)
	rs.isRunning = false

	// 停止数据收集
	if err := rs.dataCollector.Stop(ctx); err != nil {
		rs.logger.WithError(err).Error("Failed to stop data collector")
	}

	// 停止数据存储
	if err := rs.dataStore.Stop(ctx); err != nil {
		rs.logger.WithError(err).Error("Failed to stop data store")
	}

	rs.logger.Info("Statistics report system stopped")
	return nil
}

// GenerateOperationReport 生成运维操作报表
func (rs *ReportSystem) GenerateOperationReport(ctx context.Context, timeRange string) (*OperationReport, error) {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	if !rs.isRunning {
		return nil, fmt.Errorf("report system is not running")
	}

	// 检查缓存
	if rs.cacheManager != nil {
		cacheKey := fmt.Sprintf("operation_report_%s", timeRange)
		if cached := rs.cacheManager.GetCachedReport(cacheKey); cached != nil {
			if report, ok := cached.Data.(*OperationReport); ok {
				return report, nil
			}
		}
	}

	rs.logger.WithField("time_range", timeRange).Info("Generating operation report")

	// 收集运维操作统计数据
	operationStats := rs.dataCollector.GetOperationStatistics(timeRange)

	// 生成报表
	report := &OperationReport{
		ID:          fmt.Sprintf("op_report_%d", time.Now().UnixNano()),
		Type:        "operation",
		TimeRange:   timeRange,
		GeneratedAt: time.Now(),
		Statistics:  operationStats,
		Summary:     rs.generateOperationSummary(operationStats),
		Charts:      rs.generateOperationCharts(operationStats),
		Insights:    rs.generateOperationInsights(operationStats),
	}

	// 缓存报表
	if rs.cacheManager != nil {
		cacheKey := fmt.Sprintf("operation_report_%s", timeRange)
		rs.cacheManager.CacheReport(cacheKey, report)
	}

	rs.logger.WithField("report_id", report.ID).Info("Operation report generated successfully")

	return report, nil
}

// GenerateSystemHealthReport 生成系统健康度报表
func (rs *ReportSystem) GenerateSystemHealthReport(ctx context.Context, timeRange string) (*SystemHealthReport, error) {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	if !rs.isRunning {
		return nil, fmt.Errorf("report system is not running")
	}

	// 检查缓存
	if rs.cacheManager != nil {
		cacheKey := fmt.Sprintf("health_report_%s", timeRange)
		if cached := rs.cacheManager.GetCachedReport(cacheKey); cached != nil {
			if report, ok := cached.Data.(*SystemHealthReport); ok {
				return report, nil
			}
		}
	}

	rs.logger.WithField("time_range", timeRange).Info("Generating system health report")

	// 收集系统健康度统计数据
	healthStats := rs.dataCollector.GetSystemHealthStatistics(timeRange)

	// 生成报表
	report := &SystemHealthReport{
		ID:              fmt.Sprintf("health_report_%d", time.Now().UnixNano()),
		Type:            "system_health",
		TimeRange:       timeRange,
		GeneratedAt:     time.Now(),
		Statistics:      healthStats,
		Summary:         rs.generateHealthSummary(healthStats),
		Charts:          rs.generateHealthCharts(healthStats),
		Recommendations: rs.generateHealthRecommendations(healthStats),
	}

	// 缓存报表
	if rs.cacheManager != nil {
		cacheKey := fmt.Sprintf("health_report_%s", timeRange)
		rs.cacheManager.CacheReport(cacheKey, report)
	}

	rs.logger.WithField("report_id", report.ID).Info("System health report generated successfully")

	return report, nil
}

// GenerateAIUsageReport 生成AI使用报表
func (rs *ReportSystem) GenerateAIUsageReport(ctx context.Context, timeRange string) (*AIUsageReport, error) {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	if !rs.isRunning {
		return nil, fmt.Errorf("report system is not running")
	}

	// 检查缓存
	if rs.cacheManager != nil {
		cacheKey := fmt.Sprintf("ai_usage_report_%s", timeRange)
		if cached := rs.cacheManager.GetCachedReport(cacheKey); cached != nil {
			if report, ok := cached.Data.(*AIUsageReport); ok {
				return report, nil
			}
		}
	}

	rs.logger.WithField("time_range", timeRange).Info("Generating AI usage report")

	// 收集AI使用统计数据
	aiStats := rs.dataCollector.GetAIUsageStatistics(timeRange)

	// 生成报表
	report := &AIUsageReport{
		ID:          fmt.Sprintf("ai_report_%d", time.Now().UnixNano()),
		Type:        "ai_usage",
		TimeRange:   timeRange,
		GeneratedAt: time.Now(),
		Statistics:  aiStats,
		Summary:     rs.generateAIUsageSummary(aiStats),
		Charts:      rs.generateAIUsageCharts(aiStats),
		Insights:    rs.generateAIUsageInsights(aiStats),
	}

	// 缓存报表
	if rs.cacheManager != nil {
		cacheKey := fmt.Sprintf("ai_usage_report_%s", timeRange)
		rs.cacheManager.CacheReport(cacheKey, report)
	}

	rs.logger.WithField("report_id", report.ID).Info("AI usage report generated successfully")

	return report, nil
}

// GenerateDashboard 生成仪表盘
func (rs *ReportSystem) GenerateDashboard(ctx context.Context, dashboardType string) (*Dashboard, error) {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	if !rs.isRunning {
		return nil, fmt.Errorf("report system is not running")
	}

	return rs.dashboardManager.GenerateDashboard(dashboardType)
}

// GetReportHistory 获取报表历史
func (rs *ReportSystem) GetReportHistory(reportType string, limit int) ([]*ReportMetadata, error) {
	return rs.dataStore.GetReportHistory(reportType, limit)
}

// ExportReport 导出报表
func (rs *ReportSystem) ExportReport(ctx context.Context, reportID string, format string) ([]byte, error) {
	if rs.exportManager == nil {
		return nil, fmt.Errorf("export manager is not available")
	}

	return rs.exportManager.ExportReport(reportID, format)
}

// 私有方法

// autoGenerationRoutine 自动生成协程
func (rs *ReportSystem) autoGenerationRoutine(ctx context.Context) {
	ticker := time.NewTicker(rs.config.ReportGenerationInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rs.stopChan:
			return
		case <-ticker.C:
			rs.generateScheduledReports(ctx)
		}
	}
}

// cleanupRoutine 清理协程
func (rs *ReportSystem) cleanupRoutine(ctx context.Context) {
	ticker := time.NewTicker(24 * time.Hour)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-rs.stopChan:
			return
		case <-ticker.C:
			rs.cleanupOldData()
		}
	}
}

// generateScheduledReports 生成计划报表
func (rs *ReportSystem) generateScheduledReports(ctx context.Context) {
	// 生成日报
	if _, err := rs.GenerateOperationReport(ctx, "24h"); err != nil {
		rs.logger.WithError(err).Error("Failed to generate daily operation report")
	}

	if _, err := rs.GenerateSystemHealthReport(ctx, "24h"); err != nil {
		rs.logger.WithError(err).Error("Failed to generate daily health report")
	}

	if _, err := rs.GenerateAIUsageReport(ctx, "24h"); err != nil {
		rs.logger.WithError(err).Error("Failed to generate daily AI usage report")
	}
}

// cleanupOldData 清理旧数据
func (rs *ReportSystem) cleanupOldData() {
	if err := rs.dataStore.Cleanup(); err != nil {
		rs.logger.WithError(err).Error("Failed to cleanup old data")
	}

	if rs.cacheManager != nil {
		rs.cacheManager.Cleanup()
	}
}

// generateOperationSummary 生成运维操作摘要
func (rs *ReportSystem) generateOperationSummary(stats *OperationStatistics) *ReportSummary {
	successRate := float64(stats.SuccessfulCommands) / float64(stats.TotalCommands) * 100

	return &ReportSummary{
		Title:       "运维操作统计摘要",
		Description: "系统运维操作的整体统计和分析",
		KeyMetrics: map[string]interface{}{
			"total_commands":    stats.TotalCommands,
			"success_rate":      fmt.Sprintf("%.1f%%", successRate),
			"average_exec_time": stats.AverageExecutionTime.String(),
		},
		Highlights: []string{
			fmt.Sprintf("总计执行 %d 条命令", stats.TotalCommands),
			fmt.Sprintf("成功率达到 %.1f%%", successRate),
			fmt.Sprintf("平均执行时间 %s", stats.AverageExecutionTime.String()),
		},
		Concerns: []string{
			fmt.Sprintf("失败命令数: %d", stats.FailedCommands),
		},
	}
}

// generateOperationCharts 生成运维操作图表
func (rs *ReportSystem) generateOperationCharts(stats *OperationStatistics) []*Chart {
	charts := []*Chart{
		{
			ID:          "command_success_rate",
			Type:        "pie",
			Title:       "命令执行成功率",
			Description: "成功与失败命令的比例分布",
			Data: map[string]interface{}{
				"successful": stats.SuccessfulCommands,
				"failed":     stats.FailedCommands,
			},
			Width:  400,
			Height: 300,
		},
		{
			ID:          "commands_by_hour",
			Type:        "line",
			Title:       "每小时命令执行量",
			Description: "24小时内命令执行的时间分布",
			Data:        stats.CommandsByHour,
			Width:       800,
			Height:      400,
		},
	}

	return charts
}

// generateOperationInsights 生成运维操作洞察
func (rs *ReportSystem) generateOperationInsights(stats *OperationStatistics) []*Insight {
	insights := []*Insight{
		{
			ID:          "peak_hour_insight",
			Type:        "trend",
			Title:       "高峰时段分析",
			Description: "识别出系统使用的高峰时段",
			Severity:    "info",
			Confidence:  0.9,
			Timestamp:   time.Now(),
		},
	}

	if stats.FailedCommands > stats.TotalCommands/10 {
		insights = append(insights, &Insight{
			ID:          "high_failure_rate",
			Type:        "anomaly",
			Title:       "命令失败率偏高",
			Description: "命令失败率超过10%，需要关注",
			Severity:    "warning",
			Confidence:  0.8,
			Timestamp:   time.Now(),
		})
	}

	return insights
}

// generateHealthSummary 生成健康度摘要
func (rs *ReportSystem) generateHealthSummary(stats *SystemHealthStatistics) *ReportSummary {
	return &ReportSummary{
		Title:       "系统健康度摘要",
		Description: "系统整体健康状况的综合评估",
		KeyMetrics: map[string]interface{}{
			"overall_health": fmt.Sprintf("%.1f", stats.OverallHealthScore),
			"cpu_health":     fmt.Sprintf("%.1f", stats.CPUHealthScore),
			"memory_health":  fmt.Sprintf("%.1f", stats.MemoryHealthScore),
		},
		Highlights: []string{
			fmt.Sprintf("系统整体健康度: %.1f", stats.OverallHealthScore),
			fmt.Sprintf("CPU健康度: %.1f", stats.CPUHealthScore),
			fmt.Sprintf("内存健康度: %.1f", stats.MemoryHealthScore),
		},
		Concerns: []string{},
	}
}

// generateHealthCharts 生成健康度图表
func (rs *ReportSystem) generateHealthCharts(stats *SystemHealthStatistics) []*Chart {
	return []*Chart{
		{
			ID:          "health_scores",
			Type:        "bar",
			Title:       "系统健康度评分",
			Description: "各子系统的健康度评分对比",
			Data: map[string]interface{}{
				"CPU":     stats.CPUHealthScore,
				"Memory":  stats.MemoryHealthScore,
				"Disk":    stats.DiskHealthScore,
				"Network": stats.NetworkHealthScore,
				"Service": stats.ServiceHealthScore,
			},
			Width:  600,
			Height: 400,
		},
	}
}

// generateHealthRecommendations 生成健康度建议
func (rs *ReportSystem) generateHealthRecommendations(stats *SystemHealthStatistics) []*Recommendation {
	recommendations := []*Recommendation{}

	if stats.CPUHealthScore < 70 {
		recommendations = append(recommendations, &Recommendation{
			ID:          "cpu_optimization",
			Type:        "performance",
			Priority:    "high",
			Title:       "CPU性能优化",
			Description: "CPU健康度偏低，建议进行性能优化",
			Action:      "检查CPU使用率高的进程，考虑优化或扩容",
			Impact:      "提升系统响应速度",
			Effort:      "medium",
			Timestamp:   time.Now(),
		})
	}

	return recommendations
}

// generateAIUsageSummary 生成AI使用摘要
func (rs *ReportSystem) generateAIUsageSummary(stats *AIUsageStatistics) *ReportSummary {
	return &ReportSummary{
		Title:       "AI助手使用摘要",
		Description: "AI助手的使用情况和效果分析",
		KeyMetrics: map[string]interface{}{
			"total_conversations": stats.TotalConversations,
			"total_messages":      stats.TotalMessages,
			"avg_session_length":  stats.AverageSessionLength.String(),
		},
		Highlights: []string{
			fmt.Sprintf("总对话数: %d", stats.TotalConversations),
			fmt.Sprintf("总消息数: %d", stats.TotalMessages),
			fmt.Sprintf("平均会话时长: %s", stats.AverageSessionLength.String()),
		},
		Concerns: []string{},
	}
}

// generateAIUsageCharts 生成AI使用图表
func (rs *ReportSystem) generateAIUsageCharts(stats *AIUsageStatistics) []*Chart {
	return []*Chart{
		{
			ID:          "usage_by_hour",
			Type:        "line",
			Title:       "每小时AI使用量",
			Description: "24小时内AI助手的使用分布",
			Data:        stats.UsageByHour,
			Width:       800,
			Height:      400,
		},
	}
}

// generateAIUsageInsights 生成AI使用洞察
func (rs *ReportSystem) generateAIUsageInsights(stats *AIUsageStatistics) []*Insight {
	return []*Insight{
		{
			ID:          "usage_trend",
			Type:        "trend",
			Title:       "使用趋势分析",
			Description: "AI助手使用呈现稳定增长趋势",
			Severity:    "info",
			Confidence:  0.85,
			Timestamp:   time.Now(),
		},
	}
}

// 子组件构造函数

// NewStatisticsDataStore 创建统计数据存储
func NewStatisticsDataStore(retentionDays int, logger *logrus.Logger) *StatisticsDataStore {
	return &StatisticsDataStore{
		logger:     logger,
		data:       make(map[string][]*StatisticsEntry),
		aggregated: make(map[string]*AggregatedData),
		retention:  time.Duration(retentionDays) * 24 * time.Hour,
		maxSize:    500 * 1024 * 1024, // 500MB
	}
}

// NewStatisticsDataCollector 创建统计数据收集器
func NewStatisticsDataCollector(logger *logrus.Logger) *StatisticsDataCollector {
	return &StatisticsDataCollector{
		logger:            logger,
		operationStats:    &OperationStatistics{},
		systemHealthStats: &SystemHealthStatistics{},
		aiUsageStats:      &AIUsageStatistics{},
		userActivityStats: &UserActivityStatistics{},
		performanceStats:  &PerformanceStatistics{},
	}
}

// NewReportGenerator 创建报表生成器
func NewReportGenerator(dataCollector *StatisticsDataCollector, logger *logrus.Logger) *ReportGenerator {
	return &ReportGenerator{
		logger:        logger,
		dataCollector: dataCollector,
		templates:     make(map[string]*ReportTemplate),
	}
}

// NewChartGenerator 创建图表生成器
func NewChartGenerator(logger *logrus.Logger) *ChartGenerator {
	return &ChartGenerator{
		logger:     logger,
		chartTypes: make(map[string]ChartRenderer),
	}
}

// NewDashboardManager 创建仪表盘管理器
func NewDashboardManager(logger *logrus.Logger) *DashboardManager {
	return &DashboardManager{
		logger:     logger,
		dashboards: make(map[string]*Dashboard),
		widgets:    make(map[string]*Widget),
		layouts:    make(map[string]*DashboardLayout),
	}
}

// NewReportCacheManager 创建报表缓存管理器
func NewReportCacheManager(expiration time.Duration, logger *logrus.Logger) *ReportCacheManager {
	return &ReportCacheManager{
		logger:     logger,
		cache:      make(map[string]*CachedReport),
		expiration: expiration,
		maxSize:    100,
	}
}

// NewReportScheduler 创建报表调度器
func NewReportScheduler(logger *logrus.Logger) *ReportScheduler {
	return &ReportScheduler{
		logger:        logger,
		scheduledJobs: make(map[string]*ScheduledReportJob),
		cronJobs:      make(map[string]*CronJob),
	}
}

// NewReportExportManager 创建报表导出管理器
func NewReportExportManager(formats []string, logger *logrus.Logger) *ReportExportManager {
	manager := &ReportExportManager{
		logger:    logger,
		exporters: make(map[string]ReportExporter),
	}

	// 注册默认导出器
	for _, format := range formats {
		switch format {
		case "json":
			manager.exporters[format] = &JSONReportExporter{}
		case "csv":
			manager.exporters[format] = &CSVReportExporter{}
		case "pdf":
			manager.exporters[format] = &PDFReportExporter{}
		case "html":
			manager.exporters[format] = &HTMLReportExporter{}
		}
	}

	return manager
}

// 数据存储方法

// Start 启动数据存储
func (sds *StatisticsDataStore) Start(ctx context.Context) error {
	sds.logger.Info("Starting statistics data store")
	return nil
}

// Stop 停止数据存储
func (sds *StatisticsDataStore) Stop(ctx context.Context) error {
	sds.logger.Info("Stopping statistics data store")
	return nil
}

// Cleanup 清理过期数据
func (sds *StatisticsDataStore) Cleanup() error {
	sds.mutex.Lock()
	defer sds.mutex.Unlock()

	cutoffTime := time.Now().Add(-sds.retention)
	var removedCount int64

	for dataType, entries := range sds.data {
		var filteredEntries []*StatisticsEntry

		for _, entry := range entries {
			if entry.Timestamp.After(cutoffTime) {
				filteredEntries = append(filteredEntries, entry)
			} else {
				removedCount++
			}
		}

		sds.data[dataType] = filteredEntries
	}

	sds.logger.WithFields(logrus.Fields{
		"removed_count": removedCount,
		"cutoff_time":   cutoffTime,
	}).Info("Statistics data cleanup completed")

	return nil
}

// GetReportHistory 获取报表历史
func (sds *StatisticsDataStore) GetReportHistory(reportType string, limit int) ([]*ReportMetadata, error) {
	// 简化实现：返回模拟数据
	history := []*ReportMetadata{
		{
			ID:          "report_001",
			Type:        reportType,
			Title:       fmt.Sprintf("%s Report", reportType),
			TimeRange:   "24h",
			GeneratedAt: time.Now().Add(-24 * time.Hour),
			Size:        1024 * 50, // 50KB
			Status:      "completed",
		},
		{
			ID:          "report_002",
			Type:        reportType,
			Title:       fmt.Sprintf("%s Report", reportType),
			TimeRange:   "24h",
			GeneratedAt: time.Now().Add(-48 * time.Hour),
			Size:        1024 * 45, // 45KB
			Status:      "completed",
		},
	}

	if limit > 0 && limit < len(history) {
		history = history[:limit]
	}

	return history, nil
}

// 数据收集器方法

// Start 启动数据收集器
func (sdc *StatisticsDataCollector) Start(ctx context.Context) error {
	sdc.logger.Info("Starting statistics data collector")
	return nil
}

// Stop 停止数据收集器
func (sdc *StatisticsDataCollector) Stop(ctx context.Context) error {
	sdc.logger.Info("Stopping statistics data collector")
	return nil
}

// GetOperationStatistics 获取运维操作统计
func (sdc *StatisticsDataCollector) GetOperationStatistics(timeRange string) *OperationStatistics {
	sdc.mutex.RLock()
	defer sdc.mutex.RUnlock()

	// 简化实现：返回模拟数据
	return &OperationStatistics{
		TotalCommands:      1000,
		SuccessfulCommands: 950,
		FailedCommands:     50,
		CommandsByType: map[string]int64{
			"ls":   200,
			"cd":   150,
			"cat":  100,
			"grep": 80,
			"find": 60,
		},
		CommandsByUser: map[string]int64{
			"admin":     400,
			"operator":  300,
			"developer": 300,
		},
		CommandsByHour: map[int]int64{
			9:  80,
			10: 120,
			11: 100,
			14: 150,
			15: 130,
			16: 110,
		},
		AverageExecutionTime: 2 * time.Second,
		TopCommands: []CommandFrequency{
			{Command: "ls", Count: 200, Percentage: 20.0, LastUsed: time.Now()},
			{Command: "cd", Count: 150, Percentage: 15.0, LastUsed: time.Now()},
			{Command: "cat", Count: 100, Percentage: 10.0, LastUsed: time.Now()},
		},
		ErrorPatterns: []ErrorPattern{
			{
				Pattern:     "permission denied",
				Count:       15,
				FirstSeen:   time.Now().Add(-24 * time.Hour),
				LastSeen:    time.Now().Add(-1 * time.Hour),
				Severity:    "medium",
				Description: "权限拒绝错误",
			},
		},
		RiskDistribution: map[string]int64{
			"low":      800,
			"medium":   150,
			"high":     40,
			"critical": 10,
		},
		Timestamp: time.Now(),
	}
}

// GetSystemHealthStatistics 获取系统健康度统计
func (sdc *StatisticsDataCollector) GetSystemHealthStatistics(timeRange string) *SystemHealthStatistics {
	sdc.mutex.RLock()
	defer sdc.mutex.RUnlock()

	// 简化实现：返回模拟数据
	return &SystemHealthStatistics{
		OverallHealthScore: 85.5,
		CPUHealthScore:     88.0,
		MemoryHealthScore:  82.0,
		DiskHealthScore:    90.0,
		NetworkHealthScore: 85.0,
		ServiceHealthScore: 87.0,
		HealthTrends: &HealthTrends{
			HourlyScores:   []float64{85.0, 86.0, 84.0, 85.5, 87.0},
			DailyScores:    []float64{84.0, 85.0, 86.0, 85.5},
			WeeklyScores:   []float64{83.0, 84.0, 85.0, 85.5},
			TrendDirection: "stable",
			ChangeRate:     0.02,
		},
		AlertStatistics: &AlertStatistics{
			TotalAlerts:    25,
			ActiveAlerts:   3,
			ResolvedAlerts: 22,
			AlertsBySeverity: map[string]int64{
				"low":      15,
				"medium":   8,
				"high":     2,
				"critical": 0,
			},
			AlertsByType: map[string]int64{
				"system":      10,
				"application": 8,
				"network":     7,
			},
			MTTR: 2 * time.Hour,
			MTTD: 5 * time.Minute,
		},
		UptimeStatistics: &UptimeStatistics{
			TotalUptime:    720 * time.Hour, // 30 days
			UptimePercent:  99.5,
			DowntimeEvents: 2,
			MTBF:           360 * time.Hour, // 15 days
		},
		PerformanceMetrics: &PerformanceMetrics{
			ResponseTime:  100 * time.Millisecond,
			Throughput:    1000.0,
			ErrorRate:     0.01,
			ResourceUsage: 65.0,
			HealthScore:   85.5,
		},
		Timestamp: time.Now(),
	}
}

// GetAIUsageStatistics 获取AI使用统计
func (sdc *StatisticsDataCollector) GetAIUsageStatistics(timeRange string) *AIUsageStatistics {
	sdc.mutex.RLock()
	defer sdc.mutex.RUnlock()

	// 简化实现：返回模拟数据
	return &AIUsageStatistics{
		TotalConversations:   500,
		TotalMessages:        2500,
		AverageSessionLength: 15 * time.Minute,
		TopQuestions: []QuestionFrequency{
			{Question: "如何查看系统状态", Count: 50, Category: "system", LastAsked: time.Now()},
			{Question: "如何重启服务", Count: 40, Category: "operation", LastAsked: time.Now()},
			{Question: "如何查看日志", Count: 35, Category: "troubleshooting", LastAsked: time.Now()},
		},
		ToolUsageStats: map[string]int64{
			"command_execution": 800,
			"file_operations":   600,
			"system_monitoring": 400,
			"log_analysis":      300,
		},
		UserSatisfaction: &SatisfactionMetrics{
			AverageRating: 4.2,
			RatingDistribution: map[int]int64{
				5: 200,
				4: 180,
				3: 80,
				2: 30,
				1: 10,
			},
			PositiveFeedback: 380,
			NegativeFeedback: 40,
			TotalFeedback:    500,
		},
		ResponseTimeStats: &ResponseTimeStatistics{
			AverageTime: 2 * time.Second,
			MedianTime:  1500 * time.Millisecond,
			P95Time:     5 * time.Second,
			P99Time:     10 * time.Second,
			MinTime:     500 * time.Millisecond,
			MaxTime:     30 * time.Second,
		},
		ErrorRateStats: &ErrorRateStatistics{
			OverallErrorRate: 0.02,
			ErrorsByType: map[string]int64{
				"timeout":         15,
				"invalid_command": 10,
				"permission":      8,
				"network":         5,
			},
			ErrorTrend: []ErrorTrendPoint{
				{Timestamp: time.Now().Add(-24 * time.Hour), ErrorRate: 0.025, Count: 20},
				{Timestamp: time.Now().Add(-12 * time.Hour), ErrorRate: 0.020, Count: 15},
				{Timestamp: time.Now(), ErrorRate: 0.015, Count: 12},
			},
		},
		UsageByHour: map[int]int64{
			9:  50,
			10: 80,
			11: 70,
			14: 100,
			15: 90,
			16: 75,
		},
		UsageByUser: map[string]int64{
			"admin":     200,
			"operator":  180,
			"developer": 120,
		},
		Timestamp: time.Now(),
	}
}

// 缓存管理器方法

// GetCachedReport 获取缓存报表
func (rcm *ReportCacheManager) GetCachedReport(key string) *CachedReport {
	rcm.mutex.RLock()
	defer rcm.mutex.RUnlock()

	cached, exists := rcm.cache[key]
	if !exists {
		return nil
	}

	// 检查是否过期
	if time.Now().After(cached.ExpiresAt) {
		delete(rcm.cache, key)
		return nil
	}

	return cached
}

// CacheReport 缓存报表
func (rcm *ReportCacheManager) CacheReport(key string, data interface{}) {
	rcm.mutex.Lock()
	defer rcm.mutex.Unlock()

	// 检查缓存大小限制
	if len(rcm.cache) >= rcm.maxSize {
		rcm.evictOldest()
	}

	cached := &CachedReport{
		Key:       key,
		Data:      data,
		CreatedAt: time.Now(),
		ExpiresAt: time.Now().Add(rcm.expiration),
	}

	rcm.cache[key] = cached
}

// Cleanup 清理过期缓存
func (rcm *ReportCacheManager) Cleanup() {
	rcm.mutex.Lock()
	defer rcm.mutex.Unlock()

	now := time.Now()
	for key, cached := range rcm.cache {
		if now.After(cached.ExpiresAt) {
			delete(rcm.cache, key)
		}
	}
}

// evictOldest 驱逐最旧的缓存
func (rcm *ReportCacheManager) evictOldest() {
	var oldestKey string
	var oldestTime time.Time

	for key, cached := range rcm.cache {
		if oldestTime.IsZero() || cached.CreatedAt.Before(oldestTime) {
			oldestKey = key
			oldestTime = cached.CreatedAt
		}
	}

	if oldestKey != "" {
		delete(rcm.cache, oldestKey)
	}
}

// 调度器方法

// Start 启动调度器
func (rs *ReportScheduler) Start(ctx context.Context) {
	rs.logger.Info("Starting report scheduler")
	// 简化实现：调度器启动逻辑
}

// 仪表盘管理器方法

// GenerateDashboard 生成仪表盘
func (dm *DashboardManager) GenerateDashboard(dashboardType string) (*Dashboard, error) {
	dm.mutex.RLock()
	defer dm.mutex.RUnlock()

	// 简化实现：返回模拟仪表盘
	dashboard := &Dashboard{
		ID:          fmt.Sprintf("dashboard_%s", dashboardType),
		Name:        fmt.Sprintf("%s Dashboard", dashboardType),
		Description: fmt.Sprintf("Dashboard for %s monitoring", dashboardType),
		Type:        dashboardType,
		Layout: &DashboardLayout{
			Type:    "grid",
			Columns: 3,
			Rows:    2,
			Gap:     10,
		},
		Widgets: []*Widget{
			{
				ID:          "widget_1",
				Type:        "chart",
				Title:       "System Health",
				Position:    &WidgetPosition{X: 0, Y: 0},
				Size:        &WidgetSize{Width: 1, Height: 1},
				Data:        map[string]interface{}{"health_score": 85.5},
				RefreshRate: 30 * time.Second,
			},
			{
				ID:          "widget_2",
				Type:        "metric",
				Title:       "Total Commands",
				Position:    &WidgetPosition{X: 1, Y: 0},
				Size:        &WidgetSize{Width: 1, Height: 1},
				Data:        map[string]interface{}{"value": 1000},
				RefreshRate: 1 * time.Minute,
			},
		},
		RefreshRate: 30 * time.Second,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}

	return dashboard, nil
}

// 导出管理器方法

// ExportReport 导出报表
func (rem *ReportExportManager) ExportReport(reportID string, format string) ([]byte, error) {
	rem.mutex.RLock()
	exporter, exists := rem.exporters[format]
	rem.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("unsupported export format: %s", format)
	}

	return exporter.ExportReport(reportID)
}

// 导出器实现

// JSONReportExporter JSON报表导出器
type JSONReportExporter struct{}

// ExportReport 导出为JSON格式
func (jre *JSONReportExporter) ExportReport(reportID string) ([]byte, error) {
	// 简化实现：返回模拟JSON数据
	report := map[string]interface{}{
		"id":           reportID,
		"type":         "operation",
		"generated_at": time.Now(),
		"data": map[string]interface{}{
			"total_commands": 1000,
			"success_rate":   95.0,
		},
	}

	return json.Marshal(report)
}

// GetFormat 获取格式
func (jre *JSONReportExporter) GetFormat() string {
	return "json"
}

// GetMimeType 获取MIME类型
func (jre *JSONReportExporter) GetMimeType() string {
	return "application/json"
}

// CSVReportExporter CSV报表导出器
type CSVReportExporter struct{}

// ExportReport 导出为CSV格式
func (cre *CSVReportExporter) ExportReport(reportID string) ([]byte, error) {
	// 简化实现：返回模拟CSV数据
	csv := "Metric,Value\n"
	csv += "Total Commands,1000\n"
	csv += "Success Rate,95.0\n"
	csv += "Failed Commands,50\n"

	return []byte(csv), nil
}

// GetFormat 获取格式
func (cre *CSVReportExporter) GetFormat() string {
	return "csv"
}

// GetMimeType 获取MIME类型
func (cre *CSVReportExporter) GetMimeType() string {
	return "text/csv"
}

// PDFReportExporter PDF报表导出器
type PDFReportExporter struct{}

// ExportReport 导出为PDF格式
func (pre *PDFReportExporter) ExportReport(reportID string) ([]byte, error) {
	// 简化实现：返回模拟PDF数据
	pdfContent := fmt.Sprintf("%%PDF-1.4\nReport ID: %s\nGenerated: %s\nContent: Statistics Report\n",
		reportID,
		time.Now().Format("2006-01-02 15:04:05"),
	)

	return []byte(pdfContent), nil
}

// GetFormat 获取格式
func (pre *PDFReportExporter) GetFormat() string {
	return "pdf"
}

// GetMimeType 获取MIME类型
func (pre *PDFReportExporter) GetMimeType() string {
	return "application/pdf"
}

// HTMLReportExporter HTML报表导出器
type HTMLReportExporter struct{}

// ExportReport 导出为HTML格式
func (hre *HTMLReportExporter) ExportReport(reportID string) ([]byte, error) {
	// 简化实现：返回模拟HTML数据
	html := fmt.Sprintf(`
<!DOCTYPE html>
<html>
<head>
    <title>Statistics Report - %s</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background-color: #2E86AB; color: white; padding: 20px; }
        .metric { margin: 10px 0; padding: 10px; background-color: #f5f5f5; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Statistics Report</h1>
        <p>Report ID: %s</p>
        <p>Generated: %s</p>
    </div>

    <div class="metric">
        <h3>Total Commands: 1000</h3>
    </div>

    <div class="metric">
        <h3>Success Rate: 95.0%%</h3>
    </div>

    <div class="metric">
        <h3>Failed Commands: 50</h3>
    </div>
</body>
</html>`,
		reportID,
		reportID,
		time.Now().Format("2006-01-02 15:04:05"),
	)

	return []byte(html), nil
}

// GetFormat 获取格式
func (hre *HTMLReportExporter) GetFormat() string {
	return "html"
}

// GetMimeType 获取MIME类型
func (hre *HTMLReportExporter) GetMimeType() string {
	return "text/html"
}

// GetReportSystemStatus 获取报表系统状态
func (rs *ReportSystem) GetReportSystemStatus() *ReportSystemStatus {
	rs.mutex.RLock()
	defer rs.mutex.RUnlock()

	status := &ReportSystemStatus{
		IsRunning:             rs.isRunning,
		Config:                rs.config,
		TotalReportsGenerated: 100, // 简化实现
		CacheHitRate:          0.85,
		AverageGenerationTime: 2 * time.Second,
		LastGenerationTime:    time.Now().Add(-1 * time.Hour),
		ActiveDashboards:      3,
		ScheduledJobs:         5,
	}

	return status
}

// ReportSystemStatus 报表系统状态
type ReportSystemStatus struct {
	IsRunning             bool                `json:"is_running"`
	Config                *ReportSystemConfig `json:"config"`
	TotalReportsGenerated int64               `json:"total_reports_generated"`
	CacheHitRate          float64             `json:"cache_hit_rate"`
	AverageGenerationTime time.Duration       `json:"average_generation_time"`
	LastGenerationTime    time.Time           `json:"last_generation_time"`
	ActiveDashboards      int                 `json:"active_dashboards"`
	ScheduledJobs         int                 `json:"scheduled_jobs"`
}
