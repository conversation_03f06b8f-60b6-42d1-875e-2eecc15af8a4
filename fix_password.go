package main

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"log"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
)

// Host 主机模型
type Host struct {
	ID                int64  `json:"id" gorm:"primaryKey;autoIncrement"`
	Name              string `json:"name" gorm:"uniqueIndex;size:100;not null"`
	IPAddress         string `json:"ip_address" gorm:"size:45;not null"`
	Port              int    `json:"port" gorm:"not null;default:22"`
	Username          string `json:"username" gorm:"size:50;not null"`
	PasswordEncrypted string `json:"-" gorm:"type:text"`
	Status            string `json:"status" gorm:"size:20;not null;default:unknown"`
}

func main() {
	// 连接数据库
	db, err := gorm.Open(sqlite.Open("data/aiops.db"), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 正确的密码和加密密钥
	correctPassword := "1qaz#EDC"
	encryptionKey := "aiops-dev-encryption-key-32byte!"

	fmt.Printf("🔧 修复**************的密码\n")
	fmt.Printf("正确密码: %s\n", correctPassword)
	fmt.Printf("加密密钥: %s (长度: %d)\n", encryptionKey, len(encryptionKey))

	// 加密正确的密码
	encryptedPassword, err := encryptData(correctPassword, encryptionKey)
	if err != nil {
		log.Fatal("❌ 加密失败:", err)
	}

	fmt.Printf("✅ 加密成功: %s\n", encryptedPassword)

	// 更新数据库
	result := db.Model(&Host{}).Where("ip_address = ?", "**************").Update("password_encrypted", encryptedPassword)
	if result.Error != nil {
		log.Fatal("❌ 数据库更新失败:", result.Error)
	}

	fmt.Printf("✅ 数据库更新成功，影响行数: %d\n", result.RowsAffected)

	// 验证解密
	decrypted, err := decryptData(encryptedPassword, encryptionKey)
	if err != nil {
		fmt.Printf("❌ 解密验证失败: %v\n", err)
	} else {
		fmt.Printf("✅ 解密验证成功: %s\n", decrypted)
	}

	// 查询更新后的主机信息
	var host Host
	if err := db.Where("ip_address = ?", "**************").First(&host).Error; err != nil {
		log.Fatal("❌ 查询主机失败:", err)
	}

	fmt.Printf("\n📋 主机信息:\n")
	fmt.Printf("ID: %d\n", host.ID)
	fmt.Printf("名称: %s\n", host.Name)
	fmt.Printf("IP: %s\n", host.IPAddress)
	fmt.Printf("端口: %d\n", host.Port)
	fmt.Printf("用户名: %s\n", host.Username)
	fmt.Printf("状态: %s\n", host.Status)

	fmt.Printf("\n🎉 密码修复完成！现在可以重新测试SSH连接了。\n")
}

// encryptData 加密数据
func encryptData(data, key string) (string, error) {
	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonce := make([]byte, aead.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return "", err
	}

	ciphertext := aead.Seal(nonce, nonce, []byte(data), nil)
	return base64.StdEncoding.EncodeToString(ciphertext), nil
}

// decryptData 解密数据
func decryptData(encryptedData, key string) (string, error) {
	data, err := base64.StdEncoding.DecodeString(encryptedData)
	if err != nil {
		return "", err
	}

	block, err := aes.NewCipher([]byte(key))
	if err != nil {
		return "", err
	}

	aead, err := cipher.NewGCM(block)
	if err != nil {
		return "", err
	}

	nonceSize := aead.NonceSize()
	if len(data) < nonceSize {
		return "", fmt.Errorf("ciphertext too short")
	}

	nonce, ciphertext := data[:nonceSize], data[nonceSize:]
	plaintext, err := aead.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return "", err
	}

	return string(plaintext), nil
}
