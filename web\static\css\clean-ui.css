/* AI运维管理平台 - 简洁UI优化 */
/* 专注于整洁、干净的用户界面 */

/* ========================================
   全局优化
   ======================================== */

/* 减少视觉噪音 - 更温和的方式 */
.particle-effect,
.animated-gradient,
.neon-glow,
.text-glow {
  display: none;
}

/* 简化特定组件的阴影 */
.settings-panel,
.notification-toast,
.dropdown-menu {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 保持核心布局组件的原有样式 */
.app-container,
.app-main,
.sidebar,
.assistant-panel,
.chat-main {
  /* 不强制覆盖这些核心布局组件 */
}

/* ========================================
   色彩简化
   ======================================== */

/* 不重新定义CSS变量，使用现有的设计系统 */

/* ========================================
   布局优化
   ======================================== */

/* 温和的间距调整 */
.settings-panel .setting-group {
  padding: 1.5rem;
}

/* 简化间距 */
.setting-item {
  padding: 1rem 0;
  border-bottom: 1px solid var(--border-secondary);
}

.setting-item:last-child {
  border-bottom: none;
}

/* ========================================
   组件简化
   ======================================== */

/* 保持原有的按钮和表单样式 */

/* ========================================
   消息样式优化
   ======================================== */

/* 保持原有的消息样式 */

/* ========================================
   导航优化
   ======================================== */

/* 保持原有的导航和侧边栏样式 */

/* ========================================
   设置面板优化
   ======================================== */

.settings-panel {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.settings-header {
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-secondary);
  background: var(--bg-secondary);
}

.settings-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.setting-group {
  padding: 1.5rem;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 0;
}

.setting-label {
  font-weight: 500;
  color: var(--text-primary);
}

/* ========================================
   状态卡片优化
   ======================================== */

.status-card {
  background: var(--bg-primary);
  border: 1px solid var(--border-primary);
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 1rem;
}

.status-header {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;
  font-weight: 600;
  color: var(--text-primary);
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 0;
  border-bottom: 1px solid var(--border-secondary);
}

.status-item:last-child {
  border-bottom: none;
}

.status-label {
  color: var(--text-secondary);
  font-size: 0.875rem;
}

.status-value {
  color: var(--text-primary);
  font-weight: 600;
  font-size: 0.875rem;
}

/* ========================================
   响应式优化
   ======================================== */

@media (max-width: 768px) {
  .sidebar-content,
  .panel-content,
  .chat-container {
    padding: 1rem !important;
  }
  
  .message {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
  
  .setting-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}

/* ========================================
   动画禁用
   ======================================== */

/* 禁用所有不必要的动画 */
.particles-container,
.animated-gradient,
.card-float,
.text-glow,
.neon-glow,
.magnetic-hover {
  display: none !important;
  animation: none !important;
}

/* 保留必要的过渡效果 */
.btn,
.form-control,
.sidebar,
.settings-panel {
  transition: all 0.2s ease !important;
}

/* ========================================
   打印样式
   ======================================== */

@media print {
  .sidebar,
  .assistant-panel,
  .settings-panel,
  .app-navbar {
    display: none !important;
  }
  
  .chat-main {
    width: 100% !important;
    margin: 0 !important;
  }
}
