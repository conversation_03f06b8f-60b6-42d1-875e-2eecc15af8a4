package service

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"
	"time"

	"aiops-platform/internal/model"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ContextManager 上下文管理器
type ContextManager struct {
	db     *gorm.DB
	logger *logrus.Logger
	cache  map[string]*SessionContext
	mutex  sync.RWMutex
}

// SessionContext 会话上下文
type SessionContext struct {
	SessionID    string                 `json:"session_id"`
	UserID       int64                  `json:"user_id"`
	Messages     []ContextMessage       `json:"messages"`
	Variables    map[string]interface{} `json:"variables"`
	LastActivity time.Time              `json:"last_activity"`
	MaxMessages  int                    `json:"max_messages"`
}

// ContextMessage 上下文消息
type ContextMessage struct {
	Role      string                 `json:"role"`
	Content   string                 `json:"content"`
	Timestamp time.Time              `json:"timestamp"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// NewContextManager 创建上下文管理器
func NewContextManager(db *gorm.DB, logger *logrus.Logger) *ContextManager {
	return &ContextManager{
		db:     db,
		logger: logger,
		cache:  make(map[string]*SessionContext),
		mutex:  sync.RWMutex{},
	}
}

// GetContext 获取会话上下文
func (cm *ContextManager) GetContext(sessionID string) (*SessionContext, error) {
	cm.mutex.RLock()
	if ctx, exists := cm.cache[sessionID]; exists {
		cm.mutex.RUnlock()
		return ctx, nil
	}
	cm.mutex.RUnlock()

	// 从数据库加载
	var session model.ChatSession
	if err := cm.db.Preload("Messages").Where("session_id = ?", sessionID).First(&session).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, fmt.Errorf("session not found: %s", sessionID)
		}
		return nil, fmt.Errorf("failed to load session: %w", err)
	}

	// 构建上下文
	ctx := &SessionContext{
		SessionID:    sessionID,
		UserID:       session.UserID,
		Messages:     make([]ContextMessage, 0),
		Variables:    make(map[string]interface{}),
		LastActivity: session.LastActivity,
		MaxMessages:  50, // 默认保留最近50条消息
	}

	// 加载消息历史
	for _, msg := range session.Messages {
		contextMsg := ContextMessage{
			Role:      msg.MessageType,
			Content:   msg.Content,
			Timestamp: msg.CreatedAt,
		}

		// 加载元数据
		if msg.ExtractedParams != "" {
			var params map[string]interface{}
			if err := json.Unmarshal([]byte(msg.ExtractedParams), &params); err == nil {
				contextMsg.Metadata = params
			}
		}

		ctx.Messages = append(ctx.Messages, contextMsg)
	}

	// 加载上下文数据
	if session.ContextData != "" {
		if err := json.Unmarshal([]byte(session.ContextData), &ctx.Variables); err != nil {
			cm.logger.WithError(err).Warn("Failed to load context variables")
		}
	}

	// 缓存上下文
	cm.mutex.Lock()
	cm.cache[sessionID] = ctx
	cm.mutex.Unlock()

	return ctx, nil
}

// AddMessage 添加消息到上下文
func (cm *ContextManager) AddMessage(sessionID string, role, content string, metadata map[string]interface{}) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	ctx, exists := cm.cache[sessionID]
	if !exists {
		return fmt.Errorf("session context not found: %s", sessionID)
	}

	// 添加新消息
	msg := ContextMessage{
		Role:      role,
		Content:   content,
		Timestamp: time.Now(),
		Metadata:  metadata,
	}

	ctx.Messages = append(ctx.Messages, msg)
	ctx.LastActivity = time.Now()

	// 限制消息数量
	if len(ctx.Messages) > ctx.MaxMessages {
		ctx.Messages = ctx.Messages[len(ctx.Messages)-ctx.MaxMessages:]
	}

	return nil
}

// SetVariable 设置上下文变量
func (cm *ContextManager) SetVariable(sessionID, key string, value interface{}) error {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	ctx, exists := cm.cache[sessionID]
	if !exists {
		return fmt.Errorf("session context not found: %s", sessionID)
	}

	ctx.Variables[key] = value
	ctx.LastActivity = time.Now()

	return nil
}

// GetVariable 获取上下文变量
func (cm *ContextManager) GetVariable(sessionID, key string) (interface{}, bool) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	ctx, exists := cm.cache[sessionID]
	if !exists {
		return nil, false
	}

	value, exists := ctx.Variables[key]
	return value, exists
}

// GetRecentMessages 获取最近的消息
func (cm *ContextManager) GetRecentMessages(sessionID string, count int) ([]Message, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	ctx, exists := cm.cache[sessionID]
	if !exists {
		return nil, fmt.Errorf("session context not found: %s", sessionID)
	}

	messages := make([]Message, 0)
	start := len(ctx.Messages) - count
	if start < 0 {
		start = 0
	}

	for i := start; i < len(ctx.Messages); i++ {
		msg := ctx.Messages[i]
		messages = append(messages, Message{
			Role:    msg.Role,
			Content: msg.Content,
		})
	}

	return messages, nil
}

// SaveContext 保存上下文到数据库
func (cm *ContextManager) SaveContext(sessionID string) error {
	cm.mutex.RLock()
	ctx, exists := cm.cache[sessionID]
	if !exists {
		cm.mutex.RUnlock()
		return fmt.Errorf("session context not found: %s", sessionID)
	}

	// 创建副本以避免长时间持有锁
	contextCopy := *ctx
	cm.mutex.RUnlock()

	// 序列化上下文变量
	contextData, err := json.Marshal(contextCopy.Variables)
	if err != nil {
		return fmt.Errorf("failed to marshal context variables: %w", err)
	}

	// 更新数据库
	updates := map[string]interface{}{
		"context_data":  string(contextData),
		"last_activity": contextCopy.LastActivity,
	}

	if err := cm.db.Model(&model.ChatSession{}).
		Where("session_id = ?", sessionID).
		Updates(updates).Error; err != nil {
		return fmt.Errorf("failed to save context: %w", err)
	}

	cm.logger.WithField("session_id", sessionID).Debug("Context saved to database")
	return nil
}

// CleanupExpiredContexts 清理过期的上下文
func (cm *ContextManager) CleanupExpiredContexts(maxAge time.Duration) {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	now := time.Now()
	for sessionID, ctx := range cm.cache {
		if now.Sub(ctx.LastActivity) > maxAge {
			// 保存到数据库
			if err := cm.SaveContext(sessionID); err != nil {
				cm.logger.WithError(err).WithField("session_id", sessionID).
					Warn("Failed to save context before cleanup")
			}

			// 从缓存中删除
			delete(cm.cache, sessionID)
			cm.logger.WithField("session_id", sessionID).Debug("Context cleaned up")
		}
	}
}

// GetContextSummary 获取上下文摘要
func (cm *ContextManager) GetContextSummary(sessionID string) (*ContextSummary, error) {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()

	ctx, exists := cm.cache[sessionID]
	if !exists {
		return nil, fmt.Errorf("session context not found: %s", sessionID)
	}

	summary := &ContextSummary{
		SessionID:     sessionID,
		UserID:        ctx.UserID,
		MessageCount:  len(ctx.Messages),
		VariableCount: len(ctx.Variables),
		LastActivity:  ctx.LastActivity,
	}

	// 统计消息类型
	summary.MessageTypes = make(map[string]int)
	for _, msg := range ctx.Messages {
		summary.MessageTypes[msg.Role]++
	}

	// 获取最近的主题
	if len(ctx.Messages) > 0 {
		recentMessages := ctx.Messages
		if len(recentMessages) > 5 {
			recentMessages = recentMessages[len(recentMessages)-5:]
		}

		topics := make([]string, 0)
		for _, msg := range recentMessages {
			if msg.Role == "user" && len(msg.Content) > 0 {
				// 简单提取关键词作为主题
				if len(msg.Content) > 50 {
					topics = append(topics, msg.Content[:50]+"...")
				} else {
					topics = append(topics, msg.Content)
				}
			}
		}
		summary.RecentTopics = topics
	}

	return summary, nil
}

// ContextSummary 上下文摘要
type ContextSummary struct {
	SessionID     string         `json:"session_id"`
	UserID        int64          `json:"user_id"`
	MessageCount  int            `json:"message_count"`
	VariableCount int            `json:"variable_count"`
	MessageTypes  map[string]int `json:"message_types"`
	RecentTopics  []string       `json:"recent_topics"`
	LastActivity  time.Time      `json:"last_activity"`
}

// StartCleanupRoutine 启动清理例程
func (cm *ContextManager) StartCleanupRoutine(ctx context.Context, interval time.Duration, maxAge time.Duration) {
	ticker := time.NewTicker(interval)
	defer ticker.Stop()

	for {
		select {
		case <-ctx.Done():
			return
		case <-ticker.C:
			cm.CleanupExpiredContexts(maxAge)
		}
	}
}
