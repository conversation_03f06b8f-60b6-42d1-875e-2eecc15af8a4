package ai

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
)

// DeepSeekClient DeepSeek API客户端
type DeepSeekClient struct {
	apiKey     string
	baseURL    string
	httpClient *http.Client
	logger     *logrus.Logger
}

// DeepSeekRequest DeepSeek请求
type DeepSeekRequest struct {
	Model       string           `json:"model"`
	Messages    []DeepSeekMessage `json:"messages"`
	MaxTokens   int              `json:"max_tokens"`
	Temperature float64          `json:"temperature"`
	Stream      bool             `json:"stream"`
}

// DeepSeekMessage DeepSeek消息
type DeepSeekMessage struct {
	Role    string `json:"role"`    // system, user, assistant
	Content string `json:"content"`
}

// DeepSeekResponse DeepSeek响应
type DeepSeekResponse struct {
	ID      string `json:"id"`
	Object  string `json:"object"`
	Created int64  `json:"created"`
	Model   string `json:"model"`
	Choices []struct {
		Index   int `json:"index"`
		Message struct {
			Role    string `json:"role"`
			Content string `json:"content"`
		} `json:"message"`
		FinishReason string `json:"finish_reason"`
	} `json:"choices"`
	Usage struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"usage"`
}

// DeepSeekError DeepSeek错误响应
type DeepSeekError struct {
	Error struct {
		Message string `json:"message"`
		Type    string `json:"type"`
		Code    string `json:"code"`
	} `json:"error"`
}

// GeneratedContent 生成的内容
type GeneratedContent struct {
	Content      string `json:"content"`
	FinishReason string `json:"finish_reason"`
	TokenUsage   struct {
		PromptTokens     int `json:"prompt_tokens"`
		CompletionTokens int `json:"completion_tokens"`
		TotalTokens      int `json:"total_tokens"`
	} `json:"token_usage"`
}

// NewDeepSeekClient 创建DeepSeek客户端
func NewDeepSeekClient(apiKey string, logger *logrus.Logger) *DeepSeekClient {
	return &DeepSeekClient{
		apiKey:  apiKey,
		baseURL: "https://api.deepseek.com/v1",
		httpClient: &http.Client{
			Timeout: 60 * time.Second,
		},
		logger: logger,
	}
}

// GenerateContent 生成内容
func (dsc *DeepSeekClient) GenerateContent(
	ctx context.Context,
	req *DeepSeekRequest,
) (*GeneratedContent, error) {
	start := time.Now()

	// 设置默认值
	if req.Model == "" {
		req.Model = "deepseek-chat"
	}
	if req.MaxTokens == 0 {
		req.MaxTokens = 2000
	}
	if req.Temperature == 0 {
		req.Temperature = 0.3
	}

	dsc.logger.WithFields(logrus.Fields{
		"model":       req.Model,
		"max_tokens":  req.MaxTokens,
		"temperature": req.Temperature,
		"messages":    len(req.Messages),
	}).Info("DeepSeekClient: 开始生成内容")

	// 构建请求
	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, "POST", dsc.baseURL+"/chat/completions", bytes.NewBuffer(reqBody))
	if err != nil {
		return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	// 设置请求头
	httpReq.Header.Set("Content-Type", "application/json")
	httpReq.Header.Set("Authorization", "Bearer "+dsc.apiKey)

	// 发送请求
	resp, err := dsc.httpClient.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("读取响应失败: %w", err)
	}

	// 检查HTTP状态码
	if resp.StatusCode != http.StatusOK {
		var deepseekErr DeepSeekError
		if err := json.Unmarshal(respBody, &deepseekErr); err == nil {
			return nil, fmt.Errorf("DeepSeek API错误: %s", deepseekErr.Error.Message)
		}
		return nil, fmt.Errorf("HTTP错误: %d, 响应: %s", resp.StatusCode, string(respBody))
	}

	// 解析响应
	var deepseekResp DeepSeekResponse
	if err := json.Unmarshal(respBody, &deepseekResp); err != nil {
		return nil, fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查响应格式
	if len(deepseekResp.Choices) == 0 {
		return nil, fmt.Errorf("DeepSeek响应中没有选择项")
	}

	// 构建结果
	result := &GeneratedContent{
		Content:      deepseekResp.Choices[0].Message.Content,
		FinishReason: deepseekResp.Choices[0].FinishReason,
	}
	result.TokenUsage.PromptTokens = deepseekResp.Usage.PromptTokens
	result.TokenUsage.CompletionTokens = deepseekResp.Usage.CompletionTokens
	result.TokenUsage.TotalTokens = deepseekResp.Usage.TotalTokens

	dsc.logger.WithFields(logrus.Fields{
		"content_length":     len(result.Content),
		"finish_reason":      result.FinishReason,
		"prompt_tokens":      result.TokenUsage.PromptTokens,
		"completion_tokens":  result.TokenUsage.CompletionTokens,
		"total_tokens":       result.TokenUsage.TotalTokens,
		"generation_time":    time.Since(start),
	}).Info("DeepSeekClient: 内容生成完成")

	return result, nil
}

// GenerateSQL 生成SQL语句
func (dsc *DeepSeekClient) GenerateSQL(
	ctx context.Context,
	userInput string,
	schema string,
) (string, error) {
	prompt := fmt.Sprintf(`
用户需求: %s

数据库表结构:
%s

请生成对应的SQL语句，要求:
1. 语法正确
2. 使用标准SQL
3. 添加适当的WHERE条件
4. 考虑性能优化
5. 只返回SQL语句，不要其他说明

SQL语句:`, userInput, schema)

	req := &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: "你是一个专业的SQL生成器，只返回高质量的SQL语句。",
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   1000,
		Temperature: 0.2,
	}

	result, err := dsc.GenerateContent(ctx, req)
	if err != nil {
		return "", err
	}

	return result.Content, nil
}

// GenerateShellCommand 生成Shell命令
func (dsc *DeepSeekClient) GenerateShellCommand(
	ctx context.Context,
	userInput string,
	systemInfo string,
) (string, error) {
	prompt := fmt.Sprintf(`
用户需求: %s

系统信息:
%s

请生成对应的Shell命令，要求:
1. 命令安全可执行
2. 适用于当前系统
3. 包含必要的参数
4. 避免危险操作
5. 只返回命令，不要其他说明

Shell命令:`, userInput, systemInfo)

	req := &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: "你是一个专业的Shell命令生成器，只返回安全的Shell命令。",
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   800,
		Temperature: 0.2,
	}

	result, err := dsc.GenerateContent(ctx, req)
	if err != nil {
		return "", err
	}

	return result.Content, nil
}

// AnalyzeIntent 分析用户意图
func (dsc *DeepSeekClient) AnalyzeIntent(
	ctx context.Context,
	userInput string,
	context map[string]interface{},
) (map[string]interface{}, error) {
	contextJSON, _ := json.Marshal(context)
	
	prompt := fmt.Sprintf(`
用户输入: %s
上下文: %s

请分析用户意图并返回JSON格式的结果:
{
  "intent_type": "database_query|system_operation|monitoring|configuration|general_chat",
  "confidence": 0.95,
  "parameters": {},
  "suggested_action": "具体的建议操作",
  "requires_confirmation": false
}

分析要点:
1. 准确识别用户想要执行的操作类型
2. 提取关键参数
3. 评估操作的风险级别
4. 提供具体的执行建议`, userInput, string(contextJSON))

	req := &DeepSeekRequest{
		Messages: []DeepSeekMessage{
			{
				Role:    "system",
				Content: "你是一个专业的意图分析器，准确理解用户需求并返回结构化的分析结果。",
			},
			{
				Role:    "user",
				Content: prompt,
			},
		},
		MaxTokens:   1500,
		Temperature: 0.1,
	}

	result, err := dsc.GenerateContent(ctx, req)
	if err != nil {
		return nil, err
	}

	var intentResult map[string]interface{}
	if err := json.Unmarshal([]byte(result.Content), &intentResult); err != nil {
		// 如果JSON解析失败，返回默认结果
		return map[string]interface{}{
			"intent_type":          "general_chat",
			"confidence":           0.5,
			"parameters":           map[string]interface{}{},
			"suggested_action":     "通用对话处理",
			"requires_confirmation": false,
		}, nil
	}

	return intentResult, nil
}
