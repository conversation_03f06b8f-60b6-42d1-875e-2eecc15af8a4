package service

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	"gorm.io/gorm"
)

// ReportDataAggregator 报表数据聚合器
type ReportDataAggregator struct {
	db     *gorm.DB
	logger *logrus.Logger
}

// NewReportDataAggregator 创建报表数据聚合器
func NewReportDataAggregator(db *gorm.DB, logger *logrus.Logger) *ReportDataAggregator {
	return &ReportDataAggregator{
		db:     db,
		logger: logger,
	}
}

// AggregateData 聚合数据
func (rda *ReportDataAggregator) AggregateData(ctx context.Context, req *ReportRequest) (*ReportData, error) {
	rda.logger.WithFields(logrus.Fields{
		"report_type": req.Type,
		"time_range":  req.TimeRange,
	}).Info("Starting data aggregation")

	data := &ReportData{
		Sections: []ReportSection{},
		Metrics:  make(map[string]interface{}),
		Tables:   []TableData{},
		Raw:      make(map[string]interface{}),
	}

	// 根据报表类型聚合不同的数据
	switch req.Type {
	case "operation":
		return rda.aggregateOperationData(ctx, req, data)
	case "system_health":
		return rda.aggregateSystemHealthData(ctx, req, data)
	case "ai_usage":
		return rda.aggregateAIUsageData(ctx, req, data)
	case "performance":
		return rda.aggregatePerformanceData(ctx, req, data)
	default:
		return rda.aggregateDefaultData(ctx, req, data)
	}
}

// aggregateOperationData 聚合运维操作数据
func (rda *ReportDataAggregator) aggregateOperationData(ctx context.Context, req *ReportRequest, data *ReportData) (*ReportData, error) {
	// 生成运维操作相关的模拟数据
	data.Metrics["total_operations"] = 1250
	data.Metrics["success_rate"] = 94.8
	data.Metrics["avg_response_time"] = 245.6
	data.Metrics["failed_operations"] = 65
	data.Metrics["peak_hour"] = "14:00-15:00"

	// 操作趋势数据
	trendData := rda.generateOperationTrendData()
	data.Metrics["operation_trend"] = trendData

	// 操作类型分布
	typeDistribution := map[string]interface{}{
		"ssh_operations":      450,
		"database_queries":    320,
		"file_operations":     280,
		"system_monitoring":   150,
		"alert_handling":      50,
	}
	data.Metrics["operation_types"] = typeDistribution

	// 添加表格数据
	data.Tables = append(data.Tables, TableData{
		ID:    "operation_summary",
		Title: "操作汇总",
		Headers: []string{"操作类型", "总数", "成功数", "失败数", "成功率"},
		Rows: [][]interface{}{
			{"SSH操作", 450, 430, 20, "95.6%"},
			{"数据库查询", 320, 305, 15, "95.3%"},
			{"文件操作", 280, 265, 15, "94.6%"},
			{"系统监控", 150, 145, 5, "96.7%"},
			{"告警处理", 50, 40, 10, "80.0%"},
		},
	})

	// 添加章节
	data.Sections = append(data.Sections, ReportSection{
		ID:      "operation_overview",
		Title:   "运维操作概览",
		Type:    "content",
		Content: "在过去的时间段内，系统共执行了1250次运维操作，整体成功率达到94.8%，平均响应时间为245.6毫秒。",
		Order:   1,
	})

	return data, nil
}

// aggregateSystemHealthData 聚合系统健康数据
func (rda *ReportDataAggregator) aggregateSystemHealthData(ctx context.Context, req *ReportRequest, data *ReportData) (*ReportData, error) {
	// 系统健康指标
	data.Metrics["health_score"] = 87.5
	data.Metrics["cpu_usage"] = 45.2
	data.Metrics["memory_usage"] = 68.7
	data.Metrics["disk_usage"] = 42.1
	data.Metrics["network_latency"] = 12.3
	data.Metrics["uptime_percentage"] = 99.8

	// 资源使用趋势
	resourceTrend := rda.generateResourceTrendData()
	data.Metrics["resource_usage"] = resourceTrend

	// 告警分布
	alertDistribution := map[string]interface{}{
		"critical": 2,
		"warning":  8,
		"info":     15,
	}
	data.Metrics["alert_distribution"] = alertDistribution

	// 主机状态
	hostStatus := map[string]interface{}{
		"online":  25,
		"offline": 2,
		"warning": 3,
	}
	data.Metrics["host_status"] = hostStatus

	data.Sections = append(data.Sections, ReportSection{
		ID:      "health_overview",
		Title:   "系统健康概览",
		Type:    "content",
		Content: "系统整体健康评分为87.5分，处于良好状态。当前有25台主机在线，2台离线，3台处于警告状态。",
		Order:   1,
	})

	return data, nil
}

// aggregateAIUsageData 聚合AI使用数据
func (rda *ReportDataAggregator) aggregateAIUsageData(ctx context.Context, req *ReportRequest, data *ReportData) (*ReportData, error) {
	// AI使用指标
	data.Metrics["total_requests"] = 3420
	data.Metrics["avg_response_time"] = 1850.5
	data.Metrics["success_rate"] = 98.2
	data.Metrics["token_usage"] = 125000
	data.Metrics["cost_estimate"] = 45.67

	// AI请求趋势
	requestTrend := rda.generateAIRequestTrendData()
	data.Metrics["ai_requests"] = requestTrend

	// 模型使用分布
	modelUsage := map[string]interface{}{
		"deepseek-chat":    2100,
		"deepseek-coder":   800,
		"deepseek-math":    320,
		"other":            200,
	}
	data.Metrics["model_usage"] = modelUsage

	// 响应时间分布
	responseTimes := rda.generateResponseTimeData()
	data.Metrics["response_times"] = responseTimes

	data.Sections = append(data.Sections, ReportSection{
		ID:      "ai_overview",
		Title:   "AI使用概览",
		Type:    "content",
		Content: "AI系统共处理了3420次请求，平均响应时间1.85秒，成功率达到98.2%，预估成本45.67元。",
		Order:   1,
	})

	return data, nil
}

// aggregatePerformanceData 聚合性能数据
func (rda *ReportDataAggregator) aggregatePerformanceData(ctx context.Context, req *ReportRequest, data *ReportData) (*ReportData, error) {
	// 性能指标
	data.Metrics["avg_response_time"] = 156.8
	data.Metrics["throughput"] = 2450.0
	data.Metrics["error_rate"] = 0.8
	data.Metrics["p95_response_time"] = 320.5
	data.Metrics["p99_response_time"] = 580.2

	// 性能趋势
	performanceTrend := rda.generatePerformanceTrendData()
	data.Metrics["performance_metrics"] = performanceTrend

	// 吞吐量数据
	throughputData := rda.generateThroughputData()
	data.Metrics["throughput"] = throughputData

	data.Sections = append(data.Sections, ReportSection{
		ID:      "performance_overview",
		Title:   "性能概览",
		Type:    "content",
		Content: "系统平均响应时间156.8ms，吞吐量2450 req/s，错误率0.8%，P95响应时间320.5ms。",
		Order:   1,
	})

	return data, nil
}

// aggregateDefaultData 聚合默认数据
func (rda *ReportDataAggregator) aggregateDefaultData(ctx context.Context, req *ReportRequest, data *ReportData) (*ReportData, error) {
	// 默认指标
	data.Metrics["total_items"] = 100
	data.Metrics["active_items"] = 85
	data.Metrics["success_rate"] = 92.5

	data.Sections = append(data.Sections, ReportSection{
		ID:      "default_overview",
		Title:   "数据概览",
		Type:    "content",
		Content: "这是一个默认的报表数据概览。",
		Order:   1,
	})

	return data, nil
}

// 数据生成辅助方法

// generateOperationTrendData 生成运维操作趋势数据
func (rda *ReportDataAggregator) generateOperationTrendData() interface{} {
	now := time.Now()
	var data []map[string]interface{}

	for i := 0; i < 24; i++ {
		timestamp := now.Add(-time.Duration(23-i) * time.Hour)
		operations := 30 + i*2 + (i%4)*10
		
		data = append(data, map[string]interface{}{
			"time":       timestamp.Format("2006-01-02 15:04:05"),
			"operations": operations,
			"success":    int(float64(operations) * 0.95),
			"failed":     int(float64(operations) * 0.05),
		})
	}

	return map[string]interface{}{
		"series": []map[string]interface{}{
			{
				"name": "总操作数",
				"data": data,
				"type": "line",
			},
		},
	}
}

// generateResourceTrendData 生成资源趋势数据
func (rda *ReportDataAggregator) generateResourceTrendData() interface{} {
	now := time.Now()
	var data []map[string]interface{}

	for i := 0; i < 24; i++ {
		timestamp := now.Add(-time.Duration(23-i) * time.Hour)
		
		data = append(data, map[string]interface{}{
			"time":   timestamp.Format("2006-01-02 15:04:05"),
			"cpu":    40 + float64(i%8)*5 + float64(i%3)*2,
			"memory": 60 + float64(i%6)*8 + float64(i%4)*3,
			"disk":   35 + float64(i%10)*2,
		})
	}

	return map[string]interface{}{
		"series": []map[string]interface{}{
			{"name": "CPU使用率", "data": data, "key": "cpu"},
			{"name": "内存使用率", "data": data, "key": "memory"},
			{"name": "磁盘使用率", "data": data, "key": "disk"},
		},
	}
}

// generateAIRequestTrendData 生成AI请求趋势数据
func (rda *ReportDataAggregator) generateAIRequestTrendData() interface{} {
	now := time.Now()
	var data []map[string]interface{}

	for i := 0; i < 24; i++ {
		timestamp := now.Add(-time.Duration(23-i) * time.Hour)
		requests := 100 + i*5 + (i%6)*20
		
		data = append(data, map[string]interface{}{
			"time":     timestamp.Format("2006-01-02 15:04:05"),
			"requests": requests,
			"tokens":   requests * 45,
		})
	}

	return map[string]interface{}{
		"series": []map[string]interface{}{
			{
				"name": "AI请求数",
				"data": data,
				"type": "line",
			},
		},
	}
}

// generateResponseTimeData 生成响应时间数据
func (rda *ReportDataAggregator) generateResponseTimeData() interface{} {
	var data []map[string]interface{}

	for i := 0; i < 100; i++ {
		responseTime := 1000 + float64(i*20) + float64((i*7)%500)
		requests := 50 + i + (i%10)*5
		
		data = append(data, map[string]interface{}{
			"response_time": responseTime,
			"requests":      requests,
		})
	}

	return map[string]interface{}{
		"data": data,
	}
}

// generatePerformanceTrendData 生成性能趋势数据
func (rda *ReportDataAggregator) generatePerformanceTrendData() interface{} {
	now := time.Now()
	var data []map[string]interface{}

	for i := 0; i < 24; i++ {
		timestamp := now.Add(-time.Duration(23-i) * time.Hour)
		
		data = append(data, map[string]interface{}{
			"time":          timestamp.Format("2006-01-02 15:04:05"),
			"response_time": 150 + float64(i%8)*20 + float64(i%3)*10,
			"throughput":    2000 + float64(i%10)*100,
			"error_rate":    0.5 + float64(i%5)*0.2,
		})
	}

	return map[string]interface{}{
		"series": []map[string]interface{}{
			{"name": "响应时间", "data": data, "key": "response_time"},
			{"name": "吞吐量", "data": data, "key": "throughput"},
			{"name": "错误率", "data": data, "key": "error_rate"},
		},
	}
}

// generateThroughputData 生成吞吐量数据
func (rda *ReportDataAggregator) generateThroughputData() interface{} {
	categories := []string{"API请求", "数据库查询", "文件操作", "缓存访问", "消息队列"}
	var data []map[string]interface{}

	for i, category := range categories {
		throughput := 1000 + float64(i*300) + float64((i*50)%200)
		data = append(data, map[string]interface{}{
			"category":   category,
			"throughput": throughput,
		})
	}

	return map[string]interface{}{
		"categories": categories,
		"data":       data,
	}
}
