package agent

import (
	"context"
	"regexp"
	"strings"

	"github.com/sirupsen/logrus"
)

// ImpactAnalyzer 影响分析器
type ImpactAnalyzer struct {
	logger *logrus.Logger
}

// NewImpactAnalyzer 创建影响分析器
func NewImpactAnalyzer(logger *logrus.Logger) *ImpactAnalyzer {
	return &ImpactAnalyzer{
		logger: logger,
	}
}

// Analyze 分析命令影响
func (ia *ImpactAnalyzer) Analyze(ctx context.Context, command string, args []string, assessmentContext *AssessmentContext) *ImpactAnalysis {
	analysis := &ImpactAnalysis{
		AffectedSystems: make([]string, 0),
	}

	// 分析影响范围
	analysis.Scope = ia.analyzeScope(command, args, assessmentContext)

	// 分析受影响的系统
	analysis.AffectedSystems = ia.analyzeAffectedSystems(command, args, assessmentContext)

	// 分析数据风险
	analysis.DataRisk = ia.analyzeDataRisk(command, args)

	// 分析服务影响
	analysis.ServiceImpact = ia.analyzeServiceImpact(command, args, assessmentContext)

	// 分析恢复时间
	analysis.RecoveryTime = ia.analyzeRecoveryTime(command, args)

	// 分析可逆性
	analysis.Reversible = ia.analyzeReversibility(command, args)

	// 分析是否需要备份
	analysis.BackupRequired = ia.analyzeBackupRequirement(command, args)

	return analysis
}

// analyzeScope 分析影响范围
func (ia *ImpactAnalyzer) analyzeScope(command string, args []string, assessmentContext *AssessmentContext) string {
	fullCommand := command + " " + strings.Join(args, " ")

	// 全局影响的命令
	globalCommands := []string{
		"shutdown", "reboot", "halt", "init", "systemctl poweroff",
		"systemctl reboot", "mkfs", "fdisk", "format",
	}

	for _, cmd := range globalCommands {
		if strings.Contains(fullCommand, cmd) {
			return "global"
		}
	}

	// 网络影响的命令
	networkCommands := []string{
		"iptables", "firewall", "netsh", "route", "ifconfig",
		"ip route", "ip addr", "systemctl restart network",
	}

	for _, cmd := range networkCommands {
		if strings.Contains(fullCommand, cmd) {
			return "network"
		}
	}

	// 系统级影响的命令
	systemCommands := []string{
		"systemctl", "service", "mount", "umount", "chmod 777",
		"chown", "passwd", "useradd", "userdel", "groupadd",
	}

	for _, cmd := range systemCommands {
		if strings.Contains(fullCommand, cmd) {
			return "system"
		}
	}

	// 检查是否影响系统目录
	systemPaths := []string{"/etc", "/usr", "/var", "/opt", "/boot", "/sys", "/proc"}
	for _, path := range systemPaths {
		if strings.Contains(fullCommand, path) {
			return "system"
		}
	}

	return "local"
}

// analyzeAffectedSystems 分析受影响的系统
func (ia *ImpactAnalyzer) analyzeAffectedSystems(command string, args []string, assessmentContext *AssessmentContext) []string {
	systems := make([]string, 0)
	fullCommand := command + " " + strings.Join(args, " ")

	// 检查数据库相关
	if ia.containsAny(fullCommand, []string{"mysql", "postgresql", "mongodb", "redis", "database"}) {
		systems = append(systems, "database")
	}

	// 检查Web服务器相关
	if ia.containsAny(fullCommand, []string{"nginx", "apache", "httpd", "tomcat", "web"}) {
		systems = append(systems, "web_server")
	}

	// 检查文件系统相关
	if ia.containsAny(fullCommand, []string{"mount", "umount", "mkfs", "fsck", "disk"}) {
		systems = append(systems, "filesystem")
	}

	// 检查网络相关
	if ia.containsAny(fullCommand, []string{"iptables", "firewall", "network", "route", "dns"}) {
		systems = append(systems, "network")
	}

	// 检查安全相关
	if ia.containsAny(fullCommand, []string{"passwd", "ssh", "ssl", "certificate", "key"}) {
		systems = append(systems, "security")
	}

	return systems
}

// analyzeDataRisk 分析数据风险
func (ia *ImpactAnalyzer) analyzeDataRisk(command string, args []string) string {
	fullCommand := command + " " + strings.Join(args, " ")

	// 删除操作
	deletePatterns := []string{
		`rm\s+.*-rf`, `rmdir`, `del\s+.*\/s`, `deltree`,
		`dd\s+.*of=`, `mkfs`, `format`, `fdisk.*d`,
	}

	for _, pattern := range deletePatterns {
		if matched, _ := regexp.MatchString(pattern, fullCommand); matched {
			return "delete"
		}
	}

	// 修改操作
	modifyPatterns := []string{
		`chmod`, `chown`, `sed\s+.*-i`, `>`, `>>`,
		`mv`, `cp\s+.*--force`, `dd\s+.*conv=`,
	}

	for _, pattern := range modifyPatterns {
		if matched, _ := regexp.MatchString(pattern, fullCommand); matched {
			return "modify"
		}
	}

	// 读取操作
	readPatterns := []string{
		`cat`, `less`, `more`, `head`, `tail`, `grep`,
		`find`, `locate`, `ls`, `dir`,
	}

	for _, pattern := range readPatterns {
		if matched, _ := regexp.MatchString(pattern, fullCommand); matched {
			return "read"
		}
	}

	return "none"
}

// analyzeServiceImpact 分析服务影响
func (ia *ImpactAnalyzer) analyzeServiceImpact(command string, args []string, assessmentContext *AssessmentContext) string {
	fullCommand := command + " " + strings.Join(args, " ")

	// 导致服务中断的命令
	outageCommands := []string{
		"shutdown", "reboot", "halt", "systemctl stop",
		"service.*stop", "kill -9", "killall",
	}

	for _, cmd := range outageCommands {
		if matched, _ := regexp.MatchString(cmd, fullCommand); matched {
			return "outage"
		}
	}

	// 导致服务降级的命令
	degradedCommands := []string{
		"systemctl restart", "service.*restart",
		"systemctl reload", "kill -HUP",
	}

	for _, cmd := range degradedCommands {
		if matched, _ := regexp.MatchString(cmd, fullCommand); matched {
			return "degraded"
		}
	}

	return "none"
}

// analyzeRecoveryTime 分析恢复时间
func (ia *ImpactAnalyzer) analyzeRecoveryTime(command string, args []string) string {
	fullCommand := command + " " + strings.Join(args, " ")

	// 需要数天恢复的操作
	daysCommands := []string{
		"mkfs", "format", "fdisk.*d", "rm.*-rf.*/$",
		"dd.*of=/dev/", "systemctl disable",
	}

	for _, cmd := range daysCommands {
		if matched, _ := regexp.MatchString(cmd, fullCommand); matched {
			return "days"
		}
	}

	// 需要数小时恢复的操作
	hoursCommands := []string{
		"rm.*-rf", "systemctl stop.*database",
		"systemctl stop.*web", "passwd.*root",
	}

	for _, cmd := range hoursCommands {
		if matched, _ := regexp.MatchString(cmd, fullCommand); matched {
			return "hours"
		}
	}

	// 需要数分钟恢复的操作
	minutesCommands := []string{
		"systemctl restart", "service.*restart",
		"systemctl reload", "mount", "umount",
	}

	for _, cmd := range minutesCommands {
		if matched, _ := regexp.MatchString(cmd, fullCommand); matched {
			return "minutes"
		}
	}

	return "immediate"
}

// analyzeReversibility 分析可逆性
func (ia *ImpactAnalyzer) analyzeReversibility(command string, args []string) bool {
	fullCommand := command + " " + strings.Join(args, " ")

	// 不可逆操作
	irreversiblePatterns := []string{
		`rm\s+.*-rf`, `rmdir`, `del\s+.*\/s`, `deltree`,
		`dd\s+.*of=`, `mkfs`, `format`, `fdisk.*d`,
		`shred`, `wipe`, `secure-delete`,
	}

	for _, pattern := range irreversiblePatterns {
		if matched, _ := regexp.MatchString(pattern, fullCommand); matched {
			return false
		}
	}

	return true
}

// analyzeBackupRequirement 分析备份需求
func (ia *ImpactAnalyzer) analyzeBackupRequirement(command string, args []string) bool {
	fullCommand := command + " " + strings.Join(args, " ")

	// 需要备份的操作
	backupRequiredPatterns := []string{
		`rm\s+.*-rf`, `rmdir`, `del\s+.*\/s`,
		`dd\s+.*of=`, `mkfs`, `format`,
		`sed\s+.*-i`, `>`, `mv`, `cp\s+.*--force`,
	}

	for _, pattern := range backupRequiredPatterns {
		if matched, _ := regexp.MatchString(pattern, fullCommand); matched {
			return true
		}
	}

	return false
}

// containsAny 检查字符串是否包含任意一个子字符串
func (ia *ImpactAnalyzer) containsAny(text string, substrings []string) bool {
	for _, substr := range substrings {
		if strings.Contains(text, substr) {
			return true
		}
	}
	return false
}
