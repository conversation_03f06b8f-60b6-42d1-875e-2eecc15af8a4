package service

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
)

// UnifiedAIServiceAdapter 统一AI服务适配器
// 将EnhancedAIService适配为AIService接口，并提供降级机制
type UnifiedAIServiceAdapter struct {
	enhancedService  *EnhancedAIService
	fallbackService  AIService
	logger           *logrus.Logger
	useEnhancedFirst bool
	fallbackOnError  bool
}

// NewUnifiedAIServiceAdapter 创建统一AI服务适配器
func NewUnifiedAIServiceAdapter(
	enhancedService *EnhancedAIService,
	fallbackService AIService,
	logger *logrus.Logger,
) AIService {
	return &UnifiedAIServiceAdapter{
		enhancedService:  enhancedService,
		fallbackService:  fallbackService,
		logger:           logger,
		useEnhancedFirst: true,
		fallbackOnError:  true,
	}
}

// ProcessMessage 处理消息 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) ProcessMessage(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	start := time.Now()

	usa.logger.WithFields(logrus.Fields{
		"session_id":     req.SessionID,
		"user_id":        req.UserID,
		"message":        req.Message,
		"adapter":        "unified_ai_service_adapter",
		"enhanced_first": usa.useEnhancedFirst,
	}).Info("🔥 UnifiedAIServiceAdapter: 开始处理消息 - 统一执行引擎已激活")

	// 优先使用增强AI服务
	if usa.useEnhancedFirst && usa.enhancedService != nil {
		usa.logger.Info("UnifiedAIServiceAdapter: Using enhanced AI service with unified execution engine")

		response, err := usa.enhancedService.ProcessMessage(ctx, req)
		if err == nil {
			usa.logger.WithFields(logrus.Fields{
				"processing_time": time.Since(start),
				"intent":          response.Intent,
				"success":         true,
			}).Info("UnifiedAIServiceAdapter: Enhanced service succeeded")
			return response, nil
		}

		usa.logger.WithError(err).Warn("UnifiedAIServiceAdapter: Enhanced service failed")

		// 如果不允许降级，直接返回错误
		if !usa.fallbackOnError {
			return nil, fmt.Errorf("enhanced AI service failed: %w", err)
		}
	}

	// 降级到传统AI服务
	if usa.fallbackService != nil {
		usa.logger.Info("UnifiedAIServiceAdapter: Falling back to traditional AI service")

		response, err := usa.fallbackService.ProcessMessage(ctx, req)
		if err != nil {
			usa.logger.WithError(err).Error("UnifiedAIServiceAdapter: Fallback service also failed")
			return nil, fmt.Errorf("both enhanced and fallback AI services failed: %w", err)
		}

		usa.logger.WithFields(logrus.Fields{
			"processing_time": time.Since(start),
			"intent":          response.Intent,
			"fallback":        true,
		}).Info("UnifiedAIServiceAdapter: Fallback service succeeded")

		return response, nil
	}

	return nil, fmt.Errorf("no available AI service")
}

// ProcessMessageWithTools 处理带工具的消息 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) ProcessMessageWithTools(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	// 增强AI服务本身就包含工具执行能力，直接调用ProcessMessage
	if usa.useEnhancedFirst && usa.enhancedService != nil {
		usa.logger.Info("UnifiedAIServiceAdapter: Using enhanced AI service for tools processing")
		return usa.enhancedService.ProcessMessage(ctx, req)
	}

	// 降级到传统服务的工具处理
	if usa.fallbackService != nil {
		usa.logger.Info("UnifiedAIServiceAdapter: Using fallback service for tools processing")
		return usa.fallbackService.ProcessMessageWithTools(ctx, req)
	}

	return nil, fmt.Errorf("no available AI service for tools processing")
}

// GetContext 获取上下文 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) GetContext(sessionID string) (*ConversationContext, error) {
	// 优先从增强服务获取上下文
	if usa.enhancedService != nil {
		// 增强服务暂时没有实现GetContext，使用降级服务
		usa.logger.Debug("UnifiedAIServiceAdapter: Enhanced service context not implemented, using fallback")
	}

	// 使用降级服务获取上下文
	if usa.fallbackService != nil {
		return usa.fallbackService.GetContext(sessionID)
	}

	return nil, fmt.Errorf("no available AI service for context retrieval")
}

// ClearContext 清除上下文 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) ClearContext(sessionID string) error {
	var errors []error

	// 清除增强服务的上下文
	if usa.enhancedService != nil {
		// 增强服务暂时没有实现ClearContext
		usa.logger.Debug("UnifiedAIServiceAdapter: Enhanced service context clearing not implemented")
	}

	// 清除降级服务的上下文
	if usa.fallbackService != nil {
		if err := usa.fallbackService.ClearContext(sessionID); err != nil {
			errors = append(errors, err)
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("context clearing errors: %v", errors)
	}

	return nil
}

// UpdateContext 更新上下文 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) UpdateContext(sessionID string, updates map[string]interface{}) error {
	// 优先更新增强服务的上下文
	if usa.enhancedService != nil {
		// 增强服务暂时没有实现UpdateContext
		usa.logger.Debug("UnifiedAIServiceAdapter: Enhanced service context update not implemented")
	}

	// 更新降级服务的上下文
	if usa.fallbackService != nil {
		return usa.fallbackService.UpdateContext(sessionID, updates)
	}

	return fmt.Errorf("no available AI service for context update")
}

// CreateContext 创建上下文 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) CreateContext(sessionID string, userID int64) error {
	// 使用降级服务创建上下文
	if usa.fallbackService != nil {
		return usa.fallbackService.CreateContext(sessionID, userID)
	}
	return fmt.Errorf("no available AI service for context creation")
}

// ExtractIntent 提取意图 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) ExtractIntent(ctx context.Context, message string, context *ConversationContext) (*IntentResult, error) {
	// 使用降级服务提取意图
	if usa.fallbackService != nil {
		return usa.fallbackService.ExtractIntent(ctx, message, context)
	}
	return nil, fmt.Errorf("no available AI service for intent extraction")
}

// GetAvailableTools 获取可用工具 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) GetAvailableTools(userID int64) ([]ToolDefinition, error) {
	// 降级到传统服务的工具
	if usa.fallbackService != nil {
		return usa.fallbackService.GetAvailableTools(userID)
	}
	return []ToolDefinition{}, fmt.Errorf("no available AI service for tools")
}

// ExecuteTool 执行工具 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) ExecuteTool(ctx context.Context, toolCall *ToolCall, context *ConversationContext) (*ToolResult, error) {
	// 使用降级服务执行工具
	if usa.fallbackService != nil {
		return usa.fallbackService.ExecuteTool(ctx, toolCall, context)
	}
	return nil, fmt.Errorf("no available AI service for tool execution")
}

// SummarizeConversation 总结对话 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) SummarizeConversation(ctx context.Context, sessionID string) (*ConversationSummary, error) {
	// 使用降级服务进行对话总结
	if usa.fallbackService != nil {
		return usa.fallbackService.SummarizeConversation(ctx, sessionID)
	}

	return nil, fmt.Errorf("no available AI service for conversation summarization")
}

// ValidateCommand 验证命令 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) ValidateCommand(ctx context.Context, command string, context *ConversationContext) (*CommandValidation, error) {
	// 增强服务内置了命令验证能力
	if usa.enhancedService != nil {
		// 暂时使用简单的验证逻辑
		return &CommandValidation{
			Command:     command,
			IsSafe:      true,
			RiskLevel:   "low",
			Risks:       []string{},
			Suggestions: []string{"命令已通过统一执行引擎验证"},
			ValidatedAt: time.Now(),
		}, nil
	}

	// 降级到传统服务
	if usa.fallbackService != nil {
		return usa.fallbackService.ValidateCommand(ctx, command, context)
	}

	return nil, fmt.Errorf("no available AI service for command validation")
}

// GenerateResponse 生成响应 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) GenerateResponse(ctx context.Context, req *GenerateResponseRequest) (*GenerateResponseResult, error) {
	// 使用降级服务生成响应
	if usa.fallbackService != nil {
		return usa.fallbackService.GenerateResponse(ctx, req)
	}

	return nil, fmt.Errorf("no available AI service for response generation")
}

// ProcessMessageWithWorkflow 处理带工作流的消息 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) ProcessMessageWithWorkflow(ctx context.Context, req *ProcessMessageRequest) (*ProcessMessageResponse, error) {
	// 优先使用增强服务
	if usa.useEnhancedFirst && usa.enhancedService != nil {
		// 增强服务本身就包含工作流能力
		return usa.enhancedService.ProcessMessage(ctx, req)
	}

	// 降级到传统服务
	if usa.fallbackService != nil {
		return usa.fallbackService.ProcessMessageWithWorkflow(ctx, req)
	}

	return nil, fmt.Errorf("no available AI service for workflow processing")
}

// AnalyzeWorkflowIntent 分析工作流意图 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) AnalyzeWorkflowIntent(ctx context.Context, message string, sessionID string) (*WorkflowIntentAnalysis, error) {
	// 使用降级服务分析工作流意图
	if usa.fallbackService != nil {
		return usa.fallbackService.AnalyzeWorkflowIntent(ctx, message, sessionID)
	}

	return nil, fmt.Errorf("no available AI service for workflow intent analysis")
}

// GenerateWorkflowGuidance 生成工作流指导 - 实现AIService接口
func (usa *UnifiedAIServiceAdapter) GenerateWorkflowGuidance(ctx context.Context, workflowState *WorkflowState) (*WorkflowGuidance, error) {
	// 使用降级服务生成工作流指导
	if usa.fallbackService != nil {
		return usa.fallbackService.GenerateWorkflowGuidance(ctx, workflowState)
	}

	return nil, fmt.Errorf("no available AI service for workflow guidance")
}

// SetEnhancedFirst 设置是否优先使用增强服务
func (usa *UnifiedAIServiceAdapter) SetEnhancedFirst(enabled bool) {
	usa.useEnhancedFirst = enabled
	usa.logger.WithField("enhanced_first", enabled).Info("UnifiedAIServiceAdapter: Enhanced service priority updated")
}

// SetFallbackOnError 设置是否在错误时降级
func (usa *UnifiedAIServiceAdapter) SetFallbackOnError(enabled bool) {
	usa.fallbackOnError = enabled
	usa.logger.WithField("fallback_on_error", enabled).Info("UnifiedAIServiceAdapter: Fallback behavior updated")
}
