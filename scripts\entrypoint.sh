#!/bin/bash
# AI对话运维管理平台 - Docker容器入口脚本

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_debug() {
    if [ "${DEBUG:-false}" = "true" ]; then
        echo -e "${BLUE}[DEBUG]${NC} $1"
    fi
}

# 检查必要的环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    local required_vars=(
        "DEEPSEEK_API_KEY"
        "JWT_SECRET"
        "ENCRYPTION_KEY"
    )
    
    local missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [ -z "${!var}" ]; then
            missing_vars+=("$var")
        fi
    done
    
    if [ ${#missing_vars[@]} -gt 0 ]; then
        log_error "缺少必要的环境变量: ${missing_vars[*]}"
        log_error "请检查 .env 文件或环境变量配置"
        exit 1
    fi
    
    log_info "环境变量检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    local dirs=(
        "/app/data"
        "/app/logs"
        "/app/backups"
        "/app/tmp"
    )
    
    for dir in "${dirs[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            log_debug "创建目录: $dir"
        fi
    done
    
    # 设置目录权限
    chmod 755 /app/data /app/logs /app/backups /app/tmp
    
    log_info "目录创建完成"
}

# 等待依赖服务
wait_for_services() {
    log_info "等待依赖服务启动..."
    
    # 等待Redis (如果启用)
    if [ "${REDIS_ENABLED:-false}" = "true" ]; then
        local redis_host="${REDIS_HOST:-localhost}"
        local redis_port="${REDIS_PORT:-6379}"
        local max_attempts=30
        local attempt=1
        
        log_info "等待Redis服务 ($redis_host:$redis_port)..."
        
        while [ $attempt -le $max_attempts ]; do
            if timeout 5 bash -c "</dev/tcp/$redis_host/$redis_port" 2>/dev/null; then
                log_info "Redis服务已就绪"
                break
            fi
            
            log_debug "Redis连接尝试 $attempt/$max_attempts 失败，等待5秒..."
            sleep 5
            ((attempt++))
        done
        
        if [ $attempt -gt $max_attempts ]; then
            log_warn "Redis服务连接超时，将以无缓存模式启动"
            export REDIS_ENABLED=false
        fi
    fi
}

# 数据库初始化
init_database() {
    log_info "初始化数据库..."
    
    local db_path="${DB_PATH:-/app/data/aiops.db}"
    local db_dir=$(dirname "$db_path")
    
    # 确保数据库目录存在
    if [ ! -d "$db_dir" ]; then
        mkdir -p "$db_dir"
        log_debug "创建数据库目录: $db_dir"
    fi
    
    # 检查数据库文件是否存在
    if [ ! -f "$db_path" ]; then
        log_info "数据库文件不存在，执行初始化..."
        
        # 运行数据库迁移
        if [ -f "/app/migrate" ]; then
            log_info "执行数据库迁移..."
            /app/migrate --db-path="$db_path" --action=up
            
            if [ $? -eq 0 ]; then
                log_info "数据库迁移完成"
            else
                log_error "数据库迁移失败"
                exit 1
            fi
        else
            log_warn "迁移工具不存在，跳过数据库初始化"
        fi
    else
        log_info "数据库文件已存在，检查是否需要升级..."
        
        # 检查数据库版本并执行必要的迁移
        if [ -f "/app/migrate" ]; then
            /app/migrate --db-path="$db_path" --action=check
            
            if [ $? -ne 0 ]; then
                log_info "执行数据库升级..."
                /app/migrate --db-path="$db_path" --action=up
                
                if [ $? -eq 0 ]; then
                    log_info "数据库升级完成"
                else
                    log_error "数据库升级失败"
                    exit 1
                fi
            else
                log_info "数据库已是最新版本"
            fi
        fi
    fi
    
    # 设置数据库文件权限
    chmod 644 "$db_path"
    
    log_info "数据库初始化完成"
}

# 配置文件处理
process_configs() {
    log_info "处理配置文件..."
    
    # 如果存在配置模板，则处理环境变量替换
    if [ -d "/app/configs/templates" ]; then
        for template in /app/configs/templates/*.tmpl; do
            if [ -f "$template" ]; then
                local config_file="/app/configs/$(basename "$template" .tmpl)"
                log_debug "处理配置模板: $template -> $config_file"
                
                # 使用envsubst替换环境变量
                envsubst < "$template" > "$config_file"
            fi
        done
    fi
    
    log_info "配置文件处理完成"
}

# 健康检查
health_check() {
    log_info "执行启动前健康检查..."
    
    # 检查磁盘空间
    local disk_usage=$(df /app/data | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 90 ]; then
        log_warn "磁盘使用率过高: ${disk_usage}%"
    fi
    
    # 检查内存使用
    local mem_available=$(free -m | awk 'NR==2{printf "%.1f", $7*100/$2}')
    log_debug "可用内存: ${mem_available}%"
    
    # 检查必要文件
    local required_files=(
        "/app/aiops-platform"
        "/app/web/templates"
        "/app/web/static"
    )
    
    for file in "${required_files[@]}"; do
        if [ ! -e "$file" ]; then
            log_error "必要文件不存在: $file"
            exit 1
        fi
    done
    
    log_info "健康检查通过"
}

# 信号处理
setup_signal_handlers() {
    log_info "设置信号处理器..."
    
    # 优雅关闭处理
    trap 'log_info "收到SIGTERM信号，开始优雅关闭..."; kill -TERM $PID; wait $PID' TERM
    trap 'log_info "收到SIGINT信号，开始优雅关闭..."; kill -INT $PID; wait $PID' INT
}

# 启动应用
start_application() {
    log_info "启动AI对话运维管理平台..."
    
    # 显示启动信息
    log_info "=========================================="
    log_info "AI对话运维管理平台"
    log_info "版本: ${VERSION:-dev}"
    log_info "构建时间: ${BUILD_TIME:-unknown}"
    log_info "Git提交: ${GIT_COMMIT:-unknown}"
    log_info "环境: ${ENV:-production}"
    log_info "端口: ${PORT:-8080}"
    log_info "数据库: ${DB_PATH:-/app/data/aiops.db}"
    log_info "=========================================="
    
    # 启动应用
    exec "$@" &
    PID=$!
    
    log_info "应用已启动，PID: $PID"
    
    # 等待进程结束
    wait $PID
    local exit_code=$?
    
    log_info "应用已退出，退出码: $exit_code"
    exit $exit_code
}

# 主函数
main() {
    log_info "AI对话运维管理平台启动脚本开始执行..."
    
    # 执行初始化步骤
    check_env_vars
    create_directories
    wait_for_services
    init_database
    process_configs
    health_check
    setup_signal_handlers
    
    # 启动应用
    start_application "$@"
}

# 如果脚本被直接执行，则运行主函数
if [ "${BASH_SOURCE[0]}" = "${0}" ]; then
    main "$@"
fi
