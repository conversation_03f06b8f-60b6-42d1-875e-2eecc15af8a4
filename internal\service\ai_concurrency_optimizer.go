package service

import (
	"context"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"golang.org/x/sync/semaphore"
)

// AIConcurrencyOptimizer AI并发优化器
type AIConcurrencyOptimizer struct {
	config    *ConcurrencyConfig
	logger    *logrus.Logger
	semaphore *semaphore.Weighted
	pool      *WorkerPool
	metrics   *ConcurrencyMetrics
	mutex     sync.RWMutex
}

// ConcurrencyConfig 并发配置
type ConcurrencyConfig struct {
	MaxConcurrentRequests int           `json:"max_concurrent_requests"`
	WorkerPoolSize        int           `json:"worker_pool_size"`
	RequestTimeout        time.Duration `json:"request_timeout"`
	QueueSize             int           `json:"queue_size"`
	EnableBatching        bool          `json:"enable_batching"`
	BatchSize             int           `json:"batch_size"`
	BatchTimeout          time.Duration `json:"batch_timeout"`
}

// WorkerPool 工作池
type WorkerPool struct {
	workers   []*Worker
	taskQueue chan *AITask
	config    *ConcurrencyConfig
	logger    *logrus.Logger
	ctx       context.Context
	cancel    context.CancelFunc
	wg        sync.WaitGroup
}

// Worker 工作者
type Worker struct {
	id       int
	pool     *WorkerPool
	taskChan chan *AITask
	quit     chan bool
	logger   *logrus.Logger
}

// AITask AI任务
type AITask struct {
	ID          string
	Content     string
	Intent      string
	UserID      int64
	SessionID   string
	Priority    int
	CreatedAt   time.Time
	Timeout     time.Duration
	ResultChan  chan *AITaskResult
	Context     context.Context
}

// AITaskResult AI任务结果
type AITaskResult struct {
	TaskID      string
	Content     string
	Intent      string
	Parameters  map[string]interface{}
	Confidence  float64
	ProcessTime time.Duration
	Error       error
	FromCache   bool
}

// ConcurrencyMetrics 并发指标
type ConcurrencyMetrics struct {
	TotalTasks        int64         `json:"total_tasks"`
	CompletedTasks    int64         `json:"completed_tasks"`
	FailedTasks       int64         `json:"failed_tasks"`
	ActiveTasks       int64         `json:"active_tasks"`
	QueuedTasks       int64         `json:"queued_tasks"`
	AvgProcessTime    time.Duration `json:"avg_process_time"`
	AvgQueueTime      time.Duration `json:"avg_queue_time"`
	ThroughputPerSec  float64       `json:"throughput_per_sec"`
	LastUpdated       time.Time     `json:"last_updated"`
}

// NewAIConcurrencyOptimizer 创建AI并发优化器
func NewAIConcurrencyOptimizer(config *ConcurrencyConfig, logger *logrus.Logger) *AIConcurrencyOptimizer {
	if config == nil {
		config = &ConcurrencyConfig{
			MaxConcurrentRequests: 10,
			WorkerPoolSize:        5,
			RequestTimeout:        30 * time.Second,
			QueueSize:             100,
			EnableBatching:        true,
			BatchSize:             5,
			BatchTimeout:          100 * time.Millisecond,
		}
	}

	optimizer := &AIConcurrencyOptimizer{
		config:    config,
		logger:    logger,
		semaphore: semaphore.NewWeighted(int64(config.MaxConcurrentRequests)),
		metrics: &ConcurrencyMetrics{
			LastUpdated: time.Now(),
		},
	}

	// 创建工作池
	optimizer.pool = NewWorkerPool(config, logger)

	return optimizer
}

// NewWorkerPool 创建工作池
func NewWorkerPool(config *ConcurrencyConfig, logger *logrus.Logger) *WorkerPool {
	ctx, cancel := context.WithCancel(context.Background())
	
	pool := &WorkerPool{
		taskQueue: make(chan *AITask, config.QueueSize),
		config:    config,
		logger:    logger,
		ctx:       ctx,
		cancel:    cancel,
	}

	// 创建工作者
	pool.workers = make([]*Worker, config.WorkerPoolSize)
	for i := 0; i < config.WorkerPoolSize; i++ {
		worker := &Worker{
			id:       i,
			pool:     pool,
			taskChan: make(chan *AITask),
			quit:     make(chan bool),
			logger:   logger,
		}
		pool.workers[i] = worker
	}

	return pool
}

// Start 启动优化器
func (aco *AIConcurrencyOptimizer) Start(ctx context.Context) error {
	aco.logger.Info("Starting AI concurrency optimizer")
	
	// 启动工作池
	return aco.pool.Start(ctx)
}

// Stop 停止优化器
func (aco *AIConcurrencyOptimizer) Stop() error {
	aco.logger.Info("Stopping AI concurrency optimizer")
	
	// 停止工作池
	return aco.pool.Stop()
}

// SubmitTask 提交AI任务
func (aco *AIConcurrencyOptimizer) SubmitTask(ctx context.Context, task *AITask) (*AITaskResult, error) {
	start := time.Now()
	
	// 获取信号量
	if err := aco.semaphore.Acquire(ctx, 1); err != nil {
		return nil, err
	}
	defer aco.semaphore.Release(1)

	// 更新指标
	aco.incrementActiveTask()
	defer aco.decrementActiveTask()

	// 设置超时
	if task.Timeout == 0 {
		task.Timeout = aco.config.RequestTimeout
	}

	taskCtx, cancel := context.WithTimeout(ctx, task.Timeout)
	defer cancel()

	task.Context = taskCtx
	task.CreatedAt = time.Now()
	task.ResultChan = make(chan *AITaskResult, 1)

	// 提交到工作池
	select {
	case aco.pool.taskQueue <- task:
		aco.incrementQueuedTask()
	case <-taskCtx.Done():
		return nil, taskCtx.Err()
	}

	// 等待结果
	select {
	case result := <-task.ResultChan:
		result.ProcessTime = time.Since(start)
		aco.updateMetrics(result)
		return result, result.Error
	case <-taskCtx.Done():
		return nil, taskCtx.Err()
	}
}

// Start 启动工作池
func (wp *WorkerPool) Start(ctx context.Context) error {
	wp.logger.Info("Starting worker pool", "workers", len(wp.workers))

	// 启动所有工作者
	for _, worker := range wp.workers {
		wp.wg.Add(1)
		go worker.Start(&wp.wg)
	}

	// 启动任务分发器
	wp.wg.Add(1)
	go wp.dispatcher(&wp.wg)

	return nil
}

// Stop 停止工作池
func (wp *WorkerPool) Stop() error {
	wp.logger.Info("Stopping worker pool")

	// 停止任务分发器
	wp.cancel()

	// 停止所有工作者
	for _, worker := range wp.workers {
		worker.Stop()
	}

	// 等待所有工作者完成
	wp.wg.Wait()

	return nil
}

// dispatcher 任务分发器
func (wp *WorkerPool) dispatcher(wg *sync.WaitGroup) {
	defer wg.Done()

	for {
		select {
		case task := <-wp.taskQueue:
			// 找到空闲的工作者
			go func(t *AITask) {
				for _, worker := range wp.workers {
					select {
					case worker.taskChan <- t:
						return
					default:
						continue
					}
				}
				// 如果所有工作者都忙，等待
				worker := wp.workers[0]
				worker.taskChan <- t
			}(task)
		case <-wp.ctx.Done():
			return
		}
	}
}

// Start 启动工作者
func (w *Worker) Start(wg *sync.WaitGroup) {
	defer wg.Done()

	w.logger.Debug("Worker started", "worker_id", w.id)

	for {
		select {
		case task := <-w.taskChan:
			w.processTask(task)
		case <-w.quit:
			w.logger.Debug("Worker stopped", "worker_id", w.id)
			return
		}
	}
}

// Stop 停止工作者
func (w *Worker) Stop() {
	w.quit <- true
}

// processTask 处理任务
func (w *Worker) processTask(task *AITask) {
	start := time.Now()
	
	w.logger.Debug("Processing AI task", "worker_id", w.id, "task_id", task.ID)

	// 这里应该调用实际的AI处理逻辑
	// 暂时返回模拟结果
	result := &AITaskResult{
		TaskID:      task.ID,
		Content:     "AI处理结果",
		Intent:      task.Intent,
		Parameters:  make(map[string]interface{}),
		Confidence:  0.9,
		ProcessTime: time.Since(start),
		FromCache:   false,
	}

	// 发送结果
	select {
	case task.ResultChan <- result:
	case <-task.Context.Done():
		result.Error = task.Context.Err()
		task.ResultChan <- result
	}
}

// GetMetrics 获取并发指标
func (aco *AIConcurrencyOptimizer) GetMetrics() *ConcurrencyMetrics {
	aco.mutex.RLock()
	defer aco.mutex.RUnlock()

	metrics := *aco.metrics
	metrics.LastUpdated = time.Now()
	
	// 计算吞吐量
	if metrics.LastUpdated.Sub(aco.metrics.LastUpdated) > 0 {
		duration := metrics.LastUpdated.Sub(aco.metrics.LastUpdated).Seconds()
		metrics.ThroughputPerSec = float64(metrics.CompletedTasks) / duration
	}

	return &metrics
}

// incrementActiveTask 增加活跃任务计数
func (aco *AIConcurrencyOptimizer) incrementActiveTask() {
	aco.mutex.Lock()
	defer aco.mutex.Unlock()
	aco.metrics.ActiveTasks++
	aco.metrics.TotalTasks++
}

// decrementActiveTask 减少活跃任务计数
func (aco *AIConcurrencyOptimizer) decrementActiveTask() {
	aco.mutex.Lock()
	defer aco.mutex.Unlock()
	aco.metrics.ActiveTasks--
}

// incrementQueuedTask 增加队列任务计数
func (aco *AIConcurrencyOptimizer) incrementQueuedTask() {
	aco.mutex.Lock()
	defer aco.mutex.Unlock()
	aco.metrics.QueuedTasks++
}

// updateMetrics 更新指标
func (aco *AIConcurrencyOptimizer) updateMetrics(result *AITaskResult) {
	aco.mutex.Lock()
	defer aco.mutex.Unlock()

	if result.Error != nil {
		aco.metrics.FailedTasks++
	} else {
		aco.metrics.CompletedTasks++
	}

	// 更新平均处理时间
	if aco.metrics.CompletedTasks > 0 {
		aco.metrics.AvgProcessTime = (aco.metrics.AvgProcessTime*time.Duration(aco.metrics.CompletedTasks-1) + result.ProcessTime) / time.Duration(aco.metrics.CompletedTasks)
	}
}
