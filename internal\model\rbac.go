package model

import (
	"time"
)

// Role 角色模型
type Role struct {
	BaseModel
	Name        string `json:"name" gorm:"uniqueIndex;size:50;not null"`
	DisplayName string `json:"display_name" gorm:"size:100;not null"`
	Description string `json:"description" gorm:"size:500"`
	IsSystem    bool   `json:"is_system" gorm:"default:false"` // 系统内置角色不可删除
	IsActive    bool   `json:"is_active" gorm:"default:true"`

	// 关联关系
	Permissions []Permission `json:"permissions" gorm:"many2many:role_permissions;"`
	Users       []User       `json:"users" gorm:"many2many:user_roles;"`
}

// Permission 权限模型
type Permission struct {
	BaseModel
	Name        string `json:"name" gorm:"uniqueIndex;size:100;not null"`
	DisplayName string `json:"display_name" gorm:"size:100;not null"`
	Description string `json:"description" gorm:"size:500"`
	Resource    string `json:"resource" gorm:"size:50;not null"`   // 资源类型：user, host, alert, etc.
	Action      string `json:"action" gorm:"size:50;not null"`     // 操作类型：create, read, update, delete, execute
	Scope       string `json:"scope" gorm:"size:50;default:'all'"` // 权限范围：all, own, group
	IsSystem    bool   `json:"is_system" gorm:"default:false"`     // 系统内置权限不可删除

	// 关联关系
	Roles []Role `json:"roles" gorm:"many2many:role_permissions;"`
}

// UserRole 用户角色关联
type UserRole struct {
	BaseModel
	UserID    int64      `json:"user_id" gorm:"index;not null"`
	RoleID    int64      `json:"role_id" gorm:"index;not null"`
	GrantedBy int64      `json:"granted_by" gorm:"index"`          // 授权人ID
	GrantedAt time.Time  `json:"granted_at" gorm:"autoCreateTime"` // 授权时间
	ExpiresAt *time.Time `json:"expires_at"`                       // 过期时间，nil表示永不过期
	IsActive  bool       `json:"is_active" gorm:"default:true"`

	// 关联关系
	User          User `json:"user" gorm:"foreignKey:UserID"`
	Role          Role `json:"role" gorm:"foreignKey:RoleID"`
	GrantedByUser User `json:"granted_by_user" gorm:"foreignKey:GrantedBy"`
}

// RolePermission 角色权限关联
type RolePermission struct {
	BaseModel
	RoleID       int64     `json:"role_id" gorm:"index;not null"`
	PermissionID int64     `json:"permission_id" gorm:"index;not null"`
	GrantedBy    int64     `json:"granted_by" gorm:"index"`          // 授权人ID
	GrantedAt    time.Time `json:"granted_at" gorm:"autoCreateTime"` // 授权时间

	// 关联关系
	Role          Role       `json:"role" gorm:"foreignKey:RoleID"`
	Permission    Permission `json:"permission" gorm:"foreignKey:PermissionID"`
	GrantedByUser User       `json:"granted_by_user" gorm:"foreignKey:GrantedBy"`
}

// ResourcePolicy 资源策略
type ResourcePolicy struct {
	BaseModel
	Name        string `json:"name" gorm:"uniqueIndex;size:100;not null"`
	Description string `json:"description" gorm:"size:500"`
	Resource    string `json:"resource" gorm:"size:50;not null"`
	Conditions  string `json:"conditions" gorm:"type:text"`                    // JSON格式的条件表达式
	Effect      string `json:"effect" gorm:"size:10;not null;default:'allow'"` // allow, deny
	Priority    int    `json:"priority" gorm:"default:0"`                      // 优先级，数字越大优先级越高
	IsActive    bool   `json:"is_active" gorm:"default:true"`
}

// 预定义的系统角色
const (
	RoleAdmin     = "admin"     // 超级管理员
	RoleOperator  = "operator"  // 运维人员
	RoleViewer    = "viewer"    // 只读用户
	RoleDeveloper = "developer" // 开发人员
	RoleGuest     = "guest"     // 访客
)

// 预定义的资源类型
const (
	ResourceUser       = "user"
	ResourceRole       = "role"
	ResourcePermission = "permission"
	ResourceHost       = "host"
	ResourceAlert      = "alert"
	ResourceChat       = "chat"
	ResourceConfig     = "config"
	ResourceOperation  = "operation"
	ResourceStats      = "stats"
	ResourceSession    = "session"
	ResourceDevice     = "device"
	ResourceSystem     = "system"
)

// 预定义的操作类型
const (
	ActionCreate  = "create"
	ActionRead    = "read"
	ActionUpdate  = "update"
	ActionDelete  = "delete"
	ActionExecute = "execute"
	ActionManage  = "manage"
	ActionGrant   = "grant"
	ActionRevoke  = "revoke"
	ActionExport  = "export"
	ActionImport  = "import"
)

// 预定义的权限范围
const (
	ScopeAll   = "all"   // 所有资源
	ScopeOwn   = "own"   // 自己的资源
	ScopeGroup = "group" // 同组资源
)

// RoleRequest 角色请求
type RoleRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=50"`
	DisplayName string `json:"display_name" binding:"required,min=2,max=100"`
	Description string `json:"description" binding:"max=500"`
	IsActive    *bool  `json:"is_active"`
}

// PermissionRequest 权限请求
type PermissionRequest struct {
	Name        string `json:"name" binding:"required,min=2,max=100"`
	DisplayName string `json:"display_name" binding:"required,min=2,max=100"`
	Description string `json:"description" binding:"max=500"`
	Resource    string `json:"resource" binding:"required,oneof=user role permission host alert chat config operation stats session device system"`
	Action      string `json:"action" binding:"required,oneof=create read update delete execute manage grant revoke export import"`
	Scope       string `json:"scope" binding:"oneof=all own group"`
}

// AssignRoleRequest 分配角色请求
type AssignRoleRequest struct {
	UserID    int64      `json:"user_id" binding:"required,min=1"`
	RoleID    int64      `json:"role_id" binding:"required,min=1"`
	ExpiresAt *time.Time `json:"expires_at"`
}

// AssignPermissionRequest 分配权限请求
type AssignPermissionRequest struct {
	RoleID       int64 `json:"role_id" binding:"required,min=1"`
	PermissionID int64 `json:"permission_id" binding:"required,min=1"`
}

// RoleResponse 角色响应
type RoleResponse struct {
	ID          int64                 `json:"id"`
	Name        string                `json:"name"`
	DisplayName string                `json:"display_name"`
	Description string                `json:"description"`
	IsSystem    bool                  `json:"is_system"`
	IsActive    bool                  `json:"is_active"`
	CreatedAt   int64                 `json:"created_at"`
	UpdatedAt   int64                 `json:"updated_at"`
	Permissions []*PermissionResponse `json:"permissions,omitempty"`
	UserCount   int64                 `json:"user_count,omitempty"`
}

// PermissionResponse 权限响应
type PermissionResponse struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	DisplayName string `json:"display_name"`
	Description string `json:"description"`
	Resource    string `json:"resource"`
	Action      string `json:"action"`
	Scope       string `json:"scope"`
	IsSystem    bool   `json:"is_system"`
	CreatedAt   int64  `json:"created_at"`
	UpdatedAt   int64  `json:"updated_at"`
}

// UserRoleResponse 用户角色响应
type UserRoleResponse struct {
	ID            int64         `json:"id"`
	UserID        int64         `json:"user_id"`
	User          *UserResponse `json:"user,omitempty"`
	RoleID        int64         `json:"role_id"`
	Role          *RoleResponse `json:"role,omitempty"`
	GrantedBy     int64         `json:"granted_by"`
	GrantedByUser *UserResponse `json:"granted_by_user,omitempty"`
	GrantedAt     int64         `json:"granted_at"`
	ExpiresAt     *int64        `json:"expires_at"`
	IsActive      bool          `json:"is_active"`
}

// RoleListResponse 角色列表响应
type RoleListResponse struct {
	Roles      []*RoleResponse `json:"roles"`
	Pagination *Pagination     `json:"pagination"`
}

// PermissionListResponse 权限列表响应
type PermissionListResponse struct {
	Permissions []*PermissionResponse `json:"permissions"`
	Pagination  *Pagination           `json:"pagination"`
}

// UserPermissionResponse 用户权限响应
type UserPermissionResponse struct {
	UserID               int64                 `json:"user_id"`
	Roles                []*RoleResponse       `json:"roles"`
	Permissions          []*PermissionResponse `json:"permissions"`
	EffectivePermissions []string              `json:"effective_permissions"` // 有效权限列表
}

// PermissionCheckRequest 权限检查请求
type PermissionCheckRequest struct {
	UserID   int64  `json:"user_id" binding:"required,min=1"`
	Resource string `json:"resource" binding:"required"`
	Action   string `json:"action" binding:"required"`
	Scope    string `json:"scope"`
	Target   string `json:"target"` // 目标资源ID或标识
}

// PermissionCheckResponse 权限检查响应
type PermissionCheckResponse struct {
	Allowed     bool     `json:"allowed"`
	Reason      string   `json:"reason,omitempty"`
	Permissions []string `json:"permissions,omitempty"` // 匹配的权限列表
}
