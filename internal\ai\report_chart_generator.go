package ai

import (
	"context"
	"fmt"

	"github.com/sirupsen/logrus"
)

// 🎨 报表图表生成器 - 智能生成数据可视化图表
type ReportChartGenerator struct {
	logger *logrus.Logger
}

// ChartType 图表类型
type ChartType string

const (
	ChartTypeLine   ChartType = "line"   // 折线图
	ChartTypeBar    ChartType = "bar"    // 柱状图
	ChartTypePie    ChartType = "pie"    // 饼图
	ChartTypeArea   ChartType = "area"   // 面积图
	ChartTypeGauge  ChartType = "gauge"  // 仪表盘
	ChartTypeRadar  ChartType = "radar"  // 雷达图
)

// ChartData 图表数据
type ChartData struct {
	Labels   []string      `json:"labels"`
	Datasets []ChartDataset `json:"datasets"`
}

// ChartDataset 图表数据集
type ChartDataset struct {
	Label           string    `json:"label"`
	Data            []float64 `json:"data"`
	BackgroundColor []string  `json:"backgroundColor,omitempty"`
	BorderColor     []string  `json:"borderColor,omitempty"`
	Fill            bool      `json:"fill,omitempty"`
}

// ChartOptions 图表选项
type ChartOptions struct {
	Responsive bool                   `json:"responsive"`
	Title      *ChartTitle            `json:"title,omitempty"`
	Legend     *ChartLegend           `json:"legend,omitempty"`
	Scales     map[string]interface{} `json:"scales,omitempty"`
	Plugins    map[string]interface{} `json:"plugins,omitempty"`
}

// ChartTitle 图表标题
type ChartTitle struct {
	Display bool   `json:"display"`
	Text    string `json:"text"`
}

// ChartLegend 图表图例
type ChartLegend struct {
	Display  bool   `json:"display"`
	Position string `json:"position"`
}

// NewReportChartGenerator 创建报表图表生成器
func NewReportChartGenerator(logger *logrus.Logger) *ReportChartGenerator {
	return &ReportChartGenerator{
		logger: logger,
	}
}

// GenerateCharts 生成图表
func (rcg *ReportChartGenerator) GenerateCharts(ctx context.Context, reportType string, data interface{}) ([]ReportChart, error) {
	rcg.logger.WithField("report_type", reportType).Info("🎨 开始生成报表图表")
	
	var charts []ReportChart
	
	switch reportType {
	case "operation":
		charts = rcg.generateOperationCharts(data)
	case "health":
		charts = rcg.generateHealthCharts(data)
	case "ai_usage":
		charts = rcg.generateAIUsageCharts(data)
	case "custom":
		charts = rcg.generateCustomCharts(data)
	default:
		charts = rcg.generateDefaultCharts(data)
	}
	
	rcg.logger.WithField("charts_count", len(charts)).Info("🎨 图表生成完成")
	return charts, nil
}

// generateOperationCharts 生成运维操作图表
func (rcg *ReportChartGenerator) generateOperationCharts(data interface{}) []ReportChart {
	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return []ReportChart{}
	}
	
	var charts []ReportChart
	
	// 1. 操作成功率饼图
	if successRate, ok := dataMap["success_rate"].(float64); ok {
		failureRate := 100 - successRate
		charts = append(charts, ReportChart{
			Type:  "pie",
			Title: "操作成功率分布",
			Data: ChartData{
				Labels: []string{"成功", "失败"},
				Datasets: []ChartDataset{
					{
						Label: "操作结果",
						Data:  []float64{successRate, failureRate},
						BackgroundColor: []string{"#28a745", "#dc3545"},
					},
				},
			},
			Options: map[string]interface{}{
				"responsive": true,
				"title": map[string]interface{}{
					"display": true,
					"text":    "操作成功率分布",
				},
			},
			Description: fmt.Sprintf("成功率: %.1f%%", successRate),
		})
	}
	
	// 2. 操作类型分布柱状图
	if topOps, ok := dataMap["top_operations"].([]string); ok {
		// 模拟数据
		values := []float64{45.2, 28.7, 15.3, 10.8}
		if len(values) > len(topOps) {
			values = values[:len(topOps)]
		}
		
		charts = append(charts, ReportChart{
			Type:  "bar",
			Title: "热门操作分布",
			Data: ChartData{
				Labels: topOps,
				Datasets: []ChartDataset{
					{
						Label:           "操作次数",
						Data:            values,
						BackgroundColor: []string{"#007bff", "#28a745", "#ffc107", "#17a2b8"},
					},
				},
			},
			Options: map[string]interface{}{
				"responsive": true,
				"scales": map[string]interface{}{
					"y": map[string]interface{}{
						"beginAtZero": true,
					},
				},
			},
			Description: "显示最常用的运维操作类型",
		})
	}
	
	return charts
}

// generateHealthCharts 生成系统健康图表
func (rcg *ReportChartGenerator) generateHealthCharts(data interface{}) []ReportChart {
	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return []ReportChart{}
	}
	
	var charts []ReportChart
	
	// 1. 系统健康度仪表盘
	if healthScore, ok := dataMap["health_score"].(float64); ok {
		charts = append(charts, ReportChart{
			Type:  "gauge",
			Title: "系统健康度",
			Data: map[string]interface{}{
				"value": healthScore,
				"max":   100,
			},
			Options: map[string]interface{}{
				"responsive": true,
				"color": rcg.getHealthColor(healthScore),
			},
			Description: fmt.Sprintf("当前健康度: %.1f%%", healthScore),
		})
	}
	
	// 2. 主机状态分布饼图
	if onlineHosts, ok1 := dataMap["online_hosts"].(int64); ok1 {
		if offlineHosts, ok2 := dataMap["offline_hosts"].(int64); ok2 {
			charts = append(charts, ReportChart{
				Type:  "pie",
				Title: "主机状态分布",
				Data: ChartData{
					Labels: []string{"在线", "离线"},
					Datasets: []ChartDataset{
						{
							Label: "主机数量",
							Data:  []float64{float64(onlineHosts), float64(offlineHosts)},
							BackgroundColor: []string{"#28a745", "#dc3545"},
						},
					},
				},
				Options: map[string]interface{}{
					"responsive": true,
				},
				Description: fmt.Sprintf("在线: %d台, 离线: %d台", onlineHosts, offlineHosts),
			})
		}
	}
	
	// 3. 系统资源使用率雷达图
	cpuAvg, _ := dataMap["cpu_avg"].(float64)
	memoryAvg, _ := dataMap["memory_avg"].(float64)
	diskUsage, _ := dataMap["disk_usage"].(float64)
	
	charts = append(charts, ReportChart{
		Type:  "radar",
		Title: "系统资源使用率",
		Data: ChartData{
			Labels: []string{"CPU", "内存", "磁盘", "网络", "IO"},
			Datasets: []ChartDataset{
				{
					Label:       "使用率 (%)",
					Data:        []float64{cpuAvg, memoryAvg, diskUsage, 25.0, 30.0},
					BorderColor: []string{"#007bff"},
					Fill:        true,
				},
			},
		},
		Options: map[string]interface{}{
			"responsive": true,
			"scales": map[string]interface{}{
				"r": map[string]interface{}{
					"beginAtZero": true,
					"max":        100,
				},
			},
		},
		Description: "显示各项系统资源的使用情况",
	})
	
	return charts
}

// generateAIUsageCharts 生成AI使用图表
func (rcg *ReportChartGenerator) generateAIUsageCharts(data interface{}) []ReportChart {
	dataMap, ok := data.(map[string]interface{})
	if !ok {
		return []ReportChart{}
	}
	
	var charts []ReportChart
	
	// 1. 消息类型分布
	if userMessages, ok1 := dataMap["user_messages"].(int64); ok1 {
		if aiMessages, ok2 := dataMap["ai_messages"].(int64); ok2 {
			charts = append(charts, ReportChart{
				Type:  "pie",
				Title: "消息类型分布",
				Data: ChartData{
					Labels: []string{"用户消息", "AI回复"},
					Datasets: []ChartDataset{
						{
							Label: "消息数量",
							Data:  []float64{float64(userMessages), float64(aiMessages)},
							BackgroundColor: []string{"#007bff", "#28a745"},
						},
					},
				},
				Options: map[string]interface{}{
					"responsive": true,
				},
				Description: fmt.Sprintf("用户: %d条, AI: %d条", userMessages, aiMessages),
			})
		}
	}
	
	// 2. 热门功能使用情况
	if topFeatures, ok := dataMap["top_features"].([]string); ok {
		// 模拟使用数据
		values := []float64{35.2, 28.7, 18.3, 12.8, 5.0}
		if len(values) > len(topFeatures) {
			values = values[:len(topFeatures)]
		}
		
		charts = append(charts, ReportChart{
			Type:  "bar",
			Title: "热门功能使用排行",
			Data: ChartData{
				Labels: topFeatures,
				Datasets: []ChartDataset{
					{
						Label:           "使用频率 (%)",
						Data:            values,
						BackgroundColor: []string{"#007bff", "#28a745", "#ffc107", "#17a2b8", "#6f42c1"},
					},
				},
			},
			Options: map[string]interface{}{
				"responsive": true,
				"scales": map[string]interface{}{
					"y": map[string]interface{}{
						"beginAtZero": true,
					},
				},
			},
			Description: "显示用户最常使用的AI功能",
		})
	}
	
	return charts
}

// generateCustomCharts 生成自定义图表
func (rcg *ReportChartGenerator) generateCustomCharts(data interface{}) []ReportChart {
	// 简化实现：生成通用图表
	return rcg.generateDefaultCharts(data)
}

// generateDefaultCharts 生成默认图表
func (rcg *ReportChartGenerator) generateDefaultCharts(data interface{}) []ReportChart {
	return []ReportChart{
		{
			Type:        "line",
			Title:       "数据趋势",
			Data:        data,
			Options:     map[string]interface{}{"responsive": true},
			Description: "数据变化趋势图表",
		},
	}
}

// getHealthColor 根据健康度获取颜色
func (rcg *ReportChartGenerator) getHealthColor(score float64) string {
	if score >= 90 {
		return "#28a745" // 绿色
	} else if score >= 70 {
		return "#ffc107" // 黄色
	} else {
		return "#dc3545" // 红色
	}
}
