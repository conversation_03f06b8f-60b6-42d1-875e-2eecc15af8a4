package handler

import (
	"net/http"
	"strconv"

	"aiops-platform/internal/service"

	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
)

// EnhancedSettingsHandler 增强设置处理器
type EnhancedSettingsHandler struct {
	logger          *logrus.Logger
	settingsManager *service.EnhancedSettingsManager
}

// NewEnhancedSettingsHandler 创建增强设置处理器
func NewEnhancedSettingsHandler(logger *logrus.Logger, settingsManager *service.EnhancedSettingsManager) *EnhancedSettingsHandler {
	return &EnhancedSettingsHandler{
		logger:          logger,
		settingsManager: settingsManager,
	}
}

// RegisterRoutes 注册路由
func (esh *EnhancedSettingsHandler) RegisterRoutes(router *gin.RouterGroup) {
	settings := router.Group("/enhanced-settings")
	{
		// 系统设置
		settings.GET("/system", esh.GetSystemSettings)
		settings.GET("/system/:category/:key", esh.GetSystemSetting)
		settings.PUT("/system/:category/:key", esh.SetSystemSetting)
		
		// 用户设置
		settings.GET("/user", esh.GetUserSettings)
		settings.GET("/user/:category", esh.GetUserSettingsByCategory)
		settings.GET("/user/:category/:key", esh.GetUserSetting)
		settings.PUT("/user/:category/:key", esh.SetUserSetting)
		settings.DELETE("/user/:category/:key", esh.DeleteUserSetting)
		
		// 主题管理
		settings.GET("/themes", esh.GetAvailableThemes)
		settings.GET("/themes/:name", esh.GetTheme)
		settings.POST("/themes", esh.CreateCustomTheme)
		settings.PUT("/themes/:id", esh.UpdateTheme)
		settings.DELETE("/themes/:id", esh.DeleteTheme)
		
		// 设置导入导出
		settings.GET("/export", esh.ExportSettings)
		settings.POST("/import", esh.ImportSettings)
		
		// 统计和监控
		settings.GET("/metrics", esh.GetMetrics)
		settings.GET("/audit-logs", esh.GetAuditLogs)
		
		// 设置重置
		settings.POST("/reset", esh.ResetSettings)
	}
}

// GetSystemSettings 获取系统设置
func (esh *EnhancedSettingsHandler) GetSystemSettings(c *gin.Context) {
	category := c.Query("category")
	publicOnly := c.DefaultQuery("public_only", "true") == "true"
	
	esh.logger.WithFields(logrus.Fields{
		"category":    category,
		"public_only": publicOnly,
	}).Info("🚀 获取系统设置")
	
	settings, err := esh.settingsManager.GetSystemSettings(category, publicOnly)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to get system settings")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取系统设置失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
	})
}

// GetSystemSetting 获取单个系统设置
func (esh *EnhancedSettingsHandler) GetSystemSetting(c *gin.Context) {
	category := c.Param("category")
	key := c.Param("key")
	
	esh.logger.WithFields(logrus.Fields{
		"category": category,
		"key":      key,
	}).Info("🚀 获取系统设置")
	
	setting, err := esh.settingsManager.GetSystemSetting(category, key)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to get system setting")
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "设置不存在: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    setting,
	})
}

// SetSystemSetting 设置系统设置
func (esh *EnhancedSettingsHandler) SetSystemSetting(c *gin.Context) {
	category := c.Param("category")
	key := c.Param("key")
	
	var req struct {
		Value string `json:"value" binding:"required"`
		Type  string `json:"type" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}
	
	esh.logger.WithFields(logrus.Fields{
		"category": category,
		"key":      key,
		"value":    req.Value,
		"type":     req.Type,
	}).Info("🚀 设置系统设置")
	
	err := esh.settingsManager.SetSystemSetting(category, key, req.Value, req.Type)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to set system setting")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "设置系统设置失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "系统设置更新成功",
	})
}

// GetUserSettings 获取用户设置
func (esh *EnhancedSettingsHandler) GetUserSettings(c *gin.Context) {
	userID := int64(1) // 简化实现
	category := c.Query("category")
	
	esh.logger.WithFields(logrus.Fields{
		"user_id":  userID,
		"category": category,
	}).Info("🚀 获取用户设置")
	
	settings, err := esh.settingsManager.GetUserSettings(userID, category)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to get user settings")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取用户设置失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    settings,
	})
}

// GetUserSetting 获取单个用户设置
func (esh *EnhancedSettingsHandler) GetUserSetting(c *gin.Context) {
	userID := int64(1) // 简化实现
	category := c.Param("category")
	key := c.Param("key")
	
	esh.logger.WithFields(logrus.Fields{
		"user_id":  userID,
		"category": category,
		"key":      key,
	}).Info("🚀 获取用户设置")
	
	setting, err := esh.settingsManager.GetUserSetting(userID, category, key)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to get user setting")
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "设置不存在: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    setting,
	})
}

// SetUserSetting 设置用户设置
func (esh *EnhancedSettingsHandler) SetUserSetting(c *gin.Context) {
	userID := int64(1) // 简化实现
	category := c.Param("category")
	key := c.Param("key")
	
	var req struct {
		Value string `json:"value" binding:"required"`
		Type  string `json:"type" binding:"required"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}
	
	esh.logger.WithFields(logrus.Fields{
		"user_id":  userID,
		"category": category,
		"key":      key,
		"value":    req.Value,
		"type":     req.Type,
	}).Info("🚀 设置用户设置")
	
	err := esh.settingsManager.SetUserSetting(userID, category, key, req.Value, req.Type)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to set user setting")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "设置用户设置失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "用户设置更新成功",
	})
}

// GetAvailableThemes 获取可用主题
func (esh *EnhancedSettingsHandler) GetAvailableThemes(c *gin.Context) {
	userID := int64(1) // 简化实现
	
	esh.logger.WithField("user_id", userID).Info("🚀 获取可用主题")
	
	themes, err := esh.settingsManager.GetAvailableThemes(&userID)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to get available themes")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "获取主题列表失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    themes,
	})
}

// GetTheme 获取主题配置
func (esh *EnhancedSettingsHandler) GetTheme(c *gin.Context) {
	name := c.Param("name")
	
	esh.logger.WithField("theme_name", name).Info("🚀 获取主题配置")
	
	theme, err := esh.settingsManager.GetTheme(name)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to get theme")
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "主题不存在: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    theme,
	})
}

// CreateCustomTheme 创建自定义主题
func (esh *EnhancedSettingsHandler) CreateCustomTheme(c *gin.Context) {
	userID := int64(1) // 简化实现
	
	var theme service.ThemeConfig
	if err := c.ShouldBindJSON(&theme); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}
	
	esh.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"theme_name": theme.Name,
	}).Info("🚀 创建自定义主题")
	
	err := esh.settingsManager.CreateCustomTheme(userID, &theme)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to create custom theme")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "创建自定义主题失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "自定义主题创建成功",
		"data":    theme,
	})
}

// UpdateTheme 更新主题
func (esh *EnhancedSettingsHandler) UpdateTheme(c *gin.Context) {
	themeIDStr := c.Param("id")
	themeID, err := strconv.ParseUint(themeIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "无效的主题ID",
		})
		return
	}
	
	var updates map[string]interface{}
	if err := c.ShouldBindJSON(&updates); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}
	
	esh.logger.WithFields(logrus.Fields{
		"theme_id": themeID,
		"updates":  updates,
	}).Info("🚀 更新主题")
	
	err = esh.settingsManager.UpdateTheme(uint(themeID), updates)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to update theme")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "更新主题失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "主题更新成功",
	})
}

// ExportSettings 导出设置
func (esh *EnhancedSettingsHandler) ExportSettings(c *gin.Context) {
	userID := int64(1) // 简化实现
	categories := c.QueryArray("categories")
	
	esh.logger.WithFields(logrus.Fields{
		"user_id":    userID,
		"categories": categories,
	}).Info("🚀 导出设置")
	
	data, err := esh.settingsManager.ExportSettings(userID, categories)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to export settings")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "导出设置失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    data,
	})
}

// ImportSettings 导入设置
func (esh *EnhancedSettingsHandler) ImportSettings(c *gin.Context) {
	userID := int64(1) // 简化实现
	
	var data map[string]interface{}
	if err := c.ShouldBindJSON(&data); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "请求参数错误: " + err.Error(),
		})
		return
	}
	
	esh.logger.WithField("user_id", userID).Info("🚀 导入设置")
	
	err := esh.settingsManager.ImportSettings(userID, data)
	if err != nil {
		esh.logger.WithError(err).Error("Failed to import settings")
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   "导入设置失败: " + err.Error(),
		})
		return
	}
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "设置导入成功",
	})
}

// GetMetrics 获取设置管理器指标
func (esh *EnhancedSettingsHandler) GetMetrics(c *gin.Context) {
	metrics := esh.settingsManager.GetMetrics()
	
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    metrics,
	})
}

// 其他处理器方法的简化实现
func (esh *EnhancedSettingsHandler) GetUserSettingsByCategory(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "按分类获取用户设置功能开发中"})
}

func (esh *EnhancedSettingsHandler) DeleteUserSetting(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "删除用户设置功能开发中"})
}

func (esh *EnhancedSettingsHandler) DeleteTheme(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "删除主题功能开发中"})
}

func (esh *EnhancedSettingsHandler) GetAuditLogs(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"success": true, "data": []interface{}{}, "message": "审计日志功能开发中"})
}

func (esh *EnhancedSettingsHandler) ResetSettings(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{"success": true, "message": "重置设置功能开发中"})
}
