# PowerShell script to fix IntelligentSuggestion references in mock_ai_service.go

$filePath = "internal/service/mock_ai_service.go"
$content = Get-Content $filePath

# Replace function signatures
$content = $content -replace 'func \(m \*MockAIService\) generate\w+Suggestions\([^)]+\) \[\]IntelligentSuggestion', {
    $match = $_.Value
    $match -replace '\[\]IntelligentSuggestion', '[]MockIntelligentSuggestion'
}

# Replace variable declarations
$content = $content -replace 'var suggestions \[\]IntelligentSuggestion', 'var suggestions []MockIntelligentSuggestion'

# Replace struct literals
$content = $content -replace 'IntelligentSuggestion\{', 'MockIntelligentSuggestion{'

# Replace slice types in other contexts
$content = $content -replace '\[\]IntelligentSuggestion', '[]MockIntelligentSuggestion'

# Write back to file
$content | Set-Content $filePath

Write-Host "Fixed IntelligentSuggestion references in $filePath"
